/**
 * 请求取消控制模块
 */
import axios from 'axios';

// 存储请求标识和取消函数的Map
const pendingRequests = new Map();

// 需要跳过取消控制的请求URL集合(如上传文件这类需要多次请求的)  --- 建议通过配置config.skipCancel来控制
const SKIP_CANCEL_URLS = new Set([
	'/background/web/BusinessTripController/uploadFile',
	'/background/web/DeliverManagementController/loadpic',
]);

/**
 * 检查是否应该跳过取消控制
 * @param {Object} config - 请求配置
 * @returns {boolean} 是否跳过取消控制
 */
const shouldSkipCancel = config => {
	const { url, skipCancel } = config;
	if (SKIP_CANCEL_URLS.has(url) || skipCancel) {
		console.log('🎢 跳过取消控制', config);
		return true;
	}
	return false;
};

/**
 * 生成请求的唯一键
 * @param {Object} config - 请求配置
 * @returns {string} 请求的唯一键
 */
const generateRequestKey = config => {
	const { url, method, params, data } = config;
	// 可以根据需要调整键的生成方式，例如包含请求方法、参数等
	// return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&');
	return url;
};

/**
 * 添加请求到待处理Map
 * @param {Object} config - 请求配置
 */
export const addPendingRequest = config => {
	// 如果已有取消令牌或需要跳过取消控制，则直接返回
	if (config.cancelToken || shouldSkipCancel(config)) {
		return;
	}

	// 创建取消令牌
	const cancelToken = axios.CancelToken.source();
	config.cancelToken = cancelToken.token;

	const requestKey = generateRequestKey(config);

	// 如果已存在相同请求，则取消之前的请求
	if (pendingRequests.has(requestKey)) {
		const cancelFunc = pendingRequests.get(requestKey);
		cancelFunc('请求已被取消，因为有新的相同请求');
		pendingRequests.delete(requestKey);
	}

	// 存储新请求
	pendingRequests.set(requestKey, cancelToken.cancel);
};

/**
 * 从待处理Map中移除请求
 * @param {Object} config - 请求配置
 */
export const removePendingRequest = config => {
	if (!config) return;

	const requestKey = generateRequestKey(config);
	if (pendingRequests.has(requestKey)) {
		pendingRequests.delete(requestKey);
	}
};

/**
 * 取消所有待处理的请求
 * @param {string} message - 取消消息
 */
export const cancelAllPendingRequests = (message = '操作已取消') => {
	pendingRequests.forEach(cancelFunc => {
		cancelFunc(message);
	});
	pendingRequests.clear();
};

export default {
	addPendingRequest,
	removePendingRequest,
	cancelAllPendingRequests,
};
