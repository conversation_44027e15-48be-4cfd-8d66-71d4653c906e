<template>
	<div class="ExpenseReimbursement" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<!-- 询盘详情 -->
		<InquiryDetail v-if="showMap.InquiryDetail" ref="InquiryDetail" :inquiryOptions="tableData" @close="queryTableData(1)" />
		<!-- 明细组件 -->
		<ReimbursementForm ref="ReimbursementForm" @close="queryTableData(1)" />
		<!-- 打印 -->
		<ExpensePrinter ref="ExpensePrinter" />

		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="费用报销" name="ExpenseReimbursement">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">出差日期</span>
						<DateSelect
							defaultDate="本月"
							@change="
								searchForm.applyDateBegin = $event.startTime;
								searchForm.applyDateEnd = $event.endTime;
								queryTableData(1);
							"
						/>
						<!-- 模糊查询 -->
						<el-input
							class="searchBox"
							size="small"
							v-model.trim="searchForm.userName"
							placeholder="姓名"
							@input="queryTableData(1)"
							clearable
						></el-input>

						<el-checkbox v-model="searchForm.onlyNotApproveItems" @change="queryTableData(1)">仅显示未审批记录</el-checkbox>

						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button :disabled="!isBatch" type="text" class="el-icon-printer" @click="openPrint"
								>批量打印{{ selectedData.length ? `${selectedData.length}张` : '' }}
								{{ totalAmount ? `（合计：¥${totalAmount}）` : '' }}
							</el-button>
							<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							:row-class-name="getRowColor"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['tripBeginDate', 'applyDate'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 申请单号 -->
									<Tooltips
										v-else-if="item.colNo == 'applyNo'"
										class="hover-green green"
										:cont-str="scope.row[item.colNo]"
										:cont-width="scope.column.width || scope.column.realWidth"
										@click.native="$refs.ApplyDialog.openDialog(scope.row)"
									/>

									<!-- 询盘编号 -->
									<Tooltips
										v-else-if="item.colNo == 'idNumber'"
										class="hover-green green"
										:class="getRowColor(scope)"
										@click.native="openInquiryDetail('修改', scope.row)"
										:cont-str="scope.row[item.colNo] || '未知'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<!-- 出差人 -->
									<Tooltips
										v-else-if="item.colNo == 'tripUsers' && scope.row[item.colNo]"
										:cont-str="Object.values(scope.row[item.colNo]).join(',')"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 出差天数 -->
									<Tooltips
										v-else-if="item.colNo == 'tripDays' && scope.row[item.colNo]"
										:cont-str="scope.row[item.colNo] + '天'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 差旅类别-->
									<Tooltips
										v-else-if="item.colNo == 'tripType'"
										:cont-str="tripTypeMap[scope.row[item.colNo]]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 审核状态 0: 未提交，1已提交(待审核)，3 已审核，4，被退回修改 -->
									<Tooltips
										v-else-if="item.colNo == 'approveStatus'"
										:class="['color-999', 'red', '', 'green', 'orange'][scope.row[item.colNo]]"
										:cont-str="
											jointString(
												' ',
												['未提交', '已提交(待审核)', '', '已审核', '被退回修改'][scope.row[item.colNo]],
												dateFormat(scope.row.approveDate, 'lineM'),
												scope.row.approveUserName,
												scope.row.approveMemo,
											)
										"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 结算状态 -->
									<Tooltips
										v-else-if="item.colNo == 'settlementStatus'"
										:class="['color-999', 'green'][scope.row[item.colNo]]"
										:cont-str="
											jointString(
												' ',
												{ 0: '未结算', 1: '已结算' }[scope.row[item.colNo]],
												dateFormat(scope.row.settlementDate, 'lineM'),
											)
										"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<u-table-column label="" width="" align="left">
								<template slot-scope="scope">
									<div v-if="scope.row.approveStatus == 3" class="flex-wrap gap-10">
										<div
											v-for="item in filterOrderList(scope.row.businessTripReimbursementVOList)"
											:key="item.btrid + selectedData.length"
											class="flex-align-center flex-wrap gap-10"
										>
											<div class="flex-align-center">
												<!-- 已提交，可打印 -->
												<el-checkbox
													v-if="item.approveStatus > 0"
													class="mr3"
													v-model="item.checked"
													:true-label="1"
													:false-label="0"
													@change="changeSelectedData(item)"
												>
												</el-checkbox>

												<!-- 审核状态 0: 未提交，1已提交(待审核)，3 已审核，4，被退回修改 -->
												<el-button
													type="text"
													size="mini"
													:class="['', '', 'color-999', 'color-999', 'orange'][item.approveStatus]"
													@click.native.stop="openDetail('编辑', scope.row, item)"
												>
													<!-- {{ jointString(' / ', item.reimbursementNo, `¥${item.expenseAmount}`) }} -->
													{{ `${item.reimbursementNo}(¥${item.expenseAmount})` }}
												</el-button>
											</div>
										</div>
										<el-button type="text" size="mini" @click="openDetail('新建', scope.row)">新建报销单</el-button>
									</div>
									<!-- <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button> -->
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>

		<ApplyDialog ref="ApplyDialog" />
	</div>
</template>

<script>
import { dateFormat, jointString, sortTableData, debounce } from '@/util/tool'; //工具函数
import { bigAdd } from '@/util/math'; //大数计算
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import ReimbursementForm from './components/ReimbursementForm.vue'; //明细组件
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import ExpensePrinter from './components/ExpensePrinter.vue';
import ApplyDialog from './components/ApplyDialog.vue';
import { tripTypeMap } from '@/assets/js/contractSource'; // 差旅类别
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		ReimbursementForm,
		InquiryDetail,
		ExpensePrinter,
		ApplyDialog,
	},
	name: 'ExpenseReimbursement', //组件名应同路由名(否则keep-alive不生效)
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			activeTab: 'ExpenseReimbursement', //激活tab页
			tripTypeMap,
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '申请单号', colNo: 'applyNo', align: 'left', width: '110' },
				{ colName: '出差人', colNo: 'tripUsers', align: 'left', width: '180' },
				{ colName: '出差日期', colNo: 'tripBeginDate', align: 'center', width: '100' },
				{ colName: '出差天数', colNo: 'tripDays', align: 'right', width: '80' },
				{ colName: '客户', colNo: 'tripClientName', align: 'left', width: '120' },
				{ colName: '询盘号', colNo: 'idNumber', align: 'left', width: '110' },
				{ colName: '差旅类别', colNo: 'tripType', align: 'left', width: '80' },
				{ colName: '出差申请状态', colNo: 'approveStatus', align: 'left', width: '120' },
				{ colName: '结算状态', colNo: 'settlementStatus', align: 'left', width: '120' },
			],

			// 查询表单
			searchForm: {
				applyDateBegin: '',
				applyDateEnd: '',
				onlyNotApproveItems: false,
				tripClientName: '',
				userName: '',
				// 其他...
			},

			openMove: false, //打开组件
			showMap: {
				InquiryDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 是否批量操作
		isBatch() {
			return this.selectedData.length > 0;
		},
		// 总金额
		totalAmount() {
			return this.selectedData.reduce((total, item) => bigAdd(total, item.expenseAmount), 0);
		},
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 勾选数据
		changeSelectedData(item) {
			const index = this.selectedData.findIndex(selected => selected.btrid === item.btrid);
			// 添加或移除
			index > -1 ? this.selectedData.splice(index, 1) : this.selectedData.push(item);
		},
		async openPrint() {
			const printDataArray = this.selectedData.map(item => ({
				formInfo: {
					...item,
					...item.businessTripApplicationVO,
					applyUName: item.applyUName, // 出差申请人
					settlementType_reimbursement: item.settlementType, // 报销结算类型
				},
				tableData: item.businessTripExpensesVOList,
			}));
			this.$refs.ExpensePrinter.showDetailCom('打印', printDataArray);
		},
		// 过滤显示当前用户的报销单
		filterOrderList(orders) {
			return orders?.filter(item => item.reimbursementUid == this.userInfos?.adminUserVO.auid) || [];
		},
		// 打开明细
		openDetail(type, row, item) {
			this.$refs.ReimbursementForm.showDetailCom(type, row, item);
		},
		// 打开询盘详情
		openInquiryDetail(type, row, api) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom(type, row, api);
			});
		},
		// 表格文本高光显示
		getRowColor({ row }) {
			if (row.approveStatus == 0) {
				return 'color-999'; //未提交
			}
			// 标红处理：未联系上，阶段为空
			return row.approveStatus == 1 ? 'red' : ''; //未联系上 标红显示
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectBusinessTripApplicationWithReimbursementList'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.searchForm,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.selectedData = [];
						res.data.data?.forEach(aItem => {
							aItem?.businessTripReimbursementVOList?.forEach(item => {
								item.applyUName = aItem.applyUName || ''; // 出差申请人
								item.salesmanName = aItem?.inquiry?.salesmanName || ''; // 业务顾问
								item.settlementType_apply = aItem.settlementType; // 申请结算类型
							});
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//自定义排序(新增/覆盖比较逻辑)
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData =
					order && prop
						? sortTableData(this.tableData, prop, order, (a, b) => {
								// 新增/覆盖比较逻辑
								if (prop == 'tripUsers') {
									if (!a) return -1;
									if (!b) return 1;
									if (Object.values(a).length == Object.values(b).length) {
										const aName = Object.values(a)[0];
										const bName = Object.values(b)[0];
										return aName.localeCompare(bName, 'zh-CN');
									} else {
										return Object.values(a).length - Object.values(b).length;
									}
								} else {
									return null; //其他情况必须要返回null
								}
							})
						: this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},

		dateFormat: dateFormat, //日期format
		jointString: jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.ExpenseReimbursement {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
