<template>
	<div class="courseProductionDetail">
		<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
			<div class="detail-wrapper">
				<div class="detail-head">
					<span class="detail-title">{{ titleName }}课件</span>
					<div class="flex-align-center gap-10">
						<!-- 导入按钮 -->
						<ImportBtn v-if="detailForm.cid" buttonClass="icon-third_pl m0" buttonText="导入考题" @trigger="openImport" />
						<el-button type="text" class="icon-third_save m0" @click="saveDetail"> 保存</el-button>
						<el-button type="text" class="el-icon-arrow-left m0" @click="showCom = false">返回</el-button>
					</div>
				</div>
				<!-- 明细详情弹窗 -->
				<div class="detail-content flex-column gap-10">
					<!-- 顶部课程信息 -->
					<header class="flex-align-center gap-10 pt10 pb5">
						<span v-if="detailForm.courseNo && detailForm.courseNo.length > 0">课程编号：{{ detailForm.courseNo }}； </span>

						<span>课程名称</span>
						<el-input class="vw10 min-w-200" size="small" v-model="detailForm.courseName" placeholder="课程名称"></el-input>

						<span>课程简介</span>
						<el-input
							class="flex-1 min-w-300"
							size="small"
							v-model="detailForm.coursesIntroduction"
							placeholder="课程简介"
						></el-input>
					</header>
					<!-- 中间内容 -->
					<main class="border flex H90">
						<!-- 左侧目录 -->
						<section class="left-question">
							<div ref="leftQuestionWrapper" class="left-question-wrapper flex-column gap-10">
								<div
									class="left-question-item border pointer p10"
									:class="currentIndex == index ? ' bg-primary sticky-top-10 sticky-bottom-0' : ''"
									v-for="(item, index) in detailForm.addCourseDetailsDTOS"
									:key="'left-question-item-' + index"
									@click="currentIndex = index"
								>
									<!-- 题目 -->
									<div class="flex-align-center gap-10">
										<!-- 课程详情类型 0-图片 1-媒体 2-文本 3-考题 -->
										<div v-if="item.type == 0" class="fs-16">🖼️</div>
										<div v-else-if="item.type == 1" class="fs-16">🎞️</div>
										<div v-else-if="item.type == 2" class="fs-16">📔</div>
										<div v-else-if="item.type == 3" class="fs-16">📝</div>
										<Tooltips class="fs-12" :cont-str="`${index + 1}.${item.title} `" :cont-width="300"> </Tooltips>
										<i class="el-icon-delete ml-auto" @click.stop="deleteSection(item)"></i>
									</div>
								</div>
							</div>
							<div v-if="titleName !== '查看'" class="add-btns p10 pr20 pl20 el-icon-plus">
								<div class="fs-16 pointer" title="图片" @click="addSection(0)"> 🖼️ </div>
								<div class="fs-16 pointer" title="视频" @click="addSection(1)"> 🎞️ </div>
								<div class="fs-16 pointer" title="富文本" @click="addSection(2)"> 📔 </div>
								<div class="fs-16 pointer" title="考题" @click="addSection(3)"> 📝 </div>
							</div>
						</section>
						<!-- 中间题目信息 -->
						<section
							class="center-info"
							v-if="
								detailForm.addCourseDetailsDTOS &&
								detailForm.addCourseDetailsDTOS.length > 0 &&
								detailForm.addCourseDetailsDTOS[currentIndex].type != 2
							"
						>
							<!-- 图片 -->
							<div v-if="detailForm.addCourseDetailsDTOS && detailForm.addCourseDetailsDTOS.length > 0">
								<div class="center-info-item" v-if="detailForm.addCourseDetailsDTOS[currentIndex].type == 0">
									<span class="label-required">标题</span>
									<el-input v-model="detailForm.addCourseDetailsDTOS[currentIndex].title" placeholder="标题"> </el-input>
									<div class="flex-align-center gap-10">
										<el-upload
											class="upload-dragger"
											ref="upload"
											action=""
											accept="image/*"
											drag
											:http-request="uploadFile"
											:show-file-list="false"
										>
											<el-button type="text" size="small" class="p0 pl10 pr10">
												{{ !detailForm.addCourseDetailsDTOS[currentIndex].imageUrl ? '上传图片' : '替换图片' }}
											</el-button>
										</el-upload>

										<el-button
											v-show="detailForm.addCourseDetailsDTOS[currentIndex].imageUrl"
											@click="detailForm.addCourseDetailsDTOS[currentIndex].imageUrl = ''"
											type="warning"
											size="small"
											>删除
										</el-button>
									</div>
									<el-checkbox
										v-model="detailForm.addCourseDetailsDTOS[currentIndex].hasMinTime"
										:true-label="1"
										:false-label="0"
									>
										最短阅读时间(秒)
									</el-checkbox>

									<el-input-number
										v-show="detailForm.addCourseDetailsDTOS[currentIndex].hasMinTime"
										v-model="detailForm.addCourseDetailsDTOS[currentIndex].minReadTime"
										size="small"
										:precision="0"
										:step="5"
										:min="0"
										:max="3600"
									></el-input-number>
								</div>
							</div>

							<!-- 媒体 -->
							<div v-if="detailForm.addCourseDetailsDTOS && detailForm.addCourseDetailsDTOS.length > 0">
								<div class="center-info-item" v-if="detailForm.addCourseDetailsDTOS[currentIndex].type == 1">
									<span class="label-required">标题</span>
									<el-input v-model="detailForm.addCourseDetailsDTOS[currentIndex].title" placeholder="标题"> </el-input>
									<div class="flex-align-center gap-10">
										<el-upload
											class="upload-dragger"
											ref="upload"
											action=""
											accept="image/*"
											drag
											:http-request="uploadFile"
											:show-file-list="false"
										>
											<el-button type="text" size="small" class="p0 pl10 pr10">
												{{ !detailForm.addCourseDetailsDTOS[currentIndex].mediaUrl ? '上传媒体文件' : '替换媒体文件' }}
											</el-button>
										</el-upload>

										<el-button
											v-show="detailForm.addCourseDetailsDTOS[currentIndex].mediaUrl"
											@click="detailForm.addCourseDetailsDTOS[currentIndex].mediaUrl = ''"
											type="warning"
											size="small"
											>删除
										</el-button>
									</div>

									<el-input v-model="detailForm.addCourseDetailsDTOS[currentIndex].mediaUrl" placeholder="媒体文件链接">
									</el-input>

									<el-checkbox
										v-model="detailForm.addCourseDetailsDTOS[currentIndex].isAutoPlay"
										:true-label="1"
										:false-label="0"
									>
										自动播放
									</el-checkbox>
									<el-checkbox
										v-model="detailForm.addCourseDetailsDTOS[currentIndex].hasMinTime"
										:true-label="1"
										:false-label="0"
									>
										最短阅读时间(秒)
									</el-checkbox>
									<el-input-number
										v-show="detailForm.addCourseDetailsDTOS[currentIndex].hasMinTime"
										v-model="detailForm.addCourseDetailsDTOS[currentIndex].minReadTime"
										size="small"
										:precision="0"
										:step="5"
										:min="0"
										:max="3600"
									></el-input-number>
								</div>
							</div>

							<!-- 考题 -->
							<div v-if="detailForm.addCourseDetailsDTOS && detailForm.addCourseDetailsDTOS.length > 0">
								<div class="center-info-item" v-if="detailForm.addCourseDetailsDTOS[currentIndex].type == 3">
									<span class="label-required">标题</span>
									<el-input class="title-input" v-model="detailForm.addCourseDetailsDTOS[currentIndex].title" placeholder="标题">
									</el-input>
									<span>考题详情</span>
									<el-input v-model="detailForm.addCourseDetailsDTOS[currentIndex].examQuestionsDetail" placeholder="考题详情">
									</el-input>
									<span>选项模式</span>
									<el-radio-group
										v-model="detailForm.addCourseDetailsDTOS[currentIndex].examQuestionsMode"
										@change="changeExamMode"
									>
										<el-radio :label="0">单选题</el-radio>
										<el-radio :label="1">多选题</el-radio>
									</el-radio-group>
									<span>{{ detailForm.addCourseDetailsDTOS[currentIndex].examQuestionsMode == 0 ? '单选' : '多选' }}选项</span>
									<!-- 单选 -->
									<div v-if="detailForm.addCourseDetailsDTOS[currentIndex].examQuestionsMode == 0">
										<el-radio-group v-model="radio">
											<el-radio
												class="mb10"
												:label="item.isCorrect"
												v-for="(item, index) in detailForm.addCourseDetailsDTOS[currentIndex].addAnswerDTOS"
												:key="'radio-' + index"
												@change="singleChange(item, detailForm.addCourseDetailsDTOS[currentIndex].addAnswerDTOS)"
											>
												<el-input class="vw18 min-w-200" v-model="item.answer" placeholder="选项"> </el-input>
											</el-radio>
										</el-radio-group>
									</div>
									<!-- 多选 -->
									<div v-if="detailForm.addCourseDetailsDTOS[currentIndex].examQuestionsMode == 1">
										<el-checkbox
											class="mb10"
											:checked="item.isCorrect == 0"
											v-for="(item, index) in detailForm.addCourseDetailsDTOS[currentIndex].addAnswerDTOS"
											:key="'checkbox-' + index"
											@change="multiChange(item)"
										>
											<el-input class="vw18 min-w-200" v-model="item.answer" placeholder="选项"> </el-input>
										</el-checkbox>
									</div>
								</div>
							</div>
						</section>
						<!-- 右侧预览 -->
						<section class="right-preview">
							<div class="W100 H100" v-if="detailForm.addCourseDetailsDTOS && detailForm.addCourseDetailsDTOS.length > 0">
								<div class="right-preview-item" v-if="detailForm.addCourseDetailsDTOS[currentIndex].type == 0">
									<span class="label-title">图片预览 🖼️ </span>
									<div v-if="detailForm.addCourseDetailsDTOS[currentIndex].imageUrl">
										<el-image class="W100 H100 border" :src="detailForm.addCourseDetailsDTOS[currentIndex].imageUrl"></el-image>
									</div>
								</div>
								<div class="right-preview-item" v-if="detailForm.addCourseDetailsDTOS[currentIndex].type == 1">
									<span class="label-title">视频预览 🎞️ </span>
									<iframe
										height="650"
										class="W100 border min-h-500"
										v-if="detailForm.addCourseDetailsDTOS[currentIndex].mediaUrl"
										:src="detailForm.addCourseDetailsDTOS[currentIndex].mediaUrl"
										frameborder="0"
										allowfullscreen
									></iframe>
									<!-- <video
										class="W100 H100 border"
										v-if="detailForm.addCourseDetailsDTOS[currentIndex].mediaUrl"
										:src="detailForm.addCourseDetailsDTOS[currentIndex].mediaUrl"
										:autoplay="detailForm.addCourseDetailsDTOS[currentIndex].isAutoPlay"
										controls
									></video> -->
								</div>
								<!-- 富文本 -->
								<div class="right-preview-item" v-if="detailForm.addCourseDetailsDTOS[currentIndex].type == 2">
									<span class="label-title">富文本编辑器 📔 </span>
									<el-input v-model="detailForm.addCourseDetailsDTOS[currentIndex].title" placeholder="标题"> </el-input>
									<el-checkbox
										v-model="detailForm.addCourseDetailsDTOS[currentIndex].hasMinTime"
										:true-label="1"
										:false-label="0"
									>
										最短阅读时间(秒)
									</el-checkbox>

									<el-input-number
										class="input-number"
										v-if="detailForm.addCourseDetailsDTOS[currentIndex].hasMinTime"
										v-model="detailForm.addCourseDetailsDTOS[currentIndex].minReadTime"
										size="small"
										:precision="0"
										:step="5"
										:min="0"
										:max="3600"
									></el-input-number>

									<wang-editor
										class="W100 H100"
										v-model="detailForm.addCourseDetailsDTOS[currentIndex].textContent"
										:editorHeight="editorHeight"
									></wang-editor>
								</div>
								<!-- 考题 -->
								<div class="right-preview-item" v-if="detailForm.addCourseDetailsDTOS[currentIndex].type == 3">
									<span class="label-title">考题预览 📝 </span>
									<span>
										<span>{{ currentIndex + 1 }}. </span>
										<span> {{ detailForm.addCourseDetailsDTOS[currentIndex].title }}</span>
									</span>
									<span>{{ detailForm.addCourseDetailsDTOS[currentIndex].examQuestionsDetail }}</span>
									<!-- 单选 -->
									<div v-if="detailForm.addCourseDetailsDTOS[currentIndex].examQuestionsMode == 0">
										<el-radio-group disabled v-model="radio">
											<el-radio
												class="W100 mb10"
												v-for="(item, index) in detailForm.addCourseDetailsDTOS[currentIndex].addAnswerDTOS"
												:key="'radio-' + index"
												:label="item.isCorrect"
											>
												<span>{{ ['A. ', 'B. ', 'C. ', 'D. '][index] }} {{ item.answer }}</span>
											</el-radio>
										</el-radio-group>
									</div>
									<!-- 多选 -->
									<div :key="elementKey" v-else-if="detailForm.addCourseDetailsDTOS[currentIndex].examQuestionsMode == 1">
										<el-checkbox
											class="W90 mb10"
											disabled
											:checked="item.isCorrect == 0"
											v-for="(item, index) in detailForm.addCourseDetailsDTOS[currentIndex].addAnswerDTOS"
											:key="'checkbox-' + index"
										>
											<span>{{ ['A. ', 'B. ', 'C. ', 'D. '][index] }} {{ item.answer }}</span>
										</el-checkbox>
									</div>
								</div>
							</div>
						</section>
					</main>

					<!-- 底部操作按钮 -->
					<!-- <footer class="h-40 text-right">
						<el-button type="primary" @click="saveDetail">保存</el-button>
					</footer> -->
				</div>
			</div>
		</div>

		<!-- 导入弹窗 -->
		<ImportTable ref="ImportTable" @refresh="queryDetail(detailForm.cid)" />
	</div>
</template>
<script>
import { resetValues, checkRequired, deepClone, dateFormat, jointString, debounce } from '@/util/tool';
import { mapGetters } from 'vuex';
import wangEditor from '@/components/WangEditor/wangEditor';
import ImportTable from '@/components/ImportTable'; //数据导入
import ImportBtn from '@/components/ImportTable/ImportBtn'; //导入按钮
export default {
	name: 'courseProductionDetail',
	directives: {},
	components: { wangEditor, ImportTable, ImportBtn },
	props: {
		channelName: {
			type: Array,
			default: () => {
				return [];
			},
		},
		twidList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '明细详情',
			logList: [],
			detailForm: {
				cid: '',
				courseNo: '',
				courseName: '',
				coursesIntroduction: '',
				addCourseDetailsDTOS: [],
			},
			detailForm_Copy: [],
			currentIndex: 0,

			radio: 0,
			elementKey: 0,

			//资源列表
			teamList: [], //分销/代理列表
			userList: [],
		};
	},
	created() {},
	computed: {
		editorHeight() {
			// return this.showType ? 480 : 550;
			return 340;
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.detailForm = resetValues(this.detailForm); //重置对象
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 导入考题
		openImport: debounce(function (type = 'import') {
			const PROPS = {
				API: 'importCourse', //导入接口
				templateName: '课程导入', //模板文件名称（下载模板用）
				dataName: '课程考题数据', //数据名（提示：成功导入xxx数据xxx条!）
				type, // 导入或查看导入记录
				// 这里是 query 参数
				params: {
					cid: this.detailForm.cid,
				},
			};
			this.$refs.ImportTable.openImport(PROPS);
		}),
		multiChange(item) {
			if (item.isCorrect == 0) {
				item.isCorrect = 1;
			} else {
				item.isCorrect = 0;
			}
			this.elementKey += 1;
			// console.log(item, list)
		},
		singleChange(item, list) {
			this.radio = 0;
			for (let index = 0; index < list.length; index++) {
				if (list[index].answer == item.answer) {
					list[index].isCorrect = 0;
				} else {
					list[index].isCorrect = 1;
				}
			}
			// console.log(item, list)
		},
		addSection(type) {
			const sectionItem = {
				addAnswerDTOS: [],
				examQuestionsDetail: '',
				examQuestionsMode: 0, //考题模式 0-单选题 1-多选题
				imageUrl: '',
				isAutoPlay: false, //是否自动播放 0-是 1-否
				mediaUrl: '',
				hasMinTime: false,
				minReadTime: 0,
				textContent: '',
				title: '标题' + (this.detailForm.addCourseDetailsDTOS.length + 1),
				type: type, //课程详情类型 0-图片 1-媒体 2-文本 3-考题
			};

			if (type == 3) {
				sectionItem.title = '考题' + (this.detailForm.addCourseDetailsDTOS.length + 1);
				sectionItem.addAnswerDTOS = [
					{
						answer: '',
						isCorrect: 1, //是否为正确答案 0:正确 1:错误
					},
					{
						answer: '',
						isCorrect: 1, //是否为正确答案 0:正确 1:错误
					},
					{
						answer: '',
						isCorrect: 1, //是否为正确答案 0:正确 1:错误
					},
					{
						answer: '',
						isCorrect: 1, //是否为正确答案 0:正确 1:错误
					},
				];
			}

			this.detailForm.addCourseDetailsDTOS.push(sectionItem);
			setTimeout(() => {
				this.currentIndex = this.detailForm.addCourseDetailsDTOS.length - 1;
			}, 666);
			// 滚动到最底部
			this.$nextTick(() => {
				this.$refs.leftQuestionWrapper.scrollTo({
					top: this.$refs.leftQuestionWrapper.scrollHeight,
					behavior: 'smooth',
				});
			});
		},
		deleteSection(item) {
			this.detailForm.addCourseDetailsDTOS.splice(this.detailForm.addCourseDetailsDTOS.indexOf(item), 1);
			if (this.currentIndex >= this.detailForm.addCourseDetailsDTOS.length) {
				this.currentIndex = this.detailForm.addCourseDetailsDTOS.length - 1;
			}
			if (this.currentIndex < 0) {
				this.currentIndex = 0;
			}
		},
		// 上传文件
		uploadFile(item) {
			const isLt200M = item.file.size / 1024 / 1024 < 200; // 如果文件大于200M，则不上传
			if (!isLt200M) {
				this.$message.warning('上传文件的大小不能超过 200MB!');
				return;
			}
			const formData = new FormData();
			formData.append('file', item.file);
			this.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						if (this.detailForm.addCourseDetailsDTOS[this.currentIndex].type == 0) {
							this.detailForm.addCourseDetailsDTOS[this.currentIndex].imageUrl = res.data.data.path;
						} else {
							this.detailForm.addCourseDetailsDTOS[this.currentIndex].mediaUrl = res.data.data.path;
						}
						// this.saveEdit();
						// this.$nextTick(() => {
						// 	this.$refs.audioPlayer?.load(); // 加载新的音频源
						// });
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.error('Upload failed', error);
				});
		},

		changeExamMode(node) {
			// console.log(node)
			this.radio = 0;
			const sectionItem = this.detailForm.addCourseDetailsDTOS[this.currentIndex].addAnswerDTOS;
			for (let aindex = 0; aindex < sectionItem.length; aindex++) {
				sectionItem[aindex].isCorrect = 1;
			}
		},

		// 变更前的明细
		queryDetail(cid) {
			if (!cid) {
				return;
			}
			this.$axios
				.selectCourseDetails(JSON.stringify({ cid }))
				.then(res => {
					if (res.data.success) {
						if (!res.data.data.courseDetailsVOS) {
							res.data.data.courseDetailsVOS = [];
						}
						for (let index = 0; index < res.data.data.courseDetailsVOS.length; index++) {
							const element = res.data.data.courseDetailsVOS[index];
							element.isAutoPlay = res.data.data.courseDetailsVOS[index].isAutoPlay == 0;
							element.hasMinTime = res.data.data.courseDetailsVOS[index].hasMinTime == 0;

							element.addAnswerDTOS = element.selectAnswerVOS;
						}

						this.detailForm.cid = cid;
						this.detailForm.courseNo = res.data.data.coursesNo;
						this.detailForm.courseName = res.data.data.coursesName;
						this.detailForm.coursesIntroduction = res.data.data.coursesIntroduction;
						this.detailForm.addCourseDetailsDTOS = res.data.data.courseDetailsVOS;

						console.log(this.detailForm);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectCourseDetails |' + error);
				});
		},

		// 添加/保存信息
		saveDetail(isClose = true) {
			// if (checkRequired(this.detailForm, this.formRules)) {
			// 	return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			// }
			for (let index = 0; index < this.detailForm.addCourseDetailsDTOS.length; index++) {
				const sectionItem = this.detailForm.addCourseDetailsDTOS[index];
				sectionItem.isAutoPlay = sectionItem.isAutoPlay ? 0 : 1;
				if (!sectionItem.hasMinTime) {
					sectionItem.minReadTime = 0;
				}
				sectionItem.hasMinTime = sectionItem.hasMinTime ? 0 : 1;

				// if(sectionItem.type==3 && sectionItem.examQuestionsMode==0) {
				// 	// 设置选项答案
				// 	for (let aindex = 0; aindex < sectionItem.addAnswerDTOS.length; aindex++) {
				// 		if(this.radio == aindex) {
				// 			sectionItem.addAnswerDTOS[aindex].isCorrect = 0;
				// 		}else {
				// 			sectionItem.addAnswerDTOS[aindex].isCorrect = 1;
				// 		}
				// 	}
				// }else if(sectionItem.type==3 && sectionItem.examQuestionsMode==1) {
				// 	// 设置选项答案
				// 	for (let aindex = 0; aindex < sectionItem.addAnswerDTOS.length; aindex++) {
				// 		const answerItem = sectionItem.addAnswerDTOS[aindex];
				// 		var findex = this.checkList.findIndex(item => {
				// 			if (item == answerItem.answer) {
				// 				return true
				// 			}
				// 		});
				// 		if(findex<0) {
				// 			sectionItem.addAnswerDTOS[aindex].isCorrect = 1;
				// 		}else {
				// 			sectionItem.addAnswerDTOS[aindex].isCorrect = 0;
				// 		}
				// 	}
				// }
			}
			let API = 'addCourse';
			if (this.detailForm.cid) {
				API = 'updateCourse';
			}
			// this.detailForm.approvalStatus = 4;
			this.$axios[API](JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						isClose && (this.showCom = false);
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},

		//显示弹窗
		showDetailCom(type, rowData) {
			this.titleName = type;
			this.currentIndex = 0;
			this.detailForm = {
				cid: '',
				courseNo: '',
				courseName: '',
				coursesIntroduction: '',
				addCourseDetailsDTOS: [],
			};

			if (rowData) {
				this.detailForm = {
					...this.detailForm,
					...rowData,
				};
				this.queryDetail(rowData.cid);
			} else {
				this.detailForm_Copy = deepClone(this.detailForm);
			}
			this.showCom = true;
		},

		//日期format
		dateFormat,
		jointString,
	},
};
</script>
<style lang="scss" scoped>
.courseProductionDetail {
	main {
		.left-question {
			min-width: 23%;
			max-width: 23%;
			height: 100%;
			position: relative;
			background-color: #efefef;
			.left-question-wrapper {
				height: 100%;
				padding: 10px;
				overflow: scroll;
				padding-bottom: 30px; //消除add-btns绝对定位的影响
				.left-question-item {
					border-radius: 5px;
					background-color: #fff;
				}
			}

			.add-btns {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				z-index: 100;
				background-color: #f2f2f2;

				position: absolute;
				bottom: 0;
			}
		}
		.center-info {
			min-width: 27%;
			max-width: 27%;
			height: 100%;
			overflow: scroll;
			border-left: 1px solid #dbdee4;
			.center-info-item {
				display: flex;
				flex-direction: column;
				gap: 10px;
				padding: 15px 15px 15px 20px;
			}
		}
		.right-preview {
			width: 100%;
			height: 100%;
			overflow: scroll;
			border-left: 1px solid #dbdee4;
			.right-preview-item {
				display: flex;
				flex-direction: column;
				gap: 10px;
				padding: 15px 15px 15px 20px;
			}
		}
	}
}
</style>
