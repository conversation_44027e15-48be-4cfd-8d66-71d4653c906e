<template>
	<div id="AuditDialog">
		<el-dialog width="600px" :visible.sync="isShow" :close-on-click-modal="false" :append-to-body="true" @close="close">
			<!-- 标题 -->
			<span slot="title">质量检查评价</span>
			<!-- 表单 -->
			<el-form
				ref="editFormRef"
				:model="editForm"
				:rules="formRules"
				label-width="100px"
				label-position="left"
				@submit.native.prevent
			>
				<el-form-item label="质量检查" prop="auditStatus">
					<el-radio-group v-model="editForm.auditStatus">
						<el-radio v-for="item in auditStatusOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="检查评价" prop="auditMemo">
					<el-input
						v-model="editForm.auditMemo"
						placeholder="请输入检查评价"
						type="textarea"
						:autosize="{ minRows: 4, maxRows: 6 }"
					></el-input>
				</el-form-item>

				<el-form-item v-if="editForm.auditDate" label="检查日期" prop="auditDate">
					<span> {{ dateFormat(editForm.auditDate) }} </span>
				</el-form-item>
			</el-form>

			<div class="pb20 color-999 fs-12 el-icon-warning-outline"> 如果质量检查为不合格、合规带问题、优秀，请填写检查评价</div>
			<!-- 底部按钮 -->
			<span slot="footer">
				<el-button @click="close">取 消</el-button>
				<el-button type="primary" @click="save">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { checkRequired, resetValues, dateFormat } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'AuditDialog',
	components: {},
	props: {},
	data() {
		return {
			isShow: false,
			editForm: {
				auditStatus: '',
				auditMemo: '',
			},

			auditStatusOptions: [
				// { value: 1, label: '未评' },
				{ value: 2, label: '不合格' },
				{ value: 3, label: '合规带问题' },
				{ value: 4, label: '合格' },
				{ value: 5, label: '优秀' },
			],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		// 表单规则
		formRules() {
			this.$refs.editFormRef?.clearValidate();
			// 如果为不合格、合规带问题、优秀，则需要填写备注
			if ([2, 3, 5].includes(this.editForm.auditStatus)) {
				return {
					auditStatus: [{ required: true, message: '请选择质量检查', trigger: 'blur' }],
					auditMemo: [{ required: true, message: '请填写检查评价', trigger: 'blur' }],
				};
			}
			return {
				auditStatus: [{ required: true, message: '请选择质量检查', trigger: 'blur' }],
			};
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 打开
		open(data = {}) {
			this.editForm = { ...this.editForm, ...data };
			this.isShow = true;
		},
		// 关闭
		close() {
			this.isShow = false;
			this.editForm = resetValues(this.editForm);
			this.$nextTick(() => {
				this.$refs.editFormRef?.resetFields();
				this.$refs.editFormRef?.clearValidate();
			});
		},
		// 保存
		async save() {
			if (checkRequired(this.editForm, this.formRules)) return;
			const API = 'updateAdminUserPointsAudit';
			try {
				const res = await this.$axios[API](JSON.stringify(this.editForm));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.$emit('update');
					this.isShow = false;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		dateFormat, //日期format
	},
};
</script>
