<template>
	<div id="QandA">
		<QandADetail ref="QandADetail" @close="queryTableData" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="问答" name="QandA">
				<div class="QandA-wrapper flex">
					<!-- 问答清单 -->
					<div class="QandA-list-wrapper W100 H100 flex-column gap-10 border-right">
						<!-- 背景图 -->
						<img class="bg-logo" src="@/assets/img/lightmes-logo.webp" />
						<!-- 搜索 -->
						<div class="QandA-list-header flex-align-center gap-10">
							<SearchHistoryInput
								:showLabel="false"
								width="250"
								name="querySubject"
								placeholder="请输入问题查询"
								v-model.trim="searchForm.querySubject"
								@input="queryTableData(1)"
							/>

							<el-checkbox v-model="searchForm.isSelectOnlyWaitAnswer" :true-label="1" :false-label="0" @change="queryTableData">
								等待回答
							</el-checkbox>
							<el-button type="text" size="small" class="ml-auto el-icon-refresh pr20" @click="queryTableData('refresh')">
								刷新
							</el-button>
						</div>

						<!-- 问题列表 -->
						<div class="QandA-list flex-column gap-10">
							<div
								class="QandA-list-item flex-column pointer gap-10"
								:class="{ 'border-color-orange': !item.replyCount }"
								v-for="item in questionList"
								:key="item.qid"
								@click="openReplyDetail(item)"
							>
								<!-- 问题标题和图片 -->
								<div class="QandA-list-item-title flex-align-center">
									<!-- 问题图片 -->
									<div @click.stop.prevent="() => {}">
										<el-badge v-if="item.urls && item.urls.length > 0" :value="item.urls.length" class="mini-badge">
											<el-image
												lazy
												class="max-h-40 max-w-40 border mr10"
												fit="scale-down"
												:src="item.urls[0]"
												:preview-src-list="item.urls"
											>
												<div slot="error" class="el-image__error">
													<i class="el-icon-picture-outline"></i>
												</div>
											</el-image>
										</el-badge>
									</div>
									<!-- 问题标题 -->
									<!-- <span class="QandA-list-item-title-text">📑 {{ item.subject }}</span> -->
									<span class="QandA-list-item-title-text">{{ item.subject }}</span>

									<!-- 按钮 -->
									<div class="ml-auto">
										<!-- 删除 -->
										<el-button
											v-show="isSuperAdmin || searchForm.isSelectOnlySelf"
											type="text"
											size="mini"
											class="el-icon-delete p0 m0"
											@click.stop="delData(item)"
										>
										</el-button>
										<!-- 编辑 -->
										<el-button
											v-show="isSuperAdmin || searchForm.isSelectOnlySelf"
											type="text"
											size="mini"
											class="el-icon-edit p0 m0"
											@click.stop="$refs.QandAOperation.openEdit(item)"
										>
										</el-button>
									</div>
								</div>

								<!-- 最新的一条回复 -->
								<div v-if="item.getReplyListByQidVO" class="QandA-list-item-content flex-column gap-10 color-999 fs-12">
									<!-- 回复人和时间 -->
									<div class="flex-align-center gap-10">
										<span class="bolder color-666">👩‍🏫 {{ item.getReplyListByQidVO.replyUserName }}</span>
										<span>{{ dateFormat(item.getReplyListByQidVO.replyTime, 'lineM') }} 的回答</span>
									</div>
									<!-- 回复内容 -->
									<pre class="m0 p0 ellipsis2">{{ item.getReplyListByQidVO.replyContent }}</pre>
								</div>

								<!-- 问题待回复 -->
								<div v-else class="QandA-list-item-content flex-align-center gap-10 color-999 fs-12">
									<div class="QandA-list-item-content-text">{{ dateFormat(item.askTime, 'lineM') }}</div>
									<span class="">|</span>
									<div class="QandA-list-item-content-text flex-1">
										{{ `${item.replyCount ? '已回复：' + item.replyCount : '等待回答'}` }}
									</div>
								</div>
							</div>
						</div>

						<!-- 分页 -->
						<div class="QandA-list-footer text-right pr10">
							<el-pagination
								@size-change="handleSizeChange"
								@current-change="handleCurrentChange"
								:current-page="searchForm.pageNum"
								:page-sizes="[50, 100, 500]"
								:page-size="searchForm.pageSize"
								layout="total, sizes, prev, pager, next, jumper"
								:total="dataTotal"
							>
							</el-pagination>
						</div>
					</div>

					<!-- 问答操作 -->
					<QandAOperation ref="QandAOperation" @refresh="queryTableData" @getSearchForm="getSearchForm" />
				</div>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<script>
import { deepClone, dateFormat, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import QandAOperation from './QandAOperation.vue';
import QandADetail from './QandADetail.vue';

export default {
	name: 'QandA',
	components: { QandAOperation, QandADetail },
	data() {
		return {
			activeTab: 'QandA',
			questionList: [],
			searchForm: {
				isSelectOnlyAnswered: 0,
				isSelectOnlySelf: 0,
				isSelectOnlyWaitAnswer: 0,
				pageNum: 1,
				pageSize: 50,
				querySubject: '',
			},
			dataTotal: 0,
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 切换tab
		changeTab() {
			if (this.activeTab == 'QandA') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 打开提示
		openMsg(msg) {
			return this.$message.warning(`【${msg}】正在开发中，敬请期待！`);
		},
		// 打开回复详情
		openReplyDetail(item) {
			this.$refs.QandADetail.showDetailCom(item);
		},
		// 删除
		delData({ subject, qid }) {
			this.$confirm(`此操作将永久删除【${subject}】, 是否继续?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'deleteQuestion';
					try {
						const res = await this.$axios[API](JSON.stringify({ qid }));
						if (res.data.success) {
							this.$succ(res.data.message);
							this.queryTableData();
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 获取查询条件
		getSearchForm(selectOnly) {
			this.searchForm.isSelectOnlyAnswered = selectOnly.answered;
			this.searchForm.isSelectOnlySelf = selectOnly.self;
			this.queryTableData();
		},
		// 查询问题列表
		async queryTableData(type) {
			type && (this.searchForm.pageNum = 1);
			const API = 'selectQuestionList';
			try {
				const res = await this.$axios[API](JSON.stringify(this.searchForm));
				if (res.data.success) {
					this.questionList = res.data.data;
					this.dataTotal = res.data.totalItems;
					this.searchForm.isSelectOnlyWaitAnswer && this.$store.commit('setQAndABadgeNum', this.dataTotal);
					type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 分页
		handleSizeChange(size) {
			this.searchForm.pageSize = size;
			this.queryTableData();
		},
		// 当前页
		handleCurrentChange(page) {
			this.searchForm.pageNum = page;
			this.queryTableData();
		},
		dateFormat,
	},
};
</script>

<style lang="scss">
#QandA {
	width: 100%;
	overflow: hidden;
	position: relative;
}
.QandA-wrapper {
	width: 100%;
	height: calc(100vh - 175px);
	overflow: hidden;
	position: relative;

	border: 1px solid #d7d7d7;
	border-radius: 8px;
	background: #fff;
	color: #666;
	.QandA-list-wrapper {
		position: relative;
		padding: 10px 0 10px 10px;
		.bg-logo {
			position: absolute;
			background-size: cover;
			opacity: 0.05;
			top: 50%;
			left: 50%;
			width: 800px;
			transform: translate(-50%, -50%);
			pointer-events: none; // 禁用鼠标事件
		}

		.QandA-list {
			font-size: 14px;
			height: 100%;
			overflow-y: auto;
			padding-right: 10px;

			.QandA-list-item {
				padding: 10px;

				background: #f9f9f9;
				transition: all 0.3s;
				border-radius: 8px;
				border: 1px solid #f2f2f2;

				&:hover {
					border-color: #d7d7d7;
					background: #e9f5f1;
					border-color: #1e9d6f;
					z-index: 2;
				}
				.QandA-list-item-title {
					color: #555;
					font-size: 16px;
					font-weight: 600;
				}

				.QandA-list-item-content {
					font-size: 14px;
					display: flex;
					justify-content: space-between;
				}
				.mini-badge {
					.el-badge__content {
						z-index: 2;
						zoom: 0.8;
						padding: 0 5px;
						right: 25px;
						/* top: 2.5px; */
					}
				}
			}
		}
	}
}

@media screen and (max-width: 1280px) {
	.QandA-wrapper {
		height: calc(100vh - 120px) !important;
	}
}
</style>
