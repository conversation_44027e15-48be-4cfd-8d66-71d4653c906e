/* 
	在源码的基础上改写的一个预览图片的插件，注意：源码上 getValue 方法 应该是有bug获取不到节点的 href 其实应该是 src
	@see:https://github.com/wangeditor-team/wangEditor/blob/master/packages/basic-modules/src/modules/image/menu/ViewImageLink.ts
*/

import { IButtonMenu, IDomEditor, DomEditor, t } from '@wangeditor/editor';
function showImageFullScreen(imageUrl) {
	const img = new Image();
	img.src = imageUrl;

	img.onload = function () {
		// 容器背景 modal
		const fullscreenDiv = document.createElement('div');
		fullscreenDiv.style.position = 'fixed';
		fullscreenDiv.style.top = '0';
		fullscreenDiv.style.left = '0';
		fullscreenDiv.style.width = '100vw';
		fullscreenDiv.style.height = '100vh';
		fullscreenDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
		fullscreenDiv.style.display = 'flex';
		fullscreenDiv.style.justifyContent = 'center';
		fullscreenDiv.style.alignItems = 'center';
		fullscreenDiv.style.zIndex = 999999;

		// 图片 img
		const imgElement = document.createElement('img');
		imgElement.src = imageUrl;
		imgElement.style.maxWidth = '90vw';
		imgElement.style.maxHeight = '90vh';
		imgElement.style.cursor = 'zoom-in'; // 设置鼠标样式为放大镜样式

		fullscreenDiv.appendChild(imgElement);
		document.body.appendChild(fullscreenDiv);

		let scale = 1;
		// 滚轮放大缩小
		fullscreenDiv.addEventListener('wheel', function (event) {
			event.preventDefault();
			const delta = event.deltaY > 0 ? 1.1 : 0.9;
			scale *= delta;
			imgElement.style.transform = `scale(${scale})`;
		});
		// 点击任意地方关闭
		fullscreenDiv.addEventListener('click', function () {
			document.body.removeChild(fullscreenDiv);
		});
	};
}

class PreViewImage {
	constructor() {
		this.title = '🔎 预览图片';
		// this.iconSvg = '<svg >...</svg>'
		this.tag = 'button';
		// this.showModal = true;
	}
	getValue(editor) {
		const imageNode = DomEditor.getSelectedNodeByType(editor, 'image');
		if (imageNode) {
			// 选区处于 image node
			return imageNode.href || imageNode.src || ''; //参考源码 viewImageLink 注意：应该是有bug获取不到节点的 href 其实应该是 src
		}
		return '';
	}

	isActive(editor) {
		// 无需 active
		return false;
	}

	isDisabled(editor) {
		if (editor.selection == null) return true;

		const href = this.getValue(editor);
		if (href) {
			// 有 image href ，则不禁用
			return false;
		}
		return true;
	}

	exec(editor, value) {
		if (this.isDisabled(editor)) return;

		if (!value || typeof value !== 'string') {
			throw new Error(`View image link failed, image.href is '${value}'`);
		}

		// 查看链接
		// window.open(value, '_blank');

		// 全屏预览
		showImageFullScreen(value); // 调用全屏显示图片的函数
	}
}

export default {
	key: 'preViewImage', // 定义 menu key ：要保证唯一、不重复（重要）
	factory() {
		return new PreViewImage(this); // 把 `YourMenuClass` 替换为你菜单的 class
	},
};
