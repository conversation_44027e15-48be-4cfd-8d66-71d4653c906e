# Loading 组件

自定义加载组件，提供美观的加载动画和文本提示效果。基于 Element UI 的 loading 图标，增强了视觉效果和功能性。

## 功能特点

- 支持彩虹渐变色的加载文本动画
- 支持滚动词组动态展示
- 可自定义加载提示文本
- 提供全屏遮罩层功能
- 支持服务式调用和组件式使用
- 自动处理资源回收和销毁

## 组件属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| text | String | '' | 否 | 自定义加载提示文本 |
| wordList | Array | [] | 是 | 滚动展示的词组列表 |

## 服务式调用属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| text | String | '' | 否 | 自定义加载提示文本 |
| wordList | Array | [] | 否 | 滚动展示的词组列表 |
| showMask | Boolean | true | 否 | 是否显示全屏遮罩层 |

## 使用示例

### 组件式使用

```vue
<!-- 基本用法 -->
<template>
  <CustomLoading 
    text="加载中" 
    :wordList="['请稍候', '正在处理', '即将完成', '马上就好']" 
  />
</template>

<script>
import CustomLoading from '@/components/Loading/index.vue';

export default {
  components: {
    CustomLoading
  }
}
</script>
```

### 服务式调用

```js
// 导入 LoadingService
import LoadingService from '@/components/Loading/loading-service';

// 创建加载实例
const loading = new LoadingService({
  text: '加载中',
  wordList: ['请稍候', '正在处理', '即将完成', '马上就好'],
  showMask: true
});

// 显示加载
loading.show();

// 操作完成后关闭
setTimeout(() => {
  loading.close();
}, 3000);
```

### 在异步请求中使用

```js
import LoadingService from '@/components/Loading/loading-service';

async function fetchData() {
  const loading = new LoadingService({
    text: '数据加载中',
    wordList: ['请稍候', '正在获取数据']
  });
  
  loading.show();
  
  try {
    const response = await api.getData();
    return response.data;
  } finally {
    loading.close();
  }
}
```

## 注意事项

1. 使用服务式调用时，需要手动调用 `close()` 方法关闭加载提示
2. 组件会自动处理资源回收，不需要手动管理 DOM
3. 滚动词组动画会自动循环展示所有词组
4. 遮罩层默认为白色半透明背景，可通过修改样式自定义
5. 建议在全局请求拦截器中统一管理 loading 状态

## 实现原理

组件使用 Vue 的扩展机制创建可编程式调用的服务，通过动态创建 Vue 实例并挂载到 body 上实现全局加载提示。使用 CSS 动画实现彩虹文本效果和滚动词组动画，提供良好的视觉反馈。 