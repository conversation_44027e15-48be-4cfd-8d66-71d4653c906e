# A-TEMPLATE 模板组件

A-TEMPLATE 目录包含了一系列基础模板组件，用于快速构建标准化的页面和功能。这些模板遵循项目的设计规范，提供了常见页面结构的标准实现，可作为新页面开发的起点。

## 模板组件列表

### 1. BasePage.vue

基础页面模板，用于创建标准列表页面，包含搜索条件、表格和常用操作按钮。

**功能特点：**
- 集成了日期选择器和搜索输入框
- 支持表格数据刷新、导出和列设置
- 内置分页和排序功能
- 支持表格列自定义宽度和拖拽调整
- 适配虚拟滚动，优化大数据量展示性能

### 2. TabPage.vue

带选项卡的页面模板，在基础页面的基础上增加了选项卡功能，适用于需要分类展示数据的场景。

**功能特点：**
- 支持多选项卡切换
- 每个选项卡可以有独立的数据和操作
- 保持了BasePage的所有基础功能
- 适合复杂数据分类展示

### 3. BaseDialog.vue

基础弹窗模板，用于创建标准的表单弹窗，适用于数据新增和编辑场景。

**功能特点：**
- 预设了常用的表单布局和验证规则
- 支持表单重置和数据校验
- 适配移动端和桌面端的显示
- 支持自定义表单项和验证规则
- 内置保存和取消操作

### 4. BaseDetail.vue

基础详情模板，用于创建侧边抽屉式的详情页面，适用于展示和编辑复杂数据。

**功能特点：**
- 滑动式侧边栏设计
- 分区块展示详细信息
- 支持表单编辑和数据保存
- 包含子表格功能，适合主子表结构
- 支持自定义表单项和验证规则

## 使用方法

### 创建新页面

1. 复制适合的模板文件到目标目录
2. 修改组件名称和相关配置
3. 根据业务需求调整表单字段和表格列
4. 实现相关的数据请求和处理方法

### 示例：基于BasePage创建列表页

```vue
<template>
  <div class="YourPageName">
    <!-- 复制BasePage的模板结构，并根据需要修改 -->
    <BaseLayout>
      <template #header>
        <!-- 自定义搜索条件 -->
      </template>
      <template #main>
        <!-- 自定义表格和操作 -->
      </template>
    </BaseLayout>
  </div>
</template>

<script>
// 参考BasePage，并根据需要修改
export default {
  name: 'YourPageName',
  // ...其他配置
}
</script>
```

### 示例：基于BaseDialog创建弹窗

```vue
<template>
  <div id="YourDialogName">
    <el-dialog 
      width="600px" 
      :visible.sync="isShow" 
      :close-on-click-modal="false" 
      :append-to-body="true" 
      @close="close"
    >
      <!-- 自定义标题和表单内容 -->
    </el-dialog>
  </div>
</template>

<script>
// 参考BaseDialog的脚本部分，并根据需要修改
export default {
  name: 'YourDialogName',
  // ...其他配置
}
</script>
```

## 注意事项

1. 模板组件依赖于项目中的公共组件，如BaseLayout、DateSelect等
2. 使用前请确保相关依赖已正确引入
3. 模板中的API调用需要替换为实际的接口
4. 表格列配置和表单验证规则需要根据实际业务调整
5. 模板提供了基础功能，可根据需要进行扩展和定制

## 最佳实践

1. 保持页面结构的一致性，遵循项目设计规范
2. 合理使用表单验证，提高数据质量
3. 优化表格性能，使用虚拟滚动处理大量数据
4. 合理设计搜索条件，提高用户查询效率
5. 为复杂操作提供清晰的用户引导

## 相关组件

- BaseLayout - 基础布局组件
- DateSelect - 日期选择组件
- ExportTable - 数据导出组件
- BaseTableForm - 表单组件
- Tooltips - 提示组件 