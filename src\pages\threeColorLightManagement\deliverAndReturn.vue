<template>
	<div id="deliverAndReturn">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<el-tabs v-model="activeTab">
			<el-tab-pane label="发货与退换记录" name="deliverAndReturn">
				<BaseLayout>
					<template #header>
						<span class="search-label">发货日期</span>
						<DateSelect
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>

						<el-checkbox-group v-model="searchForm.invoiceTypes" @change="queryTableData(1)">
							<el-checkbox label="1">售出</el-checkbox>
							<el-checkbox label="2">退回</el-checkbox>
						</el-checkbox-group>
						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<ExportBtn @trigger="openExport" />
							<el-button type="text" class="icon-third_searchC" v-popover:thSearch>查找</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['invoiceTime', 'endTime', 'modifyTime'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 状态/类型 -->
									<Tooltips
										v-else-if="item.colNo == 'invoiceType'"
										:cont-str="invoiceTypeMap[scope.row.invoiceType]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<Tooltips
										v-else-if="item.colNo == 'invoiceSimVOS' && scope.row.invoiceSimVOS"
										:cont-str="getSims(scope.row.invoiceSimVOS, 'sim')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<Tooltips
										v-else-if="item.colNo == 'tricolourProductNameVOS' && scope.row.tricolourProductNameVOS"
										:cont-str="getSims(scope.row.tricolourProductNameVOS, 'pro')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
		<!-- 放大镜查询 -->
		<el-popover v-model="searchPopver" ref="thSearch" placement="bottom-end" width="400" :visible-arrow="false">
			<!-- 放大镜搜索表单 -->
			<div class="searchPop p0 fs-12">
				<div class="searchPop-header">
					<span>查找条件</span>
					<i class="searchPop-header-close el-icon-close" @click.stop="searchPopver = false"></i>
				</div>
				<el-form :model="searchForm" label-width="80px" label-position="left" class="W100 pt10 pr20">
					<el-form-item label="发货单号" prop="invoiceNo">
						<el-input placeholder="请输入发货单号" v-model="searchForm.invoiceNo" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="发货人" prop="userName">
						<el-input placeholder="请输入发货人" v-model="searchForm.userName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="团队" prop="teamName">
						<el-input placeholder="请输入团队" v-model="searchForm.teamName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="业务顾问" prop="salesName">
						<el-input placeholder="业务顾问" v-model="searchForm.salesName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="代理商" prop="agentName">
						<el-input placeholder="请输入代理商" v-model="searchForm.agentName" size="mini"></el-input>
					</el-form-item>
				</el-form>
				<div class="mt20 border-top text-right">
					<el-button type="text" class="color-666" @click.stop="queryTableData(1)">确定</el-button>
				</div>
			</div>
		</el-popover>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		ExportTable,
		ExportBtn,
	},
	name: 'deliverAndReturn',
	data() {
		return {
			activeTab: 'deliverAndReturn',
			//日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableDataCopy: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '发货单号', colNo: 'invoiceNo', align: 'left', width: '100' },
				{ colName: '发货日期', colNo: 'invoiceTime', align: 'center', width: '100' },
				{ colName: '发货人', colNo: 'userName', align: 'left', width: '100' },
				{ colName: '业务类型', colNo: 'invoiceType', align: 'center', width: '100' },
				{ colName: '团队', colNo: 'teamName', align: 'left', width: '150' },
				{ colName: '业务顾问', colNo: 'salesName', align: 'left', width: '100' },
				{ colName: '代理商', colNo: 'agentName', align: 'left', width: '100' },
				{ colName: '三色灯编号', colNo: 'invoiceSimVOS', align: 'left', width: '' },
				{ colName: '产品', colNo: 'tricolourProductNameVOS', align: 'left', width: '' },
			],
			searchForm: {
				invoiceNo: '',
				teamName: '',
				userName: '',
				salesName: '',
				agentName: '',
				invoiceTypes: ['1', '2'],
			},

			invoiceTypeMap: {
				1: '售出',
				2: '退回',
			},
			searchPopver: false,
		};
	},
	// 监听属性 类似于data概念
	computed: {},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},

	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},

		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectInvoice'; //接口
			const DATA = JSON.stringify({
				...this.dateSelectObj,
				...this.searchForm,
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,

		getSims(arr, type) {
			let str = '';
			const sims = [];
			if (arr && arr.length > 0) {
				arr?.forEach(item => {
					if (type == 'sim') {
						sims.push(item.sim);
					} else {
						sims.push(item.productName);
					}
				});
				str = sims.join('、');
			}
			return str;
		},
		//数据导出
		openExport: _.debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					...this.dateSelectObj,
					...this.searchForm,
				}), //接口参数
				API: 'invoiceDownload', //导出接口
				downloadData: '发货单', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>
<style lang="scss" scoped>
#deliverAndReturn {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
