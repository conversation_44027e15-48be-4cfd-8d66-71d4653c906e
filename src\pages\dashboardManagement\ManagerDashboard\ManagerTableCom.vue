<template>
	<div class="ManagerTableCom">
		<div class="table-title flex-align-center pl2 pr2">
			<el-badge class="mini-badge pointer pr10" :value="tableInfo.badgeNum" :max="99" :hidden="!showBadge">
				<div class="flex-align-center" :title="tableInfo.id">
					<Tooltips class="fs-12 bolder" :cont-str="tableTitle" />
					<!-- <Tooltips v-if="tableInfo.count" class="fs-12 bolder" :cont-str="`(${tableInfo.count})`" /> -->
				</div>
			</el-badge>

			<Tooltips
				v-if="tableTitle == '昨日流量'"
				class="fs-12 bolder color-999 ml10"
				:class="showBadge ? 'ml20' : ''"
				:cont-str="`${inquiryCountDayTitle}`"
			/>

			<Tooltips
				v-if="totalAmount"
				class="fs-12 bolder color-999 ml10"
				:class="showBadge ? 'ml20' : ''"
				:cont-str="`${totalAmount} 万元`"
			/>
			<Tooltips v-if="tableInfo.subTitle" class="sub-title fs-12 color-999 mr10" :cont-str="tableInfo.subTitle" />

			<el-checkbox
				v-if="tableTitle == '未清首期款'"
				v-model="monthFilter"
				:true-label="1"
				:false-label="0"
				border
				size="mini"
				class="ml10"
				@change="filterTableData('month')"
				>仅显示本月
			</el-checkbox>
			<el-checkbox
				v-if="tableTitle == '未完成的项目'"
				v-model="delayFilter"
				:true-label="1"
				:false-label="0"
				border
				size="mini"
				class="ml10"
				@change="filterTableData('marks')"
				>仅显示延误项目
			</el-checkbox>

			<el-input
				v-if="['本月积分排名', '本月扣分排名', '工作质量待评价'].includes(tableTitle)"
				class="searchBox zoom-6 mb2"
				size="mini"
				v-model="pointsFilter"
				placeholder="姓名"
				clearable
				@input="filterTableData('uname')"
			></el-input>
			<!-- <Tooltips class="fs-12 bolder mr10" :cont-str="tableTitle" /> -->
			<el-button v-if="tableInfo.button" type="text" size="mini" class="p0 ml-auto" @click="openList">
				{{ tableInfo.button }}
			</el-button>

			<el-button
				v-if="tableTitle == '需求清单'"
				type="text"
				size="mini"
				class="ml10 el-icon-plus"
				@click="openDetail('DemandManagementDetail', null)"
			>
				添加
			</el-button>

			<el-button
				v-if="tableInfo.title == '工作不规范'"
				type="text"
				size="mini"
				class="p0 ml-auto"
				@click="$emit('openDialog', '积分')"
			>
				添加
			</el-button>
		</div>
		<!-- 表格主体 -->
		<div class="table-wrapper border border-radius-8 p2">
			<u-table
				ref="uTableRef"
				class="table-main"
				:height="500"
				:row-height="25"
				:row-class-name="getRowColor"
				:empty-text="tableInfo.emptyText || '暂无数据'"
				:total="tablePageForm.total"
				:page-size="tablePageForm.pageSize"
				:current-page="tablePageForm.currentPage"
				:page-sizes="tablePageForm.pageSizes"
				:pagination-show="tableData.length > 50 ? true : false"
				@handlePageSize="handlePageSize"
				show-header-overflow="title"
				use-virtual
				stripe
			>
				<u-table-column :label="' '" width="30" type="index" align="center"></u-table-column>
				<u-table-column
					v-for="item in tableInfo.tableColumn"
					:key="item.colNo"
					:label="item.colName"
					:prop="item.colNo"
					:align="item.align"
					:width="item.width"
					resizable
				>
					<!-- sortable="custom" -->
					<template slot-scope="{ row, column }">
						<!-- 昨日流量(与其他的不通用) -->
						<div v-if="tableTitle == '昨日流量'">
							<Tooltips
								v-if="['inquiryAmountDay', 'inquiryAmountMonth', 'unitPriceDay', 'unitPriceMonth'].includes(item.colNo)"
								:cont-str="!Number(row[item.colNo]) || row[item.colNo] == '/' ? '-' : '¥' + Number(row[item.colNo]).toFixed(2)"
								:cont-width="column.realWidth - 20"
							/>
							<Tooltips v-else :cont-str="row[item.colNo] || '-'" :cont-width="column.realWidth - 20" />
						</div>

						<!-- 各种日期（默认不显示分秒 lineM ） -->
						<Tooltips
							v-else-if="item.colName.includes('时间')"
							:cont-str="dateFormat(row[item.colNo], 'MDHM')"
							:cont-width="column.realWidth - 20"
						/>

						<Tooltips
							v-else-if="item.colName.includes('到期日')"
							:cont-str="dateFormat(row[item.colNo], 'MD')"
							:cont-width="column.realWidth - 20"
						/>

						<Tooltips
							v-else-if="item.colNo.includes('validTo')"
							:cont-str="dateFormat(row[item.colNo], 'line')"
							:cont-width="column.realWidth - 20"
						/>

						<Tooltips
							v-else-if="item.colNo.includes('Month')"
							:cont-str="dateFormat(row[item.colNo], 'YM')"
							:cont-width="column.realWidth - 20"
						/>

						<Tooltips
							v-else-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
							:cont-str="dateFormat(row[item.colNo], 'MD')"
							:cont-width="column.realWidth - 20"
						/>

						<!-- ------------------询盘相关----------------- -->
						<!-- 询盘编号 -->
						<Tooltips
							v-else-if="item.colNo == 'number'"
							class="hover-green green"
							:class="getRowColor({ row })"
							@click.native="openDetail('InquiryDetail', '修改', row)"
							:cont-str="row[item.colNo] || '未知'"
							:cont-width="column.realWidth - 20"
						/>
						<!-- 区域 -->
						<Tooltips
							v-else-if="item.colNo == 'region'"
							:cont-str="jointString('/', row.province, row.city, row.area)"
							:cont-width="column.realWidth"
						/>
						<!-- 计划 -->
						<Tooltips
							v-else-if="item.colNo == 'nextPlan'"
							:cont-str="jointString('：', dateFormat(row.nextStep, 'MD'), row.nextPlan)"
							:cont-width="column.realWidth"
						/>
						<!-- 客户(公司名) -->
						<Tooltips
							v-else-if="item.colNo == 'companyName'"
							:class="row.dmid || row.idid ? 'hover-green' : ''"
							@click.native="
								row.dmid ? openDetail('ContractDetail', row) : row.idid ? openDetail('InquiryDetail', '修改', row) : () => {}
							"
							:cont-str="row.companyName || row.registeredBusinessName || row.customerName"
							:cont-width="column.realWidth"
						/>
						<!-- 客户(团队名) -->
						<Tooltips
							v-else-if="item.colNo == 'teamName'"
							:class="row.tid ? 'hover-green' : ''"
							@click.native="row.tid ? openDetail('TeamDetail', row) : () => {}"
							:cont-str="row.teamName || row.teamFullname"
							:cont-width="column.realWidth"
						/>
						<!-- 责任人/业务/姓名（去除工号） -->
						<Tooltips
							v-else-if="['本月积分排名', '本月扣分排名'].includes(tableTitle) && ['uname'].includes(item.colNo)"
							:cont-str="row[item.colNo] || ''"
							:cont-width="column.realWidth"
						/>
						<Tooltips
							v-else-if="['uname', 'salesName', 'salesmanName', 'implementName'].includes(item.colNo) && row[item.colNo]"
							:cont-str="row[item.colNo].replace(/^\d+/, '')"
							:cont-width="column.realWidth"
						/>

						<div v-else-if="item.colNo == 'salesman' && typeof row.salesman === 'object'">
							<Tooltips :cont-str="row.salesman?.userName.replace(/^\d+/, '')" :cont-width="column.realWidth" />
						</div>

						<!-- 阶段 -->
						<Tooltips
							v-else-if="item.colNo == 'stage'"
							:class="row.dmid ? 'hover-green' : ''"
							@click.native="row.dmid ? openDetail('DeliveryDetail', row) : () => {}"
							:cont-str="row.stage || ''"
							:cont-width="column.realWidth"
						/>

						<!-- 金额 -->
						<Tooltips
							v-else-if="tableTitle === '未清首期款' && item.colNo == 'amount' && row.amount"
							:cont-str="jointString(' ', dateFormat(row.complateMonth, 'MD'), convertToMillion(row.amount))"
							:cont-width="column.realWidth"
						/>
						<Tooltips
							v-else-if="['amount', 'receiptsAmount'].includes(item.colNo) && row[item.colNo]"
							:cont-str="convertToMillion(row[item.colNo])"
							:cont-width="column.realWidth"
						/>
						<Tooltips
							v-else-if="['estimatedAmount', 'dealAmount'].includes(item.colNo) && row[item.colNo]"
							:cont-str="row[item.colNo].toFixed(2)"
							:cont-width="column.realWidth"
						/>

						<!-- 咨询未完成（事项） -->
						<Tooltips
							v-else-if="item.colNo == 'incompleteConsulting'"
							class="hover-green"
							@click.native="openDetail('InquiryDetail', '修改', row)"
							:cont-str="getIncompleteConsulting(row)"
							:cont-width="column.realWidth"
						/>
						<!-- ---------------积分相关----------------- -->
						<!-- 审查状态 0: 无需检查, 1: 待评价, 2: 不合格, 3: 合规带问题, 4: 合格, 5: 优秀, 10 免检 -->
						<div v-else-if="item.colNo == 'auditStatus'" class="fs-12">
							<Tooltips
								:class="
									{ 0: 'color-999', 1: 'red', 2: 'red', 3: 'orange', 4: 'green', 5: 'green', '10': 'color-999' }[row.auditStatus]
								"
								:cont-str="jointString('：', auditStatusMap[row.auditStatus], row.auditMemo)"
								:cont-width="column.realWidth"
							/>
						</div>
						<!-- 工作项 -->
						<Tooltips
							v-else-if="item.colNo == 'operation'"
							class="hover-green"
							@click.native="openDetail('InquiryDetail', '修改', row)"
							:cont-str="jointString('-', row.operation, row.idNumber)"
							:cont-width="column.realWidth - 20"
						/>
						<Tooltips
							v-else-if="item.colNo == 'operations'"
							class="hover-green"
							@click.native="onOpenDetail(row)"
							:cont-str="getOperation(row)"
							:cont-width="column.realWidth"
						/>

						<!-- 工作质量待评价：工作项明细 -->
						<div v-else-if="item.colNo == 'operation_details'" class="W100 flex-align-center">
							<div v-for="(dItem, dIndex) in getOperationDetails(row)" :key="dIndex" class="W50 flex-align-center">
								<!-- 询盘录音播放器 -->
								<InquiryAudioPlayer
									v-if="dItem.type == '咨询录音' && dItem.content"
									class="min-w-200 zoom-7"
									:audioUrl="dItem.content"
									:idid="dItem.idid"
								/>
								<Tooltips class="mr10" v-else :cont-str="dItem.content" :cont-width="200" />
							</div>
						</div>
						<!-- 积分评价 -->
						<div v-else-if="item.colNo == 'operation_audit'">
							<el-button type="text" size="mini" class="el-icon-chat-round" @click="$emit('openDialog', '评价', row)"></el-button>
						</div>
						<!-- 积分排名 -->
						<Tooltips
							v-else-if="item.colNo == 'totalPoints'"
							class="hover-green"
							@click.native="openDetail('PointsDetails', row)"
							:cont-str="row.totalPoints"
							:cont-width="column.realWidth - 20"
						/>

						<!-- 未完成的项目进度 -->
						<div
							v-else-if="item.colNo == 'deliveryScheduleStateVOS'"
							:class="row.dmid ? 'pointer' : ''"
							@click="row.dmid ? openDetail('DeliveryDetail', row) : () => {}"
						>
							<StepsCom :rowData="row" />
						</div>
						<!-- 出差人 -->
						<Tooltips
							v-else-if="item.colNo == 'tripUsers' && row[item.colNo]"
							:cont-str="Object.values(row[item.colNo]).join(',')"
							:cont-width="column.realWidth - 20"
						/>
						<!-- ---------------团队----------------- -->
						<!-- 使用版本 -->
						<Tooltips
							v-else-if="item.colNo == 'version'"
							:cont-str="jointString('/', versionMap[row.version], row.simLimit)"
							:cont-width="column.realWidth - 20"
						/>
						<!-- 需求 -->
						<Tooltips
							v-else-if="item.colNo == 'demandDocumentName'"
							class="hover-green"
							@click.native="openDetail('DemandManagementDetail', row)"
							:cont-str="row[item.colNo]"
							:cont-width="column.realWidth - 20"
						/>
						<!-- 需求状态 -->
						<Tooltips
							v-else-if="tableTitle == '需求清单' && item.colNo == 'status'"
							:cont-str="demandStatusMap[row[item.colNo]]"
							:cont-width="column.realWidth - 20"
						/>

						<!-- 默认显示 -->
						<Tooltips v-else-if="row[item.colNo]" :cont-str="row[item.colNo]" :cont-width="column.realWidth - 20" />
					</template>
				</u-table-column>
				<!-- 其他列/操作 -->
				<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDialogRow(row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(row)"></el-button>
                </template>
              </u-table-column> -->
			</u-table>
		</div>
	</div>
</template>

<script>
import { debounce, dateFormat, jointString, sortTableData, accAdd, deepClone } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import StepsCom from '../DeliveryStepsCom';
import { demandStatusMap } from '@/assets/js/projectSource';
import InquiryAudioPlayer from '@/components/InquiryAudioPlayer'; //询盘录音播放器

export default {
	// import引入的组件需要注入到对象中才能使用
	components: { StepsCom, InquiryAudioPlayer },
	name: 'ManagerTableCom', //组件名应同路由名(否则keep-alive不生效)
	props: {
		tableInfo: Object,
	},
	data() {
		return {
			// 0: 无需检查, 1: 待评价, 2: 不合格, 3: 合规带问题, 4: 合格, 5: 优秀, 10 免检
			auditStatusMap: {
				0: '无需检查',
				1: '待评价',
				2: '不合格',
				3: '合规带问题',
				4: '合格',
				5: '优秀',
				10: '免检',
			},
			versionMap: {
				1: '标准',
				2: 'OEE',
				4: '微信',
			},
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 100, 500],
			},
			monthFilter: false,
			delayFilter: false,
			pointsFilter: '',

			demandStatusMap, // 需求状态
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 表格标题
		tableTitle() {
			return this.tableInfo.title;
		},
		// 合计金额
		totalAmount() {
			if (!this.tableData.length) return 0;
			const { title } = this.tableInfo;
			const amountMap = {
				未清首期款: 'amount',
				昨日签单: 'dealAmount',
				昨日收款: 'receiptsAmount',
				本月预计成交: 'estimatedAmount',
			};

			if (!amountMap[title]) return 0;

			return this.tableData.reduce((total, row) => {
				let amount = Number(row[amountMap[title]] || 0);
				if (title === '未清首期款') amount = this.convertToMillion(amount);
				if (title === '昨日收款') amount = this.convertToMillion(amount);
				return accAdd(total, amount, 2);
			}, 0);
		},
		// 昨日流量 子标题（昨日总数 昨日总消费 ）
		inquiryCountDayTitle() {
			let count = 0;
			let amount = 0;
			this.tableData.reduce((total, row) => {
				count += Number(row['inquiryCountDay'] || 0);
				amount += Number(row['inquiryAmountDay'] || 0);
			}, 0);

			// return  `${count} 个 \t ${amount} 元`;
			return `${count} 个`;
		},

		// 显示badge
		showBadge() {
			if (
				[
					'工作质量待评价',
					'今日人员外出',
					'60天内即将流失的客户',
					'本月预计成交',
					'昨日收款',
					'昨日签单',
					'未完成的咨询',
				].includes(this.tableTitle)
			) {
				return false;
			}
			return this.tableInfo.badgeNum;
		},
	},
	// 监控data中的数据变化
	watch: {
		'tableInfo.data'(newVal) {
			if (this.monthFilter) {
				this.filterTableData('month');
			} else if (this.delayFilter) {
				this.filterTableData('marks');
			} else if (this.pointsFilter) {
				this.filterTableData('uname');
			} else {
				this.initTableData(newVal);
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	activated() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开工作项明细
		onOpenDetail(row) {
			const { tid, dmid, idid, pmtid, pmid } = row;
			if (tid) return this.openDetail('TeamDetail', row); //团队
			if (dmid) return this.openDetail('DeliveryDetail', row); //交付
			if (idid) return this.openDetail('InquiryDetail', '修改', row); //询盘
			if (pmtid) return this.openDetail('TaskDetail', row, '查看研发任务'); //研发任务
			if (pmid) return this.openDetail('ProjectDetail', row); //研发项目
		},
		// 打开明细
		openDetail(ref, ...args) {
			this.$emit('openDetail', ref, ...args);
			this.$emit('getTableOptions', this.tableData);
		},
		// 打开清单
		openList() {
			const { button } = this.tableInfo;
			const buttonActions = {
				询盘清单: 'InquriyList',
				合同管理: 'ContractTable',
				交付管理: 'DeliveryManagement',
				项目总览: 'DeliveryOverview',
				回款与开票: 'PaybackManagement',
				客户健康度管理: 'ActivityRating',
				到期管理: 'ExpirationManagement',
				出差审批: 'TravelApproval',
				质量评价: 'IndividualPoints',
				询盘趋势: 'InquiryTrend',
				需求管理: 'DemandManagement',
			};

			if (buttonActions[button]) {
				this.$emit('openList', buttonActions[button], 'openMove');
			} else {
				this.$message.warning(`打开${button}的功能，我们正在开发中！`);
			}
		},
		// 获取工作项信息
		getOperation(row) {
			const { operation, idid, dmid, tid, pmid } = row;
			let infos = '';
			if (tid && row.teamVO) {
				infos = this.jointString(' / ', row.teamVO.teamName, this.dateFormat(row.teamVO.validTo, 'line'));
			} else if (dmid && row.deliverStageManagement) {
				infos = this.jointString(
					' / ',
					row.deliverStageManagement.stage,
					row.deliverStageManagement.standard,
					this.dateFormat(row.deliverStageManagement.complateMonth, 'line'),
				);
			} else if (idid && row.inquiryDocumentary) {
				infos = this.jointString(' / ', row.idNumber, row.inquiryDocumentary.companyName);
			} else if (pmid && row.projectManagementTask) {
				infos = this.jointString(' / ', row.projectManagementTask.projectName);
			} else {
				infos = row.auditMemo || '';
			}

			return this.jointString(' ', dateFormat(row.pointsDate, 'MDHM'), row.uname?.replace(/^\d+/, ''), ` ${operation}-`, infos);
		},
		// 行样式（颜色显示）
		getRowColor({ row }) {
			if (this.tableTitle == '本月预计成交') {
				const expectedMonth = this.$moment(row.expectedMonth).startOf('month').valueOf();
				const nowMonth = this.$moment(new Date()).startOf('month').valueOf(); // 获取当前月第一天的时间戳
				if (expectedMonth < nowMonth) return 'red'; // 预计成交月份小于当前月时显示红色
			} else if (this.tableTitle == '未清首期款') {
				const complateMonth = this.$moment(row.complateMonth).startOf('month').valueOf();
				const nowMonth = this.$moment(new Date()).startOf('month').valueOf(); // 获取当前月第一天的时间戳
				if (complateMonth == nowMonth) return 'blue'; // 预计成交月份等于当前月时显示蓝色
				if (complateMonth < nowMonth) return 'red'; // 预计成交月份小于当前月时显示红色
				if (complateMonth > nowMonth) return ''; // 预计成交月份小于当前月时正常显示
			} else if (this.tableTitle == '近30天已流失的客户' || this.tableTitle == '60天内即将流失的客户') {
				const validToMonth = this.$moment(row.validTo).startOf('month').valueOf();
				const nowMonth = this.$moment(new Date()).startOf('month').valueOf(); // 获取当前月第一天的时间戳
				if (validToMonth == nowMonth) return 'orange'; // 预计成交月份等于当前月时显示黄色
				// if (validTo < nowMonth) return 'red'; // 预计成交月份小于当前月时显示红色
				if (validToMonth > nowMonth) return ''; // 预计成交月份小于当前月时正常显示
			} else if (this.tableTitle == '未完成的咨询') {
				if (!row.hoursAgo) return '';
				// 大于等于24小时显示红色 小于24小时大于等于12小时显示黄色 小于12小时大于等于4小时显示蓝色 小于4小时正常
				const hoursAgo = row.hoursAgo;
				if (hoursAgo >= 24) {
					return 'red';
				} else if (hoursAgo >= 12) {
					return 'orange';
				} else if (hoursAgo >= 4) {
					return 'blue';
				} else {
					return '';
				}
			}
			const keywords = ['不', '期', '失', '未回复', '跟单'];
			return keywords.some(keyword => this.tableTitle.includes(keyword)) ? 'red' : undefined;
		},
		// 为咨询询盘信息
		getIncompleteConsulting(row) {
			const currentTime = new Date().getTime();
			const timeDiff = (currentTime - row.createTime) / (1000 * 60 * 60); // 计算出距离现在多少个小时
			const hoursAgo = Math.floor(timeDiff);
			row.hoursAgo = hoursAgo;
			return this.jointString(' ', row.number, hoursAgo + 'h', row.consultName?.replace(/^\d+/, ''));
		},
		// 将金额转成万单位
		convertToMillion(value, d = 2) {
			if (!value || Number(value) <= 0) return '';
			// Convert the value to million units
			const valueInMillion = value / 10000;

			// Round to two decimal places
			const resultVal = (Math.round(valueInMillion * 100) / 100).toFixed(2);
			const decimalPart = resultVal.split('.')[1];
			const decimalsToAdd = 2 - decimalPart.length;
			const finalValue = decimalsToAdd > 0 ? resultVal + '0'.repeat(decimalsToAdd) : resultVal;
			return finalValue;
		},
		// 获取工作项详情内容
		getOperationDetails(row) {
			const { pointsSource, clientKeepRecord, inquiryDocumentary, documentaryRecords } = row; //业务
			const { deliverStageManagementInfo } = row; //交付
			// console.log(pointsSource, { row });
			if (pointsSource.includes('备案') && clientKeepRecord) {
				const { clientNo, registeredBusinessName } = clientKeepRecord;
				return [{ type: '客户备案', content: `${clientNo}：${registeredBusinessName}` }];
			} else if (pointsSource.includes('咨询') && inquiryDocumentary) {
				const { consultingOther, callRecording, idid } = inquiryDocumentary;
				return [
					{ type: '咨询内容', content: consultingOther, idid },
					{ type: '咨询录音', content: callRecording, idid },
				];
			} else if (pointsSource.includes('跟单') && documentaryRecords) {
				const { content, nextStep, nextPlan } = documentaryRecords;
				return [
					{ type: '跟单内容', content: content },
					{ type: '跟单计划', content: `${dateFormat(nextStep)}：${nextPlan}` },
				];
			} else if (pointsSource.includes('计划') && deliverStageManagementInfo) {
				const { stage } = deliverStageManagementInfo;
				return [{ type: '交付计划', content: stage }];
			} else {
				return [];
			}
		},
		// 初始化表格数据/分页等
		initTableData(tableData) {
			this.tableData = deepClone(tableData);
			this.tablePageForm = {
				total: tableData.length,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 100, 500],
			};
			const { currentPage: page, pageSize: size } = this.tablePageForm;
			this.handlePageSize({ page, size });
		},
		// 过滤表格数据
		filterTableData: debounce(function (type) {
			if (type === 'month' && this.monthFilter) {
				// 过滤本月数据
				return this.initTableData(this.tableInfo.data.filter(i => new Date(i.complateMonth).getMonth() == new Date().getMonth()));
			} else if (type === 'marks' && this.delayFilter) {
				// 过滤延误的项目
				return this.initTableData(this.tableInfo.data.filter(i => i.marks == 0));
			} else if (type === 'uname' && this.pointsFilter) {
				// 过滤名称
				return this.initTableData(this.tableInfo.data.filter(i => i.uname.includes(this.pointsFilter)));
			}

			this.initTableData(this.tableInfo.data);
		}),
		// 分页(不通过接口分页)
		handlePageSize({ page, size }) {
			const tableData = this.tableData.slice((page - 1) * size, page * size);
			this.$nextTick(() => {
				this.$refs.uTableRef?.reloadData(tableData);
				this.$refs.uTableRef?.doLayout();
			});
		},
		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss">
.ManagerTableCom {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;

	.border-radius-8 {
		border-radius: 8px;
	}
	.zoom-7 {
		zoom: 0.7;
	}
	.zoom-6 {
		zoom: 0.6;
	}
	.table-title {
		color: #555;
		height: 20px;
		.mini-badge {
			.el-badge__content {
				z-index: 88;
				zoom: 0.75;
				padding: 0px 5px;
				/* right: 12px; */
				top: 6.5px;
			}
		}

		.el-checkbox.el-checkbox--mini.is-bordered {
			border: none;
		}
	}

	// 表格样式调整
	.table-wrapper {
		width: 100%;
		height: calc(100% - 20px) !important;
		&:hover {
			border-color: #1e9d6f !important;
		}

		// 表格主体
		.table-main {
			width: 100% !important;
			height: 100% !important;
			min-height: 50px !important;
			// height: calc(100% - 22px) !important;

			.el-table__body td {
				height: 25px !important;
				padding: 0px;
			}
			.el-table__body th {
				height: 25px !important;
				padding: 0px;
			}
			.is-leaf:not(:last-child) {
				padding: 0 !important;
			}
			.el-table__empty-block {
				min-height: 40px;
				width: 100% !important;
				.el-table__empty-text {
					line-height: normal;
				}
			}

			// 表格底部线去除
			.el-table::before {
				height: 0px;
				background: transparent;
			}
		}

		// 表格分页
		.myPagination {
			zoom: 0.7;
		}
	}
}
</style>
