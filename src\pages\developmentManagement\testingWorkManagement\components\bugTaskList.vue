<template>
	<div id="bugTaskList" :class="moveToggle ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<!-- 表格内容 -->
		<BaseLayout :showHeader="false">
			<template #main>
				<div class="table-toolbar">
					<div class="mr-auto">
						<el-button v-if="searchForm.userName" type="text" class="el-icon-user">{{ searchForm.userName }}</el-button>
						<el-button type="text" class="el-icon-monitor">
							捉虫清单 （ {{ $moment(searchForm.startDate).format('YYYY-MM-DD') }} -
							{{ $moment(searchForm.endDate).format('YYYY-MM-DD') }} ）
						</el-button>
					</div>

					<el-button type="text" class="el-icon-arrow-left" @click="moveToggle = false">返回</el-button>
				</div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable
						resizable
					>
						<template slot-scope="scope">
							<div
								v-if="
									item.colNo == 'startTime' ||
									item.colNo == 'endTime' ||
									item.colNo == 'testPassTime' ||
									item.colNo == 'actualEndTime'
								"
							>
								<Tooltips
									:cont-str="dateFormat(scope.row[item.colNo], 'YMD')"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>
							<div v-else-if="item.colNo == 'status'">
								<Tooltips
									:cont-str="statusMap[scope.row[item.colNo]]"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>
							<div v-else-if="item.colNo == 'turnQuality' || item.colNo == 'taskOtd'">
								<Tooltips
									style="color: red; font-weight: bold"
									:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '%' : ''"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>
							<div v-else-if="item.colNo == 'taskClassify'">
								<Tooltips
									:cont-str="taskClassifyMap[scope.row[item.colNo]]"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>
							<div v-else-if="item.colNo == 'taskLevel'">
								<Tooltips
									:cont-str="taskLevelMap[scope.row[item.colNo]]"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>

							<div v-else-if="item.colNo == 'taskNo'">
								<!-- class="hover-green"
									@click.native="openDialog(scope.row, '任务详情信息')" -->
								<Tooltips
									:style="{ color: colorMap[scope.row.status], 'font-weight': 'bold' }"
									:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
									:cont-width="(scope.column.width || scope.column.realWidth) - 18"
								>
								</Tooltips>
							</div>
							<!-- || item.colNo == 'projectTurnCheck' -->
							<div v-else-if="item.colNo == 'mentionUid' || item.colNo == 'productUid'">
								<Tooltips
									v-if="scope.row[item.colNo]?.userName"
									:cont-str="scope.row[item.colNo].userName"
									:cont-width="(scope.column.width || scope.column.realWidth) - 18"
								>
								</Tooltips>
							</div>

							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	props: { parentType: String },
	name: 'bugTaskList',
	data() {
		return {
			tableColumn: [
				{ colName: '任务编号', colNo: 'taskNo', align: 'left', width: '110' },
				{ colName: '任务', colNo: 'taskName', align: 'left', width: '' },
				{ colName: '开发人员', colNo: 'productUid', align: 'left', width: '' },
				{ colName: '要求转测日期', colNo: 'endTime', align: 'center', width: '' },
				{ colName: '实际转测日期', colNo: 'actualEndTime', align: 'center', width: '' },
				{ colName: '任务状态', colNo: 'status', align: 'left', width: '80' },
				{ colName: 'Bug数量', colNo: 'bugNum', align: 'right', width: '' },
				{ colName: '测试人', colNo: 'projectTurnCheck', align: 'left', width: '' },
				{ colName: '项目名称', colNo: 'projectName', align: 'left', width: '' },
			], //当前显示列
			searchForm: {
				userName: '',
			},
			colorMap: {
				0: '#ec808d', //红 开发延误
				1: '#bababa', //灰 计划中
				2: '#ab47bc', //紫 开发中
				3: '#2196f3', //蓝 已转测
				4: '#28d094', //绿 完成
				5: '#facd91', //橙 转测不通过
				6: '#ec808d', //红 测试延误
			},

			statusMap: {
				0: '延误',
				1: '计划中',
				2: '开发中',
				3: '已转测',
				4: '完成',
				5: '转测不通过',
				6: '测试延误',
			},

			taskClassifyMap: {
				0: '需求',
				1: '优化',
				2: 'Bug',
				3: '杂项',
			},
			taskLevelMap: {
				0: '高',
				1: '中',
				2: '低',
			},

			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 200, 500, 1000],
			},

			moveToggle: false, //滑动控制
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		moveToggle(newVal) {
			if (newVal) {
				this.queryTableData(1);
			} else {
				this.tableData = [];
				this.tablePageForm.total = 0;
				this.$refs.uTableRef.reloadData(this.tableData);
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		openDialog(row) {
			this.$refs.TaskDetail.openDetail(row);
		},
		queryTableData: _.debounce(function (type) {
			if (!this.moveToggle) {
				this.tableData = [];
				return;
			}
			type && (this.tablePageForm.currentPage = 1);
			const str = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.searchForm,
			});
			this.$axios
				.selectBugCountInfoVO(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							this.$refs.uTableRef?.reloadData(this.tableData);
							this.$refs.uTableRef?.doLayout();
						});
					} else {
						this.tableData = [];
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectBugCountInfoVO |' + error);
				});
		}),
		//日期format
		dateFormat: _.dateFormat,
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//自定义排序
		sortChange({ prop, order }) {
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop == 'productUid' || prop == 'projectTurnCheck' || prop == 'mentionUid' || prop == 'technicalManager') {
						if (!a?.userName) return -1;
						if (!b?.userName) return 1;
						return a?.userName.localeCompare(b?.userName, 'zh-CN');
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},

		openTaskList(searchForm) {
			this.searchForm = _.deepClone(searchForm);
			this.moveToggle = true;
			this.queryTableData(1);
		},
	},
};
</script>

<style lang="scss" scoped>
#bugTaskList {
	.table-main {
		width: 100%;
		height: 100%;
		min-height: 400px;
		height: calc(100vh - 230px) !important;
	}
}
</style>
