/* 差旅管理路由 */

const TravelApproval = () => import('@/pages/travelManagement/TravelApproval/TravelApproval.vue'); //出差审批
const ExpenseReimbursement = () => import('@/pages/travelManagement/ExpenseReimbursement.vue'); //费用报销
const ReimbursementReview = () => import('@/pages/travelManagement/ReimbursementReview.vue'); //报销审核

const routers = [
	{
		//出差审批
		path: '/TravelApproval',
		name: 'TravelApproval',
		component: TravelApproval,
		meta: {
			parentTitle: '差旅管理',
			title: '出差审批',
		},
	},
	{
		//费用报销
		path: '/ExpenseReimbursement',
		name: 'ExpenseReimbursement',
		component: ExpenseReimbursement,
		meta: {
			parentTitle: '差旅管理',
			title: '费用报销',
		},
	},
	{
		//报销审核
		path: '/ReimbursementReview',
		name: 'ReimbursementReview',
		component: ReimbursementReview,
		meta: {
			parentTitle: '差旅管理',
			title: '报销审核',
		},
	},
];

export default routers;
