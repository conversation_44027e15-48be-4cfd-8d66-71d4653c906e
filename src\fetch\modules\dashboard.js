/* 仪表盘 API */
const urlList = [
	'/background/web/AdminDashboardController/fetchSalesmanDashboard', //业务仪表盘
	'/background/web/AdminDashboardController/fetchManagerDashboard', //管理仪表盘
	'/background/web/AdminDashboardController/fetchImplementDashboard', //交付仪表盘
	'/background/web/AdminDashboardController/selectDevelopDashboard', //研发仪表盘
	'/background/web/AdminDashboardController/selectDeveloManagerpDashboard', //研发管理员仪表盘
];

// 重名函数映射
const apiNameMap = {};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (
		[
			'fetchSalesmanDashboard',
			'fetchManagerDashboard',
			'fetchImplementDashboard',
			'selectDevelopDashboard',
			'selectDeveloManagerpDashboard',
		].includes(urlName)
	) {
		timeout = 60000;
	}

	return { urlName, url, timeout };
});

export default apiList;
