<!-- 展开收起组件（一般用于列表展示，如日志列表等，默认展示3条，点击展开更多） -->
<template>
	<div class="expandable-list">
		<div v-for="(item, index) in showItems" :key="index">
			<slot :item="item" :index="index"></slot>
		</div>
		<span
			v-show="moreItems > 0"
			@click="toggleExpand"
			class="toggle-button"
			:class="isExpanded ? 'blue el-icon-arrow-up' : 'color-999 el-icon-arrow-right'"
		>
			{{ isExpanded ? '收起' : '展开更多' }} ({{ moreItems }} 条信息)
		</span>
	</div>
</template>

<script>
export default {
	name: 'ExpandableList',
	props: {
		items: {
			type: Array,
			required: true,
		},
		defaultCount: {
			type: Number,
			default: 3,
		},
	},
	data() {
		return {
			isExpanded: false,
		};
	},
	computed: {
		moreItems() {
			return this.items.length - this.defaultCount;
		},
		showItems() {
			if (this.isExpanded) return this.items;
			return this.items.slice(0, this.defaultCount);
		},
	},
	methods: {
		toggleExpand() {
			this.isExpanded = !this.isExpanded;
		},
	},
};
</script>

<style lang="scss" scoped>
.expandable-list {
	.toggle-button {
		padding: 0;
		font-size: 12px;
		cursor: pointer;
		color: #007bff;
		background: none;
		border: none;
	}
}
</style>
