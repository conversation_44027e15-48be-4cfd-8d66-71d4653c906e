const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const { exec } = require('child_process');

// 辅助函数：格式化日期为 _YYMMDD 形式
const formatDate = date => {
	const year = date.getFullYear().toString().slice(-2);
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	return `_${year}${month}${day}`;
};

// 配置选项
const config = {
	customName: 'OPS', // 自定义文件名前缀
	appendCounter: true, // 在文件名中添加计数器（默认） 关闭则覆盖
}; 

const formattedDate = formatDate(new Date()); // 生成日期标识
let outputFileName = `${config.customName}${formattedDate}.zip`; // 设置自定义文件名
const outputDir = path.join(__dirname, '../dist'); // 设置输出路径为 dist 目录
let outputPath = path.join(outputDir, outputFileName); // 生成完整路径

// 检查文件是否存在并处理文件名
if (config.appendCounter) {
	let counter = 2;
	while (fs.existsSync(outputPath)) {
		outputFileName = `${config.customName}${formattedDate}_${counter}.zip`; // 添加计数器
		outputPath = path.join(outputDir, outputFileName);
		counter++;
	}
}

const output = fs.createWriteStream(outputPath);
const archive = archiver('zip', {
	zlib: { level: 9 }, // 设置压缩级别
});

console.log(`正在压缩打包后的文件...`);
output.on('close', () => {
	console.log(`✨ 打包文件压缩完成，总共 ${(archive.pointer() / (1024 * 1024)).toFixed(2)}M ，zip压缩包输出到 ${outputPath} `);
});

archive.on('error', err => {
	throw err;
});

// 开始压缩
archive.pipe(output);
archive.glob('**/*', {
	cwd: outputDir,
	ignore: ['**/*.zip'], // 忽略所有 zip 文件（防止重复压缩）
});
archive.finalize().then(() => {
	// 压缩完成后打开文件夹
	exec(`start "" "${outputDir}"`, err => {
		// Windows 系统打开文件夹
		if (err) {
			console.error(`打开文件夹时出错: ${err}`);
		} else {
			console.log(`文件夹已打开: ${outputDir}`);
		}
	});
});
