<template>
	<!-- 基本信息组件（暂未迁移） -->
	<div id="BasicInfo"> </div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import eventBus from './eventBus';

export default {
	name: 'BasicInfo',
	components: {},

	props: {
		// // 询盘主键
		idid: {
			type: [String, Number],
			default: '',
		},
		// 是否公海询盘
		isPublicInquiry: { type: Boolean, default: false },
		//实施顾问列表(标签：实施)
		implementList: {
			type: Array,
			default: () => [],
		},
		talktradeList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			dialogEdit: false,

			detailForm: {}, //事件总线表单
			basicInfoFormCopy: {}, //克隆数据

			formRules: {},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 咨询表单
		basicInfoForm() {
			return {
				businessOpportunityQuality: this.detailForm.businessOpportunityQuality,
				estimatedAmount: this.detailForm.estimatedAmount,
				expectedMonth: this.detailForm.expectedMonth,
				feedback: this.detailForm.feedback,
				implement: this.detailForm.implementUid,
				// quality: this.detailForm.quality,
				stage: this.detailForm.stage,
				// callRecording: this.detailForm.callRecording,
				contractUrl: this.detailForm.contractUrl,
				contractName: this.detailForm.contractName,
				idid: this.detailForm.idid || this.idid,
				ckrid: this.detailForm.ckrid,
				talktrade: this.detailForm.talktradeUid,
				// consult: this.detailForm.consultUid,
				losingOrderReasons: this.detailForm.losingOrderReasons,
			};
		},
		// 是否修改
		isUpdate() {
			const FLAG = JSON.stringify(this.feedbackForm) !== JSON.stringify(this.basicInfoFormCopy);
			// console.log('feedbackForm_isUpdate', FLAG);
			return FLAG;
		},
		// 阶段 - 丢单
		isLosing() {
			return this.detailForm.stage == 10;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {
		// 订阅事件（注意这里要加多一层parent 使其与发布事件一致）
		eventBus.$on(`updateDetailForm_${this.$parent.$parent.$options.name}`, detailForm => {
			// 处理事件，更新本地的 detailForm 对象
			this.detailForm = detailForm;
			this.basicInfoFormCopy = _.deepClone(this.feedbackForm); //克隆数据
		});
	},
	beforeDestroy() {
		eventBus.$off(`updateDetailForm_${this.$parent.$parent.$options.name}`);
	},
	destroyed() {
		this.detailForm = null;
		this.basicInfoFormCopy = null;
	},
	// 方法集合
	methods: {
		// 删除
		delFile() {
			this.$confirm('注意,该询盘合同删除后需重新上传, 是否继续操作?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					this.detailForm.contractName = '';
					this.detailForm.contractUrl = '';
					this.saveEdit();
				})
				.catch(() => {
					this.$message.info('已取消操作！');
				});
		},
		// 保存基本信息(修改询盘业务反馈信息)
		saveEdit: _.debounce(async function () {
			if (_.checkRequired(this.feedbackForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}

			if (this.detailForm.stage == 5 && !this.detailForm.contractUrl) {
				return this.$message.warning('当前阶段为已签单，请上传合同后再保存！');
			}
			if (this.detailForm.stage == 5 && (!this.detailForm.expectedMonth || !this.detailForm.estimatedAmount)) {
				return this.$message.warning('当前阶段为已签单，请填写成交月份和成交金额后再保存！');
			}

			const API = 'feedbackUpdate';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.feedbackForm, consult: this.detailForm.consultUid }));
				if (res.data.success) {
					this.basicInfoFormCopy = _.deepClone(this.feedbackForm); //克隆数据
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		dateFormat: _.dateFormat,
	},
};
</script>
<style lang="scss">
#BasicInfo {
}
</style>
