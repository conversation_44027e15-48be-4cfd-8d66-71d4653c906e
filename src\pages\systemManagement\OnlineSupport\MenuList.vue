<template>
	<!--  研发在线支持工作台 - 菜单清单 -->
	<div id="MenuList">
		<header class="h-40 flex-align-center overflow-x-auto gap-10 pr10">
			<div class="label-title bolder fs-16 min-w-50">功能</div>
			<!-- <SearchHistoryInput
				:showLabel="false"
				className="vw6"
				name="functionPointSearch"
				placeholder="功能名称"
				v-model.trim="searchForm.functionPointSearch"
				@input="queryList"
			/> -->
			<el-checkbox class="ml-auto" v-model="searchForm.onlyShowTodo" :true-label="1" :false-label="0" @change="queryList">
				待办
			</el-checkbox>
		</header>

		<main class="H100 overflow-y-auto">
			<div v-if="listData.length" class="menu-list mr10">
				<div
					v-for="item in listData"
					:key="'topRcid' + item.topRcid || item.menuName + item.count"
					class="menu-list-item"
					:class="topRcid == item.topRcid ? 'active' : ''"
					@click="getTopRcid(item.topRcid)"
				>
					<div class="flex-align-center gap-10">
						<img :src="item.topRcid == -1 ? boardIcon : folder" />
						<div v-if="item.topRcid == -1" class="flex-align-center gap-10">
							<span>{{ item.menuName }}</span>
							<span class="ml-auto">({{ item.count }})</span>
						</div>
						<el-badge v-else :value="item.count" :max="99" :hidden="!item.count" class="mini-badge">
							<span>{{ item.menuName }}</span>
						</el-badge>
					</div>
				</div>
			</div>
			<div v-else class="H100 flex-center">
				<div class="fs-16 color-999">暂无数据</div>
			</div>
		</main>
	</div>
</template>
<script>
import { jointString, dateFormat } from '@/util/tool';
import { mapGetters } from 'vuex';
import folder from '@/assets/img/folder.svg';
import boardIcon from '@/assets/img/boardIcon.svg';

export default {
	name: 'MenuList',
	components: {},
	props: {},
	data() {
		return {
			folder,
			boardIcon,
			listData: [], // 资源列表
			topRcid: '',
			searchForm: {
				onlyShowTodo: 1,
				isOnlyShowUnReply: 0,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['userInfos']) },
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 获取顶部资源id
		getTopRcid(topRcid) {
			this.topRcid = topRcid == this.topRcid ? '1' : topRcid; // 如果topRcid为空，则设置为1表示全部
			this.$emit('getTopRcid', this.topRcid);
		},
		// 查询清单
		async queryList() {
			const API = 'onlineSupportWorkBenchOfRDMenuInfo';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.searchForm }));
				if (res.data.success) {
					this.listData = res.data.data || [];
					// 不记录 topRcid == -1 的资源
					const unReplyCount = res.data.data?.filter(item => item.topRcid != -1).reduce((acc, cur) => acc + cur.count, 0);
					this.$store.commit('setUnReplyCount', unReplyCount);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 清空数据
		cleanData() {
			this.listData = [];
			this.topRcid = '';
		},
		// 日期格式化
		dateFormat: dateFormat,
	},
};
</script>

<style lang="scss">
#MenuList {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	color: #666;
	font-size: 14px;
	display: flex;
	flex-direction: column;
	gap: 10px;
	.menu-list {
		font-size: 14px;
		// height: 100%;
		// overflow-y: auto;
		display: flex;
		flex-direction: column;
		&-item {
			padding: 10px;
			transition: all 0.3s;
			cursor: pointer;
			display: flex;
			flex-direction: column;
			gap: 10px;
			&:hover {
				border-color: #d7d7d7;
				background: #e9f5f1;
				border-color: #1e9d6f;
				z-index: 2;
			}
			&.active {
				position: sticky;
				top: 0;
				bottom: 0;
				z-index: 2;
				border-color: #1e9d6f;
				background: #e9f5f1;
			}
		}
	}

	.mini-badge {
		.el-badge__content {
			z-index: 2;
			zoom: 0.8;
			padding: 0 5px;
			right: 0;
		}
	}
}
</style>
