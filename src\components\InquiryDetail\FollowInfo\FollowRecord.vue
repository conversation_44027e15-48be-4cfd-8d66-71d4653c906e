<template>
	<!-- 跟单记录卡片 -->
	<div class="follow-card-wrapper">
		<div class="follow-card border flex" v-for="item in newDocumentaryRecordsList" :key="item.drid">
			<!-- 跟单内容 -->
			<div class="follow-card-item W60" :class="{ W50: item.nextPlan || item.nextStep }">
				<div class="follow-card-title flex-align-center gap-10">
					<div class="follow-card-name fs-16 bolder">
						{{ item.salesmanName }}
					</div>
					<div class="follow-card-time color-999">
						{{ dateFormat(item.documentaryTime, 'lineM') }}
					</div>
					<i class="el-icon-edit-outline hover-green" @click="openDialog('跟单', item)"></i>

					<!-- 跟单方式 -->
					<div class="fs-12 flex-1 flex-align-center gap-10">
						<span v-if="item.recordType">跟单方式：{{ item.recordType }}</span>
						<div v-if="item.facilitatorMap" class="fs-12 flex-align-center gap-10">
							<span>协助人：</span>
							<Tooltips class="max-w-200" :cont-str="Object.values(item.facilitatorMap).join(',')" :cont-width="180" />
						</div>
						<!-- 附件信息 -->
						<div v-if="item.recordAttachment && item.configurationVO" class="flex-1 flex-align-center gap-10">
							<span class="min-w-50">{{ item.configurationVO.cfgName == '电话跟单' ? '📞 录音' : '📄 附件' }}</span>
							<!-- 录音播放器 -->
							<AudioPlayer v-if="item.configurationVO.cfgName == '电话跟单'" :audioUrl="item.recordAttachment" />
							<FilePopover v-else class="inline-block max-w-200 fs-12 pointer" :url="item.recordAttachment" :isIcon="true" />
						</div>
					</div>
				</div>
				<pre class="follow-card-content mt10 mb0">{{ item.content }}</pre>
			</div>

			<!-- 下一步内容 -->
			<div v-if="item.nextPlan || item.nextStep" class="follow-card-item ml20 W40">
				<div class="flex-align-center">
					<!-- 计划信息 -->
					<div class="follow-card-title flex-align-center" :class="{ 'cancel-plan': item.planStatus }">
						<div class="follow-card-name fs-16 bolder"> 下一步计划</div>
						<div class="follow-card-time color-999 ml15 mr15">
							{{ dateFormat(item.nextStep, 'lineM') }}
						</div>
						<i class="el-icon-edit-outline hover-green" @click="openDialog('计划', item)"></i>
					</div>

					<!-- 出差信息(已有出差记录或者计划未关闭时可点击) -->
					<div v-if="item.btaid || !item.planStatus" class="travel-info ml10 flex-align-center flex-wrap">
						<span class="flex-align-center hover-green" @click="openDialog('出差', item)">
							<i :class="item.btaid ? '' : 'color-999'" class="el-icon-suitcase-1 mr5"></i>
							<Tooltips v-if="item.tripUsers" class="max-w-200 fs-12" :cont-str="item.tripUsers" />
						</span>
						<span v-if="item.btaid" class="ml5 pointer fs-12">
							<span v-if="item.approveStatus > 0">
								{{ '当前' + ['未提交', '已提交(待审核)', '', '已审核通过', '被退回修改'][item.approveStatus] }}
							</span>
							<el-button
								v-if="item.approveStatus == 0 || item.approveStatus == 4"
								type="text"
								size="mini"
								@click="submitApprove(item)"
							>
								{{ ['提交申请', '撤回', '', '撤回', '重新提交'][item.approveStatus] }}
							</el-button>
						</span>
					</div>
				</div>
				<pre class="follow-card-content mt10 mb0" :class="{ 'cancel-plan': item.planStatus }">{{ item.nextPlan }}</pre>
			</div>
		</div>

		<!-- 跟单时间/计划弹窗 -->
		<el-dialog width="666px" :visible.sync="dialogEdit" :close-on-click-modal="false" append-to-body @close="closeDialog">
			<span slot="title">修改{{ dialogTitle }}信息</span>

			<el-form :model="planForm" :rules="formRules" label-width="100px" label-position="left" @submit.native.prevent>
				<div v-if="dialogTitle == '跟单'">
					<el-form-item label="跟单时间" prop="documentaryTime">
						<el-date-picker
							class="W100"
							v-model="planForm.documentaryTime"
							value-format="timestamp"
							type="datetime"
							format="yyyy-MM-dd HH:mm"
							placeholder="请选择日期时间"
							:clearable="false"
							:default-value="new Date()"
						></el-date-picker>
					</el-form-item>
					<el-form-item label="跟单内容" prop="content">
						<el-input
							disabled
							type="textarea"
							:autosize="{ minRows: 2, maxRows: 4 }"
							v-model="planForm.content"
							placeholder="请输入跟单内容..."
						></el-input>
					</el-form-item>
				</div>

				<div v-else>
					<el-form-item label="计划日期" prop="nextStep">
						<el-date-picker
							class="W100"
							v-model="planForm.nextStep"
							value-format="timestamp"
							type="datetime"
							format="yyyy-MM-dd HH:mm"
							placeholder="选择日期后再输入内容"
							:clearable="false"
							:picker-options="pickerOptions"
							:default-value="$moment(new Date()).endOf('day').valueOf()"
							default-time="23:59:59"
						></el-date-picker>
					</el-form-item>

					<el-form-item label="计划内容" prop="nextPlan">
						<el-input
							type="textarea"
							:autosize="{ minRows: 2, maxRows: 4 }"
							v-model="planForm.nextPlan"
							placeholder="请输入以何种方式联系谁达成什么目标,如：微信联系张总了解其对报价单的看法"
						></el-input>
					</el-form-item>

					<el-form-item label="计划状态" prop="planStatus">
						<el-radio-group v-model="planForm.planStatus">
							<el-radio :label="0">进行</el-radio>
							<el-radio :label="1">关闭</el-radio>
						</el-radio-group>
					</el-form-item>
				</div>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="savePlan('保存', planForm)">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 出差申请弹窗 -->
		<el-dialog width="888px" :visible.sync="dialogTravel" :close-on-click-modal="false" append-to-body @close="closeDialog">
			<span slot="title">
				<span> {{ dialogTitle }}申请</span>
				<span v-show="travelForm.btaid">{{
					'当前' + ['未提交', '已提交(待审核)', '', '已审核通过', '被退回修改'][travelForm.approveStatus]
				}}</span>
			</span>

			<el-form
				:disabled="!!travelForm.planStatus"
				:model="travelForm"
				:rules="travelFormRules"
				label-width="100px"
				label-position="left"
				@submit.native.prevent
			>
				<div class="flex-justify-between">
					<el-form-item label="客户" prop="tripClientName">
						<Tooltips :cont-str="travelForm.tripClientName" />
					</el-form-item>
					<el-form-item class="W30 ml20" label="承担方" prop="expenseParty">
						<!-- <Tooltips class="pl10" :cont-str="expensePartyMap[travelForm.expenseParty]" :cont-width="100" /> -->
						<el-select v-model="travelForm.expenseParty" placeholder="承担方" clearable filterable>
							<!-- <el-option v-for="[key, value] in Object.entries(expensePartyMap)" :key="key" :label="value" :value="Number(key)">
							</el-option> -->
							<el-option v-for="item in expensePartyOptions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
						</el-select>
					</el-form-item>
				</div>

				<el-form-item label="目的地" prop="tripDestination">
					<el-autocomplete
						ref="cautocomplete"
						class="W100"
						v-model="travelForm.tripDestination"
						placeholder="请输入/点击选择目的地"
						clearable
						:debounce="500"
						:fetch-suggestions="querySearch"
						@select="handleSelect"
						@clear="$refs.cautocomplete.activated = true"
					>
						<template slot-scope="{ item }">
							<span>{{ item.clientAddress }}</span>
						</template>
					</el-autocomplete>
				</el-form-item>
				<el-form-item label="事由" prop="tripReason">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						v-model="travelForm.tripReason"
						placeholder="请输入事由"
					></el-input>
				</el-form-item>
				<div class="flex-justify-between">
					<el-form-item class="W30" label="差旅类别 " prop="tripType">
						<el-select v-if="isParter" v-model="travelForm.tripType" placeholder="差旅类别" clearable filterable>
							<el-option
								v-for="item in tripTypeOptions"
								:key="item.tripType"
								:label="item.travelCategoryName"
								:value="item.tripType"
							>
								<span>{{ item.travelCategoryName }}：</span>
								<span>{{ item.expense }}元 / 天</span>
								<span v-if="item.isDealDeduct"> | 成交扣减{{ item.dealDeductExpense }}元</span>
							</el-option>
						</el-select>
						<span v-else>{{ tripTypeMap[travelForm.tripType] }}</span>
					</el-form-item>
					<el-form-item class="W35 ml20 mr20" label="开始日期" prop="tripBeginDate">
						<!-- <span>{{ dateFormat(travelForm.tripBeginDate, 'line') }}</span> -->
						<el-date-picker
							class="W100"
							v-model="travelForm.tripBeginDate"
							value-format="timestamp"
							type="date"
							format="yyyy-MM-dd"
							placeholder="开始日期"
							:clearable="false"
						></el-date-picker>
					</el-form-item>
					<el-form-item class="W35" label="出差天数" prop="tripDays">
						<el-input v-model="travelForm.tripDays" placeholder="请输入出差天数" clearable></el-input>
					</el-form-item>
				</div>
				<el-form-item label="出差人" prop="tripUidsArray">
					<el-select
						v-model="travelForm.tripUidsArray"
						placeholder="请选择出差人"
						popper-class="select-column-4"
						clearable
						filterable
						multiple
						class="W100"
					>
						<el-option disabled v-if="tripUserList.length == 0">
							<span class="orange fs-14">当前分销/代理未与系统用户绑定，请联系管理员绑定用户后再操作！</span>
						</el-option>
						<el-option v-else v-for="item in tripUserList" :key="item.auid" :label="item.userName" :value="item.auid">
						</el-option>
					</el-select>
				</el-form-item>
				<div class="flex-justify-between">
					<el-form-item class="W45" label="申请人" prop="applyUName">
						<span>{{ travelForm.applyUName }}</span>
					</el-form-item>
					<el-form-item class="W45" label="申请时间" prop="applyDate">
						<span>{{ dateFormat(travelForm.applyDate, 'lineM') }}</span>
					</el-form-item>
				</div>
				<div
					v-if="!travelForm.planStatus && (travelForm.approveStatus == 1 || travelForm.approveStatus == 3)"
					class="red text-right"
				>
					* 当前为【{{ { 1: '已提交（待审核）', 3: '审核通过' }[travelForm.approveStatus] }}】
					状态，保存后将需要再重新进行提交申请审核的操作，请悉知！
				</div>
			</el-form>

			<span v-if="!travelForm.planStatus" slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveTravel">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { debounce, resetValues, checkRequired, dateFormat, jointString } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';
import AudioPlayer from '@/components/AudioPlayer'; // 录音播放器
import { tripTypeMap, expensePartyMap } from '@/assets/js/contractSource'; // 差旅类别
export default {
	name: 'FollowRecord',
	components: { FilePopover, AudioPlayer },
	props: {
		// 询盘主键
		idid: { type: [String, Number], default: '' },
		// 明细表单
		detailForm: { type: Object, default: () => {} },
		// 跟单记录
		newDocumentaryRecordsList: { type: Array, default: () => [] },
	},

	data() {
		return {
			dialogEdit: false,
			dialogTravel: false,
			dialogTitle: '',

			// 跟单/计划弹窗表单
			planForm: {
				content: '',
				drid: '',
				nextPlan: '',
				nextStep: '',
				planStatus: 0,
				documentaryTime: '',
			},
			expensePartyMap, //承担方
			// 出差申请弹窗表单
			tripTypeOptions: [], //差旅类别
			tripTypeMap,
			travelForm: {
				applyMemo: '',
				applySource: '',
				btaid: '',
				dmid: '',
				dmsid: '',
				dmsiid: '',
				drid: '',
				expenseParty: '',
				idid: '',
				tripBeginDate: '',
				tripClientName: '',
				tripDays: '',
				tripDestination: '',
				tripType: '',
				tripReason: '',
				tripUids: '',
				tripUidsArray: [], //用于转换为el-select的value
				twid: '',
			},
			formRules: {
				documentaryTime: [{ required: true, message: '请选择跟单日期', trigger: 'change' }],
				content: [{ required: true, message: '请输入跟单内容', trigger: 'blur' }],
				nextStep: [{ required: true, message: '请选择计划日期', trigger: 'change' }],
				nextPlan: [{ required: true, message: '请输入计划内容', trigger: 'blur' }],
				planStatus: [{ required: true, message: '请输入计划状态', trigger: 'change' }],
			},
			travelFormRules: {
				expenseParty: [{ required: true, message: '请输入承担方', trigger: 'change' }],
				tripDestination: [{ required: true, message: '请输入目的地', trigger: 'change' }],
				tripReason: [{ required: true, message: '请输入事由', trigger: 'blur' }],
				tripType: [{ required: true, message: '请输入差旅类别', trigger: 'blur' }],
				tripBeginDate: [{ required: true, message: '请输入开始日期', trigger: 'blur' }],
				tripDays: [{ required: true, message: '请输入出差天数', trigger: 'blur' }],
				tripUidsArray: [{ required: true, message: '请输入出差人', trigger: 'blur' }],
				tripUids: [{ required: true, message: '请输入出差人', trigger: 'blur' }],
			},
			destinationList: [], // 目的地列表

			pickerOptions: {
				disabledDate: time => {
					// 不允许选择过去日期 只能选择今天以及未来93天内的日期
					const now = this.$moment().startOf('day').valueOf() - 1;
					const pickTime = this.$moment(time).startOf('day').valueOf();
					const thirtyDaysAfter = this.$moment().add(93, 'days').valueOf();
					return pickTime <= now || pickTime >= thirtyDaysAfter;
				},
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userList']),
		// 当前用户是否合伙人
		isParter() {
			return this.userInfos.adminUserVO.userName.startsWith('8');
		},
		// 承担方列表 （如果是合伙人只返回承担方为申请人 3）
		expensePartyOptions() {
			const options = Object.entries(this.expensePartyMap).map(([key, value]) => ({ id: Number(key), name: value }));
			return this.isParter ? options.filter(item => item.id == 3) : options;
		},
		// 出差人列表
		tripUserList() {
			// 如果是合伙人，不可以勾选自己
			return this.isParter ? this.userList?.filter(item => item.auid != this.userInfos.adminUserVO.auid) : this.userList;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 保存修改 - 修改跟单/计划
		savePlan: debounce(async function (type, planForm) {
			if (type === '关闭计划') {
				if (!planForm.idid || !planForm.drid) {
					return this.$message.warning('未找到该计划，请重新刷新后再操作！');
				}
				planForm.planStatus = 1; //关闭计划
			}
			if (this.dialogTitle == '跟单') {
				if (!planForm.documentaryTime || !planForm.content) {
					this.$message.warning('请完善跟单时间/跟单内容');
					return;
				}
			} else {
				// 修改计划
				if (!planForm.nextPlan || !planForm.nextStep) {
					this.$message.warning('请完善计划时间/计划内容');
					return;
				}
			}

			const API = 'updateNextPlan';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...planForm }));
				if (res.data.success) {
					this.closeDialog();
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		// 打开跟单/计划弹窗
		openDialog(title, item) {
			this.dialogTitle = title;

			if (title == '出差') {
				// 只有当前询盘的业务顾问可以提交出差申请
				if (this.detailForm.salesmanUid != this.userInfos?.adminUserVO.auid) {
					return this.$message.warning('只有当前询盘的业务顾问可以提交出差申请！');
				}
				this.travelForm = {
					...this.travelForm,
					...item,
					tripType: this.isParter ? '' : 1, //从询盘创建的出差申请默认为商务 交付为交付
					expenseParty: this.isParter ? 3 : '', // 合伙人出差默认承担方为申请人
					tripDays: item.tripDays || 1, //出差天数为所需天数
					tripClientName: this.detailForm.registeredBusinessName || item.tripClientName || '', //客户为拜访的公司
					tripReason: item.nextPlan || item.tripReason || '', //事由为计划内容
					tripBeginDate: item.tripBeginDate || item.nextStep || '', //开始日期为计划日期
					applyUName: item.applyUName || this.userInfos?.adminUserVO.userName,
					applyDate: item.applyDate || new Date().getTime(),

					// 自动补充出差人（咨询业务实施等）
					tripUidsArray:
						item?.tripUids?.split(',') || this.isParter ? [] : Array.from(new Set([this.detailForm.salesmanUid].filter(Boolean))),
					//  各种询盘相关的id
					idid: this.detailForm.idid || this.idid,
					ckrid: this.detailForm.ckrid,
					twid: this.detailForm.twid,
				};

				this.queryDestinationList(); // 查询目的地并自动补充
				this.isParter && this.queryTripTypeOptions(); // 查询差旅类别
				this.dialogTravel = true;
			} else {
				// 跟单/计划弹窗表单
				this.planForm = {
					content: item.content,
					planStatus: item.planStatus,
					idid: this.detailForm.idid || this.idid,
					drid: item.drid,
					nextPlan: item.nextPlan,
					nextStep: item.nextStep,
					documentaryTime: item.documentaryTime,
				};
				this.dialogEdit = true;
			}
		},
		// 关闭弹窗
		closeDialog() {
			if (this.dialogTitle == '出差') {
				this.dialogTravel = false;
				this.travelForm = resetValues(this.travelForm);
			} else {
				this.dialogEdit = false;
				this.planForm = resetValues(this.planForm);
			}
		},

		// 保存修改 - 修改出差申请
		saveTravel: debounce(async function () {
			this.travelForm.tripUids = this.travelForm.tripUidsArray?.join(',') || '';

			if (checkRequired(this.travelForm, this.travelFormRules)) return; //必填项校验
			const API = this.travelForm.btaid ? 'updateBusinessTripApplication' : 'addBusinessTripApplication';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.travelForm }));
				if (res.data.success) {
					this.closeDialog();
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		// 提交差旅申请到审核状态
		async submitApprove(item) {
			const API = 'updateBusinessTripApplicationSubmitApprove';
			try {
				const res = await this.$axios[API](JSON.stringify({ id: item.btaid }));
				if (res.data.success) {
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 查询差旅类别
		queryTripTypeOptions: debounce(async function () {
			const API = 'selectTravelCategoryConfiguration';
			try {
				const res = await this.$axios[API](JSON.stringify({ cfGrouping: 0 }));
				if (res.data.success) {
					this.tripTypeOptions = res.data.data || [];
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		// 查询目的地
		async queryDestinationList() {
			if (!this.detailForm.ckrid) return (this.destinationList = []);
			const API = 'selectClientAddressesByClientKeepRecordId';
			try {
				const res = await this.$axios[API](JSON.stringify({ id: this.detailForm.ckrid }));
				if (res.data.success) {
					// this.$succ(res.data.message);
					this.destinationList = res.data.data || [];
					if (this.destinationList.length && !this.travelForm.tripDestination) {
						this.travelForm.tripDestination = this.destinationList[this.destinationList.length - 1].clientAddress;
					}
				} else {
					// this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// querySearch(查询接口:xxx)
		querySearch(queryStr, cb) {
			let result = this.destinationList;
			if (queryStr) {
				result = this.destinationList?.filter(item => item.clientAddress.indexOf(queryStr) > -1) || [];
			}
			cb(result);
		},
		// handleSelect(选择时:xxx)
		handleSelect(item) {
			this.travelForm.tripDestination = item.clientAddress;
		},

		dateFormat: dateFormat,
		jointString: jointString,
	},
};
</script>

<style lang="scss" scoped>
.follow-card-wrapper {
	color: #555;
	// max-height: 80vh;
	// overflow-y: auto;
	.follow-card {
		// background-image: url('@/assets/img/wave-bg.webp');
		// background-size: 12% 110%;
		// background-repeat: repeat;
		background-color: #f5f5f5;
		border-color: #e9e9e9;
		border-radius: 3px;
		margin: 10px 0;
		padding: 10px;
		.follow-card-content {
			white-space: break-spaces;
		}
		.cancel-plan {
			// 关闭计划
			text-decoration: line-through;
			color: #999;
		}
		&:hover {
			background-size: 10% 100%;
			border-color: #1e9d6f !important;
			border-width: 2px; // 加粗边框
			background-color: #e9f5f1;
			box-shadow: 0 2px 12px 0 rgba(30, 157, 111, 0.2); // 增强阴影效果

			// 使用整数缩放值并添加文本渲染优化
			// transform: scale(1.01); // 使用整数缩放值
			// transform-origin: center; // 确保从中心缩放
			// -webkit-font-smoothing: antialiased; // 优化文本渲染
			// backface-visibility: hidden; // 提高渲染性能

			transition: all 0.3s ease-in-out;
		}
	}
}
</style>
