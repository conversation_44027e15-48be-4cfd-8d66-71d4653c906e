<template>
	<div id="taskDetailCom" :class="moveToggle ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<!-- 头部标题 -->
			<div class="detail-head">
				<div class="detail-title">
					<div class="flex-align-center">
						<span>【{{ taskForm.taskNo }}】 </span>
						<Tooltips :cont-str="taskForm.taskNam" />
						<span :style="{ color: colorMap[taskForm.status] }"> （{{ statusMap[taskForm.status] }}） </span>
					</div>
				</div>
				<el-button type="text" class="el-icon-arrow-left" @click.stop="moveToggle = false">返回</el-button>
			</div>
			<!-- 主体内容 -->
			<div class="detail-content p10">
				<!-- 任务表单 -->
				<el-form
					:model="taskForm"
					:rules="taskFormRules"
					ref="taskForm"
					label-width="120px"
					class="taskForm"
					size="mini"
					label-position="right"
					:disabled="!taskAuth"
				>
					<el-row>
						<el-col :span="8">
							<el-form-item label="任务" prop="taskName" required label-width="60px" class="label-left">
								<el-input placeholder="选择日期" v-model="taskForm.taskName"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="4">
							<el-form-item label="关联" prop="relevanceTaskNo">
								<el-autocomplete
									v-model="taskForm.relevanceTaskNo"
									:fetch-suggestions="querySearchAsync"
									@clear="handleSelect(null)"
									placeholder="搜索关联其他任务"
								>
									<template slot-scope="{ item }">
										<Tooltips :cont-str="item.taskNo + '-' + item.taskName"> </Tooltips>
									</template>
								</el-autocomplete>
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item label="分类" prop="taskClassify" required>
								<el-radio-group v-model="taskForm.taskClassify">
									<el-radio :label="0">需求</el-radio>
									<el-radio :label="1">优化</el-radio>
									<el-radio :label="2">Bug</el-radio>
									<el-radio :label="3">杂项</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :span="5">
							<el-form-item label="优先级" prop="taskLevel" required>
								<el-radio-group v-model="taskForm.taskLevel">
									<el-radio :label="0">高</el-radio>
									<el-radio :label="1">中</el-radio>
									<el-radio :label="2">低</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
					</el-row>
					<!-- 12 7 5 -->
					<el-row>
						<el-col :span="4">
							<el-form-item label="开发产出工时" prop="productTime" label-width="110px" class="label-left">
								<el-input v-model="taskForm.productTime" @change="changeProductTime"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="4">
							<el-form-item label="难度系数" prop="difficulty" required>
								<el-input v-model="taskForm.difficulty"></el-input>
							</el-form-item>
						</el-col>
						<el-col :span="4">
							<el-form-item label="要求转测日期" prop="endTime" required>
								<el-date-picker
									v-model="taskForm.endTime"
									value-format="timestamp"
									type="date"
									placeholder="选择要求转测日期"
									clearable
									format="MM/dd"
									class="W100"
								></el-date-picker>
							</el-form-item>
						</el-col>

						<el-col :span="4">
							<el-form-item label="开发者" prop="productUid" required>
								<el-select v-model="taskForm.productUid" placeholder="请选择开发者" clearable filterable>
									<el-option v-for="item in developerList" :key="item.auid" :label="item.userName" :value="item.auid">
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="3">
							<el-form-item label="测试产出工时" prop="testProductTime">
								<el-input v-model="taskForm.testProductTime"></el-input>
							</el-form-item>
						</el-col>

						<el-col :span="5">
							<el-form-item label="计划测试日期" prop="planTestTime">
								<el-date-picker
									v-model="taskForm.planTestTime"
									class="W100"
									value-format="timestamp"
									type="date"
									placeholder="选择计划测试日期"
									clearable
									format="MM/dd"
								></el-date-picker>
							</el-form-item>
						</el-col>
					</el-row>

					<!-- 12 7 5 -->
					<el-row>
						<el-col :span="4">
							<el-form-item label="提出人" prop="mentionUid" required label-width="80px" class="label-left">
								<el-select v-model="taskForm.mentionUid" placeholder="请选择提出人" clearable filterable>
									<el-option v-for="item in implementList" :key="item.auid" :label="item.userName" :value="item.auid">
									</el-option>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="4">
							<el-form-item label="来源客户" prop="customer"> <el-input v-model="taskForm.customer"></el-input> </el-form-item>
						</el-col>
						<el-col :span="4">
							<el-form-item label="项目" prop="pmcid" required>
								<el-cascader
									ref="elCascader"
									clearable
									v-model="taskForm.pmcid"
									filterable
									@change="changeProject"
									:options="projectList"
									:show-all-levels="false"
									:props="{
										emitPath: false,
										checkStrictly: true,
										expandTrigger: 'hover',
										children: 'selectProjectGanttChartVOS',
										label: 'classifyName',
										value: 'pmcid',
									}"
									@visible-change="elCascaderOnlick('elCascader')"
									@expand-change="elCascaderOnlick('elCascader')"
								></el-cascader>
							</el-form-item>
						</el-col>

						<el-col :span="7">
							<el-form-item label="Bug负责人" prop="bugProductUid">
								<el-row>
									<el-col :span="12">
										<el-select v-model="taskForm.bugProductUid" placeholder="请选择开发人员" clearable filterable>
											<el-option v-for="item in developerList" :key="item.auid" :label="item.userName" :value="item.auid">
											</el-option>
										</el-select>
									</el-col>
									<el-col :span="12">
										<el-select v-model="taskForm.bugTestUid" placeholder="请选择测试人员" clearable filterable>
											<el-option v-for="item in testerList" :key="item.auid" :label="item.userName" :value="item.auid">
											</el-option>
										</el-select>
									</el-col>
								</el-row>
							</el-form-item>
						</el-col>

						<el-col :span="5">
							<el-form-item label="测试人" prop="projectTurnTest">
								<el-select v-model="taskForm.projectTurnTest" placeholder="请选择测试人员" clearable filterable class="W100">
									<el-option v-for="item in testerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>

					<!-- 12 7 5 -->
					<el-row>
						<el-col :span="19">
							<el-form-item label="关联信息" prop="relevanceTask" label-width="80px" class="label-left">
								<span class="span-text">{{ taskForm.relevanceTask }}</span>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form>
				<!-- 转测信息栏 -->
				<div v-if="taskForm.projectManagementTaskNgVOS?.length > 0">
					<!-- v-model="activeName" -->
					<el-collapse accordion>
						<el-collapse-item v-for="(item, index) in taskForm.projectManagementTaskNgVOS" :key="item.pmtid">
							<template slot="title">
								<span class="mr10">{{ dateFormat(item.projectTurnTime, 'lineM') }}</span>
								<span class="mr10">{{ taskForm.projectTurnCheck }}</span>
								<span class="mr10">第{{ taskForm.projectManagementTaskNgVOS.length - index }}次提交转测</span>
								<span class="mr10">{{ item.projectTurnCheck ? item.projectTurnCheck.userName : '' }}</span>
								<span class="mr10"> {{ dateFormat(item.testCheckTime, 'lineM') }} 完成测试</span>
								<span> 测试结果：</span>
								<span :class="['mr10', 'bolder', item.result == 4 ? 'green' : 'red']">
									{{ item.result == 4 ? '通过 ' : '不通过 ' }}
								</span>
								<span>BUG数量：</span>
								<span :class="['mr10', 'bolder', item.result == 4 ? 'green' : 'red']">{{ Number(item.bugNum) }}</span>
								<span> 测试报告详情 <i class="ml5 mr5 el-icon-info"></i></span>
							</template>
							<wang-editor class="W100" :disabled="true" v-model="item.sumbitContent" :editorHeight="editorHeight"></wang-editor>
						</el-collapse-item>
						<el-collapse-item title="开发任务详情" name="defaultActive">
							<wang-editor
								class="W100"
								v-model="taskForm.content"
								:editorHeight="editorHeight"
								:disabled="!taskAuth"
							></wang-editor>
						</el-collapse-item>
					</el-collapse>
				</div>
				<!-- 富文本编辑器 -->
				<wang-editor
					v-else
					class="W100"
					v-model="taskForm.content"
					:editorHeight="editorHeight"
					:disabled="!taskAuth"
				></wang-editor>
				<!-- 转测信息栏 -->
				<div v-if="showType" class="trun-form flex-justify-between align-center fs-14 color-666 mt6">
					<div class="W40 flex-align-center">
						<el-input size="middle" placeholder="请输入备注信息" v-model="trunForm.remark"> </el-input>
					</div>
					<div class="W20 flex-align-center">
						<div class="mr10">开发实际用时</div>
						<el-input class="w-150" type="number" size="middle" placeholder="单位(h)" v-model="trunForm.actualProductTime">
						</el-input>
					</div>

					<el-select size="middle" v-model="trunForm.projectTurnTest.auid" filterable placeholder="请选择测试人员">
						<template slot="prepend">测试人员</template>
						<el-option v-for="item in testerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
					<el-date-picker
						v-model="trunForm.turnTestLimit"
						size="middle"
						type="date"
						placeholder="要求测试完成日期"
						clearable
						format="yyyy/MM/dd"
						value-format="timestamp"
					></el-date-picker>
					<el-button type="primary" size="middle" @click="submitTurnTest"
						><span v-if="trunForm.sumbitNum">第{{ trunForm.sumbitNum }}次</span>提交转测
					</el-button>
					<el-button v-if="taskAuth" type="danger" size="middle" @click="submitTurnTest">修改任务 </el-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { debounce, dateFormat } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import wangEditor from '@/components/WangEditor/wangEditor';
export default {
	name: 'taskDetailCom',
	props: {},
	components: {
		wangEditor,
	},
	data() {
		return {
			moveToggle: false, //滑动控制
			titleName: '任务详情',
			taskAuth: false, //权限控制
			rowData: {},
			editForm: {},
			showType: '',
			activeName: 'defaultActive',
			// 任务表单
			taskForm: {},
			taskFormRules: {
				taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
				taskClassify: [{ required: true, message: '请选择分类', trigger: 'blur' }],
				taskLevel: [{ required: true, message: '请选择优先级', trigger: 'blur' }],
				productTime: [{ required: true, message: '请输入开发产出工时', trigger: 'blur' }],
				difficulty: [{ required: true, message: '请输入难度系数', trigger: 'blur' }],
				endTime: [{ required: true, message: '请选择要求转测日期', trigger: 'blur' }],
			},
			// 转测表单
			trunForm: {
				projectTurnTest: {
					auid: '',
				},
				actualProductTime: '',
				remark: '',
				turnTestLimit: '',
			},

			projectList: [],

			// 颜色和状态
			statusMap: {
				0: '延误',
				1: '计划中',
				2: '开发中',
				3: '已转测',
				4: '完成',
				5: '转测不通过',
				6: '测试延误',
			},
			colorMap: {
				0: '#ec808d', //红 开发延误
				1: '#bababa', //灰 计划中
				2: '#ab47bc', //紫 开发中
				3: '#2196f3', //蓝 已转测
				4: '#28d094', //绿 完成
				5: '#facd91', //橙 转测不通过
				6: '#ec808d', //红 测试延误
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userList']),

		editorHeight() {
			// return this.showType ? 480 : 550;
			return 480;
		},
		//开发人员列表(标签：开发)
		developerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('开发')) || [];
		},
		//测试人员列表(标签：测试)
		testerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('测试')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
	},
	// 监控data中的数据变化
	watch: {
		moveToggle(newVal) {
			if (!newVal) {
				this.activeName = 'defaultActive';
				// this.taskForm = resetValues(this.taskForm);
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.getProjectList();
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		//模糊匹配
		querySearchAsync(queryString, cb) {
			let str = JSON.stringify({
					pmid: this.taskForm.pmid,
					pmtid: this.taskForm.pmtid,
					queryParam: queryString ? queryString : '',
				}),
				result = [];
			this.$axios
				.likeQueryTask(str)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item['value'] = item.taskNo;
						});
						result = res.data.data;
						cb(result);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('likeQueryTask |' + error);
				});
		},
		// 打开任务详情
		openDetail(row, type) {
			type == '执行研发任务' && (this.showType = '执行研发任务');

			this.rowData = row;
			this.getDetail();
		},
		// 获取任务详情
		getDetail() {
			const str = JSON.stringify({ pmtid: this.rowData.pmtid });
			this.$axios
				.selectTaskDetailByPmtid(str)
				.then(res => {
					if (res.data.success) {
						this.taskAuth = !res.data.data.taskAuth; //权限
						// 人员id转换
						const keyArr = ['bugProductUid', 'bugTestUid', 'mentionUid', 'productUid', 'projectTurnCheckUid', 'testManagement'];
						keyArr.map(key => {
							res.data.data[key] = res.data.data[key].auid;
						});
						res.data.data['projectTurnTest'] = res.data.data.projectTurnCheckUid; //字段名称不一致特殊处理
						// 关联信息提取
						res.data.data?.relevanceTaskVOs?.map(item => {
							item.userName = item.productUid.userName;
							const { userName, taskName, endTime, status, taskNo } = item;
							let relevanceTask = '';
							relevanceTask = `${userName};${taskName};要求转测：${this.dateFormat(endTime)};状态：${this.statusMap[status]} `;
							res.data.data.relevanceTask = relevanceTask;
							res.data.data.relevanceTaskNo = taskNo;
						});
						this.taskForm = { ...res.data.data }; //taskForm 表单赋值

						if (this.showType == '执行研发任务') {
							// 转测信息
							this.trunForm.projectTurnTest.auid = this.taskForm.projectTurnTest
								? this.taskForm.projectTurnTest
								: this.taskForm.testManagement;
							this.trunForm.remark = this.taskForm.remark;
							const oneDay = 24 * 60 * 60 * 1000;

							// 首次转测默认设置：计划测试时间 || 要求转测时间+1天
							this.trunForm.sumbitNum = 0;
							this.trunForm.actualProductTime = this.taskForm.productTime;
							this.trunForm.turnTestLimit = this.taskForm.planTestTime; // 2024年10月21日14:20:25 改为默认计划测试时间
							// if (this.taskForm.projectManagementTaskNgVOS && this.taskForm.projectManagementTaskNgVOS.length > 0) {
							// 	// 非首次转测时
							// 	this.trunForm.sumbitNum = this.taskForm.projectManagementTaskNgVOS.length + 1;
							// 	this.trunForm.turnTestLimit = new Date().getTime() + oneDay;
							// } else {
							// 	// 首次转测默认设置：计划测试时间 || 要求转测时间+1天
							// 	this.trunForm.sumbitNum = 0;
							// 	this.trunForm.actualProductTime = this.taskForm.productTime;
							// 	this.trunForm.turnTestLimit = this.taskForm.planTestTime
							// 		? this.taskForm.planTestTime
							// 		: new Date().getTime() + oneDay;
							// }
						}

						this.moveToggle = true;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTaskDetailByPmtid |' + error);
				});
		},
		// 输入开发产出工时(自动计算)
		changeProductTime() {
			if (this.taskForm.productTime) {
				this.taskForm.testProductTime = Number(this.taskForm.productTime / 4).toFixed(1);
				this.updateTask();
			}
		},
		// 修改项目
		changeProject() {
			this.$nextTick(() => {
				const nodeData = this.$refs['elCascader'].getCheckedNodes();
				if (!nodeData) return;
				this.taskForm.pmcid = nodeData[0]?.data.pmcid || '';
				this.taskForm.pmid = nodeData[0]?.data.pmid || '';
				this.taskForm.projectName = nodeData[0]?.pathLabels[0] || '';
				this.taskForm.pmtid && this.updateTask('autoSave');
			});
		},
		// 添加/修改任务详情
		updateTask: debounce(function () {
			const URL = 'updateProjectTask';
			this.$axios[URL](
				JSON.stringify({
					...this.taskForm,
				}),
			)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addProjectTask |' + error);
				});
		}),
		// 提交转测
		submitTurnTest: debounce(function () {
			if (!this.trunForm.turnTestLimit) {
				return this.$message.warning('请选择要求测试时间!');
			}

			const str = JSON.stringify({
				actualProductTime: this.trunForm.actualProductTime,
				pmid: this.taskForm.pmid,
				pmtid: this.taskForm.pmtid,
				projectTurnTest: this.trunForm.projectTurnTest.auid,
				status: '3',
				turnTestLimit: this.trunForm.turnTestLimit,
				remark: this.trunForm.remark,
			});
			this.$axios
				.taskTurnTest(str)
				.then(res => {
					if (res.data.success) {
						this.showType == '执行研发任务' && this.$emit('turnTask', this.taskForm.pmtid);
						this.moveToggle = false;
						this.$succ('任务已转测!');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('taskTurnTest |' + error);
				});
		}),

		// 获取项目列表
		getProjectList() {
			this.projectList = [];
			const str = JSON.stringify({
				statusList: [0, 1, 2, 3, 4, 5, 6, 7, 9],
			});
			this.$axios
				.selectAllONGoingProject(str)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item.selectProjectGanttChartVOS[0].classifyName = item.projectName;
							this.projectList.push(item.selectProjectGanttChartVOS[0]);
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectProjectGanttChart |' + error);
				});
		},
		elCascaderOnlick(refObj, flag) {
			const that = this;
			if (flag === undefined) {
				setTimeout(function () {
					document.querySelectorAll('.el-cascader-node__label').forEach(el => {
						el.onclick = function () {
							this.previousElementSibling.click();
							that.$refs[refObj].dropDownVisible = false;
						};
					});
					document.querySelectorAll('.el-cascader-panel .el-radio').forEach(el => {
						el.onclick = function () {
							that.$refs[refObj].dropDownVisible = false;
						};
					});
				}, 100);
			}
		},
		//日期format
		dateFormat: dateFormat,
	},
};
</script>

<style lang="scss" scoped>
#taskDetailCom {
	.trun-form {
		position: sticky;
		bottom: 0;
	}
}
</style>

<style lang="scss">
#taskDetailCom {
	.taskForm {
		width: 100%;

		.span-text {
			vertical-align: middle;
			font-size: 14px;
			color: #606266;
			box-sizing: border-box;
		}

		.el-form-item--mini.el-form-item {
			margin-bottom: 10px;
		}

		.label-left {
			.el-form-item__label {
				text-align: left;
			}
		}
	}

	.el-collapse-item__header {
		font-size: 14px;
		color: #606266;
		height: 28px;
		line-height: 28px;
	}
}
</style>
