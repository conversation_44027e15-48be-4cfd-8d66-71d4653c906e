'use strict';
const path = require('path');
const utils = require('./utils');
const config = require('../config');
const vueLoaderConfig = require('./vue-loader.conf');
const MomentLocalesPlugin = require('moment-locales-webpack-plugin');
const { VueLoaderPlugin } = require('vue-loader');
const webpack = require('webpack');

function resolve(dir) {
	return path.join(__dirname, '..', dir);
}

module.exports = {
	context: path.resolve(__dirname, '../'),
	entry: {
		// 入口起点
		app: './src/main.js',
	},
	output: {
		// 输出
		path: config.build.assetsRoot,
		filename: '[name].js',
		publicPath: process.env.NODE_ENV === 'production' ? config.build.assetsPublicPath : config.dev.assetsPublicPath,
	},
	resolve: {
		extensions: ['.js', '.vue', '.json', '.scss'],
		alias: {
			//别名
			vue$: 'vue/dist/vue.esm.js',
			'@': resolve('src'),
			'@util': resolve('src/util'),
			'@styles': resolve('src/styles'),
			'@pages': resolve('src/pages'),
			'@router': resolve('src/router'),
			'@components': resolve('src/components'),
			'@assets': resolve('src/assets'),
			'@fetch': resolve('src/fetch'),
			'@vuex': resolve('src/vuex'),
		},
	},
	module: {
		rules: [
			// 详细loader配置
			{
				test: /\.vue$/,
				loader: 'vue-loader',
				options: vueLoaderConfig,
			},
			{
				test: /\.js$/,
				loader: 'babel-loader',
				exclude: [resolve('node_modules'), resolve('static')],
				// include: [resolve('src'), resolve('test'), resolve('node_modules/webpack-dev-server/client'), resolve('node_modules/@wangeditor')],
				options: {
					cacheDirectory: true, //开启缓存，让第二次打包构建速度更快
				},
			},
			{
				//下面的loader只会匹配一个，处理性能更好，可以避免重复处理同一个模块，提高打包速度，
				//也可以根据不同的匹配规则，对同一个模块使用不同的loader，提高打包的灵活性。
				//注意：不能有两个配置处理同一种类型文件
				oneOf: [
					{
						test: /\.css$/,
						use: ['style-loader', 'css-loader'],
						exclude: resolve('static'),
					},
					{
						test: /\.s[ca]ss$/,
						use: [
							'style-loader',
							'css-loader',
							{
								loader: 'sass-loader',
								options: {
									sassOptions: {
										// 忽略sass因旧写法导致的警告
										silenceDeprecations: ['legacy-js-api', 'import', 'function-units', 'slash-div', 'global-builtin'],
									},
								},
							},
						],
					},

					{
						test: /\.svg$/,
						type: 'asset',
						include: resolve('src/assets/img'),
						parser: {
							dataUrlCondition: {
								maxSize: 8 * 1024,
							},
						},
						generator: {
							filename: utils.assetsPath('img/[name].[hash:7].[ext]'),
						},
					},
					{
						test: /\.(png|jpe?g|gif|webp)(\?.*)?$/,
						type: 'asset',
						// use: [
						// 	{
						// 		loader: "image-webpack-loader",
						// 		options: {
						// 			disable: process.env.NODE_ENV !== "production",
						// 			mozjpeg: { progressive: true, quality: 50 }, // 压缩JPEG图像
						// 			optipng: { enabled: true }, // 压缩PNG图像
						// 			pngquant: { quality: [0.65, 0.9], speed: 4 }, // 压缩PNG图像
						// 			gifsicle: { interlaced: false } // 压缩GIF图像
						// 		}
						// 	}
						// ],
						include: resolve('src/assets/img'),
						parser: {
							dataUrlCondition: {
								maxSize: 8 * 1024,
							},
						},
						generator: {
							filename: utils.assetsPath('img/[name].[hash:7].[ext]'),
						},
					},
					{
						test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
						type: 'asset',
						parser: {
							dataUrlCondition: {
								maxSize: 8 * 1024,
							},
						},
						generator: {
							filename: utils.assetsPath('[name].[hash:7].[ext]'),
						},
					},
					{
						test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
						type: 'asset/resource',
						generator: {
							filename: utils.assetsPath('fonts/[name].[hash:7].[ext]'),
						},
					},
				],
			},
		],
	},

	plugins: [
		new VueLoaderPlugin(),
		new MomentLocalesPlugin({ localesToKeep: ['zh-cn'] }), // moment本地中文时间插件去掉一些多余的包，减小体积
		new webpack.IgnorePlugin({
			resourceRegExp: /^\.\/locale$/,
			contextRegExp: /moment$/,
		}),
	],
};
