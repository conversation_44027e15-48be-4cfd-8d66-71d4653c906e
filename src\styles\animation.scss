/*
 ************
 *	 动画   *
 ************ 
*/

/*  
  描述：全局 菜单/路由切换 transition 动画 
  使用：<transition  name="xxxx" mode="out-in"></transition>
*/

// 菜单/路由切换动画过渡 router-transform
.router-transform-leave-active,
.router-transform-enter-active {
  transition: all 0.5s;
}

.router-transform-enter-from {
  opacity: 0;
  transition: all 0.5s;
  transform: translateX(-30px);
}

.router-transform-leave-to {
  opacity: 0;
  transition: all 0.5s;
  transform: translateX(30px);
}

// 折叠动画过渡 fold-transition
.fold-transition-enter-active,
.fold-transition-leave-active {
	transition: all 0.3s ease-in-out;
	// transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}
.fold-transition-enter {
	transform: translateY(-20px);
	opacity: 0.5;
}
.fold-transition-leave-to {
	transform: translateY(-20px);
	opacity: 0.5;
}

// 全局 el-dialog 打开关闭时动画
.el-dialog__wrapper {
  transition-duration: 0.3s;
}

.dialog-fade-enter-active {
  animation: none !important;
}

.dialog-fade-leave-active {
  transition-duration: 0.15s !important;
  animation: none !important;
}

.dialog-fade-enter-active .el-dialog,
.dialog-fade-leave-active .el-dialog {
  animation-fill-mode: forwards;
}

.dialog-fade-enter-active .el-dialog {
  animation-duration: 0.3s;
  animation-name: dialog-open;
  animation-timing-function: cubic-bezier(0.6, 0, 0.4, 1);
}

.dialog-fade-leave-active .el-dialog {
  animation-duration: 0.3s;
  animation-name: dialog-close;
}

@keyframes dialog-open {
  0% {
    opacity: 0;
    transform: scale3d(0, 0, 1);
  }

  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

@keyframes dialog-close {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: scale3d(0.5, 0.5, 1);
  }
}

/*
	基本弃用，后续可删除
	开发管理 - 任务dialog动画 给el-dialog 包一层div
	注意区分:animation-name 
*/
#taskDialog {

	.el-dialog {
		.el-dialog__header {
			font-size: 14px;
			padding: 5px 10px;
		}
		.el-dialog__body {
			position: relative;
			padding: 0 10px;
		}
		.el-dialog__footer {
			position: sticky;
			bottom: 0px;
		}
	}
	.dialog-fade-enter-active .el-dialog {
		animation-duration: 0.3s;
		animation-name: anim-open2;
		animation-timing-function: cubic-bezier(0.6, 0, 0.4, 1);
	}

	.dialog-fade-leave-active .el-dialog {
		animation-duration: 0.3s;
		animation-name: anim-close2;
	}
	@keyframes anim-open2 {
		0% {
			opacity: 0;
			transform: translateX(100%);
		}

		100% {
			opacity: 1;
		}
	}

	@keyframes anim-close2 {
		0% {
			opacity: 1;
		}

		100% {
			opacity: 0;
			transform: translateX(100%);
		}
	}
}
