<template>
	<div id="onlineTraining">
		<!-- 明细组件 -->
		<CourseDetail ref="CourseDetail" @close="refreshAction()" />

		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="课程目录" name="onlineTraining">
				<div class="tab-content flex-column">
					<div class="list-top">
						<el-tabs v-model="tpid" @tab-click="handleClick">
							<el-tab-pane :label="item.trainPlan" :name="item.tpid" v-for="(item, index) in planData" :key="index">
							</el-tab-pane>
						</el-tabs>
					</div>
					<div class="list-content">
						<el-button type="text" class="mr30" icon="el-icon-refresh-right" @click="queryTableData()"
							>{{ tablePageForm.total + '  课程' }}
						</el-button>
						<el-checkbox v-model="searchForm.showOnlyUncompletedCourse" @change="queryTableData(1)">
							仅显示未完成的计划课程
						</el-checkbox>

						<span v-if="planEndTime && isCurrentLoginUserTrainingPlanDelay != 1" class="label"
							>计划结束: {{ dateFormat(planEndTime, 'lineM') }}</span
						>
						<span v-else-if="planEndTime && isCurrentLoginUserTrainingPlanDelay == 1" class="label red"
							>计划结束: {{ dateFormat(planEndTime, 'lineM') }} 状态：延误</span
						>

						<div class="course-list-bg">
							<div class="course-list">
								<div class="course-cell pointer" v-for="(item, index) in tableData" :key="index" @click="openDetail(item)">
									<div class="top-bg" :style="bg">
										<div class="info">
											<span class="top-label">{{ item.coursesName }}</span>
											<span class="user-label">{{ item.userName }}</span>
											<span class="time-label">{{ agoTime(item.publishDate) }}</span>
										</div>
									</div>

									<div class="bottom-bg">
										<el-progress
											:percentage="item.userCourseCompleteProgress"
											:show-text="false"
											:color="'#409eff'"
											:define-back-color="'#999'"
										></el-progress>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { debounce, dateFormat } from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import CourseDetail from './CourseDetail.vue'; //明细组件
import backGroundImg from '@/assets/img/traning-bg.webp';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		CourseDetail,
	},
	name: 'onlineTraining', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			backGroundImg,
			bg: {
				background: `url(${backGroundImg})`,
				backgroundSize: '100% 100%',
			},
			planData: [],

			tpid: '', //激活tab页
			planEndTime: '',
			isCurrentLoginUserTrainingPlanDelay: '',
			activeTab: 'onlineTraining', //激活tab页
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 500,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '课程编号', colNo: 'courseNo', align: 'left', width: '120' },
				{ colName: '课程名称', colNo: 'courseName', align: 'left', width: '' },
				{ colName: '课程简介', colNo: 'courseIntroduction', align: 'left', width: '' },
				{ colName: '创建人', colNo: 'createUserName', align: 'left', width: '' },
				{ colName: '发布日期', colNo: 'publishDate', align: 'center', width: '150' },
			],
			// 查询表单
			searchForm: {
				tpid: '',
				showOnlyUncompletedCourse: false,
				// 其他...
			},
			// 日期相关
			dateSelectObj: {
				endDate: '',
				startDate: '',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTrainingPlanData();
	},
	activated() {
		this.queryTrainingPlanData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		refreshAction() {
			this.queryTrainingPlanData();
		},
		// 打开明细
		openDetail(row) {
			this.$refs.CourseDetail.showDetailCom({ ...row, tpid: this.tpid });
		},
		// 切换tab
		changeTab() {},
		handleClick(node) {
			this.tpid = this.planData[node.index].tpid;
			this.planEndTime = this.planData[node.index].planEndTime;
			this.isCurrentLoginUserTrainingPlanDelay = this.planData[node.index].isCurrentLoginUserTrainingPlanDelay;
			// console.log(this.tpid)
			this.queryTableData();
		},
		//查询目录
		queryTrainingPlanData() {
			const API = 'selectBelongsCurrentUserTrainingPlan'; //接口
			const DATA = JSON.stringify({});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.planData = res.data.data;
						if (this.planData.length > 0) {
							// 查询是否已有对应记录
							const index = this.planData.findIndex(sitem => {
								if (this.tpid == sitem.tpid) {
									return true;
								}
							});
							if (index < 0) {
								//没有记录
								this.tpid = this.planData[0].tpid;
								this.planEndTime = this.planData[0].planEndTime;
								this.isCurrentLoginUserTrainingPlanDelay = this.planData[0].isCurrentLoginUserTrainingPlanDelay;
							} else {
								this.tpid = this.planData[index].tpid;
								this.planEndTime = this.planData[index].planEndTime;
								this.isCurrentLoginUserTrainingPlanDelay = this.planData[index].isCurrentLoginUserTrainingPlanDelay;
							}
							this.queryTableData();
						}
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectCurrentTpidCourse'; //接口
			const DATA = JSON.stringify({
				...this.searchForm,
				tpid: this.tpid,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		agoTime(dateTimeStamp) {
			// this.dateTimeStamp = this.time
			// if (!dateTimeStamp) {
			// if (!new Date().toString()) {
			//     return '刚刚'
			// }
			// }
			dateTimeStamp = new Date(dateTimeStamp).getTime();
			const minute = 1000 * 60;
			const hour = minute * 60;
			const day = hour * 24;
			const halfamonth = day * 15;
			const month = day * 30;
			const now = new Date().getTime();
			const diffValue = now - dateTimeStamp;
			const monthC = diffValue / month;
			const weekC = diffValue / (7 * day);
			const dayC = diffValue / day;
			const hourC = diffValue / hour;
			const minC = diffValue / minute;
			if (parseInt(monthC) >= 1) {
				return parseInt(monthC) + '个月前';
			} else if (parseInt(dayC) > 1) {
				return parseInt(dayC) + '天前';
			} else if (parseInt(dayC) === 1) {
				return '昨天';
			} else if (parseInt(hourC) >= 1) {
				return parseInt(hourC) + '小时前';
			} else if (parseInt(minC) >= 1) {
				return parseInt(minC) + '分钟前';
			} else {
				return '刚刚';
			}
		},
		//日期format
		dateFormat: dateFormat,
	},
};
</script>

<style lang="scss">
#onlineTraining {
	width: 100%;
	overflow: hidden;
	position: relative;
	.tab-content {
		background-color: #fff;
		height: calc(100vh - 165px);
		padding: 5px 20px 10px 20px;
		border: 1px solid #d7d7d7;
		border-radius: 8px;
		.list-top {
			// 激活tab时底部的bar
			.el-tabs__active-bar {
				height: 2px;
				background-color: #1e9d6f;
			}
			.el-tabs__active-bar:after {
				width: 0%;
				height: 3px;
				left: 50%;
				margin-left: -30%;
			}
		}
		.list-content {
			// background-color: #efefef;
			height: 93%;
			width: 100%;
			.label {
				margin-left: 20px;
				font-size: 14px;
				color: #606666;
				font-weight: 500;
			}
			.red {
				color: red;
			}
			.course-list-bg {
				background-color: #efefef;
				height: 93%;
				width: 100%;
				overflow: scroll;
				.course-list {
					width: 100%;
					display: flex;
					flex-wrap: wrap;
					padding-bottom: 15px;
					.course-cell {
						// background-color: #fff;
						width: 23.5%;
						height: 210px;
						// border: solid 1px #cecece;
						border-radius: 6px;
						margin: 15px 0px 0px 15px;
						display: flex;
						justify-content: center;
						flex-wrap: wrap;
						overflow: hidden;

						.top-bg {
							// background-color: #1e9d6f;
							height: 203px;
							width: 100%;
							display: flex;
							align-items: center;

							.info {
								width: 180px;
								height: 80px;
								background-color: rgba($color: #fff, $alpha: 0.75);
								border-radius: 6px;
								margin-left: -8px;
								padding: 10px 0 10px 15px;
								display: flex;
								flex-wrap: wrap;
								align-items: center;
								.top-label {
									font-size: 20px;
									font-weight: 600;
									width: 100%;
									color: #333;
									white-space: nowrap; /* 确保文本在一行内显示，不换行 */
									overflow: hidden; /* 隐藏溢出的内容 */
									text-overflow: ellipsis; /* 当文本超出容器宽度时显示省略号 */
								}
								.user-label {
									font-size: 14px;
									color: #409eff;
									max-width: 50%;
									white-space: nowrap; /* 确保文本在一行内显示，不换行 */
									overflow: hidden; /* 隐藏溢出的内容 */
									text-overflow: ellipsis; /* 当文本超出容器宽度时显示省略号 */
								}
								.time-label {
									font-size: 12px;
									margin-left: 10px;
									color: #666;
									max-width: 40%;
									white-space: nowrap; /* 确保文本在一行内显示，不换行 */
									overflow: hidden; /* 隐藏溢出的内容 */
									text-overflow: ellipsis; /* 当文本超出容器宽度时显示省略号 */
								}
							}
						}
						.bottom-bg {
							// background-color: gray;
							width: 100%;
							height: 8px;
						}
					}
				}
			}
		}
	}
}
</style>
