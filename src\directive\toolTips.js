function createTooltip() {
	// 创建浮层元素并设置样式
	const vTooltipDom = document.createElement('div');
	vTooltipDom.style.cssText = `
    min-width: 100px;
    max-width: 500px;
    position:absolute;
    background: #f5f5f5;
    border-radius: 5px;
    padding: 5px 10px;
    color:#666;
    font-size:12px;
    white-space: break-spaces;
    z-index:1000
  `;
	// 设置id方便寻找
	vTooltipDom.setAttribute('id', 'v-tooltip');
	// 将浮层插入到body中
	document.body.appendChild(vTooltipDom);
}

function showTooltip(content) {
	const vTooltipDom = document.getElementById('v-tooltip');
	if (vTooltipDom) {
		vTooltipDom.innerHTML = content;
		vTooltipDom.style.opacity = '1'; // 显示tooltip
	}
}
function hideTooltip() {
	const vTooltipDom = document.getElementById('v-tooltip');
	if (vTooltipDom) {
		vTooltipDom.style.opacity = '0'; // 隐藏tooltip
	}
}

function removeTooltip() {
	const vTooltipDom = document.getElementById('v-tooltip');
	vTooltipDom && document.body.removeChild(vTooltipDom);
}

export default {
	bind(el, binding) {
		// 鼠标移入时，创建浮层元素并显示
		el.onmouseenter = function (e) {
			if (binding.value) {
				createTooltip();
				showTooltip(binding.value);
			}
		};

		// 鼠标移动时，动态修改浮层的位置属性
		el.onmousemove = function (e) {
			const vTooltipDom = document.getElementById('v-tooltip');
			if (vTooltipDom) {
				const tooltipWidth = vTooltipDom.offsetWidth;
				const tooltipHeight = vTooltipDom.offsetHeight;
				// 边界判断
				const maxX = window.innerWidth - tooltipWidth - 15;
				const maxY = window.innerHeight - tooltipHeight - 15;

				let tooltipX = e.clientX + 15;
				let tooltipY = e.clientY + 15;

				if (tooltipX > maxX) {
					tooltipX = maxX;
				}

				if (tooltipY > maxY) {
					tooltipY = maxY;
				}

				vTooltipDom.style.left = tooltipX + 'px';
				vTooltipDom.style.top = tooltipY + 'px';
			}
		};
		// 找到浮层元素并移出
		el.onmouseleave = function () {
			removeTooltip();
		};
	},

	update(el, binding) {
		// 更新浮层内容
		if (binding.value) {
			showTooltip(binding.value);
		} else {
			hideTooltip();
		}
	},

	unbind(el) {
		// 移除浮层元素
		removeTooltip();
	},
};
