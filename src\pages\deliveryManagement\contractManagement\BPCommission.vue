<template>
	<div v-if="BPData" class="BPCommission">
		<table>
			<thead>
				<tr>
					<th class="w-80">项</th>
					<th class="w-80">单位</th>
					<th>数量</th>
					<th>单价</th>
					<th>金额/万元</th>
					<th>备注</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td>税款</td>
					<td>项</td>
					<td class="text-right">
						<Tooltips :cont-str="bigDiv(BPData.contractAmount, 10000, 2)" :cont-width="300"></Tooltips>
					</td>
					<td class="text-right">{{ bigMul(BPData.taxFactor, 100, 2) }}%</td>
					<td class="text-right"> {{ bigDiv(BPData.tax, 10000, 2) }}</td>
					<td></td>
				</tr>
				<tr>
					<td>BPUL</td>
					<td>项</td>
					<td class="text-right">{{ bigDiv(BPData.contractAmount, 10000, 2) }}</td>
					<td class="text-right">{{ bigMul(BPData.bpulFactor, 100, 2) }}%</td>
					<td class="text-right">{{ bigDiv(BPData.bpuL, 10000, 2) }}</td>
					<td></td>
				</tr>

				<tr>
					<td>货款</td>
					<td>工作中心</td>
					<td class="text-right">{{ BPData.standardProductCount || 0 }}</td>
					<td class="text-right">{{ BPData.standardCostFactor || 0 }}</td>
					<td class="text-right">{{ bigDiv(BPData.standardCost, 10000, 2) }}</td>
					<td></td>
				</tr>
				<tr>
					<td>其他货款</td>
					<td>项</td>
					<td class="text-right">1</td>
					<td class="text-right">100%</td>
					<td class="text-right">{{ bigDiv(BPData.otherGoodsAmount, 10000, 2) }}</td>
					<td></td>
				</tr>
				<tr>
					<td>定制</td>
					<td>人天</td>
					<td class="text-right">{{ BPData.customizedFactor || 0 }}</td>
					<td class="text-right bg-yellow">{{ BPData.customizedDayCost || 0 }}</td>
					<td class="text-right">{{ bigDiv(BPData.customized, 10000, 2) }}</td>
					<td>
						<div class="flex-align-center gap-10">
							<span>人天由研发经理提供（聊天确认截图）</span>
							<div class="min-w-80 flex-align-center gap-10 ml-auto">
								<span class="ml-auto">附件：</span>
								<FilePopover
									v-if="detailForm.customizedServiceDevelopDaysFile"
									isIcon
									class="pointer blue"
									trigger="click"
									:url="detailForm.customizedServiceDevelopDaysFile"
								/>
								<span v-else>未上传</span>
							</div>
						</div>
					</td>
				</tr>
				<tr>
					<td colspan="4">BP分成金额</td>
					<td class="text-right">{{ bigDiv(BPData.bpCommissionSystemAllocate, 10000, 2) }}</td>
					<td></td>
				</tr>
				<tr>
					<td colspan="4">BP分成比</td>
					<td class="text-right">{{ bigMul(BPData.bpCommissionSystemShareRatio, 100, 2) }}%</td>
					<td></td>
				</tr>
				<tr>
					<td colspan="4">BP保底分成比</td>
					<td class="text-right">{{ bigMul(BPData.bpCommissionRatioLowerLimit, 100, 2) }}%</td>
					<td>按合同金额分级底：30万以内30%保底，30万以上不高于50万25%保底；50万以上20%保底</td>
				</tr>
				<tr>
					<td colspan="4">补BP金额</td>
					<td class="text-right">{{ bigDiv(BPData.bpCommissionCompensation, 10000, 2) }}</td>
					<td></td>
				</tr>
				<tr>
					<td colspan="4">定制人天实际单价（元）</td>
					<td class="text-right" :class="getColor(BPData.finalCustomizedServiceDayCost)">
						<Tooltips :cont-str="bigDiv(BPData.finalCustomizedServiceDayCost, 1, 2)" :cont-width="300"></Tooltips>
					</td>
					<td>
						<div class="flex gap-10">
							<span>
								黄色(低于1500不低于1400)，BPU书面批准；浅红色(低于1400不低于1300)，业务总监书面批准；深红(低于1300)，总经理书面批准。
							</span>
							<div class="min-w-80 flex-align-center gap-10">
								<span class="ml-auto">附件：</span>
								<FilePopover
									v-if="detailForm.customizedServiceDevelopCostAuditFile"
									isIcon
									class="pointer blue"
									trigger="click"
									:url="detailForm.customizedServiceDevelopCostAuditFile"
								/>
								<span v-else>未上传</span>
							</div>
						</div>
					</td>
				</tr>
				<tr>
					<td colspan="4">定制金额</td>
					<td class="text-right"> {{ bigDiv(BPData.finalCustomizedAmount, 10000, 2) }}</td>
					<td></td>
				</tr>
				<tr>
					<td colspan="4">定制金额占比</td>
					<td class="text-right">{{ bigMul(BPData.finalCustomizedRatio, 100, 2) }}%</td>
					<td></td>
				</tr>
				<tr>
					<td colspan="4">BP最终分成额</td>
					<td class="text-right">{{ bigDiv(BPData.finalBPCommission, 10000, 2) }}</td>
					<td></td>
				</tr>
				<tr>
					<td colspan="4">BP最终分成比</td>
					<td class="text-right">{{ bigMul(BPData.finalBPCommissionShareRatio, 100, 2) }}%</td>
					<td></td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script>
import { dateFormat, jointString } from '@/util/tool'; //按需引入常用工具函数
import { bigDiv, bigMul } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';

export default {
	name: 'BPCommission', //组件名应同路由名(否则keep-alive不生效)
	// import引入的组件需要注入到对象中才能使用
	components: {
		FilePopover,
	},
	props: {
		detailForm: {
			type: Object,
			default: () => {},
		},
	},
	// mixins: [btnAuth],
	data() {
		return {};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		BPData() {
			return this.detailForm?.commissionContext || null;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	activated() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 获取颜色
		getColor(val) {
			if (val > 0 && val < 1300) {
				return 'bg-red';
			} else if (val >= 1300 && val < 1400) {
				return 'bg-yellow';
			} else if (val >= 1400 && val < 1500) {
				return 'bg-pink';
			}
		},
		bigDiv, // 大数除法
		bigMul, // 大数乘法
		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.BPCommission {
	width: 100%;
	overflow: hidden;
	position: relative;

	table {
		width: 100%;
		font-size: 12px;
		border-collapse: collapse;
		border-color: #d7d7d7;
	}
	th,
	td {
		border: 1px solid #d7d7d7;
		padding: 8px;
		text-align: center;
	}
	th {
		// 不换行
		white-space: nowrap;
		background-color: #f2f2f2;
	}
	td {
		text-align: left;
	}

	.bg-yellow {
		color: #666 !important;
		background-color: #ffff00 !important;
	}
	.bg-pink {
		color: #666 !important;
		background-color: #f5a0a4 !important;
	}
	.bg-red {
		color: #666 !important;
		background-color: #f03131 !important;
	}
}
</style>
