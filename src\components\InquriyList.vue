<!-- 询盘列表（通用） -->
<template>
	<div class="InquriyList" :class="openMove ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<!-- 动态询盘详情：聚心城使用聚心城询盘详情，其他使用默认询盘详情 -->
		<component
			v-if="showMap.InquiryDetail"
			:is="isJuxinCity ? 'InquiryDetail_JXC' : 'InquiryDetail'"
			ref="InquiryDetail"
			:inquiryOptions="tableData"
			@openContract="openContract"
			@close="queryTableData"
		/>

		<!-- 合同详情 -->
		<ContractDetail v-if="showMap.ContractDetail" ref="ContractDetail" :contractOptions="tableData" @close="queryTableData" />
		<!-- 数据导出弹窗 -->
		<ExportTable v-if="showMap.ExportTable" ref="ExportTable" />
		<BaseLayout>
			<template #header>
				<span class="label-title">{{ jointString(' - ', '询盘列表', titleName) }}</span>

				<el-select
					class="w-150"
					size="small"
					v-model="searchForm.quality"
					placeholder="全部询盘质量"
					@change="queryTableData(1)"
					clearable
					multiple
					collapse-tags
				>
					<el-option v-for="key in Object.keys(qualityMap)" :key="key" :label="qualityMap[key]" :value="Number(key)"> </el-option>
				</el-select>
				<el-select
					size="small"
					v-model="searchForm.channel"
					placeholder="全部来源"
					multiple
					collapse-tags
					clearable
					filterable
					@change="queryTableData"
				>
					<el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-select size="small" v-model="searchForm.rePurchase" placeholder="全部客户类型" clearable @change="queryTableData(1)">
					<!-- 0:新客户,1:复购，2：全部 -->
					<el-option label="新客户" :value="0"> </el-option>
					<el-option label="复购" :value="1"> </el-option>
					<el-option label="全部" :value="2"> </el-option>
				</el-select>

				<div class="ml-auto">
					<!-- 导出按钮 -->
					<ExportBtn @trigger="openExport" />
					<el-button type="text" class="el-icon-arrow-left" @click.stop="getback">返回</el-button>
				</div>
			</template>
			<template #main>
				<div class="table-toolbar">
					<el-checkbox-group class="mr-auto" v-model="searchForm.stage" @change="queryTableData(1)">
						<el-checkbox v-for="item in stageList" :label="item.value" :key="item.value">
							<span class="fs-12">{{ item.label }}</span>
						</el-checkbox>
					</el-checkbox-group>
				</div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
					:row-class-name="getRowColor"
				>
					<u-table-column label="序号" width="50" type="index" align="center"> </u-table-column>
					<!-- 展开行 -->
					<u-table-column label="" width="20" type="expand">
						<template slot-scope="scope">
							<div class="p_expand" v-for="(item, index) in scope.row.documentaryRecordsList" :key="index + item.documentaryTime">
								<span>{{ dateFormat(item.documentaryTime, 'lineM') }} </span>
								<span> {{ item.salesmanName ? item.salesmanName : '跟单员' }}</span>
								<span>：{{ item.content }} </span>
								<span v-show="item.nextPlan || item.nextStep"> | 计划安排：</span>
								<span
									:style="{
										display: item.nextPlan ? 'content' : 'none',
										'text-decoration': item.planStatus == 1 ? 'line-through' : '',
									}"
								>
									{{ dateFormat(item.nextStep, 'MD') }} {{ item.nextPlan }}
								</span>
							</div>

							<div class="p_expand" v-show="!scope.row.documentaryRecordsList">
								<span>当前暂无跟单记录</span>
							</div>
						</template>
					</u-table-column>

					<!-- :width="columnWidthCurr[item.colNo]" -->
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="item.colNo == 'lastDate'"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="item.colNo == 'idDateTime' || item.colNo == 'signingDate'"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 预计月份 -->
							<Tooltips
								v-else-if="item.colNo == 'expectedMonth'"
								class="orange"
								:cont-str="dateFormat(scope.row[item.colNo], 'YM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 行业 -->
							<Tooltips
								v-else-if="item.colNo == 'industry'"
								:cont-str="jointString('/', scope.row.industry, scope.row.industryRemark)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 行业熟练程度 -->
							<Tooltips
								v-else-if="['industryProficiency'].includes(item.colNo)"
								:cont-str="[, '不熟（1-5）', '熟练（6-8）', '擅长（9-10）'][scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 来源 -->
							<Tooltips
								v-else-if="item.colNo == 'channel'"
								:cont-str="
									jointString('/', sourceMap[scope.row[item.colNo]], scope.row.promotionalVidUserName, scope.row.promotionalVid)
								"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 询盘质量 -->
							<Tooltips
								v-else-if="item.colNo == 'quality'"
								:cont-str="qualityMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 商机质量 -->
							<Tooltips
								v-else-if="item.colNo == 'businessOpportunityQuality'"
								:cont-str="businessQualityMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 阶段 -->
							<Tooltips
								v-else-if="item.colNo == 'stage'"
								:cont-str="stageMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<Tooltips
								v-else-if="item.colNo == 'estimatedAmount'"
								:cont-str="scope.row[item.colNo]?.toFixed(2)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 合同金额 -->
							<Tooltips
								v-else-if="item.colNo == 'contractAmount' && scope.row.stage == 5"
								:cont-str="scope.row[item.colNo]?.toFixed(2)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- rePurchase 1 复购 #de873d  status 0 已备案  status 1 未备案 #ff1744-->
							<Tooltips
								v-else-if="item.colNo == 'companyName' && scope.row.rePurchase"
								:class="getRowColor(scope) || 'blue'"
								:cont-str="(scope.row[item.colNo] || '未知') + '（复购）'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<Tooltips
								v-else-if="item.colNo == 'companyName' && !scope.row.rePurchase && scope.row.status"
								:class="getRowColor(scope) || 'red'"
								:cont-str="(scope.row[item.colNo] || '未知') + '（未备案）'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 询盘编号 -->
							<Tooltips
								v-else-if="item.colNo == 'number'"
								class="hover-green primary"
								:class="getRowColor(scope)"
								@click.native="openDetail('修改', scope.row)"
								:cont-str="scope.row[item.colNo] || '未知'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<!-- 录音 -->
							<div v-else-if="item.colNo == 'callRecording' && scope.row[item.colNo]">
								<!-- 询盘录音播放器 -->
								<InquiryAudioPlayer :audioUrl="scope.row[item.colNo]" :idid="scope.row.idid" />
							</div>

							<!-- 是否代理 -->
							<span v-else-if="item.colNo == 'isProxy'">
								{{ scope.row[item.colNo] ? '是' : '否' }}
							</span>
							<!-- 是否复购 -->
							<span v-else-if="item.colNo == 'rePurchase'">
								{{ scope.row[item.colNo] ? '是' : '否' }}
							</span>
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>

					<u-table-column label="">
						<template slot-scope="scope">
							<el-button type="text" class="el-icon-edit-outline" @click="openDetail('修改', scope.row)"></el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>
<script>
import { jointString, dateFormat, bigAdd, sortTableData, debounce, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 自定义组件（询盘详情弹窗）
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryDetail_JXC from '@/components/InquiryDetail_JXC/InquiryDetail.vue'; //询盘详情弹窗
import ExportTable from '@/components/ExportTable'; //导出组件
import ContractDetail from '@/pages/deliveryManagement/contractManagement/contractDetailCom.vue';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
import { sourceList, sourceMap, stageList, stageMap, qualityMap, businessQualityMap } from '@/assets/js/inquirySource.js';
import InquiryAudioPlayer from '@/components/InquiryAudioPlayer'; //询盘录音播放器
export default {
	name: 'InquriyList',
	components: {
		ExportTable,
		InquiryDetail,
		InquiryDetail_JXC,
		ContractDetail,
		ExportBtn,
		InquiryAudioPlayer,
	},
	props: {
		titleName: String, //标题名
		requestType: Number,
		twidList: Array, //代理
		channelName: Array, //渠道
		auid: String,
		parentComName: String, //tab名
	},
	data() {
		return {
			openMove: false,
			tableSort: { prop: 'estimatedAmount', order: 'descending' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'center', state: true, width: '120' },
				{ colName: '登记日期', colNo: 'idDateTime', align: 'center', state: true, width: '120' },
				{ colName: '登记人', colNo: 'createName', align: 'left', state: true, width: '80' },
				{ colName: '咨询人', colNo: 'consultName', align: 'left', state: true, width: '80' },
				{ colName: '业务顾问', colNo: 'salesmanName', align: 'left', state: true },
				{ colName: '营销分管', colNo: 'marketingDivisionName', align: 'left', state: true, width: '80' },
				{ colName: '来源', colNo: 'channel', align: 'left', state: true, width: '80' },
				{ colName: '推广素材来源', colNo: 'promotionalMaterialSource', align: 'left', state: true, width: '80' },
				{ colName: '区域', colNo: 'region', align: 'left', state: true },
				{ colName: '客户称呼', colNo: 'customerName', align: 'left', state: true },
				{ colName: '公司简称', colNo: 'companyName', align: 'left', state: true },
				{ colName: '所属行业', colNo: 'industry', align: 'left', state: true },
				{ colName: '分销/代理', colNo: 'twidName', align: 'left', state: true, width: '90' },
				{ colName: '咨询情况', colNo: 'consultingCase', align: 'left', state: true },
				{ colName: '产品', colNo: 'keyword', align: 'left', state: true },
				// { colName: '业务反馈', colNo: 'feedback', align: 'left', state: true },
				{ colName: '询盘质量', colNo: 'quality', align: 'left', state: true, width: '65' },
				{ colName: '商机质量', colNo: 'businessOpportunityQuality', align: 'left', state: true, width: '65' },
				{ colName: '行业熟练程度', colNo: 'industryProficiency', align: 'left', state: true, width: '65' },
				{ colName: '跟进频次', colNo: 'followUpFrequency', align: 'left', state: true, width: '' },
				{ colName: '跟进次数', colNo: 'count', align: 'right', state: true, width: '65' },
				{ colName: '最后跟进日期', colNo: 'lastDate', align: 'center', state: true, width: '78' },
				{ colName: '静置天数', colNo: 'restDays', align: 'right', state: true, width: '65' },
				{ colName: '预计成交月份', colNo: 'expectedMonth', align: 'center', state: true, width: '75' },
				{ colName: '预计成交金额（万元）', colNo: 'estimatedAmount', align: 'right', state: true, width: '100' },
				{ colName: '阶段', colNo: 'stage', align: 'left', state: true, width: '70' },
				{ colName: '录音', colNo: 'callRecording', align: 'left', state: true, width: '55' },
				{ colName: '成交时间', colNo: 'signingDate', align: 'center', state: true },
				{ colName: '合同金额（万元）', colNo: 'contractAmount', align: 'right', state: true, width: '100' },
				{ colName: '是否复购', colNo: 'rePurchase', align: 'center', state: true },
				{ colName: '是否代理', colNo: 'isProxy', align: 'center', state: true },
			],
			searchForm: {
				quality: [],
				stage: [],
				channel: [],
			},
			qualityMap, // 询盘质量
			businessQualityMap, // 商机质量
			stageList, // 阶段
			stageMap, // 阶段
			// 来源列表
			sourceList,
			sourceMap,

			rowData: {},
			showMap: {
				InquiryDetail: false,
				ContractDetail: false,
				ExportTable: false,
			},
		};
	},
	watch: {},
	mounted() {},
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['isJuxinCity']), //是否聚心城人员
		// 查询/导出参数
		queryObject() {
			// 业务河流
			if (this.parentComName == '业务河流') {
				return {
					queryApi: 'selectMonthData',
					downloadApi: 'exportMonthData',
					downloadData: '导出业务员月份数据',
					data: {
						pageNum: this.tablePageForm.currentPage,
						pageSize: this.tablePageForm.pageSize,

						requestType: this.requestType,
						salesman: this.auid,
						endDate: this.endTime,
						startDate: this.startTime,
						twidList: this.twidList,
						channelName: this.channelName,

						stage: this.searchForm.stage,
						qualityList: this.searchForm.quality,
						channels: this.searchForm.channel,
						rePurchase: this.searchForm.rePurchase,
					},
				};
			}

			// 询盘趋势/咨询评价/询盘分配
			return {
				queryApi: 'selectInquiryDocumentaryList',
				downloadApi: 'export',
				downloadData: '询盘记录',

				data: {
					pageNum: this.tablePageForm.currentPage,
					pageSize: this.tablePageForm.pageSize,
					endDate: this.endTime,
					startDate: this.startTime,

					twidList: this.twidList,
					channelName: this.channelName,

					channel: this.searchForm.channel,
					salesman: this.searchForm.salesman,
					consult: this.searchForm.consult,
					quality: this.searchForm.quality,
					stage: this.searchForm.stage,
					region: this.searchForm.region,
					keyword: this.searchForm.keyword,
					rePurchase: this.searchForm.rePurchase,
					consultStartDate: this.searchForm.consultStartDate,
					consultEndDate: this.searchForm.consultEndDate,
				},
			};
		},
	},
	methods: {
		// 打开合同
		openContract(data) {
			this.showMap.ContractDetail = true;
			this.$nextTick(() => {
				this.$refs.ContractDetail.showDetailCom(data);
			});
		},
		// 默认排序
		getDefaultSort() {
			/*
			 询盘分配默认排序特殊处理，根据用户类型筛选
			 1咨询员:根据咨询时间升序
			 2业务员:根据跟进次数升序
			*/
			if (this.searchForm?.userType == 1) {
				this.tableSort = { prop: 'consultingDate', order: 'ascending' };
			} else if (this.searchForm?.userType == 2) {
				this.tableSort = { prop: 'count', order: 'ascending' }; //根据跟进次数升序
			} else {
				this.tableSort = this.tableSort || { prop: 'estimatedAmount', order: 'descending' };
			}
		},
		// 表格文本高光显示
		getRowColor({ row }) {
			// if (row.quality == 5) {
			// 	return 'color-999'; //无效
			// }
			/* 
			 询盘分配标红处理特殊处理，根据用户类型筛选
			 1咨询员:咨询时间为空/阶段：空/未联系上,非已签单
			 2业务员:跟进次数为0,阶段：非未联系上7 非已签单5，质量：非无效5，非复购
			*/
			const userType = this.searchForm?.userType || null;
			const { consultingDate, stage, count, quality, rePurchase } = row;
			if (userType == 1 && !consultingDate && stage == null && stage == 7 && stage !== 5) {
				return 'red';
			} else if (userType == 2 && !count && !(stage == 7 || stage == 5 || quality == 5 || rePurchase == 1)) {
				return 'red';
			} else {
				return stage === 7 || stage === null ? 'red' : ''; //未联系上 标红显示
			}
		},
		jointString: jointString, // 拼接字符串

		// 获取数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.$axios[this.queryObject.queryApi](JSON.stringify(this.queryObject.data))
				.then(res => {
					if (res.data.success) {
						this.openMove = true;
						res.data.data.forEach(item => {
							if (item.idTime) {
								item.idTime = item.idTime.replace('-', ':');
								item.idDateTime = this.getDateTime(item.idDate, item.idTime);
							}
							if (item.province == '其他') {
								item.region = '其他';
							} else {
								item.region = jointString('/', item.province, item.city, item.area);
							}
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;

						this.sortChange(this.tableSort, true);
					} else {
						this.tableData = [];
						this.$nextTick(() => {
							this.$refs.uTableRef?.reloadData(this.tableData);
							this.$refs.uTableRef?.doLayout();
						});
						this.$emit('refresh', this.startTime);
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.$message.warning('无法查看或为空');
					this.$emit('refresh', this.startTime);
					console.log('selectMonthData |' + error);
				});
		}),
		// 时间字符串转换成时间戳
		getDateTime(date, time) {
			if (date !== null) {
				const DATE = Number(new Date(date).getTime());
				if (time !== null) {
					const hour = String(time).split(':')[0] - 8;
					const min = String(time).split(':')[1];
					// let sec = String(time).split(":")[2]; + Number(sec)
					const TIME = (Number(hour * 60 * 60) + Number(min * 60)) * 1000;
					return DATE + TIME;
				}
			}
		},
		openDetail(type, row) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom(type, row);
			});
		},
		//日期format
		dateFormat: dateFormat,
		// 返回并清空数据
		getback() {
			this.openMove = false;
			this.$emit('refresh', this.startTime);
			this.searchForm = resetValues(this.searchForm);
			this.$refs?.uTableRef?.reloadData([]);
		},
		//显示弹窗
		openList(DATA) {
			this.searchForm.stage = DATA?.stage || [];
			this.searchForm.rePurchase = DATA?.rePurchase;
			this.searchForm.salesman = DATA?.salesman || ''; //业务人员
			this.searchForm.consult = DATA?.consult || ''; //咨询人员
			this.searchForm.consultStartDate = DATA?.consultStartDate || ''; //咨询评价日期
			this.searchForm.consultEndDate = DATA?.consultEndDate || ''; //咨询评价日期
			this.startTime = DATA?.startTime || DATA?.startDate || '';
			this.endTime = DATA?.endTime || DATA?.endDate || '';

			if (this.parentComName == '业务河流') {
				// 业务河流
				this.searchForm.channel = DATA.channelList || [];
				this.searchForm.quality = DATA.quality;
			} else {
				// 询盘趋势/咨询评价/询盘分配
				const { channel, qualityList, channelList } = DATA;
				this.searchForm.channel = channel || channelList || [];
				this.searchForm.quality = qualityList || [];
				this.searchForm.keyword = DATA.keyword || '';
				this.searchForm.region = DATA.region || '';

				if (this.parentComName == '询盘分配') {
					this.searchForm.userType = DATA.userType; //根据不同类型对数据标红处理
				}
			}
			this.getDefaultSort();
			this.queryTableData();
		},
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},

		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify(this.queryObject.data), //接口参数
				API: this.queryObject.downloadApi, //导出接口
				downloadData: this.queryObject.downloadData, //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.showMap.ExportTable = true;
			this.$nextTick(() => {
				this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
			});
		}),
	},
};
</script>
<style lang="scss" scoped>
.moveToggle {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 70;
	opacity: 1;
	transition: all 0.3s ease-in-out;
	transform: translateX(0);
	background: #f2f2f2;
	// .table-main {
	// 	height: calc(100vh - 265px) !important; //修改这里height以调整表格高度
	// }
	.p_expand {
		margin: 5px 20px;
		border: 1px solid #f5f5f5;
		padding: 10px;
		width: max-content;
		background: aliceblue;
		font-weight: 400;
	}
}

.moveToggle-hide {
	opacity: 0;
	transform: translateX(110%);
}
</style>
