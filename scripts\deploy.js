const SftpClient = require('ssh2-sftp-client'); //cnpm i ssh2-sftp-client
const inquirer = require('inquirer'); //cnpm i inquirer @^6.5.2
const sftp = new SftpClient();
const chalk = require('chalk');
// 服务器配置
const servers = {
	dev: {
		host: '************', // 服务器 IP 地址
		port: '22', // SFTP 端口，通常为 22
		username: 'root', // 服务器用户名
		password: 'lightMES@2025', // 服务器密码
		remotePath: '/home/<USER>/background', // 服务器目标目录
		description: '开发环境', // 备注信息
	},
	test: {
		host: '************',
		port: '22',
		username: 'root',
		password: 'lightMES@2025',
		remotePath: '/home/<USER>/background',
		description: '测试环境',
	},
	// prod: {
	//   host: '************',
	//   port: '22',
	// username: 'root',
	// password: 'lightMES@2025',
	// remotePath: '/home/<USER>/background',
	//   description: '生产环境',
	// },
};

// 检查服务器配置
const serverKeys = Object.keys(servers);

if (serverKeys.length === 0) {
	console.error(chalk.red('没有可用的服务器配置，操作失败。'));
	process.exit(1); // 终止执行
} else if (serverKeys.length === 1) {
	// 只有一个服务器配置，直接连接
	connectToServer(servers[serverKeys[0]]);
} else {
	// 提示用户选择服务器
	inquirer
		.prompt([
			{
				type: 'list',
				name: 'serverType',
				message: '请选择需要部署【OPS】WEB文件的环境✨',
				choices: [
					...serverKeys.map(key => ({
						name: `${servers[key].description} (${key}) ✨`,
						value: key,
					})),
					{ name: '取消（close）❌', value: null },
				],
			},
		])
		.then(answers => {
			if (answers.serverType) {
				connectToServer(servers[answers.serverType]);
			} else {
				console.log('操作已取消。❌');
				process.exit(1); // 终止执行
			}
		})
		.catch(error => {
			console.error(chalk.red('发生错误: '), error);
		});
}

// 连接到 SFTP 服务器的函数
function connectToServer(currentServer) {
	console.log(chalk.green('开始连接服务器 -->'), currentServer.host);
	// 连接到 SFTP 服务器
	return sftp
		.connect(currentServer)
		.then(() => {
			console.log(chalk.green('连接服务器成功 -->'), currentServer);
			return sftp.uploadDir('dist', currentServer.remotePath);
		})
		.then(() => {
			console.log(chalk.green('文件上传成功，部署完成！✨'));
		})
		.catch(err => {
			console.error(chalk.red(`上传失败: ${err.message}❌`));
		})
		.finally(() => {
			sftp.end();
		});
}
