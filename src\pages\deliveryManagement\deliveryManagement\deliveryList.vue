<template>
	<div id="deliveryList" :class="moveToggle ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<!-- 动态询盘详情：聚心城使用聚心城询盘详情，其他使用默认询盘详情 -->
		<component
			v-if="showMap.InquiryDetail"
			:is="isJuxinCity ? 'InquiryDetail_JXC' : 'InquiryDetail'"
			ref="InquiryDetail"
			:inquiryOptions="tableData"
			@close="queryTableData"
		/>
		<!-- 交付详情 -->
		<DeliveryDetail ref="deliveryDetailCom" @close="queryTableData"></DeliveryDetail>
		<BaseLayout>
			<template #header>
				<span class="search-label">收款月份</span>
				<el-date-picker
					class="w-150"
					v-model="dateSelectObj.dateStart"
					type="month"
					value-format="timestamp"
					placeholder="不限"
					@change="queryTableData(1)"
					clearable
					size="small"
				>
				</el-date-picker>

				<el-input
					class="searchBox"
					size="small"
					clearable
					v-model="queryStr"
					placeholder="客户工商注册名称"
					@input="queryTableData(1)"
				></el-input>

				<el-checkbox-group v-model="searchForm.contractFlag" @change="queryTableData(1)">
					<el-checkbox :label="1">已完成项目阶段</el-checkbox>
					<el-checkbox :label="0">未完成项目阶段</el-checkbox>
				</el-checkbox-group>

				<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>

				<div class="ml-auto">
					<el-checkbox v-model="searchForm.ignoreOneStage" :true-label="1" :false-label="0" @change="queryTableData(1)"
						>排除第一个阶段</el-checkbox
					>
					<el-button type="text" class="el-icon-arrow-left" @click="moveToggle = false">返回</el-button>
				</div>
			</template>
			<template #main>
				<div class="table-toolbar">
					<span class="mr-auto">计量单位: 万元</span>
				</div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<!-- <u-table-column type="selection" width="30" label=""></u-table-column> -->
					<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="item.colNo == 'estimatedCompletion_receiptMonth'"
								:cont-str="dateFormat(scope.row[item.colNo], 'YM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 询盘编号 -->
							<Tooltips
								class="hover-green green"
								v-else-if="item.colNo == 'number'"
								@click.native="openInquiryDetail(scope.row)"
								:cont-str="scope.row[item.colNo] || '未知'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>
							<!-- 项目名称 -->
							<Tooltips
								v-else-if="item.colNo == 'projectName'"
								class="hover-green green"
								@click.native="openContractDetailCom(scope.row, '交付详情')"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							/>
							<!-- 阶段金额和延期次数 -->
							<div v-else-if="item.colNo == 'paymentAmount'">
								<el-tooltip :content="`延期次数：${scope.row.requestQty}`" placement="top">
									<span v-if="scope.row.requestQty" class="red"> ({{ scope.row.requestQty }})</span>
								</el-tooltip>
								<span> {{ scope.row.paymentAmount }}</span>
							</div>
							<!-- 剩余有效天数 -->
							<Tooltips
								v-else-if="item.colNo == 'surplusValidDay'"
								:style="{ color: scope.row[item.colNo] < 10 ? '#e57373' : '' }"
								:cont-str="scope.row[item.colNo]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
							<!-- 状态 -->
							<Tooltips
								v-else-if="item.colNo == 'status'"
								:style="{ color: scope.row[item.colNo] == 1 ? '#1E9D6F' : '#e57373' }"
								:cont-str="scope.row[item.colNo] == 1 ? '已完成' : '未完成'"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 项目类型 -->
							<Tooltips
								v-else-if="item.colNo == 'projectType'"
								:cont-str="projectTypeMap[scope.row[item.colNo]]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<u-table-column label="" width="60" align="right">
						<template slot-scope="scope">
							<el-button type="text" size="mini" @click="openContractDetailCom(scope.row, '交付详情')">交付</el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DeliveryDetail from './components/deliveryDetailCom.vue';
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryDetail_JXC from '@/components/InquiryDetail_JXC/InquiryDetail.vue'; //询盘详情弹窗(聚心城)
import { projectTypeOptions, projectTypeMap } from '@/assets/js/contractSource';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DeliveryDetail,
		InquiryDetail,
		InquiryDetail_JXC,
	},
	props: { twidList: Array, channelName: Array },
	name: 'deliveryList',
	data() {
		return {
			uid: '',
			regionArr: [],
			activeTab: 'deliveryList',
			queryStr: '',
			rowData: {},
			titleName: '',
			openMove: '',
			//日期相关
			dateSelectObj: {
				dateStart: null,
				dateEnd: null,
			},
			tableData: [],
			tableDataCopy: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'left', width: '' },
				{ colName: '项目名称', colNo: 'projectName', align: 'left', width: '' },
				{ colName: '项目类别', colNo: 'projectType', align: 'left', width: '' },
				{ colName: '客户工商注册名称', colNo: 'registeredBusinessName', align: 'left', width: '170' },
				{ colName: '业务顾问', colNo: 'salesmanName', align: 'left', width: '' },
				{ colName: '实施顾问', colNo: 'implementName', align: 'left', width: '' },
				{ colName: '合同编号', colNo: 'contractNo', align: 'left', width: '' },
				{ colName: '阶段', colNo: 'stage', align: 'left', width: '' },
				{ colName: '完工标准', colNo: 'completionStandard', align: 'left', width: '' },
				{ colName: '支付金额', colNo: 'paymentAmount', align: 'right', width: '' },
				{ colName: '支付比率', colNo: 'paymentRatio', align: 'right', width: '' },
				{ colName: '硬件金额', colNo: 'hardwareCost', align: 'right', width: '' },
				{ colName: '软件金额', colNo: 'softwareCost', align: 'right', width: '' },
				{ colName: '居间费', colNo: 'brokerageFee', align: 'right', width: '' },
				{ colName: '收款月份', colNo: 'estimatedCompletion_receiptMonth', align: 'center', width: '' },
				{ colName: '回款金额', colNo: 'receivedPaymentAmount', align: 'right', width: '' },
			],
			searchForm: {
				uid: '',
				contractFlag: ['0', '1'],
				twidList: '',
				userType: '',
			},
			channelList: [],
			salesmanList: [],
			dialogEdit: false,
			dialogExtension: false,
			editForm: {
				abbreviation: '',
				clientNeed: '',
				linkman: '',
				linkphone: '',
				region: '',
				regions: [],
				registeredBusinessName: '',
				salesman: '',
			},
			btnFlag: false,
			showChekedBtn: true,
			dialogTitle: '客户详情',
			editRules: {
				abbreviation: [{ required: true, message: '客户简称', trigger: 'blur' }],
				clientNeed: [{ required: true, message: '客户需求', trigger: 'blur' }],
				linkman: [{ required: true, message: '请输入对接人信息', trigger: 'blur' }],
				linkphone: [{ required: true, message: '请输入对接人联系方式', trigger: 'blur' }],
				region: [{ required: true, message: '客户地址', trigger: 'blur' }],
				regions: [{ required: true, message: '客户地址', trigger: 'blur' }],
				salesman: [{ required: true, message: '请输入业务顾问', trigger: 'change' }],
				registeredBusinessName: [{ required: true, message: '客户工商注册名称', trigger: 'blur' }],
				protectDeadline: [{ required: true, message: '请输入备案保护日期', trigger: 'change' }],
				spreadTime: [{ required: true, message: '请输入展期日期', trigger: 'change' }],
			},
			moveToggle: false, //滑动控制
			projectTypeMap,
			showMap: {
				InquiryDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['isJuxinCity']), //是否聚心城人员
	},
	// 监控data中的数据变化
	watch: {
		twidList(newVal) {
			this.queryTableData();
		},
		channelName(newVal) {
			this.queryTableData();
		},
		moveToggle(newVal) {
			if (newVal) {
				this.queryTableData();
			} else {
				this.tableData = [];
				this.tableDataCopy = _.deepClone(this.tableData);
				this.tablePageForm.total = 0;
				this.$emit('closeMove');
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.uid = this.$route?.query?.queryId || '';
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 将金额转成万单位
		convertToMillion(value, d = 2) {
			if (!value || value <= 0) return '';
			// Convert the value to million units
			const valueInMillion = value / 10000;

			// Round to two decimal places
			const resultVal = (Math.round(valueInMillion * 100) / 100).toFixed(2);
			const decimalPart = resultVal.split('.')[1];
			const decimalsToAdd = 2 - decimalPart.length;
			const finalValue = decimalsToAdd > 0 ? resultVal + '0'.repeat(decimalsToAdd) : resultVal;
			return finalValue;
		},

		// 打开交付详情
		openContractDetailCom(rowData) {
			this.showMap.deliveryDetailCom = true;
			this.$nextTick(() => {
				this.$refs.deliveryDetailCom.showDetailCom(rowData);
			});
		},
		// 打开询盘详情
		openInquiryDetail(row) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom('修改', row);
			});
		},
		openDialog(row, title) {
			this.editForm = _.deepClone(row);
			this.dialogTitle = title;
			this.dialogExtension = true;
		},
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			if (!this.moveToggle) {
				this.tableData = [];
				return;
			}
			const pageNum = this.tablePageForm.currentPage,
				pageSize = this.tablePageForm.pageSize,
				startTime = this.dateSelectObj.dateStart,
				endTime = this.dateSelectObj.dateEnd;

			const str = JSON.stringify({
				endTime: endTime,
				twid: this.twidList,
				channelName: this.channelName,
				contractFlag: this.searchForm.contractFlag,
				flag: 2, //1 合同管理列表 2 ：交付管理列表
				pageNum: pageNum,
				pageSize: pageSize,
				queryParam: this.queryStr,
				startTime: startTime,
				uid: this.searchForm.uid,
				userType: this.searchForm.userType,
				ignoreOneStage: this.searchForm.ignoreOneStage,
			});
			this.$axios
				.selectDeliverRecordByUidAndTime(str)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item['salesmanName'] = item.salesman.userName;
							item['implementName'] = item.implement.userName;
							item['consultingName'] = item.consulting.userName;
							item['talkName'] = item.talk.userName;

							item.paymentRatio = item.paymentRatio ? item.paymentRatio + '%' : '';
							item.paymentAmount = this.convertToMillion(item.paymentAmount);
							item.receivedPaymentAmount = this.convertToMillion(item.receivedPaymentAmount);
							item.brokerageFee = this.convertToMillion(item.brokerageFee);
							item.hardwareCost = this.convertToMillion(item.hardwareCost);
							item.softwareCost = this.convertToMillion(item.softwareCost);
						});
						this.tableData = res.data.data;
						this.tableDataCopy = _.deepClone(this.tableData);
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							this.$refs.uTableRef?.reloadData(this.tableData);
							this.$refs.uTableRef?.doLayout();
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectDeliverRecordByUidAndTime |' + error);
				});
		}),
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},

		openDeliverList(monthIndex, row, searchForm, year) {
			// console.log(monthIndex, row, searchForm, year);
			// 获取当前月份第一天时间戳
			const now = new Date(year); // 获取当前年份
			console.log(monthIndex);
			if (monthIndex !== 'total' && monthIndex < 13) {
				// 非合计
				const firstDayOfMonth = new Date(now.getFullYear(), monthIndex - 1, 1); // 当月的第一天
				this.dateSelectObj.dateStart = firstDayOfMonth.getTime(); // 获取时间戳
				this.dateSelectObj.dateEnd = _.getNowMonthEndDay(this.dateSelectObj.dateStart); // 当前月份最后一天时间戳
			} else {
				// 合计
				const firstDayOfYear = new Date(now.getFullYear(), 0, 1); // 当年的第一天
				this.dateSelectObj.dateStart = firstDayOfYear.getTime(); // 获取时间戳
				const lastDayOfYear = new Date(now.getFullYear(), 11, 31); // 当年的最后一天
				this.dateSelectObj.dateEnd = lastDayOfYear.getTime() + 86399999; // 获取时间戳
			}

			// 默认查询条件
			this.searchForm.uid = row?.uid || '';
			// this.searchForm.contractFlag = [0, 1];
			this.searchForm.contractFlag = searchForm?.status || [0, 1];
			this.searchForm.userType = searchForm?.type || 1;
			this.searchForm.ignoreOneStage = searchForm?.ignoreOneStage || 0;
			this.queryStr = '';

			this.moveToggle = true;
			this.queryTableData();
		},
	},
};
</script>

<style lang="scss" scoped>
.moveToggle {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99;
	opacity: 1;
	transition: all 0.3s ease-in-out;
	transform: translateX(0);
	background: #f2f2f2;
}

.moveToggle-hide {
	opacity: 0;
	transform: translateX(110%);
}
</style>
