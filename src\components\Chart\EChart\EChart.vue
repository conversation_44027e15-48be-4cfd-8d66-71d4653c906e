<template>
	<h3 v-if="isSeriesEmpty">暂无数据</h3>
	<div v-else class="chart"> </div>
</template>

<script>
import * as Echarts from 'echarts';
import ResizeListener from 'element-resize-detector';
import { merge, isEmpty } from 'lodash';
import { basicOption } from './defaultOption.js';
import { chartColor } from './../color.js';
import { autoToolTip } from './../echarts_auto_tooltip.js';

export default {
	name: 'EChart',
	props: {
		// 正常的业务数据，对应echarts饼图配置中series[0].data
		seriesData: {
			type: Array,
			required: true,
			default: () => [],
		},
		// 表示需要特殊定制的配置
		// 一般UI会规定一个统一的设计规范（比如颜色，字体，图例格式，位置等）
		// 但不排除某个图标会和设计规范不同，需要特殊定制样式，所以开放这个配置，增强灵活性
		extraOption: {
			type: Object,
			default: () => ({}),
		},
		toolTipOption: {
			// 自动触发tooltip
			type: Object,
			default: () => ({
				isAuto: false,
				duration: 3,
				loopSeries: false,
				seriesIndex: 0,
			}),
		},
		// 通过重置setOption达到更新动画的效果
		activeOption: {
			type: Object,
			default: () => ({
				isActive: false,
				duration: 10,
			}),
		},
	},
	data() {
		return {
			chart: null,
			refreshInterval: null, //定时刷新
			tootipTimer: null,
		};
	},

	computed: {
		isSeriesEmpty() {
			// return isEmpty(this.seriesData) || this.seriesData.every((item) => !item.value);
			return isEmpty(this.seriesData) || this.seriesData.length <= 0;
		},
	},

	//监听数据变化，更新图表
	watch: {
		seriesData: {
			deep: true,
			handler() {
				if (this.isSeriesEmpty) {
					this.chart = null;
				} else {
					this.updateChartView();
				}
			},
		},
		extraOption: {
			deep: true,
			handler() {
				if (this.isSeriesEmpty) {
					this.chart = null;
				} else {
					this.updateChartView();
				}
			},
		},
	},

	mounted() {
		this.chart = Echarts.init(this.$el);
		// this.updateChartView();
		window.addEventListener('resize', this.handleWindowResize);
		this.addChartResizeListener();
		if (this.activeOption.isActive) {
			this.refreshInterval = setInterval(() => {
				this.updateChartView();
			}, this.activeOption.duration * 1000);
		}
		// 检查父组件是否注册了 onClick 事件
		if (this.$listeners.onClick) {
			console.log('父组件注册了 onClick 事件');
			this.onClick();
		}
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.handleWindowResize);
		clearInterval(this.refreshInterval);
		this.refreshInterval = null;
	},

	methods: {
		/* 合并配置项和数据 */

		assembleDataToOption() {
			// 这部分的图例formatter取决于UI要求，如果你的项目中不需要，就可以不写formatter
			// 由于echarts版本的迭代，这里的写法也有稍许改变
			// eslint-disable-next-line no-unused-vars
			const formatter = name => {
				const total = this.seriesData.reduce((acc, cur) => acc + cur.value, 0);
				const data = this.seriesData.find(item => item.name === name) || {};
				const percent = data.value ? `${Math.round((data.value / total) * 100)}%` : '0%';

				return `${name} ${percent}`;
			};
			if (this.extraOption.series) {
				const seriesCurr = [];
				for (let i = 0; i < this.extraOption.series.length; i++) {
					seriesCurr.push({
						data: this.seriesData,
					});
				}
				return merge(
					{},
					basicOption,
					{ color: chartColor },
					{
						// legend: { formatter },
						series: seriesCurr,
					},
					this.extraOption,
				);
			} else {
				return merge(
					{},
					basicOption,
					{ color: chartColor },
					{
						// legend: { formatter },
						series: [{ data: this.seriesData }],
					},
					this.extraOption,
				);
			}
		},
		/**
		 * 对chart元素尺寸进行监听，当发生变化时同步更新echart视图
		 */
		addChartResizeListener() {
			const instance = ResizeListener({
				strategy: 'scroll',
				callOnAdd: true,
			});

			instance.listenTo(this.$el, () => {
				if (!this.chart) return;
				this.chart.resize();
			});
		},

		/**
		 * 更新echart视图
		 */
		updateChartView() {
			if (!this.chart) return;
			const fullOption = this.assembleDataToOption();
			this.chart.clear();
			this.chart.setOption(fullOption, true);
			this.openAutoToolTip();
		},

		/**
		 * 当窗口缩放时，echart动态调整自身大小
		 */
		handleWindowResize() {
			if (!this.chart) return;
			this.chart.resize();
		},
		/**
		 * 自动toolTip效果
		 */
		openAutoToolTip() {
			if (!this.chart || !this.toolTipOption.isAuto) return;
			this.tootipTimer && this.tootipTimer.clearLoop();
			this.tootipTimer = null;
			const fullOption = this.assembleDataToOption();
			this.tootipTimer = autoToolTip(this.chart, fullOption, {
				// 轮播间隔时间 默认3s
				interval: this.toolTipOption.duration * 1000,
				// 是否循环轮播所有序列
				loopSeries: this.toolTipOption.loopSeries,
				// 第1个被轮播的序列下标
				seriesIndex: this.toolTipOption.seriesIndex,
			});
		},

		/*
		 * 点击柱状图，获取被点击的柱状图数据
		 */
		onClick() {
			const _this = this;
			this.chart.on('click', function (params) {
				params.data && _this.$emit('onClick', params);
			});
		},
	},
};
</script>

<style scoped="scoped" lang="scss">
.chart {
	width: 100%;
	height: 100%;
}
</style>
