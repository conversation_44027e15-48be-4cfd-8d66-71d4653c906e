'use strict';
const { merge } = require('webpack-merge');
const prodEnv = require('./prod.env');
// 开发地址切换
module.exports = merge(prodEnv, {
	// NODE_ENV: '"development"',
	// API_HOST: '"http://ops.lightmes.cn/"', //生产环境
	// API_ADDRESS: '"ops.lightmes.cn/"',
	// API_HOST: '"http://test.m.lightmes.cn/"', //旧测试环境
	// API_ADDRESS: '"test.m.lightmes.cn/"',
	API_HOST: '"https://ops.test.base.lightmes.cn/"', //测试环境
	API_ADDRESS: '"ops.test.base.lightmes.cn/"',
	// API_HOST: '"http://dev.ops.lightmes.com/"', //开发环境
	// API_ADDRESS: '"dev.ops.lightmes.com/"',
});
