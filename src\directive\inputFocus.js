/* 
自定义指令  - 输入框聚焦
<input v-inputFocus></input>
import inputFocus from '@/directive/inputFocus.js'
directives: { inputFocus },
*/
const inputFocus = {
  bind: function (el, option) {
    let defClass = 'el-input',
      defTag = 'input';
    let value = option.value || true;
    if (typeof value === 'boolean') value = { cls: defClass, tag: defTag, foc: value };
    else value = { cls: value.cls || defClass, tag: value.tag || defTag, foc: value.foc || false };
    if (el.classList.contains(value.cls) && value.foc) {
      setTimeout(() => {
        el.getElementsByTagName(value.tag)[0].focus();
      }, 50);
    }
  },
  // update: function (el, binding) {
  //   el.value = binding.value;
  // },
};
export default inputFocus;
