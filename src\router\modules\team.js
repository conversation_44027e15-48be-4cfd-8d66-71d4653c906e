/* 团队管理路由 */

const teamDataMain = () => import('@/pages/teamManagement/teamDataMain/teamDataMain.vue'); //团队管理主数据
const MachineToolManagement = () => import('@/pages/teamManagement/MachineToolManagement.vue'); //售服团队管理
const teamCreation = () => import('@/pages/teamManagement/teamApproval/teamCreation.vue'); //团队创建申请
const teamChange = () => import('@/pages/teamManagement/teamApproval/teamChange.vue'); //团队变更申请
const teamApproval = () => import('@/pages/teamManagement/teamApproval/teamApproval.vue'); //团队申请审批

const routers = [
	{
		//团队管理
		path: '/teamDataMain',
		name: 'teamDataMain',
		component: teamDataMain,
		meta: {
			parentTitle: '团队管理',
			title: '团队主数据',
		},
	},
	{
		//团队创建申请
		path: '/teamCreation',
		name: 'teamCreation',
		component: teamCreation,
		meta: {
			parentTitle: '团队管理',
			title: '团队创建申请',
		},
	},
	{
		//团队变更申请
		path: '/teamChange',
		name: 'teamChange',
		component: teamChange,
		meta: {
			parentTitle: '团队管理',
			title: '团队变更申请',
		},
	},
	{
		//团队申请审批
		path: '/teamApproval',
		name: 'teamApproval',
		component: teamApproval,
		meta: {
			parentTitle: '团队管理',
			title: '团队申请审批',
		},
	},
	{
		// 售服团队管理
		path: '/MachineToolManagement*',
		name: 'MachineToolManagement',
		component: MachineToolManagement,
		meta: {
			parentTitle: '团队管理',
			title: '售服团队管理',
		},
	},
];

export default routers;
