<template>
	<div id="teamCreation">
		<!-- 明细组件 -->
		<DetailCom ref="DetailComRef" @close="queryTableData(1)" />
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			type="team"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twidList = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="团队创建申请" name="teamCreation">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">申请日期</span>
						<DateSelect
							:dateKeys="['startDate', 'endDate']"
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>
						<!-- 模糊查询 -->
						<el-input
							class="searchBox"
							size="small"
							v-model="searchForm.query"
							placeholder="申请人"
							@input="queryTableData(1)"
							clearable
						></el-input>

						<!--  
							所有状态：1 创建申请 2 创建驳回 3 创建申请通过 4 修改申请 5 修改驳回 6 修改申请通过 						
				      可查状态：    
							- 团队创建申请： 123 456
							- 团队变更申请：456
							- 团队申请审批：123456
						-->
						<el-checkbox
							v-model="checkboxValue"
							:true-label="1"
							:false-label="0"
							@change="
								checkboxValue == 0 && (searchForm.statusList = [1, 2, 3, 4, 5, 6]);
								checkboxValue == 1 && (searchForm.statusList = [1, 2]);
								queryTableData(1);
							"
							>仅显示未审批的申请
						</el-checkbox>

						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button type="text" class="icon-third-bt_newdoc" @click="openDetail('添加', null)">添加</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['startTime', 'endTime', 'createTime'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 使用版本 -->
									<Tooltips
										v-else-if="item.colNo == 'teamCode'"
										class="green hover-green"
										@click.native="openDetail(scope.row.approvalStatus > 2 ? '查看' : '修改', scope.row)"
										:cont-str="scope.row[item.colNo]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 使用版本 -->
									<Tooltips
										v-else-if="item.colNo == 'version'"
										:cont-str="versionMap[scope.row.version]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 使用状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:cont-str="{ 0: '禁用', 1: '正式运行', 2: '试用' }[scope.row.status]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 申请状态 -->
									<Tooltips
										v-else-if="item.colNo == 'approvalStatus'"
										:class="{
											red: scope.row.approvalStatus == 2,
											green: scope.row.approvalStatus == 3,
										}"
										:cont-str="statusMap[scope.row.approvalStatus]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<u-table-column label="" width="" align="right">
								<template slot-scope="scope">
									<el-button type="text" @click="openDetail(scope.row.approvalStatus > 2 ? '查看' : '修改', scope.row)">
										{{ scope.row.approvalStatus > 2 ? '查看' : '修改' }}
									</el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import DetailCom from './teamCreationDetail.vue'; //明细组件
import ChannelSelect from '@/components/ChannelSelect.vue';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		ChannelSelect,
		DetailCom,
	},
	name: 'teamCreation', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'teamCreation', //激活tab页
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '团队编号', colNo: 'teamCode', align: 'left', width: '100' },
				{ colName: '申请日期', colNo: 'createTime', align: 'center', width: '150' },
				{ colName: '申请人', colNo: 'createName', align: 'left', width: '' },
				{ colName: '短名称', colNo: 'teamName', align: 'left', width: '' },
				{ colName: '使用版本', colNo: 'version', align: 'left', width: '' },
				{ colName: '分销/代理', colNo: 'teamworkName', align: 'left', width: '' },
				{ colName: '业务顾问', colNo: 'salesName', align: 'left', width: '' },
				{ colName: '实施顾问', colNo: 'consultantName', align: 'left', width: '' },
				{ colName: '申请状态', colNo: 'approvalStatus', align: 'left', width: '' },
			],
			checkboxValue: 1,
			// 查询表单
			searchForm: {
				channelName: [],
				query: '',
				statusList: [1, 2],
				twidList: [],
				// 其他...
			},
			// 日期相关
			dateSelectObj: {
				endDate: '',
				startDate: '',
			},
			versionMap: {
				1: '标准版',
				2: 'OEE',
				4: '微信版',
			},
			statusMap: {
				1: '申请中',
				2: '驳回',
				3: '已审批',
				// 在创建页面的审批状态
				4: '已审批',
				5: '已审批',
				6: '已审批',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开明细
		openDetail(type, row) {
			this.$refs.DetailComRef.showDetailCom(type, row);
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectTeamByStatus'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.dateSelectObj,
				...this.searchForm,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
	},
};
</script>

<style lang="scss" scoped>
#teamCreation {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
