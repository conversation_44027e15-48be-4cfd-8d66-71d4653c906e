<template>
	<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}</span>
				<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content">
				<div class="sticky-top-5 detail-content-item border-bottom pb10">
					<!-- 标题 -->
					<p class="detail-content-title fs-14">
						<span>{{ detailForm.adminUserName }}：</span>
						<span class="ellipsis">{{ detailForm.subject }}</span>
					</p>

					<!-- 内容 -->
					<div class="detail-content-content">
						<pre>{{ detailForm.content }}</pre>
					</div>

					<!-- 图片 -->
					<div class="flex-align-center flex-wrap max-h-350 overflow-y-auto mb10 mt10">
						<div v-for="(item, index) in detailForm.urls" :key="index" class="img-box">
							<el-image class="w-100 h-100 border mr10" fit="scale-down" :src="item" :preview-src-list="detailForm.urls">
							</el-image>
						</div>
					</div>

					<!-- 日期 -->
					<div class="flex-align-center gap-10">
						<div class="fs-12 color-999 el-icon-time"> {{ dateFormat(detailForm.askTime, 'lineM') }}</div>
					</div>
				</div>

				<!-- 回答输入框 -->
				<p class="text-right">
					<el-input
						v-model="detailForm.replyContent"
						placeholder="请输入内容"
						type="textarea"
						:autosize="{ minRows: 4, maxRows: 6 }"
					></el-input>
					<el-button class="mt10 W10 min-w-200" type="primary" @click="saveReply"> 回答 </el-button>
				</p>

				<!-- 回答清单 -->
				<p class="detail-content-title fs-14">回答 · {{ replyList.length }}</p>
				<!-- 回复树 -->
				<ReplyTree :replies="replyList" :detailForm="detailForm" @refresh="queryDetail(detailForm)" />
			</div>
		</div>
	</div>
</template>
<script>
import { deepClone, dateFormat, resetValues } from '@/util/tool';
import { mapGetters } from 'vuex';
import ReplyTree from './ReplyTree.vue';

export default {
	name: 'materielsDetail',
	directives: {},
	components: {
		ReplyTree,
	},
	props: {},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '问题详情',

			detailForm: {
				parentRid: '',
				qid: '',
				replyContent: '',
				content: '',
				rid: '',
			},
			replyList: [],
		};
	},
	created() {},
	computed: {
		...mapGetters(['userInfos']),
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.replyList = [];
				this.detailForm = resetValues(this.detailForm);
				this.$emit('close', false);
			}
		},
	},
	mounted() {},
	methods: {
		// 保存回答
		async saveReply() {
			const { qid, replyContent } = this.detailForm;
			const API = 'addReply';
			try {
				const res = await this.$axios[API](JSON.stringify({ parentRid: '', qid, replyContent }));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.detailForm.replyContent = '';
					this.queryDetail(this.detailForm);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 查询详情
		async queryDetail({ qid }) {
			const API = 'getReplyListByQid';
			try {
				const res = await this.$axios[API](JSON.stringify({ qid }));
				if (res.data.success) {
					this.replyList = res.data.data;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 打开详情
		showDetailCom(item) {
			this.detailForm = { ...this.detailForm, ...item };
			this.queryDetail(this.detailForm);
			this.showCom = true;
		},

		dateFormat,

		openReply(item) {
			this.detailForm.parentRid = item.replyAuid;
		},
	},
};
</script>
<style lang="scss" scoped>
.sticky-top-5 {
	position: sticky;
	top: -5px;
	z-index: 88;
	background: #fff;
}
</style>
