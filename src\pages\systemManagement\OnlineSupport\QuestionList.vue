<template>
	<!-- 问题清单 -->
	<div id="QuestionList">
		<!-- 标题和操作 -->
		<header class="h-40 flex-align-center flex-justify-between pr10">
			<div class="label-title bolder fs-16">提问清单 · {{ total }}</div>
		</header>
		<!-- 提问列表 -->
		<main class="H100 overflow-y-auto">
			<div v-if="listData.length" class="question-list mr10">
				<div
					v-for="item in listData"
					:key="item.mrqid"
					class="question-list-item"
					:class="nowDetail.mrqid === item.mrqid ? 'active' : ''"
					@click="getDetail(item)"
				>
					<!-- <Tooltips :cont-str="jointString('', `${item.questioner}： \n`, item.problem)"></Tooltips> -->
					<span class="ellipsis" :title="jointString('', `${item.questioner}： \n`, item.problem)">
						{{ jointString('', `${item.questioner}： \n`, item.problem) }}
					</span>
					<!-- 已回答 -->
					<div v-if="item.replyContentLatest" class="color-999 flex-column">
						<div class="flex-align-center gap-10">
							<!-- replyUserNameLast !== item.consultantName -->
							<span>{{ item.replyUserNameLast }} {{ dateFormat(item.replyLatestDate, 'lineM') }} 的回答 </span>
							<!-- 研发：问题状态 状态 0：可能存在bug 1：存在bug 2：误报 3：不为bug -->
							<span v-if="workbenche.type == '研发'" :class="statusMap[item.status].color">
								{{ statusMap[item.status].label }}
							</span>
							<span v-else class="red"> {{ item.isReply ? '' : '(未回复)' }}</span>
						</div>
						<Tooltips :cont-str="item.replyContentLatest"></Tooltips>
					</div>
					<!-- 等待回答 -->
					<div v-else class="red flex-align-center gap-10">
						<span> {{ dateFormat(item.questionDate, 'lineM') }} | 等待 @{{ item.consultantName }} 回答</span>
					</div>
				</div>
			</div>
			<div v-else class="H100 flex-center">
				<div class="fs-16 color-999"> 👈 请先选择左侧清单的内容，查看对应的提问清单 </div>
			</div>
		</main>
		<!-- 分页 -->
		<footer class="h-40 flex-align-center flex-justify-end">
			<el-pagination
				background
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				:current-page="searchForm.pageNum"
				:page-sizes="[50, 100, 500]"
				:page-size="searchForm.pageSize"
				:total="total"
				layout="total, sizes, prev, pager, next, jumper"
			></el-pagination>
		</footer>
	</div>
</template>
<script>
import { jointString, resetValues, dateFormat, deepClone } from '@/util/tool';
import { mapGetters } from 'vuex';

export default {
	name: 'QuestionList',
	components: {},
	props: {
		// 资源描述数据
		resData: {
			type: Object,
			default: () => {},
		},
		// 工作台
		workbenche: {
			type: Object,
			default: () => {},
		},

		// 详情数据
		nowDetail: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			searchForm: {
				columnNumber: '',
				pageNum: 1,
				pageSize: 100,
				rcid: '',
			},
			listData: [], // 提问列表
			total: 0, // 总条数
			statusMap: {
				0: { label: '(待处理)', color: 'red' },
				1: { label: '(BUG已接收)', color: 'blue' },
				2: { label: '(误报)', color: 'orange' },
				3: { label: '(非BUG)', color: '' },
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['menuTitle', 'userInfos', 'menuList']),
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 获取详情
		async getDetail(item) {
			if (item?.mrqid === this.nowDetail?.mrqid) {
				this.$emit('getDetail', 'reset', item);
			} else {
				this.$emit('getDetail', 'query', item);
			}
		},

		// 打开编辑弹窗
		openDialog() {
			const { parentRcid, rcid, columnNumber } = this.searchForm;
			this.$refs.QuestionDialog.open({ columnNumber, rcid, parentRcid });
		},

		// 查询清单
		async queryList() {
			if (this.workbenche.type == '研发' && !this.resData.topRcid) {
				return console.log('没有topRcid');
			}
			this.searchForm.topRcid = this.resData.topRcid;
			this.searchForm.tid = this.resData.nowTid;
			this.searchForm.isSelectAll = 0;
			// this.searchForm.isAllOfConsult = this.searchForm.tid ? 0 : 1; // 全部
			// this.searchForm.isAllOfSystemAdmin = this.searchForm.topRcid ? 0 : 1; // 全部

			const API = this.workbenche.type == '研发' ? 'selectRDQuestionListByTopId' : 'selectQuestionListByMenuAndColumn';
			try {
				const res = await this.$axios[API](JSON.stringify(this.searchForm));
				if (res.data.success) {
					this.listData = res.data.data;
					this.total = res.data.totalItems;
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 分页
		handleSizeChange(size) {
			this.searchForm.pageSize = size;
			this.queryList();
		},
		// 当前页
		handleCurrentChange(page) {
			this.searchForm.pageNum = page;
			this.queryList();
		},
		// 清空数据
		cleanData() {
			this.listData = [];
			this.total = 0;
			// this.searchForm = resetValues(this.searchForm);
		},
		dateFormat: dateFormat, // 日期格式化
		jointString, // 拼接字符串
	},
};
</script>

<style lang="scss" scoped>
#QuestionList {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	display: flex;
	flex-direction: column;
	gap: 10px;
	.question-list {
		font-size: 14px;
		// height: 100%;
		// overflow-y: auto;
		display: flex;
		flex-direction: column;
		gap: 10px;
		&-item {
			padding: 15px;
			background: #f9f9f9;
			transition: all 0.3s;
			border-radius: 8px;
			border: 1px solid #f2f2f2;
			cursor: pointer;
			display: flex;
			flex-direction: column;
			gap: 10px;
			&:hover {
				border-color: #d7d7d7;
				background: #e9f5f1;
				border-color: #1e9d6f;
				z-index: 1;
			}
			&.active {
				position: sticky;
				top: 0;
				bottom: 0;
				z-index: 2;
				border-color: #1e9d6f;
				background: #e9f5f1;
				box-shadow: 0 2px 12px 0 rgba(30, 157, 111, 0.2); // 增强阴影效果
				// &::after {
				// 	content: '📑';
				// 	position: absolute;
				// 	right: -5px;
				// 	top: -10%;
				// }
			}
		}
	}
}
</style>
