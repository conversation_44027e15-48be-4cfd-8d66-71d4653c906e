<template>
	<div id="lightMain">
		<!-- 导入弹窗 -->
		<ImportTable ref="ImportTable" @refresh="queryTableData" />
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="三色灯清单" name="lightMain">
				<BaseLayout>
					<template #header>
						<span class="search-label">生产日期</span>
						<DateSelect
							@change="
								searchForm.productionStartTime = $event.startTime;
								searchForm.productionEndTime = $event.endTime;
								queryTableData(1);
							"
						/>

						<span class="search-label">发货日期</span>
						<DateSelect
							@change="
								searchForm.invoiceStartTime = $event.startTime;
								searchForm.invoiceEndTime = $event.endTime;
								queryTableData(1);
							"
						/>
						<SearchHistoryInput name="sim" placeholder="三色灯编号" v-model.trim="searchForm.sim" @input="queryTableData(1)" />

						<el-checkbox-group v-model="searchForm.onLine" @change="queryTableData(1)">
							<el-checkbox label="0">离线</el-checkbox>
							<el-checkbox label="1">在线</el-checkbox>
						</el-checkbox-group>

						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新 </el-button>
						<el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<el-button type="text" class="el-icon-check" @click="openDialogUpgrade">批量升级</el-button>
							<el-button type="text" class="icon-third_xiugai" @click="editLightAll">批量修改</el-button>
							<el-button type="text" class="el-icon-close" @click="deleteAll">批量删除</el-button>
							<el-button type="text" class="icon-third-bt_newdoc" @click="dialogAdd = true">添加</el-button>
							<!-- 导入按钮 -->
							<ImportBtn @trigger="openImport" />
							<!-- 导出按钮 -->
							<ExportBtn @trigger="openExport" />
							<el-button type="text" class="icon-third_searchC" v-popover:thSearch>查找</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:dialog-data="tableColumn"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							@header-dragend="headerDragend"
							@reset="updateColumn(tableColumnCopy)"
							@show-field="updateColumn"
							@selection-change="selectedData = $event"
							selectTrClass="selectTr"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column type="selection" width="30" label=""></u-table-column>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="(item, index) in tableColumn.filter(item => item.state)"
								:key="item.colNo + index"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['productionDate', 'invoiceTime', 'bindingTime', 'operatorExpiration'].includes(item.colNo)"
										:toolClass="'tdTwoNormal'"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<Tooltips
										v-else-if="['simTime', 'updateTime', 'createTime'].includes(item.colNo)"
										:toolClass="'tdTwoNormal'"
										:cont-str="dateFormat(scope.row[item.colNo], 'ALL')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 编号 -->
									<Tooltips
										v-else-if="item.colNo == 'sim'"
										:toolClass="'tdTwoNormal'"
										class="hover-green green"
										@click.native="openLightDetail(scope.row, null, true)"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<!-- 出厂检查 -->
									<Tooltips
										v-else-if="item.colNo == 'lightType'"
										:cont-str="lightTypeMap[scope.row.lightType]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 升级状态 -->
									<Tooltips
										v-else-if="item.colNo == 'updateStatu'"
										:cont-str="updateStatusMap[scope.row.updateStatu]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 在线状态 -->
									<div v-else-if="item.colNo == 'onLine'">
										<span v-if="scope.row.onLine">
											<i
												v-if="scope.row.currentState"
												class="icon-third_graysd fs-24"
												:style="{ color: onlineColorMap[scope.row.currentState] }"
											></i>
											<span v-else>{{ onlineStatusMap[scope.row.onLine] }}</span>
										</span>
										<span v-else>{{ onlineStatusMap[scope.row.onLine] }}</span>
									</div>

									<Tooltips
										v-else
										:toolClass="'tdTwoNormal'"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="三色灯产品" name="lithgtProData">
				<lithgtProData v-if="activeTab == 'lithgtProData'" />
			</el-tab-pane>
			<el-tab-pane label="固件版本" name="firmwareVersion">
				<firmwareVersion v-if="activeTab == 'firmwareVersion'" />
			</el-tab-pane>
		</el-tabs>
		<div id="lightMainToggle">
			<div style="padding: 0">
				<div
					style="
						border-radius: 15px 15px 0 0;
						width: 100%;
						border: 1px solid #e9e9e9;
						background: #f9f9f9;
						border-bottom: 1px solid transparent;
						box-sizing: border-box;
					"
				>
					<el-row class="orgManageHead" style="padding: 10px; box-sizing: border-box">
						<el-col :span="8" style="font-size: 18px; line-height: 40px; font-weight: 500">
							<span style="margin-left: 0.5vw">{{ lightEdit ? '修改三色灯参数' : '查看三色灯参数' }}</span>
						</el-col>
						<el-col :span="8" :offset="8" style="text-align: right">
							<el-button v-show="!lightEdit" type="text" class="icon-third_xiugai" @click="lightEdit = true">修改</el-button>
							<el-button type="text" class="el-icon-arrow-left" @click="cleartLight">返回</el-button>
						</el-col>
					</el-row>
					<div
						style="
							background: #fff;
							border-top: 1px solid #d7d7d7;
							padding: 0 10px;
							box-sizing: border-box;
							min-height: 80vh;
							overflow: auto;
						"
					>
						<div style="padding-left: 1vw">
							<p class="p_row" style="align-items: flex-end">
								<span style="display: flex-inline; align-items: center">
									<span class="p_label"></span>
									<span class="p_text">基本信息</span>
								</span>
								<canvas id="qrcodeLight" style="margin-bottom: -13px"></canvas>
							</p>
							<table class="workCtable" cellpadding="5" cellspacing="0">
								<tr>
									<th>三色灯编号</th>
									<th>产品</th>
									<th>绿灯闪烁间隔(0.1秒)</th>
									<th>黄灯闪烁间隔(0.1秒)</th>
									<th>红灯闪烁间隔(0.1秒)</th>
									<th>离线重启次数(次)</th>
									<th>消抖时长(毫秒)</th>
									<th>MQTT连接方式</th>
								</tr>
								<tr>
									<td>
										<span>{{ lightInfos.sim }}</span>
									</td>
									<td>
										<el-select v-show="lightEdit" class="w-200" v-model="lightInfos.productId" placeholder="请选择产品">
											<el-option
												v-for="item in productList"
												:key="'product' + item.id"
												:label="item.productName"
												:value="item.id"
											>
											</el-option>
										</el-select>
										<span v-show="!lightEdit">{{ lightInfos.productName }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入绿灯闪烁间隔"
											v-model="lightInfos.greenInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.greenInterval }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入黄灯闪烁间隔"
											v-model="lightInfos.yellowInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.yellowInterval }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入红灯闪烁间隔"
											v-model="lightInfos.redInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.redInterval }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入离线重启次数"
											v-model="lightInfos.offlineReset"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.offlineReset }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入消抖时长"
											v-model="lightInfos.debounceTime"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.debounceTime }}</span>
									</td>
									<td>
										<el-select type="number" v-show="lightEdit" placeholder="请选择MQTT连接方式" v-model="lightInfos.mqttType">
											<el-option
												v-for="key in Object.keys(mqttTypeMap)"
												:key="key"
												:label="mqttTypeMap[key]"
												:value="Number(key)"
											>
											</el-option>
										</el-select>
										<span v-show="!lightEdit">{{ mqttTypeMap[lightInfos.mqttType] }}</span>
									</td>
								</tr>
								<tr>
									<th>心跳间隔(秒)</th>
									<th>脉冲间隔(秒)</th>
									<th>信号强度</th>
									<th>MAC地址</th>
									<th>消息版本</th>
									<th>数据周期(秒)</th>
									<th colspan="2">空数据发送</th>
								</tr>
								<tr>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入心跳间隔"
											v-model="lightInfos.heartInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.heartInterval }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入脉冲间隔"
											v-model="lightInfos.pulseInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.pulseInterval }}</span>
									</td>
									<td>
										<span>{{ lightInfos.signalStrength }}</span>
									</td>
									<td>
										<span>{{ lightInfos.macUrl }}</span>
									</td>
									<td>
										<span>{{ lightInfos.messageVersion }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入计数间隔(秒)"
											v-model="lightInfos.countInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.countInterval }}</span>
									</td>
									<td colspan="2">
										<el-select
											class="W100"
											type="number"
											v-show="lightEdit"
											placeholder="请选择空数据发送"
											v-model="lightInfos.countZeroReport"
										>
											<el-option
												v-for="item in noDataList"
												:key="'nd' + item.id"
												:label="item.label"
												:value="item.id"
											></el-option>
										</el-select>
										<span v-show="!lightEdit">{{ noDataMap[lightInfos.countZeroReport] }}</span>
									</td>
								</tr>
								<tr>
									<th colspan="2">URL</th>
									<th>用户名</th>
									<th>密码</th>
									<th>产品ProductKey</th>
									<th>产品唯一标识</th>
									<th colspan="2">设备密钥</th>
								</tr>
								<tr>
									<td colspan="2">
										<!-- <el-input type="text" v-show="lightEdit" placeholder="请输入MQTT连接地址"
											v-model="lightInfos.mqttUrl"></el-input> -->
										<el-autocomplete
											v-show="lightEdit"
											placeholder="请输入MQTT连接地址"
											v-model="lightInfos.mqttUrl"
											:fetch-suggestions="
												(queryString, cb) => {
													querySaved(queryString, cb, 'urls');
												}
											"
											@select="handleSelect($event, 'mqttUrl')"
											:debounce="500"
											class="W100"
										>
											<div slot-scope="scope">
												<div style="width: max-content"
													><span>{{ scope.item }}</span></div
												>
											</div>
										</el-autocomplete>
										<span v-show="!lightEdit">{{ lightInfos.mqttUrl }}</span>
									</td>
									<td>
										<!-- <el-input type="text" v-show="lightEdit" placeholder="请输入MQTT用户名"
											v-model="lightInfos.mqttUser"></el-input> -->
										<el-autocomplete
											v-show="lightEdit"
											placeholder="请输入MQTT用户名"
											v-model="lightInfos.mqttUser"
											:fetch-suggestions="
												(queryString, cb) => {
													querySaved(queryString, cb, 'users');
												}
											"
											@select="handleSelect($event, 'mqttUser')"
											:debounce="500"
											class="W100"
										>
											<div slot-scope="scope">
												<div style="width: max-content"
													><span>{{ scope.item }}</span></div
												>
											</div>
										</el-autocomplete>
										<span v-show="!lightEdit">{{ lightInfos.mqttUser }}</span>
									</td>
									<td>
										<!-- <el-input type="password" v-show="lightEdit" placeholder="请输入MQTT密码"
											v-model="lightInfos.mqttPawd"></el-input> -->
										<el-autocomplete
											v-show="lightEdit"
											placeholder="请输入MQTT密码"
											v-model="lightInfos.mqttPawd"
											:fetch-suggestions="
												(queryString, cb) => {
													querySaved(queryString, cb, 'pawds');
												}
											"
											@select="handleSelect($event, 'mqttPawd')"
											:debounce="500"
											class="W100"
										>
											<div slot-scope="scope">
												<div style="width: max-content"
													><span>{{ scope.item }}</span></div
												>
											</div>
										</el-autocomplete>
										<span v-show="!lightEdit">{{ lightInfos.mqttPawd }}</span>
									</td>
									<td>
										<el-input
											type="text"
											v-show="lightEdit"
											placeholder="请输入产品roductKey"
											v-model="lightInfos.locaProductKey"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.locaProductKey }}</span>
									</td>
									<td>
										<el-input
											type="text"
											v-show="lightEdit"
											placeholder="请输入产品唯一标识"
											v-model="lightInfos.locaDeviceName"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.locaDeviceName }}</span>
									</td>
									<td colspan="2">
										<el-input
											type="text"
											v-show="lightEdit"
											placeholder="请输入物联网平台设备密钥"
											v-model="lightInfos.locaDeviceSecret"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.locaDeviceSecret }}</span>
									</td>
								</tr>
							</table>
							<div :style="{ visibility: lightInfos.updated && lightInfos.updated != 1 ? 'visible' : 'hidden' }" class="bot_left">
								<span
									>*<span>{{ getUpdateStr(lightInfos.updated) }}</span></span
								>
								<el-button
									type="text"
									class="el-icon-refresh-right"
									@click="openLightDetail(lightInfos, true, !lightEdit)"
									style="margin-left: 2vw"
									>刷新
								</el-button>
							</div>
							<div style="text-align: right; margin-top: 0.5vh">
								<el-button
									type="text"
									style="margin-right: 2vw"
									v-show="multipleLights.length > 1"
									:disabled="mulIndex == 0"
									@click="openLight('pre')"
									>上一个</el-button
								>
								<el-button
									type="text"
									style="margin-right: 8vw"
									v-show="multipleLights.length > 1"
									:disabled="mulIndex == multipleLights.length - 1"
									@click="openLight('next')"
									>下一个</el-button
								>
								<el-button
									type="text"
									@click="saveThreeClight"
									v-show="lightEdit"
									style="
										width: 100px;
										margin-right: 1.5vw;
										background-color: #28d094 !important;
										color: #fff !important;
										border-color: #dcdfe6 !important;
									"
									>保存</el-button
								>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 放大镜查询 -->
		<el-popover v-model="searchPopver" ref="thSearch" placement="bottom-end" width="400" :visible-arrow="false">
			<!-- 放大镜搜索表单 -->
			<div class="searchPop p0 fs-12">
				<div class="searchPop-header">
					<span>查找条件</span>
					<i class="searchPop-header-close el-icon-close" @click.stop="searchPopver = false"></i>
				</div>
				<el-form :model="searchForm" label-width="80px" label-position="left" class="W100 pt10 pr20">
					<el-form-item label="三色灯编号" prop="productName">
						<el-input placeholder="请输入三色灯编号" v-model="searchForm.sim" size="mini" @input="queryTableData(1)"></el-input>
					</el-form-item>
					<el-form-item label="产品" prop="productName">
						<el-input placeholder="请输入产品" v-model="searchForm.productName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="芯片" prop="chipName">
						<el-input placeholder="请输入芯片" v-model="searchForm.chipName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="固件版本" prop="firmwareName">
						<el-input placeholder="请输入固件版本" v-model="searchForm.firmwareName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="运营商" prop="operator">
						<el-input placeholder="请输入运营商" v-model="searchForm.operator" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="绑定团队" prop="bindingTeamName">
						<el-input placeholder="请输入绑定团队" v-model="searchForm.bindingTeamName" size="mini"></el-input>
					</el-form-item>
				</el-form>
				<div class="mt20 border-top text-right">
					<el-button type="text" class="color-666" @click.stop="queryTableData(1)">确定</el-button>
				</div>
			</div>
		</el-popover>
		<!-- 批量升级固件版本 -->
		<el-dialog :visible.sync="dialogUpgrade" width="30%" :close-on-click-modal="false" @close="cancelUpLevel">
			<el-row slot="title">批量升级固件版本</el-row>
			<el-form :model="upgradeForm" label-width="7vw" label-position="left">
				<el-form-item label="产品" prop="chipName">
					<el-input placeholder="请输入产品" v-model="upgradeForm.productName"></el-input>
				</el-form-item>
				<el-form-item label="芯片" prop="phoneNo">
					<el-input placeholder="请输入芯片" v-model="upgradeForm.chipName"></el-input>
				</el-form-item>
				<el-form-item label="目标固件版本" prop="ftid">
					<el-select class="W100" v-model="upgradeForm.ftid" placeholder="请选择适用产品">
						<el-option
							v-for="item in updateFirmwareList"
							:key="'product' + item.ftid"
							:label="item.firmwareName + ' ' + dateFormat(item.releaseTime, 'line')"
							:value="item.ftid"
						>
						</el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveUpLevel">批量升级</el-button>
			</el-row>
		</el-dialog>
		<!-- 添加三色灯 -->
		<el-dialog :visible.sync="dialogAdd" width="30%" :close-on-click-modal="false" @close="cancelAddLight">
			<el-row slot="title">添加三色灯</el-row>
			<el-form :model="editLightForm" label-width="6vw" label-position="left" ref="addLightRef" :rules="editRules">
				<el-form-item label="三色灯编号" prop="sim">
					<el-input placeholder="请输入三色灯编号" v-model="editLightForm.sim"></el-input>
				</el-form-item>
				<el-form-item label="产品" prop="productId">
					<el-select clearable class="W100" v-model="editLightForm.productId" placeholder="请选择产品">
						<el-option v-for="item in productList" :key="'product' + item.id" :label="item.productName" :value="item.id">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="芯片" prop="chipName">
					<el-autocomplete
						placeholder="请输入芯片"
						v-model="editLightForm.chipName"
						:fetch-suggestions="queryChipData"
						class="W100"
					>
					</el-autocomplete>
					<!-- <el-input placeholder="请输入芯片" v-model="editLightForm.chipName" ></el-input> -->
				</el-form-item>
				<el-form-item label="运营商" prop="operator">
					<el-input placeholder="移动" v-model="editLightForm.operator"></el-input>
				</el-form-item>
				<el-form-item label="运营商到期时间" prop="newNum">
					<el-date-picker
						v-model="editLightForm.operatorExpiration"
						type="date"
						class="W100"
						placeholder="请选择运营商到期时间"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="渠道" prop="channel">
					<el-select class="W100" v-model="editLightForm.channel" placeholder="请选择渠道">
						<el-option v-for="item in channelList" :key="'channel' + item.id" :label="item.channelName" :value="item.id">
						</el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveAddLight">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData, setCookie, getCookie } from '@/util/tool'; //按需引入常用工具函数
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
import lithgtProData from '@/pages/threeColorLightManagement/lithgtProData';
import firmwareVersion from '@/pages/threeColorLightManagement/firmwareVersion';
import ImportTable from '@/components/ImportTable'; //导入组件
import ImportBtn from '@/components/ImportTable/ImportBtn'; //导入按钮
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
import ExportTable from '@/components/ExportTable'; //导出组件
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import QRCode from 'qrcode';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		lithgtProData,
		firmwareVersion,
		ExportTable,
		ExportBtn,

		ImportTable,
		ImportBtn,
	},
	name: 'lightMainData',
	data() {
		return {
			activeTab: 'lightMain',

			// 表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '三色灯编号', colNo: 'sim', align: 'left', width: '100' },
				{ colName: '产品', colNo: 'productName', align: 'left', width: '' },
				{ colName: '生产日期', colNo: 'productionDate', align: 'left', width: '90' },
				{ colName: '出厂检查', colNo: 'lightType', align: 'left', width: '' },
				{ colName: '发货日期', colNo: 'invoiceTime', align: 'left', width: '90' },
				{ colName: '绑定团队', colNo: 'bindingTeamName', align: 'left', width: '' },
				{ colName: '绑定日期', colNo: 'bindingTime', align: 'left', width: '90' },
				{ colName: '芯片', colNo: 'chipName', align: 'left', width: '' },
				{ colName: '固件版本', colNo: 'firmwareName', align: 'left', width: '160' },
				{ colName: '目标固件版本', colNo: 'updateFirmwareName', align: 'left', width: '110' },
				{ colName: '升级状态', colNo: 'updateStatu', align: 'left', width: '' },
				{ colName: '升级尝试次数', colNo: 'upgradeCount', align: 'right', width: '70' },
				{ colName: '本地时间', colNo: 'simTime', align: 'left', width: '90' },
				{ colName: '最后心跳', colNo: 'updateTime', align: 'left', width: '90' },
				{ colName: '在线状态', colNo: 'onLine', align: 'center', width: '' },
				{ colName: '离线小时', colNo: 'offlineDuration', align: 'right', width: '' },
				{ colName: '运营商', colNo: 'operator', align: 'left', width: '' },
				{ colName: '运营商到期日', colNo: 'operatorExpiration', align: 'left', width: '90' },
				{ colName: '运营商剩余天数', colNo: 'remainderDays', align: 'right', width: '70' },
				{ colName: '创建人', colNo: 'createUserName', align: 'left', width: '80' },
				{ colName: '创建时间', colNo: 'createTime', align: 'left', width: '90' },
			],
			tableColumnCopy: [],

			// 搜索表单
			searchForm: {
				chipName: '',
				firmwareName: '',
				bindingTeamName: '', //绑定团队
				operator: '',
				productName: '',
				sim: '',
				onLine: ['0', '1'],
			},
			selectedData: [], //批量选择的数据
			onlineStatusMap: {
				false: '离线',
				true: '在线',
			},
			onlineColorMap: {
				'001': '#28D094',
				'010': '#F7A944',
				100: '#DC143C',
			},
			lightTypeMap: {
				0: '未测试',
				1: '测试未通过',
				2: '待出库',
				3: '已经发货',
				4: '退货',
				5: '报废',
			},
			updateStatusMap: {
				0: '待升级',
				1: '升级中',
				2: '升级失败',
				3: '升级成功',
			},
			// 放大镜查询
			searchPopver: false,
			dialogUpgrade: false,
			// 升级表单
			upgradeForm: {
				productName: '',
				chipName: '',
				ftid: '',
			},

			updateFirmwareList: [], //可升级固件列表
			updateSims: [],
			//三色灯编辑
			editLightForm: {
				sim: '',
				productId: '',
				channel: '',
				chipName: '',
				operator: '',
				operatorExpiration: '',
			},
			dialogAdd: false,
			editRules: {
				sim: [{ required: true, message: '请输入三色灯编号', trigger: 'blur' }],
				productId: [{ required: true, message: '请选择适用产品', trigger: ['blur', 'change'] }],
				chipName: [{ required: true, message: '请输入芯片', trigger: 'blur' }],
			},
			productList: [], //产品列表
			channelList: [
				{
					id: 0,
					channelName: '未知',
				},
				{
					id: 1,
					channelName: '预装',
				},
				{
					id: 2,
					channelName: '改造',
				},
			],

			lightInfos: {
				qrCode: '',
				sim: '',
				greenInterval: 0,
				yellowInterval: 0,
				redInterval: 0,
				offlineReset: 0,
				debounceTime: 0,
				heartInterval: '',
				pulseInterval: '',
				signalStrength: '',
				macUrl: '',
				messageVersion: '',
				updated: 1,
				//物联网平台设置
				mqttType: 0,
				countInterval: '',
				countZeroReport: 0, //计数为0是否上传
				mqttUrl: '',
				mqttUser: '',
				mqttPawd: '',
				locaProductKey: '', //产品所属ProductKey
				locaDeviceName: '', //设备在产品内唯一标识
				locaDeviceSecret: '', //物联网平台为设备颁发的设备密钥
			},
			multipleLights: [],
			mulIndex: '',
			lightEdit: false,

			mqttTypeMap: {
				0: '阿里云物联网平台',
				1: '本地MQTT服务',
				2: '3A精益华为云',
			},
			noDataList: [
				{
					id: 0,
					label: '上传',
				},
				{
					id: 1,
					label: '不上传',
				},
			],
			noDataMap: {
				0: '上传',
				1: '不上传',
			},

			isUpdate: false,
		};
	},
	// 监听属性 类似于data概念
	computed: {},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.tableColumnCopy = deepClone(this.tableColumn);
		this.tableColumn = getColumn(this.tableColumn, this.$options.name);
		this.queryTableData();
		this.queryProductList();
	},
	activated() {
		this.changeTab();
		this.queryProductList();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		//切换tab页
		changeTab(tab, event) {
			if (this.activeTab == 'lightMain') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			this.$axios
				.getTricolourLightList(JSON.stringify(this.searchForm))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('getTricolourLightList |' + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 展示字段
		updateColumn(data) {
			this.tableColumn = updateColumn(this.$options.name, data);
			this.sortChange(this.tableSort, true);
		},
		// 列宽拖动
		headerDragend(newWidth, oldWidth, column) {
			updateColumnWidth(this.$options.name, column.property, newWidth, this.tableColumn);
		},

		toggleAside(value, type) {
			//侧边栏滑出
			const workDiv = document.getElementById('lightMainToggle');
			const outDiv = document.getElementById('lightMain');
			if (workDiv) {
				let newValue = 0;
				if (value) {
					newValue = 0;
					outDiv.style.height = 'auto';
					outDiv.style.overflow = 'initial';
				} else {
					newValue = '110%';
					outDiv.style.height = '100%';
					outDiv.style.overflow = 'hidden';
				}
				workDiv.style.left = newValue;
			}
		},

		cancelUpLevel() {
			this.dialogUpgrade = false;
			this.updateFirmwareList = [];
			this.updateSims = [];
			this.upgradeForm.chipName = '';
			this.upgradeForm.productName = '';
			this.upgradeForm.ftid = '';
		},
		saveUpLevel() {
			const ftid = this.upgradeForm.ftid;
			if (!ftid) {
				this.$message.warning('请选择目标固件版本');
				return;
			}
			const str = JSON.stringify({
				sims: this.updateSims,
				ftid: ftid,
			});
			this.$axios
				.batchFirmwareUpgrade(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.cancelUpLevel();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('batchFirmwareUpgrade |' + error);
				});
		},
		//日期format
		dateFormat: dateFormat,

		editLightAll() {
			this.multipleLights = [];
			if (this.selectedData && this.selectedData.length > 0) {
				this.multipleLights = this.selectedData;
				this.mulIndex = 0;
				this.openLightDetail(this.multipleLights[this.mulIndex]);
			} else {
				this.$message.warning('请勾选三色灯后再修改参数');
			}
		},
		openDialogUpgrade() {
			let sims = [],
				flag = true;
			if (this.selectedData && this.selectedData.length > 0) {
				const proId = this.selectedData[0].productId,
					chipName = this.selectedData[0].chipName,
					productName = this.selectedData[0].productName;
				// console.log('aaa',this.selectedData)
				this.selectedData.every(item => {
					// console.log('aaaa',proId,chipName,item.productId,item.chipName)
					if (item.productId == proId && item.chipName == chipName) {
						sims.push(item.sim);
						return true;
					} else {
						// console.log('prrrr',proId,chipName,item.productId,item.chipName)
						flag = false;
						return false;
					}
				});
				// console.log('aabb',flag)
				if (flag) {
					this.updateSims = sims;
					this.updateFirmwareList = [];
					this.$axios
						.selectUpgradeFirmwareByProductId(JSON.stringify({ productId: proId, chipName: chipName }))
						.then(res => {
							if (res.data.success) {
								this.updateFirmwareList = res.data.data;
								this.upgradeForm.productName = productName;
								this.upgradeForm.chipName = chipName;
								this.upgradeForm.ftid = '';
								this.dialogUpgrade = true;
							} else {
								this.$message.warning(res.data.message);
							}
						})
						.catch(error => {
							console.log('selectUpgradeFirmwareByProductId |' + error);
						});
				} else {
					this.$message.warning('必须是同产品且同芯片的三色灯可以进行批量升级');
				}
			} else {
				this.$message.warning('请勾选三色灯后再批量升级');
			}
		},
		// 批量删除三色灯
		deleteAll() {
			const simList = [],
				tlIdList = [];
			if (this.selectedData && this.selectedData.length > 0) {
				this.$confirm('勾选的三色灯将被删除', '删除三色灯', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.selectedData.forEach(item => {
							simList.push(item.sim);
							tlIdList.push(item.tlId);
						});
						this.$axios
							.batchDeleteTricolourLight(
								JSON.stringify({
									simList: simList,
									tlIdList: tlIdList,
								}),
							)
							.then(res => {
								if (res.data.success) {
									this.$succ('操作成功!');
									this.queryTableData(1);
								} else {
									this.$err(res.data.message);
								}
							})
							.catch(error => {
								console.log('batchDeleteTricolourLight |' + error);
							});
					})
					.catch(() => {
						this.$message.info('已取消');
					});
			} else {
				this.$message.warning('请勾选三色灯后再删除');
			}
		},

		cancelAddLight() {
			this.dialogAdd = false;
			this.editLightForm.sim = '';
			this.editLightForm.productId = '';
			this.editLightForm.operator = '';
			this.editLightForm.chipName = '';
			this.editLightForm.channel = '';
			this.editLightForm.operatorExpiration = '';
			this.$refs.addLightRef.resetFields();
		},
		// 添加三色灯保存
		saveAddLight() {
			const sim = this.editLightForm.sim,
				proId = this.editLightForm.productId,
				chipName = this.editLightForm.chipName;
			if (!sim) {
				this.$message.warning('请输入三色灯编号');
				return;
			}
			if (!proId) {
				this.$message.warning('请选择适用产品');
				return;
			}
			if (!chipName) {
				this.$message.warning('请输入芯片');
				return;
			}
			const str = JSON.stringify({
				channel: this.editLightForm.channel,
				chipName: this.editLightForm.chipName,
				operator: this.editLightForm.operator,
				operatorExpiration: this.editLightForm.operatorExpiration
					? new Date(this.editLightForm.operatorExpiration).getTime()
					: '',
				productId: proId,
				sim: sim,
			});
			this.$axios
				.addTricolourLight(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.saveChipInput(this.editLightForm.chipName);
						this.cancelAddLight();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addTricolourLight |' + error);
				});
		},
		queryProductList() {
			this.productList = [];
			this.$axios
				.getTricolourProductList(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.productList = res.data.data;
					}
				})
				.catch(error => {
					console.log('getTricolourProductList |' + error);
				});
		},
		cleartLight() {
			this.toggleAside(0);
			this.lightInfos.sim = '';
			this.lightInfos.qrCode = '';
			this.lightInfos.greenInterval = 0;
			this.lightInfos.yellowInterval = 0;
			this.lightInfos.redInterval = 0;
			this.lightInfos.offlineReset = 0;
			this.lightInfos.debounceTime = 0;
			this.lightInfos.heartInterval = 0;
			this.lightInfos.pulseInterval = 0;
			this.lightInfos.signalStrength = '';
			this.lightInfos.macUrl = '';
			this.lightInfos.messageVersion = '';
			this.lightInfos.updated = 1;
			this.lightInfos.countInterval = '';
			this.lightInfos.countZeroReport = 0;
			this.lightInfos.mqttUrl = '';
			this.lightInfos.mqttUser = '';
			this.lightInfos.mqttPawd = '';
			this.lightInfos.mqttType = 0;
			this.lightInfos.locaProductKey = '';
			this.lightInfos.locaDeviceName = '';
			this.lightInfos.locaDeviceSecret = '';
			this.mulIndex = 0;
			this.multipleLights = [];
			this.$refs.uTableRef.clearSelection();

			// 如果已经触发修改，则刷新表格
			if (this.isUpdate) {
				this.queryTableData();
			}
		},

		openLightDetail(lightInfos, flag, edit) {
			if (!lightInfos.sim) {
				this.$message.warning('数据异常');
				return;
			}
			this.$axios
				.selectTricolourLightParamBySim(JSON.stringify({ sim: lightInfos.sim }))
				.then(res => {
					if (res.data.success) {
						this.lightInfos = { ...this.lightInfos, ...res.data.data };
						this.lightInfos.sim = res.data.data.sim;
						this.lightInfos.qrCode = res.data.data.simQRCode.replace(/\\/, '');
						this.lightInfos.greenInterval = res.data.data.green;
						this.lightInfos.yellowInterval = res.data.data.yellow;
						this.lightInfos.redInterval = res.data.data.red;
						this.lightInfos.offlineReset = res.data.data.restartCount;
						this.lightInfos.debounceTime = res.data.data.debounce;
						this.lightInfos.heartInterval = res.data.data.heartbeat;
						this.lightInfos.pulseInterval = res.data.data.circleduration;
						this.lightInfos.signalStrength = res.data.data.signal;
						this.lightInfos.macUrl = res.data.data.mac;
						this.lightInfos.messageVersion = res.data.data.version;
						this.lightInfos.mqttType = res.data.data.mqttType || 0;
						this.lightInfos.countInterval = res.data.data.countInterval;
						this.lightInfos.countZeroReport = res.data.data.countZeroReport || 0;

						if (!flag) {
							this.toggleAside(1, 'threeClight');
							setTimeout(() => {
								this.getQrcode(this.lightInfos.qrCode);
							}, 500);
						} else {
							this.getQrcode(this.lightInfos.qrCode);
						}
						if (!edit) {
							this.lightEdit = true;
						} else {
							this.lightEdit = false;
						}
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTricolourLightParamBySim |' + error);
				});
		},
		saveThreeClight() {
			let str,
				sim = this.lightInfos.sim;
			const green = this.lightInfos.greenInterval + '',
				yellow = this.lightInfos.yellowInterval + '',
				red = this.lightInfos.redInterval + '';
			if (!sim) {
				return;
			}
			if (green.indexOf('.') > -1) {
				this.$message.warning('绿灯闪烁间隔不能包含小数');
				return;
			}
			if (yellow.indexOf('.') > -1) {
				this.$message.warning('黄灯闪烁间隔不能包含小数');
				return;
			}
			if (red.indexOf('.') > -1) {
				this.$message.warning('红灯闪烁间隔不能包含小数');
				return;
			}
			str = JSON.stringify({
				circleduration: this.lightInfos.pulseInterval,
				debounce: this.lightInfos.debounceTime,
				green: this.lightInfos.greenInterval,
				heartbeat: this.lightInfos.heartInterval,
				red: this.lightInfos.redInterval,
				restartCount: this.lightInfos.offlineReset,
				sim: sim,
				yellow: this.lightInfos.yellowInterval,
				mqttType: this.lightInfos.mqttType,
				mqttUrl: this.lightInfos.mqttUrl,
				mqttUser: this.lightInfos.mqttUser,
				mqttPawd: this.lightInfos.mqttPawd,
				locaProductKey: this.lightInfos.locaProductKey,
				locaDeviceName: this.lightInfos.locaDeviceName,
				locaDeviceSecret: this.lightInfos.locaDeviceSecret,
				countZeroReport: this.lightInfos.countZeroReport,
				countInterval: this.lightInfos.countInterval,
				productId: this.lightInfos.productId,
				productName: this.lightInfos.productName,
			});
			this.$axios
				.updateTricolourLightParam(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.lightInfos.updated = 4;
						this.saveInputed(); //保存输入过的数据
						this.isUpdate = true;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('updateTricolourLightParam |' + error);
				});
		},
		openLight(type) {
			let sim;
			if (type == 'pre') {
				this.mulIndex -= 1;
			} else if (type == 'next') {
				this.mulIndex += 1;
			}
			const light = this.multipleLights[this.mulIndex];
			this.openLightDetail(light, true);
		},
		getQrcode(str) {
			const canvas = document.getElementById('qrcodeLight');
			QRCode.toCanvas(canvas, str, error => {
				if (error) {
					console.log('二维码异常', error);
				}
			});
		},
		getUpdateStr(status) {
			//刷新字符串
			let str = '',
				nowTime = new Date();
			if (!status) {
				return;
			}
			switch (status) {
				case 1:
					str = '';
					break;
				case 2:
					str = '等待参数同步结果,请检查三色灯是否在线，稍后“刷新”查看同步结果';
					break;
				case 3:
					str = '参数已同步';
					break;
				case 4:
					str = this.$moment(nowTime).format('MM/DD HH:mm:ss') + '同步参数...  稍后点击“刷新”查看同步结果';
					break;
				default:
					str = '';
			}
			return str;
		},
		//存储最近输入的芯片
		saveChipInput(str) {
			let chipRecent = null;
			if (window.localStorage) {
				chipRecent = JSON.parse(window.localStorage.getItem('chipInput'));
			} else {
				chipRecent = JSON.parse(getCookie('chipInput'));
			}
			if (!chipRecent) {
				chipRecent = [];
				chipRecent.push(str);
				if (window.localStorage) {
					window.localStorage.setItem('chipInput', JSON.stringify(chipRecent));
				} else {
					setCookie('chipInput', JSON.stringify(chipRecent));
				}
			} else {
				//存在最近输入,插入最近,并保存
				chipRecent.unshift(str);
				if (chipRecent.length > 3) {
					chipRecent.length = 3;
				}
				if (window.localStorage) {
					window.localStorage.setItem('chipInput', JSON.stringify(chipRecent));
				} else {
					setCookie('chipInput', JSON.stringify(chipRecent));
				}
			}
		},
		// 查询最近输入的芯片
		queryChipData(str, cb) {
			//最近输入
			let newChipData = [],
				chipRecent = null;
			if (window.localStorage) {
				chipRecent = JSON.parse(window.localStorage.getItem('chipInput')) || [];
			} else {
				chipRecent = JSON.parse(getCookie('chipInput')) || [];
			}
			if (chipRecent.length > 0) {
				newChipData = chipRecent.map(item => {
					return { value: item };
				});
			}
			const results = str
				? newChipData.filter(item => {
						return item.value.indexOf(str) === 0;
					})
				: newChipData;
			cb(results);
		},
		// 保存输入过的数据
		saveInputed() {
			let urls = [],
				users = [],
				pawds = [];
			// lightInfos.mqttUrl lightInfos.mqttUser lightInfos.mqttPawd
			if (window.localStorage) {
				urls = JSON.parse(window.localStorage.getItem('lightMain_urls')) || [];
				users = JSON.parse(window.localStorage.getItem('lightMain_users')) || [];
				pawds = JSON.parse(window.localStorage.getItem('lightMain_pawds')) || [];

				this.lightInfos.mqttUrl &&
					!urls.includes(this.lightInfos.mqttUrl) &&
					urls.push(this.lightInfos.mqttUrl) &&
					window.localStorage.setItem('lightMain_urls', JSON.stringify(urls));
				this.lightInfos.mqttUser &&
					!users.includes(this.lightInfos.mqttUser) &&
					users.push(this.lightInfos.mqttUser) &&
					window.localStorage.setItem('lightMain_users', JSON.stringify(users));
				this.lightInfos.mqttPawd &&
					!pawds.includes(this.lightInfos.mqttPawd) &&
					pawds.push(this.lightInfos.mqttPawd) &&
					window.localStorage.setItem('lightMain_pawds', JSON.stringify(pawds));
			}
		},
		querySaved(str, cb, type) {
			//获取保存的数据
			let res = [];
			if (window.localStorage) {
				res = JSON.parse(window.localStorage.getItem(`lightMain_${type}`)) || [];
			}
			const results = str
				? res.filter(item => {
						return item.indexOf(str) === 0;
					})
				: res;
			// console.log(str, cb, type, res, results);
			cb(results);
		},
		handleSelect(item, value) {
			this.lightInfos[value] = item;
		},
		//数据导出
		openExport: debounce(function () {
			const PROPS = {
				DATA: JSON.stringify(this.searchForm), //接口参数
				API: 'tricolourLightDownload', //导出接口
				downloadData: '三色灯清单', //数据报表参数（后台确认字段downloadData）
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
		// 数据导入
		openImport: debounce(function (type = 'import') {
			const PROPS = {
				API: 'batchImport', //导入接口
				templateName: 'tricolourlight_template', //模板文件名称（下载模板用）
				dataName: '三色灯数据', //数据名（提示：成功导入xxx数据xxx条!）
				type, // 导入或查看导入记录
			};
			this.$refs.ImportTable.openImport(PROPS);
		}),
	},
};
</script>

<style lang="scss">
#lightMain {
	width: 100%;
	overflow: hidden;
	position: relative;

	#lightMainToggle {
		position: absolute;
		width: 100%;
		min-height: 100%;
		top: 0;
		left: 110%;
		z-index: 66;
		transition: left 0.3s linear;
		background: #f2f2f2;
		// overflow-y:overlay;
		overflow-x: hidden;

		.p_row {
			padding: 0;
			display: flex;
			align-items: center;

			.p_label {
				width: 8px;
				height: 16px;
				display: inline-block;
				background-color: #23b781;
			}

			.p_text {
				margin-left: 0.3vw;
				font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
				font-weight: 650;
				color: #666666;
			}
		}

		.p_nor {
			padding: 0 0.5vw;
			margin: 0.2vh 0;
			display: flex;
			align-items: center;

			span {
				color: #999;
				font-size: 12px;
			}

			pre {
				color: #999;
				font-family: 'Microsoft YaHei';
				font-size: 12px;
				margin: 0;
				margin-left: 0.5vw;
				display: inline-block;
			}
		}

		.workCtable {
			width: 99%;
			border-left: 1px solid #e9e9e9;
			border-bottom: 1px solid #e9e9e9;

			tr {
				border-color: #e9e9e9;

				th {
					text-align: left;
					font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
					font-weight: 650;
					font-style: normal;
					font-size: 14px;
					color: #666666;
					padding: 15px 0 15px 5px;
					background: #f5f5f5;
					border-right: 1px solid #e9e9e9;
					border-top: 1px solid #e9e9e9;
					word-wrap: break-word;
				}

				td {
					border-right: 1px solid #e9e9e9;
					border-top: 1px solid #e9e9e9;
					font-size: 12px;
					height: 28px;

					.norSpan {
						font-size: 14px;
						margin-left: 0.2vw;
						color: #666;
					}
				}
			}
		}

		.bot_left {
			font-size: 13px;
			margin-top: 1.5vh;
			display: flex;
			align-items: center;
			color: #ec808d;
		}
	}
}
</style>
