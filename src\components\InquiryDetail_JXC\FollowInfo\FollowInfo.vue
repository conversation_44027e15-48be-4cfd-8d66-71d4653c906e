<template>
	<!-- 跟单信息组件 -->
	<div id="FollowInfo" v-loading.lock="isUploading">
		<table class="base-table input-border-none" cellpadding="5" cellspacing="0">
			<tr>
				<th class="W60 p0">
					<div class="flex-align-center flex-wrap min-h-50">
						<div class="flex-align-center m5">
							<span class="label-required pl10 pr10 min-w-50">日期</span>
							<el-date-picker
								:disabled="isPublicInquiry"
								class="min-w-100 vw10"
								size="mini"
								v-model="followForm.documentaryTime"
								value-format="timestamp"
								type="datetime"
								format="yyyy-MM-dd HH:mm"
								placeholder="选择日期"
								:clearable="false"
								:default-value="new Date()"
							></el-date-picker>
						</div>
					</div>
				</th>
				<th class="W40 p0">
					<div class="flex-align-center">
						<span class="label-required min-w-100">下一步计划</span>
						<el-date-picker
							:disabled="isPublicInquiry"
							class="min-w-100 vw10"
							size="mini"
							v-model="followForm.nextStep"
							value-format="timestamp"
							type="datetime"
							format="yyyy-MM-dd HH:mm"
							placeholder="选择日期后再输入内容"
							:default-value="$moment(new Date()).endOf('day').valueOf()"
							default-time="23:59:59"
						></el-date-picker>
					</div>
				</th>
			</tr>
			<tr>
				<td>
					<el-input
						ref="focusInput"
						:disabled="!followForm.documentaryTime || isPublicInquiry"
						class="mr20"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						v-model="followForm.content"
						placeholder="请输入跟单情况..."
					></el-input>
				</td>
				<td>
					<el-input
						:disabled="!followForm.nextStep || isPublicInquiry"
						class="mr20"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						v-model="followForm.nextPlan"
						placeholder="请输入以何种方式联系谁达成什么目标,如：微信联系张总了解其对报价单的看法"
					></el-input>
				</td>
			</tr>
		</table>
		<div class="bottom-button" v-show="!isPublicInquiry" :class="{ mt0: !isPublicInquiry && showTips }">
			<!-- <div class="fs-12 red" v-show="showTips">* 当前阶段发布跟单时，下一步计划不能为空！</div> -->
			<el-button class="ml-auto" @click="savePublish" :disabled="!isPublish" :type="isPublish ? 'primary' : ''"
				>发布跟单
			</el-button>
		</div>

		<!-- 跟单记录 -->
		<FollowRecord
			ref="FollowRecord"
			:idid="idid"
			:detailForm="detailForm"
			:newDocumentaryRecordsList="documentaryRecordsList"
			@refresh="$emit('refresh')"
		/>
	</div>
</template>
<script>
import { debounce, dateFormat, jointString, resetValues, checkRequired, deepClone } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import eventBus from '../eventBus';
import FollowRecord from './FollowRecord.vue';

export default {
	name: 'FollowInfo',
	components: {
		FollowRecord,
	},
	props: {
		// 询盘主键
		idid: {
			type: [String, Number],
			default: '',
		},
		// 是否公海询盘
		isPublicInquiry: { type: Boolean, default: false },
		// 跟单记录
		documentaryRecordsList: {
			type: Array,
			default: () => [],
		},
		// 用户列表
		userList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			detailForm: {}, //事件总线表单

			scoreConfigList: [], //积分配置

			// 跟单/计划表单(非弹窗的)
			followForm: {
				content: '',
				idid: '',
				drid: '',
				ckrid: '',
				nextPlan: '',
				nextStep: '',
				documentaryTime: '',

				aupcid: '', //积分配置项id
				recordType: '', // 跟单类型 冗余字段，来自积分配置项 cfg_name
				recordAttachment: '', //  依据积分配置项cfgAttachments来判断是否需要上传附件
				facilitatorUids: '', //是否需要协助人cfgHasFacilitator，多个协助人因为逗号隔开 eg: 1001,1002
				facilitatorUidArray: [], //显示和转换用

				cfgAttachments: '', // 需附件
				cfgHasFacilitator: '', // 需协助人
			},

			// 跟单/计划弹窗表单
			planForm: {
				content: '',
				drid: '',
				nextPlan: '',
				nextStep: '',
				planStatus: 0,
				documentaryTime: '',
			},
			isUploading: false, //上传loading
			travelListMap: new Map(), //出差申请列表（Map）
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 是否可发布
		isPublish() {
			if (this.detailForm.stage == 5 || this.detailForm.stage == 10) {
				return this.followForm.documentaryTime && this.followForm.content;
			} else {
				return this.followForm.documentaryTime && this.followForm.content && this.followForm.nextPlan && this.followForm.nextStep;
			}
		},
		// 显示发布提示
		showTips() {
			// 如果阶段不是【'已签单' '丢单'】 则提示:当前阶段下一步计划不能为空！。
			if (this.detailForm.stage == 5 || this.detailForm.stage == 10) {
				return false;
			} else {
				return !this.followForm.nextPlan;
			}
		},
	},
	// 监控data中的数据变化
	watch: {},
	created() {
		// 订阅事件（注意这里要加多一层parent 使其与发布事件一致）
		eventBus.$on(`updateDetailForm_${this.$parent.$parent.$options.name}`, detailForm => {
			// 处理事件，更新本地的 detailForm 对象
			this.detailForm = detailForm;
			this.followForm.documentaryTime = new Date().getTime();
			// console.log(`updateDetailForm_${this.$parent.$parent.$options.name}`, this.detailForm, detailForm);
			console.log('eventBus - on ', `updateDetailForm_${this.$parent.$parent.$options.name}`);
		});
	},

	beforeDestroy() {
		eventBus.$off(`updateDetailForm_${this.$parent.$parent.$options.name}`);
		console.log('eventBus - off ', `updateDetailForm_${this.$parent.$parent.$options.name}`);
	},
	destroyed() {
		this.detailForm = null;
		this.followForm = null;
	},
	// 方法集合
	methods: {
		// 聚焦输入框
		focus() {
			this.$refs?.focusInput?.focus();
		},

		// 保存跟单 - 添加跟单记录
		savePublish: debounce(async function () {
			const API = 'addDocumentaryRecords';
			try {
				const res = await this.$axios[API](
					JSON.stringify({ ...this.followForm, idid: this.detailForm.idid || this.idid, ckrid: this.detailForm.ckrid }),
				);
				if (res.data.success) {
					this.clearFollowForm();
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		openDialog(title, item) {
			this.$refs.FollowRecord.openDialog(title, item);
		},
		// 跟单情况表单清空
		clearFollowForm() {
			this.followForm = resetValues(this.followForm);
			this.followForm.documentaryTime = new Date().getTime();
		},
		dateFormat,
		jointString,
	},
};
</script>
