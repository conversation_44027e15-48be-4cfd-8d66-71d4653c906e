/* 团队管理API */

const urlList = [
	// 团队管理
	'/background/web/teamController/selectTeam', //查询团队
	'/background/web/teamController/createTeam', //创建团队
	'/background/web/teamController/updateTeam', //修改团队
	'/background/web/teamController/deleteTeam', //删除团队
	'/background/web/teamController/teamDownload', //导出团队
	'/background/web/teamController/uploadWebPic', //上传团队图标
	'/background/web/teamController/selectAllPrintTemplateConfig', //查询所有框标打印模板
	'/background/web/teamController/selectAllWorkorderTemplateConfig', //查询所有工单打印模板
	'/background/web/teamController/selectTaskTemplateConfig', //查询生产任务单打印模板
	'/background/web/teamController/selectTaskPrintConfig', //查询任务标签模板
	'/background/web/teamController/getAllResources', //查询资源
	'/background/web/teamController/addTeamResources', //添加资源
	'/background/web/teamController/deleteTeamResources', //删除资源
	'/background/web/teamController/selectTeamType', //查询版本信息
	'/background/web/teamController/selectTeamByTid', //根据tid查询团队信息

	'/background/web/teamController/selectTeamByStatus', //根据状态查询团队
	'/background/web/teamController/updateTeamStatus', //修改团队审批状态
	'/background/web/teamController/teamChan', //申请变更团队
	'/background/web/teamController/selectTeamChanInfo', //查询团队修改信息
	'/background/web/teamController/updateTeamChanInfo', //修改审批中的团队信息
	'/background/web/teamController/selectTeamLogByTid', //根据团队查询操作日志

	// 资源说明
	'/background/web/teamController/addResourceExplain', //补充资源说明
	'/background/web/teamController/selectFieldResources', //查询字段说明资源
	'/background/web/teamController/selectFieldDescriptionConfig', //查询字段列表
	'/background/web/teamController/updateFieldDescriptionConfig', //修改字段说明
	'/background/web/teamController/updateResourceDescriptionConfig', //修改功能说明

	// 售服管理
	'/background/web/machineFatoryController/selectFactory', // 查询机床厂列表
	'/background/web/machineFatoryController/deleteFactory', // 删除机床厂
	'/background/web/machineFatoryController/factoryDownload', // 机床厂导出
	'/background/web/machineFatoryController/getAllResources', // 获取机床厂下分配的资源信息
	'/background/web/machineFatoryController/updateFactory', // 修改机床厂
	'/background/web/machineFatoryController/createFactory', // 创建机床厂
	'/background/web/machineFatoryController/addFactoryResources', // 创建机床厂
	'/background/web/machineFatoryController/deleteFactoryResources', // 删除机床厂下的资源信息
	'/background/web/machineFatoryController/selectFactoryDetail', // 查询机床厂详情
	'/background/web/machineFatoryController/selectResourceTemplateConfig', // 查询机床厂模板
];

// 重名函数映射
const apiNameMap = {
	'/background/web/machineFatoryController/getAllResources': 'MacgetAllResources',
};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['teamDownload'].includes(urlName)) {
		timeout = 60000;
	}
	return { urlName, url, timeout };
});

export default apiList;
