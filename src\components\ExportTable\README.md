# ExportTable 组件使用文档

## 组件简介

ExportTable 是一个数据导出管理组件，提供了导出日志查询和文件下载功能。组件以弹窗形式展示，包含导出记录列表，支持查看导出状态、下载文件等操作。配合 ExportBtn 组件使用，提供完整的导出解决方案。

## 功能特点

- 显示数据导出历史记录
- 支持文件下载功能
- 显示导出状态（导出完成、文件过期等）
- 支持分页查询和刷新功能
- 自动管理文件生命周期（服务器上暂存三天后自动删除）
- 提供导出按钮组件（ExportBtn），支持导出和查看历史记录

## 使用方法

### 基本引入

```javascript
import ExportTable from '@/components/ExportTable';
import ExportBtn from '@/components/ExportTable/ExportBtn';

export default {
  components: {
    ExportTable,
    ExportBtn
  }
}
```

### 模板调用

```html
<!-- 导出按钮组件 -->
<ExportBtn @trigger="handleExport" />

<!-- 导出表格组件 -->
<ExportTable ref="ExportTable" />
```

### 触发导出

```javascript
// 处理导出按钮点击
handleExport(type) {
  if (type === 'export') {
    // 调用导出功能并显示导出日志
    this.$refs.ExportTable.openExport({
      API: 'yourExportApiName', // 导出接口名称
      DATA: yourRequestParams, // 导出请求参数
      downloadData: '数据报表名称', // 数据标识名称，
    });
  } else if (type === 'record') {
    // 仅查看导出记录
    this.$refs.ExportTable.openExport({
      downloadData: '数据报表名称',
      type: 'record'
    });
  }
}
```

## API 说明

### ExportTable 方法

| 方法名 | 说明 | 参数 |
| ----- | ---- | ---- |
| openExport | 调用导出接口并显示导出记录 | `{DATA, API, downloadData, type}` |
| queryExportTable | 获取导出日志数据 | `type`: 可选参数，传入'refresh'时会显示刷新成功提示 |
| downloadTable | 下载文件 | `path`: 文件下载路径 |
| handlePageSize | 处理分页变化 | `{page, size}`: 页码和每页条数 |

### openExport 参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| API | String | 是(type为'export'时) | 导出接口名称 |
| DATA | Object | 是(type为'export'时) | 导出请求参数 |
| downloadData | String | 是 | 数据标识名称，如"计件明细"、"工时统计"等 |
| type | String | 否 | 操作类型，'export'(默认)执行导出，'record'仅查看记录 |

### ExportBtn 属性

| 参数名 | 类型 | 默认值 | 说明 |
| ----- | ---- | ---- | ---- |
| show | Boolean | true | 是否显示导出按钮 |
| buttonType | String | 'text' | 按钮类型，同Element UI按钮类型 |
| buttonClass | String | 'icon-third_xiazai' | 按钮自定义类名 |
| buttonText | String | '导出' | 按钮文本 |
| iconClass | String | 'el-icon-arrow-down m0' | 图标类名 |
| dropdownItems | Array | 默认两项 | 下拉菜单项配置，默认为导出文件和导出记录 |

### ExportBtn 事件

| 事件名 | 说明 | 回调参数 |
| ----- | ---- | ---- |
| trigger | 点击下拉菜单项触发 | type: 'export'或'record' |

## 组件事件

| 事件名 | 说明 | 回调参数 |
| ----- | ---- | ---- |
| refresh | 导出操作完成后触发刷新 | - |

## 注意事项

1. 导出的文件会在服务器上暂存三天，之后自动删除
2. 根据后台返回的导出状态可能需要调整代码中的状态判断逻辑
3. 下载文件时请确保路径正确，本地化部署可能需要补充完整URL
4. 对于大量数据的导出，建议使用异步导出模式，避免前端阻塞

## 示例代码

```javascript
// 示例：客户数据导出完整实现
<template>
  <div>
    <ExportBtn @trigger="handleExportCustomer" />
    <ExportTable ref="ExportTable" @refresh="refreshTable" />
  </div>
</template>

<script>
import ExportTable from '@/components/ExportTable';
import ExportBtn from '@/components/ExportTable/ExportBtn';

export default {
  components: {
    ExportTable,
    ExportBtn
  },
  data() {
    return {
      queryForm: {
        startDate: '',
        endDate: '',
        customerId: ''
      }
    }
  },
  methods: {
    // 处理客户数据导出
    handleExportCustomer(type) {
      if (type === 'export') {
        // 导出客户数据
        this.$refs.ExportTable.openExport({
          API: 'exportCustomerList',
          DATA: {
            startDate: this.queryForm.startDate,
            endDate: this.queryForm.endDate,
            customerId: this.queryForm.customerId
          },
          downloadData: '客户数据报表'
        });
      } else if (type === 'record') {
        // 查看导出记录
        this.$refs.ExportTable.openExport({
          downloadData: '客户数据报表',
          type: 'record'
        });
      }
    },
    // 刷新表格
    refreshTable() {
      this.getTableData();
    }
  }
}
</script>
```

## 导出流程

1. 用户点击导出按钮，选择导出文件
2. 系统调用后台导出接口
3. 后台处理导出请求，生成文件
4. 前端显示导出记录，提供文件下载
5. 用户可以在导出记录中下载文件
6. 文件在服务器上保存三天后自动删除

## 依赖项

组件依赖于以下工具/组件：
- Element UI（按钮、进度条、表格等）
- u-table 表格组件
- Tooltips 提示组件
- dateFormat 日期格式化工具 