/* 工具/函数类型的全局样式 */

//将px转换为vw/vh
$designWidth: 1920;
$designHeight: 1080;
//这里宽高参考PSD设计稿 ；我这里宽为1920px，高为1080px为例，所以这里 计算vw时除以1920，计算vh时除以1080；以便在使用时，不用去计算倍数， PSD设计稿中的尺寸是多少，我们样式就是多少！！！
/* 
  案例：一个class为title的div，宽100px, 高300px
  
  .title {
	  width: px2vw(100);
	  height: px2vh(300);
  }
  200/3840
	200/1920
  */
@function px2vw($px) {
	@return ($px/$designWidth) * 100vw;
}
@function px2vh($px) {
	@return ($px/$designHeight) * 100vh;
}



// 定义一个Sass mixin，用于select选择器生成不同列数option的网格布局
@mixin select-column($className,$columns, $item-width: 150px) {
	// 循环生成不同列数的网格布局
	@for $i from 2 through $columns {
		.#{$className}-#{$i} {
			// 根据不同列数设置网格布局的列数
			.el-select-dropdown__list {
				max-width: #{$i * $item-width}; // 根据最大宽度显示多少列
				display: grid;
				grid-template-columns: repeat(auto-fit, minmax($item-width, 1fr));
				grid-auto-columns: 1fr;
				gap: 0;
				// 子项样式设置
				.el-select-dropdown__item {
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}
			}
		}
	}
}


/* 
	如果我在某个组件要生成要自定义生成列和每个项宽度，可以使用 @include 
	但因为el-select-dropdown的样式比较特殊，它是不在当前组件的scoped的，所以自定义时请注意样式覆盖问题
	例子如下：生成5列的网格布局样式，每个项的宽度为150px
*/
@include select-column('select-column',5,150px); // 生成1到5列的网格布局样式，默认每个项宽度为150px
@include select-column('select-column-w200', 3, 200px); // 生成3列的网格布局样式，每个项宽度为200px



