<template>
	<div id="dealProtection">
		<!-- 动态询盘详情：聚心城使用聚心城询盘详情，其他使用默认询盘详情 -->
		<component
			v-if="showMap.InquiryDetail"
			:is="isJuxinCity ? 'InquiryDetail_JXC' : 'InquiryDetail'"
			ref="InquiryDetail"
			:inquiryOptions="tableData"
			@close="queryTableData"
		/>

		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="inquiry"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twidList = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="成交客户保护" name="dealProtection">
				<BaseLayout>
					<template #header>
						<span class="search-label">成交日期</span>
						<DateSelect
							:dateList="['不限定', '最近30天', '最近60天']"
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>
						<SearchHistoryInput
							name="companyName"
							placeholder="客户工商注册名称"
							v-model="searchForm.query"
							@input="searchForm.query = $event"
						/>

						<el-checkbox v-model="searchForm.status" :true-label="0" :false-label="1" @change="queryTableData(1)"
							>仅显示未审核记录
						</el-checkbox>

						<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<div class="table-toolbar"> </div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="item.colNo == 'bargainTime' || item.colNo == 'protectDeadline'"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<Tooltips
										v-else-if="item.colNo == 'spreadTime'"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 询盘编号 -->
									<Tooltips
										v-else-if="item.colNo == 'number'"
										class="hover-green green"
										@click.native="openDetail(scope.row)"
										:cont-str="scope.row[item.colNo] || '未知'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>
									<!-- 状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:class="[scope.row[item.colNo] == 0 ? 'red' : 'green']"
										:cont-str="scope.row[item.colNo] == 0 ? '未审核' : '已审核'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<u-table-column label="" width="350" align="right">
								<template slot-scope="scope">
									<div class="flex-align-center">
										<el-button type="text" size="mini" @click="openDialog(scope.row, '成交客户保护审核')">
											<span v-show="scope.row.status == 0 && isSuperAdmin">审核</span>
											<span v-show="scope.row.status == 1" class="color-666">
												<Tooltips
													:cont-str="scope.row.checkRecord"
													:cont-obj="[
														{ title: '审核人', content: scope.row.checkUname },
														{ title: '审核结果', content: scope.row.checkStatus == 0 ? '通过' : '不通过' },
														{ title: '审核意见', content: scope.row.checkOpinion || '' },
														{ title: '审核时间', content: dateFormat(scope.row.checkDate, 'lineM') },
													]"
													:cont-width="(scope.column.width || scope.column.realWidth) - 20"
												/>
											</span>
										</el-button>
									</div>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
		<!-- 展期申请弹窗 -->
		<el-dialog :visible.sync="dialogExtension" width="666px" :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="8vw" label-position="left" ref="editFormRef" :rules="editRules">
				<el-form-item label="客户工商注册名称" prop="registeredBusinessName">
					<el-col :span="24">
						<span> {{ editForm.registeredBusinessName }}</span>
					</el-col>
				</el-form-item>
				<el-form-item label="客户简称" prop="abbreviation">
					<span> {{ editForm.abbreviation }}</span>
				</el-form-item>
				<el-form-item label="所在地区" prop="region">
					<span> {{ editForm.region }}</span>
				</el-form-item>
				<el-form-item label="当前保护期" prop="protectDeadline">
					<span> {{ dateFormat(editForm.protectDeadline, 'lineM') }}</span>
				</el-form-item>
				<el-form-item label="展期至" prop="spreadTime">
					<el-date-picker
						v-model="editForm.spreadTime"
						type="date"
						class="W100"
						placeholder="请选择展期结束时间"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="审核意见" prop="checkOpinion">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						placeholder="请输入审核意见，不同意时请填写说明"
						v-model="editForm.checkOpinion"
					></el-input>
				</el-form-item>
				<el-form-item label="">
					<div style="display: flex; align-items: center; height: 40px">
						<el-radio-group v-model="editForm.checkStatus" class="flex-align-center">
							<el-radio :label="0">同意</el-radio>
							<el-radio :label="1">不同意</el-radio>
						</el-radio-group>
					</div>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveEdit">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
import ChannelSelect from '@/components/ChannelSelect.vue';
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryDetail_JXC from '@/components/InquiryDetail_JXC/InquiryDetail.vue'; //询盘详情弹窗

export default {
	components: {
		ChannelSelect,
		DateSelect,
		InquiryDetail,
		InquiryDetail_JXC,
	},
	name: 'dealProtection',
	data() {
		return {
			uid: '',
			activeTab: 'dealProtection',
			rowData: {},
			titleName: '',
			openMove: '',
			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'left', width: '' },
				{ colName: '客户编号', colNo: 'clientNo', align: 'left', width: '' },
				{ colName: '客户工商注册名称', colNo: 'registeredBusinessName', align: 'left', width: '' },
				{ colName: '分销/代理', colNo: 'teamworkName', align: 'left', width: '100' },
				{ colName: '询盘状态', colNo: 'stageName', align: 'center', width: '100' },
				{ colName: '成交日期', colNo: 'bargainTime', align: 'center', width: '' },
				{ colName: '状态', colNo: 'status', align: 'center', width: '100' },
			],
			searchForm: {
				query: '',
				status: 0,
				twidList: [],
				channelName: '',
			},
			isSuperAdmin: false,
			dialogEdit: false,
			dialogExtension: false,
			editForm: {
				cbpid: '',
				checkOpinion: '',
				phoneNo: '',
				spreadTime: '',
				protectDeadline: '',
				checkStatus: '',
			},
			dialogTitle: '客户详情',
			editRules: {
				abbreviation: [{ required: true, message: '客户简称', trigger: 'blur' }],
				clientNeed: [{ required: true, message: '客户需求', trigger: 'blur' }],
				linkman: [{ required: true, message: '请输入对接人信息', trigger: 'blur' }],
				linkphone: [{ required: true, message: '请输入对接人联系方式', trigger: 'blur' }],
				region: [{ required: true, message: '客户地址', trigger: 'blur' }],
				regions: [{ required: true, message: '客户地址', trigger: 'blur' }],
				salesman: [{ required: true, message: '请输入业务顾问', trigger: 'blur' }],
				registeredBusinessName: [{ required: true, message: '客户工商注册名称', trigger: 'blur' }],
				protectDeadline: [{ required: true, message: '请输入备案保护日期', trigger: 'blur' }],
				spreadTime: [{ required: true, message: '请输入展期日期', trigger: 'blur' }],
			},

			showMap: {
				InquiryDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['isJuxinCity']), //是否聚心城人员
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.uid = this.$route?.query?.queryId || '';
		if (this.userInfos?.adminUserVO.adminRoleVOS) {
			this.isSuperAdmin = false;
			this.userInfos?.adminUserVO.adminRoleVOS.map(item => {
				//  超级管理员/渠道经理 可审核
				if (item.arid == 15 || item.arid == 1) this.isSuperAdmin = true;
			});
		}
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 询盘详情
		openDetail(row) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom('修改', row);
			});
		},
		changeTab() {},

		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const str = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.dateSelectObj,
				...this.searchForm,
			});
			this.$axios
				.selectBargainProtect(str)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item['checkRecord'] = `
              ${item.checkStatus == 0 ? '通过' : '不通过'}，
              ${item.checkUname}，
              ${item.checkOpinion ? item.checkOpinion : ''}，
              ${this.dateFormat(item.checkDate, 'lineM')}`;
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectBargainProtect |' + error);
				});
		}),
		openDialog(row, type) {
			this.dialogTitle = type;
			if (row) {
				this.editForm.region = row.province + row.city + row.area;
				this.editForm.abbreviation = row.abbreviation;
				this.editForm.spreadTime = 4102415999000;
				this.editForm.cbpid = row.cbpid;
				this.editForm.registeredBusinessName = row.registeredBusinessName;
				this.editForm.protectDeadline = row.protectDeadline;
				this.editForm.checkOpinion = row.checkOpinion;
				this.editForm.checkStatus = row.checkStatus == null ? '' : row.checkStatus;
			} else {
				//添加
			}
			if (type == '成交客户保护审核') {
				this.dialogExtension = true;
			} else {
				this.dialogEdit = true;
			}
		},
		closeDialog() {
			this.dialogEdit = false;
			this.dialogExtension = false;

			this.$refs.editFormRef.resetFields();
		},
		saveEdit() {
			const { checkOpinion, spreadTime, cbpid, checkStatus } = this.editForm;

			if (!checkStatus && checkStatus !== 0) {
				this.$message.warning('请选择同意/不同意');
				return;
			}
			if (checkStatus == 1 && !checkOpinion) {
				this.$message.warning('请填写申请不通过的原因！');
				return;
			}
			const str = JSON.stringify({
				checkOpinion: checkOpinion ? checkOpinion : ' ',
				spreadTime,
				cbpid,
				checkStatus,
			});
			this.$axios
				.approvalBargainProtect(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.closeDialog();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('approvalBargainProtect  |' + error);
				});
		},
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,

		//自定义排序规则
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs.uTableRef.reloadData(sortedData);
			} else {
				this.$refs.uTableRef.reloadData(this.tableData);
			}
		},
	},
};
</script>

<style lang="scss" scoped>
#dealProtection {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
