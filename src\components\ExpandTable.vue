<template>
	<!-- 展开明细表格 ：用于一些明细表格的展开或者弹窗显示（无分页）-->
	<div class="ExpandTable table-wrapper m10 mt0 p10">
		<div class="label-title ellipsis">{{ label }}</div>
		<u-table class="table-main table-main2" :data="data" :max-height="500" :row-height="45" show-header-overflow="title" stripe>
			<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
			<u-table-column
				v-for="item in columns"
				:key="'expandCol' + item.colNo"
				:label="item.colName"
				:prop="item.colNo"
				:align="item.align"
				:width="item.width"
				sortable
				resizable
			>
				<template slot-scope="scope">
					<slot :row="scope.row" :prop="item.colNo" :columnWidth="(scope.column.width || scope.column.realWidth) - 20"></slot>
				</template>
			</u-table-column>
		</u-table>
	</div>
</template>

<script>
export default {
	props: {
		label: String, //表格标题
		data: Array, //表格数据
		columns: Array, //表格列配置
		showLable: { type: Boolean, default: true }, //是否显示label
	},
};
</script>
<style lang="scss" scoped>
.ExpandTable {
	.table-main2 {
		height: auto !important;
	}
}
</style>
