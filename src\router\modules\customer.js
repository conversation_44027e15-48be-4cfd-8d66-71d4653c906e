/* 客户成功管理路由 */

const customerList = () => import('@/pages/customerManagement/customerList.vue'); //我的客户
const activityRating = () => import('@/pages/customerManagement/activityRating.vue'); //健康评分
const expirationManagement = () => import('@/pages/customerManagement/expirationManagement.vue'); //到期管理
const industryCase = () => import('@/pages/customerManagement/industryCase/industryCase.vue'); //到期管理
const routers = [
	{
		//我的客户
		path: '/customerList',
		name: 'customerList',
		component: customerList,
		meta: {
			parentTitle: '客户成功管理',
			title: '我的客户',
		},
	},
	{
		//健康评分
		path: '/activityRating',
		name: 'activityRating',
		component: activityRating,
		meta: {
			parentTitle: '客户成功管理',
			title: '健康评分',
		},
	},
	{
		//到期管理
		path: '/expirationManagement',
		name: 'expirationManagement',
		component: expirationManagement,
		meta: {
			parentTitle: '客户成功管理',
			title: '到期管理',
		},
	},
	{
		//到期管理
		path: '/industryCase',
		name: 'industryCase',
		component: industryCase,
		meta: {
			parentTitle: '客户成功管理',
			title: '行业案例',
		},
	},
];

export default routers;
