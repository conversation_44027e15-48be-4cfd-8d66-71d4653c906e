<template>
	<!-- 咨询信息组件 -->
	<div id="ConsultInfo" v-loading.lock="detailForm.isUpLoadAudio" element-loading-text="正在上传录音中，请勿中途退出...">
		<table class="base-table" cellpadding="5" cellspacing="0">
			<tr>
				<th colspan="5">
					<div class="flex-align-center">
						<span class="label-required">咨询情况</span>
						<span class="fs-12 color-999 ml5">
							开场：X总，您好！，我这里是树字标品MES，你在XX上留言想要了解我们的树字MES生产管理系统（注意语速，不然客户没听到你讲什么）
						</span>
						<el-button
							v-show="!detailForm.consultingOther"
							type="text"
							size="mini"
							class="p0 fs-12 ml-auto"
							@click="fillEmpty('咨询情况', '不详')"
						>
							点击自动补全"不详"
						</el-button>
					</div>
				</th>
			</tr>

			<tr>
				<td colspan="5">
					<div class="flex-align-center W100 p10">
						<div class="consult-tips fs-14">
							<div>1.重点需求：您想重点解决什么问题？</div>
							<div>2.产品：您工厂是做什么产品？</div>
							<div>3.主要工序：您主要工序是哪些？</div>
							<div>4.规模：您公司有多少人都用哪些设备，xx设备有多少台？</div>
							<div
								>5.项目阶段：您公司有计划什么时侯上系统？您上系统管理解决这些问题有没有什么心理预期的费用？还了解过哪些其他系统吗？</div
							>
							<div>6.联系人背景：您是公司的老板吗？您是负责生产部门吗？您是哪个部门？</div>
							<div>7.其他：我了解您工厂做XX产品，主要工序是XXX,您重点想要解决的问题是XXXX,您看一下步需要我们怎么配合？</div>
						</div>
						<el-input
							:disabled="isPublicInquiry"
							class="flex-1 H100"
							type="textarea"
							:autosize="{ minRows: 8, maxRows: 8 }"
							v-model="detailForm.consultingOther"
							placeholder="请输入内容"
						></el-input>
					</div>

					<!-- 对于已经咨询完成的询盘且非公海询盘，默认不显示行业案例 -->
					<div
						v-show="detailForm.consultationTime && !showCase && !isPublicInquiry"
						class="label-title pointer"
						@click="() => (showCase = !showCase)"
					>
						行业案例 <i class="el-icon-arrow-down"></i>
					</div>
					<transition name="fold-transition" mode="out-in">
						<IndustryCaseList
							v-show="(!detailForm.consultationTime && !isPublicInquiry) || showCase"
							ref="IndustryCaseList"
							class="W100"
							:detailForm="detailForm"
							@openCaseList="() => (showCase = !showCase)"
						/>
					</transition>
				</td>
			</tr>

			<!--  -->
			<tr>
				<th class="W10">咨询日期时间</th>
				<th class="label-required W10">询盘质量</th>
				<th class="label-required W10">行业熟练程度</th>
				<th class="W15">阶段</th>
				<th class="W45">询盘录音</th>
			</tr>
			<tr class="input-border-none">
				<td>
					<el-date-picker
						:disabled="isPublicInquiry"
						class="w-180"
						v-model="detailForm.consultingDate"
						value-format="timestamp"
						type="datetime"
						placeholder="请选择"
						:clearable="false"
						format="yyyy-MM-dd HH:mm"
						:default-value="new Date()"
					></el-date-picker>
				</td>
				<td>
					<el-select :disabled="isPublicInquiry" v-model="detailForm.quality" placeholder="询盘质量" @change="changeQuality">
						<el-option v-for="key in Object.keys(qualityMap)" :key="key" :label="qualityMap[key]" :value="Number(key)">
							<div class="flex-align-center gap-5">
								<span>{{ qualityMap[key] }}</span>
								<Tooltips class="fs-12 color-999" :cont-str="qualityRemark[qualityMap[key]]" :cont-width="888" />
							</div>
						</el-option>
					</el-select>
				</td>
				<td>
					<el-select :disabled="isPublicInquiry" v-model="detailForm.industryProficiency" placeholder="行业熟练程度">
						<el-option label="不熟（1-5）" :value="1"> </el-option>
						<el-option label="熟练（6-8）" :value="2"> </el-option>
						<el-option label="擅长（9-10）" :value="3"> </el-option>
					</el-select>
				</td>
				<td>
					<el-radio-group
						:disabled="detailForm.stage == 5 || isPublicInquiry"
						v-model="detailForm.stage"
						@change="detailForm.stage == 7 ? fillEmpty('未联系上', '未联系上') : null"
					>
						<el-radio :label="7">未联系上</el-radio>
						<el-radio :label="0">咨询</el-radio>
					</el-radio-group>
				</td>
				<td>
					<el-upload
						:disabled="isPublicInquiry"
						v-show="!detailForm.callRecording"
						ref="uploadRef"
						class="ml-auto mr20 upload-dragger"
						action=""
						accept="audio/*"
						:http-request="handleAudioUpload"
						:show-file-list="false"
						drag
					>
						<el-button type="text" class="el-icon-service p0">上传询盘录音(仅支持音频文件拖拽上传)</el-button>
					</el-upload>
					<div v-if="detailForm.callRecording" :key="detailForm.idid || idid" class="flex-align-center ml-auto">
						<!-- 询盘录音播放器 -->
						<InquiryAudioPlayer
							:audioUrl="detailForm.callRecording"
							:idid="detailForm.idid"
							:autoReleaseResource="false"
							@onLoad="detailForm.recordingDuration = $event.duration"
						/>
						<el-tooltip v-show="detailForm.callRecording" effect="dark" placement="top-start">
							<div slot="content">
								<p>录音说明：</p>
								<p>如需替换录音文件请在咨询完成时间三天内操作。</p>
								<p>若已超过三天系统不支持自由删除，如需替换您可联系管理员删除后再上传。</p>
								<p>对于一些受保护的录音格式，可能会不支持播放该音频，请转换格式上传或下载录音。</p>
							</div>
							<i type="text" class="el-icon-upload pointer ml10" @click="openUpload"></i>
						</el-tooltip>
						<i
							type="text"
							v-show="detailForm.callRecording"
							class="el-icon-download pointer ml10"
							@click="downloadAudio(detailForm.callRecording)"
						></i>
						<i v-if="isDelete" type="text" class="el-icon-delete pointer ml10" @click="delAudio"></i>
					</div>
				</td>
			</tr>

			<tr>
				<th class="W10" colspan="4">
					<div class="flex-align-center">
						<span class="label-required">跟进策略</span>
						<el-button
							v-show="!detailForm.followUpStrategy"
							type="text"
							size="mini"
							class="p0 fs-12 ml-auto"
							@click="fillEmpty('跟进策略', '加微信发资料')"
						>
							点击自动补全"加微信发资料"
						</el-button>
					</div>
				</th>
				<th class="label-required W10">跟进频次</th>
			</tr>
			<tr class="input-border-none">
				<td colspan="4">
					<el-input
						:disabled="isPublicInquiry"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						v-model="detailForm.followUpStrategy"
						placeholder="请输入跟进策略"
					></el-input>
				</td>
				<td>
					<div class="flex-align-center">
						<el-radio-group :disabled="isPublicInquiry" v-model="detailForm.followUpFrequency">
							<el-radio v-for="item in frequencys" :key="item" :label="item" class="pt2 pb2">
								<span v-if="item == '紧急'" :class="detailForm.followUpFrequency == '紧急' ? 'red ' : ''">
									{{ item }}
								</span>
							</el-radio>
							<el-radio :label="isOther ? detailForm.followUpFrequency : ''">其他</el-radio>
						</el-radio-group>
						<el-input v-show="isOther" v-model="detailForm.followUpFrequency" placeholder="其他跟进频次" clearable></el-input>
					</div>
				</td>
			</tr>
		</table>

		<div class="bottom-button flex-align-center">
			<div class="text-left">
				<!-- 咨询评价 -->
				<el-button
					type="text"
					class="green"
					:disabled="!isEvaluator"
					@click="openDialog('评价')"
					v-toolTips="isEvaluator ? '' : '当前只有询盘评价员可评分'"
				>
					<span>咨询评价：</span>
					<span v-if="detailForm.consultingScore == null">暂无</span>
					<span v-else>
						{{ jointString(',', (detailForm.consultingScore || 0) + '分', detailForm.consultingView) }}
					</span>
				</el-button>
				<div v-show="detailForm.consultationTime">咨询完成时间：{{ dateFormat(detailForm.consultationTime, 'line') }}</div>
			</div>

			<el-tooltip effect="light" placement="top">
				<div slot="content">
					<p>咨询说明：</p>
					<p>1、保存咨询时，咨询人员必须为当前系统登录用户</p>
					<p>2、保存咨询后，咨询人员将被锁定不可再修改</p>
					<p>3、除询盘质量为无效/择时再打/复购的询盘外，保存时必须上传询盘录音</p>
					<p>
						4、如果不是无效/择时且无跟单记录保存时:将自动弹出下一步计划，保存后生成跟单记录，跟单方式为【首次咨询】，跟单内容为咨询内容
					</p>
				</div>
				<i v-show="!isPublicInquiry" class="el-icon-warning-outline mr10 ml-auto pointer"></i>
			</el-tooltip>
			<el-button v-show="!isPublicInquiry" @click="saveEdit(isFirstConsult)" :type="isUpdate ? 'primary' : ''"
				>{{ isFirstConsult ? '保存咨询并跟单' : '保存咨询' }}
			</el-button>
		</div>

		<!-- 咨询评价弹窗 -->
		<el-dialog width="500px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">咨询评价</span>
			<el-form :model="editForm" :rules="formRules" label-width="60px" label-position="left" @submit.native.prevent>
				<el-form-item label="分值" prop="consultingScore">
					<el-input v-model="editForm.consultingScore" placeholder="请输入分值" clearable></el-input>
				</el-form-item>
				<el-form-item label="内容" prop="consultingView">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						v-model="editForm.consultingView"
						placeholder="请输入内容..."
					></el-input>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEvaluate">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 跟单计划弹窗 -->
		<el-dialog
			width="666px"
			:visible.sync="dialogFollow"
			:close-on-click-modal="false"
			:append-to-body="true"
			:show-close="false"
			@close="closeDialog"
		>
			<span slot="title">跟单计划（对于首次咨询需填写跟单计划，未填写或退出则视为完成咨询）</span>
			<el-form :model="followForm" :rules="followFormRules" label-width="100px" label-position="left" @submit.native.prevent>
				<el-form-item label="跟单日期" prop="documentaryTime">
					<Tooltips :cont-str="dateFormat(followForm.documentaryTime, 'line')" :cont-width="400" />
				</el-form-item>
				<el-form-item label="跟单内容" prop="content">
					<Tooltips :cont-str="followForm.content" :cont-width="400" />
				</el-form-item>
				<el-form-item label="计划日期" prop="nextStep">
					<el-date-picker
						class="W100"
						v-model="followForm.nextStep"
						value-format="timestamp"
						type="datetime"
						format="yyyy-MM-dd HH:mm"
						placeholder="选择日期后再输入内容"
						:clearable="false"
						:picker-options="pickerOptions"
						:default-value="$moment(new Date()).endOf('day').valueOf()"
						default-time="23:59:59"
					></el-date-picker>
				</el-form-item>

				<el-form-item label="计划内容" prop="nextPlan">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						v-model="followForm.nextPlan"
						placeholder="请输入以何种方式联系谁达成什么目标,如：微信联系张总了解其对报价单的看法"
					></el-input>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<!-- <el-button @click="closeDialog">取 消</el-button> -->
				<el-button type="primary" @click="savePublish">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { debounce, dateFormat, jointString, resetValues, checkRequired, deepClone } from '@/util/tool'; //按需引入常用工具函数
import { qualityMap, qualityRemark, frequencys } from '@/assets/js/inquirySource';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import eventBus from '../eventBus';
import toolTips from '@/directive/toolTips';
import IndustryCaseList from './IndustryCaseList';
import InquiryAudioPlayer from '@/components/InquiryAudioPlayer';
export default {
	name: 'ConsultInfo',
	components: { IndustryCaseList, InquiryAudioPlayer },
	directives: { toolTips },
	props: {
		// 询盘主键
		idid: {
			type: [String, Number],
			default: '',
		},
		// 是否公海询盘
		isPublicInquiry: { type: Boolean, default: false },
		//业务顾问列表(标签：业务)
		salesmanList: {
			type: Array,
			default: () => [],
		},
		//跟单记录
		documentaryRecordsList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			qualityMap, // 询盘质量
			qualityRemark, // 询盘质量说明
			frequencys, // 跟进频次
			showCase: false, // 是否展开行业案例
			dialogEdit: false,
			detailForm: {}, //事件总线表单
			consultFormCopy: {}, //克隆数据

			// 咨询评价
			editForm: {
				consultingScore: '',
				consultingView: '',
				idid: '',
			},
			formRules: {
				// salesmanUid: [{ required: true, message: '请输入业务人员', trigger: 'blur' }],
				quality: [{ required: true, message: '请输入询盘质量', trigger: 'blur' }],
				industryProficiency: [{ required: true, message: '请输入行业熟练程度', trigger: 'blur' }],
				followUpStrategy: [{ required: true, message: '请输入跟进策略', trigger: 'blur' }],
				followUpFrequency: [{ required: true, message: '请输入跟进频次', trigger: 'blur' }],
				consultingScore: [{ required: true, message: '请输入分值', trigger: 'blur' }],
				consultingView: [{ required: true, message: '请输入内容', trigger: 'blur' }],
				consultingOther: [{ required: true, message: '请输入咨询情况', trigger: 'blur' }],
			},

			// 跟单表单
			dialogFollow: false,
			followForm: {
				content: '',
				idid: '',
				drid: '',
				ckrid: '',
				nextPlan: '',
				nextStep: '',
				documentaryTime: '',
				planStatus: 0,
				aupcid: null, //积分配置项id
				recordType: '首次咨询', // 跟单类型 冗余字段，来自积分配置项 cfg_name
			},
			followFormRules: {
				nextStep: [{ required: true, message: '请输入计划时间！', trigger: 'blur' }],
				nextPlan: [{ required: true, message: '请输入计划内容！', trigger: 'blur' }],
			},
			// 计划日期选择器
			pickerOptions: {
				disabledDate: time => {
					// 不允许选择过去日期 只能选择今天以及未来93天内的日期
					const now = this.$moment().startOf('day').valueOf() - 1;
					const pickTime = this.$moment(time).startOf('day').valueOf();
					const thirtyDaysAfter = this.$moment().add(93, 'days').valueOf();
					return pickTime <= now || pickTime >= thirtyDaysAfter;
				},
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 咨询表单(用于检测表单是否修改，以及作为接口入参)
		consultForm() {
			return {
				callRecording: this.detailForm.callRecording,
				// consult: this.detailForm.consultUid,
				salesman: this.detailForm.salesmanUid,
				// hosterTwid: this.detailForm.hosterTwid,
				consultingOther: this.detailForm.consultingOther,
				consultingDate: this.detailForm.consultingDate,
				contactBackground: this.detailForm.contactBackground,
				contactInfo: this.detailForm.contactInfo,
				followUpFrequency: this.detailForm.followUpFrequency,
				followUpStrategy: this.detailForm.followUpStrategy,
				idid: this.detailForm.idid || this.idid,
				industryProficiency: this.detailForm.industryProficiency,
				keyRequire: this.detailForm.keyRequire,
				mainProcess: this.detailForm.mainProcess,
				product: this.detailForm.product,
				projectPhase: this.detailForm.projectPhase,
				quality: this.detailForm.quality,
				scale: this.detailForm.scale,
				stage: this.detailForm.stage,
				recordingDuration: this.detailForm.recordingDuration || 0,
			};
		},
		// 是否首次咨询：如果不是无效/择时,无咨询完成日期或无跟单记录 :自动弹出下一步计划，保存后生成跟单记录，跟单方式为首次咨询，跟单内容为咨询内容
		isFirstConsult() {
			return !(this.detailForm.quality == 5 || this.detailForm.quality == 6) && !this.documentaryRecordsList?.length;
		},
		// 是否修改
		isUpdate() {
			const FLAG = JSON.stringify(this.consultForm) !== JSON.stringify(this.consultFormCopy);
			// console.log('consultForm_isUpdate', FLAG);
			return FLAG;
		},
		// 只有超级管理员/管理员可删除
		isDelete() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '管理员') || false;
		},
		// 只有询盘评价员可评分
		isEvaluator() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '询盘评价员') || false;
		},
		// 是否为其他跟进频次
		isOther() {
			return !this.frequencys.includes(this.detailForm.followUpFrequency);
		},
		// 当前用户是否为该询盘的咨询人员
		isConsultUser() {
			return this.userInfos?.adminUserVO.auid == this.detailForm.consultUid || this.isSuperAdmin;
		},
		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {
		// 订阅事件（注意这里要加多一层parent 使其与发布事件一致）
		eventBus.$on(`updateDetailForm_${this.$parent.$parent.$options.name}`, detailForm => {
			// 处理事件，更新本地的 detailForm 对象
			this.detailForm = detailForm;
			this.consultFormCopy = deepClone(this.consultForm); //克隆数据
		});
	},

	beforeDestroy() {
		eventBus.$off(`updateDetailForm_${this.$parent.$parent.$options.name}`);
	},
	destroyed() {
		this.detailForm = null;
		this.consultFormCopy = null;
	},
	// 方法集合
	methods: {
		// 获取咨询人员信息
		getConsultInfo({ consultUid }) {
			this.detailForm.consultUid = consultUid;
		},
		// 刷新咨询信息
		refreshDetailForm(data) {
			this.detailForm.consultationTime = data.consultationTime;
			this.detailForm.documentaryRecordsList = data.documentaryRecordsList;
		},
		// 下载录音
		downloadAudio(url) {
			window.open(url, '_blank');
		},
		// 保存跟单 - 添加跟单记录
		savePublish: debounce(async function () {
			if (!this.followForm.idid) return this.$message.warning('该数据出现异常，请刷新后重试！');
			if (checkRequired(this.followForm, this.followFormRules)) return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			const API = 'addDocumentaryRecords';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.followForm }));
				if (res.data.success) {
					// this.$emit('refresh'); //刷新详情
					// this.$succ(res.data.message);
					this.saveEdit(false);
					this.closeDialog();
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		// 若用户为无效:设置无效值
		setInvalid() {
			this.detailForm.consultingOther = '无效'; //咨询情况
			this.detailForm.quality = 5; //询盘质量
			this.detailForm.followUpStrategy = '无效'; //跟进策略
			this.detailForm.followUpFrequency = '无效'; //跟进频次
			this.detailForm.stage = 0; //阶段：咨询
			this.detailForm.industryProficiency = 1; //行业熟练程度 部署
			this.saveEdit();
		},
		// 重新上传替换
		openUpload() {
			// 超过3天不允许替换
			const isOver = this.detailForm.consultationTime < new Date().getTime() - 3 * 24 * 60 * 60 * 1000;
			if (isOver) {
				return this.$message.warning('咨询完成时间已超过3天不允许替换，如有需要请联系管理员!');
			}
			this.$confirm('正在进行替换的操作, 上传成功后原录音将会被替换（不保留历史录音），是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$refs?.uploadRef.$children[0].handleClick();
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 修改质量
		async changeQuality(quality) {
			try {
				//如果选择无效，“跟进频率”默认选择其他，并备注无效。下面“跟进策略”默认备注无效
				if (quality == 5) {
					this.detailForm.followUpStrategy = '无效';
					this.detailForm.followUpFrequency = '无效';
				} else if (quality == 6) {
					// 如果选择择时再打，下面“跟进策略”默认备注择时再打，“跟进频率”默认选择其他，并备注择时再打.
					this.detailForm.followUpStrategy = '择时再打';
					this.detailForm.followUpFrequency = '择时再打';
				}
			} catch (error) {
				console.log(error);
			}
		},

		// 填充空值
		fillEmpty(type, content) {
			const fields = {
				未联系上: ['followUpFrequency', 'followUpStrategy'],
				跟进策略: ['followUpStrategy'],
				咨询情况: ['keyRequire', 'product', 'mainProcess', 'scale', 'projectPhase', 'contactBackground', 'consultingOther'],
			}[type];
			fields?.forEach(field => {
				this.$set(this.detailForm, field, this.detailForm[field] || content);
			});
		},
		jointString: jointString, //拼接字符串
		openDialog(type) {
			if (type == '评价') {
				// 咨询评价
				this.editForm = {
					consultingScore: this.detailForm.consultingScore,
					consultingView: this.detailForm.consultingView,
					idid: this.detailForm.idid || this.idid,
				};
				this.dialogEdit = true;
			} else if (type == '跟单') {
				this.followForm = {
					content: this.detailForm.consultingOther,
					idid: this.detailForm.idid || this.idid,
					drid: '',
					ckrid: this.detailForm.ckrid,
					nextPlan: '',
					nextStep: '',
					documentaryTime: new Date().getTime(),
					planStatus: 0,
					aupcid: null, //积分配置项id
					recordType: '首次咨询', // 跟单类型 冗余字段，来自积分配置项 cfg_name
				};
				this.dialogFollow = true;
			}
		},
		closeDialog() {
			if (this.dialogEdit) {
				this.editForm = resetValues(this.editForm);
			} else if (this.dialogFollow) {
				this.followForm = resetValues(this.followForm);
			}
			this.dialogEdit = false;
			this.dialogFollow = false;
		},
		// 删除录音
		delAudio() {
			this.$confirm('注意,该询盘录音删除后不可恢复, 是否继续操作?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					const str = JSON.stringify({ idid: this.detailForm.idid || this.idid });
					this.$axios
						.deleteCallRecording(str)
						.then(res => {
							if (res.data.success) {
								this.detailForm.callRecording = '';
								this.consultFormCopy = deepClone(this.consultForm); //克隆数据
								this.$message.success('删除成功！');
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteCallRecording |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消操作！');
				});
		},
		// 上传录音
		handleAudioUpload(item) {
			const isLt50M = item.file.size / 1024 / 1024 < 50; // 如果文件大于50M，则不上传
			if (!isLt50M) {
				this.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}
			this.detailForm.isUpLoadAudio = true;
			const formData = new FormData();
			formData.append('file', item.file);
			this.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						this.detailForm.callRecording = res.data.data.path;
						this.detailForm.isUpLoadAudio = false;

						this.saveEdit(this.isFirstConsult);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.detailForm.callRecording = '';
					this.detailForm.isUpLoadAudio = false;
					console.error('Upload failed', error);
				});
		},
		// 保存基本信息(修改询盘咨询信息)
		saveEdit: debounce(async function (isFirstConsult = false) {
			if (checkRequired(this.consultForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}
			if (!this.isConsultUser) {
				console.log('isConsultUser：', this.userInfos?.adminUserVO.auid, this.detailForm.consultUid);
				return this.$message.warning('您不是当前询盘的咨询人员，请修改【基本信息】的咨询人员并保存后再进行此操作！');
			}

			// 除询盘质量为无效/择时再打/复购的询盘外，保存时必须上传询盘录音
			if (!([5, 6].includes(this.detailForm.quality) || this.detailForm.rePurchase) && !this.detailForm.callRecording) {
				console.log('isSave：', this.detailForm.quality, this.detailForm.rePurchase, this.detailForm.callRecording);
				return this.$message.warning('除询盘质量为无效/择时再打/复购的询盘外，保存时必须上传询盘录音！');
			}
			// 如果是首次咨询，则弹出跟单对话框
			if (isFirstConsult) {
				return this.openDialog('跟单');
			}

			const API = 'updateConsultingInfo';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.consultForm }));
				if (res.data.success) {
					this.consultFormCopy = deepClone(this.consultForm); //克隆数据
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		// 保存评价
		saveEvaluate: debounce(async function () {
			// if (checkRequired(this.editForm, this.formRules)) {
			// 	return;
			// }
			this.editForm.consultingScore = parseFloat(this.editForm.consultingScore);
			const API = 'updateConsultingEvaluate';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.editForm }));
				if (res.data.success) {
					this.detailForm.consultingScore = this.editForm.consultingScore;
					this.detailForm.consultingView = this.editForm.consultingView;
					this.consultFormCopy = deepClone(this.consultForm); //克隆数据
					this.closeDialog();
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),

		dateFormat, //日期format
	},
};
</script>

<style lang="scss" scoped>
#ConsultInfo {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;

	.consult-tips {
		display: flex;
		height: 180px;
		flex-direction: column;
		justify-content: space-between;
	}
}
</style>
