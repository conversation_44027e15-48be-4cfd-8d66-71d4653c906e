import Vue from 'vue';
import ElementUI, { Table } from 'element-ui';
import 'umy-ui/lib/theme-chalk/index.css';
import '@/assets/fonts'; // 导入字体文件
import '@/styles/main.scss';
import { UTable, UTableColumn } from 'umy-ui';
import App from '@/App';
import store from '@/vuex';
import router from '@/router';
import axios from '@/fetch';
import moment from 'moment';
import pollerManager from './util/pollerManager/pollerManager';

import { scrollBoard } from '@jiaminghi/data-view';
import { succ, err } from '@/util/notify'; //全局 notify

// 解决 ElTable 自动宽度高度导致的「ResizeObserver loop limit exceeded」问题
const fixElTableErr = table => {
	const oldResizeListener = table.methods.resizeListener;
	table.methods.resizeListener = function () {
		window.requestAnimationFrame(oldResizeListener.bind(this));
	};
};
// 一定要在Vue.use之前执行此函数
fixElTableErr(Table);

Vue.config.productionTip = false; //在生产环境下是否显示生产提示

Vue.use(ElementUI); //饿了么组件
Vue.use(UTable); //umy-ui 按需引入表格
Vue.use(UTableColumn); //umy-ui 按需引入表格列
Vue.use(scrollBoard);

// 全局属性
Vue.prototype.$axios = axios; //axios http库
Vue.prototype.$moment = moment; //moment.js 日期格式
Vue.prototype.$succ = succ; //全局 notify 操作成功提示
Vue.prototype.$err = err; //全局 notify 操作报错提示
Vue.prototype.$poller = pollerManager; // 全局轮询管理器

// 全局组件
import BaseLayout from '@/components/BaseLayout'; //常规报表页面布局组件
Vue.component('BaseLayout', BaseLayout);
import Tooltips from '@/components/Tooltips'; //文本冒泡提示组件
Vue.component('Tooltips', Tooltips);
import SearchHistoryInput from '@/components/SearchHistoryInput.vue'; //搜索历史输入框组件
Vue.component('SearchHistoryInput', SearchHistoryInput);

new Vue({
	router,
	store,
	render: h => h(App),
}).$mount('#app');
