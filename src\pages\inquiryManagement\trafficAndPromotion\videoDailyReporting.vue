<template>
	<div class="videoDailyReporting" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">流量报工 - {{ userInfos?.adminUserVO.userName }}</span>
				<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
			</div>
			<!-- <iframe src="http://192.168.1.200:3030/chat/DJroZ0nNrXvslDGQ" style="width: 100%; height: 100%; min-height: 700px" frameborder="0" allow="microphone"></iframe> -->

			<!-- 明细详情弹窗 -->
			<div class="detail-content">
				<!-- 工作日期 -->
				<div class="top-content">
					<span class="search-label">工作日期</span>
					<el-date-picker
						size="small"
						v-model="selectTime"
						:default-value="selectTime"
						type="date"
						value-format="timestamp"
						format="yyyy-MM-dd"
						placeholder="请选择工作日期"
						:clearable="false"
						@change="queryDetail()"
						:picker-options="pickerOptions"
					>
					</el-date-picker>
				</div>

				<!-- 今日数据 -->
				<div class="center-content">
					<!-- 标题 -->
					<div class="title-content">
						<p class="detail-content-title">今日数据 </p>
						<el-button type="primary" @click="addVideoDayCount">保存</el-button>
					</div>
					<!-- 表格 -->
					<div class="table-content">
						<span class="search-label"
							>询盘合计：{{ totalInquiries }}个 投流金额合计：{{ Number(totalMoneyAmount.toFixed(2)) }}元
						</span>
						<span class="search-label" v-if="detailForm.isVideoType">总播放量：{{ Number(totalPlayCount.toFixed(2)) }}次 </span>
						<span class="search-label" v-if="detailForm.isVideoType">发布量</span>
						<el-input
							type="number"
							v-if="detailForm.isVideoType"
							class="w-100 right-aligned-input"
							v-model.number="detailForm.publishingCount"
							placeholder=""
							size="small"
							clearable
						></el-input>
						<span class="search-label" v-if="detailForm.isVideoType">剪辑量</span>
						<el-input
							type="number"
							v-if="detailForm.isVideoType"
							class="w-100 right-aligned-input"
							v-model.number="detailForm.editingCount"
							placeholder=""
							size="small"
							clearable
						></el-input>

						<div class="data-cell cell-header">
							<div class="column-view cell-title">账号</div>
							<div class="column-view">抖音播放量</div>
							<div class="column-view">抖音询盘数</div>
							<div class="column-view">视频号播放量</div>
							<div class="column-view">视频号询盘数</div>
							<div class="column-view">其他平台播放量</div>
							<div class="column-view">其他询盘数</div>
							<div class="column-view">今日投流金额</div>
						</div>

						<div class="data-cell" v-for="(item, index) in detailForm.selectAdminVideoDayCountVOS" :key="index">
							<div class="column-view cell-title">{{ item.accountName }}</div>
							<div class="column-view">
								<el-input
									type="number"
									class="right-aligned-input"
									v-model.number="item.douyinPlayCount"
									placeholder=""
									size="small"
									clearable
								></el-input>
							</div>
							<div class="column-view">
								<el-input
									type="number"
									class="right-aligned-input"
									v-model.number="item.douyinInquiryCount"
									placeholder=""
									size="small"
									clearable
								></el-input>
							</div>
							<div class="column-view">
								<el-input
									type="number"
									class="right-aligned-input"
									v-model.number="item.videoAccountPlayCount"
									placeholder=""
									size="small"
									clearable
								></el-input>
							</div>
							<div class="column-view">
								<el-input
									type="number"
									class="right-aligned-input"
									v-model.number="item.videoAccountInquiryCount"
									placeholder=""
									size="small"
									clearable
								></el-input>
							</div>
							<div class="column-view">
								<el-input
									type="number"
									class="right-aligned-input"
									v-model.number="item.outerPlayformsPlayCount"
									placeholder=""
									size="small"
									clearable
								></el-input>
							</div>
							<div class="column-view">
								<el-input
									type="number"
									class="right-aligned-input"
									v-model.number="item.otherPlatformsInquiryCount"
									placeholder=""
									size="small"
									clearable
								></el-input>
							</div>
							<div class="column-view">
								<el-input
									type="number"
									class="right-aligned-input"
									v-model.number="item.todayMoneyAmount"
									placeholder=""
									size="small"
									clearable
								></el-input>
							</div>
						</div>
					</div>
				</div>
				<!-- 今日工作 -->
				<div class="center-content">
					<!-- 标题 -->
					<div class="title-content">
						<p class="detail-content-title">今日工作 </p>
						<el-button type="primary" @click="addPointDayCount">保存</el-button>
					</div>
					<!-- 表格 -->
					<div class="table-content">
						<span class="search-label">积分：{{ Number(totalPoints.toFixed(2)) }} </span>
						<div class="data-cell cell-header bottom-data-cell">
							<div class="column-view cell-title">工作项</div>
							<div class="column-view">数量</div>
							<div class="column-view">获得积分值</div>
						</div>

						<div class="data-cell bottom-data-cell" v-for="(item, index) in detailForm.selectPointsCountVOS" :key="index">
							<div class="column-view cell-title">{{ item.cfgName }}</div>
							<div class="column-view">
								<el-input
									type="number"
									class="right-aligned-input"
									v-model.number="item.pointsWorkingDays"
									placeholder=""
									size="small"
									clearable
								></el-input>
							</div>
							<div class="column-view">
								<el-input
									type="number"
									class="right-aligned-input"
									disabled
									:value="item.pointsWorkingDays * item.cfgPoints"
									placeholder=""
									size="small"
									clearable
								></el-input>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';

export default {
	name: 'videoDailyReporting',
	directives: {},
	components: {},
	props: {},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			tpid: '',
			cid: '',
			logList: [],
			detailForm: {
				selectAdminVideoDayCountVOS: [],
				selectPointsCountVOS: [],
			},
			detailForm_Copy: [],
			currentIndex: 0,
			addSelected: false,

			elementKey: 0,

			radio: true,

			//资源列表
			teamList: [], //分销/代理列表
			userList: [],

			selectTime: new Date().setHours(0, 0, 0, 0), //取当天0点的时间戳
			pickerOptions: {
				disabledDate: time => {
					const today = new Date();
					const yesterday = new Date(today);
					yesterday.setDate(yesterday.getDate() - 1);

					// 只允许选择今天和昨天的日期
					return time.getTime() < yesterday.setHours(0, 0, 0, 0) || time.getTime() > today.setHours(23, 59, 59, 999);
				},
			},
		};
	},
	created() {},
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队等）
		editorHeight() {
			// return this.showType ? 480 : 550;
			return 500;
		},
		totalInquiries() {
			return this.detailForm.selectAdminVideoDayCountVOS.reduce((total, item) => {
				return (
					total + (item.douyinInquiryCount || 0) + (item.videoAccountInquiryCount || 0) + (item.otherPlatformsInquiryCount || 0)
				);
			}, 0);
		},
		totalMoneyAmount() {
			return this.detailForm.selectAdminVideoDayCountVOS.reduce((total, item) => {
				return total + (item.todayMoneyAmount || 0);
			}, 0);
		},
		totalPlayCount() {
			return this.detailForm.selectAdminVideoDayCountVOS.reduce((total, item) => {
				return total + (item.douyinPlayCount || 0) + (item.videoAccountPlayCount || 0) + (item.outerPlayformsPlayCount || 0);
			}, 0);
		},
		totalPoints() {
			return this.detailForm.selectPointsCountVOS.reduce((sum, item) => {
				return sum + (item.pointsWorkingDays || 0) * (item.cfgPoints || 0);
			}, 0);
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.detailForm = _.resetValues(this.detailForm); //重置对象
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 查询明细
		queryDetail() {
			this.$axios
				.selectAdminVideoDayCount(
					JSON.stringify({
						date: this.selectTime,
					}),
				)
				.then(res => {
					if (res.data.success) {
						if (res.data.data.categoryNames?.some(cat => cat === '视频类')) {
							res.data.data.isVideoType = true;
						} else {
							res.data.data.isVideoType = false;
						}
						if (!res.data.data.selectAdminVideoDayCountVOS) {
							res.data.data.selectAdminVideoDayCountVOS = [];
						}
						if (!res.data.data.selectPointsCountVOS) {
							res.data.data.selectPointsCountVOS = [];
						}

						this.detailForm = res.data.data;
						console.log(this.detailForm);
						this.detailForm_Copy = _.deepClone(this.detailForm);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('selectCourseDetails |' + error);
				});
		},

		// 添加/保存今日数据信息
		addVideoDayCount() {
			// console.log(this.detailForm)
			const paramas = {
				adminVideoDayCountDTOS: this.detailForm.selectAdminVideoDayCountVOS,
				date: this.selectTime,
				editingCount: this.detailForm.editingCount,
				publishingCount: this.detailForm.publishingCount,
			};
			// console.log(paramas)

			const API = 'updateAdminVideoDayCount';
			this.$axios[API](JSON.stringify(paramas))
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功！');
						setTimeout(() => {
							this.queryDetail();
						}, 1000);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log(`${API} | ` + error);
				});
		},
		// 添加/保存今日积分信息
		addPointDayCount() {
			// console.log(this.detailForm)
			this.detailForm.selectPointsCountVOS.forEach(item => {
				if (item.pointsWorkingDays && item.cfgPoints) {
					item.points = (item.pointsWorkingDays || 0) * (item.cfgPoints || 0);
				}else {
					item.points = 0;
				}
			});
			const paramas = {
				updatePointsByDayDTO: this.detailForm.selectPointsCountVOS,
				date: this.selectTime,
			};
			// console.log(paramas)

			const API = 'updatePointByDayCount';
			this.$axios[API](JSON.stringify(paramas))
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功！');
						setTimeout(() => {
							this.queryDetail();
						}, 1000);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log(`${API} | ` + error);
				});
		},
		//显示弹窗
		showDetailCom() {
			this.selectTime = new Date().setHours(0, 0, 0, 0); //取当天0点的时间戳
			this.queryDetail();
			this.showCom = true;
		},

		//点击返回
		closeDetailCom() {
			// 本地存储聚焦状态
			let isSameData = true;
			isSameData = JSON.stringify(this.detailForm) == JSON.stringify(this.detailForm_Copy);
			// console.log(JSON.stringify(this.detailForm), JSON.stringify(this.detailForm_Copy));
			console.log('isSameData', isSameData);
			if (!isSameData) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定退出',
					cancelButtonText: '取消退出',
				})
					.then(() => {
						this.clearDetailData(); // 关闭并清空数据
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.clearDetailData(); // 关闭并清空数据
			}
		},
		// 关闭并清空数据
		clearDetailData() {
			this.showCom = false;
			this.detailForm = _.resetValues(this.detailForm); //重置对象
		},
		//日期format
		dateFormat: _.dateFormat,

		jointString: _.jointString,
	},
};
</script>
<style lang="scss">
.videoDailyReporting .detail-content {
	background: #fff;
	border-top: 1px solid #f2f2f2;
	padding: 0 2vw;
	height: calc(100% - 102px);
	box-sizing: border-box;
	padding: 5px 20px;
	overflow-y: scroll;

	.top-content {
		padding-top: 15px;
	}
	.center-content {
		padding-top: 15px;
		.title-content {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.table-content {
			border: 1px solid #ccc;
			border-radius: 8px;
			padding: 10px;

			.data-cell {
				display: flex;
				align-items: center;
				width: 100%;
				justify-content: space-between;
				.column-view {
					padding: 10px;
					width: 110px;
					font-size: 12px;
					color: #606266;
					white-space: nowrap; /* 防止文字换行 */
					overflow: hidden; /* 隐藏溢出的内容 */
					text-overflow: ellipsis; /* 显示省略号表示被隐藏的文字（可选） */
					text-align: right;
					.right-aligned-input .el-input__inner {
						text-align: right; /* 右对齐输入框内的文本 */
					}
				}
				.cell-title {
					text-align: left;
					width: 230px;
				}
			}
			.cell-header {
				background-color: #efefef;
				margin-top: 15px;
			}
			.bottom-data-cell {
				display: flex;
				align-items: center;
				// width: 100%;
				justify-content: flex-start;
			}
		}
	}
}
</style>
