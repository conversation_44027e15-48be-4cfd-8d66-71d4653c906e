'use strict';
process.env.NODE_ENV = 'production';
const rm = require('rimraf');
const path = require('path');
const webpack = require('webpack');
const config = require('../config');
const webpackConfig = require('./webpack.prod.conf');
const prodEnv = require('../config/prod.env');

rm(path.join(config.build.assetsRoot, config.build.assetsSubDirectory), err => {
	if (err) throw err;
	webpack(webpackConfig, (err, stats) => {
		if (err) throw err;
		/* 为了打包信息简洁点隐藏下面提示 一般会报warnings平时不会用到 有报错errors再打开注释看看具体 */
		// process.stdout.write(
		// 	stats.toString({
		// 		colors: true,
		// 		modules: false,
		// 		children: false, // If you are using ts-loader, setting this to true will make TypeScript errors show up during build.
		// 		chunks: false,
		// 		chunkModules: false,
		// 	}) + '\n\n',
		// );

		if (stats.hasErrors()) {
			process.exit(1);
		}
		// 打印打包地址
		console.log(`\x1B[42m ✌  Build complete with address : ${prodEnv.API_HOST} \x1B[0m \n`);
	});
});
