<template>
	<div class="inquiry-audio-player">
		<AudioPlayer
			ref="audioPlayer"
			:audioUrl="audioUrl"
			:autoReleaseResource="autoReleaseResource"
			@loadMetadata="onLoadMetadata"
			@playStart="onPlayStart"
			@playPause="onPlayPause"
			@playEnded="onPlayEnded"
		/>
	</div>
</template>

<script>
import { debounce } from '@/util/tool';
import AudioPlayer from '@/components/AudioPlayer';
import { mapGetters } from 'vuex';
export default {
	name: 'InquiryAudioPlayer',
	components: {
		AudioPlayer,
	},
	props: {
		// 录音URL
		audioUrl: {
			type: String,
			default: '',
			required: true,
		},
		// 询盘ID
		idid: {
			type: String,
			default: '',
			required: true,
		},
		/**
		 * 是否自动释放资源
		 * 设为true时，组件不可见时会释放音频资源
		 */
		autoReleaseResource: {
			type: Boolean,
			default: true,
		},
	},

	data() {
		return {
			// 播放统计信息
			playStats: null,
			inquiryData: {
				idid: '', // 询盘ID
				listeningProgressRatio: 0, // 听音进度
				listenedDuration: 0, // 已听时长 单位秒
				recordingDuration: 0, // 录音时长 单位秒
				recordUrl: '', // 录音URL
			},
		};
	},
	computed: {
		...mapGetters(['userInfo', 'userRoles', 'userLabels']),
		// 是否超级管理员
		isSuperAdmin() {
			return this.userRoles?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
		// 是否是BPUL
		isBPUL() {
			return this.userLabels?.some(item => item.remark == 'BPUL' || item.remark == '询盘评价员') || false;
		},
		// 是否记录播放时长
		isRecordPlayDuration() {
			return this.isSuperAdmin || this.isBPUL;
		},
	},
	methods: {
		// 音频元数据加载完成事件处理
		onLoadMetadata(stats) {
			this.playStats = stats;
			this.$emit('onLoad', {
				...stats,
				inquiryData: this.inquiryData,
			});
		},
		// 播放开始事件处理
		onPlayStart(stats) {
			this.playStats = stats;
			this.$emit('playStart', {
				...stats,
				inquiryData: this.inquiryData,
			});
			// 首次播放时查询询盘详情
			this.isRecordPlayDuration && this.playStats.playCount == 1 && this.queryInquiryDetail();
		},
		// 播放暂停事件处理
		onPlayPause(stats) {
			this.playStats = stats;
			this.$emit('playPause', {
				...stats,
				inquiryData: this.inquiryData,
			});
			this.updateRecordPlayInfo(stats, 'pause');
		},
		// 播放结束事件处理
		onPlayEnded(stats) {
			this.playStats = stats;

			this.$emit('playEnded', {
				...stats,
				inquiryData: this.inquiryData,
			});
			this.updateRecordPlayInfo(stats, 'ended');
		},
		// 查询询盘详情
		async queryInquiryDetail() {
			const API = 'selectInquiryDocumentaryOne';
			try {
				const res = await this.$axios[API](JSON.stringify({ idid: this.idid }), { skipCancel: true });
				if (res.data.success) {
					this.inquiryData = { ...this.inquiryData, ...res.data.data };
					const { listenedDuration = 0, recordingDuration = 0 } = this.inquiryData;
					// 已听过时自动定位到已听过时长，如果听过时长大于录音时长，则从0开始播放
					if (listenedDuration) {
						const currentTime = listenedDuration > recordingDuration ? 0 : listenedDuration;
						const duration = listenedDuration; // 注意播放器已自动播放
						this.$refs.audioPlayer?.updatePlayDuration(duration, currentTime);
					}
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 记录播放信息
		updateRecordPlayInfo: debounce(async function (stats, type) {
			if (!this.isRecordPlayDuration) return console.log('非超级管理员或BPUL，不记录播放时长');
			// 如果设置了询盘ID，在这里向后端记录播放信息
			if (!this.inquiryData.idid || !stats) return console.log('updateRecordPlayInfo', this.inquiryData, stats);

			const API = 'updateListenedDurationByIdid';
			try {
				const { idid } = this.inquiryData;
				this.inquiryData.listenedDuration += Math.floor(stats.currentSessionDuration); // 如果是暂停，则累加当前会话时长
				const listenedDuration = type === 'pause' ? this.inquiryData.listenedDuration : Number(stats.playDuration?.toFixed(0)); // 如果是播放结束，则直接使用播放器播放时长
				const recordingDuration = this.formatTimeToSeconds(stats.audioDuration) || this.inquiryData.recordingDuration || 0;
				const res = await this.$axios[API](JSON.stringify({ idid, listenedDuration, recordingDuration }), { skipCancel: true });
				if (res.data.success) {
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}, 300),
		// 获取播放统计信息
		getPlayStats() {
			// 通过ref可以直接获取底层组件的播放统计
			if (this.$refs.audioPlayer) {
				return this.$refs.audioPlayer.getPlayStats();
			}
			return this.playStats;
		},

		/**
		 * 转换为秒
		 * @param {String} time - 时间字符串 (MM:SS)
		 * @returns {Number} 秒数
		 */
		formatTimeToSeconds(time) {
			if (!time) return 0;
			const [mins, secs] = time.split(':').map(Number);
			return mins * 60 + secs;
		},
	},
	beforeDestroy() {
		// 组件销毁前，如果正在播放，记录播放信息
		if (this.$refs.audioPlayer) {
			// const stats = this.getPlayStats();
			// if (stats && stats.isPlaying) {
			// 	this.updateRecordPlayInfo(stats, 'pause');
			// }
			// 销毁子组件
			this.$refs.audioPlayer.$destroy();
		}
	},
};
</script>

<style lang="scss" scoped>
.inquiry-audio-player {
	width: 100%;

	.play-stats {
		font-size: 12px;
		color: #909399;
		margin-top: 4px;
		text-align: right;
	}
}
</style>
