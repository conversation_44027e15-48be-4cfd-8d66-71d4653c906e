<template>
	<div class="DemandTable">
		<!-- 需求信息 -->
		<div v-if="titleName == '需求信息'" class="header-info pb10 fs-12">
			<div class="flex-align-center gap-10">
				<div class="flex-align-center">
					<div class="">客户：</div>
					<Tooltips :cont-str="nowDetailForm.registeredBusinessName" :cont-width="480" />
				</div>
				<div class="flex-align-center">
					<div class="min-w-25">合同：</div>
					<Tooltips :cont-str="nowDetailForm.contractNo || '暂无'" :cont-width="480" />
				</div>
				<div class="flex-align-center">
					<div class="min-w-60">项目类别：</div>
					<Tooltips :cont-str="projectCategoryMap[nowDetailForm.projectCategory]" :cont-width="480" />
				</div>
			</div>
		</div>
		<!-- 需求表格 -->
		<u-table
			class="table-main detail-table W100 input-border-none"
			size="mini"
			ref="uTableRef"
			max-height="400px"
			:row-height="30"
			:data="nowTableData"
			:row-style="{ height: '0' }"
			:cell-style="{ padding: '0 0', borderBottom: '1px solid #e9e9e9' }"
			:header-cell-style="{ border: 'transparent', padding: '5px 0 !important' }"
			:show-summary="showSummary"
			:summary-method="summaryMethod"
		>
			<u-table-column
				v-for="col in tableColumn"
				:key="col.colNo"
				:label="col.colName"
				:prop="col.colNo"
				:align="col.align"
				:width="col.width"
			>
				<template slot-scope="scope">
					<!-- 需求描述 -->
					<Tooltips
						class="pl10"
						v-if="col.colNo == 'demandDescription' && titleName !== '需求信息'"
						:cont-str="getDemandDescription(scope.row)"
						:cont-width="(scope.column.width || scope.column.realWidth) - 20"
					/>
					<!-- 人天评估 @贝炉：在技术评审前都可以修改-->
					<el-input
						v-else-if="col.colNo == 'manDay' && titleName == '需求信息' && nowDetailForm.status == 2"
						class="input-green"
						size="mini"
						v-model="scope.row[col.colNo]"
						:placeholder="col.colName"
						clearable
						@change="changeManDay(scope.row)"
					></el-input>

					<!-- 未评审 -->
					<div v-else-if="isNotReview">
						<!-- 共计 -->
						<Tooltips
							class="pr10"
							v-if="col.colNo == 'totalDuration'"
							:cont-str="scope.row[col.colNo]"
							:cont-width="(scope.column.width || scope.column.realWidth) - 20"
						/>
						<!-- 输入工时 -->
						<el-input
							v-else
							size="mini"
							v-model="scope.row[col.colNo]"
							:placeholder="col.colName"
							clearable
							@change="changeHour(scope.row)"
						></el-input>
					</div>

					<!-- 需求类别 -->
					<Tooltips
						class="pl10"
						v-else-if="col.colNo == 'demandType'"
						:cont-str="demandTypeMap[scope.row[col.colNo]]"
						:cont-width="(scope.column.width || scope.column.realWidth) - 20"
					/>

					<!-- 客户要求完成日期 -->
					<Tooltips
						v-else-if="col.colNo == 'customerRequiredCompletionDate'"
						:cont-str="dateFormat(scope.row[col.colNo])"
						:cont-width="(scope.column.width || scope.column.realWidth) - 20"
					/>
					<!-- 开发承诺完成日期 -->
					<div v-else-if="col.colNo == 'developmentPromiseCompletionDate'">
						<!-- 开发经理签署 -->
						<el-date-picker
							v-if="isDevNotSign && isDevManager"
							v-model="scope.row[col.colNo]"
							type="date"
							size="mini"
							placeholder="承诺完成日期"
							:class="`W100 ${scope.row[col.colNo] || 'input-border-red'}`"
							value-format="timestamp"
						>
						</el-date-picker>
						<Tooltips
							v-else
							:cont-str="dateFormat(scope.row[col.colNo])"
							:cont-width="(scope.column.width || scope.column.realWidth) - 20"
						/>
					</div>

					<!-- 数值 -->
					<Tooltips
						class="pr10"
						v-else-if="numberKey.includes(col.colNo)"
						:cont-str="scope.row[col.colNo] ? scope.row[col.colNo].toFixed(1) : '-'"
						:cont-width="(scope.column.width || scope.column.realWidth) - 20"
					/>

					<!-- 其他 -->
					<Tooltips
						v-else
						class="pl10"
						:cont-str="scope.row[col.colNo] ? scope.row[col.colNo] + '' : ''"
						:cont-width="(scope.column.width || scope.column.realWidth) - 20"
					/>
				</template>
			</u-table-column>
		</u-table>
		<!-- 日志 -->
		<div v-if="nowLogList.length > 0" class="mt10 max-h-300 overflow-y-auto">
			<ExpandableList :items="nowLogList" :defaultCount="1">
				<template slot-scope="{ item }">
					<div class="flex-align-center gap-10 fs-12" :class="item.submitReject ? '' : 'red'">
						<div>{{ item.submitRejectName }}</div>
						<div>{{ dateFormat(item.submitRejectTime, 'YMDddddHm') }}</div>
						<div>{{ item.submitReject ? (titleName == '需求信息' ? '提交' : '同意') : '驳回' }}</div>
						<div>{{ item.remarks || '' }}</div>
						<div v-if="item.appointDevelopmentManagerName">{{ `指定 ${item.appointDevelopmentManagerName} 为开发经理` }}</div>
						<div v-if="item.testManagerName">{{ `指定 ${item.testManagerName} 为测试经理` }}</div>
					</div>
					<div v-if="item.projectNo" class="flex-align-center gap-10 fs-12">
						<div class="green hover-green" @click="openProject(item)">项目号：{{ item.projectNo }}</div>
						<!-- <div class="green hover-green" @click="openProject(item)">任务规划</div> -->
						<div>计划发版日期：{{ dateFormat(item.planReleaseDate, 'line') }}</div>
						<div>当前进展：{{ projectStatusMap[item.projectStatus] }}</div>
					</div>
				</template>
			</ExpandableList>
		</div>
		<!-- 操作 -->
		<div class="operation-btn pb10" :class="detailForm.status == componentIndex + 1 ? 'pt10' : ''">
			<!-- 技术评审 -->
			<div v-show="isNotReview">
				<!-- 开发经理（含仅产品总监、超级管理）可操作 -->
				<div v-if="isDevManager" class="flex-justify-between gap-10">
					<el-button :type="isUpdate ? 'primary' : ''" size="mini" @click="saveTechReview"> 保存</el-button>
					<el-input
						class="flex-1"
						size="mini"
						v-model="nowDetailForm.reviewOpinion"
						placeholder="请输入备注"
						clearable
					></el-input>
					<el-button
						:disabled="!nowDetailForm.reviewOpinion"
						:type="isUpdate ? '' : 'danger'"
						size="mini"
						@click="submitTechReview(0)"
					>
						驳回
					</el-button>
					<el-button class="m0" :type="isUpdate ? '' : 'primary'" size="mini" @click="submitTechReview(1)"> 同意 </el-button>
				</div>
				<div v-else-if="nowLogList.length == 0" class="blue fs-12"> 待技术经理评审 </div>
			</div>

			<!-- 需求签署 -->
			<div v-show="isNotSign">
				<!-- 产品总监或超级管理可操作 -->
				<div v-if="isTechLeader" class="flex-justify-between gap-10">
					<el-input
						class="flex-1"
						size="mini"
						v-model="nowDetailForm.signOpinion"
						placeholder="请输入签署意见"
						clearable
					></el-input>
					<el-select
						:class="[!nowDetailForm.appointDevelopmentManagerAuid ? 'input-border-red' : '']"
						v-model="nowDetailForm.appointDevelopmentManagerAuid"
						placeholder="指定开发经理"
						clearable
						filterable
						size="mini"
					>
						<el-option v-for="item in developerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
					<el-button :disabled="!nowDetailForm.signOpinion" type="danger" size="mini" @click="submitSign(0)"> 驳回 </el-button>
					<el-button
						:disabled="!nowDetailForm.appointDevelopmentManagerAuid"
						class="m0"
						type="primary"
						size="mini"
						@click="submitSign(1)"
					>
						同意
					</el-button>
				</div>
				<div v-else-if="nowLogList.length == 0" class="blue fs-12">待产品总监签署</div>
			</div>

			<!-- 开发经理签署 -->
			<div v-show="isDevNotSign">
				<!-- 开发经理（含产品总监、超级管理）可操作 -->
				<div v-if="isDevManager" class="flex-justify-between gap-10">
					<el-select
						:class="[!nowDetailForm.testManagerAuid ? 'input-border-red' : '']"
						v-model="nowDetailForm.testManagerAuid"
						placeholder="指定测试经理"
						clearable
						filterable
						size="mini"
					>
						<el-option v-for="item in testerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
					<el-autocomplete
						:class="[!nowDetailForm.pmid ? 'input-border-red' : '']"
						ref="projectNo"
						v-model="nowDetailForm.projectNo"
						placeholder="请输入项目名称"
						clearable
						size="mini"
						:debounce="200"
						:fetch-suggestions="(queryStr, cb) => queryByInput(queryStr, cb, '项目', nowDetailForm)"
						@select="handleSelect($event, '项目', nowDetailForm)"
						@clear="$refs.projectNo.activated = true"
					>
						<template slot-scope="{ item }">
							<span>{{ jointString(' | ', item.projectNo, item.projectName) }}</span>
						</template>
					</el-autocomplete>
					<el-button type="info" size="mini" @click="openProject(nowDetailForm)"
						>{{ nowDetailForm.pmid ? '编辑项目' : '创建项目' }}
					</el-button>
					<el-input class="flex-1" size="mini" v-model="nowDetailForm.opinion" placeholder="请输入签署意见" clearable></el-input>
					<el-button
						:disabled="!nowDetailForm.pmid || !nowDetailForm.testManagerAuid"
						type="primary"
						size="mini"
						@click="submitDevSign"
					>
						接受任务
					</el-button>
				</div>
				<div v-else-if="nowLogList.length == 0" class="blue fs-12">待开发经理签署</div>
			</div>
		</div>
	</div>
</template>
<script>
import { dateFormat, resetValues, jointString, deepClone } from '@/util/tool';
import { bigAdd, bigDiv } from '@/util/math';
import ExpandableList from '@/components/ExpandableList.vue';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { demandTypeMap, projectCategoryMap } from '@/assets/js/projectSource';
export default {
	name: 'DemandTable',
	components: { ExpandableList },
	props: {
		// 索引
		componentIndex: {
			type: Number,
			default: 0,
		},
		// 用户列表
		userList: {
			type: Array,
			default: () => [],
		},
		// 详情表单
		detailForm: {
			type: Object,
			default: () => {},
		},
		// 表格数据
		tableData: {
			type: Array,
			default: () => [],
		},

		// 表格映射
		tableInfo: {
			type: Object,
			default: () => {},
		},
		// 产品总监或超级管理(签署需求
		isTechLeader: {
			type: Boolean,
			default: false,
		},
		// 开发经理（技术评审，接受任务）
		isDevManager: {
			type: Boolean,
			default: false,
		},
		// 是否显示组件
		showCom: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			demandTypeMap, // 需求类别
			// 项目类别
			projectCategoryMap, // 需求项目类别
			nowTableData: [], // 当前表格数据
			nowTableDataCopy: [], // 当前表格数据副本
			// 当前详情表单
			nowDetailForm: {
				status: 1,
			},
			// 数值列
			numberKey: ['javaDuration', 'webDuration', 'iosDuration', 'andriodDuration', 'totalDuration', 'manDay'],
			// 状态映射
			projectStatusMap: {
				0: '规划中',
				1: '需求与方案评审通过',
				2: '功能分解完成',
				3: '开发计划完成',
				4: '开发全部完成',
				5: '测试全部完成',
				6: '功能验收通过',
				7: '发版准备就绪',
				8: '发版完成',
				9: '延误',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		// 表格标题
		titleName() {
			return this.tableInfo?.title || '';
		},
		// 表格列
		tableColumn() {
			return this.tableInfo?.tableColumn || [];
		},
		// 汇总列
		summaryKeys() {
			return this.tableInfo?.summaryKeys || [];
		},
		// 未评审
		isNotReview() {
			return this.nowDetailForm.status == 2 && this.titleName == '技术评审';
		},
		// 未签署
		isNotSign() {
			return this.nowDetailForm.status == 3 && this.titleName == '需求签署';
		},
		// 开发经理签署
		isDevNotSign() {
			return this.nowDetailForm.status == 4 && this.titleName == '开发经理签署';
		},
		// 已接收
		isSigned() {
			return this.nowDetailForm.status == 5 && this.titleName == '已接收';
		},
		// 当前日志(最近的日期排在前面)
		nowLogList() {
			return (
				this.detailForm?.demandSupervisionLogsVOS
					?.filter(item => item.status == this.componentIndex)
					.sort((a, b) => b.submitRejectTime - a.submitRejectTime) || []
			);
		},
		// 是否显示表尾合计
		showSummary() {
			const status = this.detailForm.status;
			// 不显示研发工时和技术人天时和关闭表尾合计
			if (this.titleName == '技术评审' && !this.isDevManager) {
				return false;
			}
			return this.summaryKeys.length > 0 && status != 1 && status != 2;
		},

		//开发人员列表(标签：开发)
		developerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('研发经理')) || [];
		},
		//测试人员列表(标签：测试)
		testerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('测试')) || [];
		},

		// 是否提示保存按钮
		isUpdate() {
			return JSON.stringify(this.nowTableData) != JSON.stringify(this.nowTableDataCopy);
		},
	},
	// 监控data中的数据变化
	watch: {
		'detailForm.dsid': {
			handler(newVal) {
				this.nowDetailForm = { ...this.nowDetailForm, ...this.detailForm };
				this.nowDetailForm.status = this.detailForm.status ? this.detailForm.status : 1;
				this.nowTableData = this.tableData;
				this.nowTableDataCopy = deepClone(this.nowTableData);
			},
			immediate: true,
		},
		// 是否显示组件
		showCom(newVal) {
			if (!newVal) {
				this.clear();
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.nowDetailForm = this.detailForm;
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		this.clear();
	},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			for (let columnIndex = 1; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					if (this.summaryKeys.includes(column.property)) return Number(item[column.property]) || 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? bigAdd(prev, curr, 1) : prev;
				}, 0); //合计计算
				means[columnIndex] = sum > 0 ? <span class="pr10 green">{sum}</span> : '';
			}
			return [means];
		},

		// 计算开发合计工时
		changeHour(row) {
			const { javaDuration, webDuration, iosDuration, andriodDuration } = row;
			row.totalDuration = bigAdd(bigAdd(javaDuration, webDuration), bigAdd(iosDuration, andriodDuration));
		},

		// 获取需求描述
		getDemandDescription(row) {
			const { number, demandDescription, demandType, manDay, totalDuration } = row;
			// 商务人天
			const businessManDay = this.titleName == '需求签署' ? `${manDay} 商务人天` : '';

			// 技术人天
			let technicalManDay = '';
			if ((this.titleName == '需求签署' || this.titleName == '开发经理签署') && this.isDevManager) {
				// 合计工时并转换为人天
				technicalManDay = `${(totalDuration / 8).toFixed(2)} 技术人天`;
			}

			const manDayStr = manDay ? `${manDay}人天` : '';

			return jointString(
				' | ',
				number, // 需求编号
				demandDescription, // 需求描述
				demandTypeMap[demandType], // 需求类别
				businessManDay || manDayStr, // 人天
				technicalManDay, // 技术人天
			);
		},

		// 修改人天
		async changeManDay(row) {
			const API = 'updateDemandSupervisionDetailByDsid';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.nowDetailForm, ...row }));
				if (res.data.success) {
					this.$message.success('修改成功');
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 保存技术评审
		async saveTechReview() {
			const API = 'technicalReview';
			try {
				const res = await this.$axios[API](JSON.stringify(this.nowTableData));
				if (res.data.success) {
					this.nowTableDataCopy = deepClone(this.nowTableData);
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 驳回/同意技术评审
		async submitTechReview(reviewResult) {
			if (this.isUpdate) {
				return this.$message.warning('请先保存需求信息!');
			}
			const API = 'submitOrRejectTechnicalReview';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.nowDetailForm, reviewResult }));
				if (res.data.success) {
					this.$emit('close');
					this.clear();
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 驳回/同意需求签署
		async submitSign(status) {
			const API = 'demandSign';
			try {
				const { appointDevelopmentManagerAuid, dsid, signOpinion } = this.nowDetailForm;
				const res = await this.$axios[API](JSON.stringify({ appointDevelopmentManagerAuid, dsid, signOpinion, status }));
				if (res.data.success) {
					this.$emit('close');
					this.clear();
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 开发经理签署 --接受任务
		async submitDevSign() {
			const API = 'developmentManagerSign';
			try {
				const res = await this.$axios[API](
					JSON.stringify({ ...this.nowDetailForm, developmentManagerSignDetailDTOS: this.nowTableData }),
				);
				if (res.data.success) {
					this.$emit('close');
					this.clear();
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 查询
		queryByInput(queryStr, cb, type, row) {
			if (!queryStr) {
				this.handleSelect({}, type, row);
			}

			const API_MAP = {
				项目: {
					API: 'selectProject',
					DATA: JSON.stringify({ channelName: [], queryParam: queryStr, pageNum: 1, pageSize: 100 }),
				},
			};
			this.$axios[API_MAP[type].API](API_MAP[type].DATA)
				.then(res => {
					if (res.data.success) {
						cb(res.data.data);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API_MAP[type].API} |` + error);
				});
		},
		// 选择
		handleSelect(item, type, row) {
			if (type == '项目') {
				this.nowDetailForm.projectNo = item?.projectNo || '';
				this.nowDetailForm.pmid = item?.pmid || '';
				this.nowDetailForm.testManagerAuid = item?.testManager?.auid || '';
			}
		},
		// 打开项目
		openProject(item) {
			if (item.pmid) {
				this.$emit('openProject', item); //编辑
			} else {
				// 新增
				const data = {
					projectName: item.demandDocumentName ? item.demandDocumentName.replace('需求说明书', '').replace(/\.[^.]+$/, '') : '', //去掉需求说明书和.之后的文字
					customer: item.registeredBusinessName ? item.registeredBusinessName.slice(0, 6) : '', //取前六位
					businessPeople: item?.submitterAuid || '', //交付项目经理 - 提交人
					projectManager: item?.appointDevelopmentManagerAuid || '', //开发项目经理 - 开发经理
					testManager: item?.testManagerAuid || '', //测试经理
					technicalManager: item?.technicalManagerAuid || '', //技术经理
					startTime: new Date().getTime(), //开始时间
					demandTime: new Date().getTime(), //需求与方案评审通过
					functionTime: new Date().getTime(), //功能分解完成
					planTime: new Date().getTime(), //开发计划确定
					status: 3, // 开发计划完成
				};
				this.$emit('openProject', data);
			}
		},
		// 清空
		async clear() {
			this.nowDetailForm = resetValues(this.detailForm);
			this.nowTableData = [];
			this.nowTableDataCopy = [];
		},

		dateFormat, // 日期格式化
		jointString, // 拼接字符串
	},
};
</script>

<style lang="scss">
.DemandTable {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	color: #666;

	.detail-table {
		height: auto !important;
		min-height: fit-content;
		td {
			height: 30px !important;
		}
	}
	.el-table__body-wrapper,
	.el-table__footer-wrapper {
		td,
		.cell {
			padding: 0 !important;
		}
	}

	.table-wrapper .table-main td .cell {
		padding: 0 !important;
	}
}
</style>
