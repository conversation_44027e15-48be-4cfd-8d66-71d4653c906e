/* 成交管理路由 */

const contractManagement = () => import('@/pages/deliveryManagement/contractManagement/contractManagement.vue'); //合同管理
const deliveryManagement = () => import('@/pages/deliveryManagement/deliveryManagement/deliveryManagement.vue'); //交付管理
const extensionApply = () => import('@/pages/deliveryManagement/extensionApply.vue'); //延期申请
const orderManagement = () => import('@/pages/deliveryManagement/orderManagement/orderManagement.vue'); //订单管理
const paybackManagement = () => import('@/pages/deliveryManagement/paybackManagement/paybackManagement.vue'); //回款与开票

const routers = [
	{
		//合同管理
		path: '/contractManagement',
		name: 'contractManagement',
		component: contractManagement,
		meta: {
			parentTitle: '成交管理',
			title: '合同管理',
		},
	},
	{
		//交付管理
		path: '/deliveryManagement',
		name: 'deliveryManagement',
		component: deliveryManagement,
		meta: {
			parentTitle: '成交管理',
			title: '交付管理',
		},
	},
	{
		//延期申请
		path: '/extensionApply',
		name: 'extensionApply',
		component: extensionApply,
		meta: {
			parentTitle: '成交管理',
			title: '延期申请',
		},
	},
	{
		//订单管理
		path: '/orderManagement',
		name: 'orderManagement',
		component: orderManagement,
		meta: {
			parentTitle: '成交管理',
			title: '订单管理',
		},
	},
	{
		//回款与开票
		path: '/paybackManagement',
		name: 'paybackManagement',
		component: paybackManagement,
		meta: {
			parentTitle: '成交管理',
			title: '回款与开票',
		},
	},
];

export default routers;
