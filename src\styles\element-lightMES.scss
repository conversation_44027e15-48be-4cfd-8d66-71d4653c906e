/* 
	根据业务需求对全局的 elementUI 样式调整 
	每个样式都应有父组件包裹,如：
	.el-dialog {
		.el-dialog__header {}
		.el-dialog__body {}
		····
	}
	尽量不要单独分散设置，特殊情况如：customClass
	
	折叠所有区域代码的快捷键：ctrl+k, ctrl+0;
	展开所有折叠区域代码的快捷键：ctrl +k, ctrl+J;
*/
@use "sass:color";
@import './element-variables.scss';

/* 设置 notification/message 层级在 loading 之上 */
.el-message,
.el-notification {
	z-index: 99999 !important;
}
/*   customClass:"notice-save" 操作成功提示样式 */
.notice-save {
	align-items: center;
	i {
		font-size: 36px;
		width: 40px;
		height: 48px;
	}
	.icon-third_succ {
		color: $--color-primary-lighter;
	}
	.el-notification__group {
		h2 {
			font-size: 14px;
		}
		.el-notification__content {
			color: #999;
		}
	}
}
/*  customClass:"notice-err" 操作失败提示样式 */
.notice-err {
	align-items: center;
	i {
		color: $--color-danger-light;
		font-size: 36px;
		width: 40px;
		height: 48px;
	}
	.el-notification__group {
		.el-notification__title {
			display: none;
		}
		.el-notification__content {
			color: $--color-danger;
		}
	}
}

/* el-drawer 抽屉样式 */
.el-drawer {
	.el-drawer__header {
		background: #f5f5f5;
		padding: 10px;
		margin: 0;
	}
	.el-drawer__body {
		overflow: auto;
	}
}

/* el-autocomplete 文本框样式 */
.el-autocomplete-suggestion {
	//输入搜索,自适应宽度
	width: auto !important;
}

/* el-popover 文本框样式 */
.el-popover .el-popper {
	max-height: 800px !important;
	overflow: auto !important;
}

/* el-dialog 弹窗样式 */
.el-dialog {
	min-width: 300px;
	border-radius: 8px !important;
	.el-dialog__header {
		font-size: 16px;
		padding: px2vh(18) px2vw(15);
		color: #555;
		border-radius: 8px 8px 0 0 !important;
		box-sizing: border-box;
		background-color: #f5f5f5;
		border-bottom: 1px solid #e9e9e9;
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		.el-dialog__headerbtn {
			padding-top: 5px;
			position: static;
		}
	}

	.el-dialog__body {
		padding: 0 px2vw(30);
		//弹框内checkbox勾选框样式
		.el-checkbox {
			margin-right: 15px;
			.el-checkbox__inner {
				box-sizing: border-box;
				width: 20px;
				height: 18px;
				border: 2px solid $--color-primary-light !important;
				&::after {
					border-color: $--color-primary-light !important;
				}
			}
		}
		.el-checkbox .is-checked {
			.el-checkbox__inner {
				background-color: #fff !important;
			}
			.el-checkbox__inner::after {
				border-width: 2px;
				height: 8px;
				left: 6px;
			}
		}
		.el-checkbox__input + .el-checkbox__label {
			font-weight: 400;
			color: #555;
		}

		// 弹框内form样式
		.el-form {
			margin-top: 22px;
			.el-date-editor {
				width: 100%;
			}
		}

		// form 中存在两列 form-item
		.multiColumn-form {
			display: flex;
			flex-wrap: wrap;
			.el-form-item {
				width: 47%;
				&:nth-child(2n-1) {
					margin-right: 5%;
				}
			}
		}
	}

	/* slot="footer" 底部按钮*/
	.el-dialog__footer {
		padding: px2vh(12) px2vw(30);
		border-top: 1px solid #e9e9e9;
		// 默认button size = small
		.el-button {
			font-size: 14px;
			padding: 12px 18px;
			margin-left: px2vw(15);
		}
	}
}

/*  el-tabs样式 */
.el-tabs {
	.el-tabs__content {
		overflow: hidden;
		position: inherit; //当子组件使用滑动窗口组件时对整个tab绝对定位
	}

	.el-tabs__item {
		font-size: 16px;
		font-weight: 650;
		color: #5e5e5e;
	}

	.el-tabs__item:hover {
		color: #5e5e5e;
	}

	.el-tabs__item.is-active {
		color: $--color-primary;
	}

	.el-tabs__nav-wrap::after {
		background-color: transparent;
	}

	// 激活tab时底部的bar
	.el-tabs__active-bar {
		// width:28px !important;
		// position: relative;
		height: 6px;
		background: transparent;

		&:after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 35px;
			height: 6px;
			left: 50%;
			margin-left: -16.5px;
			// background: linear-gradient(270deg, rgb(7, 236, 228) 0%, rgb(15, 229, 207) 26%, rgb(40, 208, 148) 100%);
			// background: linear-gradient(270deg, $--tabs-active-bar 0%, $--tabs-active-bar-blod 25%, $--tabs-active-bar-bloder 100%);
			background: linear-gradient(
				270deg,
				color.scale($--color-primary, $lightness: 20%, $saturation: 20%) 0%,
				color.scale($--color-primary, $lightness: 15%, $saturation: 15%) 25%,
				color.scale($--color-primary, $lightness: 8%, $saturation: 8%) 100%
			);
			border: none;
			border-radius: 6px;
		}
	}

	// 禁用tab
	.el-tabs__item.is-disabled {
		cursor: not-allowed;
		color: #999 !important;
	}
}

/*  el-tag样式 */
.el-tag {
	cursor: pointer;
}

/* confirm 全局确认消息"*/
.el-message-box {
	margin-top: -30vh !important;
	border-radius: px2vw(8) !important;
	.el-message-box__header {
		padding: 15px;
		color: #555;
		border-radius: px2vw(8) px2vw(8) 0 0 !important;
		box-sizing: border-box;
		background-color: #f5f5f5;
		border-bottom: 1px solid #e9e9e9;
	}
	.el-message-box__content {
		min-height: 150px;
	}
	.el-message-box__btns {
		border-top: 1px solid #e9e9e9;
		padding: 10px 15px 0;

		.el-button {
			font-size: 14px;
			padding: 12px 18px;
			margin-left: px2vw(15);
			// &:first-child {
			//   display: none;
			// }
		}
	}
}
/* customClass="show-cancelButton" 显示取消按钮 */
// .show-cancelButton {
//   .el-message-box__btns {
//     .el-button {
//       &:first-child {
//         display: inline-block;
//         color: #ffffff;
//         background-color: #909399;
//         border-color: #909399;
//       }
//     }
//   }
// }
/* 下拉框最大高度 */
.el-select-dropdown__wrap {
	max-height: 50vh !important;
}
/* 联级选择器 */
.el-cascader-panel {
	// 隐藏 el-radio
	.el-radio {
		color: #606266;
		cursor: pointer;
		visibility: hidden !important; // 加上这一行
		margin-right: 0px;
		display: none;
	}
}

/* el-color-picker 颜色选择器禁用样式 */
.el-color-picker__mask {
	background-color: transparent;
}

/* el-backtop */
.el-backtop {
	div {
		font-size: 14px;
		text-align: center;
		line-height: 14px;
	}
}

/* 时间范围选择器 */
.el-date-editor {
	.el-range-separator {
		width: 10% !important;
	}
}

/* 表单必填字段的标签旁边的红色星号位置(*) */
.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
	display: none;
}
.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label::after {
	content: '*';
	color: #f56c6c;
	margin-left: 4px;
}

/* el-upload 上传组件的拖拽框样式 */
.upload-dragger {
	display: flex;
	align-items: center;
	.el-upload-dragger {
		width: fit-content;
		height: fit-content;
		padding: 5px;
	}
}

/* el-radio 单选按钮 size = mini 只在有边框时才生效故需单独定义 */
.mini-radio {
	display: flex !important;
  align-items: center;
  .el-radio {
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-right: 10px;
    font-weight: 400;
  }
  .el-radio__label {
    font-size: 12px;
  }
}
// 红色的radio
.red-radio {
  .is-checked {
    .el-radio__inner {
      border-color: #f56c6c !important;
      background: #f56c6c !important;
    }
    & + .el-radio__label {
      color: #f56c6c !important;
    }
  }
}

/* el-tree树结构指引线 */
.tree-guide-line {
  // 注意这里使用：绝对定位消除padding-left 16px 的影响 因此需要父元素使用相对定位relative
  position: absolute;
  left: -16px;
  width: calc(100% + 16px);
  .el-tree-node {
    position: relative;
    padding-left: 10px;
  }
  .el-tree-node__children {
    padding-left: 16px;
  }
  // 竖线
  .el-tree-node::before {
    content: '';
    height: 100%;
    width: 1px;
    position: absolute;
    left: -3px;
    top: -26px;
    border-width: 1px;
    border-left: 1px dashed #ccc;
  }

  .el-tree-node:last-child::before {
    height: 38px;
  }
  // 横线
  .el-tree-node::after {
    content: '';
    width: 18px;
    height: 20px;
    position: absolute;
    left: -3px;
    top: 12px;
    border-width: 1px;
    border-top: 1px dashed #ccc;
  }
  .el-tree {
    > .el-tree-node::after {
      // 仅 el-tree 的第一个 el-tree-node 不显示横线
      border-top: none;
    }
  }
  & > .el-tree-node::after {
    border-top: none;
  }
  & > .el-tree-node::before {
    border-left: none;
  }

  .el-tree-node__expand-icon {
    font-size: 16px;
    &.is-leaf {
      color: transparent; // 叶子节点不显示icon
    }
  }

  // 叶子延长横线
  .no-children {
    &::before {
      content: '';
      width: 12px;
      height: 0px;
      position: absolute;
      left: 16px;
      top: 12px;
      border-width: 1px;
      border-top: 1px dashed #ccc;
    }
  }
}