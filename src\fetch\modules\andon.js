/* 安灯相关API（安灯盒子、三色灯、手环） */

const urlList = [
	// 三色灯清单
	'/background/web/tricolourLightController/getTricolourLightList', //查询列表
	'/background/web/tricolourLightController/addTricolourLight', //添加
	'/background/web/tricolourLightController/updateTricolourLight', //修改
	'/background/web/tricolourLightController/batchDeleteTricolourLight', //删除/批量删除
	'/background/web/tricolourLightController/batchImport', //批量导入
	'/background/web/tricolourLightController/batchUpgradeTricolourLight', //批量升级
	'/background/web/tricolourLightController/selectTricolourLightParamBySim', //参数查询
	'/background/web/tricolourLightController/updateTricolourLightParam', //参数修改
	'/background/web/tricolourLightController/tricolourLightDownload', //导出
	'/background/web/tricolourLightController/printTricolourLight', //批量打印

	// 三色灯产品
	'/background/web/tricolourProduct/getTricolourProductList', //查询列表
	'/background/web/tricolourProduct/addTricolourProduct', //添加产品
	'/background/web/tricolourProduct/updateTricolourProduct', //修改产品
	'/background/web/tricolourProduct/deleteTricolourProduct', //删除产品
	'/background/web/tricolourProduct/getTricolourProductDetail', //产品详情
	'/background/web/tricolourProduct/getClassType', //三色灯类型

	// 固件版本
	'/background/web/tricolourLightChip/getClassTypeMenu', //查询芯片菜单列表
	'/background/web/tricolourLightChip/addChipType', //添加芯片
	'/background/web/tricolourLightChip/updateChipType', //修改芯片
	'/background/web/tricolourLightChip/deleteChipType', //删除芯片
	'/background/web/tricolourLightChip/getChipTypeAndFirmwareType', //查询芯片下所有固件
	'/background/web/tricolourLightChip/getFirmwareType', //查询固件详情
	'/background/web/tricolourLightChip/addFirmwareType', //添加固件
	'/background/web/tricolourLightChip/updateFirmwareType', //修改固件详情
	'/background/web/tricolourLightChip/deleteFirmwareType', //删除固件详情
	'/background/web/tricolourLightChip/uploadFirmware', //上传固件
	'/background/web/tricolourLightChip/getTricolourProductByClassType', //根据灯类型查产品列表
	'/background/web/tricolourLightChip/selectUpgradeFirmwareByProductId', //查询可升级固件
	'/background/web/tricolourLightChip/batchFirmwareUpgrade', //三色灯批量升级固件

	// 发货与退换
	'/background/web/invoiceController/selectInvoice', //查询发货单
	'/background/web/invoiceController/invoiceDownload', //导出发货单

	// 安灯盒子
	'/background/web/andonBoxController/andonBoxList', // 安灯盒子管理列表
	'/background/web/andonBoxController/addAndonBox', // 单个录入安灯盒子
	'/background/web/andonBoxController/inportAndonBoxList', // 批量导入安灯盒子
	'/background/web/andonBoxController/deleteAndonLights', // 删除安灯盒子
	'/background/web/andonBoxController/addAndonFirmwareType', // 添加安灯盒子固件
	'/background/web/andonBoxController/andonFirmwareUpgrade', // 安灯盒子固件版本升级
	'/background/web/andonBoxController/selectAndonFirmwareTypeVO', // 查询固件版本
	'/background/web/andonBoxController/uploadFirmware', // 上传固件,参数 file ，andonLightType 4G/wifi, chipName 芯片名称
	'/background/web/andonBoxController/updateAndonFirmwareType', // 修改安灯盒子固件
	'/background/web/andonBoxController/deleteAndonFirmwareType', // 删除安灯盒子固

	// 安灯手环
	'/watch/web/andonBraceletBoxController/selectBraceletBoxList', // 查询安灯手环盒子列表
	'/watch/web/andonBraceletBoxController/insertAndonBraceletBox', // 添加安灯手环盒子
	'/watch/web/andonBraceletBoxController/deleteAndonBraceletBox', // 删除安灯手环盒子
	'/watch/web/andonBraceletBoxController/updateAndonBraceletBox', // 修改安灯手环盒子
	'/watch/web/andonBraceletController/selectAndonBraceletList', // 查询安灯手环列表
	'/watch/web/andonBraceletController/updateAndonBracelet', // 修改安灯手环
	'/watch/web/andonBraceletController/insertAndonBracelet', // 添加安灯手环
	'/watch/web/andonBraceletController/deleteAndonBracelet', // 删除安灯手环
];

// 重名函数映射
const apiNameMap = {
	'/background/web/andonBoxController/uploadFirmware': 'uploadFirmwareBox',
};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['batchImport', 'uploadFirmware', 'tricolourLightDownload', 'invoiceDownload', 'getTricolourLightList'].includes(urlName)) {
		timeout = 60000;
	}

	return { urlName, url, timeout };
});

export default apiList;
