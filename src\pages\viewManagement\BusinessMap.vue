<template>
	<div class="BusinessMap flex-column W100 H100">
		<EChart :seriesData="[{}]" :extraOption="mapOption" @onClick="handleClick"> </EChart>
		<div class="map-button">
			<el-button v-show="mapList.length > 0" type="text" class="el-icon-arrow-left white p0 m0" @click="goBack"> 返回 </el-button>
		</div>
	</div>
</template>
<script>
import { EChart } from '@/components/Chart/index.js';
import { registerMap } from 'echarts';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'BusinessMap',
	components: { EChart },
	props: {},
	data() {
		return {
			areaDataMap: {}, // 业务数据
			mapOption: {}, // 地图配置
			mapList: [], // 记录地图层级
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		// 当前地区名称
		currArea() {
			return this.mapList[this.mapList.length - 1] || {};
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryBusinessData();
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		this.areaDataMap = null;
		this.mapOption = null;
		this.mapList = null;
	},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 查询业务数据
		async queryBusinessData() {
			const API = 'selectInquiryAllocationPerformanceIndicatorList';
			try {
				const res = await this.$axios[API](JSON.stringify({}));
				if (res.data.success) {
					// 创建区域名称到数据的映射
					const areaDataMap = {};
					// 处理业务数据，提取区域信息
					res.data.data?.forEach(item => {
						if (item.businessArea) {
							// 分割区域字符串，通常格式为"金华,浙江"
							const areas = item.businessArea.split(',');

							// 遍历每个区域名称
							areas.forEach(area => {
								// 如果该区域已存在，增加计数
								if (areaDataMap[area]) {
									areaDataMap[area].count += 1;
									areaDataMap[area].items.push(item);
								} else {
									// 否则创建新记录
									areaDataMap[area] = {
										count: 1,
										items: [item],
									};
								}
							});
						}
					});
					this.areaDataMap = areaDataMap;
					console.log('areaDataMap:', this.areaDataMap);
					this.renderMapEcharts('100000_full'); // 初始化绘制中国地图
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 使用线上API
		async getMapJson(mapName) {
			const url = `https://geo.datav.aliyun.com/areas_v3/bound/${mapName}.json`;
			const mapJson = await fetch(url).then(res => res.json());
			return mapJson;
		},
		// 更新地图配置options
		setOptions(mapName, mapData) {
			// 找出最大值并向上取整到2的倍数
			const maxValue = Math.max(...mapData.map(item => item.data), 2);
			const maxCount = Math.ceil(maxValue / 2) * 2; // 向上取整到2的倍数

			return {
				darkMode: true,
				// 标题
				title: {
					text: `树字MES业务地图（${new Date().getFullYear()}年 ${this.currArea.name || '全国'}）`,
					subtext: '数据来源业绩驱动器',
					// sublink: 'https://datav.aliyun.com/portal/school/atlas/area_selector',
					textStyle: {
						color: '#fff',
						fontSize: 24,
					},
					subtextStyle: {
						color: '#ccc',
						fontSize: 18,
					},
					top: '1%',
					left: '0',
				},
				// 鼠标悬浮提示
				tooltip: {
					show: true,
					backgroundColor: 'rgba(14, 60, 112,.8)',
					borderColor: 'rgba(38, 196, 163)',
					borderWidth: 1,
					textStyle: {
						color: '#fff',
					},
					formatter: function (params) {
						if (params && params.data) {
							const { adcode, name, data, businessItems } = params.data;

							// 基本信息
							const titleStyle = `font-weight:bold;border-bottom:1px solid #ccc; padding-bottom:5px;margin-bottom:5px;`;
							const title = `<div style="${titleStyle}">${name} ${data ? data + '人' : ''}</div>`;
							let tooltipContent = '';
							// 如果有业务项，显示详细信息
							if (businessItems && businessItems.length > 0) {
								const maxShowCount = 20;
								// 最多显示n个业务顾问
								const displayItems = businessItems?.slice(0, maxShowCount) || [];
								displayItems?.forEach(item => {
									const info = `${item.auname}`;
									tooltipContent += `<div>${info}</div>`;
								});

								// 如果有更多项，显示省略号
								if (businessItems.length > maxShowCount) {
									tooltipContent += `<div>... 等${businessItems.length - maxShowCount}人</div>`;
								}
							}

							return title + tooltipContent;
						}
					},
				},
				// 左下角的数据颜色条
				visualMap: {
					show: true,
					min: 0,
					max: maxCount,
					left: 'left',
					top: 'bottom',
					text: ['高', '低'], // 文本，默认为数值文本
					textStyle: {
						color: '#ccc',
					},
					calculable: true,
					seriesIndex: [0],
					inRange: {
						color: ['#596066', '#1e9d82'],
					},
				},
				// geo地图
				geo: {
					map: mapName,
					roam: true, //开启鼠标缩放和平移漫游。默认不开启
					select: false,
					// 图形上的文本标签，可用于说明图形的一些数据信息，比如值，名称等。
					selectedMode: 'single',
					label: {
						color: '#ccc',
						show: true,
					},
					emphasis: {
						itemStyle: {
							areaColor: '#1e9d6f',
							borderColor: '#389BB7',
							borderWidth: 0,
						},
						label: {
							color: '#fff',
							fontSize: 14,
						},
					},
				},
				series: [
					// 地图数据
					{
						type: 'map',
						map: mapName,
						roam: true,
						geoIndex: 0,
						select: false,
						data: mapData,
					},
					// 散点
					{
						name: '散点',
						type: 'scatter',
						coordinateSystem: 'geo',
						data: mapData.filter(item => item.data > 0), // 只显示有数据的点
						itemStyle: {
							color: '#4abd93',
						},
					},
					// 气泡点(无数据时不显示)
					{
						name: '点',
						type: 'scatter',
						coordinateSystem: 'geo',
						symbol: 'pin', //气泡
						symbolSize: function (val) {
							if (val) {
								return val[2] / 4 + 20;
							}
						},
						label: {
							show: true,
							formatter: function (params) {
								return params.data.data || 0;
							},
							color: '#fff',
							fontSize: 9,
						},
						itemStyle: {
							color: '#F62157', //标志颜色
						},
						zlevel: 6,
						data: mapData.filter(item => item.data > 0), // 只显示有数据的点
					},

					// 地图标点
					{
						name: 'Top 5',
						type: 'effectScatter',
						coordinateSystem: 'geo',
						data: mapData.map(item => {
							if (item.data > 0) return item;
						}),
						symbolSize: 10,
						showEffectOn: 'render',
						//涟漪特效
						rippleEffect: {
							brushType: 'stroke',
							// period: 2, //动画时间，值越小速度越快
							// scale: 3, //波纹圆环最大限制，值越大波纹越大
						},
						label: {
							formatter: '{b}',
							position: 'right',
							show: false,
						},
						itemStyle: {
							color: '#26c4a3',
							shadowBlur: 10,
							shadowColor: '#26c4a3',
						},
						zlevel: 1,
					},
				],
			};
		},
		// 渲染地图
		async renderMapEcharts(mapName) {
			const mapJson = await this.getMapJson(mapName);
			registerMap(mapName, mapJson); // 注册地图

			// 为地图生成数据
			const mapdata = mapJson.features.map(item => {
				const areaName = item.properties.name;
				const areaData = this.areaDataMap[areaName];

				// 如果有匹配的业务数据，使用实际数据，否则使用随机数据
				const data = areaData ? areaData.count : 0;

				const tempValue = item.properties.center ? [...item.properties.center, data] : item.properties.center;
				return {
					name: areaName,
					value: tempValue, // 中心点经纬度
					adcode: item.properties.adcode, // 区域编码
					level: item.properties.level, // 层级
					data, // 实际数据或模拟数据
					businessItems: areaData ? areaData.items : [], // 保存相关的业务项
				};
			});

			// 更新地图options
			this.mapOption = this.setOptions(mapName, mapdata);
		},

		// 点击下转
		handleClick(param) {
			// 只有点击地图才触发
			if (param.seriesType !== 'map') return;
			const { adcode, level } = param.data;
			const mapAdcode = level === 'district' ? adcode : adcode + '_full';
			// 防止最后一个层级被重复点击，返回上一级出错
			if (this.mapList[this.mapList.length - 1]?.adcode === mapAdcode) {
				return this.$message.warning('已经是最下层了');
			}
			// 每次下转都记录下地图的name，在返回的时候使用
			this.mapList.push(param.data);
			this.renderMapEcharts(mapAdcode);
		},

		// 点击返回上一级地图
		goBack() {
			const { adcode, level } = this.mapList[this.mapList.length - 2] || {};
			const mapAdcode = adcode ? (level === 'district' ? adcode : adcode + '_full') : '100000_full';
			this.mapList.pop();
			this.renderMapEcharts(mapAdcode);
		},
	},
};
</script>

<style lang="scss" scoped>
.BusinessMap {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	// 背景图
	background-image: url('@/assets/img/map-bg.webp');
	background-repeat: round;
	background-size: cover;

	.map-button {
		position: absolute;
		top: 10px;
		right: 20px;
	}
}
</style>
