/**
 * 路由主入口
 */
import Vue from 'vue';
import Router from 'vue-router';
import { routes } from './routes';
import { fixNavigationDuplication } from './error-handling';
import { setupBeforeEachGuard, setupAfterEachGuard } from './guards';

Vue.use(Router);

// 修复路由导航重复问题
fixNavigationDuplication();

// 创建路由实例
const router = new Router({
	linkActiveClass: 'linkActive',
	base: __dirname,
	routes
});

// 设置路由守卫
setupBeforeEachGuard(router);
setupAfterEachGuard(router);

export default router;
