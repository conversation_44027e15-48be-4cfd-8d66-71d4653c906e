<template>
	<div id="contractManagement">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 动态询盘详情：聚心城使用聚心城询盘详情，其他使用默认询盘详情 -->
		<component
			v-if="showMap.InquiryDetail"
			:is="isJuxinCity ? 'InquiryDetail_JXC' : 'InquiryDetail'"
			ref="InquiryDetail"
			:inquiryOptions="tableOptions"
			@openContract="openContract"
			@close="$refs.ContractTable.queryTableData()"
		/>
		<!-- 合同详情 -->
		<ContractDetailCom ref="ContractDetailCom" :contractOptions="tableOptions" @close="$refs.ContractTable.queryTableData()" />
		<!-- 业绩分配 -->
		<AllocationPlan ref="AllocationPlan" @close="$refs.ContractTable.queryTableData()" />
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="deal"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twidList = $event.twidList;
				$refs.ContractTable?.queryTableData('init');
				$refs.ContractTable?.queryUserByTwids();
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="合同管理" name="contractManagement">
				<ContractTable
					ref="ContractTable"
					:twidList="searchForm.twidList"
					:channelName="searchForm.channelName"
					@openContract="openContract"
					@openAllocation="openAllocation"
					@openInquiryDetail="openInquiryDetail"
					@getTableOptions="tableOptions = $event"
					@openExport="$refs.ExportTable.openExport($event)"
				/>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable';
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import ChannelSelect from '@/components/ChannelSelect.vue';
import ContractDetailCom from './contractDetailCom.vue';
import AllocationPlan from './allocationPlan.vue';
import ContractTable from './ContractTable.vue';
import InquiryDetail_JXC from '@/components/InquiryDetail_JXC/InquiryDetail.vue'; //询盘详情弹窗

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		InquiryDetail,
		InquiryDetail_JXC,
		ContractDetailCom,
		ChannelSelect,

		AllocationPlan,
		ContractTable,
	},
	name: 'contractManagement',
	data() {
		return {
			activeTab: 'contractManagement',

			searchForm: {
				twidList: [],
				channelName: [],
			},

			tableOptions: [], //表格数据用于组件里上下页切换
			showMap: {
				InquiryDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['isJuxinCity']), //是否聚心城人员
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 打开分配计划详情
		openAllocation(type, rowData) {
			this.$refs.AllocationPlan.showDetailCom(type, rowData);
		},
		// 打开合同详情
		openContract(rowData) {
			this.$refs.ContractDetailCom.showDetailCom(rowData);
		},
		// 打开询盘详情
		openInquiryDetail(type, row) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom(type, row);
			});
		},
		// 切换tab
		changeTab() {},
	},
};
</script>

<style lang="scss" scoped>
#contractManagement {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
