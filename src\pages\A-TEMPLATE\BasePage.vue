<template>
	<!-- 基础页面（主页面） -->
	<div class="BasePage">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 明细组件 -->
		<!-- <BaseDetail ref="BaseDetail" @close="queryTableData(1)" /> -->
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="基础页面" name="BasePage">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">期间</span>
						<DateSelect
							@change="
								searchForm.startTime = $event.startTime;
								searchForm.endTime = $event.endTime;
								queryTableData(1);
							"
						/>

						<!-- 带搜索历史的输入框 -->
						<SearchHistoryInput name="name" placeholder="姓名" v-model.trim="searchForm.name" @input="queryTableData(1)" />

						<!-- 操作按钮 -->
						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<!-- 导出按钮 -->
							<ExportBtn @trigger="openExport" />
							<el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:dialog-data="tableColumn"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							@header-dragend="headerDragend"
							@reset="updateColumn(tableColumnCopy)"
							@show-field="updateColumn"
							show-header-overflow="title"
							header-drag-style
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="({ colNo, colName, ...col }, index) in filteredCols"
								:key="colNo + index"
								:prop="colNo"
								:label="colName"
								:align="col.align"
								:width="col.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="{ row, column }">
									<!-- 日期 eg: 2025-07-29 08:47 -->
									<Tooltips
										v-if="colNo.includes('Time')"
										:cont-str="dateFormat(row[colNo], 'lineM')"
										:cont-width="column.realWidth"
									/>
									<!-- 日期 eg: 2025-07-29 -->
									<Tooltips
										v-else-if="colNo.includes('Date')"
										:cont-str="dateFormat(row[colNo], 'line')"
										:cont-width="column.realWidth"
									/>

									<!-- 状态/类型 -->
									<Tooltips
										v-else-if="colNo == 'sampleType'"
										:cont-str="['按比例', '按批量'][row[colNo]]"
										:cont-width="column.realWidth"
									/>

									<!-- 默认显示 -->
									<Tooltips v-else :cont-str="row[colNo]" :cont-width="column.realWidth" />
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="{ row, $index }">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDetail('编辑',row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(row,$index)"></el-button>
                </template>
              </u-table-column> -->
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="tab页面" name="TabPage">
				<TabPage ref="TabPage" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等） eg：import 《组件名称》 from '《组件路径》'
import { deepClone, debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import ExportTable from '@/components/ExportTable'; //导出组件
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
// import BaseDetail from './BaseDetail.vue'; //明细组件
// import btnAuth from '@/mixins/btnAuth';
import TabPage from './TabPage.vue'; //tab页面

export default {
	name: 'BasePage', //组件名应同路由名(否则keep-alive不生效)
	// 引入组件
	components: {
		ExportTable,
		ExportBtn,
		DateSelect,
		// BaseDetail,
		TabPage,
	},
	// mixins: [btnAuth], 按钮权限
	data() {
		return {
			activeTab: 'BasePage', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [], //表格数据
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '编号', colNo: 'xxxNo', align: 'left', width: '' },
				{ colName: '名称', colNo: 'xxxName', align: 'left', width: '' },
				{ colName: '类型', colNo: 'xxxType', align: 'center', width: '' },
				{ colName: '数量', colNo: 'qty', align: 'right', width: '' },
				{ colName: '最近修改时间', colNo: 'xxxTime', align: 'center', width: '' },
				{ colName: '修改人', colNo: 'userName', align: 'left', width: '' },
			],
			tableColumnCopy: [],

			// 选择器数据 - 车间/部门/类型等...
			didOptions: [],
			// 查询表单
			searchForm: {
				did: '',
				query: '',
				// 其他...
			},
		};
	},
	// 过滤器 用于处理模板中的文本转换和格式化，如 {{ value | filterName }}
	// filters: {},
	// 计算属性 - 用于处理数据逻辑并缓存结果，当依赖变化时自动更新，避免在模板中放置复杂逻辑，与监听器不同，计算属性更适合处理简单的数据转换和格式化
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 缓存过滤后的列配置避免重复计算
		filteredCols() {
			return this.tableColumn.filter(col => col.state);
		},
	},
	// 监听 - 监听数据变化并执行相应操作，与计算属性不同，监听器更适合执行异步操作或开销较大的操作，当数据变化时触发
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.tableColumnCopy = deepClone(this.tableColumn);
		this.tableColumn = getColumn(this.tableColumn, this.$options.name);
		this.queryTableData();
	},
	activated() {
		this.queryTableData(1);
		// this.changeTab();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合 - 包含所有业务逻辑和功能实现
	methods: {
		// 打开明细
		openDetail(type, row) {
			this.$refs.BaseDetail.showDetailCom(type, row);
		},

		// 切换tab
		changeTab() {
			if (this.activeTab == 'BasePage') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 查询表格数据
		queryTableData: debounce(async function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectInquiryDocumentaryList'; //接口
			try {
				const res = await this.$axios[API](JSON.stringify(this.searchForm));
				if (res.data.success) {
					this.tableData = res.data.data;
					this.tablePageForm.total = res.data.totalItems;
					type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					this.sortChange(this.tableSort, true);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 展示字段
		updateColumn(data) {
			this.tableColumn = updateColumn(this.$options.name, data);
			this.sortChange(this.tableSort, true);
		},
		// 列宽拖动
		headerDragend(newWidth, oldWidth, column) {
			updateColumnWidth(this.$options.name, column.property, newWidth, this.tableColumn);
		},

		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({ ...this.searchForm }), //接口参数
				API: 'xxxxxDownload', //导出接口
				downloadData: 'xxxxx明细', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<!-- 样式 scoped 表示样式只在本组件中生效 -->
<style lang="scss" scoped>
.BasePage {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
