# 树字工厂OPS后台管理系统编码规范说明书（复制版）

## 文件组织 📔
### 文件命名 
> 文件名应该清晰、简洁，并且能够反映文件的功能。

  - pages：创建pages下的文件夹时，按照MENU.js即菜单信息，按照小驼峰命名，当该菜单的页面有很多tab时应单独为一个文件夹如iqcManagement里的CheckSetting，pages保持与项目菜单统一结构。
  - more...
### 目录结构
> 根据功能或模块将文件组织到不同的目录中，以提高代码的可维护性。

  - components文件夹：
      - 公共/通用组件，按照大驼峰命名放在src下的components文件夹中，非公共/通用组件，放在当前的components文件夹中。
      - 当前文件夹里的components，是用于放置该菜单的组件如详情组件等，并以其调用的父组件名前缀xxxDetail命名（大驼峰）便于快速定位查找，父组件太长时可适当简写。
  - api文件夹：
      - 接口文件按照MENU.js即菜单信息，按照小驼峰命名，添加接口时应按照页面、Controller进行归类，而不是随意添加在下面。
      - 添加接口时需检查调用名（apiName）是否有重复，如有重名的apiName需根据情况修改apiName，以防调错接口。
      ``` js
        // 如batchDelete为同名apiName 修改时建议是在尾部接上该Controller/组件/功能的前缀
          if (apiName == 'batchDelete' && controller == 'canningController') {
              apiName = 'batchDeleteCanning';
          }
      ```
  - routeList文件夹：路由文件按照MENU.js即菜单信息，按照小驼峰命名，添加路由时应按照页面顺序进行添加并且填写对应的meta的信息。
  - more...

## 代码风格 🧑‍💻
### 代码检测和格式化
  - 代码检测：开发时必须开启ESLint代码检测工具。
  - 代码格式化：开发时必须开启Prettier代码格式化工具。
### 代码模板和注释
  - 组件模板：A-TEMPLATE文件夹下为常见组件模板，大部分页面应参考该模板代码的风格和使用习惯，以此做到统一风格。
  - 代码片段：项目中的vue.code-snippets文件，已预设了很多Element UI的代码片段，它将会给你带来很多便捷。
  - 注释：
      - 当某段代码量足够多时应该注释其每小段的步骤意图和功能，尽量清晰、简洁。
      - tool.js里的工具函数应填写详细的注释包括：概述其功能、入参数据类型+数据描述、返回的数据类型、步骤意图和功能。
### 组件单一原则
  - 所有的组件文件应遵循单一原则，即组件应为单一功能，不应融入太多其他业务。
  - 非特殊情况，若文件代码行超过500行时应考虑抽离部分的逻辑来维护。
### 代码简洁之道
  - 完成业务需求后应该将页面重审一边，将复制时带过来的无用代码如没有用到的变量、函数、样式等删除（非特殊情况）。
  - 强烈安利阅读一遍下面的代码简洁之道（GitHub 87K+ starts 23/12/08）。
### 变量和函数名命名规范
> 变量和函数名使用小驼峰命名法，类名和组件命名使用大驼峰命名法。

  - 除特殊情况，禁止使用拼音，英文首字母简写或其他不明确表述等命名变量和函数。
  - let/const：变量创建使用let小驼峰命名，常量创建使用const创建并单词使用大写命名。
  - query/get: 获取请求接口数据的函数使用：queryXXX 命名 , getXXX 用于获取非接口的数据。
  - open/close：按钮打开/关闭dialog弹窗或者滑动的组件时使用，如openDialog、openDetail、openExport 等。
  - add/delete/close：分别为添加数据/删除数据/关闭（结案）数据，在单个组件下没有其他的同样操作时，不需写明操作的是xx的数据如（添加产品 addProduct），应使用通用的写法addRow/addData等
  - save：保存按钮命名为 saveEdit/saveData/saveDetial等。在单个组件下没有其他的保存操作时，不需要写明保存的是xx的数据。
### Vue编码规范
  - 一般情况，组件初始时获取异步获取数据时应放在created或者mounted这两个生命周期。
  - v-for和v-if不应该写在同一个标签，需过滤数据时可使用computed替代。
  ``` js
    computed: {
		filteredData() {
			return this.tableData.filter(item => !item.disable);
		},
	},
  ```
### 样式编写规范
  - 禁止在html代码中使用行内样式编写style，优先考虑使用我们自己制定的全局class变量,类似于tailwind，css原子类在class上写样式。
  - 在使用动态样式时，应优先考虑使用动态class，如根据状态变化颜色 :class="['green', 'red', 'orange'][scope.row.status] 这样会更直观
  - 选择器命名: id (大驼峰) class (小驼峰 "-"拼接) ，可参考BEM架构(BEM是指block、element、modifier的缩写，分别为块层、元素层、修饰符层，element UI 也使用的是这种架构)。
  ``` css
  <style>
    .block {}
    
    .block__element {}
    
    .block--modifier {}
  </style>
  ```
  - 样式代码编写尽量不嵌套太多层（5层后可读性较差，迁移时成本也比较高）。
  more...
## 版本控制 📄
  - Git使用：使用Git进行版本控制，并遵循分支管理策略。
  - Commit规范：提交信息应该清晰、简洁，并能够反映提交的内容。
  - more...

## 其他学习链接 📚
[代码简洁之道 | JavaScript代码简洁之道 (https://github.com/ryanmcdermott)](https://github.com/ryanmcdermott/clean-code-javascript)
[代码简洁之道 | JavaScript代码简洁之道(中文) (https://github.com/beginor)](https://github.com/beginor/clean-code-javascript)

[Vue风格指南 | Vue2官方文档(v2.cn.vuejs.org/)](https://v2.cn.vuejs.org/v2/style-guide/index.html)

[高效开发 | Vue3 入门指南与实战案例 (chengpeiquan.com)](https://vue3.chengpeiquan.com/efficient.html)

more...