<template>
	<div id="deliverySchedule" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<BaseLayout>
			<template #header>
				<!-- 模糊查询 -->
				<el-input
					class="searchBox ml20"
					size="small"
					v-model.trim="searchForm.implementName"
					placeholder="请输入实施顾问名称"
					@input="queryTableData(1)"
					clearable
				></el-input>

				<el-checkbox v-model="searchForm.myImplementName" :true-label="1" :false-label="0" @change="queryTableData(1)">
					只看我的
				</el-checkbox>
				<el-checkbox v-model="searchForm.isNull" :true-label="1" :false-label="0" @change="queryTableData(1)">
					不显示空行
				</el-checkbox>

				<span v-for="(item, index) in statuss" :key="index" :class="[item.color]">
					<span class="fs-20 ml20">●</span>
					<span>{{ item.status }}</span>
				</span>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<el-button type="text" class="el-icon-time" @click="changeWeek('back')"> 返回当前周</el-button>
					<el-button type="text" @click="changeWeek('reduce')">
						<i class="el-icon-arrow-left m0 p0"><span>上一周</span></i>
					</el-button>
					<el-button type="text" @click="changeWeek('add')">
						<span>下一周 <i class="el-icon-arrow-right"></i></span>
					</el-button>

					<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
				</div>
			</template>
			<template #main>
				<div class="table-wrapper">
					<table class="table-main-daily" cellpadding="5" cellspacing="0">
						<!-- 表格标题 -->
						<tr class="sticky-top">
							<th class="w-50 ellipsis sticky-column text-center">序号</th>
							<th class="min-w-50 ellipsis sticky-column">实施顾问</th>
							<th
								class="th-mini w-50 ellipsis"
								:class="{ 'border-right-red': item.isToday, 'color-999': item.isWeekend }"
								v-for="item in weekList"
								:key="item.date"
							>
								<div class="th-date"> {{ item.date }}</div>
								<div class="th-week"> {{ item.week }}</div>
							</th>
						</tr>
						<!-- 表格内容 -->
						<tr class="table-content" v-for="(aItem, index) in tableData" :key="index">
							<td class="text-center">{{ index + 1 }}</td>
							<td class="ellipsis sticky-column">{{ aItem.implementName }}</td>
							<td
								v-for="bItem in getScheduleData(aItem)"
								:key="bItem.timeStamp"
								:class="{ 'border-right-red': bItem.isToday, 'bg-color-fafafa': bItem.isWeekend }"
							>
								<Tooltips
									class="max-w-50 hover-green"
									:class="clolorMap[citem.status]"
									v-for="(citem, cIndex) in bItem.schedules"
									:key="cIndex"
									:cont-str="jointString(' | ', citem.projectName, citem.stage)"
									:cont-width="50"
									@click.native="openDetail(citem, '交付详情')"
								/>
							</td>
						</tr>
					</table>
				</div>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import 'moment/locale/zh-cn';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
export default {
	// import引入的组件需要注入到对象中才能使用
	props: {
		salesmanList: Array,
		twidList: Array,
		channelName: Array,
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	components: {},
	name: 'deliverySchedule',
	data() {
		return {
			weekList: [],
			tableData: [],
			startTime: this.$moment().startOf('day').valueOf(),
			clolorMap: {
				1: '',
				2: 'orange',
				3: 'red',
				4: 'green',
			},
			statuss: [
				{ id: 1, status: '待开始', color: '' },
				{ id: 2, status: '正在进行', color: 'orange' },
				{ id: 3, status: '已延误', color: 'red' },
				{ id: 4, status: '已完成', color: 'green' },
			],
			searchForm: {
				implementName: '',
				isNull: 0, //不显示空行,0显示空行,1不显示空行，
				myImplementName: 0, //只看自己的 0不是,1只看自己
			},

			openMove: false, //打开组件
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		channelName() {
			this.queryTableData(1);
		},
		twidList() {
			this.queryTableData();
		},
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.searchForm = JSON.parse(window?.localStorage.getItem(this.$options.name + '_searchForm')) || this.searchForm;
		this.getWeekList();
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 查询详情数据
		openDetail(row) {
			this.$emit('openDetail', row);
		},
		// 获取计划数据
		getScheduleData({ deliveryScheduleTimeInfoVOS }) {
			const scheduleMap = {};
			deliveryScheduleTimeInfoVOS?.forEach(aitem => {
				aitem.timestamp?.forEach(timeStamp => {
					// 如果scheduleMap 没有这个时间戳，就初始化一个数组并插入数据
					if (!scheduleMap[timeStamp]) {
						scheduleMap[timeStamp] = [{ ...aitem }];
					} else {
						scheduleMap[timeStamp].push({ ...aitem });
					}
				});
			});

			const scheduleData = Array.from({ length: this.weekList.length }, (v, i) => {
				const { timeStamp, isWeekend, isToday } = this.weekList[i];
				const schedules = scheduleMap[timeStamp] || [];
				return {
					timeStamp,
					isWeekend,
					isToday,
					schedules,
				};
			});
			return scheduleData;
		},

		// 切换周次
		changeWeek: _.debounce(function (type) {
			if (type == 'add') {
				this.startTime = this.$moment(this.startTime).add(1, 'weeks').startOf('day').valueOf();
			} else if (type == 'reduce') {
				this.startTime = this.$moment(this.startTime).subtract(1, 'weeks').startOf('day').valueOf();
			} else {
				this.startTime = this.$moment().startOf('day').valueOf();
			}

			this.getWeekList();
			this.queryTableData();
		}),
		// 获取(传入的日期)本周到未来3周的日期 1+3
		getWeekList() {
			const todayTime = this.$moment().startOf('day').valueOf();
			const startOfWeek = this.$moment(this.startTime).startOf('isoWeek'); //本周起始日期（周一）
			const weekList = [];

			for (let i = 0; i < 4; i++) {
				const weekStart = startOfWeek.clone().add(i, 'weeks');
				const weekEnd = weekStart.clone().endOf('isoWeek');

				for (let day = weekStart.clone(); day.isSameOrBefore(weekEnd); day.add(1, 'days')) {
					weekList.push({
						date: day.format('MM/DD'), //日期
						week: day.locale('zh-cn').format('dddd'), //星期几
						timeStamp: day.valueOf(),
						isToday: day.isSame(todayTime, 'day') && day.isSame(todayTime, 'week'), //今天
						isWeekend: day.isoWeekday() === 7, //周日
					});
				}
			}

			this.weekList = weekList;
		},
		// 跟单日报数据
		queryTableData: _.debounce(function (type) {
			window?.localStorage.setItem(this.$options.name + '_searchForm', JSON.stringify(this.searchForm));
			const str = JSON.stringify({
				endTime: this.weekList[this.weekList.length - 1].timeStamp + 86400000 - 1,
				startTime: this.weekList[0].timeStamp,
				twidList: this.twidList,
				channelName: this.channelName,
				...this.searchForm,
			});

			this.$axios
				.webDeliveryScheduleTime(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('webDeliveryScheduleTime |' + error);
				});
		}),

		dateFormat: _.dateFormat, //日期format
		jointString: _.jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
#deliverySchedule {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	.bg-color-fafafa {
		background-color: #fafafa;
	}
	.border-right-red {
		border-right: 1px dashed #ffcdd2 !important;
	}

	.table-wrapper {
		.table-card {
			height: calc(100vh - 250px);
		}
	}

	.table-main-daily {
		width: 100%;
		color: #606266;
		border-left: 1px solid #e9e9e9;
		border-bottom: 1px solid #e9e9e9;

		.sticky-top {
			position: sticky;
			top: -10px;
			z-index: 100;
		}
		.sticky-column {
			position: sticky;
			left: 0;
			z-index: 99;
		}

		tr {
			th {
				font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
				font-weight: 650;
				font-size: 14px;
				color: #666666;
				background: #f5f5f5;
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				word-wrap: break-word;
				padding: 3px;
				margin: 0;
			}

			td {
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				font-size: 12px;
				vertical-align: top;
				padding: 3px;
				// max-width: 5vw;
				width: 5%;
			}

			.th-mini {
				font-size: 12px;
				font-weight: 400;
				padding: 0;
				.th-week {
					border-top: 1px solid #e9e9e9;
				}
			}
		}

		// 斑马纹效果
		.table-content {
			color: #606266;
			z-index: 1;

			&:nth-of-type(odd) {
				background: #f2f2f2 !important;
			}
			&:nth-of-type(even) {
				background: #fff !important;
			}
		}
	}
}
</style>
