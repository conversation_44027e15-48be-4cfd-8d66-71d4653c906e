<template>
	<div id="inquiryTrend" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 询盘清单 -->

		<InquriyList
			ref="InquriyList"
			:titleName="titleName"
			:twidList="twidList"
			:channelName="channelName"
			parentComName="询盘趋势"
			@refresh="changeDateSelect(selectTime)"
		/>

		<BaseLayout>
			<template #header>
				<span class="search-label">年度</span>
				<el-date-picker
					size="small"
					class="w-120"
					v-model="selectTime"
					:default-value="selectTime"
					type="year"
					value-format="timestamp"
					format="yyyy 年"
					placeholder="请选择年份"
					:clearable="false"
					@change="changeDateSelect"
				>
				</el-date-picker>

				<el-radio-group v-model="searchForm.viewList" @change="queryTableData(1)">
					<el-radio :label="1">来源视图</el-radio>
					<el-radio :label="2">星级视图</el-radio>
					<el-radio :label="3">区域视图</el-radio>
					<el-radio :label="4">产品视图</el-radio>
				</el-radio-group>

				<span class="search-label">来源</span>
				<el-select
					size="small"
					class="w-200"
					multiple
					collapse-tags
					v-model="searchForm.channelList"
					placeholder="来源"
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<!-- <el-select
					class="w-100 "
					size="small"
					v-model="searchForm.rePurchase"
					placeholder="客户类型"
					@change="queryTableData(1)"
				>
					<el-option label="新客户" :value="0"> </el-option>
					<el-option label="复购" :value="1"> </el-option>
					<el-option label="全部" :value="2"> </el-option>
				</el-select> -->
				<el-select class="w-100" size="small" v-model="searchForm.rePurchase" placeholder="客户类型" @change="queryTableData(1)">
					<!-- 0:新客户,1:复购，2：全部 -->
					<el-option label="全部客户" :value="2"> </el-option>
					<el-option label="新客户" :value="0"> </el-option>
					<el-option label="复购" :value="1"> </el-option>
				</el-select>
				<el-checkbox v-model="searchForm.quality" :true-label="1" :false-label="0" @change="queryTableData(1)"
					>包含无效询盘
				</el-checkbox>
				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>

				<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
			</template>
			<template #main>
				<div class="table-toolbar">
					<ExportBtn v-if="isSuperAdmin" @trigger="openExport" />
				</div>
				<!-- :pagination-show="true" -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:data="tableData"
					@sort-change="sortChange"
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column label="序号" width="50" align="center" type="index"> </u-table-column>

					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						resizable
						sortable="custom"
					>
						<template slot-scope="scope">
							<div v-if="item.colNo != 'channel' && isSuperAdmin">
								<Tooltips
									@click.native="openDetail(scope.row, item.colNo)"
									class="hover-green"
									:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
									:cont-width="(scope.column.width || scope.column.realWidth) - 18"
								>
								</Tooltips>
							</div>
							<div v-else-if="item.colNo == 'channel'">
								<!-- <span>{{ sourceMap[scope.row[item.colNo]] }}</span> -->
								<span>{{ getRowStr(scope.row[item.colNo]) }}</span>
							</div>

							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { debounce, deepClone, sortTableData } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable'; //导出组件
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
import InquriyList from '@/components/InquriyList.vue';
import { sourceList, sourceMap, qualityMap } from '@/assets/js/inquirySource.js';
export default {
	props: {
		salesmanList: Array,
		twidList: Array,
		channelName: Array,
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		InquriyList,
		ExportBtn,
	},
	name: 'inquiryTrend',
	data() {
		return {
			activeTab: 'inquiryTrend',
			searchForm: {
				channelList: [], //来源
				viewList: 1, //1:来源视图,2:星级视图，3：区域视图，4：产品视图
				rePurchase: 2, //0:新客户,1:复购，2：全部
				quality: 1, //无效询盘质量1：是,0：否
				channel: [],
				qualityList: [],
			},
			titleName: '',
			channel: [],
			//日期相关
			selectTime: new Date(),
			startTime: '',
			endTime: '',
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			qualityMap, // 询盘质量
			sourceMap, // 来源
			sourceList, // 来源列表
			tableColumn: [
				{ colName: '来源', colNo: 'channel', align: 'left' },
				{ colName: '一月', colNo: '0', align: 'right' },
				{ colName: '二月', colNo: '1', align: 'right' },
				{ colName: '三月', colNo: '2', align: 'right' },
				{ colName: '四月', colNo: '3', align: 'right' },
				{ colName: '五月', colNo: '4', align: 'right' },
				{ colName: '六月', colNo: '5', align: 'right' },
				{ colName: '七月', colNo: '6', align: 'right' },
				{ colName: '八月', colNo: '7', align: 'right' },
				{ colName: '九月', colNo: '8', align: 'right' },
				{ colName: '十月', colNo: '9', align: 'right' },
				{ colName: '十一月', colNo: '10', align: 'right' },
				{ colName: '十二月', colNo: '11', align: 'right' },
				{ colName: '合计', colNo: '12', align: 'right' },
			],

			openMove: false, //打开组件
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）

		// filteredData() {
		// 	return this.tableData.sort((a, b) => b['12'] - a['12']);
		// }
		// 是超级管理
		isSuperAdmin() {
			return this.userInfos?.adminUserVO.adminRoleVOS.some(item => item.arid == 1);
		},
	},
	// 监控data中的数据变化
	watch: {
		twidList() {
			this.queryTableData();
		},
		channelName() {
			this.queryTableData();
		},

		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.changeDateSelect(new Date(new Date().getFullYear(), '0', '1').getTime());
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		summaryMethod({ columns, data }) {
			const means = []; // 合计
			columns.forEach((column, columnIndex) => {
				if (columnIndex === 0) {
					means.push('合计');
				} else if (columnIndex === 1) {
					means.push('');
				} else {
					const values = data.map(item => Number(item.channelList[column.property]));
					// 合计
					if (!values.every(value => isNaN(value))) {
						means[columnIndex] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return this.accAdd(prev, curr);
							} else {
								return prev;
							}
						}, 0);
						if (means[columnIndex] > 0) {
							if (this.isSuperAdmin) {
								means[columnIndex] = 
									<span
										class="hover-green "
										on-click={e => {
											e.stopPropagation();
											this.openDetail('', columnIndex);
										}}
									>
										{means[columnIndex]}
									</span>
								;
							}
						} else {
							means[columnIndex] = '';
						}
					} else {
						means[columnIndex] = '';
					}
				}
			});
			// 返回一个二维数组的表尾合计(不要平均值，你就不要在数组中添加)
			return [means];
		},
		accAdd(arg1, arg2) {
			let r1, r2, m;
			try {
				r1 = arg1.toString().split('.')[1].length;
			} catch (e) {
				r1 = 0;
			}
			try {
				r2 = arg2.toString().split('.')[1].length;
			} catch (e) {
				r2 = 0;
			}
			m = Math.pow(10, Math.max(r1, r2));
			m *= 10;
			return parseInt(arg1 * m + arg2 * m) / m;
		},

		//自定义排序(非虚拟表格 )
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = sortTableData(this.tableData, prop, order);
				this.tableData = [...sortedData];
			} else {
				this.tableData = [...this.tableDataCopy];
			}
		},
		// 时间选择
		changeDateSelect(date) {
			const year = new Date(date).getFullYear();
			this.startTime = new Date(year, '0', '1').getTime();
			this.endTime = new Date(year + 1, '0', '1').getTime() - 1;
			// console.log(date);
			this.queryTableData();
		},
		// 打开该询盘
		openDetail(row, months) {
			if (row) {
				this.titleName = this.sourceMap[row.channel];
				this.titleName = this.getRowStr(row.channel);
				// 选择点击某月份时
				if (months < 12) {
					const year = new Date(this.startTime).getFullYear();
					const month = Number(months) + 1;
					const firstDay = new Date(year, month - 1, 1).getTime();
					const lastDay = new Date(year, month, 1).getTime() - 1;
					this.startTime = firstDay;
					this.endTime = lastDay;
				} else {
					this.titleName = `${new Date(this.startTime).getFullYear()}年`;
					this.changeDateSelect(this.startTime);
				}
			} else {
				months = months - 1;
				this.titleName = new Date(this.startTime).getFullYear() + '年' + months + '月';
				if (months <= 12) {
					const year = new Date(this.startTime).getFullYear();
					const firstDay = new Date(year, months - 1, 1).getTime();
					const lastDay = new Date(year, months, 1).getTime() - 1;
					this.startTime = firstDay;
					this.endTime = lastDay;
				} else {
					this.changeDateSelect(this.startTime);
				}
			}
			setTimeout(() => {
				const StrMap = {
					1: 'channel', //'来源'
					2: 'qualityList', //星级
					3: 'region', //区域
					4: 'keyword', //产品
				};
				Object.values(StrMap).forEach(item => {
					this.searchForm[item] = '';
				});
				//来源和星级特殊处理
				if (this.searchForm.viewList === 1) {
					this.searchForm.channel = row ? [row.channel] : [];
					this.searchForm.qualityList = this.searchForm.quality == 1 ? [] : [0, 1, 2, 3, 4];
				} else if (this.searchForm.viewList === 2) {
					const qualityList = this.searchForm.quality == 1 ? [] : [0, 1, 2, 3, 4];
					this.searchForm.qualityList = row ? [row.channel] : qualityList;
				} else {
					this.searchForm[StrMap[this.searchForm.viewList]] = row.channel;
					this.searchForm.qualityList = this.searchForm.quality == 1 ? [] : [0, 1, 2, 3, 4];
				}
				const DATA = {
					...this.searchForm,
					startTime: this.startTime,
					endTime: this.endTime,
				};
				this.$refs.InquriyList.openList(DATA);
			}, 300);
		},
		// 根据不同类型获取列名
		getRowStr(item) {
			const rowMap = {
				1: this.sourceMap[item],
				2: this.qualityMap[item],
				3: item,
				4: item,
			};
			return rowMap[this.searchForm.viewList];
		},
		// 获取询盘趋势数据
		queryTableData: debounce(function (type) {
			const colNmaeMap = {
				1: '来源',
				2: '星级',
				3: '区域',
				4: '产品',
			};
			this.tableColumn[0].colName = colNmaeMap[this.searchForm.viewList];
			const str = JSON.stringify({
				endDate: this.endTime,
				startDate: this.startTime,
				twidList: this.twidList,
				channelName: this.channelName,
				channelList: this.searchForm.channelList,
				viewList: this.searchForm.viewList,
				rePurchase: this.searchForm.rePurchase,
				quality: this.searchForm.quality,
				qualityList: this.searchForm.quality == 1 ? [] : [0, 1, 2, 3, 4],
			});
			this.$axios
				.selectTrend(str)
				.then(res => {
					if (res.data.success) {
						// 将channelList放到数据里
						res.data.data.forEach(item => {
							item.channelList.map((cItem, cIndex) => {
								item[cIndex] = cItem;
							});
						});
						this.tableData = res.data.data.sort((a, b) => b['12'] - a['12']);
						this.tableDataCopy = deepClone(this.tableData);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开

						if (this.tableSort) {
							this.sortChange(this.tableSort);
						}
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTrend |' + error);
				});
		}),
		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					endDate: this.endTime,
					startDate: this.startTime,
					twidList: this.twidList,
					channelName: this.channelName,
					channelList: this.searchForm.channelList,
					viewList: this.searchForm.viewList,
					rePurchase: this.searchForm.rePurchase,
					quality: this.searchForm.quality,
					qualityList: this.searchForm.quality == 1 ? [] : [0, 1, 2, 3, 4],
				}), //接口参数
				API: 'exportTrend', //导出接口
				downloadData: '询盘趋势导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
#inquiryTrend {
	width: 100%;
	overflow: hidden;
	position: relative;
	&.moveToggle {
		.table-main {
			height: calc(100vh - 245px) !important;
		}
	}
}
</style>
