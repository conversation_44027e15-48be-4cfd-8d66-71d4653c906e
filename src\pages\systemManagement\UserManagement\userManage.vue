<template>
	<div id="userManage">
		<!-- 导入弹窗 -->
		<ImportTable ref="ImportTable" @refresh="queryTableData(1)" />

		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="用户管理" name="userManage">
				<BaseLayout>
					<template #header>
						<el-checkbox-group v-model="searchForm.statusList" @change="queryTableData(1)">
							<el-checkbox label="1">启用</el-checkbox>
							<el-checkbox label="0">禁用</el-checkbox>
						</el-checkbox-group>

						<!-- 模糊查询 -->
						<SearchHistoryInput
							name="company_name"
							placeholder="公司/姓名"
							v-model.trim="searchForm.query"
							@input="queryTableData(1)"
						/>

						<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
						<!-- 导入按钮 -->
						<ImportBtn @trigger="openImport" />
					</template>
					<template #main>
						<div class="table-toolbar">
							<el-button type="text" class="icon-third-bt_addman" @click="openDialogAdd">添加</el-button>
							<el-button type="text" class="icon-third_searchC" v-popover:thSearch>查找</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" type="index" width="60" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								:sortable="item.sortable ? 'custom' : false"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="item.colNo == 'createTime'"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:class="{ red: scope.row.status == 0 }"
										:cont-str="statusMap[scope.row.status]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 角色 -->
									<Tooltips
										v-else-if="item.colNo == 'adminRoleVOS'"
										class="hover-green"
										@click.native="openRoleSet(scope.row)"
										:cont-str="getRolesStr(scope.row.adminRoleVOS) ? getRolesStr(scope.row.adminRoleVOS) : '未设置'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 用户标签 -->
									<Tooltips
										v-else-if="item.colNo == 'adminUserLabelVOS'"
										class="hover-green"
										@click.native="openDialogEdit(scope.row)"
										:cont-str="getLabelStr(scope.row.adminUserLabelVOS) ? getLabelStr(scope.row.adminUserLabelVOS) : '未设置'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 部门 -->
									<Tooltips
										v-else-if="item.colNo == 'adminDepartmentVO'"
										class="hover-green"
										@click.native="openDialogEdit(scope.row, scope.$index)"
										:cont-str="scope.row.adminDepartmentVO ? scope.row.adminDepartmentVO.fullDepartmentName : '未设置'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 渠道 -->
									<Tooltips
										v-else-if="item.colNo == 'channelName' && scope.row.channelName"
										class="hover-green"
										@click.native="openDialogEdit(scope.row, scope.$index)"
										:cont-str="scope.row.channelName.length > 0 ? scope.row.channelName?.join(',') : '未设置'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 分销/代理 -->
									<Tooltips
										v-else-if="item.colNo == 'teamwork'"
										class="hover-green"
										@click.native="openDialogEdit(scope.row, scope.$index)"
										:cont-str="getTeamsStr(scope.row.teamwork) ? getTeamsStr(scope.row.teamwork) : '未设置'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 业务范围 -->
									<Tooltips
										v-else-if="item.colNo == 'salesman'"
										class="hover-green"
										@click.native="openDialogEdit(scope.row, scope.$index)"
										:cont-str="getSalesmanStr(scope.row.salesman) ? getSalesmanStr(scope.row.salesman) : '未设置'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>

							<u-table-column label="" width="120" align="right" fixed="right">
								<template slot-scope="scope">
									<el-button type="text" class="el-icon-edit-outline" @click="openDialogEdit(scope.row, scope.$index)">
									</el-button>
									<el-button
										type="text"
										class="icon-third-bt_usdis"
										:class="{ red: scope.row.status == 0 }"
										@click="closeRow(scope.row, scope.$index)"
									>
									</el-button>
									<el-button type="text" class="icon-third-bt_usdel" @click="deleteRow(scope.row, scope.$index)"></el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="角色管理" name="roleManagement">
				<RoleManagement v-if="activeTab == 'roleManagement'" />
			</el-tab-pane>
			<el-tab-pane label="部门管理" name="departmentManagement">
				<DepartmentManagement v-if="activeTab == 'departmentManagement'" />
			</el-tab-pane>
		</el-tabs>

		<!-- 放大镜查询 -->
		<el-popover v-model="searchPopver" ref="thSearch" placement="bottom-end" width="400" :visible-arrow="false">
			<!-- 放大镜搜索表单 -->
			<div class="searchPop p0 fs-12">
				<div class="searchPop-header">
					<span>查找条件</span>
					<i class="searchPop-header-close el-icon-close" @click.stop="searchPopver = false"></i>
				</div>
				<el-form :model="searchForm" label-width="50px" size="mini" label-position="left" class="W100 pt10 pr20">
					<el-form-item label="姓名" prop="userName">
						<el-input placeholder="姓名" v-model="searchForm.userName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="手机号" prop="phoneNo">
						<el-input placeholder="手机号" v-model="searchForm.phoneNo" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="性别" prop="genders">
						<el-checkbox-group v-model="searchForm.genders">
							<el-checkbox label="0">女</el-checkbox>
							<el-checkbox label="1">男</el-checkbox>
						</el-checkbox-group>
					</el-form-item>
				</el-form>
				<div class="mt20 border-top text-right">
					<el-button type="text" class="color-666" @click.stop="queryTableData(1)">确定</el-button>
				</div>
			</div>
		</el-popover>

		<!-- 新增、编辑 -->
		<el-dialog
			:visible.sync="dialogEdit"
			width="888px"
			append-to-body
			:close-on-click-modal="false"
			@close="closeDialogEdit"
			@click="isAddLabel = false"
		>
			<el-row slot="title">{{ isEdit ? '编辑用户' : '添加用户' }}</el-row>
			<el-form class="multiColumn-form" ref="userFormRef" :model="editForm" label-width="100px" :rules="editRules">
				<el-form-item label="姓名" prop="userName">
					<el-input placeholder="姓名" v-model="editForm.userName"></el-input>
				</el-form-item>
				<el-form-item label="电话" prop="phoneNo">
					<el-input placeholder="电话号码(登录账号)" v-model="editForm.phoneNo"></el-input>
				</el-form-item>
				<el-form-item label="公司" prop="departmentName">
					<el-select v-model="editForm.departmentName" placeholder="请选择公司" clearable filterable class="W100">
						<!-- <el-option v-for="item in departmentNameList" :key="item" :label="item" :value="item"> </el-option> -->
						<el-option v-for="item in departmentList" :key="item.adid" :label="item.departmentName" :value="item.departmentName">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="部门" prop="adid">
					<el-cascader
						:disabled="!editForm.departmentName"
						ref="addCascader"
						class="W100"
						v-model="editForm.adid"
						:props="{
							checkStrictly: true,
							emitPath: false,
							expandTrigger: 'hover',
							value: 'adid',
							label: 'departmentName',
						}"
						:options="departmentList"
						:show-all-levels="true"
						placeholder="请选择部门"
						@visible-change="elCascaderOnlick('addCascader')"
						@expand-change="elCascaderOnlick('addCascader')"
						clearable
						filterable
					></el-cascader>
				</el-form-item>
				<el-form-item label="渠道" prop="channelName">
					<el-select
						v-model="editForm.channelName"
						placeholder="请选择渠道"
						clearable
						filterable
						collapse-tags
						multiple
						class="W100"
						@change="editForm.twid = []"
					>
						<el-option v-for="item in channelNameList" :key="item.twid" :label="item.channelName" :value="item.channelName">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="分销/代理" prop="twid">
					<el-select v-model="editForm.twid" placeholder="分销/代理" filterable multiple clearable collapse-tags class="W100">
						<el-option v-for="item in teamworkNameList" :key="item.twid" :label="item.teamworkName" :value="item.twid">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="业务范围" prop="salesman">
					<el-select
						v-model="editForm.salesmanUid"
						placeholder="请选择业务范围"
						filterable
						clearable
						collapse-tags
						multiple
						class="W100"
					>
						<el-option v-for="item in salesmanList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="托管方" prop="hosterList">
					<el-select
						v-model="editForm.hosterList"
						placeholder="请选择托管方"
						filterable
						multiple
						clearable
						collapse-tags
						class="W100"
					>
						<el-option v-for="item in hosterList" :key="item.twid" :label="item.teamworkName" :value="item.twid"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="性别">
					<el-radio-group v-model="editForm.gender">
						<el-radio :label="1">男</el-radio>
						<el-radio :label="0">女</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="邮箱">
					<el-input v-model="editForm.email" placeholder="请输入邮箱" clearable></el-input>
					<span v-show="editForm.email && !isEmail" class="fs-12 red">* 请输入正确的邮箱格式！</span>
				</el-form-item>

				<el-form-item label="用户标签" v-show="isEdit">
					<!-- <span class="fs-12 color-999">*双击标签进入修改状态</span> -->
					<el-checkbox-group class="label-checkbox" v-model="editForm.remarks" size="small">
						<el-checkbox v-for="(item, index) in userLabelOptions" :key="index" :label="item.remark" border>
							<el-input
								v-if="item.inputVisible"
								class="w-120 m0 p0 input-border-none"
								size="mini"
								v-model="newLabel"
								placeholder="请输入"
								@change="updateLabel(item, index)"
							></el-input>

							<div v-else class="flex-align-center gap-10" @dblclick.stop="showUpdateInput(item.remark, index)">
								<Tooltips class="max-w-100" :cont-str="item.remark" :cont-width="80" />
								<!-- <i class="fs-20 color-999 pointer p0 m0 el-icon-circle-close" @click.stop="deleteLabel(item, index)"></i> -->
							</div>
						</el-checkbox>
						<el-checkbox @click.native.stop="showAddInput" label="+添加标签" border>
							<el-input
								v-if="isAddLabel"
								v-model="newLabel"
								class="w-120 m0 p0 input-border-none"
								size="mini"
								placeholder="请输入"
								clearable
								ref="labelInput"
								@keyup.enter.stop="addNewLabel(newLabel)"
								@change="addNewLabel(newLabel)"
							></el-input>
						</el-checkbox>
					</el-checkbox-group>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveEditUser">保存</el-button>
			</el-row>
		</el-dialog>

		<!-- 角色设置 -->
		<el-dialog :visible.sync="dialogApprove" width="30%" append-to-body :close-on-click-modal="false" @close="cancelDiaApprover">
			<el-row slot="title">用户角色设置</el-row>
			<div class="roleSetBox mt20">
				<div v-for="(item, index) in approvalBtnList" :key="'appro' + index" class="roleSetItem">
					<span>{{ item.btnName }}</span>
					<i
						v-show="item.arid != 1"
						class="el-icon-error"
						style="cursor: pointer; color: #c3c7ce; margin-left: 0.5vw"
						@click="delProveSet(item, index)"
					></i>
				</div>
			</div>
			<el-button type="text" class="icon-third-bt_manmenu mt20" @click="dialogAddRole = true">添加角色</el-button>

			<el-row slot="footer">
				<el-button size="mini" type="primary" @click="setApproval">确定</el-button>
			</el-row>
			<!--添加角色-->
			<el-dialog
				:visible.sync="dialogAddRole"
				width="25%"
				append-to-body
				:close-on-click-modal="false"
				@close="dialogAddRole = false"
			>
				<el-row slot="title">角色选择</el-row>

				<el-form label-width="5vw">
					<el-form-item label="角色">
						<el-select v-model="selectedRole" filterable multiple clearable collapse-tags placeholder="请选择" class="W100">
							<div class="flex-wrap w-450">
								<el-option class="w-150" v-for="item in roleList" :label="item.roleName" :key="item.arid" :value="item.arid + ''">
								</el-option>
							</div>
						</el-select>
					</el-form-item>
				</el-form>
				<el-row slot="footer">
					<el-button size="mini" type="primary" @click="saveRoleSet">保存</el-button>
				</el-row>
			</el-dialog>
		</el-dialog>
	</div>
</template>

<script>
import { deepClone, debounce, checkRequired, dateFormat, sortTableData, uniqueArrayByKey, resetValues } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import ImportTable from '@/components/ImportTable'; //导入组件
import ImportBtn from '@/components/ImportTable/ImportBtn'; //导入按钮
import RoleManagement from './roleManagement.vue';
import DepartmentManagement from './departmentManagement.vue';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'

export default {
	// import引入的组件需要注入到对象中才能使用
	components: { RoleManagement, DepartmentManagement, ImportTable, ImportBtn },
	name: 'userManage',
	data() {
		return {
			activeTab: 'userManage',

			salesmanList: [],
			departmentList: [],
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '公司', colNo: 'departmentName', align: 'left', width: '80', sortable: true },
				{ colName: '部门', colNo: 'adminDepartmentVO', align: 'left', width: '80', sortable: true },
				{ colName: '姓名', colNo: 'userName', align: 'left', width: '120', sortable: true },
				{ colName: '状态', colNo: 'status', align: 'center', width: '80', sortable: true },
				{ colName: '登记人', colNo: 'createUserName', align: 'left', width: '80', sortable: true },
				{ colName: '登记时间', colNo: 'createTime', align: 'center', width: '100', sortable: true },
				{ colName: '角色', colNo: 'adminRoleVOS', align: 'left', width: '', sortable: true },
				{ colName: '渠道', colNo: 'channelName', align: 'left', width: '80' },
				{ colName: '分销/代理', colNo: 'teamwork', align: 'left', width: '120' },
				{ colName: '业务范围', colNo: 'salesman', align: 'left', width: '120' },
				{ colName: '用户标签', colNo: 'adminUserLabelVOS', align: 'left', width: '', sortable: true },
			],
			searchForm: {
				query: '',
				userName: '',
				phoneNo: '',
				departmentName: '',
				// photo:'',
				// picture:'',
				statusList: ['1'],
				genders: [],
			},
			allTeamList: [], //所有渠道/分销/代理/托管等列表
			statusMap: {
				0: '禁用',
				1: '启用',
			},
			searchPopver: false,
			dialogEdit: false,
			//人员修改
			editForm: {
				adid: '', //后台用户部门id
				auid: '',
				channelName: [],
				departmentName: '',
				email: '',
				gender: '',
				hosterList: [],
				phoneNo: '',
				photo: '',
				picture: '',
				salesmanUid: [],
				twid: [],
				userName: '',
				remarks: [],
				adminUserLabelDTOS: [],
			},
			isEdit: false,
			isAddLabel: false, //添加标签
			//角色添加
			dialogApprove: false,

			approvalBtnList: [],
			adminCurr: [],
			dialogAddRole: false,
			selectedRole: [],
			roleList: [],
			auidCurr: '',
			userLabelOptions: [],
			newLabel: '',
			//必填项
			editRules: {
				userName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
				phoneNo: [{ required: true, message: '请输入电话号码', trigger: 'blur' }],
				departmentName: [{ required: true, message: '请输入公司', trigger: 'blur' }],
				adid: [{ required: true, message: '请输入部门', trigger: 'blur' }],
				channelName: [{ required: true, message: '请输入渠道', trigger: 'blur' }],
				twid: [{ required: true, message: '请选择分销/代理', trigger: 'change' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 判断输入的内容是否为邮箱格式
		isEmail() {
			if (this.editForm.email) {
				return /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/.test(this.editForm.email);
			} else {
				return false;
			}
		},
		//渠道列表
		channelNameList() {
			return uniqueArrayByKey(
				[{ twid: '888', channelName: '*' }].concat(this.allTeamList.filter(item => item.channelName)),
				'channelName',
			);
		},
		//分销/代理列表
		teamworkNameList() {
			if (this.editForm.channelName?.includes('*')) {
				return [{ twid: '888', teamworkName: '*' }].concat(this.allTeamList);
			} else if (this.editForm.channelName?.length) {
				return [{ twid: '888', teamworkName: '*' }].concat(
					this.allTeamList.filter(item => this.editForm.channelName?.includes(item.channelName)),
				);
			} else {
				return [];
			}
		},
		hosterList() {
			//托管列表
			return [{ twid: '888', teamworkName: '*' }].concat(this.teamworkNameList.filter(item => item.channelPattern == 2));
		},
		// 业务顾问列表
		// salesmanList() {
		// 	const salesmanList = this.tableDataCopy.filter(item => {
		// 		// 过滤出adminRoleVOS里有8的是业务顾问
		// 		const isSalesMan = item.adminRoleVOS?.some(item => item.arid == '8');
		// 		return isSalesMan;
		// 	});
		// 	return [{ auid: '888', userName: '*' }].concat(salesmanList);
		// },
		//部门列表
		// adidOptions() {
		// 	return this.departmentList?.find(i => i.departmentName === this.editForm.departmentName)?.children || [];
		// },

		// 超级管理员/人力资源
		isAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.roleName == '超级管理员' || item.roleName == '人力资源管理') || false;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.getRoleList();
		this.queryTableData('init');
		this.getAllTeamWork();
		this.queryDepartmentList();
	},
	activated() {
		this.getRoleList();
		this.queryTableData('init');
		this.getAllTeamWork();
		this.queryDepartmentList();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 显示修改标签输入框
		showUpdateInput(remark, index) {
			// 去除 '+添加标签'
			this.editForm.remarks = this.editForm.remarks.filter(item => item != '+添加标签');
			this.isAddLabel = false;
			this.newLabel = remark;
			this.userLabelOptions.forEach((item, i) => {
				if (i == index) {
					item.inputVisible = true;
				} else {
					item.inputVisible = false;
				}
			});
		},
		// 显示添加标签输入框
		showAddInput() {
			this.userLabelOptions.forEach((item, i) => {
				item.inputVisible = false;
			});
			this.newLabel = '';
			this.isAddLabel = true;
			// 聚焦
			this.$nextTick(() => {
				this.$refs?.labelInput?.focus();
			});
		},
		// 修改标签
		async updateLabel(item, index) {
			const oldQuery = this.userLabelOptions[index].remark;
			const API = 'updateUserTag';
			try {
				const res = await this.$axios[API](JSON.stringify({ oldQuery, query: this.newLabel }));
				if (res.data.success) {
					item.inputVisible = false;
					this.editForm.remarks.filter(item => item != oldQuery);
					this.userLabelOptions[index].remark = this.newLabel;
					this.editForm.remarks.push(this.newLabel);
					this.newLabel = '';
					this.$succ(res.data.message);
				} else {
					this.newLabel = '';
					item.inputVisible = false;
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 删除标签
		async deleteLabel(item, index) {
			this.$confirm('此操作将永久删除该标签（其他已设置的用户标签也会被删除）, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const query = this.userLabelOptions[index].remark;
					const API = 'deleteUserTag';
					try {
						const res = await this.$axios[API](JSON.stringify({ query }));
						if (res.data.success) {
							this.editForm.remarks.filter(item => item != query);
							this.userLabelOptions.splice(index, 1);
							this.$succ(res.data.message);
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},

		// 添加标签
		addNewLabel(newLabel) {
			// 去除 '+添加标签'
			this.editForm.remarks = this.editForm.remarks.filter(item => item != '+添加标签');
			this.isAddLabel = false;
			if (!newLabel) return;
			this.editForm.remarks.push(newLabel);
			this.userLabelOptions.push({ aulid: '', remark: this.newLabel });
			this.newLabel = '';
			// this.saveEditUser();
		},
		// 查询标签
		async queryUserLabel(query) {
			const API = 'selectUserTag';
			try {
				const res = await this.$axios[API](JSON.stringify({ query }));
				if (res.data.success) {
					// this.$succ(res.data.message);
					this.userLabelOptions = res.data.data.map(item => {
						const aulid = this.editForm?.odlRemarks?.find(i => i.remark == item)?.aulid || '';
						return {
							aulid,
							remark: item,
						};
					});
					// console.log('allLabels', this.userLabelOptions);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		changeTab(tab) {
			if (tab.name == 'userManage') {
				this.getRoleList();
				this.queryDepartmentList();
			}
		},
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		//获取渠道/分销/代理/托管信息
		getAllTeamWork() {
			this.$axios
				.selectAllTeamWork(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.allTeamList = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAllTeamWork |' + error);
				});
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const str = JSON.stringify({ ...this.searchForm });
			this.$axios
				.selectAdminUser(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						// if (type == 'init' || type == 'update') {
						// 	// 根据每项的auid去重
						// 	this.tableDataCopy = this.salesmanList
						// 		.concat(this.tableData)
						// 		.filter((v, i, a) => a.findIndex(t => t.auid === v.auid) === i);
						// }
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAdminUser |' + error);
				});
		}),
		// 查询数据
		queryDepartmentList: debounce(function (type) {
			this.$axios
				.selectAdminDepartmentList(JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						this.departmentList = res.data.data?.reduce((acc, curr) => {
							const order = curr.departmentOrder;
							const path = curr.departmentPath.split('');
							let parent = acc;

							for (let i = 0; i < path.length; i++) {
								const node = parent.find(n => n.departmentOrder === order && n.departmentPath === path.slice(0, i + 1).join(''));
								if (node) {
									parent = node.children || (node.children = []);
								}
							}

							parent.push({
								...curr,
								children: null,
							});

							return acc;
						}, []);
						console.log(this.departmentList);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAdminDepartmentList |' + error);
				});
		}),
		// 简单粗暴的每次打开弹窗都查询业务员
		querySalesmanList(row) {
			this.salesmanList = [];
			const str = JSON.stringify({
				pageNum: 1,
				pageSize: 1000,
				query: '',
				departmentName: '',
				genders: [],
				phoneNo: '',
				statusList: ['0', '1'],
				userName: '',
			});
			this.$axios
				.selectAdminUser(str)
				.then(res => {
					if (res.data.success) {
						const salesmanList = res.data.data.filter(item => {
							// 如果是当前用户则直接添加进去 过滤出adminRoleVOS里有8 或者标签有业务的是业务顾问
							const isSalesMan =
								item.auid == row?.auid ||
								item.adminRoleVOS?.some(rItem => rItem.arid == '8') ||
								item.adminUserLabelVOS?.some(lItem => lItem?.remark?.indexOf('业务'));
							return isSalesMan;
						});
						this.salesmanList = [{ auid: '888', userName: '*' }].concat(salesmanList);
						// this.departmentNameList = [...new Set(res.data.data.map(item => item.departmentName).filter(item => item))];
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAdminUser |' + error);
				});
		},
		// 分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 关闭编辑弹窗
		closeDialogEdit() {
			this.dialogEdit = false;
			this.isAddLabel = false;
			this.$refs?.userFormRef?.resetFields();
			this.editForm = resetValues(this.editForm);
		},
		// 保存用户信息
		saveEditUser() {
			if (checkRequired(this.editForm, this.editRules)) {
				return;
			}

			if (this.editForm.email && !this.isEmail) {
				return this.$message.warning('请输入正确的邮箱格式！');
			}

			// 用户标签
			this.editForm.adminUserLabelDTOS = this.userLabelOptions
				.map(item => {
					const { aulid, remark } = item;
					// 如果有aulid，且remarks里面也有则就是无修改，直接返回
					if (aulid && this.editForm.remarks.includes(remark)) return { ...item };
					// 如果有aulid，且remarks里面也没有则就是去除
					if (aulid && !this.editForm.remarks.includes(remark)) return null;
					// 如果没有有aulid，且remarks里面也有则是新增
					if (!aulid && this.editForm.remarks.includes(remark)) {
						return {
							remark,
						};
					}
					// 如果没有有aulid，且remarks里面也没有则是不添加
					if (!aulid && !this.editForm.remarks.includes(remark)) return null;
					// 如果不满足上述两个条件，则跳过当前元素
					return null;
				})
				.filter(item => item !== null);

			const DATA = JSON.stringify({ ...this.editForm });
			// 分销/代理添加单独接口（俺也不知道为啥要这样搞）
			!this.isEdit &&
				this.$axios
					.updateUserTeamWork(DATA)
					.then(res => {
						if (res.data.success) {
							//
						}
					})
					.catch(error => {
						console.log('updateUserTeamWork |' + error);
					});

			// 新增/修改
			const URL = this.isEdit ? 'updateAdminUser' : 'insertAdminUser';
			this.$axios[URL](DATA)
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功!');
						this.closeDialogEdit();
						this.queryTableData('update');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${URL} |` + error);
				});
		},
		//日期format
		dateFormat: dateFormat,
		// 打开添加用户弹窗
		openDialogAdd() {
			this.querySalesmanList(null);
			this.dialogEdit = true;
			this.isEdit = false;
		},
		// 打开编辑用户弹窗
		openDialogEdit(row, index, type) {
			this.querySalesmanList(row);
			this.editForm = { ...this.editForm, ...deepClone(row) };
			const str = JSON.stringify({ auid: row.auid });
			this.$axios
				.selectPhoneNoByAuid(str)
				.then(res => {
					if (res.data.success) {
						this.editForm.channelName = row.channelName?.filter(Boolean) || [];
						this.editForm.phoneNo = res.data.data.phoneNo;
						this.editForm.email = res.data.data.email || '';
						this.editForm.salesmanUid = row.salesman && Object?.keys(row.salesman);
						this.editForm.twid = row.teamwork && Object?.keys(row.teamwork);
						this.editForm.hosterList = row.hosterIdList;
						this.editForm.userName = row.userName;
						this.editForm.departmentName = row.departmentName;
						this.editForm.gender = row.gender || '';
						this.editForm.auid = row.auid;
						this.editForm.remarks = row?.adminUserLabelVOS?.map(item => item.remark) || [];
						this.editForm.odlRemarks = row?.adminUserLabelVOS || [];
						this.isEdit = true;
						this.dialogEdit = true;
						this.queryUserLabel('');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectPhoneNoByAuid |' + error);
				});
		},
		//禁用/启用用户
		closeRow(row, index) {
			const btnName = row.status ? '禁用' : '启用';
			const newStatus = row.status ? 0 : 1;
			const msg = '【' + row.userName + '】' + '将被' + btnName;
			this.$confirm(msg, btnName + '用户', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					// if(uid == this.userInfos){
					//     this.$message.warning('该用户为当前登录用户,无法操作');
					//     return
					// }
					const str = JSON.stringify({ auid: row.auid, status: newStatus });
					this.$axios
						.forbiddenAdminUser(str)
						.then(res => {
							if (res.data.success) {
								this.$succ('操作成功!');
								row.status = newStatus;
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('forbiddenAdminUser |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 删除该行
		deleteRow(row, index) {
			const user = row.userName;
			const twidList = row.teamwork && Object.keys(row.teamwork);
			const auid = row.auid;
			const msg = `【${user}】将被删除`;
			this.$confirm(msg, '删除用户', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					// if(uid == this.userInfos){
					//     this.$message.warning('该用户为当前登录用户,无法操作');
					//     return
					// }
					const str = JSON.stringify({ auid, twidList });
					this.$axios
						.deleteAdminUser(str)
						.then(res => {
							if (res.data.success) {
								this.$succ('删除成功!');
								this.queryTableData(1);
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteAdminUser |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 打开角色设置弹窗
		openRoleSet(row) {
			this.auidCurr = '';
			this.approvalBtnList = [];
			this.adminCurr = [];
			/* 不晓得写的什么？？？？ */
			if (this.isAdmin) {
				this.selectedRole = [];
				this.auidCurr = row.auid;
				if (row.adminRoleVOS && row.adminRoleVOS.length > 0) {
					row.adminRoleVOS.map(adItem => {
						this.approvalBtnList.push({
							arid: adItem.arid,
							btnName: adItem.roleName,
						});
						if (adItem.arid == 1) {
							this.adminCurr.push({
								arid: adItem.arid,
								btnName: adItem.roleName,
							});
						} else {
							this.selectedRole.push(adItem.arid + '');
						}
					});
				}
				this.dialogApprove = true;
			} else {
				this.$message.warning('只有超级管理员/人力资源管理可以修改用户角色');
			}
		},
		// 取消角色设置弹窗
		cancelDiaApprover() {
			this.dialogApprove = false;
			this.approvalBtnList = [];
			this.auidCurr = '';
			this.selectedRole = [];
			this.adminCurr = [];
		},
		// 删除角色设置
		delProveSet(row, index) {
			this.approvalBtnList.splice(index, 1);
		},
		// 设置角色
		setApproval() {
			const auid = this.auidCurr;
			const arids = Array.from(new Set(this.approvalBtnList.map(item => item.arid)));
			const str = JSON.stringify({
				auid: auid,
				arids: arids,
			});
			this.$axios
				.distributeAdminRole(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.cancelDiaApprover();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('distributeAdminRole |' + error);
				});
		},
		// 保存角色设置
		saveRoleSet() {
			const newRoleList = this.selectedRole
				.map(item => {
					const rItem = this.roleList.find(role => role.arid === item);
					return {
						arid: item,
						btnName: rItem ? rItem.roleName : '',
					};
				})
				.filter(item => item.btnName !== '');
			this.approvalBtnList = [...this.adminCurr, ...newRoleList];
			this.dialogAddRole = false;
		},
		// 获取角色列表
		getRoleList() {
			this.$axios
				.selectAdminRole(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.roleList = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAdminRole |' + error);
				});
		},
		// 数组转字符串
		getRolesStr(roleList) {
			return roleList ? roleList.map(item => item.roleName).join(',') : '';
		},
		getLabelStr(labelList) {
			return labelList ? labelList.map(item => item.remark).join(',') : '';
		},
		getSalesmanStr(salesmanList) {
			return salesmanList ? Object.values(salesmanList).join(',') : '';
		},
		getTeamsStr(teamwork) {
			return teamwork
				? Object.values(teamwork)
						.map(key => key === '888' ? '*' : key)
						.join(',')
				: '';
		},

		// 联级选择器点击设置(隐藏元素)
		elCascaderOnlick(refObj) {
			const that = this;
			setTimeout(() => {
				const cascaderNodes = document.querySelectorAll('.el-cascader-node__label');
				cascaderNodes.forEach(el => {
					el.addEventListener('click', () => {
						el.previousElementSibling.click();
						that.$refs[refObj].dropDownVisible = false;
					});
				});

				const radioElements = document.querySelectorAll('.el-cascader-panel .el-radio');
				radioElements.forEach(el => {
					el.addEventListener('click', () => {
						that.$refs[refObj].dropDownVisible = false;
					});
				});
			}, 100);
		},

		// 数据导入
		openImport: debounce(function (type = 'import') {
			const PROPS = {
				API: 'importAdminUser', //导入接口
				templateName: '后台用户导入模板', //模板文件名称（下载模板用）
				dataName: '用户', //数据名（提示：成功导入xxx数据xxx条!）
				type, // 导入或查看导入记录
			};
			this.$refs.ImportTable.openImport(PROPS);
		}),
	},
};
</script>

<style lang="scss" scoped>
#userManage {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
<style lang="scss">
.roleSetBox {
	display: flex;
	justify-content: flex-start;
	flex-wrap: wrap;

	.roleSetItem {
		display: flex;
		justify-content: center;
		align-items: center;
		border: 1px solid #d7d7d7;
		padding: 8px 20px;
		margin: 2px 5px;
		font-size: 12px;
		border-radius: 5px;
	}
}

.label-checkbox {
	width: 750px;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	// gap: 2px;
	.el-checkbox.is-bordered.el-checkbox--small {
		padding: 3px 5px;
		border-radius: 15px;
		height: 32px;
		margin-right: 0;
		display: flex;
		align-items: center;
		margin: 0;
		margin: 0 5px 10px 0;
		.el-checkbox__input {
			display: none;
		}
		.el-checkbox__label {
			padding: 0;
		}
	}
}
</style>
