<template>
	<div class="HealthDegreeOfBPList" :class="openMove ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<!-- 询盘详情 -->
		<InquiryDetail v-if="showMap.InquiryDetail" ref="InquiryDetail" :inquiryOptions="tableData" @close="queryTableData()" />

		<BaseLayout>
			<template #header>
				<span class="search-label">部门：{{ searchForm.departmentName }}</span>
				<span class="search-label">姓名：{{ searchForm.bpname }}</span>
				<span class="search-label">
					日期：
					{{ dateFormat(searchForm.startTime, 'line') }} - {{ dateFormat(searchForm.endTime, 'line') }}
				</span>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<el-button type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
				</div>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<!-- <div class="table-toolbar"></div> -->
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					:row-class-name="getRowColor"
					@sort-change="sortChange"
					show-header-overflow="title"
					:pagination-show="false"
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="item.colNo.includes('time')"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 审查状态 0: 无需检查, 1: 待评价, 2: 不合格, 3: 合规带问题, 4: 合格, 5: 优秀, 10 免检 -->
							<div v-else-if="item.colNo == 'qualityCheck'" class="fs-12">
								<span v-if="scope.row.qualityCheck === 0" class="color-999">无需评价</span>
								<span v-else-if="scope.row.qualityCheck === 10" class="color-999">免检</span>

								<el-button
									v-else-if="scope.row.qualityCheck === 1"
									class="red p0"
									size="mini"
									type="text"
									@click="openDialog('检查', scope.row)"
									>未评</el-button
								>
								<el-button
									v-else
									class="p5"
									:type="{ 2: 'danger', 3: 'warning', 4: 'primary', 5: 'primary' }[scope.row.qualityCheck]"
									size="mini"
									@click="openDialog('检查', scope.row)"
								>
									{{ qualityCheckMap[scope.row.qualityCheck] }}
								</el-button>
							</div>
							<!-- 工作项 -->
							<div
								v-else-if="item.colNo == 'workItem' && scope.row.idid"
								class="hover-green"
								@click="openDetail('修改', scope.row)"
							>
								<Tooltips class="W100" :cont-str="scope.row.workItem" :cont-width="200" />
							</div>
							<!-- 详情 -->
							<div v-else-if="item.colNo == 'detailInfo'" class="flex-align-center gap-10">
								<Tooltips class="W100" :cont-str="scope.row.detailInfo" :cont-width="200" />
								<div v-if="scope.row.recordUrl" class="W60">
									<!-- 询盘录音播放器 -->
									<InquiryAudioPlayer
										:audioUrl="scope.row.recordUrl"
										:idid="scope.row.idid"
										@playPause="queryTableData"
										@playEnded="queryTableData"
									/>
								</div>
							</div>
							<!-- 录音已听占比 -->
							<div v-else-if="item.colNo == 'listeningProgressRatio' && scope.row.listeningProgressRatio">
								<Tooltips class="W100" :cont-str="`${(scope.row.listeningProgressRatio * 100).toFixed(0)}%`" :cont-width="200" />
							</div>
							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>

		<AuditDialog ref="AuditDialog" @update="queryTableData(1)" />
	</div>
</template>

<script>
import { debounce, dateFormat, sortTableData, jointString, deepClone, checkRequired, resetValues } from '@/util/tool'; //按需引入常用工具函数
import { bigMul } from '@/util/math';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import AuditDialog from '@/pages/performanceManagement/individualPoints/AuditDialog.vue'; //质量检查评价
import InquiryAudioPlayer from '@/components/InquiryAudioPlayer'; //询盘音频播放器

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		InquiryDetail,
		AuditDialog,
		InquiryAudioPlayer,
	},
	name: 'individualPoints', //组件名应同路由名(否则keep-alive不生效)
	props: {},
	data() {
		return {
			openMove: false, //打开组件
			activeTab: 'HealthDegreeOfBPList', //激活tab页
			activeUser: '', //激活tab页
			userTabList: [],
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},

			// 选择器数据 - 车间/部门/类型等...
			adpidList: [],
			// 查询表单
			searchForm: {
				id: '',
			},
			// 0: 无需检查, 1: 待评价, 2: 不合格, 3: 合规带问题, 4: 合格, 5: 优秀, 10 免检
			qualityCheckMap: {
				0: '无需检查',
				1: '待评价',
				2: '不合格',
				3: '合规带问题',
				4: '合格',
				5: '优秀',
				10: '免检',
			},

			showMap: {
				InquiryDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）

		// 动态表格列
		tableColumn() {
			// 如果是新询盘 只返回部分列
			if (this.searchForm.VOS_Key === 'newInquiryDetailVOS') {
				return [
					{ colName: '时间', colNo: 'time', align: 'center', width: '150' },
					{ colName: '客户', colNo: 'customer', align: '', width: '200' },
					{ colName: '工作项', colNo: 'workItem', align: 'left', width: '' },
				];
			}
			return [
				{ colName: '时间', colNo: 'time', align: 'center', width: '120' },
				{ colName: '客户', colNo: 'customer', align: '', width: '150' },
				{ colName: '工作项', colNo: 'workItem', align: 'left', width: '160' },
				{ colName: '积分', colNo: 'points', align: 'right', width: '80' },
				{ colName: '详情', colNo: 'detailInfo', align: 'left', width: '' },
				{ colName: '录音已听占比', colNo: 'listeningProgressRatio', align: 'right', width: '150' },
				{ colName: '质量检查', colNo: 'qualityCheck', align: 'left', width: '100' },
			];
		},
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('close');
				this.searchForm = resetValues(this.searchForm);
				this.tableData = [];
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	activated() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开询盘详情
		openDetail(type, row, api) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom(type, row, api);
			});
		},
		// 打开组件
		showDetailCom(searchForm) {
			this.openMove = true;
			this.searchForm = searchForm;
			this.queryTableData(1);
		},

		// 打开弹窗
		openDialog(type, row) {
			if (!row.auplid) return;
			row.auditStatus = row.qualityCheck;
			this.$refs.AuditDialog.open(row);
		},

		// 切换tab
		changeTab() {},

		// 查询表格数据
		queryTableData: debounce(function (type) {
			if (!this.searchForm.auid && this.openMove == true) return;
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectHealthDetailOfBP'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data[this.searchForm.VOS_Key] || [];
						this.sortChange(this.tableSort, true);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格文本高光显示
		getRowColor({ row }) {
			// 标红处理： 未评
			return row?.qualityCheck === 1 ? 'red' : ''; //未评
		},

		dateFormat, //日期format
		jointString, //拼接字符串
		bigMul, //大数乘法
	},
};
</script>

<style lang="scss" scoped>
.HealthDegreeOfBPList {
	.table-wrapper .table-main {
		height: calc(100vh - 205px) !important;
	}
}
</style>
