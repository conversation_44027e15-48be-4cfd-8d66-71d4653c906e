<template>
	<BaseLayout :showHeader="false">
		<template #main>
			<div class="promotionConfig overflow-auto">
				<p v-for="item in RuleData" :key="item.ogcid" class="flex-align-center">
					<span class="form-labe w-180 flex-align-center">
						<Tooltips :cont-str="item.ogcKeyCn" :cont-width="160" />
						<el-tooltip :content="item.memo" effect="light" placement="top">
							<i class="el-icon-warning-outline ml5 pointer hover-green"></i>
						</el-tooltip>
					</span>

					<el-input class="flex-1" v-model="item.ogcValue" :placeholder="item.memo" clearable @change="saveEdit(item)"></el-input>
				</p>
			</div>
		</template>
	</BaseLayout>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'promotionConfig', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'promotionConfig', //激活tab页
			// editForm: {
			// 	name: '',
			// },
			// formRules: {
			// 	name: [{ required: true, message: '请输入内容', trigger: 'blur' }],
			// },

			RuleData: [],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		// this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		async saveEdit(data) {
			const API = 'updateInquiryPromotionalMaterialConfiguration';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...data }));
				if (res.data.success) {
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			const API = 'fetchInquiryPromotionalMaterialConfiguration'; //接口
			this.$axios[API](JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.RuleData = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
	},
};
</script>

<style lang="scss">
.promotionConfig {
	width: 100%;
	height: calc(100vh - 200px);
	overflow: hidden;
	position: relative;

	font-size: 14px;
	color: #666;
	// background-color: #fff;
	// border: solid 1px #d7d7d7;
	// border-radius: 8px;
	// padding: 20px !important;
	// box-sizing: border-box;
}
</style>
