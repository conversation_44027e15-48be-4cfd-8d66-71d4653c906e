/* 开发管理API */

const urlList = [
	// 人员管理
	'/background/web/ResearchDepartmentController/addResearchDepartment', // 人员管理-添加部门人员信息
	'/background/web/ResearchDepartmentController/exportResearchDepartment', // 人员管理-导出部门人员列表
	'/background/web/ResearchDepartmentController/exportYearResearchDepartment', // 人员管理-导出员年度系数一览表
	'/background/web/ResearchDepartmentController/selectResearchDepartmentList', // 人员管理-查询部门人员列表
	'/background/web/ResearchDepartmentController/selectYearResearchDepartment', // 人员管理-查询人员年度系数一览表
	'/background/web/ResearchDepartmentController/udapteResearchDepartment', // 人员管理-修改部门人员信息

	'/background/web/ProjectManagementController/addProject', // 项目管理-添加项目
	'/background/web/ProjectManagementController/selectProject', // 项目管理-项目列表查询
	'/background/web/ProjectManagementController/exportProject', // 项目管理-导出项目列表
	'/background/web/ProjectManagementController/selectProjectGanttChart', // 计划管理-项目甘特图
	'/background/web/ProjectManagementController/selectAllONGoingProject', // 计划管理-项目甘特图
	'/background/web/ProjectManagementController/selectProjectUserLoad', // 计划管理-人员负荷
	'/background/web/ProjectManagementController/batchUploadFile', // 项目管理-文件上传
	'/background/web/ProjectManagementController/deleteFile', // 项目管理-删除文件
	'/background/web/ProjectManagementController/selectProjectDetail', // 项目管理-项目明细
	'/background/web/ProjectManagementController/taskAuth', // 查询当前登录用户是否是当前项目/技术经理/测试经理
	'/background/web/ProjectManagementController/selectUserInHandProject', // 查询人员全部进行中的项目
	'/background/web/ProjectManagementController/deleteProject', // 删除项目

	// 任务规划
	'/background/web/ProjectManagementClassifyContrller/addClassify', // 开发管理-任务规划-添加/编辑模块
	'/background/web/ProjectManagementClassifyContrller/selectTaskInfoByPmcid', // 开发管理-任务规划-查询任务规划下任务详情
	'/background/web/ProjectManagementClassifyContrller/selectClassify', // 开发管理-任务规划-查询项目分类树结构
	'/background/web/ProjectManagementClassifyContrller/deleteClassify', // 开发管理-任务规划-删除模块
	'/background/web/ProjectManagementClassifyContrller/selectTaskDetailByPmtid', // 开发管理-执行任务
	'/background/web/ProjectManagementClassifyContrller/updateBathTaskInfoDate', // 开发管理-任务规划-批量修改
	'/background/web/ProjectManagementTaskController/addProjectTask', // 开发管理-任务规划-添加任务详情
	'/background/web/ProjectManagementTaskController/deleteProjectTask', // 开发管理-任务规划-删除任务详情
	'/background/web/ProjectManagementTaskController/updateProjectTask', // 开发管理-任务规划-修改任务详情
	'/background/web/ProjectManagementTaskController/getProductMyAgent', // 开发管理-我的代办任务
	'/background/web/ProjectManagementTaskController/getTaskManagerList', // 开发管理-任务管理
	'/background/web/ProjectManagementTaskController/selectTaskDetail', // 开发管理-任务明细
	'/background/web/ProjectManagementTaskController/getTestMyAgent', // 测试管理-我的待办
	'/background/web/ProjectManagementTaskController/getTestTaskManagerList', // 测试管理-测试任务管理
	'/background/web/ProjectManagementTaskController/selectBugCount', // 测试管理-捉虫量
	'/background/web/ProjectManagementTaskController/selectBugCountInfoVO', // 测试管理-捉虫量统计详情
	'/background/web/ProjectManagementTaskController/taksTestSumbit', // 测试管理-测试人员提交任务
	'/background/web/ProjectManagementTaskController/taskTurnTest', // 开发管理-开发人员转测
	'/background/web/ProjectManagementTaskController/contextUploadFile', // 编辑器-图片/视频等文件上传
	'/background/web/ProjectManagementTaskController/taskDrag', // 项目任务拖拽
	'/background/web/ProjectManagementTaskController/likeQueryTask', // 根据名称/编号 查询任务
	'/background/web/ProjectManagementTaskController/relevanceTask', // 任务关联/解除关联
	'/background/web/ProjectManagementTaskController/exportTaskDetail', // 任务明细导出
	'/background/web/ProjectManagementTaskController/achievementAssessment', // 成果评价
	'/background/web/ProjectManagementTaskController/achievementAssessmentExport', // 成果评价导出
	'/background/web/ProjectManagementTaskController/productTaskRiver', // 开发任务河流
	'/background/web/ResearchDepartmentController/selectDepartmentDaily', // 研发日报

	// 缺陷管理
	'/background/web/bugRecordController/addBugRecord', // 缺陷管理-添加
	'/background/web/bugRecordController/bugLevelView', // 缺陷等级视图
	'/background/web/bugRecordController/bugRateView', // 缺陷率视图
	'/background/web/bugRecordController/bugRecordList', // 现网缺陷记录列表
	'/background/web/bugRecordController/closeBugRecord', // 现网缺陷记录结案
	'/background/web/bugRecordController/dutyView', // 责任人视图
	'/background/web/bugRecordController/save8DRecord', // 保存8D报告
	'/background/web/bugRecordController/updateBugRecord', // 修改现网缺陷记录

	// 需求管理
	'/background/web/demandSupervisionController/addDemandSuperVision', // 新增需求管理 --保存
	'/background/web/demandSupervisionController/selectDemandSuperVisionList', // 查询需求管理列表
	'/background/web/demandSupervisionController/submitDemandSuperVision', // 提交需求管理
	'/background/web/demandSupervisionController/updateDemandDocumentUrl', // 更改需求文件
	'/background/web/demandSupervisionController/selectDemandDetailList', // 需求清单查询
	'/background/web/demandSupervisionController/exportDemandDetailList', // 需求清单导出

	'/background/web/demandSupervisionDetailController/selectDemandSupervisionDetailByDsid', // 根据id查询需求管理详情
	'/background/web/demandSupervisionDetailController/updateDemandSupervisionDetailByDsid', // 根据id修改需求管理详情
	'/background/web/demandSupervisionDetailController/submitOrRejectTechnicalReview', // 技术评审 --驳回或提交
	'/background/web/demandSupervisionDetailController/technicalReview', // 技术评审 -- 保存(批量)
	'/background/web/demandSupervisionDetailController/demandSign', // 需求签署 --驳回或通过
	'/background/web/demandSupervisionDetailController/developmentManagerSign', // 开发经理签署 --接受任务
];

// 重名函数映射
const apiNameMap = {};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['selectAllONGoingProject'].includes(urlName)) {
		timeout = 60000;
	}
	return { urlName, url, timeout };
});

export default apiList;
