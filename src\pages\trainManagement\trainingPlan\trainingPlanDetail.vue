<template>
	<div class="trainingPlanDetail" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}</span>
				<div class="flex-align-center gap-10">
					<el-button type="text" class="icon-third_save m0" @click="saveDetail"> 保存</el-button>
					<el-button type="text" class="el-icon-arrow-left m0" @click="showCom = false">返回</el-button>
				</div>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content flex-column gap-10">
				<!-- 顶部信息 -->
				<header class="flex-align-center gap-10 mt5">
					<span class="label-required">计划结束</span>
					<el-date-picker
						class="w-200"
						size="small"
						v-model="detailForm.planEndTime"
						value-format="timestamp"
						type="datetime"
						placeholder="请选择计划结束日期"
						:clearable="false"
						format="yyyy-MM-dd HH:mm"
						:default-value="new Date()"
						:default-time="'23:59:59'"
					></el-date-picker>
					<span class="label-required">培养计划</span>
					<el-input class="w-200" size="small" v-model="detailForm.trainPlan" placeholder="培养计划"></el-input>
					<span class="label">计划说明</span>
					<el-input class="min-w-200 flex-1" size="small" v-model="detailForm.planDesc" placeholder="计划说明"></el-input>
				</header>
				<!-- 中间内容 -->
				<main class="flex-column gap-10 H90">
					<section v-if="titleName != '修改人员'" class="flex-column gap-10 flex-1">
						<span class="label-title">计划课程（{{ detailForm.cids.length }}）</span>
						<!-- 列表 -->
						<div class="border min-h-150 max-h-300 overflow-y-auto p10">
							<el-checkbox-group v-model="detailForm.cids">
								<el-checkbox v-for="item in courseList" :key="item.cid" :label="item.cid">
									<span class="w-100 inline-block fs-12 ellipsis">{{ item.courseName }}</span>
								</el-checkbox>
							</el-checkbox-group>
						</div>
					</section>
					<section v-if="titleName != '修改课程'" class="flex-column gap-10">
						<div class="flex-align-center gap-10">
							<span class="label-title">参与人员（{{ detailForm.auids.length }}）</span>
							<el-input class="searchBox" size="mini" v-model="userQuery" placeholder="请输入人员名称" clearable></el-input>

							<el-select
								v-model="userTag"
								placeholder="用户标签"
								popper-class="select-column-3"
								size="mini"
								multiple
								collapse-tags
								clearable
								filterable
							>
								<el-option v-for="item in userTagOptions" :key="item" :label="item" :value="item"> </el-option>
							</el-select>
						</div>

						<!-- 列表 -->
						<div class="border min-h-300 max-h-450 overflow-y-auto p10 mb20">
							<el-checkbox
								class="mb10"
								:indeterminate="isIndeterminate"
								v-model="detailForm.isSelectAll"
								:true-label="1"
								:false-label="0"
								@change="selectAllUser"
							>
								<span class="fs-12 bolder">全选{{ `${isFilter ? '（已筛选）' : ''}` }}</span>
							</el-checkbox>

							<el-checkbox-group v-model="detailForm.auids">
								<el-checkbox :label="item.auid" v-for="item in filterUserList" :key="item.auid" @change="changeUser(item)">
									<span class="w-120 inline-block fs-12 ellipsis">{{ item.userName }}</span>
								</el-checkbox>
							</el-checkbox-group>
						</div>
					</section>
				</main>

				<!-- 底部操作按钮 -->
				<!-- <footer class="h-40 text-right">
						<el-button type="primary" @click="saveDetail">保存</el-button>
					</footer> -->
			</div>
		</div>
	</div>
</template>
<script>
import { debounce, resetValues, deepClone } from '@/util/tool';
import { mapGetters } from 'vuex';

export default {
	name: 'courseCreationDetail',
	directives: {},
	components: {},
	props: {},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '明细详情',

			detailForm: {
				tpid: '',
				auids: [], //已选人员
				cids: [], //已选课程
				planDesc: '', //计划说明
				trainPlan: '', //培养计划
				planEndTime: '', //计划结束
				isSelectAll: 0,
			},
			userQuery: '', //用户查询
			courseList: [], //课程列表

			isIndeterminate: false,
			userTagOptions: [], //用户标签
			userTag: [], //用户标签
		};
	},
	created() {},
	computed: {
		...mapGetters(['userList']), //用户列表
		// 过滤人员列表
		filterUserList() {
			return this.userList.filter(item => {
				const isIncludeName = this.userQuery ? item.userName.includes(this.userQuery) : true;
				const isIncludeTag = this.userTag.length ? this.userTag?.map(tag => item.userLabel?.includes(tag)).some(Boolean) : true;
				return isIncludeName && isIncludeTag;
			});
		},
		// 是否已筛选
		isFilter() {
			return this.userTag.length > 0 || this.userQuery;
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.userQuery = '';
				this.detailForm = resetValues(this.detailForm); //重置对象
				this.$emit('close');
			}
		},
	},
	mounted() {
		this.queryUserLabel();
	},
	methods: {
		// 查询用户标签
		queryUserLabel() {
			this.$axios.selectUserTag(JSON.stringify({})).then(res => {
				if (res.data.success) {
					this.userTagOptions = res.data.data;
				}
			});
		},
		//全选
		selectAllUser(val) {
			const nowAuids = this.filterUserList?.map(item => item.auid) || [];
			if (!nowAuids.length) return;
			if (val) {
				const auids = this.detailForm.auids;
				this.detailForm.auids = [...new Set([...auids, ...nowAuids])];
			} else {
				this.detailForm.auids = this.detailForm.auids.filter(item => !nowAuids.includes(item));
			}
			this.isIndeterminate = false;
		},
		changeUser(val) {
			const checkedCount = val.length;
			this.detailForm.isSelectAll = checkedCount === this.filterUserList.length ? 1 : 0;
			this.isIndeterminate = checkedCount > 0 && checkedCount < this.filterUserList.length;
		},
		//查询课程
		queryCourseData() {
			const API = 'selectCourseNameAndCid'; //接口
			this.$axios[API](JSON.stringify({ pageNum: 1, pageSize: 500, queryCourseName: '' }))
				.then(res => {
					if (res.data.success) {
						this.courseList = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},

		// 变更前的明细
		queryDetail(tpid) {
			if (!tpid) return;
			this.$axios
				.selectTrainPlanDetail(JSON.stringify({ tpid }))
				.then(res => {
					if (res.data.success) {
						this.detailForm.auids = res.data.data?.trainPlanUserVOS?.map(item => item.auid) || [];
						this.detailForm.cids = res.data.data?.trainPlanCourseVOS?.map(item => item.cid) || [];
						this.detailForm.isSelectAll = res.data.data.isSelectAll;
						console.log(this.detailForm);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectCourseDetails |' + error);
				});
		},

		// 添加/保存信息
		saveDetail(isClose = true) {
			if (!this.detailForm.planEndTime) {
				return this.$err('请先选择计划结束时间');
			}

			let API = 'addTrainPlan';
			if (this.detailForm.tpid) {
				API = 'updateTrainPlanDetail';
				this.detailForm.typeInterFace = this.titleName == '修改课程' ? 1 : 0;
			}

			this.detailForm.isSelectAll = this.detailForm.auids.length === this.userList.length ? 1 : 0;
			this.$axios[API](JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						isClose && (this.showCom = false);
						this.$emit('close', true);
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},

		//显示弹窗
		async showDetailCom(type, rowData) {
			this.titleName = type;
			await this.queryCourseData();
			if (rowData) {
				this.detailForm = { ...this.detailForm, ...rowData };
				await this.queryDetail(rowData.tpid);
			}
			this.showCom = true;
		},
	},
};
</script>
<style lang="scss">
.trainingPlanDetail {
	main {
		.el-checkbox-group {
			display: flex;
			flex-wrap: wrap;
			gap: 5px;
			.el-checkbox {
				display: flex;
				align-items: center;
				margin-right: 10px;
				.el-checkbox__label {
					line-height: normal;
				}
			}
		}
	}
}
</style>
