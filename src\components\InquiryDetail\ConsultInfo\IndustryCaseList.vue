<template>
	<div class="IndustryCaseList table-wrapper border-top pt5">
		<div class="flex-align-center gap-10">
			<div class="label-title pointer" @click="$emit('openCaseList')">行业案例 <i class="el-icon-arrow-up"></i></div>
			<el-select
				v-model="searchForm.industries"
				placeholder="行业"
				size="small"
				class="w-180"
				popper-class="select-column-4"
				clearable
				filterable
				multiple
				collapse-tags
				@change="queryTableData(1)"
			>
				<el-option v-for="item in industryOptions" :key="item" :label="item" :value="item" :title="item.item"> </el-option>
			</el-select>

			<!-- 模糊查询 -->
			<el-input
				class="searchBox"
				size="small"
				v-model.trim="searchForm.project"
				placeholder="项目"
				@input="queryTableData(1)"
				clearable
			></el-input>
			<el-input
				class="searchBox"
				size="small"
				v-model.trim="searchForm.mainProduct"
				placeholder="主营产品"
				@input="queryTableData(1)"
				clearable
			></el-input>
			<el-input
				class="searchBox"
				size="small"
				v-model.trim="searchForm.mainProcess"
				placeholder="主要工序"
				@input="queryTableData(1)"
				clearable
			></el-input>

			<el-select
				v-model="searchForm.demands"
				placeholder="需求"
				size="small"
				class="w-180"
				popper-class="select-column-3"
				clearable
				filterable
				multiple
				collapse-tags
				@change="queryTableData(1)"
			>
				<el-option v-for="item in productOptions" :key="item" :label="item" :value="item" :title="item.item"> </el-option>
			</el-select>

			<el-input
				class="searchBox"
				size="small"
				v-model.trim="searchForm.area"
				placeholder="地区"
				@input="queryTableData(1)"
				clearable
			></el-input>

			<div class="ml-auto">
				<!-- <el-button type="text" class="el-icon-question" @click="openFeedback">反馈建议</el-button> -->
				<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
			</div>
		</div>

		<!-- 表格主体 -->
		<u-table
			ref="uTableRef"
			class="table-main"
			:row-height="35"
			:max-height="300"
			:total="tablePageForm.total"
			:page-size="tablePageForm.pageSize"
			:current-page="tablePageForm.currentPage"
			:page-sizes="tablePageForm.pageSizes"
			@handlePageSize="handlePageSize"
			@sort-change="sortChange"
			show-header-overflow="title"
			pagination-show
			use-virtual
			stripe
		>
			<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
			<u-table-column
				v-for="(item, index) in tableColumn"
				:key="item.colNo + index"
				:label="item.colName"
				:prop="item.colNo"
				:align="item.align"
				:width="item.width"
				sortable="custom"
				resizable
			>
				<template slot-scope="scope">
					<!-- 各种日期（默认不显示分秒 lineM ） -->
					<Tooltips
						v-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
						:cont-str="dateFormat(scope.row[item.colNo], 'line')"
						:cont-width="(scope.column.width || scope.column.realWidth) - 20"
					/>
					<!-- 样板工厂 -->
					<span v-else-if="item.colNo == 'isModelFactory' && scope.row[item.colNo] !== null">
						<i :class="scope.row[item.colNo] ? 'el-icon-check green' : ''"></i>
					</span>
					<!-- 项目 -->
					<Tooltips
						v-else-if="item.colNo == 'project' && scope.row.number"
						:cont-str="`${scope.row.project}(${scope.row.number})`"
						:cont-width="(scope.column.width || scope.column.realWidth) - 20"
					/>
					<!-- 发布状态 -->
					<el-button
						v-else-if="item.colNo == 'isPublish'"
						type="text"
						size="small"
						:class="!scope.row[item.colNo] ? 'color-999' : ''"
						@click="openPublish(scope.row)"
						>{{ ['未发布', '已发布'][scope.row[item.colNo]] || '未发布' }}</el-button
					>
					<!-- 项目状态 -->
					<Tooltips
						v-else-if="item.colNo == 'status'"
						:class="scope.row[item.colNo] == 0 ? '' : 'green'"
						:cont-str="['未交付', '已交付'][scope.row[item.colNo]]"
						:cont-width="scope.column.width || scope.column.realWidth"
					/>

					<!-- 附件 -->
					<i
						v-else-if="item.colNo == 'fileVOList' && scope.row[item.colNo]"
						class="el-icon-paperclip pointer"
						@click="$refs.CaseFiles.openDialog(scope.row)"
					>
						<span>（{{ scope.row[item.colNo].length }}）</span>
					</i>

					<!-- 链接 -->
					<div v-else-if="item.colNo == 'url' && scope.row[item.colNo]">
						<el-popover placement="right" width="365" trigger="click">
							<div class="fs-12 color-666">链接：</div>
							<div v-for="(uItem, uIndex) in scope.row.url" :key="uIndex" class="flex-justify-between align-center">
								<a :href="encodeURI(uItem)" target="_blank">
									<Tooltips class="max-w-350" :cont-str="encodeURI(uItem)" :cont-width="320" />
								</a>
								<el-button type="text" size="mini" class="el-icon-document-copy" @click="copyToClipboard(encodeURI(uItem))">
								</el-button>
							</div>
							<i slot="reference" class="el-icon-paperclip pointer">
								<span>（{{ scope.row[item.colNo].length }}）</span>
							</i>
						</el-popover>
					</div>

					<!-- 点赞 -->
					<div v-else-if="item.colNo == 'userLikeCount'" class="flex-justify-between align-center">
						<i
							class="icon-third-like pointer fs-18"
							:class="scope.row.currentUserIsLikeCase ? 'red' : ''"
							@click="likeCase(scope.row)"
						></i>

						<span>{{ scope.row[item.colNo] || 0 }}</span>
					</div>

					<!-- 问答 -->
					<div v-else-if="item.colNo == 'question'" class="flex-justify-between align-center">
						<i
							class="icon-third-wenda pointer fs-18"
							:class="scope.row.question ? 'green' : ''"
							@click="openQuestion(scope.row)"
						></i>
						<span>{{ scope.row[item.colNo] || 0 }}</span>
					</div>

					<!-- 默认显示 -->
					<Tooltips
						v-else
						:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
						:cont-width="(scope.column.width || scope.column.realWidth) - 20"
					/>
				</template>
			</u-table-column>
		</u-table>

		<CaseFiles ref="CaseFiles" />
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData, copyToClipboard } from '@/util/tool'; //按需引入常用工具函数
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import { industryOptions, productOptions } from '@/assets/js/inquirySource.js';
import CaseFiles from '@/components/CaseFiles.vue'; //案例附件

// import btnAuth from '@/mixins/btnAuth';

export default {
	name: 'IndustryCaseList', //组件名应同路由名(否则keep-alive不生效)
	// import引入的组件需要注入到对象中才能使用
	components: {
		CaseFiles,
	},
	// mixins: [btnAuth],
	props: {
		detailForm: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			activeTab: 'IndustryCaseList', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 10,
				currentPage: 1,
				pageSizes: [10, 20, 50],
			},
			tableColumn: [
				{ colName: '类型', colNo: 'type', align: 'left', width: '' },
				{ colName: '样板工厂', colNo: 'isModelFactory', align: 'center', width: '' },
				{ colName: '项目', colNo: 'project', align: 'left', width: '' },
				{ colName: '项目状态', colNo: 'status', align: 'center', width: '' },
				{ colName: '行业', colNo: 'industry', align: 'left', width: '' },
				{ colName: '主营产品', colNo: 'mainProduct', align: 'left', width: '' },
				{ colName: '主要工序', colNo: 'mainProcess', align: 'left', width: '' },
				{ colName: '需求', colNo: 'demand', align: 'left', width: '' },
				{ colName: '地区', colNo: 'area', align: 'left', width: '' },
				{ colName: '业务顾问', colNo: 'businessAdviser', align: 'left', width: '' },
				{ colName: '实施顾问', colNo: 'implementName', align: 'left', width: '' },
				{ colName: '创作者', colNo: 'creatorName', align: 'left', width: '' },
				{ colName: '备注', colNo: 'remark', align: 'left', width: '' },
				{ colName: '附件', colNo: 'fileVOList', align: 'right', width: '' },
				{ colName: '链接', colNo: 'url', align: 'center', width: '' },
				// { colName: '发布状态', colNo: 'isPublish', align: 'center', width: '' },
				{ colName: '点赞', colNo: 'userLikeCount', align: 'right', width: '80' },
				// { colName: '问答', colNo: 'question', align: 'right', width: '80' },
			],
			tableColumnCopy: [],
			// 查询表单
			searchForm: {
				area: '',
				demands: [],
				industries: [],
				isPublish: 1,
				mainProcess: '',
				mainProduct: '',
				project: '',
				twid: '',
				// 其他...
			},
			industryOptions,
			productOptions: productOptions.filter(item => item.item != '合伙人'),
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		'detailForm.idid': {
			handler(newVal) {
				if (newVal) {
					const { industries, industry, demands, keyword, case_area, province } = this.detailForm;
					this.searchForm = {
						idid: this.detailForm.idid,
						area: case_area || province || '', // 地区
						demands: demands ? demands : keyword ? keyword : [], //产品（原关键词）
						industries: industries ? industries : industry ? industry : [], //行业
						mainProcess: this.detailForm.mainProcess || '',
						mainProduct: this.detailForm.mainProduct || '',
					};
					this.queryTableData('init');
					// console.log('getCaseSearch', this.searchForm, this.detailForm);
				} else {
					this.$refs.uTableRef?.reloadData([]);
				}
			},
			immediate: true,
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	activated() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 更新询盘中行业案例搜索框快照
		async updateCaseSearch() {
			const API = 'insertOrUpdateInquiryIndustryCaseSearchBoxSnapshot';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.searchForm }));
				if (res.data.success) {
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 打开反馈建议
		openFeedback: debounce(async function (row) {
			return this.$message.warning('正在开发中，暂未开放...');
		}),

		// 打开问答
		openQuestion: debounce(async function (row) {
			return this.$message.warning('正在开发中，暂未开放...');
		}),
		// 点赞
		likeCase: debounce(async function (row) {
			const API = 'updateIndustryCaseLikeStatus';
			try {
				const res = await this.$axios[API](JSON.stringify({ icid: row.icid, likeStatus: row.currentUserIsLikeCase ? 0 : 1 }));
				if (res.data.success) {
					row.currentUserIsLikeCase = row.currentUserIsLikeCase ? 0 : 1;
					row.userLikeCount = row.userLikeCount + (row.currentUserIsLikeCase ? 1 : -1);
					// this.queryTableData();
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),

		// 切换tab
		changeTab() {
			if (this.activeTab == 'IndustryCaseList') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			// 如果查询条件为空，则不查询
			const keys = ['area', 'demands', 'industries', 'mainProcess', 'mainProduct'];
			if (keys.every(key => this.searchForm[key] == '' || this.searchForm[key] == [])) {
				this.tablePageForm.total = 0;
				this.$refs.uTableRef?.reloadData([]);
				return;
			}

			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectIndustryCases'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm, isPublish: 1 }))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
						if (type != 'init' && type != 'refresh') {
							this.updateCaseSearch();
						}
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}, 200),
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		dateFormat, //日期format
		jointString, //拼接字符串
		copyToClipboard, //复制到剪切板
	},
};
</script>

<style lang="scss">
.IndustryCaseList.table-wrapper {
	width: 100%;

	overflow: hidden;
	position: relative;
	.table-main {
		min-height: 100px;
		height: auto !important;
		tr {
			th {
				background-color: #fff;
				vertical-align: top;
				font-size: 12px;
				color: #606266;
				font-weight: 400;
				border-bottom: 1px solid #e9e9e9 !important;
				padding: 10px 0 6px 0;
				padding: 10px 0;
			}
			td {
				height: 35px !important;
				padding: 5px 0;
				font-size: 12px;
				border-color: transparent;
				// 单元格样式
				.cell {
					overflow: hidden;
					vertical-align: top;
				}
			}
		}
	}
}
</style>
