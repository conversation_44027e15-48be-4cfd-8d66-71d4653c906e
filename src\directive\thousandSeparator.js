/* 
自定义指令  - 输入千分位显示
<input v-thousandSeparator></input>
import thousandSeparator from '@/directive/thousand-separator.js'
directives: { thousandSeparator },
*/
const thousandSeparator = {
  bind: function (el) {
    el.addEventListener('input', function (event) {
      let value = event.target.value;
      value = value.replace(/,/g, '');
      value = value.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
      event.target.value = value;
    });
  },
  update: function (el, binding) {
    el.value = binding.value;
  },
};
export default thousandSeparator;
