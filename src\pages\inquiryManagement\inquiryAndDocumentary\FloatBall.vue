<template>
	<!--    全屏容器    -->
	<div
		class="floating-ball"
		ref="FloatBallRef"
		@mousemove="mouseMove"
		@mouseup="mouseUp"
		:style="{ 'z-index': mouseDownState ? 999 : '' }"
	>
		<!--  点击蒙版  -->
		<div v-if="openMenu" @click.stop="closeMenu" class="floating-ball-modal"></div>
		<div
			class="menu-wrapper"
			:class="{ 'menu-open': openMenu, 'menu-modal': !openMenu }"
			ref="actionMgr"
			:style="position"
			@mousedown.stop="mousedown"
		>
			<div class="list-wrapper flex-column fs-14" v-if="openMenu">
				<div class="label-title green p5 fs-14">
					最近操作的询盘
					<el-tooltip content="保存最近操作过的询盘（30个），超出后会保留新数据，移出旧数据" effect="light" placement="top">
						<i class="el-icon-warning-outline ml10"></i>
					</el-tooltip>
				</div>
				<div class="flex-center W100 H100" v-if="inquiryList.length == 0">
					<span>暂无数据...</span>
				</div>
				<div
					class="flex-align-center p5"
					v-for="item in inquiryList"
					:key="item.idid"
					:label="jointString(' ', item.number, item.companyName)"
					:value="item.idid"
					@click.stop="openDetail('修改', item)"
				>
					<!-- <Tooltips
						class="w-300 hover-green fs-12 "
						:cont-str="jointString(' ', item.number, item.companyName || item.registeredBusinessName, item.customerName)"
						:cont-width="200"
					/> -->
					<span
						class="ellipsis w-300 hover-green fs-12"
						:title="jointString(' ', item.number, item.companyName || item.registeredBusinessName)"
					>
						{{ jointString(' ', item.number, item.companyName || item.registeredBusinessName, item.customerName) }}
					</span>
					<span class="fs-12 mr5">{{ item.time && dateFormat(item.time, 'MD') }}</span>
					<i class="el-icon-circle-close mr5 pointer" @click.stop="removeInquiry(item)"></i>
				</div>
			</div>

			<div v-if="!openMenu" class="ball-icon" @click="openMenu = !openMenu">
				<i class="icon-kehuxunpan iconfont fs-18"></i>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';
export default {
	name: 'FloatBall',
	props: {
		// 通过position来设置初始定位
		position: {
			type: Object,
			default: function () {
				return {
					top: '50%',
					right: '0',
				};
			},
		},
	},
	data() {
		return {
			openMenu: false, //  菜单展开状态
			mouseDownState: false, //  鼠标点击状态
			iX: 0,
			iY: 0,
			dX: 1920,
			dY: 500, //  初始定位
			lastMoveIndex: 0, //  拖拽计数
			curMoveIndex: 0, //  历史计数
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['inquiryList']),
	},
	methods: {
		// 移除询盘
		removeInquiry(item) {
			this.$store.commit('removeInquiry', item);
		},
		// 调用父组件的询盘详情
		openDetail(type, row, api) {
			this.$emit('openDetail', type, row, api);
			this.openMenu = !this.openMenu;
		},
		//  鼠标按下
		mousedown(event) {
			//  如果打开了菜单，则不做响应
			if (this.openMenu) {
				this.mouseDownState = false;
				return;
			}
			console.log('mousedown', event);
			/* 此处判断  pc 或 移动端 得到 event 事件 */
			let touch;
			if (event.touches) {
				touch = event.touches[0];
			} else {
				touch = event;
			}
			// 鼠标点击 面向页面 的 x坐标 y坐标
			const { clientX, clientY } = touch;
			// 鼠标x坐标 - 拖拽按钮x坐标  得到鼠标 距离 拖拽按钮 的间距
			this.iX = clientX - this.$refs.actionMgr.offsetLeft;
			// 鼠标y坐标 - 拖拽按钮y坐标  得到鼠标 距离 拖拽按钮 的间距
			this.iY = clientY - this.$refs.actionMgr.offsetTop;
			// 设置当前 状态为 鼠标按下
			this.mouseDownState = true;
		},
		//  鼠标拖拽
		mouseMove(event) {
			//鼠标按下 切移动中
			if (this.mouseDownState) {
				console.log('mouseMove', event);
				/* 此处判断  pc 或 移动端 得到 event 事件 */
				let touch;
				if (event.touches) {
					touch = event.touches[0];
				} else {
					touch = event;
				}
				// 鼠标移动时 面向页面 的 x坐标 y坐标
				const { clientX, clientY } = touch;
				//当前页面全局容器 dom 元素  获取容器 宽高
				const { clientHeight: FloatBallY, clientWidth: FloatBallX } = this.$refs.FloatBallRef;
				/* 鼠标坐标 - 鼠标与拖拽按钮的 间距坐标  得到 拖拽按钮的 左上角 x轴y轴坐标 */
				let [x, y] = [clientX - this.iX, clientY - this.iY];

				//拖拽按钮 dom 元素  获取 宽高 style 对象
				const { clientHeight: actionMgrY, clientWidth: actionMgrX, style: actionMgrStyle } = this.$refs.actionMgr;
				/* 此处判断 拖拽按钮 如果超出 屏幕宽高 或者 小于
					 设置 屏幕最大 x=全局容器x y=全局容器y 否则 设置 为 x=0 y=0
				*/
				if (x > FloatBallX - actionMgrX) x = FloatBallX - actionMgrX;
				else if (x < 0) x = 0;
				if (y > FloatBallY - actionMgrY) y = FloatBallY - actionMgrY;
				else if (y < 0) y = 0;
				this.dX = x;
				this.dY = y;
				// 计算后坐标  设置 按钮位置
				actionMgrStyle.left = `${x}px`;
				actionMgrStyle.top = `${y}px`;
				actionMgrStyle.bottom = 'auto';
				actionMgrStyle.right = 'auto';
				//  move Index
				this.lastMoveIndex++;
				//  当按下键滑动时， 阻止屏幕滑动事件
				event.preventDefault();
			}
		},
		//    鼠标抬起
		mouseUp(event) {
			console.log('mouseUp', event);
			//  当前页面全局容器 dom 元素  获取容器 宽高
			const { clientHeight: windowHeight, clientWidth: windowWidth } = document.documentElement;
			// console.log('全局容器:', windowWidth, windowHeight);
			//  拖拽按钮 dom 元素  获取 宽高 style 对象
			const { clientHeight: actionMgrY, clientWidth: actionMgrX, style: actionMgrStyle } = this.$refs.actionMgr;

			// console.log('拖拽按钮', actionMgrY, actionMgrX, actionMgrStyle);

			// 计算后坐标  设置 按钮位置
			if (this.dY > 0 && this.dY < windowHeight - 50) {
				//  不在顶部 且 不在底部
				if (this.dX <= windowWidth / 2) {
					//  left 小于等于屏幕一半
					actionMgrStyle.left = 0;
					actionMgrStyle.right = 'auto';
				} else {
					//  left 大于屏幕一半
					actionMgrStyle.left = 'auto';
					actionMgrStyle.right = 0;
				}
				if (this.dY >= windowHeight / 2) {
					//  宽度大于1/2时，是将top改为auto，调整bottom
					actionMgrStyle.top = 'auto';
					actionMgrStyle.bottom = windowHeight - this.dY - 50 + 'px';
				}
			} else {
				if (this.dY === 0) {
					//  在顶部
					actionMgrStyle.top = 0;
					actionMgrStyle.bottom = 'auto';
				} else if (this.dY === windowHeight - 50) {
					actionMgrStyle.bottom = 0;
					actionMgrStyle.top = 'auto';
				}
				if (this.dX >= windowWidth / 2) {
					//  右侧是将left改为auto，调整right
					actionMgrStyle.left = 'auto';
					actionMgrStyle.right = windowWidth - this.dX - 50 + 'px';
				}
			}
			this.mouseDownState = false;
		},
		// 单击事件
		clickBall() {
			this.openMenu = !this.openMenu;
		},
		// 点击空白关闭菜单
		closeMenu() {
			// this.curMoveIndex = this.lastMoveIndex;
			this.openMenu = false;
		},
		jointString: _.jointString, // 拼接字符串
		dateFormat: _.dateFormat, //日期format
	},
};
</script>

<style lang="scss" scoped>
.floating-ball {
	position: absolute;
	top: 0;
	height: 100%;
	width: 100%;
	cursor: pointer;
	&-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 998;
	}
}

// 菜单 - 打开
.menu-open {
	position: fixed;
	z-index: 9999;
	width: 15vw;
	height: 20vh;
	border-radius: 5px;
	border: #fafafa solid 1px;
	background: #fafafa;
	color: #666;
	transition: 0.5s;
}
// 菜单modal
.menu-modal {
	/* 如果碰到滑动问题，1.3 请检查 z-index。z-index需比web大一级*/
	z-index: 9999;
	position: fixed;
	width: 45px;
	height: 45px;
	border-radius: 50%;
	background-color: #28d094;
	line-height: 35px;
	text-align: center;
	color: #fff;
	opacity: 0.6;
	transition: 0.5s;
}
.menu-modal:hover {
	opacity: 1;
}
.list-wrapper {
	width: 15vw;
	height: 20vh;
	overflow-y: auto;
}
.ball-icon {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
}
</style>
