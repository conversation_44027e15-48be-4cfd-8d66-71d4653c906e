'use strict';
const path = require('path');
const config = require('../config');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const packageConfig = require('../package.json');

exports.assetsPath = function (_path) {
	const assetsSubDirectory =
		process.env.NODE_ENV === 'production' ? config.build.assetsSubDirectory : config.dev.assetsSubDirectory;

	return path.posix.join(assetsSubDirectory, _path);
};

/* 生成css样式Loader */
exports.cssLoaders = function (options) {
	options = options || {};

	const cssLoader = {
		loader: 'css-loader',
		options: {
			sourceMap: options.sourceMap,
		},
	};

	const postcssLoader = {
		loader: 'postcss-loader',
		options: {
			sourceMap: options.sourceMap,
		},
	};

	// generate loader string to be used with extract text plugin
	function generateLoaders(loader, loaderOptions) {
		const loaders = options.usePostCSS ? [cssLoader, postcssLoader] : [cssLoader];

		if (loader) {
			loaders.push({
				loader: loader + '-loader',
				options: Object.assign({}, loaderOptions, {
					sourceMap: options.sourceMap,
				}),
			});
		}
		// 提取css （生产环境）
		if (options.extract) {
			return [
				{
					loader: MiniCssExtractPlugin.loader,
				},
			].concat(loaders);
		} else {
			return [
				{
					loader: 'style-loader',
				},
			].concat(loaders);
		}
	}

	// https://vue-loader.vuejs.org/en/configurations/extract-css.html
	return {
		css: generateLoaders(),
		postcss: generateLoaders(),
		sass: generateLoaders('sass', { indentedSyntax: true }),
		scss: generateLoaders('sass'),
	};
};

/* 生成单独的样式文件 （外部样式文件） */
exports.styleLoaders = function (options) {
	const output = [];
	const loaders = exports.cssLoaders(options);

	for (const extension in loaders) {
		const loader = loaders[extension];
		output.push({
			test: new RegExp('\\.' + extension + '$'),
			use: loader,
		});
	}

	return output;
};

/* 信息提示 */
exports.createNotifierCallback = () => {
	const notifier = require('node-notifier');

	return (severity, errors) => {
		if (severity !== 'error') return;

		const error = errors[0];
		const filename = error.file && error.file.split('!').pop();

		notifier.notify({
			title: packageConfig.name,
			message: severity + ': ' + error.name,
			subtitle: filename || '',
			icon: path.join(__dirname, 'logo.png'),
		});
	};
};
