/**
 * 权限、菜单相关
 */
const state = {
	menuTitle: {
		id: '',
		menuTitleName: '',
		node: {
			id: '',
			menuTitleName: '',
			node: {
				id: '',
				menuTitleName: '',
			},
		},
	},
	isJuxinCity: false, //是否聚心城人员
	menuList: [], //菜单列表
	buttonMap: null, //按钮权限
	userInfos: null, //登录后获取的用户信息
	pswSimple: 0, //密码简单判断
};

const actions = {};

const getters = {
	isJuxinCity: state => state.isJuxinCity,
	menuTitle: state => state.menuTitle, //菜单id用于权限判断
	menuList: state => state.menuList, //菜单列表
	buttonMap: state => state.buttonMap, //按钮权限
	userInfos: state => state.userInfos, //登录后获取的用户信息
	userInfo: state => state.userInfos?.adminUserVO || {}, //用户个人信息
	userLabels: state => state.userInfos?.adminUserVO?.adminUserLabelVOS || [], //用户标签
	userRoles: state => state.userInfos?.adminUserVO?.adminRoleVOS || [], //用户角色
	pswSimple: state => state.pswSimple, //密码简单判断
};

const mutations = {
	setMenuTitle(state, res) {
		const { id, menuTitleName, node } = res;
		state.menuTitle.id = id;
		state.menuTitle.menuTitleName = menuTitleName;
		if (node) {
			state.menuTitle.node = {
				...state.menuTitle.node,
				...{
					id: node.id,
					menuTitleName: node.menuTitleName,
					node: node.node ? { id: node.node.id, menuTitleName: node.node.menuTitleName } : null,
				},
			};
		}
	},

	setButtonMap(state, res) {
		state.buttonMap = res;
	},
	setUserInfos(state, res) {
		state.userInfos = JSON.parse(JSON.stringify(res));
		state.menuList = res.adminMenuAndButtonVO.menuList || [];
	},
	setIsJuxinCity(state, res) {
		state.isJuxinCity = res;
		console.log('🔶 是否聚心城人员:', res);
	},
	setpswSimple(state, res) {
		state.pswSimple = res;
	},
};

export default {
	state,
	actions,
	getters,
	mutations,
};
