# InquiryAudioPlayer 询盘录音播放器组件

一个专为询盘场景设计的音频播放器组件，基于AudioPlayer组件封装，提供询盘录音播放和进度记录功能。

## 功能特点

- 基于AudioPlayer的音频播放控制
- 询盘录音播放进度记录与同步
- 自动查询和更新询盘录音信息
- 记录已听时长和播放进度
- 与后端API集成

## 使用方法

```vue
<template>
  <InquiryAudioPlayer 
    :audioUrl="recordUrl" 
    :idid="inquiryId"
    @playEnded="onPlayEnded"
  />
</template>

<script>
import InquiryAudioPlayer from '@/components/InquiryAudioPlayer';

export default {
  components: {
    InquiryAudioPlayer
  },
  data() {
    return {
      recordUrl: 'https://example.com/audio.mp3',
      inquiryId: '12345'
    }
  },
  methods: {
    onPlayEnded(data) {
      console.log('播放结束', data);
      // 可以在这里执行其他操作
    }
  }
}
</script>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| audioUrl | String | '' | 录音文件URL（必填） |
| idid | String | '' | 询盘ID（必填） |

## 事件

| 事件名 | 参数 | 说明 |
|-------|------|------|
| playStart | stats对象 | 开始播放时触发 |
| playPause | stats对象 | 暂停播放时触发 |
| playEnded | stats对象 | 播放结束时触发 |

## 事件参数 (stats对象)

事件参数包含以下字段：

```js
{
  // AudioPlayer的stats字段
  playCount: Number,        // 播放次数
  playDuration: Number,     // 总播放时长(秒)
  currentSessionDuration: Number, // 当前会话播放时长(秒)
  lastPlayedAt: Date,       // 最后播放时间
  playHistory: Array,       // 播放历史记录
  isPlaying: Boolean,       // 是否正在播放
  currentProgress: Number,  // 当前播放进度(百分比)
  
  // 询盘数据
  inquiryData: {
    idid: String,           // 询盘ID
    listeningProgressRatio: Number, // 听音进度比例
    listenedDuration: Number, // 已听时长(秒)
    recordingDuration: Number, // 录音总时长(秒)
    recordUrl: String       // 录音URL
  }
}
```

## 方法

| 方法名 | 参数 | 返回值 | 说明 |
|-------|------|-------|------|
| getPlayStats | 无 | stats对象 | 获取播放统计信息 |

## 工作流程

1. 组件初始化时，通过idid自动查询询盘详情
2. 开始播放时触发playStart事件，并查询询盘信息
3. 暂停播放或播放结束时，自动向后端更新已听时长
4. 组件会自动跟踪和同步播放进度到后端

## 集成的API

组件集成了以下后端API：

1. `selectInquiryDocumentaryOne`: 查询询盘详情
2. `updateListenedDurationByIdid`: 更新已听时长


## 注意事项

1. 组件需要提供正确的询盘ID(idid)才能正常工作
2. 组件会自动处理录音进度的同步，无需手动调用更新方法
3. 播放暂停或结束时会自动向后端同步已听时长
4. 需确保后端API正确配置，否则可能影响进度同步 