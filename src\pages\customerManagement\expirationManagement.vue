<template>
	<div id="expirationManagement" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<ExpirationList ref="ExpirationList" :twidList="searchForm.twidList" :channelName="searchForm.channelName" />
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="team"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twidList = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab">
			<el-tab-pane label="到期河流图" name="expirationManagement">
				<BaseLayout>
					<template #header>
						<span class="search-label">年度</span>
						<el-date-picker
							size="small"
							class="w-150"
							v-model="selectTime"
							:default-value="selectTime"
							type="year"
							value-format="timestamp"
							format="yyyy 年"
							placeholder="请选择年份"
							:clearable="false"
							@change="changeDateSelect"
						>
						</el-date-picker>

						<el-radio-group v-model="searchForm.isConditions" @change="queryTableData(1)">
							<el-radio :label="1">按客数</el-radio>
							<el-radio :label="0">按金额</el-radio>
						</el-radio-group>
						<el-radio-group v-model="searchForm.isPeople" @change="queryTableData(1)">
							<el-radio :label="1">业务顾问视图</el-radio>
							<el-radio :label="0">实施顾问视图</el-radio>
						</el-radio-group>
						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
						</div>
					</template>
					<template #main>
						<div class="table-toolbar">
							<ExportBtn @trigger="openExport" />
						</div>

						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							pagination-show
							use-virtual
							stripe
							show-summary
							:summary-method="summaryMethod"
						>
							<u-table-column label="序号" width="50" align="center" type="index"> </u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<div
										:class="{ red: getExpireColor(item.colNo) }"
										class="hover-green"
										v-if="item.colNo != 'name' && item.colNo != 'totalAnnualFee' && scope.row[item.colNo]"
										@click="openList(item.colNo, scope.row)"
									>
										{{ scope.row[item.colNo].amoney }}
									</div>
									<!-- 合计 -->
									<Tooltips
										v-else-if="item.colNo == 'totalAnnualFee' && scope.row[item.colNo]"
										class="hover-green"
										@click.native="openList(item.colNo, scope.row)"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<script>
import { debounce, dateFormat, jointString, sortTableData, accAdd } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import ChannelSelect from '@/components/ChannelSelect.vue';
import ExportTable from '@/components/ExportTable'; //导出组件
import ExpirationList from './components/ExpirationList.vue';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	name: 'expirationManagement',
	components: { ChannelSelect, ExportTable, ExpirationList, ExportBtn },
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			activeTab: 'expirationManagement', //激活tab页
			//日期相关
			selectTime: new Date(),
			startDate: '',
			endDate: '',
			searchForm: {
				isConditions: 1,
				isPeople: 1,
				channelName: [],
				twidList: [],
			},
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableColumn: [
				{ colName: '业务顾问', colNo: 'name', align: 'left', width: 100 },
				{ colName: '一月', colNo: '1', align: 'right' },
				{ colName: '二月', colNo: '2', align: 'right' },
				{ colName: '三月', colNo: '3', align: 'right' },
				{ colName: '四月', colNo: '4', align: 'right' },
				{ colName: '五月', colNo: '5', align: 'right' },
				{ colName: '六月', colNo: '6', align: 'right' },
				{ colName: '七月', colNo: '7', align: 'right' },
				{ colName: '八月', colNo: '8', align: 'right' },
				{ colName: '九月', colNo: '9', align: 'right' },
				{ colName: '十月', colNo: '10', align: 'right' },
				{ colName: '十一月', colNo: '11', align: 'right' },
				{ colName: '十二月', colNo: '12', align: 'right' },
				{ colName: '合计', colNo: 'totalAnnualFee', align: 'right' },
			],

			openMove: false, //打开组件
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['menuTitle']) },
	// 监控data中的数据变化
	watch: {
		twidList(newVal) {
			this.queryTableData();
		},
		'searchForm.isPeople'(newVal) {
			this.tableColumn[0].colName = newVal == 1 ? '业务顾问' : '实施顾问';
		},

		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		const nowYear = new Date().getFullYear();
		this.changeDateSelect(new Date(nowYear, '0', '1').getTime());
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},

	// 方法集合
	methods: {
		getExpireColor(number) {
			const nowYear = new Date().getFullYear();
			const selectYear = new Date(this.selectTime).getFullYear();
			// 如果是今年则比较number和当前月份的大小小于返回true大于返回false，
			if (selectYear == nowYear) {
				if (number < new Date().getMonth() + 1) {
					return true;
				} else {
					return false;
				}
			} // 如果小于当前今年则返回true，如果大于当前则返回false
			if (selectYear < nowYear) {
				return true;
			} else {
				return false;
			}
		},
		// 将金额转成万单位
		convertToMillion(value, d = 2) {
			if (!value || Number(value) <= 0) return '';
			// Convert the value to million units
			const valueInMillion = value / 10000;

			// Round to two decimal places
			const resultVal = (Math.round(valueInMillion * 100) / 100).toFixed(2);
			const decimalPart = resultVal.split('.')[1];
			const decimalsToAdd = 2 - decimalPart.length;
			const finalValue = decimalsToAdd > 0 ? resultVal + '0'.repeat(decimalsToAdd) : resultVal;
			return finalValue;
		},
		// 交付清单
		openList(monthIndex, row) {
			let searchForm = {};
			if (row) {
				const { isPeople } = this.searchForm;
				searchForm = {
					consultantName: !isPeople ? row.name : '',
					salesName: isPeople ? row.name : '',
				};
			}

			this.$refs.ExpirationList.showList(monthIndex, row, searchForm, this.startDate);
		},
		// 获取河流数据
		queryTableData: debounce(function (type) {
			this.tableData = [];
			const str = JSON.stringify({
				endDate: this.endDate,
				startDate: this.startDate,
				...this.searchForm,
			});
			this.$axios
				.selectMaturityManagement(str)
				.then(res => {
					if (res.data.success) {
						res.data.data.map(item => {
							// item.totalAnnualFee = this.convertToMillion(item.totalAnnualFee);
							item.userMonthDateListVo.map(monthItem => {
								const monthObject = {
									// amoney: this.convertToMillion(monthItem.amoney),
									amoney: monthItem.amoney.toFixed(this.searchForm.isConditions == 1 ? 0 : 2),
									requestQty: monthItem.requestQty,
									status: monthItem.status,
								};
								item[Number(monthItem.month)] = monthObject;
							});
						});
						// 去掉最后一行合计
						this.tableData = res.data.data.slice(0, -1).sort((a, b) => b.totalAnnualFee - a.totalAnnualFee);
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
						this.sortChange(this.tableSort, true);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectMaturityManagement |' + error);
				});
		}),

		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = [];
			columns.forEach((column, columnIndex) => {
				if (columnIndex === 0) {
					means[columnIndex] = '合计';
				} else if (columnIndex === 1) {
					means[columnIndex] = '';
				} else {
					const values = data?.map(item => {
						return Number(item[column.property]?.amoney || item[column.property]);
					});

					if (values.some(value => !isNaN(value))) {
						means[columnIndex] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return accAdd(prev, curr, this.searchForm.isConditions == 1 ? 0 : 2);
							} else {
								return prev;
							}
						}, 0);
						if (!isNaN(means[columnIndex])) {
							means[columnIndex] = (
								<span
									class="hover-green"
									on-click={e => {
										e.stopPropagation();
										this.openList(columnIndex - 1, null);
									}}
								>
									{means[columnIndex]}
								</span>
							);
						} else {
							means[columnIndex] = '';
						}
					} else {
						means[columnIndex] = '';
					}
				}
			});
			return [means];
		},

		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 时间选择
		changeDateSelect(date) {
			const year = new Date(date).getFullYear();
			this.startDate = new Date(year, '0', '1').getTime();
			this.endDate = new Date(year + 1, '0', '1').getTime() - 1;
			// console.log(date);
			this.queryTableData(1);
		},
		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					endDate: this.endDate,
					startDate: this.startDate,
					...this.searchForm,
				}), //接口参数
				API: 'exportSelectMaturityManagement', //导出接口
				downloadData: '到期管理导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
#expirationManagement {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
