<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<!-- 解决阿里云访问限制，请求的时候会带浏览器信息到阿里云 -->
		<meta name="referrer" content="no-referrer" />
		<!-- 强制Chromium内核，作用于360浏览器、QQ浏览器等国产双核浏览器 -->
		<meta name="renderer" content="webkit" />
		<!-- 强制Chromium内核，作用于其他双核浏览器 -->
		<meta name="force-rendering" content="webkit" />
		<!-- 禁用缓存 -->
		<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
		<meta http-equiv="Pragma" content="no-cache" />
		<meta http-equiv="Expires" content="0" />
		<meta http-equiv="X-UA-Compatible" content="ie=edge,chrome=1" />
		<meta
			name="viewport"
			content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
		/>
		<meta name="keywords" content="lightMES | OPS" />
		<meta name="description" content="lightMES | OPS" />
		<title>lightMES | OPS</title>

		<!-- 其他标签 -->
		<% htmlWebpackPlugin.options.cdnConfig.forEach(function(item){ if(item.css){ %>
		<link href="<%= item.css %>" rel="stylesheet" />
		<% }}) %>
		<!-- 其他标签 -->
		<% htmlWebpackPlugin.options.cdnConfig.forEach(function(item){ if(item.js && !htmlWebpackPlugin.options.onlyCss){ %>
		<script type="text/javascript" src="<%= item.js %>" ></script>
		<% }}) %>

	</head>
	
	<body>
		<div id="app"></div>
		<!-- built files will be auto injected -->
	</body>
</html>