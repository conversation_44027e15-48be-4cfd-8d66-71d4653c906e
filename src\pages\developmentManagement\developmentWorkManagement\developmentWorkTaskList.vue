selectBugCount
<template>
	<div id="developmentWorkTaskList" :class="moveToggle ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<!-- 任务明细弹窗 -->
		<TaskDetail ref="TaskDetail" @close="queryTableData(1)" />
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />

		<!-- 表格内容 -->
		<BaseLayout :showHeader="false">
			<template #main>
				<div class="table-toolbar">
					<div class="mr-auto">
						<el-button v-if="searchForm.productUname || searchForm.projectTurnTest" type="text" class="el-icon-user">
							{{ searchForm.productUname || searchForm.projectTurnTest }}
						</el-button>
						<el-button v-if="parentType == 'river'" type="text" class="el-icon-monitor">
							开发任务清单 （ {{ $moment(searchForm.dateStart || searchForm.startTime).format('YYYY-MM-DD') }} -
							{{ $moment(searchForm.dateEnd || searchForm.endTime).format('YYYY-MM-DD') }} ）
						</el-button>
						<el-button v-if="parentType == 'result'" type="text" class="el-icon-finished">
							已完成任务清单 （
							{{ $moment(searchForm.dateStart || searchForm.testPassStartTime).format('YYYY-MM-DD') }} -
							{{ $moment(searchForm.dateEnd || searchForm.testPassEndTime).format('YYYY-MM-DD') }} ）
						</el-button>
					</div>

					<ExportBtn @trigger="openExport" />
					<el-button type="text" class="el-icon-arrow-left" @click="moveToggle = false">返回</el-button>
				</div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable
						resizable
					>
						<template slot-scope="scope">
							<div
								v-if="
									item.colNo == 'startTime' ||
									item.colNo == 'endTime' ||
									item.colNo == 'testPassTime' ||
									item.colNo == 'actualEndTime'
								"
							>
								<Tooltips
									:cont-str="dateFormat(scope.row[item.colNo], 'YMD')"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>
							<div v-else-if="item.colNo == 'status'">
								<Tooltips
									:cont-str="statusMap[scope.row[item.colNo]]"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>
							<div v-else-if="item.colNo == 'turnQuality' || item.colNo == 'taskOtd'">
								<Tooltips
									style="color: red; font-weight: bold"
									:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '%' : ''"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>
							<div v-else-if="item.colNo == 'taskClassify'">
								<Tooltips
									:cont-str="taskClassifyMap[scope.row[item.colNo]]"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>
							<div v-else-if="item.colNo == 'taskLevel'">
								<Tooltips
									:cont-str="taskLevelMap[scope.row[item.colNo]]"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>

							<div v-else-if="item.colNo == 'taskNo'">
								<Tooltips
									:style="{ color: colorMap[scope.row.status], 'font-weight': 'bold' }"
									class="hover-green"
									@click.native="openDetail(scope.row, '任务详情信息')"
									:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
									:cont-width="(scope.column.width || scope.column.realWidth) - 18"
								>
								</Tooltips>
							</div>

							<div v-else-if="item.colNo == 'mentionUid' || item.colNo == 'productUid' || item.colNo == 'projectTurnCheck'">
								<Tooltips
									:cont-str="scope.row[item.colNo].userName ? scope.row[item.colNo].userName + '' : ''"
									:cont-width="(scope.column.width || scope.column.realWidth) - 18"
								>
								</Tooltips>
							</div>

							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import TaskDetail from './components/taskDetailCom'; //任务明细弹窗
import ExportTable from '@/components/ExportTable';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		TaskDetail,
		ExportBtn,
	},
	props: { parentType: String },
	name: 'developmentWorkTaskList',
	data() {
		return {
			tableColumn: [
				{ colName: '任务编号', colNo: 'taskNo', align: 'left', width: '110' },
				{ colName: '项目名称', colNo: 'projectName', align: 'left', width: '150' },
				{ colName: '任务', colNo: 'taskName', align: 'left', width: '200' },
				{ colName: '产出工时', colNo: 'productTime', align: 'right', width: '' },
				{ colName: '开发人员', colNo: 'productUid', align: 'left', width: '100' },
				{ colName: '要求转测日期', colNo: 'endTime', align: 'center', width: '120' },
				{ colName: '实际转测日期', colNo: 'actualEndTime', align: 'center', width: '120' },
				{ colName: '准交率', colNo: 'taskOtd', align: 'right', width: '80' },
				{ colName: '转测次数', colNo: 'projectTurnCount', align: 'right', width: '80' },
				{ colName: '转测质量', colNo: 'turnQuality', align: 'right', width: '80' },
				{ colName: '研发绩效工时', colNo: 'performance', align: 'right', width: '120' },
				{ colName: '测试产出工时', colNo: 'testProductTime', align: 'right', width: '120' },
				{ colName: '测试通过日期', colNo: 'testPassTime', align: 'center', width: '120' },
				{ colName: '测试人', colNo: 'projectTurnCheck', align: 'left', width: '' },
			], //当前显示列
			searchForm: {
				statusList: [],
				startTime: '',
				endTime: '',
				productUname: '',
				testPassStartTime: '',
				testPassEndTime: '',
			},
			colorMap: {
				0: '#ec808d', //红 开发延误
				1: '#bababa', //灰 计划中
				2: '#ab47bc', //紫 开发中
				3: '#2196f3', //蓝 已转测
				4: '#28d094', //绿 完成
				5: '#facd91', //橙 转测不通过
				6: '#ec808d', //红 测试延误
			},

			statusMap: {
				0: '延误',
				1: '计划中',
				2: '开发中',
				3: '已转测',
				4: '完成',
				5: '转测不通过',
				6: '测试延误',
			},

			taskClassifyMap: {
				0: '需求',
				1: '优化',
				2: 'Bug',
				3: '杂项',
			},
			taskLevelMap: {
				0: '高',
				1: '中',
				2: '低',
			},

			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 200, 500, 1000],
			},

			moveToggle: false, //滑动控制
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		// productUname() {
		// 	this.queryTableData(1);
		// },

		moveToggle(newVal) {
			if (newVal) {
				this.queryTableData(1);
			} else {
				this.tableData = [];
				this.tablePageForm.total = 0;
				this.$refs.uTableRef.reloadData(this.tableData);
				this.$emit('close');
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		openDetail(row) {
			this.$refs.TaskDetail.openDetail(row);
		},
		queryTableData: _.debounce(function (type) {
			if (!this.moveToggle) {
				this.tableData = [];
				return;
			}
			type && (this.tablePageForm.currentPage = 1);

			const str = JSON.stringify({
				queryParam: this.searchForm.productUname,
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.searchForm,
			});
			this.$axios
				.selectTaskDetail(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							this.$refs.uTableRef?.reloadData(this.tableData);
							this.$refs.uTableRef?.doLayout();
						});
					} else {
						this.tableData = [];
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {

					console.log('selectTaskDetail |' + error);
				});
		}),
		//日期format
		dateFormat: _.dateFormat,
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//自定义排序
		sortChange({ prop, order }) {
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop == 'productUid' || prop == 'projectTurnCheck' || prop == 'mentionUid' || prop == 'technicalManager') {
						if (!a?.userName) return -1;
						if (!b?.userName) return 1;
						return a?.userName.localeCompare(b?.userName, 'zh-CN');
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		//数据导出
		openExport: _.debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					queryParam: this.searchForm.productUname,
					pageNum: this.tablePageForm.currentPage,
					pageSize: this.tablePageForm.pageSize,
					...this.searchForm,
				}), //接口参数
				API: 'exportTaskDetail', //导出接口
				downloadData: '任务明细导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),

		//打开当前组件 （任务列表）
		openTaskList(searchForm) {
			this.searchForm = _.deepClone(searchForm);
			this.moveToggle = true;
			this.queryTableData(1);
		},
	},
};
</script>

<style lang="scss" scoped>
#developmentWorkTaskList {
	// width: 100%;
	// overflow: hidden;
	// position: relative;
	.table-main {
		width: 100%;
		height: 100%;
		min-height: 400px;
		height: calc(100vh - 230px) !important;
	}
}
</style>
