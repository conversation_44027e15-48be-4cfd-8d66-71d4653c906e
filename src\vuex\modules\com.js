/**
 * 通用配置（或者未分类的配置）
 */
import axios from '@/fetch';

/**
 * state - 存储模块的状态数据
 * 包含网络状态、用户列表、驾驶舱数据和未回复数等状态
 * 所有组件都可以通过 this.$store.state.xxx 或 mapState 访问
 */
const state = {
	network: true, //联网判断

	teamWorkList: [], //当前用户所有分销/代理
	userList: [], //用户列表
	dashboardList: [], //驾驶舱数据

	qAndABadgeNum: 0, //问答badge数
	unReplyCount: 0, //未回复数
};

/**
 * actions - 处理异步操作和业务逻辑
 * 可以包含异步API调用，然后通过commit提交mutation来修改state
 * 组件中通过 this.$store.dispatch('xxx') 或 mapActions 调用
 */
const actions = {
	// 查询当前用户所有分销/代理
	async queryTwidOptions({ commit }) {
		const API = 'selectUserTeamwork';
		try {
			const res = await axios[API](JSON.stringify({ twids: [], counselor: '' }), { skipCancel: true });
			if (res.data.success) {
				const teamWorkList = Object?.entries(res.data.data?.teamworkMap || {})?.map(([twid, twName]) => ({
					teamworkId: twid,
					teamworkName: twName,
					twid,
					twName,
				}));
				commit('setTeamWorkList', teamWorkList || []);
			} else {
				this.$err(res.data.message);
			}
		} catch (error) {
			console.error(`${API} | ${error}`);
		}
	},
	// 查询当前登录的用户所属渠道/代理下用户（不限制范围）
	async queryUserByTwidsSimple({ commit }) {
		const API = 'selectSalesmanByTwidsSimple';
		try {
			const res = await axios[API](JSON.stringify({ twids: [], counselor: '' }), { skipCancel: true });
			if (res.data.success) {
				commit('setUserList', res.data.data || []);
			} else {
				this.$err(res.data.message);
			}
		} catch (error) {
			console.error(`${API} |` + error);
		}
	},
	// 全局初始化数据
	async initGlobalData({ dispatch }) {
		await dispatch('queryTwidOptions');
		await dispatch('queryUserByTwidsSimple');
	},
};

/**
 * getters - 从state派生出的状态
 * 类似于计算属性，用于对state中的数据进行加工处理
 * 组件中通过 this.$store.getters.xxx 或 mapGetters 访问
 */
const getters = {
	network: state => state.network, //联网判断
	teamWorkList: state => state.teamWorkList, //当前用户所有分销/代理
	userList: state => state.userList, //用户列表
	dashboardList: state => state.dashboardList, //驾驶舱数据

	qAndABadgeNum: state => state.qAndABadgeNum, //问答badge数
	unReplyCount: state => state.unReplyCount, //未回复数
};

/**
 * mutations - 用于修改state的唯一方法
 * 必须是同步函数，直接变更state状态
 * 组件中通过 this.$store.commit('xxx') 或 mapMutations 调用
 */
const mutations = {
	changeNetwork(state, res) {
		state.network = res;
	},
	setTeamWorkList(state, res) {
		state.teamWorkList = res;
	},
	setUserList(state, res) {
		state.userList = res;
	},
	setDashboardList(state, list) {
		state.dashboardList = list;
	},
	setQAndABadgeNum(state, res) {
		state.qAndABadgeNum = res;
	},
	setUnReplyCount(state, res) {
		state.unReplyCount = res;
	},
};
export default {
	state,
	actions,
	getters,
	mutations,
};
