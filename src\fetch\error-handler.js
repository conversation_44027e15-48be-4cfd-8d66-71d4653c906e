/**
 * 错误处理模块
 */
import axios from 'axios';
import { Message } from 'element-ui';
import store from '@/vuex';

/**
 * HTTP 错误处理
 * @param {Error} error - 错误对象
 * @returns {Promise} 被拒绝的Promise
 */
export const httpErrorStatusHandle = error => {
	// 处理被取消的请求 - 不显示错误信息
	if (axios.isCancel(error)) {
		return Promise.reject(error);
	}

	let message = '';

	if (error && error.response) {
		// HTTP状态码错误处理
		const errorMessages = {
			204: '请求成功，但没有返回内容。',
			301: '请求的资源已永久移动到新位置。',
			302: '接口重定向了，请联系管理员！',
			400: '参数不正确，请检查后重试！',
			401: '您未登录，或者登录已经超时，请先登录！',
			403: '您没有权限操作，请联系管理员！',
			404: `未找到请求地址"${error.response.config.url}”，请联系管理员！ `,
			408: '请求超时，请稍后再试！',
			409: '请求冲突，资源状态不一致。',
			500: '服务器内部错误，请稍后再试！',
			501: '服务未实现，请稍后再试！',
			502: '网关错误，请稍后再试！',
			503: '服务不可用，请稍后再试！',
			504: '服务暂时无法访问，请稍后再试！',
		};
		if (error && error.response) {
			// HTTP状态码错误处理
			message = errorMessages[error.response.status]
				? `${error.response.status} : ${errorMessages[error.response.status]}`
				: '服务器异常，请联系管理员！';
			Message({ type: 'error', message });
			return Promise.reject(error);
		}
	}

	// 处理网络超时
	if (error.message.includes('timeout')) {
		message = '网络请求超时,请重试';
		Message({ type: 'warning', message });
		return Promise.reject(error);
	}

	// 处理网络异常
	if (error.message.includes('Network')) {
		message = window.navigator.onLine ? '服务端异常！' : '当前网络连接异常,请检查您的网络';
		Message({ type: 'warning', message });
		setTimeout(() => {
			store.commit('changeNetwork', false);
		}, 2000);
	}

	return Promise.reject(error);
};

export default {
	httpErrorStatusHandle,
};
