<template>
	<div id="deliveryPlan">
		<BaseLayout>
			<template #header>
				<label class="search-label">视图类型</label>
				<el-select
					size="small"
					v-model="activeClass"
					placeholder="请选择项目类型"
					:clearable="false"
					filterable
					@change="getGanttView"
				>
					<el-option
						v-for="(item, index) in viewNames"
						:key="index"
						:label="item.name"
						:value="item.id"
						:disabled="item.id == 3 && yearRange == 0"
					>
					</el-option>
				</el-select>

				<label class="search-label">项目类型</label>
				<el-select
					size="small"
					v-model="searchForm.projectType"
					placeholder="请选择项目类型"
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option v-for="item in projectTypeOptions" :key="item.id" :label="item.label" :value="item.id"> </el-option>
				</el-select>

				<label class="search-label">实施顾问</label>
				<el-select
					v-model="searchForm.implementList"
					size="small"
					placeholder="请选择实施顾问"
					multiple
					clearable
					filterable
					collapse-tags
					@change="queryTableData(1)"
				>
					<el-option v-for="item in implementList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
				</el-select>

				<el-input
					class="searchBox"
					size="small"
					clearable
					v-model="searchForm.query"
					placeholder="项目名称"
					@input="queryTableData(1)"
				></el-input>

				<el-checkbox-group v-model="searchForm.statusList" @change="queryTableData(1)">
					<el-checkbox v-for="(item, index) in statuss" :key="'color' + index" :label="item.id">
						<span class="fs-20" :class="[item.color]">●</span>
						<span>{{ item.status }}</span>
					</el-checkbox>
				</el-checkbox-group>

				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="Gantt-table">
					<div class="Gantt-table_flex W100">
						<div class="Gantt-table_left W20">
							<!-- sticky-top -->
							<div class="Gantt-table_left_title W100 color-666">
								<div class="W10">序号</div>
								<div class="">项目编号</div>
								<div class="W30">项目名称</div>
								<div class="">软件类型</div>
								<div class="">实施顾问</div>
							</div>
							<div id="projectAryy W100 " v-for="(item, index) in tableData" :key="index">
								<div class="Gantt-table_left_list W100">
									<div class="Gantt-table_left_intro W10">
										<p>{{ index + 1 }}</p>
									</div>
									<div class="Gantt-table_left_intro">
										<p :title="item.number">{{ item.number }}</p>
									</div>
									<div class="Gantt-table_left_intro W30">
										<p class="hover-green" @click="queryTableData(1, item)" :title="item.projectName">{{ item.projectName }}</p>
									</div>
									<div class="Gantt-table_left_intro">
										<p :title="projectTypeMap[item.projectType]">
											{{ projectTypeMap[item.projectType] }}
										</p>
									</div>
									<div class="Gantt-table_left_intro">
										<p :title="item.implementName">{{ item.implementName }}</p>
									</div>
								</div>
							</div>
						</div>
						<!-- 日期（年月日） -->
						<div class="Gantt-table_right W80">
							<div class="Gantt-table_right_rew">
								<div id="dateHeader">
									<!-- 年-月 -->
									<div class="tableYear" ref="tableYear" v-if="activeClass == 0">
										<p
											v-for="(item, index) in MothY"
											class="Gantt-table_header bolder fs-14"
											:key="item.name + index"
											:style="{ width: item.width }"
										>
											{{ item.name }}
										</p>
									</div>
									<!-- 年/季度/月 -->
									<div class="tableYear bolder fs-14" ref="tableYear" v-else>
										<p v-for="item in MothY1" :key="item.name" :style="{ width: item.width }">
											{{ item.name }}
										</p>
									</div>
									<!-- 日/月 -->
									<div class="tableDay" ref="tableDay">
										<p
											v-for="(item, index) in getActiveClassData()"
											:key="index"
											:id="item.isToday ? 'today' : ''"
											:class="[item.isToday ? 'today' : '', item.bool ? 'Gantt-table_weekend' : '']"
											:style="{ width: item.width }"
											:ref="item.isToday ? 'today' : 'null'"
										>
											{{ item.name }}{{ activeClass == 1 ? ' 月' : '' }}
										</p>
									</div>
								</div>

								<!-- 进度显示区域 -->
								<div id="projectBarAryy">
									<div class="tableList" :style="tableListStyles" v-for="(aItem, index) in tableData" :key="index">
										<div v-for="item in aItem.deliveryScheduleInfoVOS" :key="item.dmsiid">
											<div
												:title="item.stage"
												class="tableBar text-center color-666"
												:class="[colorMap[item.status]]"
												:style="{
													width: item.offsetwidth,
													left: item.leftwidth,
													// background: '#' + ((Math.random() * 0x1000000) << 0).toString(16).slice(-6),
												}"
											></div>
											<!-- {{ item.stage }} -->
											<!-- <p
									class="tableNameintro fs-12"
									:style="{
										left: item.tableNameintroWidth,
										'z-index': 666,
									}"
									>{{ item.projectName }}</p
								> -->
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- 分页 -->
				<el-pagination
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					:page-size="tablePageForm.pageSize"
					:pager-count="5"
					layout="total, sizes, prev, pager, next"
					:total="tablePageForm.total"
				>
				</el-pagination>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { projectTypeOptions, projectTypeMap } from '@/assets/js/contractSource';

export default {
	name: 'deliveryPlan',
	props: { twidList: Array, channelName: Array },
	data() {
		const currentDate = new Date(); //当前日期
		const currentYear = currentDate.getFullYear(); //当前年份
		const yearRange = 1; // 前后1年
		const startDate = currentYear - yearRange; //前1年
		const endDate = currentYear + yearRange; //后1年
		const today = currentDate.getDate(); // 获取今天是几号
		const currentMonth = currentDate.getMonth(); //月
		const displayedYears = {}; // 用于记录已显示的年份
		return {
			currentDate, //当前日期
			currentYear, //当前年份
			yearRange, // 前后1年
			startDate, //前1年
			endDate, //后1年
			today, // 获取今天是几号
			currentMonth, //月
			displayedYears, // 用于记录已显示的年份
			viewNames: [
				{ id: 0, name: '日视图' },
				{ id: 1, name: '月视图' },
				{ id: 2, name: '季度视图' },
				{ id: 3, name: '年度视图' },
			],
			quarters: ['第一季度', '第二季度', '第三季度', '第四季度'], //季度标识
			Annuals: ['上半年', '下半年'], // 年度标识
			activeClass: 0,
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 25,
				currentPage: 1,
				pageSizes: [25, 50, 100],
			},
			dateRange: [],
			MothY: [], //年-月
			MothY1: [], //年
			dayView: [], //日视图
			monthView: [], //月视图
			quarterView: [], //季度视图
			yearView: [], //年度视图
			width: 0,
			newW: 0,

			userList: [], //渠道/代理人员列表
			// 查询条件
			searchForm: {
				channelName: [],
				dmid: '',
				endDate: '',
				implementList: [],
				projectType: '',
				query: '',
				startDate: '',
				statusList: [],
				twidList: [],
			},
			statuss: [
				{ id: 1, status: '待开始', color: '' },
				{ id: 2, status: '正在进行', color: 'orange' },
				{ id: 3, status: '已延误', color: 'red' },
				{ id: 4, status: '已完成', color: 'green' },
			],
			colorMap: {
				1: '',
				2: 'bg-orange',
				3: 'bg-red',
				4: 'bg-green',
			},
			projectTypeOptions,
			projectTypeMap,
		};
	},
	computed: {
		// 右边表格刻度线
		tableListStyles() {
			return {
				width: this.width + 'px',
				background: `repeating-linear-gradient(to right, transparent,  #f5f5f5 1px, transparent 1px, transparent ${this.newW}px)`,
				'border-bottom': '0px',
				'background-size': `${this.newW}px 100%`,
			};
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
	},
	// 监控data中的数据变化
	watch: {
		channelName() {
			this.searchForm.channelName = this.channelName;
			this.queryTableData();
			this.queryUserByTwids();
		},
		twidList() {
			this.searchForm.twidList = this.twidList;
			this.queryTableData();
			this.queryUserByTwids();
		},
	},
	created() {},
	mounted() {
		this.searchForm.channelName = this.channelName || [];
		this.searchForm.twidList = this.twidList || [];
		this.getGanttView(0);
		this.queryUserByTwids();
		this.$nextTick(() => {
			this.tableRectangle(this.activeClass); // 传入实际的 id 值
		});
	},
	updated() {
		this.$nextTick(() => {
			this.tableRectangle(this.activeClass);
		});
	},
	methods: {
		//分页
		handleSizeChange(val) {
			this.tablePageForm.pageSize = val;
			this.tablePageForm.currentPage = 1;
			this.queryTableData();
		},
		handleCurrentChange(val) {
			this.tablePageForm.currentPage = val;
			this.queryTableData();
		},
		// 查询渠道/代理下用户
		queryUserByTwids: _.debounce(function () {
			const str = JSON.stringify({
				twids: this.searchForm.twidList,
				counselor: '',
			});
			this.$axios
				.selectSalesmanByTwids(str)
				.then(res => {
					if (res.data.success) {
						
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {

					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		// 查询表格数据
		queryTableData: _.debounce(function (type, item) {
			if (item?.dmid) {
				this.searchForm.dmid = item.dmid;
				// 找出最大和最小的年分
				// const startYears = item.deliveryScheduleInfoVOS.map(item => new Date(item.plannedStartTime).getFullYear());
				// const endYears = item.deliveryScheduleInfoVOS.map(item => new Date(item.plannedTime).getFullYear());

				// this.startDate = Math.min(...startYears);
				// this.endDate = Math.max(...endYears);
				// console.log(this.startDate, this.endDate);
			} else {
				this.searchForm.dmid = '';
				// this.startDate = this.currentYear - this.yearRange;
				// this.endDate = this.currentYear + this.yearRange;
			}

			type && (this.tablePageForm.currentPage = 1);
			const API = 'deliverySchedule'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.searchForm,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item.deliveryScheduleInfoVOS?.sort((a, b) => {
								return b?.sort - a?.sort;
								// if (a?.plannedTime == b?.plannedTime) {
								// 	return b?.sort - a?.sort;
								// } else {
								// 	return b?.plannedTime - a?.plannedTime;
								// }
							});
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							this.generateYears(this.activeClass);
							this.tableTh(this.activeClass);
						});
						console.log(this.tableData);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 修改视图类型
		async getGanttView(index) {
			this.activeClass = index;
			this.queryTableData(1);
		},
		// 渲染表头信息（年月日）
		tableTh(id) {
			const dateRange = [];
			if (id == 0) {
				//日视图的年月渲染
				for (let year = this.startDate; year <= this.endDate; year++) {
					for (let month = 0; month < 12; month++) {
						const lastDay = new Date(year, month + 1, 0).getDate();
						for (let day = 1; day <= lastDay; day++) {
							dateRange.push(new Date(year, month, day));
						}
						// 在 .tableYear 中添加年份和月份信息
						const yearMonthStr = year + '-' + (month + 1 < 10 ? '0' : '') + (month + 1);
						const width = lastDay * 25 + 'px'; // 计算宽度
						this.MothY.push({ name: yearMonthStr, width: width });
					}
				}
				//日视图的日渲染，标出周末以及当前日期深颜色
				for (let i = 0; i < dateRange?.length; i++) {
					const currentDate = dateRange[i];
					const dayNumber = currentDate.getDay(); // 获取星期几 (0 = 星期日, 1 = 星期一, ...)
					//计算周末
					const isWeekend = dayNumber === 0 || dayNumber === 6;

					const dayText = currentDate.getDate(); //获取日
					const dayYear = currentDate.getFullYear(); // 获取年份
					const dayMonth = currentDate.getMonth(); // 获取月份（注意：月份是从 0 到 11，0 表示一月，11 表示十二月）
					//获取当前日期进行匹配
					const isToday = dayText === this.today && dayYear === this.currentYear && dayMonth === this.currentMonth;
					this.dayView.push({ name: currentDate.getDate(), bool: isWeekend, isToday: isToday });
				}
				//计算任务的宽度 开始时间的位置和结束时间的位置
				for (let j = 0; j < this.tableData?.length; j++) {
					const project = this.tableData[j].deliveryScheduleInfoVOS;
					for (let k = 0; k < project?.length; k++) {
						const ls = project[k];
						const endTime = ls.plannedTime + 86400 * 1000 - 1;
						const startTime = ls.plannedStartTime + 86400 * 1000;
						const offsetDays = this.getOffsetDays(startTime, endTime);
						const leftDays = this.getOffsetDays(
							startTime,
							this.$moment(this.startDate + '-' + 1 + '-' + 1)
								.startOf('year')
								.valueOf(),
						);

						const offsetwidth = Math.abs(offsetDays * 25) + 'px';
						const leftwidth = Math.abs(leftDays * 25) + 'px';
						// const tableNameintroWidth = Math.abs(leftDays * 25) + offsetDays * 25 + 25 + 'px';
						//向数组中添加新字段
						ls.offsetwidth = offsetwidth;
						ls.leftwidth = leftwidth;
						// ls.tableNameintroWidth = tableNameintroWidth;
					}
				}

				this.$nextTick(() => {
					this.tableRectangle(this.activeClass); // 传入实际的 id 值
				});
			}
		},
		//计算任务开始日期和结束日期相差的天数
		getOffsetDays(startDate, endDate) {
			const days = (startDate - endDate) / (3600000 * 24);
			return days;
		},
		// 滚动到视图中间（当前日期），获取表格宽度（用于生成右边表格刻度线）
		tableRectangle() {
			this.width = this.$refs?.tableDay?.offsetWidth;
			const target = document?.getElementById('today');
			const scrollElement = document.querySelector('.Gantt-table_right_rew');
			if (target && scrollElement) {
				scrollElement.scrollLeft = target.offsetLeft - scrollElement.offsetWidth / 2 + target.offsetWidth / 2;
			}
			this.newW = this.activeClass == 0 ? 25 : 201;
			//将屏幕滚到视口位置(会影响父元素)
			// const todayElement = this.$refs.today;
			// todayElement[0]?.scrollIntoView({ behavior: 'auto', block: 'center', inline: 'center' });
		},
		//视图切换
		generateYears(id) {
			this.displayedYears = {}; //清空记录显示的年份
			this.MothY1 = [];
			this.monthView = [];
			this.quarterView = [];
			this.yearView = [];
			for (let year = this.startDate; year <= this.endDate; year++) {
				let newLength = '';
				const quarterElement = '';
				let newwidth = '';
				if (id == 1) {
					newLength = 12;
					newwidth = 12 * 201 + 'px';
				} else if (id == 2) {
					newLength = 4;
					newwidth = 4 * 201 + 'px';
				} else if (id == 3) {
					newLength = 2;
					newwidth = 2 * 201 + 'px';
				}
				for (let quarterIndex = 0; quarterIndex < newLength; quarterIndex++) {
					const width = 201 + 'px'; // 计算宽度

					if (!this.displayedYears[year]) {
						this.MothY1.push({ name: year, width: newwidth });
						this.displayedYears[year] = true; // 记录已显示的年份
					}
					if (id == 1) {
						var isToday = year === this.currentYear && quarterIndex === this.currentMonth;
						this.monthView.push({
							name: quarterIndex + 1,
							width: width,
							isToday: isToday,
						});
					} else if (id == 2) {
						var currentQuarter;
						if (this.currentMonth >= 1 && this.currentMonth <= 3) {
							currentQuarter = 1;
						} else if (this.currentMonth >= 4 && this.currentMonth <= 6) {
							currentQuarter = 2;
						} else if (this.currentMonth >= 7 && this.currentMonth <= 9) {
							currentQuarter = 3;
						} else {
							currentQuarter = 4;
						}
						var isToday = year === this.currentYear && quarterIndex + 1 === currentQuarter;
						this.quarterView.push({
							name: this.quarters[quarterIndex],
							width: width,
							isToday: isToday,
						});
					} else {
						var currentQuarter;
						if (this.currentMonth >= 1 && this.currentMonth <= 6) {
							currentQuarter = 1;
						} else {
							currentQuarter = 2;
						}
						var isToday = year === this.currentYear && quarterIndex + 1 === currentQuarter;

						this.yearView.push({
							name: this.Annuals[quarterIndex],
							width: width,
							isToday: isToday,
						});
					}
				}
			}
			for (let j = 0; j < this.tableData?.length; j++) {
				const project = this.tableData[j].deliveryScheduleInfoVOS;
				for (let k = 0; k < project?.length; k++) {
					const ls = project[k];
					const endTime = ls.plannedTime + 86400 * 1000 - 1;
					const startTime = ls.plannedStartTime + 86400 * 1000;
					const offsetDays = this.getOffsetDays(startTime, endTime);
					const leftDays = this.getOffsetDays(
						startTime,
						this.$moment(this.startDate + '-' + 1 + '-' + 1)
							.startOf('year')
							.valueOf(),
					);

					//向数组中添加新字段
					if (id == 1) {
						ls.offsetwidth = Math.abs(Math.abs(offsetDays * 200)) / 30 + 'px';
						ls.leftwidth = Math.abs(leftDays * 200) / 30 + 'px';
						// ls.tableNameintroWidth = Math.abs(leftDays * 200) / 30 + Math.abs(offsetDays * 200) / 30 + 25 + 'px';
					} else if (id == 2) {
						ls.offsetwidth = Math.abs(offsetDays * 200) / 90 + 'px';
						ls.leftwidth = Math.abs(leftDays * 200) / 90 + 'px';
						// ls.tableNameintroWidth = Math.abs(leftDays * 200) / 90 + Math.abs(offsetDays * 200) / 90 + 25 + 'px';
					} else if (id == 3) {
						ls.offsetwidth = Math.abs(offsetDays * 200) / 180 + 'px';
						ls.leftwidth = Math.abs(leftDays * 200) / 180 + 'px';
						// ls.tableNameintroWidth = Math.abs(leftDays * 200) / 180 + Math.abs(offsetDays * 200) / 180 + 25 + 'px';
					}
				}
			}
			this.$nextTick(() => {
				this.tableRectangle(this.activeClass); // 传入实际的 id 值
			});
		},
		// 切换图表类型
		getActiveClassData() {
			switch (this.activeClass) {
				case 0:
					return this.dayView;
				case 1:
					return this.monthView;
				case 2:
					return this.quarterView;
				case 3:
					return this.yearView;
				default:
					return [];
			}
		},
	},
};
</script>

<style lang="scss" scoped>
#deliveryPlan {
	position: relative;
	height: 100%;
	width: 100%;
	overflow: hidden;

	font-size: 12px;
	color: 666;

	.sticky-top {
		position: sticky;
		top: 0;
	}

	#projectBarAryy {
		color: 666;
		// margin-top: 79px;
	}
	body,
	table,
	th,
	td,
	p,
	ul {
		padding: 0px;
		margin: 0px;
	}

	table {
		border-collapse: collapse;
		width: 100%;
	}

	table tr th,
	table tr td {
		border: 1px solid #dddddd;
		padding: 10px;
	}

	.Gantt-table {
		height: calc(100vh - 300px);
		overflow: auto;
	}
	.Gantt-table_flex {
		position: relative;
		// overflow: hidden;
		display: flex;
		padding: 5px;
		// height: calc(100% - 0px);
		// overflow: scroll;
		// border-bottom: 1px solid #dddddd;
	}

	.Gantt-table_left {
		position: relative;
		width: 20%;
	}

	.Gantt-table_left_title,
	.Gantt-table_left_list {
		display: flex;
		// justify-content: space-between;
		height: 25px;
	}

	.Gantt-table_left_title {
		height: 50px;
	}

	.Gantt-table_left_title div,
	.Gantt-table_left_list div {
		width: 20%;
		text-align: center;
		// border: 1px solid #dddddd;
		display: flex;
		justify-content: center;
		align-items: center;
		border-right: 0px;
	}

	.Gantt-table_left_title div:last-of-type,
	.Gantt-table_left_list div:last-of-type {
		border-right: 1px solid #dddddd;
	}

	.Gantt-table_left_intro p {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		display: block;
		-o-text-overflow: ellipsis;
		line-height: 25px;
		// padding: 0px 10px;
		padding: 0;
	}

	.Gantt-table_right {
		width: 80%;
	}
	.tableYear {
		text-align: left;
	}
	.Gantt-table_right .tableDay,
	.Gantt-table_right .tableYear,
	.Gantt-table_right {
		position: relative;
		display: inline-flex;
	}

	.Gantt-table_right .tableDay p,
	.Gantt-table_right .tableList .Gantt-table_header {
		line-height: 25px;
		width: 25px;
		height: 25px;
	}

	.Gantt-table_right .tableDay p,
	.Gantt-table_right .tableYear p,
	.Gantt-table_right .tableList .Gantt-table_header {
		border: 1px solid #dddddd;
		border-right: 0px;
		text-align: center;
	}

	.Gantt-table_right .tableYear p {
		border-bottom: 0px;
		line-height: 25px;
		height: 25px;
	}

	.Gantt-table_right .tableList .Gantt-table_header {
		border-top: 0px;
	}

	.Gantt-table_right .tableDay p:nth-child(1),
	.Gantt-table_right .tableYear p:nth-child(1),
	.Gantt-table_right .tableList p:nth-child(1) {
		border-left: 0px;
	}

	.Gantt-table_right .tableDay p:last-of-type,
	.Gantt-table_right .tableYear p:last-of-type,
	.Gantt-table_right .tableList:last-of-type {
		border-right: 1px solid #dddddd;
	}

	.Gantt-table_right_rew {
		// width: 1500px;
		overflow-x: scroll;
		overflow-y: hidden;
		scroll-behavior: smooth;
		text-align: left;
		// width: 100%;
		// height: 100%;t
		// position: relative;
	}

	.Gantt-table_weekend {
		background-color: #eeeeee;
	}

	.Gantt-table_right .today {
		position: relative;
		// background-color: #660099;
		background-color: #1e9d6f;
		color: #ffffff;
	}

	/* .tableList{width: 1500px;height: 25px;position: relative; background-color: white;} */
	.tableList {
		width: 1200px;
		height: 25px;
		border: 1px solid #dddddd;
		border-left: 0px;
		border-top: 0px;
		margin-left: -1px;
		position: relative;
		display: flex;
		align-items: center;
	}

	.tableList:hover {
		cursor: pointer;
		background-color: #eeeeee;
	}

	.tableBar {
		background: #eeeeee;
		width: 200px;
		height: 15px;
		top: 5px;
		position: absolute;
		border-radius: 10px;
		left: 200px;
		border: 1px solid #fafafa;
	}

	.tableNameintro {
		white-space: nowrap;
		position: absolute;
	}

	.tableBar:hover {
		border-radius: 0px;
		z-index: 999;
	}
}
</style>
