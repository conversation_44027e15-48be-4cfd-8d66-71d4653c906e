/* 
	根据element-ui的loading组件，封装一个loading服务
  源码：node_modules\element-ui\packages\loading\src\index.js
*/

import Vue from 'vue';
import LoadingComponent from './index.vue';

// 使用vue构造器，创建Vue实例
const LoadingConstructor = Vue.extend(LoadingComponent);

export default class LoadingService {
	constructor(options = {}) {
		this.instance = null;
		this.options = options;
	}

	// 显示，创建实例并挂载
	show() {
		const propsData = {
			text: this.options.text,
			wordList: this.options.wordList,
		};

		// 创建Vue实例
		const instance = new LoadingConstructor({
			el: document.createElement('div'),
			propsData: propsData, // 传递props数据
		});

		// 添加到body
		document.body.appendChild(instance.$el);

		// 设置样式使其居中显示
		instance.$el.style.position = 'fixed';
		instance.$el.style.top = '45%';
		instance.$el.style.left = '50%';
		instance.$el.style.transform = 'translate(-50%, -50%)';
		instance.$el.style.zIndex = '9999';

		this.instance = instance;

		// 如果需要遮罩层
		if (this.options.showMask !== false) {
			this.createMask();
		}

		return instance;
	}

	// 创建遮罩
	createMask() {
		const mask = document.createElement('div');
		mask.className = 'loading-mask';
		mask.style.position = 'fixed';
		mask.style.top = '0';
		mask.style.left = '0';
		mask.style.right = '0';
		mask.style.bottom = '0';
		mask.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
		mask.style.zIndex = '9998';

		// 添加过渡效果
		mask.style.transition = 'opacity 0.3s ease';
		mask.style.opacity = '0';
		setTimeout(() => {
			mask.style.opacity = '1';
		}, 0);

		document.body.appendChild(mask);
		this.mask = mask;
	}

	// 关闭，并销毁实例
	close() {
		if (this.instance) {
			if (this.mask) {
				// 添加淡出效果
				this.mask.style.opacity = '0';
				setTimeout(() => {
					if (this.mask && this.mask.parentNode) {
						document.body.removeChild(this.mask);
					}
					this.mask = null;
				}, 300);
			}
			document.body.removeChild(this.instance.$el);
			this.instance.$destroy();
			this.instance = null;
		}
	}
}
