<template>
	<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper" v-loading.lock="detailForm.isLoading" element-loading-text="正在上传文件，请勿中途退出...">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}行业案例</span>
				<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content">
				<p class="detail-content-title">基本信息 </p>
				<table class="base-table input-border-none" cellpadding="5" cellspacing="0">
					<tbody v-for="(tItem, index) in formList" :key="index">
						<!-- 标题 -->
						<tr>
							<th v-for="item in tItem" :key="item.prop" :colspan="item.colspan" :class="item.class">
								<div v-if="item.prop == 'urlList'" class="flex-align-center gap-10">
									<span>{{ item.name }}</span>
									<el-button type="text" size="mini" class="el-icon-plus" @click="addUrl"></el-button>
								</div>
								<span v-else>{{ item.name }}</span>
							</th>
						</tr>
						<!-- 输入框 -->
						<tr>
							<td v-for="item in tItem" :key="item.prop" :colspan="item.colspan">
								<!-- 只读文本 -->
								<Tooltips v-if="item.type == 'text'" :cont-str="detailForm[item.prop]" />

								<!-- 单选框 -->
								<div v-else-if="item.type == 'check'" class="text-center">
									<el-checkbox v-model="detailForm[item.prop]" :true-label="1" :false-label="0" />
								</div>
								<div v-else-if="item.type == 'radio'">
									<el-radio-group v-model="detailForm[item.prop]">
										<div v-if="item.prop == 'isModelFactory'">
											<el-radio :label="0">否</el-radio>
											<el-radio :label="1">是</el-radio>
										</div>
										<div v-else-if="item.prop == 'status'" class="min-w-180">
											<el-radio :label="0">未交付</el-radio>
											<el-radio :label="1">已交付</el-radio>
										</div>
									</el-radio-group>
								</div>

								<!-- 日期 -->
								<el-date-picker
									class="w-150"
									v-else-if="item.type == 'date'"
									v-model="detailForm[item.prop]"
									type="date"
									:placeholder="item.name"
									format="yyyy-MM-dd"
									value-format="timestamp"
								>
								</el-date-picker>

								<!-- 类型 行业方案 行业案例 视频推文-->
								<el-select
									v-else-if="item.prop == 'type'"
									v-model="detailForm[item.prop]"
									:placeholder="item.name"
									clearable
									filterable
								>
									<el-option label="行业方案" value="行业方案"> </el-option>
									<el-option label="行业案例" value="行业案例"> </el-option>
									<el-option label="视频推文" value="视频推文"> </el-option>
								</el-select>
								<!-- 行业 -->
								<el-select
									v-else-if="item.prop == 'industries'"
									v-model="detailForm[item.prop]"
									:placeholder="item.name"
									popper-class="select-column-4"
									clearable
									filterable
									multiple
									collapse-tags
								>
									<el-option v-for="i in industryOptions" :key="i" :label="i" :value="i" :title="i"> </el-option>
								</el-select>
								<!-- 需求 -->
								<el-select
									v-else-if="item.prop == 'demands'"
									v-model="detailForm[item.prop]"
									:placeholder="item.name"
									popper-class="select-column-3"
									clearable
									filterable
									multiple
									collapse-tags
								>
									<el-option v-for="p in productOptions" :key="p" :label="p" :value="p" :title="p"> </el-option>
								</el-select>
								<!-- 业务顾问/创作者 -->
								<el-select
									v-else-if="item.prop == 'businessAdviserId' || item.prop == 'creatorId'"
									v-model="detailForm[item.prop]"
									:placeholder="item.name"
									popper-class="select-column-3"
									clearable
									filterable
								>
									<el-option
										v-for="user in salesmanList"
										:key="user.auid"
										:label="user.userName"
										:value="user.auid"
										:title="user.userName"
									>
									</el-option>
								</el-select>
								<!-- 实施顾问 -->
								<el-select
									v-else-if="item.prop == 'implement'"
									v-model="detailForm[item.prop]"
									:placeholder="item.name"
									popper-class="select-column-3"
									clearable
									filterable
								>
									<el-option
										v-for="user in implementList"
										:key="user.auid"
										:label="user.userName"
										:value="user.auid"
										:title="user.userName"
									>
									</el-option>
								</el-select>

								<!-- 附件 -->
								<div v-else-if="item.type == 'upload'" class="flex flex-justify-between">
									<div class="flex-column gap-10 max-h-300 overflow-y-auto">
										<div v-for="(file, fIndex) in detailForm.fileDTOS" :key="fIndex" class="flex">
											<FilePopover class="inline-block max-w-300" trigger="click" :url="file.file" :content="file.fileName" />
											<div class="img-delete el-icon-circle-close pointer ml5" @click.stop="deleteImg(fIndex)"></div>
										</div>
									</div>
									<el-upload
										class="felx-1 upload-dragger"
										action=""
										accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
										:http-request="uploadFile"
										:show-file-list="false"
										:limit="9"
										multiple
										drag
									>
										<el-button type="text" class="p0" size="small">
											<div>上传附件(支持批量选择和拖拽上传)</div>
											<div class="mt5 fs-12 color-999">tips:单个文件大小不超过100M，如PPT这类大文件可将其转为PDF再上传</div>
										</el-button>
									</el-upload>
								</div>

								<!-- 链接 -->
								<div v-else-if="item.prop == 'urlList'">
									<div v-for="(uItem, uIndex) in detailForm.urlList" :key="uIndex" class="flex-align-center gap-10">
										<el-input v-model="uItem.url" placeholder="链接" clearable></el-input>
										<a v-show="uItem.url" :href="uItem.url" target="_blank" title="点击查看">
											<i class="el-icon-link"></i>
										</a>
										<el-button type="text" size="mini" class="el-icon-delete" @click="deleteUrl(uIndex)"></el-button>
									</div>
								</div>
								<!-- 默认输入框 -->
								<el-input v-else v-model="detailForm[item.prop]" :placeholder="item.name" clearable></el-input>
							</td>
						</tr>
					</tbody>
				</table>
				<div class="bottom-button">
					<el-button v-if="detailForm.icid" class="mr20" @click="delDetail">删 除</el-button>
					<el-button @click="saveDetail" type="primary">保 存</el-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { resetValues, checkRequired, deepClone, debounce, dateFormat, jointString } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';
export default {
	name: 'industryCaseDetail',
	components: {
		FilePopover,
	},
	props: {
		// 行业
		industryOptions: {
			type: Array,
			default: () => [],
		},
		// 需求
		productOptions: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '',
			formList: [
				[
					{ name: '类型', prop: 'type', class: 'label-required W10', type: 'select' },
					{ name: '样板工厂', prop: 'isModelFactory', class: 'label-required W10', type: 'radio' },
					{ name: '项目', prop: 'project', class: 'label-required  W10' },
					{ name: '项目状态', prop: 'status', class: 'label-required  W10', type: 'radio' },
					{ name: '行业', prop: 'industries', class: 'W10', type: 'select' },
					{ name: '主营产品', prop: 'mainProduct', class: 'W10' },
					{ name: '主要工序', prop: 'mainProcess', class: 'W10' },
					{ name: '需求', prop: 'demands', class: 'W10', type: 'select' },
				],
				[
					{ name: '地区', prop: 'area', class: 'W10' },
					{ name: '业务顾问', prop: 'businessAdviserId', class: 'W10', type: 'select' },
					{ name: '实施顾问', prop: 'implement', class: 'W10', type: 'select' },
					{ name: '创作者', prop: 'creatorId', class: ' W10', type: 'select' },
					{ name: '备注', prop: 'remark', class: 'W10', colspan: 5 },
				],
				[
					{ name: '附件', prop: 'fileDTOS', class: 'W10', colspan: 4, type: 'upload' },
					{ name: '链接', prop: 'urlList', class: 'W10', colspan: 4 },
				],
			],
			detailFormCopy: [],
			detailForm: {
				//明细详情
				area: '',
				businessAdviserId: '',
				demands: [],
				fileDTOS: [],
				industries: [],
				isModelFactory: '',
				implement: '',
				mainProcess: '',
				mainProduct: '',
				project: '',
				status: '',
				creatorId: '',
				twid: '',
				type: '',
				url: [],
				urlList: [],
				remark: '',

				isLoading: false,
			},
			formRules: {
				type: [{ required: true, message: '请输入类型！', trigger: 'blur' }],
				isModelFactory: [{ required: true, message: '请输入是否样板工厂！', trigger: 'blur' }],
				project: [{ required: true, message: '请输入项目！', trigger: 'blur' }],
				status: [{ required: true, message: '请输入项目状态！', trigger: 'blur' }],
			},
		};
	},
	created() {},
	computed: {
		...mapGetters(['userInfos', 'userList']), //当前登录用户信息（含团队/菜单/权限等）
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务') || user?.userLabel?.includes('实施')) || [];
		},
		// 实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.detailForm = resetValues(this.detailForm); //重置对象
				this.detailForm.fileDTOS = [];
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 添加链接
		addUrl() {
			this.detailForm.urlList.push({ url: '' });
		},
		// 删除链接
		deleteUrl(index) {
			this.detailForm.urlList.splice(index, 1);
		},
		// 删除图片
		deleteImg(index) {
			this.detailForm.fileDTOS.splice(index, 1);
		},
		// 上传文件
		async uploadFile(item) {
			try {
				if (!(item.file.size / 1024 / 1024 < 100)) {
					return this.$message.warning('上传文件大小不能超过 100MB!');
				}
				this.detailForm.isLoading = true;
				const formData = new FormData();
				formData.append('file', item.file);
				formData.append('type', 2);
				// type 1-用户头像,2-团队头像,3-团队图片,4-设备图片,5-反馈图片,6-原料图片,7-模具图片,8-产品图片,9-产品工艺图纸图片
				const res = await this.$axios.uploadFile(formData);
				if (res.data.success) {
					this.detailForm.fileDTOS.push({
						file: res.data.data.path,
						fileName: res.data.data.fileName,
					});
				} else {
					this.$message.warning(res.data.message);
				}
				this.detailForm.isLoading = false;
			} catch (error) {
				this.$message.warning(error.message);
				this.detailForm.isLoading = false;
			} finally {
				this.detailForm.isLoading = false;
			}
		},

		// 添加/保存信息
		saveDetail: debounce(function (isClose) {
			if (checkRequired(this.detailForm, this.formRules)) return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			this.detailForm.url = this.detailForm.urlList.map(item => item.url);
			// 请添加附件或者链接
			if (!this.detailForm.fileDTOS.length && !this.detailForm.urlList.length) {
				return this.$message.warning('请添加附件或者链接！');
			}
			const API = this.detailForm.icid ? 'updateIndustryCases' : 'addIndustryCase';
			this.$axios[API](JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						isClose && (this.showCom = false);
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		}),

		// 删除信息
		delDetail() {
			this.$confirm('该数据将被删除，删除后数据不可恢复', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteIndustryCase(JSON.stringify({ icid: this.detailForm.icid }))
						.then(res => {
							if (res.data.success) {
								this.showCom = false;
								this.$succ(res.data.message);
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteIndustryCase |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		//获取详情数据
		queryDetailData({ pid }) {
			const str = JSON.stringify({ pid });
			this.$axios
				.selectProductDetail(str)
				.then(res => {
					if (res.data.success) {
						this.detailForm = res.data.data;
						this.detailFormCopy = deepClone(this.detailForm);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectProductDetail |' + error);
				});
		},
		// 获取日志
		queryLogData({ pid }) {
			const str = JSON.stringify({ pid });
			this.$axios
				.selectLog(str)
				.then(res => {
					if (res.data.success) {
						this.logList = res.data.data;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectLog |' + error);
				});
		},
		//显示弹窗
		showDetailCom(rowData) {
			this.titleName = rowData?.icid ? '修改' : '添加';
			if (rowData && rowData?.icid) {
				this.detailForm = { ...this.detailForm, ...rowData };
				this.detailForm.industries = rowData.industry ? rowData.industry?.split(',') || [] : [];
				this.detailForm.demands = rowData.demand ? rowData?.demand?.split(',') || [] : [];
				this.detailForm.fileDTOS = rowData.fileVOList || [];
				this.detailForm.urlList = rowData.url ? rowData.url.map(item => ({ url: item })) : [];
				this.detailFormCopy = deepClone(this.detailForm);
				// this.queryDetailData(rowData); //如果有查询接口
			} else {
				this.detailForm = { ...this.detailForm, ...rowData };
				this.detailFormCopy = deepClone(this.detailForm);
			}
			this.showCom = true;
		},

		//点击返回
		closeDetailCom() {
			let isSameData = true;
			isSameData = JSON.stringify(this.detailForm) == JSON.stringify(this.detailFormCopy);
			console.log('isSameData', isSameData);
			if (!isSameData && this.showCom) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.showCom = false;
					})
					.catch(() => {
						return this.$message.info('取消返回！');
					});
			} else {
				this.showCom = false;
			}
		},

		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>
