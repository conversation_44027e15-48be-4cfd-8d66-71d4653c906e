<template>
	<div id="Login">
		<div class="lg-wrapper">
			<div class="lg-card">
				<!-- logo -->
				<div class="lg-logo flex-align-center">
					<img class="lg-logo-img" src="@assets/img/lightmes-logo.webp" />
				</div>

				<!-- 登录表单 -->
				<div class="lg-mian">
					<el-tabs v-model="loginType" @tab-click="clearCountdown()">
						<el-tab-pane label="密码登录" name="1">
							<div class="lg-form">
								<el-input placeholder="请输入账号" v-model="loginForm.phoneNo">
									<template slot="prepend">
										<svg
											version="1.1"
											xmlns:xlink="http://www.w3.org/1999/xlink"
											width="18px"
											height="19px"
											xmlns="http://www.w3.org/2000/svg"
										>
											<g transform="matrix(1 0 0 1 -46 -110 )">
												<path
													d="M 12.8453158950617 4.95807258144947  C 12.8389095084544 2.22444913445663  10.6714629539543 0.0118820031994987  8 0.0118898634890292  C 5.32401985527675 0.0118898634685334  3.15469803717035 2.23169456962927  3.15469803717035 4.969962444918  C 3.15469803717035 7.70823032020674  5.32401985527675 9.92803502636747  8.00001393223208 9.92803502636747  C 10.6760080091874 9.92803502636747  12.8453298272938 7.70823032020674  12.8453298272938 4.969962444918  C 12.8453298272938 4.96599915282124  12.8453251832131 4.96203586215017  12.8453158950617 4.95807258144947  Z M 10.3471314429012 18.9643304095329  L 10.3355119791667 18.9643304095329  L 5.68772694830247 18.9643304095329  C 3.61946259645062 18.9643304095329  0.540305015432098 19.416145182638  0.0522875771604929 16.4912390406832  L 0.0522875771604929 15.6589486951463  C 0.475147719025776 12.8425963739458  2.89341928917972 10.7975155159379  5.6761074845679 10.9030037535322  L 10.3238925154321 10.9030037535322  C 13.1109368295156 10.7913302801888  15.5359996142784 12.8379258204318  15.9593318865741 15.6589486951463  L 15.9593318865741 16.4912390406832  C 15.4248366126543 19.416145182638  12.392156867284 18.9643304095329  10.3471314429012 18.9643304095329  Z "
													fill-rule="nonzero"
													fill="#ffffff"
													stroke="none"
													transform="matrix(1 0 0 1 46 110 )"
												/>
											</g>
										</svg>
									</template>
								</el-input>
								<el-input placeholder="请输入密码" v-model="loginForm.password" show-password @keyup.enter.native="userLogin">
									<template slot="prepend">
										<svg
											version="1.1"
											xmlns:xlink="http://www.w3.org/1999/xlink"
											width="18px"
											height="19px"
											xmlns="http://www.w3.org/2000/svg"
										>
											<g transform="matrix(1 0 0 1 -46 -159 )">
												<path
													d="M 15 6  L 15 7  L 17 7  C 17.5522847498308 7  18 7.44771525016921  18 8  L 18 20  C 18 20.5522847498308  17.5522847498308 21  17 21  L 1 21  C 0.447715250169207 21  0 20.5522847498308  0 20  L 0 8  C 0 7.44771525016921  0.447715250169207 7  1 7  L 3 7  L 3 6  C 3 2.68629150101524  5.68629150101524 0  9 0  C 12.3137084989848 0  15 2.68629150101524  15 6  Z M 7 12.9999491924311  C 7 13.7144803722475  7.38119784648299 14.3747344100918  8 14.732  L 8 17  L 10 17  L 10 14.732  C 10.618802153517 14.3747344100918  11 13.7144803722475  11 12.9999491924311  C 11 11.8953796927695  10.1045694996616 10.9999491924311  9 10.9999491924311  C 7.89543050033841 10.9999491924311  7 11.8953796927695  7 12.9999491924311  Z M 5 7  L 13 7  L 13 6  C 13 3.79086100067683  11.2091389993232 2  9 2  C 6.79086100067683 2  5 3.79086100067683  5 6  L 5 7  Z "
													fill-rule="nonzero"
													fill="#ffffff"
													stroke="none"
													transform="matrix(1 0 0 1 46 159 )"
												/>
											</g>
										</svg>
									</template>
								</el-input>
							</div>
						</el-tab-pane>
						<el-tab-pane label="短信登录" name="0">
							<div class="lg-form">
								<el-input placeholder="手机号" v-model="loginForm.phoneNo">
									<template slot="prepend">
										<svg
											version="1.1"
											xmlns:xlink="http://www.w3.org/1999/xlink"
											width="15px"
											height="22px"
											xmlns="http://www.w3.org/2000/svg"
										>
											<g transform="matrix(1 0 0 1 -46 -108 )">
												<path
													d="M 15 1.43562609179688  C 14.9376417271205 0.644091701171875  14.3102796595982 0  13.531746030971 0  L 1.46825396902902 0  C 0.689720340401785 0  0.0623582728794644 0.644091701171875  0 1.43562609179688  L 0 20.5643739082031  C 0.0623582728794644 21.3559082988281  0.689720319475447 22  1.46825396902902 22  L 13.5241874581473 22  C 14.3027210867746 22  14.9300831542969 21.3559082988281  15 20.5643739082031  L 15 1.43562609179688  Z M 13.7962962890625 20.5643739082031  L 13.8038548828125 20.5643739082031  C 13.8038548828125 20.7195767324219  13.6829176130022 20.8437389960938  13.531746030971 20.8437389746094  L 1.46825396902902 20.8437389746094  C 1.31708238699777 20.8437389746094  1.1961451171875 20.7195767109375  1.1961451171875 20.5643739082031  L 1.1961451171875 1.43562609179688  C 1.1961451171875 1.28042326757813  1.31708238699777 1.15626100390625  1.46825396902902 1.15626102539063  L 13.5241874581473 1.15626102539063  C 13.6753590401786 1.15626102539063  13.7962963099888 1.2804232890625  13.7962962890625 1.43562609179688  L 13.7962962890625 20.5643739082031  Z M 10.8786848144531 19.0433862480469  C 10.8711262416295 18.7252204648438  10.6216931291853 18.4691358027344  10.3042327985491 18.4691358027344  L 4.69576720145089 18.4691358027344  C 4.38586546456473 18.4691358027344  4.12887377929688 18.7252204648438  4.12887377929688 19.0433862480469  C 4.12887377929688 19.36155203125  4.38586546456473 19.6176366933594  4.69576720145089 19.6176366933594  L 10.3117913922991 19.6176366933594  C 10.6216931291853 19.6176366933594  10.8786848144531 19.36155203125  10.8786848144531 19.0433862480469  Z "
													fill-rule="nonzero"
													fill="#ffffff"
													stroke="none"
													transform="matrix(1 0 0 1 46 108 )"
												/>
											</g>
										</svg>
									</template>
								</el-input>
								<el-input
									placeholder="请输入验证码"
									type="text"
									v-model="loginForm.smsCode"
									maxlength="6"
									@keyup.enter.native="userLogin"
								>
									<div slot="suffix">
										<span v-if="ckInterval">{{ ckCount }}秒后可重新获取</span>
										<span v-else class="pointer" @click="queryLoginSmsCode">获取验证码</span>
									</div>
									<template slot="prepend">
										<svg
											version="1.1"
											xmlns:xlink="http://www.w3.org/1999/xlink"
											width="15px"
											height="18px"
											xmlns="http://www.w3.org/2000/svg"
										>
											<g transform="matrix(1 0 0 1 -46 -163 )">
												<path
													d="M 1.1505126953125 4.04855357142857  L 1.1505126953125 1.82788392857143  C 1.15147378086441 1.49305329106522  1.42612457139128 1.22185525333354  1.76521809895833 1.22090625  L 13.1988525390625 1.22090625  C 13.5379547395733 1.2218660093368  13.8126224540146 1.49304485713988  13.8136393229167 1.82788392857143  L 13.8136393229167 4.04875446428571  L 14.9605305989583 4.04875446428571  L 14.9605305989583 1.29489508928571  C 14.9605305989583 0.627247767857143  14.4070841471354 0.0808593749999998  13.7307332356771 0  L 1.23331705729167 0  C 0.556966145833334 0.0808593749999998  0.00382486979166726 0.627247767857143  0.00382486979166726 1.29489508928571  L 0.00382486979166726 4.04867410714286  L 1.15049235026042 4.04867410714286  Z M 14.9605305989583 13.7760669642857  L 13.8136393229167 13.7760669642857  L 13.8136393229167 16.1944553571429  C 13.8124999555281 16.5292245390928  13.5378841871942 16.8002982591716  13.1988525390625 16.8013125  L 1.76521809895833 16.8013125  C 1.42619514268151 16.8003089837054  1.1515963188652 16.5292161046372  1.1505126953125 16.1944553571429  L 1.1505126953125 13.7760669642857  L 0.00386555989583259 13.7760669642857  L 0.00386555989583259 16.7274441964286  C 0.00386555989583259 17.3950915178571  0.557189941406249 17.9415803571429  1.23331705729167 18  L 13.7307332356771 18  C 14.406982421875 17.9415803571429  14.9605305989583 17.3951919642857  14.9605305989583 16.7274441964286  L 14.9605305989583 13.7760669642857  Z M 2.67561848958333 11.9760669642857  L 1.5098876953125 11.9760669642857  L 1.5098876953125 7.63662053571429  C 1.0838623046875 8.02944642857143  0.581970214843749 8.32021875  0.00384521484374956 8.50925892857143  L 0.00384521484374956 7.46433482142857  C 0.308003743489583 7.36621875  0.638427734374999 7.17942857142857  0.995422363281249 6.905109375  C 1.35227457682292 6.63058928571429  1.59698486328125 6.31038616071429  1.72967529296875 5.94452008928571  L 2.67551676432292 5.94452008928571  L 2.67551676432292 11.9760669642857  Z M 9.183349609375 10.9064732142857  L 9.183349609375 11.9762879464286  L 5.09635416666667 11.9762879464286  C 5.14093017578125 11.5716495535714  5.27364095052083 11.1886071428571  5.49491373697917 10.8270803571429  C 5.7159423828125 10.4639866071429  6.15291341145833 9.98477678571428  6.80582682291667 9.38603571428571  C 7.3316650390625 8.90270758928571  7.65323893229167 8.57481026785715  7.7728271484375 8.40272544642857  C 7.93302408854167 8.16533035714286  8.01332600911458 7.93030580357143  8.01332600911458 7.697953125  C 8.01332600911458 7.44161383928571  7.94333902994792 7.24403571428571  7.80375162760417 7.10612276785714  C 7.66365559895833 6.96849107142857  7.47176106770833 6.89936383928572  7.22519938151042 6.89936383928572  C 6.98207600911458 6.89936383928572  6.78802490234375 6.97168526785715  6.6441650390625 7.11608705357143  C 6.50032552083333 7.26101116071429  6.41719563802083 7.50139955357143  6.39520263671875 7.83743303571429  L 5.23333740234375 7.72304464285714  C 5.30271402994792 7.08954910714286  5.51979573567708 6.63450669642857  5.88458251953125 6.35896205357143  C 6.24969482421875 6.08279464285714  6.70613606770833 5.9450625  7.25406901041667 5.9450625  C 7.85426839192708 5.9450625  8.32564290364583 6.10449107142857  8.66916910807292 6.42439285714286  C 9.0120849609375 6.74385267857143  9.18353271484375 7.14127901785714  9.18353271484375 7.61649107142857  C 9.18451326837165 7.88076293805058  9.13441630855226 8.1427923415835  9.03592936197917 8.38852232142857  C 8.93739827473958 8.63300892857143  8.78228759765625 8.88934821428572  8.56907145182292 9.157078125  C 8.42812093098959 9.33460714285714  8.17364501953125 9.58980133928572  7.80582682291667 9.92378571428572  C 7.43843587239583 10.2563236607143  7.20542399088542 10.4785915178571  7.10707600911458 10.5871138392857  C 7.01689163017263 10.6855547146591  6.93694174005038 10.7926854408785  6.86842854817708 10.9068950892857  L 9.183349609375 10.9068950892857  Z M 11.5500284830729 11.6090959821429  C 11.1877237955729 11.2962053571429  10.9774169921875 10.8869263392857  10.9194946289063 10.3822232142857  L 12.0479329427083 10.2470625  C 12.0840861002604 10.5311450892857  12.1807454427083 10.748390625  12.3378499348958 10.89815625  C 12.4960530598958 11.0481026785714  12.6867879231771 11.123296875  12.9107666015625 11.123296875  C 13.1514689127604 11.123296875  13.3539835611979 11.0328950892857  13.5182495117188 10.8534977678571  C 13.6831461588542 10.6728950892857  13.7655436197917 10.429734375  13.7655436197917 10.1240558035714  C 13.7655436197917 9.83428794642857  13.6864827473958 9.60502901785714  13.5290730794271 9.43581696428572  C 13.3713785807292 9.26590178571429  13.1790771484375 9.18104464285714  12.9524129231771 9.18104464285714  C 12.8028361002604 9.18104464285714  12.6247151692708 9.20953125  12.4168904622396 9.26714732142857  L 12.5455322265625 8.32897767857143  C 12.8609619140625 8.33711383928571  13.1010538736979 8.26943303571428  13.2673136393229 8.12613616071429  C 13.4330444335937 7.98275892857143  13.5162760416667 7.79257366071429  13.5162760416667 7.55457589285714  C 13.5162760416667 7.35275892857143  13.4554443359375 7.19116071428571  13.3343912760417 7.07114732142857  C 13.2119140625 6.95101339285714  13.0500284830729 6.891046875  12.8484497070312 6.891046875  C 12.6493937174479 6.891046875  12.4788818359375 6.95902901785714  12.3378295898437 7.09581696428571  C 12.1973063151042 7.23202232142857  12.1116943359375 7.43164955357143  12.0808715820313 7.693734375  L 11.0063680013021 7.513734375  C 11.0810546875 7.15086160714286  11.194091796875 6.85996875  11.3446044921875 6.64254241071428  C 11.4956461588542 6.42591964285714  11.7054443359375 6.25508035714286  11.975341796875 6.13092857142857  C 12.2451171875 6.00667633928571  12.5470987955729 5.94454017857143  12.8817952473958 5.94454017857143  C 13.4543863932292 5.94454017857143  13.9130452473958 6.12486160714286  14.2593994140625 6.48556473214285  C 14.5437622070312 6.78027455357143  14.6865844726562 7.11361607142857  14.6865844726562 7.48482589285714  C 14.6865844726562 8.01206919642857  14.3947143554687 8.43265848214285  13.8111572265625 8.74667410714286  C 14.1595052083333 8.82054241071429  14.4384562174479 8.98603794642857  14.6473185221354 9.24287946428571  C 14.8561604817708 9.49960044642857  14.9606323242187 9.80949776785714  14.9606323242187 10.1724910714286  C 14.9606323242187 10.7000357142857  14.7654418945312 11.1493325892857  14.3756510416667 11.520421875  C 13.9856363932292 11.8920535714286  13.5003255208333 12.0778191964286  12.9192911783854 12.0778191964286  C 12.3687540690104 12.0778191964286  11.9128214518229 11.9211629464286  11.5500284830729 11.6090959821429  Z "
													fill-rule="nonzero"
													fill="#ffffff"
													stroke="none"
													transform="matrix(1 0 0 1 46 163 )"
												/>
											</g>
										</svg>
									</template>
								</el-input>
							</div>
						</el-tab-pane>
					</el-tabs>
				</div>

				<!-- 登录按钮 -->
				<el-button class="W100 fs-16 bolder mt25 mb10" type="primary" @click="userLogin()"> 登 录 </el-button>
			</div>
		</div>
		<div class="lg-bg"></div>
	</div>
</template>

<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { debounce, getCookie, setCookie } from '@util/tool';
import JSEncrypt from 'jsencrypt';
const encrypt = new JSEncrypt(); //JSEncrypt 实例对象

export default {
	data() {
		return {
			loginType: '1', // 0: 验证码登录 1: 密码登录
			loginForm: {
				phoneNo: '',
				password: '',
				smsCode: '',
			},

			userInfo: null, // 用户信息
			ckInterval: null, // 验证码倒计时
			ckCount: 300, // 验证码倒计时
		};
	},
	computed: {
		// ...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	created() {
		const adminInfos = JSON.parse(window.sessionStorage.getItem('OPS_USER_INFO'));
		const queryId = adminInfos?.adminUserVO?.phoneNo;
		// 有登录信息
		if (queryId) {
			const OPS_LAST_PATH =
				window.localStorage.getItem('OPS_LAST_PATH_' + queryId) || getCookie('OPS_LAST_PATH_' + queryId) || 'welCome';
			this.$router.replace({
				path: '/' + OPS_LAST_PATH,
				query: { queryId },
			}); // 登录成功后跳入浏览的当前页面
		}
	},
	mounted() {
		const publicKey =
			'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCDLLQFNRAkSNX5Yl/QJCTPfJSkLDA5kuNma9UXciQwWFdTSrQEbxhRq7qnmZ+bBtqz+ScCAJzJBeCp0Q/LYz0EYDbohmcT6HqQ6ZK7Yx6h9diJWmvfpptKwRkKQsAa29WaRa6K1OfDWt5OHe2ohvZMUdSGkWfKIreCQCqvyMuR0QIDAQAB';
		encrypt && encrypt.setPublicKey(publicKey); //设置公钥
	},
	methods: {
		// 登录
		userLogin: debounce(function () {
			const { password, phoneNo, smsCode } = this.loginForm;
			const API = this.loginType == 0 ? 'smsCodeLogin' : 'pwLogin';
			const params = this.loginType == 0 ? { smsCode, phoneNo } : { phoneNo, password: encrypt.encrypt(password) || password };
			if (!phoneNo) {
				this.$message.warning('请输入手机号');
				return;
			}
			// 验证码登录
			if (this.loginType == 0) {
				if (!/^1[3-9]\d{9}$/.test(phoneNo)) {
					this.$message.warning('手机号格式不正确');
					return;
				}
				if (!/^\d{6}$/.test(smsCode)) {
					this.$message.warning('请输入正确的验证码');
					return;
				}
			} else if (!password) {
				this.$message.warning('请输入密码');
				return;
			}

			this.$axios[API](JSON.stringify(params))
				.then(res => {
					if (res.data.success) {
						this.saveLoginInfo(res.data.data, phoneNo);
						this.redirectRouter(phoneNo);
						this.clearCountdown();
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('pwLogin |' + error);
				});
		}),

		// 保存登录信息
		saveLoginInfo(userInfo, phoneNo) {
			// const queryId = userInfo.adminUserVO.phoneNo || phoneNo;
			this.$store.commit('setUserInfos', userInfo); //存储用户信息到vuex
			window.sessionStorage.setItem('OPS_USER_INFO', JSON.stringify(userInfo)); //存储用户信息到本地sessionStorage
		},
		// 登录后重定向
		redirectRouter(queryId) {
			const OPS_LAST_PATH =
				window.localStorage.getItem('OPS_LAST_PATH_' + queryId) || getCookie('OPS_LAST_PATH_' + queryId) || 'welCome';
			this.$router.replace({
				path: '/' + OPS_LAST_PATH, // 登录成功后跳入最近一次的地址
				query: { queryId },
			});
		},
		// 查询登录验证码
		queryLoginSmsCode: debounce(function () {
			this.clearCountdown();
			const { phoneNo } = this.loginForm;
			if (!phoneNo) {
				this.$message.warning('请输入手机号码');
				return;
			}

			if (!/^1[3-9]\d{9}$/.test(phoneNo)) {
				this.$message.warning('请输入正确的手机号码');
				return;
			}

			this.$axios
				.sendAdminLoginSmsCode(JSON.stringify({ phoneNo }))
				.then(res => {
					if (res.data.success) {
						this.$message.success('短信已发送,请查看手机');
						this.startCountdown();
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('sendSmsCode |' + error);
				});
		}),
		// 开始倒计时
		startCountdown() {
			this.ckCount = 300;
			this.ckInterval = setInterval(() => {
				if (this.ckCount === 0) {
					clearInterval(this.ckInterval);
					this.ckInterval = null;
				} else {
					this.ckCount--;
				}
			}, 1000);
		},
		// 清除计时器
		clearCountdown() {
			this.ckInterval && clearInterval(this.ckInterval);
			this.ckInterval = null;
		},
	},
};
</script>

<style lang="scss" scoped>
#Login {
	font-size: 16px;
	font-weight: 400 !important;
	width: 100vw;
	height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;

	.lg-wrapper {
		width: 18vw;
		min-width: 300px;
		max-width: 520px;
		zoom: 1.2;
		.lg-card {
			border-radius: 8px;
			padding: 15px 20px;
			background-color: rgb(242, 242, 242);
			display: flex;
			flex-direction: column;
		}
		.lg-logo {
			border-bottom: 3px solid #ccc;
			&-img {
				height: 45px;
				margin-bottom: 8px;
			}
		}

		.lg-mian {
			display: flex;
			flex-direction: column;
			gap: 20px;
			.lg-form {
				display: flex;
				flex-direction: column;
				gap: 20px;
			}
		}
	}
	.lg-wrapper:hover ~ .lg-bg {
		transform: scale(1.05);
		filter: blur(3px);
	}
	.lg-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: inherit;
		background-image: url(@assets/img/loginB2.webp);
		background-repeat: no-repeat;
		background-size: cover;
		z-index: -1;
		transition: all 0.5s ease-in-out;
	}
}
</style>
<style lang="scss">
#Login {
	.el-tabs {
		.el-tabs__header {
			margin: 0 0 15px !important;
		}
		.el-tabs__item {
			font-size: 15px !important;
			font-weight: 650;
			color: #5e5e5e;
			padding-right: 0;
		}
	}
	.el-input-group__append,
	.el-input-group__prepend {
		background-color: #d7d7d7 !important;
		padding: 0 15px !important;
		line-height: 10px !important;
	}

	.el-input__suffix {
		line-height: 40px !important;
	}

	.el-input__inner {
		border-color: #fff !important;
	}
}
</style>
