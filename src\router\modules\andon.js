/* 安灯相关路由（安灯盒子、三色灯、手环） */

const AndonBoxList = () => import('@/pages/andonBoxManagement/AndonBoxList.vue'); //安灯盒子-清单
const lightMainData = () => import('@/pages/threeColorLightManagement/lightMainData.vue'); //三色灯-清单
const lightUserData = () => import('@/pages/threeColorLightManagement/lightUserData.vue'); //三色灯-打标签
const lightAutoRegistData = () => import('@/pages/threeColorLightManagement/lightAutoRegistData.vue'); //三色灯-客户用

const deliverAndReturn = () => import('@/pages/threeColorLightManagement/deliverAndReturn.vue'); //三色灯-发货
const BoxList = () => import('@/pages/braceletManagement/BoxList.vue'); // 安灯手环盒子清单
const BraceletList = () => import('@/pages/braceletManagement/BraceletList.vue'); //安灯手环清单

const routers = [
	{
		//安灯盒子主数据
		path: '/AndonBoxList',
		name: 'AndonBoxList',
		component: AndonBoxList,
		meta: {
			parentTitle: '安灯盒子管理',
			title: '安灯盒子清单',
		},
	},
	{
		//三色灯主数据
		path: '/lightMainData',
		name: 'lightMainData',
		component: lightMainData,
		meta: {
			parentTitle: '三色灯管理',
			title: '三色灯清单',
		},
	},

	{
		//三色灯主数据
		path: '/lightUserData',
		name: 'lightUserData',
		component: lightUserData,
		meta: {
			parentTitle: '三色灯管理',
			title: '团队三色灯',
		},
	},
	{
		//三色灯主数据
		path: '/lightAutoRegistData',
		name: 'lightAutoRegistData',
		component: lightAutoRegistData,
		meta: {
			parentTitle: '三色灯管理',
			title: '三色灯标签打印',
		},
	},
	{
		//发货与退换
		path: '/deliverAndReturn',
		name: 'deliverAndReturn',
		component: deliverAndReturn,
		meta: {
			parentTitle: '三色灯管理',
			title: '发货与退换',
		},
	},
	{
		// 安灯手环盒子清单
		path: '/BoxList*',
		name: 'BoxList',
		component: BoxList,
		meta: {
			parentTitle: '安灯手环盒子管理',
			title: '安灯手环盒子清单',
		},
	},
	{
		// 安灯手环清单
		path: '/BraceletList*',
		name: 'BraceletList',
		component: BraceletList,
		meta: {
			parentTitle: '安灯手环盒子管理',
			title: '安灯手环清单',
		},
	},
];

export default routers;
