<template>
	<!-- 该组件用于预览展示不同格式的文件 -->
	<div class="flex-column flex-1 H100 min-h-666 border p2" ref="container">
		<el-image lazy v-if="isImage" fit="scale-down" class="flex-1" :src="url" :preview-src-list="[url]"></el-image>
		<iframe v-else-if="isDoc" ref="previewFrame" :src="docUrl" frameborder="0" @load="setIframeHeight"></iframe>
		<iframe v-else-if="isPdf" ref="previewFrame" :src="url" frameborder="0" @load="setIframeHeight"></iframe>
		<div class="red fs-12 m-auto" v-else>
			<p>当前不支持预览该格式的文件！</p>
		</div>
	</div>
</template>

<script>
import { debounce } from '@/util/tool';
export default {
	name: 'FilePreview',
	props: {
		url: String, // 需要预览的文件路径
		content: {
			// 需要显示的文本默认为文件路径
			type: String,
			default: '',
		},
	},
	data() {
		return {};
	},
	computed: {
		// 判断文档类型
		fileType() {
			if (!this.url) return '';
			const fileTypeMapping = {
				'.jpeg': 'image',
				'.gif': 'image',
				'.jpg': 'image',
				'.png': 'image',
				'.bmp': 'image',
				'.pic': 'image',
				'.svg': 'image',
				'.docx': 'doc',
				'.doc': 'doc',
				'.pdf': 'pdf',
			};
			const fileExtension = this.url.substring(this.url.lastIndexOf('.')).toLowerCase();
			return fileTypeMapping[fileExtension] || '';
		},
		isImage() {
			return this.fileType === 'image' && this.isShow;
		},
		isDoc() {
			return this.fileType === 'doc';
		},
		isPdf() {
			return this.fileType === 'pdf';
		},
		docUrl() {
			return this.isDoc ? `https://view.officeapps.live.com/op/view.aspx?src=${this.url}` : '';
		},
		displayContent() {
			return this.content || this.url;
		},
	},
	watch: {},
	mounted() {
		window.addEventListener('resize', this.setIframeHeight);
		this.$nextTick(() => {
			this.setIframeHeight();
		});
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.setIframeHeight);
	},
	methods: {
		setIframeHeight: debounce(function () {
			const iframe = this.$refs.previewFrame;
			const container = this.$refs.container;
			if (!iframe || !container) return;

			// 获取父容器的实际高度
			const containerHeight = container.parentElement.clientHeight;
			// 设置 iframe 的高度
			iframe.style.height = `${containerHeight}px`;
		}, 888),
	},
};
</script>

<style lang="scss" scoped></style>
