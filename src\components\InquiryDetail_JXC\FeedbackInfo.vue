<template>
	<!-- 业务反馈组件 -->
	<div id="FeedbackInfo" v-loading.lock="detailForm.isUpLoadContract" element-loading-text="正在上传合同中，请勿中途退出...">
		<table class="base-table" cellpadding="5" cellspacing="0">
			<!-- 次行咨询信息 -->
			<tr>
				<th class="W10">商机质量</th>
				<th class="W10">预计成交月份</th>
				<th class="W10">预计成交金额（万元）</th>
				<th class="W10">实施顾问</th>
				<th class="W10">谈单人员</th>
				<th class="W65">
					<div class="flex-align-center">
						<span class="mr10">阶段</span>
						<span class="primary" v-show="detailForm.signingDate">
							签单日期： {{ dateFormat(detailForm.signingDate, 'lineM') }}
						</span>
						<el-button v-if="detailForm.dmid" type="text" size="mini" class="ml-auto" @click="openContract">
							{{ detailForm.contractNo || '合同详情' }}</el-button
						>
					</div>
				</th>
			</tr>
			<tr class="input-border-none">
				<td>
					<el-select :disabled="isPublicInquiry" v-model="detailForm.businessOpportunityQuality" placeholder="商机质量">
						<el-option
							v-for="key in Object.keys(businessQualityMap)"
							:key="key"
							:label="businessQualityMap[key]"
							:value="Number(key)"
						>
						</el-option>
					</el-select>
				</td>
				<td>
					<el-date-picker
						:disabled="isPublicInquiry"
						class="w-120"
						v-model="detailForm.expectedMonth"
						value-format="timestamp"
						type="month"
						placeholder="成交月份"
						clearable
						format="yyyy-MM"
					></el-date-picker>
				</td>
				<td>
					<el-input
						:disabled="isPublicInquiry"
						class="w-200"
						v-model="detailForm.estimatedAmount"
						placeholder="预计成交金额"
						clearable
					></el-input
				></td>
				<td>
					<el-select
						:disabled="isPublicInquiry"
						v-model="detailForm.implementUid"
						placeholder="请选择实施顾问"
						popper-class="select-column-3"
						clearable
						filterable
					>
						<el-option disabled v-if="!detailForm.twid" value="">
							<span class="orange">请选择对应的分销/代理后再操作！</span>
						</el-option>
						<el-option disabled v-else-if="detailForm.twid && implementList.length == 0" value="">
							<span class="orange">当前分销/代理未与系统用户绑定(实施顾问)，请联系管理员绑定用户后再操作！</span>
						</el-option>
						<el-option
							v-else
							v-for="item in implementList"
							:key="item.auid"
							:label="item.userName"
							:value="item.auid"
							:title="item.userName"
						>
						</el-option>
					</el-select>
				</td>
				<td>
					<el-select
						:disabled="isPublicInquiry"
						v-model="detailForm.talktradeUid"
						placeholder="谈单人员"
						popper-class="select-column-3"
						clearable
						filterable
					>
						<el-option disabled v-if="!detailForm.twid" value="">
							<span class="orange">请选择对应的分销/代理后再操作！</span>
						</el-option>
						<el-option disabled v-else-if="detailForm.twid && talktradeList.length == 0" value="">
							<span class="orange">当前分销/代理未与系统用户绑定(谈单人员)，请联系管理员绑定用户后再操作！</span>
						</el-option>
						<el-option
							v-else
							v-for="item in talktradeList"
							:key="item.auid"
							:label="item.userName"
							:value="item.auid"
							:title="item.userName"
						>
						</el-option>
					</el-select>
				</td>
				<td>
					<div class="flex-align-center flex-justify-between">
						<el-radio-group :disabled="detailForm.stage == 5 || isPublicInquiry" v-model="detailForm.stage">
							<el-radio :label="8">业务接手</el-radio>
							<el-radio :label="1">线上会议</el-radio>
							<el-radio :label="9">试点试用</el-radio>
							<el-radio :label="2">已上门拜访</el-radio>
							<el-radio :label="3">已报价</el-radio>
							<el-radio :label="4">合同流程</el-radio>
							<el-radio :label="5">已签单</el-radio>
							<el-radio :label="10">丢单</el-radio>
							<el-input
								class="border mt5"
								v-show="isLosing"
								v-model="detailForm.losingOrderReasons"
								placeholder="请输入丢单原因"
								type="textarea"
								:autosize="{ minRows: 2, maxRows: 4 }"
							></el-input>
						</el-radio-group>
						<span>
							<!-- 合同未上传 -->
							<el-upload
								:disabled="isPublicInquiry"
								v-if="detailForm.stage == 5 && !detailForm.contractName"
								ref="upload"
								action=""
								accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
								:http-request="uploadFile"
								:show-file-list="false"
							>
								<el-button size="mini" type="warning" class="ml20"> 上传合同 </el-button>
							</el-upload>

							<el-upload
								v-else-if="detailForm.contractName"
								action=""
								accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
								:http-request="uploadFile"
								:show-file-list="false"
							>
								<FilePopover class="inline-block max-w-200" :url="detailForm.contractUrl" :content="detailForm.contractName" />
							</el-upload>
						</span>
					</div>
				</td>
			</tr>
		</table>

		<div class="bottom-button flex-align-center">
			<div class="text-left">
				<div v-show="detailForm.oldConsultantConsultName">原咨询人：{{ detailForm.oldConsultantConsultName }}</div>
				<div v-show="detailForm.oldConsultant">原业务员：{{ detailForm.oldConsultant }}</div>
				<div v-show="detailForm.businessTakeoverTime">
					业务接手时间：{{ dateFormat(detailForm.businessTakeoverTime, 'line') }}
				</div>
			</div>
			<div v-show="!isPublicInquiry" class="ml-auto">
				<el-button @click="saveEdit" :type="isUpdate ? 'primary' : ''">保存反馈 </el-button>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import eventBus from './eventBus';
import { stageList, businessQualityMap } from '@/assets/js/inquirySource.js';
import FilePopover from '@/components/FilePopover.vue';

export default {
	name: 'FeedbackInfo',
	components: { FilePopover },

	props: {
		// // 询盘主键
		idid: {
			type: [String, Number],
			default: '',
		},
		// 是否公海询盘
		isPublicInquiry: { type: Boolean, default: false },
		//实施顾问列表(标签：实施)
		implementList: {
			type: Array,
			default: () => [],
		},
		talktradeList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			dialogEdit: false,

			detailForm: {}, //事件总线表单
			feedbackFormCopy: {}, //克隆数据
			// 质量列表
			businessQualityMap, // 商机质量
			stageList, // 询盘阶段

			formRules: {
				// feedback: [{ required: true, message: '请输入业务跟进反馈', trigger: 'blur' }],
				businessOpportunityQuality: [{ required: true, message: '请选择商机质量', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 咨询表单
		feedbackForm() {
			return {
				businessOpportunityQuality: this.detailForm.businessOpportunityQuality,
				estimatedAmount: this.detailForm.estimatedAmount,
				expectedMonth: this.detailForm.expectedMonth,
				feedback: this.detailForm.feedback,
				implement: this.detailForm.implementUid,
				// quality: this.detailForm.quality,
				stage: this.detailForm.stage,
				// callRecording: this.detailForm.callRecording,
				contractUrl: this.detailForm.contractUrl,
				contractName: this.detailForm.contractName,
				idid: this.detailForm.idid || this.idid,
				ckrid: this.detailForm.ckrid,
				talktrade: this.detailForm.talktradeUid,
				// consult: this.detailForm.consultUid,
				losingOrderReasons: this.detailForm.losingOrderReasons,
			};
		},
		// 是否修改
		isUpdate() {
			const FLAG = JSON.stringify(this.feedbackForm) !== JSON.stringify(this.feedbackFormCopy);
			// console.log('feedbackForm_isUpdate', FLAG);
			return FLAG;
		},
		// 阶段 - 丢单
		isLosing() {
			return this.detailForm.stage == 10;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {
		// 订阅事件（注意这里要加多一层parent 使其与发布事件一致）
		eventBus.$on(`updateDetailForm_${this.$parent.$parent.$options.name}`, detailForm => {
			// 处理事件，更新本地的 detailForm 对象
			this.detailForm = detailForm;
			this.feedbackFormCopy = _.deepClone(this.feedbackForm); //克隆数据
		});
	},
	beforeDestroy() {
		eventBus.$off(`updateDetailForm_${this.$parent.$parent.$options.name}`);
	},
	destroyed() {
		this.detailForm = null;
		this.feedbackFormCopy = null;
	},
	// 方法集合
	methods: {
		// 打开合同
		openContract() {
			this.$emit('openContract');
		},

		// 电子合同上传
		uploadFile(item) {
			const isLt50M = item.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				this.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}
			this.detailForm.isUpLoadContract = true;
			const formData = new FormData();
			formData.append('file', item.file);

			this.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						this.detailForm.contractName = res.data.data.fileName;
						this.detailForm.contractUrl = res.data.data.path;
						this.detailForm.isUpLoadContract = false;
						this.saveEdit();
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.detailForm.isUpLoadContract = false;
					this.$message.warning(error.message);
				});
		},

		// 删除
		delFile() {
			this.$confirm('注意,该询盘合同删除后需重新上传, 是否继续操作?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					this.detailForm.contractName = '';
					this.detailForm.contractUrl = '';
					this.saveEdit();
				})
				.catch(() => {
					this.$message.info('已取消操作！');
				});
		},
		// 保存基本信息(修改询盘业务反馈信息)
		saveEdit: _.debounce(async function () {
			const API = 'feedbackUpdate';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.feedbackForm, consult: this.detailForm.consultUid }));
				if (res.data.success) {
					this.feedbackFormCopy = _.deepClone(this.feedbackForm); //克隆数据
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.detailForm.stage = '';
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		dateFormat: _.dateFormat,
	},
};
</script>
<style lang="scss">
#FeedbackInfo {
	.el-radio {
		margin-right: 10px;
	}
}
</style>
