<template>
	<!-- 开发项目步骤图 -->
	<div class="DevelopStepsCom project-steps-wrap">
		<span :class="rowData.status == 9 ? 'red' : 'color-999'">
			{{ jointString(' ', rowData.projectNo, rowData.projectName, rowData.projectManagerName, statusMap[rowData.status]) }}
		</span>
		<!-- 状态/类型 -->
		<el-steps class="project-steps" :space="space" :active="rowData.status">
			<el-step
				v-for="pItem in planTableData"
				:key="pItem.status"
				:status="getStatus(rowData, pItem)"
				:class="getStepClass(pItem.status)"
			>
				<template slot="title">
					<Tooltips class="max-w-60" :cont-str="pItem.target" :cont-width="50" />
				</template>

				<template slot="description">
					<!-- 项目阶段 -->
					<Tooltips :cont-str="dateFormat(rowData[pItem.time], 'MD')" class="max-w-120" :cont-width="120" />
				</template>
			</el-step>
		</el-steps>
	</div>
</template>
<script>
import { debounce, jointString, dateFormat } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'DevelopStepsCom',
	components: {},
	props: {
		rowData: { type: Object, default: () => {} },
		space: { type: Number, default: () => 100 },
	},
	data() {
		return {
			statusMap: {
				0: '规划中',
				1: '需求与方案评审通过',
				2: '功能分解完成',
				3: '开发计划完成',
				4: '开发全部完成',
				5: '测试全部完成',
				6: '功能验收通过',
				7: '发版准备就绪',
				8: '发版完成',
				9: '延误',
			},

			hasDelay: false,
			planTableData: [
				{ target: '需求与方案评审通过', description: '交付项目经理负责，技术经理确认', status: 1, time: 'demandTime' },
				{ target: '功能分解完成', description: '技术经理负责，交付项目经理确认', status: 2, time: 'functionTime' },
				{ target: '开发计划确定', description: '技术经理负责，交付项目经理确认', status: 3, time: 'planTime' },
				{ target: '开发全部完成', description: '开发项目经理负责，技术经理确认', status: 4, time: 'developmentTime' },
				{ target: '测试全部完成', description: '测试经理负责，开发项目经理确认', status: 5, time: 'testTime' },
				{ target: '功能验收通过', description: '交付项目经理负责，测试经理确认', status: 6, time: 'checkTime' },
				{ target: '发版准备就绪', description: '开发项目经理负责，技术经理确认', status: 7, time: 'prepareTime' },
				{ target: '发版完成', description: '技术经理负责，技术经理确认', status: 8, time: 'publishTime' },
			],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 进行状态
		getStatus(row, plan) {
			if (row.status == 9) {
				return 'error';
			}

			const tiemMap = {
				1: 'demandTime',
				2: 'functionTime',
				3: 'planTime',
				4: 'developmentTime',
				5: 'testTime',
				6: 'checkTime',
				7: 'prepareTime',
				8: 'publishTime',
			};

			if (row.status >= plan.status) {
				return 'success';
			} else if (row.status + 1 == plan.status) {
				const planeTime = this.$moment(row[tiemMap[row.status + 1]])
					.startOf('day')
					.valueOf(); //当前阶段计划时间
				const nowTime = this.$moment().startOf('day').valueOf(); //当前时间
				if (planeTime && nowTime > planeTime) {
					return 'error';
				}
				return 'process';
			} else {
				return 'wait';
			}
		},

		// 获取最后一个完成的状态4 的索引 如果没有则查询最后一个延误3的索引
		getActive(data) {
			const lastIndex3 = data?.findLastIndex(item => item?.status === 3);
			if (lastIndex3 !== -1) {
				return lastIndex3 + 1;
			}
			const lastIndex4 = data?.findLastIndex(item => item?.status === 4);
			return lastIndex4 + 1;
		},
		// el-step样式修改
		getStepClass: debounce(function () {
			// 步骤图标修改 性能不太行 后续在优化
			const errors = document.getElementsByClassName('el-step__icon-inner is-status el-icon-close');
			Array.from(errors).forEach(item => {
				item.classList.add('icon-third-icon-yanwu');
				item.classList.remove('el-icon-close');
			});
		}),

		jointString,
		dateFormat,
	},
};
</script>

<style lang="scss">
// el-steps样式调整
.DevelopStepsCom.project-steps-wrap {
	// width: max-content;
	.max-w-60 {
		max-width: 60px;
	}
	overflow-x: scroll;
	.project-steps {
		font-size: 12px !important;
		height: 28px;
		.el-step {
			zoom: 0.9;
			min-width: 100px;
			.el-step__main {
				position: absolute;
				top: -9px;
				right: 10px;
			}
		}
		.el-step__title {
			font-size: 12px;
			line-height: 24px;
		}
		.is-process {
			color: #ff9800;
			border-color: #ff9800;
		}
	}
}
</style>
