<!-- 任务规划-任务明细（添加和修改任务） -->
<template>
	<div class="workPlan-taskDetail">
		<div id="taskDetailCom" :class="moveToggle ? 'moveToggle' : 'moveToggle moveToggle-hide'">
			<div class="detail-wrapper">
				<!-- 头部标题 -->
				<div class="detail-head">
					<div class="detail-title">
						<div v-if="taskForm.pmtid" class="flex-align-center gap-10">
							<span :style="{ color: colorMap[taskForm.status] }">({{ statusMap[taskForm.status] }})</span>
							<span>【{{ taskForm.taskNo }}】</span>
							<Tooltips class="max-w-500" :cont-str="taskForm.taskName" :cont-width="300" />
							<span v-show="taskForm?.createUserInfo?.userName"> 创建人：{{ taskForm?.createUserInfo?.userName }} </span>
						</div>
						<div v-else class="flex-align-center">
							<span>新增任务 </span>
						</div>
					</div>

					<!-- 操作按钮 -->
					<div class="flex-align-center text-right gap-10">
						<!-- 翻页 -->
						<div class="flex-align-center gap-10" v-if="tableData.length > 1 && taskForm.pmtid">
							<el-button type="text" :disabled="nowIndex == 0" @click="changePage('back')"> 上一页</el-button>
							<el-select v-model="nowPmtid" placeholder="请选择" size="mini" filterable @change="changePage('find', nowPmtid)">
								<el-option
									v-for="(item, index) in tableData"
									:key="index"
									:label="jointString(' | ', item.taskNo, item.taskName, item.productUid?.userName)"
									:value="item.pmtid"
								>
									<div class="flex-align-center">
										<span class="w-100">{{ item.taskNo }}</span>
										<span class="w-200 ellipsis mr10" :title="item.taskName">{{ item.taskName }}</span>
										<span>{{ item.productUid?.userName || '--' }}</span>
									</div>
								</el-option>
							</el-select>
							<el-button type="text" :disabled="nowIndex == tableData.length - 1" @click="changePage('next')">下一页 </el-button>
						</div>
						<el-button type="text" class="el-icon-s-claim m0 p0" @click="isBug ? saveBug(taskForm) : updateTask('save')">
							保存
						</el-button>
						<el-button type="text" class="el-icon-arrow-left m0 p0" @click.stop="moveToggle = false">返回</el-button>
					</div>
				</div>
				<!-- 主体内容 -->
				<div class="detail-content p10">
					<!-- 任务表单 -->
					<el-form
						ref="taskFormRef"
						class="taskForm"
						:disabled="!taskAuth"
						:model="taskForm"
						:rules="formRules"
						label-width="120px"
						size="mini"
						label-position="right"
					>
						<el-row>
							<el-col :span="8">
								<el-form-item label="任务" prop="taskName" label-width="60px" class="label-left">
									<el-input placeholder="任务名称" v-model="taskForm.taskName"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="关联" prop="relevanceTaskNo">
									<el-autocomplete
										ref="autocomplete"
										v-model="taskForm.relevanceTaskNo"
										:fetch-suggestions="queryByInput"
										@select="handleSelect"
										@clear="handleSelect(null)"
										clearable
										placeholder="搜索关联其他任务"
									>
										<template slot-scope="{ item }">
											<Tooltips
												class="max-w-500"
												:cont-str="jointString(' | ', item.taskNo, item.taskName, item.productUid?.userName)"
												:cont-width="500"
											>
											</Tooltips>
										</template>
									</el-autocomplete>
								</el-form-item>
							</el-col>
							<el-col :span="7">
								<el-form-item label="分类" prop="taskClassify">
									<el-radio-group :disabled="isBug" v-model="taskForm.taskClassify">
										<el-radio :label="0">需求</el-radio>
										<el-radio :label="1">优化</el-radio>
										<el-radio :label="2">Bug</el-radio>
										<el-radio :label="3">杂项</el-radio>
									</el-radio-group>
								</el-form-item>
							</el-col>
							<el-col :span="5">
								<el-form-item label="优先级" prop="taskLevel">
									<el-radio-group :disabled="isBug" v-model="taskForm.taskLevel">
										<el-radio :label="0">高</el-radio>
										<el-radio :label="1">中</el-radio>
										<el-radio :label="2">低</el-radio>
									</el-radio-group>
								</el-form-item>
							</el-col>
						</el-row>
						<!-- 12 7 5 -->
						<el-row>
							<el-col :span="4">
								<el-form-item label="开发产出工时" prop="productTime" label-width="110px" class="label-left">
									<el-input v-model.trim="taskForm.productTime" placeholder="工时" @change="changeProductTime"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="难度系数" prop="difficulty">
									<el-input v-model="taskForm.difficulty" placeholder="系数"></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="要求转测日期" prop="endTime">
									<el-date-picker
										v-model="taskForm.endTime"
										value-format="timestamp"
										type="date"
										placeholder="要求转测日期"
										clearable
										format="MM/dd"
										class="W100"
										@change="taskForm.pmtid && updateTask('autoSave')"
									></el-date-picker>
								</el-form-item>
							</el-col>

							<el-col :span="4">
								<el-form-item label="开发者" prop="productUid">
									<el-select
										v-model="taskForm.productUid"
										placeholder="开发者"
										clearable
										filterable
										@change="taskForm.pmtid && updateTask('autoSave')"
									>
										<el-option v-for="item in developerList" :key="item.auid" :label="item.userName" :value="item.auid">
										</el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="3">
								<el-form-item label="测试产出工时" prop="testProductTime">
									<el-input
										v-model="taskForm.testProductTime"
										placeholder="工时"
										@change="taskForm.pmtid && updateTask('autoSave')"
									></el-input>
								</el-form-item>
							</el-col>

							<el-col :span="5">
								<el-form-item label="计划测试日期" prop="planTestTime">
									<el-date-picker
										v-model="taskForm.planTestTime"
										class="W100"
										value-format="timestamp"
										type="date"
										placeholder="选择计划测试日期"
										clearable
										format="MM/dd"
										@change="taskForm.pmtid && updateTask('autoSave')"
									></el-date-picker>
								</el-form-item>
							</el-col>
						</el-row>

						<!-- 12 7 5 -->
						<el-row>
							<el-col :span="4">
								<el-form-item label="提出人" prop="mentionUid" label-width="80px" class="label-left">
									<el-select v-model="taskForm.mentionUid" placeholder="提出人" clearable filterable>
										<el-option v-for="item in implementList" :key="item.auid" :label="item.userName" :value="item.auid">
										</el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="来源客户" prop="customer"> <el-input v-model="taskForm.customer"></el-input> </el-form-item>
							</el-col>
							<el-col :span="4">
								<el-form-item label="项目" prop="pmcid">
									<el-cascader
										ref="elCascader"
										v-model="taskForm.pmcid"
										:options="projectList"
										:show-all-levels="false"
										:props="{
											emitPath: false,
											checkStrictly: true,
											expandTrigger: 'hover',
											children: 'selectProjectGanttChartVOS',
											label: 'classifyName',
											value: 'pmcid',
										}"
										clearable
										filterable
										@visible-change="elCascaderOnlick('elCascader')"
										@expand-change="elCascaderOnlick('elCascader')"
										@change="changeProject"
									></el-cascader>
								</el-form-item>
							</el-col>

							<el-col :span="7">
								<el-form-item label="Bug负责人" prop="bugProductUid">
									<el-row>
										<el-col :span="12">
											<el-select v-model="taskForm.bugProductUid" placeholder="开发人员" clearable filterable>
												<el-option v-for="item in developerList" :key="item.auid" :label="item.userName" :value="item.auid">
												</el-option>
											</el-select>
										</el-col>
										<el-col :span="12">
											<el-select v-model="taskForm.bugTestUid" placeholder="测试人员" clearable filterable>
												<el-option v-for="item in testerList" :key="item.auid" :label="item.userName" :value="item.auid">
												</el-option>
											</el-select>
										</el-col>
									</el-row>
								</el-form-item>
							</el-col>

							<el-col :span="5">
								<el-form-item label="测试人" prop="projectTurnTest">
									<el-select
										v-model="taskForm.projectTurnTest"
										placeholder="测试人员"
										clearable
										filterable
										class="W100"
										@change="taskForm.pmtid && updateTask('autoSave')"
									>
										<el-option v-for="item in testerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>
						<el-form-item label="关联信息" prop="relevanceTaskRemark" label-width="80px" class="label-left">
							<div class="flex-align-center gap-10 color-666">
								<Tooltips class="max-w-500 fs-14" :cont-str="taskForm.relevanceTaskRemark" :cont-width="500"> </Tooltips>
								<el-button v-show="taskForm.relevanceTaskRemark" type="text" class="" @click="handleSelect(null)">
									解除关联
								</el-button>
							</div>
						</el-form-item>
					</el-form>

					<!-- 富文本编辑器 -->
					<wang-editor v-if="moveToggle" class="W100" v-model="taskForm.content" :editorHeight="500"></wang-editor>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { debounce, dateFormat, checkRequired, resetValues, deepClone, jointString } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import wangEditor from '@/components/WangEditor/wangEditor';
export default {
	name: 'taskDetailCom',
	props: {
		// 表格数据
		tableData: {
			type: Array,
			default: () => [],
		},
		// 项目信息
		projectForm: {
			type: Object,
			default: () => {},
		},
		// 是否BUG
		isBug: {
			type: Boolean,
			default: false,
		},
	},
	components: {
		wangEditor,
	},
	data() {
		return {
			moveToggle: false, //滑动控制
			titleName: '任务详情',
			taskAuth: true, //权限控制
			nowIndex: 0, // 当前任务索引
			nowPmtid: '', // 当前任务id
			// 任务表单
			taskForm: {
				classifyName: '',
				relevancePmtid: '',
				createUserInfo: { arid: null, auid: null, userName: '' },
				relevanceTaskVOs: { arid: null, auid: null, userName: '' },
				actualEndTime: '',
				actualStartTime: '',
				bugProductUid: '',
				bugTestUid: '',
				projectTurnCheckUid: '',
				content: '',
				customer: '',
				difficulty: '1.0',
				endTime: '',
				mentionUid: '',
				pmcid: '',
				pmid: '',
				pmtid: '',
				productTime: '',
				productUid: '',
				projectName: '',
				startTime: '',
				status: '',
				taskClassify: 0,
				taskLevel: 1,
				taskName: '',
				testProductTime: '',
				planTestTime: '',

				// 关联信息
				relevanceTaskNo: '',
				relevanceTaskRemark: '',
			},
			taskFormCopy: {},

			projectList: [],

			// 颜色和状态
			statusMap: {
				0: '延误',
				1: '计划中',
				2: '开发中',
				3: '已转测',
				4: '完成',
				5: '转测不通过',
				6: '测试延误',
			},
			colorMap: {
				0: '#ec808d', //红 开发延误
				1: '#bababa', //灰 计划中
				2: '#ab47bc', //紫 开发中
				3: '#2196f3', //蓝 已转测
				4: '#28d094', //绿 完成
				5: '#facd91', //橙 转测不通过
				6: '#ec808d', //红 测试延误
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userList']), //当前登录用户信息（含团队/菜单/权限等）

		//开发人员列表(标签：开发)
		developerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('开发')) || [];
		},
		//测试人员列表(标签：测试)
		testerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('测试')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
		// 表单规则
		formRules() {
			// 基础验证规则
			const baseRules = {
				taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
				taskClassify: [{ required: true, message: '请选择分类', trigger: 'blur' }],
				taskLevel: [{ required: true, message: '请选择优先级', trigger: 'blur' }],
				productTime: [{ required: true, message: '请输入开发产出工时', trigger: 'blur' }],
				difficulty: [{ required: true, message: '请输入难度系数', trigger: 'blur' }],
				endTime: [{ required: true, message: '请选择要求转测日期', trigger: 'blur' }],
				pmcid: [{ required: true, message: '请选择项目', trigger: 'blur' }],
			};

			// 如果是BUG，添加额外的验证规则
			if (this.isBug || this.taskForm.taskClassify == 2) {
				return {
					...baseRules,
					bugProductUid: [{ required: true, message: '请选择BUG负责人（开发）', trigger: 'blur' }],
					bugTestUid: [{ required: true, message: '请选择BUG负责人（测试）', trigger: 'blur' }],
				};
			}

			return baseRules;
		},
	},
	// 监控data中的数据变化
	watch: {
		moveToggle(newVal) {
			if (!newVal) {
				this.taskForm = resetValues(this.taskForm);

				this.taskForm.difficulty = '1.0';
				this.taskForm.taskClassify = 0;
				this.taskForm.taskLevel = 1;

				this.$emit('close');
			} else {
				this.$nextTick(() => {
					this.$refs.taskFormRef?.clearValidate(); // 清空表单验证
					// this.$refs.taskFormRef?.resetFields(); // 清空表单数据
				});
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryProjectList();
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		//模糊匹配
		queryByInput(queryString, cb) {
			const { pmid = '', pmtid = '' } = this.taskForm;
			const API = 'likeQueryTask';
			this.$axios[API](JSON.stringify({ pmid, pmtid, queryParam: queryString || '' }))
				.then(res => {
					if (res.data.success) {
						cb(res.data.data);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
		handleSelect(item) {
			this.taskForm.relevancePmtid = item?.pmtid || '';
			this.taskForm.queryParam = item?.taskName || '';
			this.taskForm.relevanceTaskNo = item?.taskNo || '';
			this.taskForm.relevanceTaskRemark = item
				? `${item?.productUid?.userName || ''} | ${item.taskName}| 
			${this.dateFormat(item.endTime)}  |当前状态：${this.statusMap[item.status]}`
				: '';
			//添加操作时该任务pmtid未生成 将关联的pmtid另存到添加的接口的relevancePmtid中
			if (!this.taskForm.pmtid) {
				this.$message.success(`关联成功，任务：${item.taskNo}`);
				return;
			}
			const API = 'relevanceTask';
			const DATA = JSON.stringify({
				pmid: this.taskForm.pmid,
				pmtid: this.taskForm.pmtid,
				queryParam: this.taskForm.queryParam,
				relevancePmtids: this.taskForm.relevancePmtid ? [this.taskForm.relevancePmtid] : [],
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						if (item === null) {
							this.$message.success(`关联解绑成功!`);
							this.taskForm.relevanceTaskRemark = '';
							this.taskForm.relevanceTaskNo = '';
							// this.$nextTick(() => {
							// 	this.$refs.autocomplete.activated = true;
							// 	this.$refs.autocomplete.focus();
							// });
						}
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
		// 打开任务详情
		open(row = {}) {
			// 获取项目信息
			this.taskForm = { ...this.taskForm, ...deepClone(this.projectForm), ...deepClone(row) };

			if (row?.pmtid) {
				this.queryTaskData(row);
			} else {
				// 默认设置提出人为项目交付经理 如果businessPeople是对象，则取auid
				if (typeof this.projectForm?.businessPeople === 'object') {
					this.taskForm.mentionUid = this.projectForm?.businessPeople?.auid || null;
				} else {
					this.taskForm.mentionUid = this.projectForm?.businessPeople || null;
				}
				// 默认设置测试人为项目测试经理 如果testManager是对象，则取auid
				if (typeof this.projectForm?.testManager === 'object') {
					this.taskForm.projectTurnTest = this.projectForm?.testManager?.auid || null;
				} else {
					this.taskForm.projectTurnTest = this.projectForm?.testManager || null;
				}

				this.taskForm.productUid = row?.productUid || null;
				this.taskForm.endTime = ''; // 默认设置要求转测日期为空
				this.taskForm.status = ''; // 默认设置状态为空
				console.log('🚨 任务表单初始化：', this.taskForm);
			}

			this.moveToggle = true;
		},
		// 查询任务数据
		async queryTaskData(row) {
			const API = 'selectTaskDetailByPmtid';
			try {
				const res = await this.$axios[API](JSON.stringify({ pmtid: row.pmtid }));
				if (res.data.success) {
					const rowData = { ...row, ...res.data.data };
					// 人员id转换
					const keyArr = ['bugProductUid', 'bugTestUid', 'mentionUid', 'productUid', 'projectTurnCheckUid', 'testManagement'];
					keyArr.map(key => {
						rowData[key] = rowData[key]?.auid || '';
					});
					rowData['projectTurnTest'] = rowData.projectTurnCheckUid; //字段名称不一致特殊处理

					// 关联信息提取
					rowData?.relevanceTaskVOs?.map(item => {
						rowData.relevanceTaskRemark = item?.pmtid
							? `${item?.productUid?.userName || ''} | ${item.taskName}| 
						${this.dateFormat(item.endTime)}  |当前状态：${this.statusMap[item.status]}`
							: '';
						rowData.relevanceTaskNo = item.taskNo;
					});
					this.taskForm = { ...this.taskForm, ...deepClone(rowData) };
					this.taskFormCopy = deepClone(this.taskForm);
					this.nowIndex = this.tableData.findIndex(item => item.pmtid == this.taskForm.pmtid);
					this.nowPmtid = this.taskForm.pmtid;
					console.log('🚨 任务表单查询：', this.taskForm);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 上下页切换
		async changePage(type, pmid) {
			// 保存当前任务
			if (JSON.stringify(this.taskForm) !== JSON.stringify(this.taskFormCopy)) {
				await this.updateTask('autoSave', deepClone(this.taskForm));
			}

			// 获取新索引对应的数据
			if (type == 'back') {
				this.nowIndex > 0 ? --this.nowIndex : (this.nowIndex = this.tableData.length - 1);
			} else if (type == 'next') {
				this.nowIndex < this.tableData.length - 1 ? ++this.nowIndex : (this.nowIndex = 0);
			} else if ('find' && pmid) {
				this.nowIndex = this.tableData.findIndex(item => item.pmtid == pmid);
			}
			this.queryTaskData(this.tableData[this.nowIndex]);
		},
		// 输入开发产出工时(自动计算)
		changeProductTime() {
			if (this.taskForm.productTime) {
				this.taskForm.testProductTime = Number(this.taskForm.productTime / 4).toFixed(1);
				this.taskForm.pmtid && this.updateTask('autoSave');
			}
		},
		// 修改项目
		changeProject() {
			this.$nextTick(() => {
				const nodeData = this.$refs['elCascader'].getCheckedNodes();
				if (!nodeData) return;
				this.taskForm.pmcid = nodeData[0]?.data.pmcid || '';
				this.taskForm.pmid = nodeData[0]?.data.pmid || '';
				this.taskForm.projectName = nodeData[0]?.pathLabels[0] || '';
				this.taskForm.pmtid && this.updateTask('autoSave');
			});
		},
		// 研发工作台接收BUG，因为后台会调添加接口，所以不需要在updateTask调用接口处理
		saveBug: debounce(function (taskForm) {
			if (checkRequired(taskForm, this.formRules)) return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			this.$emit('save', taskForm);
			this.moveToggle = false;
		}),
		// 添加/修改任务详情
		updateTask: debounce(function (type = 'save', taskForm = this.taskForm) {
			if (checkRequired(taskForm, this.formRules)) return; // 如果有空字段，则停止执行后面的逻辑，并输出提示

			const URL = taskForm.pmtid ? 'updateProjectTask' : 'addProjectTask';
			const DATA = JSON.stringify({ ...taskForm });
			this.$axios[URL](DATA)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.taskFormCopy = deepClone(taskForm);
						if (taskForm.pmtid && type == 'autoSave') return;
						this.$emit('save', taskForm);
						this.$emit('close', taskForm.pmtid ? 'update' : 'add');
						this.moveToggle = false;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addProjectTask |' + error);
				});
		}),

		// 获取项目列表
		queryProjectList() {
			this.projectList = [];
			this.$axios
				.selectAllONGoingProject(JSON.stringify({ statusList: [0, 1, 2, 3, 4, 5, 6, 7, 9] }), { skipCancel: true })
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item.selectProjectGanttChartVOS[0].classifyName = item.projectName;
							this.projectList.push(item.selectProjectGanttChartVOS[0]);
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAllONGoingProject |' + error);
				});
		},
		elCascaderOnlick(refObj, flag) {
			const that = this;
			if (flag === undefined) {
				setTimeout(function () {
					document.querySelectorAll('.el-cascader-node__label').forEach(el => {
						el.onclick = function () {
							this.previousElementSibling.click();
							that.$refs[refObj].dropDownVisible = false;
						};
					});
					document.querySelectorAll('.el-cascader-panel .el-radio').forEach(el => {
						el.onclick = function () {
							that.$refs[refObj].dropDownVisible = false;
						};
					});
				}, 100);
			}
		},

		dateFormat, //日期format
		jointString, // 拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.workPlan-taskDetail {
	#taskDetailCom {
		.sticky-bottom-0 {
			position: sticky;
			bottom: 0;
		}
	}
}
</style>

<style lang="scss">
.workPlan-taskDetail {
	#taskDetailCom {
		.taskForm {
			width: 100%;

			.el-form-item--mini.el-form-item {
				margin-bottom: 15px;
			}

			.label-left {
				.el-form-item__label {
					text-align: left;
				}
			}
		}

		.el-collapse-item__header {
			font-size: 14px;
			color: #606266;
			height: 28px;
			line-height: 28px;
		}
	}
}
</style>
