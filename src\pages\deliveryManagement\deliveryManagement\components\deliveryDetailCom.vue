<template>
	<div>
		<!-- 项目交付详情 -->
		<div class="deliveryDetailCom" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
			<div class="detail-wrapper">
				<div class="detail-head">
					<span class="detail-title">{{ titleName }}</span>
					<el-button type="text" class="el-icon-arrow-left" @click="clearDetailForm">返回</el-button>
				</div>
				<!-- 明细详情弹窗 -->
				<div class="detail-content">
					<p class="detail-content-title">基本信息 </p>
					<BaseTableForm :detailForm="detailForm" :formList="formList" @update="detailForm = $event">
						<template #th-xxx></template>
						<template #td-dealMonth="{ item, formData }">
							<span>{{ dateFormat(formData[item.prop], 'YM') }}</span>
						</template>
						<template #td-contractType="{ item, formData }">
							<span>{{ { 0: '直销', 1: '合伙' }[formData[item.prop]] }}</span>
						</template>
						<template #td-contractDate="{ item, formData }">
							<span>{{ dateFormat(formData[item.prop], 'line') }}</span>
						</template>
						<template #td-contractName="{ item, formData }">
							<FilePopover
								class="inline-block w-150"
								trigger="click"
								v-if="formData[item.prop]"
								:url="formData.contractUrl"
								:content="formData[item.prop]"
							/>
							<span v-else type="text">无</span>
						</template>
						<template #td-feeCalculateTime="{ item, formData }">
							<span>{{ dateFormat(formData[item.prop], 'line') }}</span>
						</template>
						<template #td-projectType="{ item, formData }">
							<span>{{ projectTypeMap[formData[item.prop]] }}</span>
						</template>
						<template #td-travelExpensesParty="{ item, formData }">
							<span>{{ expensePartyMap[formData[item.prop]] }}</span>
						</template>
						<template #td-deliverIssuesUrl="{ item, formData }">
							<a class="ellipsis max-w-300" :href="formData[item.prop]" target="_blank">
								{{ formData[item.prop] }}
							</a>
						</template>
					</BaseTableForm>

					<!-- 项目阶段 -->
					<deliveryStage :detailForm="detailForm" :userList="userList" @update="queryDetailData" />
					<!-- 交付过程 -->
					<div>
						<p class="detail-content-title">交付过程 </p>
						<table class="base-table input-border-none" cellpadding="5" cellspacing="0">
							<tr>
								<th>
									<div class="flex-align-center">
										<span class="label-required">交付内容</span>
										<el-date-picker
											class="w-200 ml10 p0"
											size="mini"
											v-model="processForm.releaseTime"
											value-format="timestamp"
											type="datetime"
											format="yyyy-MM-dd HH:mm"
											placeholder="选择日期后再输入内容"
											:clearable="false"
											:default-value="new Date()"
										></el-date-picker>
									</div>
								</th>
								<th>
									<div class="flex-align-center">
										<span>下一步计划</span>
										<el-date-picker
											class="w-200 ml10 p0"
											size="mini"
											v-model="processForm.nextStep"
											value-format="timestamp"
											type="datetime"
											format="yyyy-MM-dd HH:mm"
											placeholder="选择日期后再输入内容"
											:default-value="new Date()"
										></el-date-picker>
									</div>
								</th>
							</tr>
							<tr>
								<td>
									<el-input
										ref="focusInput"
										:disabled="!processForm.releaseTime"
										class="mr20"
										type="textarea"
										:autosize="{ minRows: 2, maxRows: 4 }"
										v-model="processForm.content"
										placeholder="请输入交付情况..."
									></el-input>
								</td>
								<td>
									<el-input
										:disabled="!processForm.nextStep"
										class="mr20"
										type="textarea"
										:autosize="{ minRows: 2, maxRows: 4 }"
										v-model="processForm.nextPlan"
										placeholder="请输入以何种方式联系谁达成什么目标,如：微信联系张总了解其对报价单的看法"
									></el-input>
								</td>
							</tr>
						</table>
						<div class="bottom-button">
							<el-button @click="savePublish" :disabled="!isPublish" :type="isPublish ? 'primary' : ''">发布</el-button>
						</div>
						<!-- 交付记录卡片 -->
						<div class="follow-card-wrapper">
							<div class="follow-card border flex" v-for="item in detailForm.deliverProcessVOS" :key="item.dpid">
								<div class="follow-card-item" :class="{ W50: item.nextPlan || item.nextStep }">
									<div class="follow-card-title flex-align-center">
										<div class="follow-card-name fs-16 bolder">
											{{ item.uname }}
										</div>
										<div class="follow-card-time color-999 ml15 mr15">
											{{ dateFormat(item.releaseTime, 'lineM') }}
										</div>
										<i class="el-icon-edit-outline hover-green" @click="openDialogProcess('交付', item)"></i>
									</div>
									<pre class="follow-card-content mt10 mb0">{{ item.content }}</pre>
								</div>
								<!-- 下一步内容 -->
								<div
									v-if="item.nextPlan || item.nextStep"
									class="follow-card-item ml20 W50"
									:class="{ 'cancel-plan': item.planStatus }"
								>
									<div class="follow-card-title flex-align-center">
										<div class="follow-card-name fs-16 bolder"> 下一步计划</div>
										<div class="follow-card-time color-999 ml15 mr15">
											{{ dateFormat(item.nextStep, 'lineM') }}
										</div>
										<i class="el-icon-edit-outline hover-green" @click="openDialogProcess('计划', item)"></i>
									</div>
									<pre class="follow-card-content mt10 mb0">{{ item.nextPlan }}</pre>
								</div>
							</div>
						</div>
					</div>
					<div>
						<p class="detail-content-title">
							<span>行业案例</span>
							<el-button type="text" class="ml10" @click="openIndustryCase()">
								{{ detailForm.selectIndustryCasesVO ? `${industryInfo}` : ' + 添加行业案例' }}
							</el-button>
						</p>
					</div>
					<div>
						<p class="detail-content-title">项目验收报告 </p>
						<el-popover v-if="detailForm.acceptanceName" width="160" placement="left">
							<p>请选择您要执行的操作！</p>
							<div class="flex-align-center flex-justify-between">
								<el-upload
									action=""
									accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
									:http-request="uploadFile"
									:show-file-list="false"
								>
									<el-button type="text" size="mini" class="color-999"> 替换 </el-button>
								</el-upload>
								<el-button size="mini" @click="downloadFile(detailForm.acceptanceUrl)">下载</el-button>
							</div>
							<el-button type="text" slot="reference">
								<Tooltips :cont-str="detailForm.acceptanceName" :cont-width="100"> </Tooltips>
							</el-button>
						</el-popover>

						<el-upload
							v-else
							action=""
							accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
							:http-request="uploadFile"
							:show-file-list="false"
						>
							<span class="button-add">+</span>
						</el-upload>

						<div class="mt20 mb20">
							<el-button v-show="!detailForm.status" class="m0" @click="saveFinish(1)">确认项目完工</el-button>
							<el-button v-show="detailForm.status == 1" @click="saveFinish(0)" class="m0 button-error">撤销完工状态</el-button>
						</div>
					</div>
				</div>
			</div>

			<!-- 交付时间/计划弹窗 -->
			<el-dialog
				width="30%"
				:visible.sync="dialogProcess"
				:close-on-click-modal="false"
				:append-to-body="true"
				@close="closeDialog"
			>
				<span slot="title">修改交付过程信息</span>

				<el-form :model="processForm" :rules="formRules" label-width="100px" label-position="left" @submit.native.prevent>
					<div v-if="dialogTitle == '交付'">
						<el-form-item label="交付时间" prop="releaseTime">
							<el-date-picker
								class="W100"
								v-model="processForm.releaseTime"
								value-format="timestamp"
								type="datetime"
								format="yyyy-MM-dd HH:mm"
								placeholder="请选择日期时间"
								:clearable="false"
								:default-value="new Date()"
							></el-date-picker>
						</el-form-item>
						<el-form-item label="交付内容" prop="content">
							<el-input
								type="textarea"
								:autosize="{ minRows: 2, maxRows: 4 }"
								v-model="processForm.content"
								placeholder="请输入以何种方式联系谁达成什么目标,如：微信联系张总了解其对报价单的看法"
							></el-input>
						</el-form-item>
					</div>

					<div v-else>
						<el-form-item label="计划日期" prop="nextStep">
							<el-date-picker
								class="W100"
								v-model="processForm.nextStep"
								value-format="timestamp"
								type="datetime"
								format="yyyy-MM-dd HH:mm"
								placeholder="选择日期后再输入内容"
								:clearable="false"
								:default-value="new Date()"
							></el-date-picker>
						</el-form-item>

						<el-form-item label="计划内容" prop="nextPlan">
							<el-input
								type="textarea"
								:autosize="{ minRows: 2, maxRows: 4 }"
								v-model="processForm.nextPlan"
								placeholder="请输入以何种方式联系谁达成什么目标,如：微信联系张总了解其对报价单的看法"
							></el-input>
						</el-form-item>

						<el-form-item label="计划状态" prop="planStatus">
							<el-radio-group v-model="processForm.planStatus">
								<el-radio :label="0">进行</el-radio>
								<el-radio :label="1">取消</el-radio>
							</el-radio-group>
						</el-form-item>
					</div>
				</el-form>

				<span slot="footer">
					<el-button @click="closeDialog">取 消</el-button>
					<el-button type="primary" @click="savePublish('update')">确 定</el-button>
				</span>
			</el-dialog>
		</div>

		<!-- 行业案例明细组件 -->
		<industryCaseDetail
			ref="industryCaseDetail"
			:industryOptions="industryOptions"
			:productOptions="productOptions"
			@close="queryDetailData(1)"
		/>
	</div>
</template>
<script>
import { jointString, dateFormat, deepClone, resetValues } from '@/util/tool';
import FilePopover from '@/components/FilePopover.vue';
import BaseTableForm from '@/components/BaseTableForm';
import { mapGetters } from 'vuex';
import deliveryStage from './deliveryStage.vue';
import industryCaseDetail from '@/pages/customerManagement/industryCase/industryCaseDetail.vue';
import { projectTypeOptions, projectTypeMap, expensePartyMap } from '@/assets/js/contractSource';
import { industryOptions, productOptions } from '@/assets/js/inquirySource.js';

export default {
	name: 'deliveryDetailCom',
	components: { FilePopover, BaseTableForm, deliveryStage, industryCaseDetail },
	props: {
		userList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	data() {
		return {
			showCom: '', //控制弹窗显隐
			titleName: '', //滑窗标题

			formList: [
				[
					{ name: '询盘单号', prop: 'number', class: 'W10', type: 'text' },
					{ name: '公司工商注册名称', prop: 'registeredBusinessName', class: 'W20', type: 'text', colspan: 2 },
					{ name: '业务顾问', prop: 'salesmanName', class: 'W10', type: 'text' },
					{ name: '实施顾问', prop: 'implementName', class: 'W10', type: 'text' },
					{ name: '咨询', prop: 'consultName', class: 'W10', type: 'text' },
					{ name: 'BPUL', prop: 'businessPartnerUnitName', class: 'W10', type: 'text' },
					{ name: '成交月份', prop: 'dealMonth', class: 'W10', type: 'text' },
					{ name: '成交金额（万元）', prop: 'dealAmount', class: 'W10', type: 'text' },
				],
				[
					{ name: '合同类型', prop: 'contractType', class: 'W10', type: 'text' },
					{ name: '合同编号', prop: 'contractNo', class: 'W10', type: 'text' },
					{ name: '合同日期', prop: 'contractDate', class: 'W10', type: 'text' },
					{ name: '电子合同文件', prop: 'contractName', class: 'W10', type: 'text' },
					{ name: '年费起算日期', prop: 'feeCalculateTime', class: 'W10', type: 'text' },
					{ name: '团队ID', prop: 'teamCode', class: 'W10', type: 'text' },
					{ name: '团队简称', prop: 'teamName', class: 'W10', type: 'text' },
					{ name: '项目名称', prop: 'projectName', class: 'W10', type: 'text' },
					{ name: '项目类型', prop: 'projectType', class: 'W10', type: 'text' },
				],
				[
					{ name: '辅导天数', prop: 'tutoringDays', class: 'W10', type: 'text' },
					{ name: '差旅费用承担方', prop: 'travelExpensesParty', class: 'W10', type: 'text', colspan: 2 },
					{ name: '问题与需求清单', prop: 'deliverIssuesUrl', class: 'W10', type: 'url', colspan: 3 },
					{ name: '', prop: 'other', class: 'W10', type: 'text', colspan: 3 },
				],
			],

			dialogProcess: false, //交付过程弹窗
			dialogTitle: '', // 弹窗标题

			// 交付过程表单
			processForm: {
				content: '',
				dmid: '',
				dpid: '',
				nextPlan: '',
				nextStep: '',
				planStatus: '',
				releaseTime: new Date().getTime(),
			},
			formRules: {
				releaseTime: [{ required: true, message: '请选择交付日期', trigger: 'change' }],
				content: [{ required: true, message: '请输入交付内容', trigger: 'blur' }],
				nextStep: [{ required: true, message: '请选择计划日期', trigger: 'change' }],
				nextPlan: [{ required: true, message: '请输入计划内容', trigger: 'blur' }],
				planStatus: [{ required: true, message: '请输入计划状态', trigger: 'change' }],
			},

			detailForm: {
				//详情
				deliverProcessDTOS: [], //交付过程
				deliverStageManagementVOS: [], //项目阶段
			},
			detailFormCopy: {},

			projectTypeMap, //项目类型
			expensePartyMap, // 费用承担方
			industryOptions, // 行业
			productOptions, // 产品
		};
	},
	created() {},
	computed: {
		// 是否可发布
		isPublish() {
			return this.processForm.releaseTime && this.processForm.content;
		},
		industryInfo() {
			if (!this.detailForm.selectIndustryCasesVO) {
				return '';
			}
			const { type, isModelFactory, project, status } = this.detailForm.selectIndustryCasesVO;
			return jointString(' | ', type, isModelFactory ? '样板工厂' : '非样板工厂', project, status ? '已交付' : '未交付');
		},
	},
	watch: {
		'detailForm.deliverStageManagementVOS'(newVal) {
			if (!newVal || newVal.length == 0) {
				return;
			}
			if (!newVal[0].reportName) {
				newVal[0].showButton = true;
				return;
			}
			let unique = false;
			for (let i = 1; i < newVal.length - 1; i++) {
				if (newVal[i - 1].reportName && !newVal[i].reportName) {
					unique = true;
					newVal[i].showButton = true;
				}
			}
			if (!unique) {
				newVal[newVal.length - 1].showButton = true;
			}
		},
	},
	mounted() {},
	destroyed() {
		this.detailForm = null;
		this.detailFormCopy = null;
	},
	methods: {
		// 打开行业案例
		openIndustryCase() {
			this.$refs.industryCaseDetail.showDetailCom(
				this.detailForm.selectIndustryCasesVO || {
					dmid: this.detailForm.dmid,
					type: '行业案例',
					project: this.detailForm.projectName,
					status: this.detailForm.status || 0,
					businessAdviserId: this.detailForm.salesman || '',
					implement: this.detailForm?.implement || '',
					creatorId: this.detailForm?.implement || '',
				},
			);
		},
		// 打开交付/计划弹窗
		openDialogProcess(title, item) {
			this.dialogTitle = title;
			// 交付计划
			this.processForm = {
				content: item.content,
				planStatus: item.planStatus,
				dmid: this.detailForm.dmid,
				dpid: item.dpid,
				nextPlan: item.nextPlan,
				nextStep: item.nextStep,
				releaseTime: item.releaseTime,
			};
			this.dialogProcess = true;
		},

		// 发布交付过程进程信息
		savePublish(type) {
			let api = 'releaseProcess';
			if (type == 'update') {
				api = 'updateReleaseProcess';
				if (this.dialogTitle == '交付') {
					if (!this.processForm.releaseTime || !this.processForm.content) {
						this.$message.warning('请完善交付时间/交付内容');
						return;
					}
				} else {
					if (!this.processForm.nextPlan || !this.processForm.nextStep) {
						this.$message.warning('请完善计划时间/计划内容');
						return;
					}
				}
			}
			this.processForm.dmid = this.detailForm.dmid;
			this.$axios[api](JSON.stringify({ ...this.processForm }))
				.then(res => {
					if (res.data.success) {
						this.processForm.releaseTime = new Date().getTime();
						this.processForm.content = '';
						this.processForm.nextPlan = '';
						this.processForm.nextStep = '';
						this.dialogProcess = false;
						this.queryDetailData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('releaseProcess |' + error);
				});
		},

		//项目报告上传
		uploadFile(item) {
			const self = this;
			const isLt50M = item.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				self.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', item.file);

			self.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						self.detailForm.acceptanceName = res.data.data.fileName;
						self.detailForm.acceptanceUrl = res.data.data.path;
						self.saveFinish(1);
					} else {
						self.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					self.$message.warning(error.message);
				});
		},
		// 保存确认/撤回完工（调用修改接口改状态和完工报告）
		saveFinish(status) {
			// 项目类型(1 OEE版本 5 硬件/10 OEE续费/11 MES续费) 项目完工时，不需要添加行业案例
			const projectType = this.detailForm.projectType;
			if (status == 1 && !this.detailForm.selectIndustryCasesVO && ![1, 5, 10, 11].includes(projectType)) {
				this.$message.warning('请添加行业案例');
				return;
			}
			this.detailForm.status = status;
			const str = JSON.stringify({
				idid: this.detailForm.idid,
				tid: this.detailForm.tid,
				dmid: this.detailForm.dmid,
				registeredBusinessName: this.detailForm.registeredBusinessName,
				twid: this.detailForm.twid,
				status: this.detailForm.status,
				acceptanceName: this.detailForm.acceptanceName,
				acceptanceUrl: this.detailForm.acceptanceUrl,
				operation: 'project-complete', // 2024年7月18日14:04:00 新增 operation 操作标识字段用于 project-complete 标识来自项目完工操作
			});
			this.$axios
				.addDeliverManagement(str)
				.then(res => {
					if (res.data.success) {
						// this.queryDetailData(this.detailForm.dmid);
						this.$succ('操作成功!');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addDeliverManagement |' + error);
				});
		},
		closeDialog() {
			this.queryDetailData();
			this.editForm.complateMonthApply = '';
			this.editForm.postpone = '0';
			this.editForm.remark = '';
			this.processForm = resetValues(this.processForm);

			this.dialogProcess = false;
			this.dialogDelay = false;
			this.dialogReport = false;
		},

		downloadFile(url) {
			// this.detailForm.deliverStageManagementVOS[index].reportUrl
			// let url = row.reportUrl ? row.reportUrl : row.url
			window.open(url, '_blank');
		},

		//获取明细信息
		queryDetailData() {
			const str = JSON.stringify({ dmid: this.detailForm.dmid });
			this.$axios
				.selectDeliverManagementDetail(str)
				.then(res => {
					if (res.data.success) {
						this.detailForm = res.data.data;
						this.$set(this.detailForm, 'salesmanName', res.data.data.salesman?.userName || '');
						this.$set(this.detailForm, 'implementName', res.data.data.implement?.userName || '');
						this.$set(this.detailForm, 'talkName', res.data.data.talk?.userName || '');
						this.$set(this.detailForm, 'businessPartnerUnitName', res.data.data.businessPartnerUnit?.userName || '');
						this.$set(this.detailForm, 'consultName', res.data.data.consulting?.userName || '');

						this.$set(this.detailForm, 'salesman', res.data.data.salesman?.auid || '');
						this.$set(this.detailForm, 'implement', res.data.data.implement?.auid || '');
						this.$set(this.detailForm, 'talktrade', res.data.data.talk?.auid || '');
						this.$set(this.detailForm, 'businessPartnerUnitUid', res.data.data.businessPartnerUnit?.auid || '');
						this.$set(this.detailForm, 'consult', res.data.data.consulting?.auid || '');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectDeliverManagementDetail |' + error);
				});
		},

		//显示弹窗
		showDetailCom(rowData) {
			if (rowData) {
				this.detailForm = { ...this.detailForm, ...deepClone(rowData) };
				this.queryDetailData();
				this.titleName = '交付详情';
			} else {
				this.titleName = '添加交付';
				this.detailFormCopy = deepClone(this.detailForm);
			}

			this.releaseTime = new Date();
			this.showCom = true;
		},
		// 关闭组件并做修改判断
		closeCom() {
			let isSameData = true;
			isSameData = JSON.stringify(this.detailForm) == JSON.stringify(this.detailFormCopy);
			console.log('isSameData', isSameData);
			if (!isSameData) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出操作？`, '重要提醒', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.clearDetailForm();
					})
					.catch(() => {
						return;
					});
			} else {
				this.clearDetailForm();
			}
		},
		//点击返回
		clearDetailForm() {
			this.showCom = false;
			this.isUpLoading = false;
			this.detailForm = resetValues(this.detailForm);
			this.$emit('close');
		},

		dateFormat: dateFormat, //日期format
	},
};
</script>
<style lang="scss">
.deliveryDetailCom {
	.el-input-group__prepend {
		background-color: transparent;
	}

	.el-date-picker {
		.el-input__inner {
			border: none;
		}
	}

	.follow-card-wrapper {
		color: #555;
		// max-height: 80vh;
		// overflow-y: auto;
		.follow-card {
			background-color: #f5f5f5;
			border-color: #e9e9e9;
			border-radius: 3px;
			margin: 10px 0;
			padding: 10px;
			.follow-card-content {
				white-space: break-spaces;
			}
			.cancel-plan {
				// 取消计划
				text-decoration: line-through;
				color: #999;
			}
		}
	}

	.button-add {
		display: inline-block;
		height: 24px;
		line-height: 24px;
		border: 1px solid transparent;
		min-width: 4vw;
		font-size: 16px;
		border-radius: 15px;
		text-align: center;
		background-color: #d7d7d7;
		color: #555;

		&:hover {
			background-color: #23b781;
			color: #fff;
			//border-color: #fff;
			cursor: pointer;
		}
	}

	.td-contractName {
		.el-button--text {
			max-width: 8.5vw;
		}
	}

	.button-warning {
		background-color: #ffffff;
		border-color: #f9a825;
		color: #f9a825;

		&:hover {
			background-color: #f9a825;
			border-color: #fcaf35;
			color: #fff;
		}
	}

	.button-error {
		background-color: #fff;
		border-color: #f44336;
		color: #f44336;

		&:hover {
			background-color: #f44336;
			border-color: #f84d41;
			color: #fff;
		}
	}
}
</style>
