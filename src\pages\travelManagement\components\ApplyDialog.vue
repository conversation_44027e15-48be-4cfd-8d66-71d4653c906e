<template>
	<div class="ApplyDialog">
		<el-dialog width="888px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">【{{ applyForm.applyNo }}】出差申请</span>
			<el-form :model="applyForm" label-width="88px" label-position="left" @submit.native.prevent>
				<div class="flex-justify-between">
					<el-form-item class="W70" label="客户：" prop="tripClientName">
						<Tooltips :cont-str="applyForm.tripClientName" :cont-width="100" />
					</el-form-item>
					<el-form-item class="W30" label="承担方：" prop="expenseParty">
						<Tooltips :cont-str="expensePartyMap[applyForm.expenseParty]" :cont-width="100" />
					</el-form-item>
				</div>
				<el-form-item label="目的地：" prop="tripDestination">
					<Tooltips :cont-str="applyForm.tripDestination" :cont-width="100" />
				</el-form-item>
				<el-form-item label="事由：" prop="tripReason">
					<Tooltips :cont-str="applyForm.tripReason" :cont-width="100" />
				</el-form-item>
				<div class="flex-justify-between">
					<el-form-item class="W30" label="差旅类别：" prop="tripType">
						<span>{{ tripTypeMap[applyForm.tripType] }}</span>
					</el-form-item>
					<el-form-item class="W30" label="开始日期" prop="tripBeginDate">
						<Tooltips :cont-str="dateFormat(applyForm.tripBeginDate, 'line')" :cont-width="100" />
					</el-form-item>
					<el-form-item class="W30" label="出差天数：" prop="tripDays">
						<Tooltips :cont-str="applyForm.tripDays" :cont-width="100" />
					</el-form-item>
				</div>
				<el-form-item label="出差人：" prop="tripUsers">
					<Tooltips
						v-if="applyForm.tripUsers"
						:cont-str="Object.values(applyForm?.tripUsers)?.join(',') || ''"
						:cont-width="100"
					/>
				</el-form-item>
				<div class="flex-justify-between">
					<el-form-item class="W30" label="申请人：" prop="applyUName">
						<span>{{ applyForm.applyUName }}</span>
					</el-form-item>
					<el-form-item class="W30" label="申请时间：" prop="applyDate">
						<span>{{ dateFormat(applyForm.applyDate, 'lineM') }}</span>
					</el-form-item>
				</div>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">关 闭</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { dateFormat } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { tripTypeMap, expensePartyMap } from '@/assets/js/contractSource'; // 差旅类别 费用承担方

export default {
	name: 'ApplyDialog',
	components: {},
	data() {
		return {
			tripTypeMap, // 差旅类别
			expensePartyMap, // 费用承担方
			dialogEdit: false,
			applyForm: {},
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['userInfos']) },
	// 监控data中的数据变化
	watch: {},

	// 方法集合
	methods: {
		openDialog(row) {
			this.applyForm = row;
			this.dialogEdit = true;
		},
		closeDialog() {
			this.dialogEdit = false;
		},
		dateFormat,
	},
};
</script>

<style lang="scss" scoped>
.ApplyDialog {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
}
</style>
