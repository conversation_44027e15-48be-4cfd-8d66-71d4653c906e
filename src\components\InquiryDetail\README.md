# InquiryDetail 组件

询盘详情组件，用于展示和管理客户询盘的详细信息。该组件提供了完整的询盘管理功能，包括基本信息、咨询信息、跟单信息等模块的展示与编辑。

## 功能特点

- 支持查看和编辑询盘的完整信息
- 提供公海询盘领取功能
- 支持复购询盘生成
- 包含锚点导航，快速定位到不同信息模块
- 支持全部展开/收起功能
- 提供询盘列表翻页功能
- 使用事件总线进行组件间通信
- 响应式设计，可根据需要显示或隐藏

## 组件结构

InquiryDetail 组件由以下子组件组成：

1. **BasicInfo** - 基本信息组件
   - 显示和编辑客户基本资料
   - 包含公司名称、联系人、联系方式等信息

2. **ConsultInfo** - 咨询信息组件
   - 包含 ConsultInfo.vue - 咨询情况记录
   - 包含 IndustryCaseList.vue - 行业案例列表

3. **FollowInfo** - 跟单信息组件
   - 包含 FollowInfo.vue - 跟单信息管理
   - 包含 FollowRecord.vue - 跟单记录列表

4. **FeedbackInfo** - 业务反馈组件
   - 管理商机质量、预计成交信息
   - 处理阶段变更和合同信息

5. **eventBus.js** - 事件总线
   - 用于组件间的数据通信
   - 避免复杂的父子组件传值

## 属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| showCom | Boolean | false | 否 | 控制组件的显示与隐藏 |
| inquiryOptions | Array | [] | 否 | 询盘列表选项 |
| inquiryDetail | Object | {} | 否 | 询盘详情数据 |
| isPublicInquiry | Boolean | false | 否 | 是否为公海询盘 |
| titleName | String | '详情' | 否 | 标题名称 |

## 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:showCom | 更新显示状态 | (showState: Boolean) |
| saveSuccess | 保存成功事件 | (data: Object) |
| refreshList | 刷新列表事件 | - |
| closeDetail | 关闭详情事件 | - |

## 注意事项

1. 使用前需确保已正确配置事件总线 (eventBus.js)
2. 组件内部使用了锚点导航，确保各子组件有正确的 ID
3. 公海询盘模式下部分编辑功能会被禁用
4. 复购功能仅在已备案询盘且非公海询盘时可用
5. 组件内部处理了数据刷新和翻页逻辑，使用时只需关注事件回调

## 相关组件

- BaseLayout - 基础布局组件
- Tooltips - 提示组件
- CaseFiles - 案例文件组件

## 实现原理

组件使用 Vue 2 的组件化开发模式，通过事件总线实现组件间通信，避免了复杂的父子组件传值。使用 CSS 动画实现平滑的显示/隐藏效果，采用锚点导航提高用户体验。 