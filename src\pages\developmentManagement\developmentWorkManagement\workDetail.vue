<template>
	<div id="workDetail">
		<!-- 任务明细弹窗 -->
		<TaskDetail ref="TaskDetail" />
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<BaseLayout>
			<template #header>
				<span class="search-label">要求转测日期</span>
				<DateSelect
					:dateSelectObj="dateSelectObj"
					@change="
						dateSelectObj = $event;
						queryTableData(1);
					"
				/>
				<span class="search-label">测试通过日期</span>
				<DateSelect
					:dateSelectObj="dateSelectObj2"
					@change="
						dateSelectObj2 = $event;
						queryTableData(1);
					"
				/>

				<SearchHistoryInput
					name="projectName_taskName_developName"
					placeholder="项目/任务/开发人员"
					v-model.trim="searchForm.queryParam"
					@input="queryTableData(1)"
				/>

				<el-checkbox-group v-model="searchForm.statusList" size="mini" @change="queryTableData(1)">
					<el-checkbox v-for="item in statusList" :label="item.id" :key="item.id">
						<span class="flex-align-center">
							<span :style="{ color: colorMap[item.id], 'font-size': '19px' }">●</span>
							<span :style="{ 'font-size': '14px' }">{{ item.status }}</span>
						</span>
					</el-checkbox>
				</el-checkbox-group>

				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-toolbar">
					<ExportBtn @trigger="openExport" />
				</div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<Tooltips
								v-if="
									item.colNo == 'startTime' ||
									item.colNo == 'endTime' ||
									item.colNo == 'testPassTime' ||
									item.colNo == 'actualEndTime'
								"
								:cont-str="dateFormat(scope.row[item.colNo], 'YMD')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="item.colNo == 'status'"
								:cont-str="statusMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="item.colNo == 'turnQuality' || item.colNo == 'taskOtd'"
								class="red bolder"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '%' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="item.colNo == 'taskClassify'"
								:cont-str="taskClassifyMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="item.colNo == 'taskLevel'"
								:cont-str="taskLevelMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<Tooltips
								v-else-if="item.colNo == 'taskNo'"
								:style="{ color: colorMap[scope.row.status], 'font-weight': 'bold' }"
								class="hover-green"
								@click.native="openDetail(scope.row)"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<Tooltips
								v-else-if="item.colNo == 'mentionUid' || item.colNo == 'productUid' || item.colNo == 'projectTurnCheck'"
								:cont-str="scope.row[item.colNo].userName ? scope.row[item.colNo].userName + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { debounce, dateFormat, sortTableData } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportBtn from '@/components/ExportTable/ExportBtn';
import ExportTable from '@/components/ExportTable';
import DateSelect from '@/components/DateSelect/DateSelect'; //日期选择
import TaskDetail from './components/taskDetailCom'; //任务明细弹窗

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		DateSelect,
		TaskDetail,
		ExportBtn,
	},
	props: {},
	name: 'workDetail',
	data() {
		return {
			queryParam: '',

			activeTab: 'workDetail',
			projectList: [],

			projectName: '',

			statusList: [
				{ id: 1, status: '计划中' },
				{ id: 0, status: '开发延误' },
				{ id: 6, status: '测试延误' },
				{ id: 3, status: '已转测' },
				{ id: 5, status: '转测不通过 ' },
				{ id: 4, status: '转测通过' },
			],
			colorMap: {
				0: '#ec808d', //红 开发延误
				1: '#bababa', //灰 计划中
				2: '#ab47bc', //紫 开发中
				3: '#2196f3', //蓝 已转测
				4: '#28d094', //绿 完成
				5: '#facd91', //橙 转测不通过
				6: '#ec808d', //红 测试延误
			},
			// 列表的状态
			colorMap2: {
				0: '#ffc9c9', //红 延误
				1: '#ebeceb', //灰 计划中
				2: '#ab47bc', //紫 开发中
				3: '#d0ebff', //蓝 已转测
				4: '#c3fae8', //绿 完成
				5: '#facd91', //橙 转测不通过
				6: '#ffc9c9', //红 测试延误
			},
			statusMap: {
				0: '延误',
				1: '计划中',
				2: '开发中',
				3: '已转测',
				4: '完成',
				5: '转测不通过',
				6: '测试延误',
			},

			taskClassifyMap: {
				0: '需求',
				1: '优化',
				2: 'Bug',
				3: '杂项',
			},
			taskLevelMap: {
				0: '高',
				1: '中',
				2: '低',
			},
			//日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			//日期相关
			dateSelectObj2: {
				endTime: '',
				startTime: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 200, 500, 1000],
			},
			tableColumn: [
				{ colName: '任务编号', colNo: 'taskNo', align: 'left', width: '' },
				{ colName: '项目名称', colNo: 'projectName', align: 'left', width: '' },
				{ colName: '任务', colNo: 'taskName', align: 'left', width: '' },
				{ colName: '分类', colNo: 'taskClassify', align: 'center', width: '70' },
				{ colName: '优先级', colNo: 'taskLevel', align: 'center', width: '80' },
				{ colName: '产出工时', colNo: 'productTime', align: 'right', width: '65' },
				{ colName: '难度系数', colNo: 'difficulty', align: 'right', width: '65' },
				{ colName: '开发人员', colNo: 'productUid', align: 'left', width: '65' },
				{ colName: '要求转测日期', colNo: 'endTime', align: 'center', width: '70' },
				{ colName: '实际转测日期', colNo: 'actualEndTime', align: 'center', width: '70' },
				{ colName: '任务状态', colNo: 'status', align: 'left', width: '65' },
				{ colName: '准交率', colNo: 'taskOtd', align: 'right', width: '80' },
				{ colName: '转测次数', colNo: 'projectTurnCount', align: 'right', width: '65' },
				{ colName: '转测质量', colNo: 'turnQuality', align: 'right', width: '65' },
				{ colName: '研发绩效工时', colNo: 'performance', align: 'right', width: '70' },
				{ colName: '测试产出工时', colNo: 'testProductTime', align: 'right', width: '70' },
				{ colName: '测试通过日期', colNo: 'testPassTime', align: 'center', width: '70' },
				{ colName: '测试人', colNo: 'projectTurnCheck', align: 'left', width: '' },
				{ colName: '提出人', colNo: 'mentionUid', align: 'left', width: '' },
				{ colName: '来源客户', colNo: 'customer', align: 'left', width: '' },
			],
			searchForm: {
				queryParam: '',
				statusList: [0, 1, 2, 3, 4, 5, 6],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		try {
			this.searchForm = JSON.parse(window?.localStorage.getItem(this.activeTab + '_searchForm')) || this.searchForm;
			this.dateSelectObj = JSON.parse(window?.localStorage.getItem(this.activeTab + '_dateSelectObj')) || this.dateSelectObj;
			this.dateSelectObj2 = JSON.parse(window?.localStorage.getItem(this.activeTab + '_dateSelectObj2')) || this.dateSelectObj2;
		} catch (error) {
			console.error(error);
		}
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 获取数据
		queryTableData: debounce(function (type) {
			window?.localStorage.setItem(this.activeTab + '_dateSelectObj1', JSON.stringify(this.dateSelectObj1));
			window?.localStorage.setItem(this.activeTab + '_dateSelectObj2', JSON.stringify(this.dateSelectObj2));
			window?.localStorage.setItem(this.activeTab + '_searchForm', JSON.stringify(this.searchForm));

			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectTaskDetail'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				testPassEndTime: this.dateSelectObj2.endTime,
				testPassStartTime: this.dateSelectObj2.startTime,
				...this.dateSelectObj,
				...this.searchForm,
			});

			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 打开任务明细
		openDetail(row) {
			this.$refs.TaskDetail.openDetail(row);
		},
		//日期format
		dateFormat: dateFormat,
		//自定义排序(新增/覆盖比较逻辑)
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop == 'productUid' || prop == 'projectTurnCheck' || prop == 'mentionUid' || prop == 'technicalManager') {
						if (!a?.userName) return -1;
						if (!b?.userName) return 1;
						return a?.userName.localeCompare(b?.userName, 'zh-CN');
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					pageNum: this.tablePageForm.currentPage,
					pageSize: this.tablePageForm.pageSize,
					testPassEndTime: this.dateSelectObj2.endTime,
					testPassStartTime: this.dateSelectObj2.startTime,
					...this.dateSelectObj,
					...this.searchForm,
				}), //接口参数
				API: 'exportTaskDetail', //导出接口
				downloadData: '任务明细导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>
