<template>
	<div id="bugStatistics">
		<taskList ref="taskList" />
		<BaseLayout>
			<template #header>
				<span class="search-label">年度</span>
				<el-date-picker
					size="small"
					v-model="selectTime"
					:default-value="selectTime"
					type="year"
					value-format="timestamp"
					format="yyyy 年"
					placeholder="请选择年份"
					:clearable="false"
					@change="changeDateSelect"
				>
				</el-date-picker>

				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-toolbar"> </div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					@sort-change="sortChange"
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column label="序号" width="50" align="center" type="index"> </u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						resizable
						sortable="custom"
					>
						<template slot-scope="scope">
							<div v-if="item.colNo != 'userName' && item.colNo != 'sum' && scope.row[item.colNo]">
								<Tooltips
									class="hover-green"
									@click.native="openList(item.colNo, scope.row)"
									:cont-str="scope.row[item.colNo] ? returnDoubleFloat(scope.row[item.colNo]) + '' : ''"
									:cont-width="(scope.column.width || scope.column.realWidth) - 18"
								>
								</Tooltips>
							</div>
							<!-- 合计 -->
							<Tooltips
								v-else-if="item.colNo == 'sum' && scope.row[item.colNo]"
								class="hover-green"
								@click.native="openList(item.colNo, scope.row)"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import taskList from './components/bugTaskList.vue';

export default {
	props: {},
	// import引入的组件需要注入到对象中才能使用
	components: { taskList },
	name: 'bugStatistics',
	data() {
		return {
			selectTime: new Date(),
			//日期相关
			startTime: '',
			endTime: '',
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},

			tableColumn: [
				{ colName: '测试人员', colNo: 'userName', align: 'left', width: 200 },
				{ colName: '一月', colNo: '01', align: 'right' },
				{ colName: '二月', colNo: '02', align: 'right' },
				{ colName: '三月', colNo: '03', align: 'right' },
				{ colName: '四月', colNo: '04', align: 'right' },
				{ colName: '五月', colNo: '05', align: 'right' },
				{ colName: '六月', colNo: '06', align: 'right' },
				{ colName: '七月', colNo: '07', align: 'right' },
				{ colName: '八月', colNo: '08', align: 'right' },
				{ colName: '九月', colNo: '09', align: 'right' },
				{ colName: '十月', colNo: '10', align: 'right' },
				{ colName: '十一月', colNo: '11', align: 'right' },
				{ colName: '十二月', colNo: '12', align: 'right' },
				{ colName: '合计', colNo: 'sum', align: 'right' },
			], //当前显示列
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		const nowYear = new Date().getFullYear();
		this.changeDateSelect(new Date(nowYear, '0', '1').getTime());
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// bug清单
		openList(monthIndex, row) {
			// 获取当前月份第一天时间戳
			const now = new Date(this.startTime) || new Date(this.selectTime); // 获取当前年份
			let startDate = '';
			let endDate = '';
			if (monthIndex !== 'sum' && monthIndex < 13) {
				// 非合计
				const firstDayOfMonth = new Date(now.getFullYear(), monthIndex - 1, 1); // 当月的第一天
				startDate = firstDayOfMonth.getTime(); // 获取时间戳
				endDate = _.getNowMonthEndDay(startDate); // 当前月份最后一天时间戳
			} else {
				// 合计
				const firstDayOfYear = new Date(now.getFullYear(), 0, 1); // 当年的第一天
				startDate = firstDayOfYear.getTime(); // 获取时间戳
				const lastDayOfYear = new Date(now.getFullYear(), 11, 31); // 当年的最后一天
				endDate = lastDayOfYear.getTime() + 86399999; // 获取时间戳
			}
			const searchForm = {
				auid: row?.auid || '',
				userName: row?.userName || '',
				startDate,
				endDate,
			};
			this.$refs.taskList.openTaskList(searchForm);
		},
		changeDateSelect(date) {
			const year = new Date(date).getFullYear();
			this.startTime = new Date(year, '0', '1').getTime();
			this.endTime = new Date(year + 1, '0', '1').getTime() - 1;
			// console.log(date);
			this.queryTableData();
		},
		// 返回双精度浮点数
		returnDoubleFloat(value) {
			if (!value) return;
			var value = Math.round(parseFloat(value) * 100) / 100;
			const point = value.toString().split('.');
			// if (point.length == 1) {
			//   value = value.toString() + ".00";
			//   return value;
			// }
			if (point.length > 1) {
				if (point[1].length < 2) {
					value = value.toString() + '0';
				}
				return value;
			}
			return value;
		},
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = []; // 合计
			columns.forEach((column, columnIndex) => {
				if (columnIndex <= 1) {
					means.push(columnIndex == 0 ? '合计' : '');
				} else {
					const values = data.map(item => Number(item[column.property]));
					// 合计
					if (!values.every(value => isNaN(value))) {
						means[columnIndex] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return this.accAdd(prev, curr);
							} else {
								return prev;
							}
						}, 0);
						if (means[columnIndex] > 0) {
							means[columnIndex] = 
								<span
									on-click={e => {
										e.stopPropagation();
										this.openList(columnIndex - 1, null);
									}}
								>
									<span class="hover-green ellipsis">{means[columnIndex] + '个'}</span>
								</span>
							;
						} else {
							means[columnIndex] = '';
						}
					} else {
						means[columnIndex] = '';
					}
				}
			});
			// console.log(columns);
			// 返回一个二维数组的表尾合计(不要平均值，你就不要在数组中添加)
			return [means];
		},

		// 数值加法精准度设置
		accAdd(arg1, arg2) {
			let r1, r2, m;
			try {
				r1 = arg1.toString().split('.')[1].length;
			} catch (e) {
				r1 = 0;
			}
			try {
				r2 = arg2.toString().split('.')[1].length;
			} catch (e) {
				r2 = 0;
			}
			m = Math.pow(10, Math.max(r1, r2));
			m *= 10;
			if (this.requestType == 1) {
				return (parseInt(arg1 * m + arg2 * m) / m).toFixed(2);
			} else {
				return parseInt(arg1 * m + arg2 * m) / m;
			}
		},

		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		// 获取bug数数据
		queryTableData: _.debounce(function (type) {
			const str = JSON.stringify({
				endDate: this.endTime,
				startDate: this.startTime,
			});
			this.$axios
				.selectBugCount(str)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							const tempData = item.selectMonthResearchDepartmentVOS;
							if (tempData && tempData.length > 0) {
								tempData.forEach((obj, index) => {
									if (obj.factor && obj.month) {
										item[obj.month] = obj.factor;
									}
								});
							}
						});
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.tableData = res.data.data.sort((a, b) => b.sum - a.sum);
						this.tablePageForm.total = res.data.totalItems;

						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('selectBugCount |' + error);
				});
		}),
	},
};
</script>
