/**
 * layout 相关
 */
const state = {
	routerTags: [], //路由标签
	cacheTags: [], //需要缓存的路由
	isCollapse: false, //菜单折叠判断
	showChatBot: false, //是否显示聊天机器人

};

const actions = {};

const getters = {
	routerTags: state => state.routerTags, //路由标签
	cacheTags: state => state.cacheTags, //需要缓存的路由
	isCollapse: state => state.isCollapse, //菜单折叠判断
	showChatBot: state => state.showChatBot, //是否显示聊天机器人
};

const mutations = {
	setRouterTags(state, res) {	state.routerTags = res;},
	setCacheTags(state, res) {	state.cacheTags = res;},
	setIsCollapse(state, res) { state.isCollapse = res; },
	setShowChatBot(state, res) { state.showChatBot = res;},
};

export default {
	state,
	actions,
	getters,
	mutations,
};
