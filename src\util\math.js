import Big from 'big.js';
import { Message } from 'element-ui';

/**
 * Big.js 操作
 * @param {Number|String} a - 第一个数值。
 * @param {Number|String} b - 第二个数值。
 * @param {Number} p - 精度(如果没有填写，则取两个数值中最大的小数位数)。
 * @param {Function} operation - Big.js 操作函数。
 * @returns {Number} - 操作结果。
 */
function bigOperation(a, b, p, operation) {
	// 将输入转换为 Big 对象  ( 处理 null、undefined 和非数值字符串)
	const bigA = !a || isNaN(a) ? Big(0) : Big(a);
	const bigB = !b || isNaN(b) ? Big(0) : Big(b);

	const result = operation(bigA, bigB);
	const precisionA = a?.toString()?.split('.')[1]?.length || 0;
	const precisionB = b?.toString()?.split('.')[1]?.length || 0;
	const precision = p === undefined ? Math.max(precisionA, precisionB) : p;

	return result.toFixed(precision); // 转为数值以去除多余的零
}

/**
 * 数值加法
 * @param {Number|String} a - 第一个数值。
 * @param {Number|String} b - 第二个数值。
 * @param {Number} p - 精度。
 * @returns {Number} - 加法结果。
 */
export function bigAdd(a, b, p) {
	return bigOperation(a, b, p, (x, y) => x.plus(y));
}

/**
 * 数值减法
 * @param {Number|String} a - 第一个数值。
 * @param {Number|String} b - 第二个数值。
 * @param {Number} p - 精度。
 * @returns {Number} - 减法结果。
 */
export function bigSub(a, b, p) {
	return bigOperation(a, b, p, (x, y) => x.minus(y));
}

/**
 * 数值乘法
 * @param {Number|String} a - 第一个数值。
 * @param {Number|String} b - 第二个数值。
 * @param {Number} p - 精度。
 * @returns {Number} - 乘法结果。
 */
export function bigMul(a, b, p) {
	if (a == 0 || b == 0) return 0; // 乘以0的判断
	return bigOperation(a, b, p, (x, y) => x.times(y));
}

/**
 * 数值除法
 * @param {Number|String} a - 被除数。
 * @param {Number|String} b - 除数。
 * @param {Number} p - 精度。
 * @returns {Number} - 除法结果。
 */
export function bigDiv(a, b, p) {
	if (a == 0 || b == 0) return 0; // 除以0的判断
	return bigOperation(a, b, p, (x, y) => x.div(y), true);
}

/**
 * 数值取余
 * @param {Number|String} a - 被取余数的数值。
 * @param {Number|String} b - 取余的数值。
 * @param {Number} p - 精度。
 * @returns {Number} - 余数结果。
 */
export function bigMod(a, b, p) {
	if (a == 0 || b == 0) return 0; // 除以0的判断
	return bigOperation(a, b, p, (x, y) => x.mod(y), true);
}

/**
 * 千分制数值 1000 => 1,000
 * @param {Number|String} str - 需要转换的数值。
 * @returns {Number} - 转换后的数值。
 */
export function thousandth(str) {
	const num = parseFloat(str);
	return isNaN(num) ? '' : num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','); // 合并返回逻辑
}

/**
 * 将金额转成万单位 10000 => 1.00
 * @param Number
 * @returns Number
 */

export function convertToMillion(value, d = 2) {
	if (!value || value <= 0) return '';
	// Convert the value to million units
	const valueInMillion = value / 10000;

	// Round to two decimal places
	const resultVal = (Math.round(valueInMillion * 100) / 100).toFixed(d);
	const decimalPart = resultVal.split('.')[1];
	const decimalsToAdd = 2 - decimalPart.length;
	const finalValue = decimalsToAdd > 0 ? resultVal + '0'.repeat(decimalsToAdd) : resultVal;
	return finalValue;
}

/**
 * 输入正数时保留后几位小数
 * @param {String|Number} num - 输入的数值
 * @param {Number} d - 小数点后保留的位数
 * @param {Object} options - 配置选项
 * @param {Number} [options.min=0] - 最小值
 * @param {Number} [options.max=9999] - 最大值
 * @returns {String} - 处理后的数值
 */
export function getInputNum(num, d = 2, options = {}) {
	const { min = 0, max = 999999 } = options;

	if (!num) return min;
	const value = String(num);

	// 检查输入是否在最小值和最大值之间
	if (Number(value) < min || Number(value) > max) {
		Message.warning(`注意：输入值必须在${min}和${max}之间!`);
		return value.slice(0, -1);
	}

	// 动态创建正则表达式，根据传入的d参数限制小数位数
	const regex = d === 0 ? new RegExp(`^[0-9]*$`) : new RegExp(`^[0-9]*\\.?[0-9]{0,${d}}$`);

	// 检查输入是否符合正则表达式
	if (!regex.test(value)) {
		if (d == 0) {
			Message.warning(`注意：只能输入整数（不允许输入小数）!`);
		} else {
			Message.warning(`注意：只能输入整数/小数（小数点保留后${d}位）!`);
		}
		return value.slice(0, -1);
	}

	return value;
}

/* 以下是老的代码（全局替换后会删除） */
/**
 * 数值加法精准度设置
 * @param fuction ,Number
 * @returns
 */

export function accAdd(arg1, arg2, d) {
	let r1, r2, m;
	try {
		r1 = arg1.toString().split('.')[1].length;
	} catch (e) {
		r1 = 0;
	}
	try {
		r2 = arg2.toString().split('.')[1].length;
	} catch (e) {
		r2 = 0;
	}
	m = Math.pow(10, Math.max(r1, r2));
	m *= 10;
	return (parseInt(arg1 * m + arg2 * m) / m).toFixed(d || Math.max(r1, r2));
}

/**
 * 数值除法精准度设置
 * @param fuction ,Number
 * @returns
 */

export function accDiv(arg1, arg2, d = 2) {
	const r1 = typeof arg1 === 'number' ? arg1.toString() : arg1;
	const r2 = typeof arg2 === 'number' ? arg2.toString() : arg2;
	const m = (r2.split('.')[1] ? r2.split('.')[1].length : 0) - (r1.split('.')[1] ? r1.split('.')[1].length : 0);
	const resultVal = (Number(r1.replace('.', '')) / Number(r2.replace('.', ''))) * Math.pow(10, m);
	return Number(resultVal.toFixed(d));
}

/**
 * 数值乘法精准度设置
 * @param fuction ,Number
 * @returns
 */

export const accMul = function (arg1, arg2, d = 2) {
	const r1 = typeof arg1 === 'number' ? arg1.toString() : arg1;
	const r2 = typeof arg2 === 'number' ? arg2.toString() : arg2;
	const m = (r1.split('.')[1] ? r1.split('.')[1].length : 0) + (r2.split('.')[1] ? r2.split('.')[1].length : 0);
	const resultVal = (Number(r1.replace('.', '')) * Number(r2.replace('.', ''))) / Math.pow(10, m);
	return Number(resultVal.toFixed(d));
};
