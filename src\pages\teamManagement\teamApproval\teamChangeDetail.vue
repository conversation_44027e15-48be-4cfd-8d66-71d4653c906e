<template>
	<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}团队变更申请</span>
				<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content">
				<div class="flex-align-center">
					<p class="detail-content-title">基本信息 </p>
					<span class="ml20 color-666">
						<span class="label-required">团队编号</span>
						<el-select
							v-if="titleName == '添加'"
							class="w-200"
							size="small"
							v-model="detailForm_OLD.tid"
							placeholder="请选择团队"
							@change="queryDetail_OLD"
							clearable
							filterable
						>
							<el-option
								v-for="item in teamList"
								:key="item.tid"
								:label="jointString(' | ', item.teamCode, item.teamName)"
								:value="item.tid"
							>
							</el-option>
						</el-select>
						<span v-else>{{ detailForm_OLD.teamCode }}</span>
					</span>
				</div>
				<!-- ==============变更前部分================ -->
				<div class="color-666 pb10">变更前</div>
				<BaseTableForm
					class="input-border-none"
					:detailForm="detailForm_OLD"
					:formList="formList"
					@update="detailForm_OLD = $event"
					:disabled="true"
				>
					<!-- <template #th-xxx></template> -->
					<template #td-teamCode="{ item, formData }">
						{{ formData[item.prop] || '请选择团队' }}
					</template>

					<template #td-suid="{ item, formData }">
						<el-select
							:disabled="true"
							v-model="formData[item.prop]"
							placeholder="请选择业务顾问"
							@change="selectSaleMan"
							clearable
							filterable
						>
							<el-option v-for="oItem in salesmanList" :key="oItem.auid" :label="oItem.userName" :value="oItem.auid"> </el-option>
						</el-select>
					</template>

					<template #td-cuid="{ item, formData }">
						<el-select
							:disabled="true"
							v-model="formData[item.prop]"
							placeholder="请选择实施顾问"
							@change="selectConsultantMan"
							clearable
							filterable
						>
							<el-option v-for="oItem in implementList" :key="oItem.auid" :label="oItem.userName" :value="oItem.auid">
							</el-option>
						</el-select>
					</template>

					<template #td-version="{ item, formData }">
						<el-select :disabled="true" placeholder="使用版本" v-model="formData[item.prop]">
							<el-option label="OEE" :value="'2'"></el-option>
							<el-option label="标准版" :value="'1'"></el-option>
							<el-option label="微信版" :value="'4'"></el-option>
						</el-select>
					</template>

					<template #td-status="{ item, formData }">
						<el-select :disabled="true" placeholder="使用版本" v-model="formData[item.prop]">
							<el-option label="试用" :value="2"></el-option>
							<el-option label="正式运行" :value="1"></el-option>
						</el-select>
					</template>
				</BaseTableForm>

				<!-- ==============变更部分================ -->
				<div v-show="this.detailForm_OLD.tid">
					<div class="color-666 pt10 pb10">变更为</div>
					<BaseTableForm
						class="input-border-none"
						:detailForm="detailForm_NEW"
						:formList="formList"
						@update="detailForm_NEW = $event"
						:disabled="!isEdit"
					>
						<!-- <template #th-xxx></template> -->
						<template #td-teamCode="{ item, formData }">
							{{ formData[item.prop] || '自动生成' }}
						</template>

						<template #td-suid="{ item, formData }">
							<el-select
								:disabled="!isEdit"
								v-model="formData[item.prop]"
								placeholder="请选择业务顾问"
								@change="selectSaleMan"
								clearable
								filterable
							>
								<el-option disabled v-show="!detailForm_OLD.twid || salesmanList.length == 0" value=" ">
									<span class="orange fs-14">请选择对应的分销/代理后再操作！</span>
								</el-option>
								<el-option
									v-show="detailForm_OLD.twid"
									v-for="oItem in salesmanList"
									:key="oItem.auid"
									:label="oItem.userName"
									:value="oItem.auid"
								>
								</el-option>
							</el-select>
						</template>

						<template #td-cuid="{ item, formData }">
							<el-select
								:disabled="!isEdit"
								v-model="formData[item.prop]"
								placeholder="请选择实施顾问"
								@change="selectConsultantMan"
								clearable
								filterable
							>
								<el-option disabled v-show="!detailForm_OLD.twid || implementList.length == 0" value=" ">
									<span class="orange fs-14">请选择对应的分销/代理后再操作！</span>
								</el-option>
								<el-option
									v-show="detailForm_OLD.twid"
									v-for="oItem in implementList"
									:key="oItem.auid"
									:label="oItem.userName"
									:value="oItem.auid"
								>
								</el-option>
							</el-select>
						</template>

						<template #td-version="{ item, formData }">
							<el-select :disabled="!isEdit" placeholder="使用版本" v-model="formData[item.prop]">
								<el-option label="OEE" :value="'2'"></el-option>
								<el-option label="标准版" :value="'1'"></el-option>
								<el-option label="微信版" :value="'4'"></el-option>
							</el-select>
						</template>

						<template #td-status="{ item, formData }">
							<el-select :disabled="!isEdit" placeholder="使用版本" v-model="formData[item.prop]">
								<el-option label="试用" :value="2"></el-option>
								<el-option label="正式运行" :value="1"></el-option>
							</el-select>
						</template>

						<template #td-simLimit="{ item, formData }">
							<el-input
								:disabled="!isEdit"
								v-model="formData[item.prop]"
								placeholder="工作中心"
								@change="
									formData[item.prop] =
										formData[item.prop].trim() === '' || formData[item.prop] === '0' ? 99999 : formData[item.prop]
								"
							></el-input>
						</template>
					</BaseTableForm>
					<div class="bottom-button">
						<el-button v-show="isEdit && titleName == '添加' && this.detailForm_OLD.tid" @click="saveAddDetail" type="primary">
							提交申请
						</el-button>
						<el-button v-show="isEdit && titleName == '修改'" @click="saveChangeDetail" type="primary"> 修改申请 </el-button>
						<!-- <el-button v-show="isApprove" @click="openApproval([detailForm])" type="primary"> 审 批 </el-button> -->
					</div>
				</div>

				<div v-show="logList.length" class="flex-align-center">
					<p class="detail-content-title">审批日志 </p>
				</div>
				<div v-show="logList.length" class="detail-log">
					<div class="detail-log-item" v-for="(item, index) in logList" :key="'oper' + index">
						<span class="mr8">{{ dateFormat(item.time, 'lineM') }} </span>
						<span> {{ item.uname }}</span>
						<pre>{{ item.content }}</pre>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';
import BaseTableForm from '@/components/BaseTableForm';
export default {
	name: 'teamCreationDetail',
	directives: {},
	components: { BaseTableForm },
	props: {
		channelName: {
			type: Array,
			default: () => {
				return [];
			},
		},
		twidList: {
			type: Array,
			default: () => {
				return [];
			},
		},
		// 是否审批页面
		isApprove: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '明细详情',
			logList: [],
			detailForm_OLD: {
				//明细详情
				annualFee: '',
				artificialEfficiency: '',
				auid: '',
				cuid: '',
				machineLabel: '',
				machineMaintenance: '',
				pageNum: '',
				pageSize: '',
				phoneNo: '',
				plcid: '',
				productLabel: '',
				productionAdvice: '',
				region: '',
				simLimit: '',
				status: '',
				suid: '',
				teamFullname: '',
				teamImage: '',
				teamIntroduction: '',
				teamName: '',
				teamPic: '',
				templateNo: '',
				tid: '',
				twid: '',
				userName: '',
				validFrom: '',
				validTo: '',
				version: '',
				workorderTemplateNo: '',
				workorderPrintNo: '', //排产打印模板
				useMaterial: 0, //投料追溯
				productionReminds: [],
			},
			detailForm_NEW: {
				tcid: '',
				//明细详情
				annualFee: '',
				artificialEfficiency: '',
				auid: '',
				cuid: '',
				machineLabel: '',
				machineMaintenance: '',
				pageNum: '',
				pageSize: '',
				phoneNo: '',
				plcid: '',
				productLabel: '',
				productionAdvice: '',
				region: '',
				simLimit: '',
				status: '',
				suid: '',
				teamFullname: '',
				teamImage: '',
				teamIntroduction: '',
				teamName: '',
				teamPic: '',
				templateNo: '',
				tid: '',
				twid: '',
				userName: '',
				validFrom: '',
				validTo: '',
				version: '',
				workorderTemplateNo: '',
				workorderPrintNo: '', //排产打印模板
				useMaterial: 0, //投料追溯
				productionReminds: [],
			},
			detailForm_NEWCopy: [],
			// formList: [
			// 	[
			// 		{ name: '团队编号', prop: 'teamCode', class: 'W10', type: 'text' },
			// 		{ name: '短名称', prop: 'teamName', class: 'label-required  W10' },
			// 		{ name: '全称', prop: 'teamFullname', class: 'label-required  W15' },
			// 		{ name: '团队管理员', prop: 'userName', class: 'label-required W10' },
			// 		{ name: '手机号码', prop: 'phoneNo', class: 'label-required W10' },
			// 		{ name: '业务顾问', prop: 'suid', class: 'label-required W10', type: 'select' },
			// 		{ name: '实施顾问', prop: 'cuid', class: 'label-required W10', type: 'select' },
			// 		{ name: '使用版本', prop: 'version', class: 'label-required  W10', type: 'select' },
			// 		{ name: '状态', prop: 'status', class: 'label-required  W10', type: 'select' },
			// 	],
			// 	[
			// 		{ name: '启用日期', prop: 'validFrom', class: ' label-required W10', type: 'date' },
			// 		{ name: '到期日期', prop: 'validTo', class: ' label-required W10', type: 'date' },
			// 		{ name: '年费(元)', prop: 'annualFee', class: ' label-required W10' },
			// 		{ name: '工作中心数', prop: 'simLimit', class: ' label-required W10' },
			// 		{ name: '合同编号', prop: 'contractNo', class: ' W10' },
			// 		{ name: '备注', prop: 'remark', class: '', colspan: 4 },
			// 	],
			// ],
			formRules: {
				teamName: [{ required: true, message: '请输入短名称', trigger: 'blur' }],
				teamFullname: [{ required: true, message: '请输入全称', trigger: 'blur' }],
				userName: [{ required: true, message: '请输入团队管理员', trigger: 'blur' }],
				phoneNo: [{ required: true, message: '请输入手机号码', trigger: 'blur' }],
				suid: [{ required: true, message: '请输入业务顾问', trigger: 'blur' }],
				cuid: [{ required: true, message: '请输入实施顾问', trigger: 'blur' }],
				version: [{ required: true, message: '请输入使用版本', trigger: 'blur' }],
				status: [{ required: true, message: '请输入状态', trigger: 'blur' }],
				validFrom: [{ required: true, message: '请输入启用日期', trigger: 'blur' }],
				validTo: [{ required: true, message: '请输入到期日期', trigger: 'blur' }],
				annualFee: [{ required: true, message: '请输入年费', trigger: 'blur' }],
			},
			//资源列表
			teamList: [], //分销/代理列表
			userList: [],
		};
	},
	created() {},
	computed: {
		isEdit() {
			return this.titleName == '查看' ? false : true;
		},
		formList() {
			const formList = [
				[
					{ name: '团队编号', prop: 'teamCode', class: 'W10', type: 'text' },
					{ name: '短名称', prop: 'teamName', class: 'label-required  W10' },
					{ name: '全称', prop: 'teamFullname', class: 'label-required  W15' },
					{ name: '团队管理员', prop: 'userName', class: 'label-required W10' },
					{ name: '手机号码', prop: 'phoneNo', class: 'label-required W10' },
					{ name: '业务顾问', prop: 'suid', class: 'label-required W10', type: 'select' },
					{ name: '实施顾问', prop: 'cuid', class: 'label-required W10', type: 'select' },
					{ name: '使用版本', prop: 'version', class: 'label-required  W10', type: 'select' },
					{ name: '状态', prop: 'status', class: 'label-required  W10', type: 'select' },
				],
				[
					{ name: '启用日期', prop: 'validFrom', class: ' label-required W10', type: 'date' },
					{ name: '到期日期', prop: 'validTo', class: ' label-required W10', type: 'date' },
					{ name: '年费(元)', prop: 'annualFee', class: ' label-required W10' },
					{ name: '工作中心数', prop: 'simLimit', class: ' label-required W10' },
					{ name: '合同编号', prop: 'contractNo', class: ' W10' },
					{ name: '备注', prop: 'remark', class: '', colspan: 4 },
				],
			];
			// console.log(formList);
			return formList;
		},
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.detailForm_OLD = _.resetValues(this.detailForm_OLD); //重置对象
				this.detailForm_NEW = _.resetValues(this.detailForm_NEW); //重置对象
				this.logList = [];
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 查询审批日志
		queryLogData: _.debounce(function (tid) {
			const API = 'selectTeamLogByTid'; //接口
			const DATA = JSON.stringify({ tid });
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.logList = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 变更前的明细
		queryDetail_OLD(tid) {
			this.detailForm_NEW = _.resetValues(this.detailForm_NEW);
			if (!tid) {
				return;
			}
			this.$axios
				.selectTeamByTid(JSON.stringify({ tid }))
				.then(res => {
					if (res.data.success) {
						this.detailForm_OLD = {
							...this.detailForm_OLD,
							..._.deepClone(res.data.data),
						};
						this.detailForm_NEW = {
							...this.detailForm_NEW,
							..._.deepClone(res.data.data),
						};
						this.querySalesmans(this.detailForm_OLD.twid);
						console.log(this.detailForm_OLD);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTeamByTid |' + error);
				});
		},
		queryDetail_NEW(tid) {
			this.$axios
				.selectTeamChanInfo(JSON.stringify({ tid }))
				.then(res => {
					if (res.data.success) {
						this.detailForm_NEW = {
							...this.detailForm_NEW,
							..._.deepClone(res.data.data),
						};
						this.detailForm_NEW.tcid = res.data.data.tcid || res.data.data.tid;
						this.detailForm_NEWCopy = _.deepClone(this.detailForm_NEW);
						// this.querySalesmans(this.detailForm_OLD.twid);
					} else {
						this.detailForm_NEW = {
							...this.detailForm_NEW,
							...this.detailForm_OLD,
						};
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTeamChanInfo |' + error);
				});
		},

		// 添加/保存信息
		saveAddDetail(isClose = true) {
			if (_.checkRequired(this.detailForm_NEW, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}
			// 去除字符串空格
			if (this.detailForm_NEW.phoneNo) {
				this.detailForm_NEW.phoneNo = this.detailForm_NEW.phoneNo.replace(/\s*/g, '');
			}
			const API = 'teamChan';
			this.detailForm_NEW.approvalStatus = 4;
			this.$axios[API](JSON.stringify({ ...this.detailForm_NEW }))
				.then(res => {
					if (res.data.success) {
						isClose && (this.showCom = false);
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},
		saveChangeDetail(isClose = true) {
			if (_.checkRequired(this.detailForm_NEW, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}
			// 去除字符串空格
			if (this.detailForm_NEW.phoneNo) {
				this.detailForm_NEW.phoneNo = this.detailForm_NEW.phoneNo.replace(/\s*/g, '');
			}
			const API = 'updateTeamChanInfo';
			this.detailForm_NEW.approvalStatus = 4;
			this.$axios[API](
				JSON.stringify({
					tcid: this.detailForm_NEW.tcid || this.detailForm_NEW.tid,
					status: 1, //审批记录审批状态 1审批中， 2 驳回  3 通过
					teamDTO: { ...this.detailForm_NEW },
				}),
			)
				.then(res => {
					if (res.data.success) {
						isClose && (this.showCom = false);
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},

		//显示弹窗
		showDetailCom(type, rowData) {
			this.queryTeamList();
			this.titleName = type;
			if (rowData) {
				this.detailForm_OLD = {
					...this.detailForm_OLD,
					...rowData,
				};
				this.queryDetail_NEW(rowData.tid);
				// this.detailForm_NEW = _.deepClone(this.detailForm_OLD);
				// this.detailForm_NEWCopy = _.deepClone(this.detailForm_NEW);
				this.queryLogData(this.detailForm_OLD.tid);
				this.querySalesmans(this.detailForm_OLD.twid);
			} else {
				this.detailForm_NEWCopy = _.deepClone(this.detailForm_NEW);
			}
			this.showCom = true;
		},

		//点击返回
		closeDetailCom() {
			const isSameData = JSON.stringify(this.detailForm_NEW) == JSON.stringify(this.detailForm_NEWCopy);
			console.log('isSameData', isSameData);
			if (!isSameData) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.showCom = false;
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.showCom = false;
			}
		},
		//日期format
		dateFormat: _.dateFormat,

		selectSaleMan(auid) {
			this.detailForm_OLD.suid = auid;
			if (auid) {
				this.salesmanList.forEach(item => {
					if (item.uid == auid) this.detailForm_OLD.saleMan = item.userName;
				});
			}
		},

		selectConsultantMan(auid) {
			this.detailForm_OLD.cuid = auid;
			if (auid) {
				this.implementList.forEach(item => {
					if (item.uid == auid) this.detailForm_OLD.ConsultantName = item.userName;
				});
			}
		},
		// 修改分销/代理 - 查询业务人员
		querySalesmans(twid) {
			if (!twid) {
				return;
			}
			this.$axios
				.selectSalesmanByTwid(JSON.stringify({ twid, counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwid |' + error);
				});
		},

		// 获取分销/代理列表
		async queryTeamList() {
			try {
				const res = await this.$axios.selectTeam(
					JSON.stringify({
						twidList: this.twidList,
						channelName: this.channelName,
						version: [1, 2],
						statusList: [0, 3, 6],
						pageNum: '',
						pageSize: '',
					}),
				);
				if (res.data.success) {
					this.teamList = res.data.data;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log('selectTeam |' + error);
			}
		},

		jointString: _.jointString,
	},
};
</script>
<style lang="scss" scoped></style>
