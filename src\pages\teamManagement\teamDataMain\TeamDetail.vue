<template>
	<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}团队信息</span>
				<div class="detail-head-btn">
					<el-button type="text" class="icon-third_save" @click="$refs.TeamDetailForm.saveDetail()"> 保存</el-button>
					<el-button type="text" class="el-icon-arrow-left" @click="closeCom">返回</el-button>
				</div>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content flex-column">
				<!-- 团队表单 -->
				<TeamDetailForm ref="TeamDetailForm" :noLimit="noLimit" @close="closeCom" />

				<!-- 分配资源 -->
				<div v-show="titleName == '修改'" class="flex-column">
					<div class="flex-align-center">
						<p class="detail-content-title">分配资源 </p>
						<el-radio-group class="ml30" v-model="treeType">
							<el-radio :label="0">标品MES</el-radio>
							<el-radio :label="1">标品WMS</el-radio>
						</el-radio-group>
					</div>

					<ResourcesTree ref="ResourcesTree" :treeType="treeType" :noLimit="noLimit" />
				</div>

				<!-- 操作日志 -->
				<!-- <div v-show="titleName == '修改'">
					<p class="detail-content-title">操作日志 </p>
					<div class="detail-log">
						<div class="detail-log-item" v-for="(item, index) in detailForm.workorderOperationLogList" :key="'oper' + index">
							<span class="mr8">{{ dateFormat(item.createTime, 'MDS') }} </span>
							<span> {{ item.updateTime }}</span>
							<pre>{{ item.operationInfo }}</pre>
						</div>
					</div>
				</div> -->
			</div>
		</div>
	</div>
</template>
<script>
import { deepClone, dateFormat, resetValues } from '@/util/tool';
import { mapGetters } from 'vuex';
import TeamDetailForm from './TeamDetailForm';
import ResourcesTree from './ResourcesTree.vue';
export default {
	name: 'TeamDetail',
	directives: {},
	components: {
		TeamDetailForm,
		ResourcesTree,
	},
	props: {
		// 编辑权限(团队主数据不限制，我的客户等其他页面做对应的限制)
		noLimit: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '明细详情',
			logList: [],
			treeType: 0,
		};
	},
	created() {},
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.logList = [];
				this.$refs?.ResourcesTree?.clearTree(); //清空树
				this.$refs?.TeamDetailForm?.clearData();
				this.$emit('refresh');
			}
		},
	},
	mounted() {},
	methods: {
		// 获取日志
		queryLogData(tid) {
			this.$axios
				.selectLog(JSON.stringify({ tid }))
				.then(res => {
					if (res.data.success) {
						this.logList = res.data.data;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectLog |' + error);
				});
		},

		//显示弹窗
		showDetailCom(rowData) {
			if (rowData) {
				this.$nextTick(() => {
					this.$refs?.TeamDetailForm?.queryDetail(rowData);
				});

				setTimeout(() => {
					// this.queryLogData();
					this.$refs?.ResourcesTree?.queryResource(rowData);
				}, 1000);

				this.titleName = '修改';
			} else {
				this.titleName = '新增';
				this.$refs?.TeamDetailForm?.getInitData();
			}
			this.showCom = true;
		},

		//点击返回
		closeCom() {
			const isSameData = this.$refs?.TeamDetailForm?.checkUpdate();
			console.log('isSameData', isSameData);
			if (!isSameData) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.showCom = false;
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.showCom = false;
			}
		},

		dateFormat, //日期format
	},
};
</script>
<style lang="scss" scoped></style>
