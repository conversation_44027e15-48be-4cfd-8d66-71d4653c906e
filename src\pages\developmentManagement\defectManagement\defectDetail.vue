1
<template>
	<div class="defectDetailCom" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}缺陷报告</span>
				<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content">
				<p class="detail-content-title">基本信息 </p>
				<table class="base-table input-border-none" cellpadding="5" cellspacing="0">
					<tbody v-for="(fItem, index) in formList" :key="index">
						<!-- 标题 -->
						<tr>
							<th v-for="tItem in fItem" :key="tItem.prop" :colspan="tItem.colspan" :class="tItem.class">{{ tItem.name }} </th>
						</tr>
						<!-- 输入框 -->
						<tr>
							<td v-for="tItem in fItem" :key="tItem.prop" :colspan="tItem.colspan">
								<!-- 只读文本 -->
								<Tooltips v-if="tItem.type == 'text'" :cont-str="detailForm[tItem.prop]" />
								<el-autocomplete
									v-else-if="tItem.type == 'auto'"
									v-model="detailForm[tItem.prop]"
									:fetch-suggestions="querySearch"
									:placeholder="tItem.name"
									@select="handleSelect"
								>
									<template slot-scope="{ item }">
										<Tooltips
											:cont-str="jointString(' | ', item.materialNo, item.materialName, item.materialSpec, item.unit)"
											:cont-width="1200"
										/>
									</template>
								</el-autocomplete>
								<el-date-picker
									class="W100"
									v-else-if="tItem.type == 'datetime'"
									v-model="detailForm[tItem.prop]"
									type="datetime"
									:placeholder="tItem.name"
									format="yyyy-MM-dd  HH:mm"
									value-format="timestamp"
									:default-value="new Date()"
								>
								</el-date-picker>

								<el-select
									v-else-if="tItem.type == 'select'"
									v-model="detailForm[tItem.prop]"
									:placeholder="tItem.name"
									clearable
								>
									<div v-if="tItem.prop == 'submitUid' || tItem.prop == 'dutyUid'">
										<el-option v-for="uItem in userList" :key="uItem.auid" :label="uItem.userName" :value="uItem.auid">
										</el-option>
									</div>

									<div v-else-if="tItem.prop == 'bugLevel'">
										<el-option label="微小" :value="0"></el-option>
										<el-option label="一般" :value="1"></el-option>
										<el-option label="严重" :value="2"></el-option>
										<el-option label="致命" :value="3"> </el-option>
									</div>
								</el-select>

								<!-- 默认输入框 -->
								<el-input v-else v-model="detailForm[tItem.prop]" :placeholder="tItem.name" clearable></el-input>
							</td>
						</tr>
					</tbody>
				</table>
				<div class="bottom-button">
					<!-- <el-button v-if="titleName == '编辑'" class="mr20" @click="delDetail">删 除</el-button> -->
					<el-button @click="saveDetail" type="primary">保 存</el-button>
				</div>

				<!-- 8D报告 -->
				<div v-show="detailForm.bugLevel > 1 && detailForm.brid">
					<div v-for="dItem in eightDArry" :key="dItem.id">
						<p class="detail-content-title">
							<span class="flex-align-center">
								<span>{{ dItem.title }}</span>
								<span class="fs-12 normal ml10 color-999"> {{ dItem.subTitle }}</span>
							</span>
						</p>

						<div v-if="dItem.id == 'd2'">
							<el-input
								v-model="detailForm[dItem.id].description"
								placeholder="请输入内容"
								type="textarea"
								:autosize="{ minRows: 2, maxRows: 4 }"
							></el-input>
						</div>
						<div v-else class="table-wrapper">
							<u-table
								class="table-main detail-table th-bg-gray"
								:data="dItem.id == 'd1' ? [detailForm[dItem.id]] : detailForm[dItem.id]"
							>
								<u-table-column
									v-for="item in dItem.tableColumn"
									:key="'colCurr' + item.colNo"
									:label="item.colName"
									:prop="item.colNo"
									:align="item.align"
									:width="item.width"
									resizable
									sortable
								>
									<template slot-scope="scope">
										<el-select
											class="W100"
											v-if="item.type == 'select-user'"
											v-model="scope.row[item.colNo]"
											size="mini"
											placeholder="请选择"
											clearable
											filterable
										>
											<el-option v-for="uItem in userList" :key="uItem.auid" :label="uItem.userName" :value="uItem.auid">
											</el-option>
										</el-select>

										<el-select
											class="W100"
											v-else-if="item.type == 'select-multiple-user'"
											v-model="scope.row[item.colNo]"
											size="mini"
											placeholder="请选择"
											clearable
											filterable
											multiple
										>
											<el-option v-for="uItem in userList" :key="uItem.auid" :label="uItem.userName" :value="uItem.auid">
											</el-option>
										</el-select>

										<el-date-picker
											class="W100"
											v-else-if="item.type == 'date'"
											v-model="scope.row[item.colNo]"
											type="date"
											size="mini"
											placeholder="请输入日期"
											format="yyyy-MM-dd"
											value-format="timestamp"
										>
										</el-date-picker>

										<!-- 确认人才可点击确认 -->
										<el-button
											v-else-if="item.type == 'check'"
											:disabled="scope.row.confirmer !== nowUid"
											:type="scope.row[item.colNo] ? 'primary' : 'info'"
											class="p5"
											@click="scope.row[item.colNo] = Number(!scope.row[item.colNo])"
											>{{ scope.row[item.colNo] ? '已确认' : '待确认' }}</el-button
										>
										<!-- 默认显示 -->
										<el-input v-else v-model="scope.row[item.colNo]" size="mini" placeholder="请输入内容" clearable></el-input>
									</template>
								</u-table-column>
								<u-table-column width="100" align="right">
									<template #header>
										<el-button v-if="dItem.id !== 'd1'" type="text" class="" @click="addRow(dItem)">添加一行</el-button>
									</template>
									<template slot-scope="scope">
										<el-popover placement="top" width="160" v-model="scope.row.visible">
											<p>删除后将不可恢复,确定删除吗？</p>
											<div class="text-right">
												<el-button size="mini" type="text" @click="scope.row.visible = false">取消</el-button>
												<el-button type="primary" size="mini" @click="delRow(dItem, scope.$index)">确定</el-button>
											</div>
											<el-button slot="reference" v-if="dItem.id !== 'd1'" type="text" class="">删除</el-button>
										</el-popover>
									</template>
								</u-table-column>
							</u-table>
						</div>
					</div>
					<div class="bottom-button mb20 sticky-bottom-0">
						<!-- <el-button v-if="titleName == '编辑'" class="mr20" @click="delDetail">删 除</el-button> -->
						<el-button @click="save8DDetail" type="primary">保存8D报告</el-button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';
export default {
	name: 'defectDetailCom',
	props: { warehouseOptions: Array },
	data() {
		return {
			nowUid: '',
			showCom: false, //控制弹窗显隐
			titleName: '',
			formList: [
				[
					{ name: '提出时间', prop: 'submitTime', class: 'label-required W15', type: 'datetime' },
					{ name: '提出人', prop: 'submitUid', class: 'label-required W10', type: 'select' },
					{ name: '责任人', prop: 'dutyUid', class: 'label-required W10', type: 'select' },
					{ name: '客户来源', prop: 'customerSource', class: 'label-required  W10' },
					{ name: '缺陷等级', prop: 'bugLevel', class: 'label-required W10', type: 'select' },
					{ name: '缺陷主题', prop: 'bugTopic', class: 'label-required  W40' },
				],
				// [
				// 	{ name: '其他', prop: 'inWid', class: 'W10', type: 'select' },
				// ],
			],
			eightDArry: [
				{
					id: 'd1',
					title: 'D1：成立小组',
					subTitle:
						'小组成员具备产品知识,有配给的时间并授予了权限,同时应具有所要求的能解决问题和实施纠正措施的技术素质。小组必须有一个牵头人(小组长)和高层领导。',
					tableColumn: [
						{ colName: '小组长', colNo: 'teamLeader', align: 'left', width: '200', type: 'select-user' },
						{ colName: '指导人', colNo: 'instructor', align: 'left', width: '200', type: 'select-user' },
						{ colName: '小组成员', colNo: 'groupMember_ids', align: 'left', width: '', type: 'select-multiple-user' },
					],
				},
				{
					id: 'd2',
					title: 'D2：问题描述',
					subTitle:
						'用量化的术语（工程语言）详细说明与该问题有关的内/外部顾客抱怨,如什么、地点、时间、程度、频率等，“什么东西出了什么问题”。',
				},
				{
					id: 'd3',
					title: 'D3：临时措施',
					subTitle: '找出和选择最佳“临时抑制措施”;决策 ;实施，并作好记录;验证；所选临时措施是否会导致新的问题产生？',
					tableColumn: [
						{ colName: '临时措施', colNo: 'measure', align: 'left', width: '200' },
						{ colName: '具体内容', colNo: 'content', align: 'left', width: '' },
						{ colName: '执行人', colNo: 'executor', align: 'left', width: '150', type: 'select-user' },
						{ colName: '执行日期', colNo: 'executorDate', align: 'left', width: '150', type: 'date' },
						{ colName: '确认人', colNo: 'confirmer', align: 'left', width: '150', type: 'select-user' },
						{ colName: '确认效果', colNo: 'status', align: 'center', width: '120', type: 'check' },
					],
				},
				{
					id: 'd4',
					title: 'D4：根本原因分析',
					subTitle: ' 列出可以用来解释问题起因的所有潜在原因，将原因相互隔离并确定产生问题的根本原因。',
					tableColumn: [
						{ colName: '根本原因', colNo: 'measure', align: 'left', width: '200' },
						{ colName: '具体内容', colNo: 'content', align: 'left', width: '' },
						{ colName: '责任人', colNo: 'executor', align: 'left', width: '150', type: 'select-user' },
					],
				},
				{
					id: 'd5',
					title: 'D5：纠正措施',
					subTitle: '并对方案进行评审以确定所选的校正措施能够解决客户问题，同时对其它过程不会有不良影响。',
					tableColumn: [
						{ colName: '纠正措施', colNo: 'measure', align: 'left', width: '200' },
						{ colName: '具体内容', colNo: 'content', align: 'left', width: '' },
						{ colName: '执行人', colNo: 'executor', align: 'left', width: '150', type: 'select-user' },
						{ colName: '执行日期', colNo: 'executorDate', align: 'left', width: '150', type: 'date' },
						{ colName: '确认人', colNo: 'confirmer', align: 'left', width: '150', type: 'select-user' },
						{ colName: '确认效果', colNo: 'status', align: 'center', width: '120', type: 'check' },
					],
				},
				{
					id: 'd6',
					title: 'D6：永久措施',
					subTitle:
						'制定一个实施永久措施的计划，确定过程控制方法并纳入文件，以确保根本原因的消除。在生产中应用该措施时应监督其长期效果。',
					tableColumn: [
						{ colName: '永久措施', colNo: 'measure', align: 'left', width: '200' },
						{ colName: '具体内容', colNo: 'content', align: 'left', width: '' },
						{ colName: '执行人', colNo: 'executor', align: 'left', width: '150', type: 'select-user' },
						{ colName: '执行日期', colNo: 'executorDate', align: 'left', width: '150', type: 'date' },
						{ colName: '确认人', colNo: 'confirmer', align: 'left', width: '150', type: 'select-user' },
						{ colName: '确认效果', colNo: 'status', align: 'center', width: '120', type: 'check' },
					],
				},
				{
					id: 'd7',
					title: 'D7：预防再次发生',
					subTitle: '修改现有的管理工作惯例以防止这一问题与所有类似问题重复发生。',
					tableColumn: [
						{ colName: '预防措施', colNo: 'measure', align: 'left', width: '200' },
						{ colName: '具体内容', colNo: 'content', align: 'left', width: '' },
						{ colName: '执行人', colNo: 'executor', align: 'left', width: '150', type: 'select-user' },
						{ colName: '执行日期', colNo: 'executorDate', align: 'left', width: '150', type: 'date' },
						{ colName: '确认人', colNo: 'confirmer', align: 'left', width: '150', type: 'select-user' },
						{ colName: '确认效果', colNo: 'status', align: 'center', width: '120', type: 'check' },
					],
				},
				{
					id: 'd8',
					title: 'D8：总结',
					subTitle: '有选择的保留重要文档；流览小组工作，将心得形成文件;了解小组对解决问题的集体力量，及对解决问题作出的贡献；',
					tableColumn: [
						{ colName: '总结', colNo: 'measure', align: 'left', width: '' },
						// { colName: '具体内容', colNo: 'content', align: 'left', width: '' },
						{ colName: '验证人', colNo: 'executor', align: 'left', width: '150', type: 'select-user' },
						{ colName: '验证日期', colNo: 'executorDate', align: 'left', width: '150', type: 'date' },
						{ colName: '确认人', colNo: 'confirmer', align: 'left', width: '150', type: 'select-user' },
						{ colName: '确认效果', colNo: 'status', align: 'center', width: '120', type: 'check' },
					],
				},
			],

			detailFormCopy: [],
			detailForm: {
				//明细详情
				brid: '',
				bugLevel: 0,
				bugTopic: '',
				customerSource: '',
				d1: {
					groupMember_ids: [],
					groupMember: [],
					instructor: '',
					instructorName: '',
					teamLeader: '',
					teamLeaderName: '',
				},
				d2: {
					description: '',
				},
				d3: [],
				d4: [],
				d5: [],
				d6: [],
				d7: [],
				d8: [],
				dutyUid: '',
				dutyUname: '',
				number: '',
				status: '',
				submitTime: '',
				submitUid: '',
				submitUname: '',
			},
			formRules: {
				submitTime: [{ required: true, message: '请输入提出时间！', trigger: 'blur' }],
				submitUid: [{ required: true, message: '请输入提出人！', trigger: 'blur' }],
				dutyUid: [{ required: true, message: '请输入责任人！', trigger: 'blur' }],
				customerSource: [{ required: true, message: '客户来源！', trigger: 'blur' }],
				bugLevel: [{ required: true, message: '请输入缺陷等级！', trigger: 'blur' }],
				bugTopic: [{ required: true, message: '请输入缺陷主题！', trigger: 'blur' }],
			},
		};
	},
	created() {
		this.nowUid = this.userInfos?.adminUserVO?.auid || ''; //当前用户id
	},
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['userList']),
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.detailForm = _.resetValues(this.detailForm); //重置对象
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 新增一行
		addRow(item) {
			// this.detailForm[item.id] = this.detailForm[item.id] || [];
			this.detailForm[item.id]?.push({
				confirmer: '',
				confirmerName: '',
				content: '',
				executor: '',
				executorDate: '',
				executorName: '',
				measure: '',
				status: '',
			});
			console.log(this.detailForm[item.id]);
		},
		// 删除行
		delRow(item, index) {
			this.detailForm[item.id].splice(index, 1);
			console.log(this.detailForm[item.id]);
		},

		// querySearch(查询接口)
		querySearch(queryStr, cb) {
			if (!queryStr) {
				this.handleSelect(null);
			}
			const str = JSON.stringify({
				wid: '',
				pageNum: 1,
				pageSize: 30,
			});
			this.$axios
				.selectMaterialInventoryDetails(str)
				.then(res => {
					if (res.data.success) {
						const result = res.data.data;
						cb(result);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectMaterialInventoryDetails|' + error);
				});
		},
		// handleSelect(选择时)
		handleSelect(item) {
			this.detailForm.materialNo = item?.materialNo || '';
			this.detailForm.materialName = item?.materialName || '';
			this.detailForm.materialSpec = item?.materialSpec || '';
			this.detailForm.unit = item?.unit || '';
			this.detailForm.materialBatch = item?.materialBatch || '';
			this.detailForm.outWid = item?.wid || '';
			this.detailForm.outCargoLocationNo = item?.cargoLocationNo || '';
		},

		// 添加/保存信息
		saveDetail(isClose = true) {
			if (_.checkRequired(this.detailForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}

			this.detailForm.submitUname = this.userList?.find(item => item.auid === this.detailForm.submitUid)?.userName || '';
			this.detailForm.dutyUname = this.userList?.find(item => item.auid === this.detailForm.dutyUid)?.userName || '';

			const API = this.detailForm.brid ? 'updateBugRecord' : 'addBugRecord';
			this.$axios[API](JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功！');
						this.detailFormCopy = _.deepClone(this.detailForm);
						if (!(this.detailForm.bugLevel > 1 && this.detailForm.brid)) {
							isClose && (this.showCom = false);
						}
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},
		// 保存8D报告信息
		save8DDetail(isClose = true) {
			if (_.checkRequired(this.detailForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}
			this.detailForm.d1.instructorName =
				this.userList?.find(item => item.auid === this.detailForm.d1.instructor)?.userName || '';
			this.detailForm.d1.teamLeaderName =
				this.userList?.find(item => item.auid === this.detailForm.d1.teamLeader)?.userName || '';
			this.detailForm.d1.groupMember =
				this.userList
					?.filter(item => this.detailForm.d1?.groupMember_ids?.includes(item.auid))
					?.map(item => {
						return { employeeName: item.userName, uid: item.auid };
					}) || [];

			const API = 'save8DRecord';
			this.$axios[API](JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						// isClose && (this.showCom = false);
						this.detailFormCopy = _.deepClone(this.detailForm);
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},
		// 删除信息
		delDetail() {
			this.$confirm('该数据将被删除，删除后数据不可恢复', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteTransferOrder(JSON.stringify({ toid: this.detailForm.toid }))
						.then(res => {
							if (res.data.success) {
								this.showCom = false;
								this.$succ(res.data.message);
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteTransferOrder |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},

		//显示弹窗
		showDetailCom(type, rowData) {
			this.titleName = type;
			if (rowData) {
				rowData.d1 = rowData.d1 || this.detailForm.d1;
				rowData.d2 = rowData.d2 || this.detailForm.d2;
				rowData.d3 = rowData.d3 || this.detailForm.d3;
				rowData.d4 = rowData.d4 || this.detailForm.d4;
				rowData.d5 = rowData.d5 || this.detailForm.d5;
				rowData.d6 = rowData.d6 || this.detailForm.d6;
				rowData.d7 = rowData.d7 || this.detailForm.d7;
				rowData.d8 = rowData.d8 || this.detailForm.d8;
				this.detailForm = { ...this.detailForm, ...rowData };
				const ids = rowData?.d1?.groupMember?.map(item => item.uid) || [];
				this.$set(this.detailForm.d1, 'groupMember_ids', ids);

				this.detailFormCopy = _.deepClone(this.detailForm);
				this.showCom = true;
			} else {
				this.detailForm.submitTime = new Date().getTime();
				this.detailFormCopy = _.deepClone(this.detailForm);
				this.showCom = true;
			}
		},

		//点击返回
		closeDetailCom() {
			let isSameData = true;
			isSameData = JSON.stringify(this.detailForm) == JSON.stringify(this.detailFormCopy);
			console.log('isSameData', isSameData);
			if (!isSameData) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.showCom = false;
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.showCom = false;
			}
		},
		//日期format
		dateFormat: _.dateFormat,
		jointString: _.jointString,
	},
};
</script>
<style lang="scss" scoped>
.defectDetailCom {
	.th-bg-gray {
		th {
			padding: 5px 0 !important;
			background-color: #e9e9e9 !important;
		}
	}
	.table-main {
		height: 100% !important;
		max-height: 300px !important;
		min-height: auto !important;
	}
}
</style>
