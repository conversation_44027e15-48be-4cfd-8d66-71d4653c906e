<template>
	<h2 v-if="isSeriesEmpty">暂无数据</h2>
	<EChart v-else v-bind="$props" />
</template>

<script>
import { isEmpty } from 'lodash';
import EChart from './EChart.vue';

export default {
	name: '<PERSON>chart<PERSON><PERSON>',
	components: { EChart },
	props: EChart.props,
	computed: {
		// 针对饼图数据是不是无效的判断
		isSeriesEmpty() {
			return isEmpty(this.seriesData) || this.seriesData.every(item => !item.value);
		},
	},
};
</script>
