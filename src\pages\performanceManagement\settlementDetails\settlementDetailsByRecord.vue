1
<template>
	<div class="settlementDetailsByRecord" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<!-- 订单明细组件 -->
		<OrderDetail ref="OrderDetail" @close="queryTableData(1)" />
		<div class="detail-wrapper">
			<div class="detail-head">
				<div>
					<span class="detail-title">{{ titleName }}业绩分配明细</span>
					<el-button type="text" class="ml10" :class="isFold ? 'el-icon-s-fold' : 'el-icon-s-unfold'" @click="openFold">
						{{ isFold ? '全部收起' : '全部展开' }}
					</el-button>
				</div>
				<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content table-wrapper">
				<u-table
					ref="uTableRef"
					class="table-main table-main2"
					row-key="rowKey"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					:expand-row-keys="expandKeys"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column label="日期" prop="receiptsDate" width="120" align="center">
						<template slot-scope="scope">
							<Tooltips
								:cont-str="dateFormat(scope.row.receiptsDate || scope.row.createTime, 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>

					<u-table-column label="收款金额" prop="receiptsAmount" width="120" align="right">
						<template slot-scope="scope">
							<!-- 金额保留两位小数-->
							<Tooltips
								:cont-str="scope.row.receiptsAmount?.toFixed(2) || '0.00'"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
						</template>
					</u-table-column>

					<!-- 展开行 -->
					<u-table-column label="" width="20" type="expand" align="left">
						<template slot-scope="scope">
							<u-table
								class="table-main"
								:data="scope.row.commissionList"
								:max-height="300"
								:row-height="30"
								show-header-overflow="title"
							>
								<u-table-column label="" width="230" align="left"></u-table-column>

								<u-table-column
									v-for="item in tableColumn"
									:key="'colCurr' + item.colNo"
									:label="item.colName"
									:prop="item.colNo"
									:align="item.align"
									:width="item.width"
									sortable
									resizable
								>
									<template slot-scope="scope">
										<!-- 各种日期 -->
										<Tooltips
											v-if="['collectionTime'].includes(item.colNo)"
											:cont-str="dateFormat(scope.row[item.colNo], 'line')"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
										<!-- 对象类别  0 系统托管-代扣， 1 系统托管-公司，10 用户-->
										<Tooltips
											v-else-if="item.colNo == 'commissionType'"
											:cont-str="commissionTypeMap[scope.row[item.colNo]]"
											:cont-width="scope.column.width || scope.column.realWidth"
										/>
										<!-- 金额保留两位小数-->
										<Tooltips
											v-else-if="['receiptsAmount', 'allocateSystemAmount', 'allocateAmount'].includes(item.colNo)"
											:cont-str="scope.row[item.colNo]?.toFixed(2) || '0.00'"
											:cont-width="scope.column.width || scope.column.realWidth"
										/>

										<!-- 补偿-->
										<Tooltips
											v-else-if="['allocateCompensationAmount', 'allocateMemo'].includes(item.colNo)"
											class="red"
											:cont-str="scope.row[item.colNo] || ''"
											:cont-width="scope.column.width || scope.column.realWidth"
										/>

										<!-- 默认显示 -->
										<Tooltips
											v-else
											:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
									</template>
								</u-table-column>
							</u-table>
						</template>
					</u-table-column>

					<u-table-column label="居间费(元)" prop="mediAmount" width="120" align="right">
						<template slot-scope="scope">
							<Tooltips
								:cont-str="accAdd(scope.row.mediAmount || 0, 0, 2)"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
						</template>
					</u-table-column>
					<u-table-column label="业绩金额" prop="calculateAmount" width="120" align="right">
						<template slot-scope="scope">
							<Tooltips
								:cont-str="accAdd(scope.row.calculateAmount, 0, 2)"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
						</template>
					</u-table-column>
					<u-table-column label="合同阶段" prop="contractInfo" width="350" align="left">
						<template slot-scope="scope">
							<!-- 合同阶段信息-->
							<Tooltips :cont-str="getContractInfo(scope.row)" :cont-width="scope.column.width || scope.column.realWidth" />
						</template>
					</u-table-column>
					<u-table-column label="项目类型" prop="projectType" width="100" align="left">
						<template slot-scope="scope">
							<!-- 项目类型 -->
							<Tooltips
								:cont-str="projectTypeMap[scope.row.projectType] || '续费'"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
						</template>
					</u-table-column>
					<u-table-column label="来源" prop="dmid" width="100" align="left">
						<template slot-scope="scope">
							<!-- 来源-->
							<Tooltips
								class="hover-green"
								:cont-str="scope.row.dmid ? '合同收款' : '续费'"
								:cont-width="scope.column.width || scope.column.realWidth"
								@click.native="openDetail('查看合同', scope.row)"
							/>
						</template>
					</u-table-column>
					<u-table-column label="" width="" align="left"></u-table-column>
				</u-table>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';
import OrderDetail from '@/pages/deliveryManagement/orderManagement/OrderDetail.vue';
import { projectTypeOptions, projectTypeMap, commissionTypeMap } from '@/assets/js/contractSource';
export default {
	name: 'settlementDetailsByRecord',
	components: { OrderDetail },
	props: {
		searchForm: Object,
	},
	data() {
		return {
			isFold: false,
			showCom: false, //控制弹窗显隐
			titleName: '按收款记录检查',

			//表格相关
			expandKeys: [], //展开id
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			// tableColumn: [
			// 	{ colName: '收款日期', colNo: 'receiptsDate', align: 'center', width: '100' },
			// 	{ colName: '收款金额', colNo: 'receiptsAmount', align: 'right', width: '100' },
			// 	{ colName: '合同阶段', colNo: 'contractInfo', align: 'left', width: '' },
			// 	{ colName: '来源', colNo: 'dmid', align: 'left', width: '100' },
			// ],
			tableColumn: [
				{ colName: '分配类别', colNo: 'commissionType', align: 'center', width: '80' },
				{ colName: '分配对象', colNo: 'commissionObjectName', align: 'left', width: '150' },
				{ colName: '分配说明', colNo: 'allocateSummary', align: 'left', width: '180' },
				{ colName: '业绩分配', colNo: 'allocateSystemAmount', align: 'right', width: '100' },
				{ colName: '补偿', colNo: 'allocateCompensationAmount', align: 'right', width: '100' },
				{ colName: '备注', colNo: 'allocateMemo', align: 'left', width: '100' },
				{ colName: '个人收入', colNo: 'allocateAmount', align: 'right', width: '100' },
			],

			projectTypeMap, // 项目类型
			commissionTypeMap, // 提成类别
		};
	},
	created() {},
	computed: { ...mapGetters(['userInfos']) },

	watch: {
		showCom(val) {
			if (!val) {
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 展开和收起
		openFold() {
			this.isFold = !this.isFold;
			this.expandKeys = this.isFold ? this.tableData.map(i => i.rowKey) : this.expandKeys = [];
			this.$refs.uTableRef?.reloadData(this.tableData); // 加载页面数据
		},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			this.showCom = true;
			const dateEnd = this.$moment(this.searchForm.dateStart).endOf('month').valueOf(); // 当前月份最后一天时间戳
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectDeliverStageCommissionDetailListForOrderMgr'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.searchForm,
				dateEnd,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data.map((item, index) => {
							// const commissionItem = item?.commissionList ? item?.commissionList[0] : {};
							const deliverManagementDetailVO = item?.deliverManagementDetailVO || {}; //合同信息
							const deliverStageManagementVO = item?.deliverStageManagementVO || {}; //合同阶段信息
							const orderManagementDetailVO = item?.orderManagementDetailVO || {}; //订单信息
							// 业绩金额 = 收款金额 - 居间费
							const calculateAmount =
								_.accAdd(
									Number(orderManagementDetailVO?.receiptsAmount) || 0,
									-Number(deliverStageManagementVO?.mediAmount) || 0,
									2,
								) || 0.0;
							return {
								commissionList: item.commissionList || [],
								...orderManagementDetailVO,
								...deliverStageManagementVO,
								...deliverManagementDetailVO,
								calculateAmount,
								rowKey: index + 1,
							};
						});
						console.log('tableData', this.tableData);
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.expandKeys = this.isFold ? this.tableData.map(i => i.rowKey) : this.expandKeys = [];
				this.tableSort = { prop, order };
				const sortedData = order && prop ? _.sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 打开明细
		openDetail(type, row) {
			if (row.dmid) {
				// 有合同信息：打开合同业绩分配详情
				this.$emit('openAllocationPlan', type, row);
			} else if (row.omid) {
				// 无合同信息但有订单信息：打开订单详情
				this.$refs.OrderDetail.openDialog('查看订单', row);
			}
		},
		// 获取合同阶段信息
		getContractInfo(row) {
			if (row.dmid) {
				// 有合同信息
				const { projectName, registeredBusinessName, stage, standard } = row;
				return _.jointString(' | ', projectName || registeredBusinessName, stage, standard);
			} else if (row.omid) {
				// 无合同信息但有订单信息
				const { orderNo, registeredBusinessName, teamDueDate } = row;
				return _.jointString(' | ', registeredBusinessName, `续费订单号：${orderNo}`, `续费至：${this.dateFormat(teamDueDate)}`);
			} else {
				return '无';
			}
		},
		// 表尾合计
		summaryMethod({ columns, data }) {
			// 如果是查询这个分配对象 则对收款金额进行合计
			const summaryProp = ['receiptsAmount', 'mediAmount', 'calculateAmount'];

			const means = ['合计']; // 合计
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					if (summaryProp.includes(column.property)) return Number(item[column.property]) || 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? _.accAdd(prev, curr, 2) : prev;
				}, 0); //合计计算
				means[columnIndex] = sum > 0 ? <span>{sum}</span> : '';
			}
			return [means];
		},

		//数据导出
		openExport: _.debounce(function () {
			const PROPS = {
				DATA: JSON.stringify({
					...this.searchForm,
				}), //接口参数
				API: 'xxxxxDownload', //导出接口
				downloadData: 'xxxxx明细', //数据报表参数（后台确认字段downloadData）
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),

		dateFormat: _.dateFormat, //日期format
		accAdd: _.accAdd,
	},
};
</script>

<style lang="scss" scoped>
.settlementDetailsByRecord {
	.table-main2 {
		height: 100% !important;
	}
}
</style>
