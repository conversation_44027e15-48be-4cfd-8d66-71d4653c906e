1
<template>
	<div class="oceanTable" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head overflow-x-auto overflow-y-hidden">
				<div class="detail-title flex-align-center gap-10">
					<div class="min-w-100 ellipsis">
						{{ `公海清单(${titleName})` }}
					</div>
					<div
						class="p5 pl10 pr10 fs-12 border-radius-5 ellipsis"
						:class="visitedNum == currentData.maxVisitPerDay ? 'bg-red' : 'bg-green'"
					>
						今日已查阅 {{ `${visitedNum}/${currentData.maxVisitPerDay}` }}
					</div>
					<div
						class="p5 pl10 pr10 fs-12 border-radius-5 ellipsis"
						:class="collectedNum == currentData.maxCollectPerDay ? 'bg-red' : 'bg-green'"
					>
						今日已领取 {{ `${collectedNum || 0}/${currentData.maxCollectPerDay}` }}
					</div>

					<el-input
						class="searchBox min-w-100 vw8"
						size="mini"
						v-model="regionFilter"
						placeholder="区域"
						clearable
						@input="filterTableData"
					></el-input>
					<el-input
						class="searchBox min-w-100 vw8"
						size="mini"
						v-model="companyFilter"
						placeholder="公司名"
						clearable
						@input="filterTableData"
					></el-input>
					<el-input
						class="searchBox min-w-100 vw6"
						size="mini"
						v-model="oldConsultantConsultFilter"
						placeholder="原咨询人"
						clearable
						@input="filterTableData"
					></el-input>
					<el-input
						class="searchBox min-w-100 vw6"
						size="mini"
						v-model="oldConsultantFilter"
						placeholder="原业务员"
						clearable
						@input="filterTableData"
					></el-input>

					<!-- 死海 -->
					<el-checkbox
						v-if="isSuperAdmin"
						class="m0 p0"
						v-model="deadSeaFilter"
						:true-label="1"
						:false-label="0"
						@change="filterTableData"
					>
						含死海
					</el-checkbox>
					<!-- 含今天 -->
					<!-- <el-checkbox v-model="todayFilter" :true-label="1" :false-label="0" @change="filterTableData">含今天</el-checkbox> -->
				</div>

				<div class="flex-align-center">
					<el-button type="text" class="el-icon-refresh-right" @click="$emit('refresh')">刷新</el-button>
					<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
				</div>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content table-wrapper">
				<u-table
					ref="uTableRef"
					class="table-main table-main2"
					:height="1200"
					:row-height="45"
					:row-class-name="getRowColor"
					@sort-change="sortChange"
					show-header-overflow="title"
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="['createTime'].includes(item.colNo)"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="['highSeasTime', 'deadSeasTime'].includes(item.colNo)"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 询盘编号 -->
							<Tooltips
								v-else-if="item.colNo == 'number'"
								class="hover-green green"
								:class="getRowColor(scope)"
								@click.native="openDetail('修改', scope.row, null, scope.$index)"
								:cont-str="scope.row[item.colNo] || '未知'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<!-- 询盘质量 -->
							<Tooltips
								v-else-if="item.colNo == 'quality'"
								:cont-str="qualityMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 商机质量 -->
							<Tooltips
								v-else-if="item.colNo == 'businessOpportunityQuality'"
								:cont-str="businessQualityMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 公司名 -->
							<Tooltips
								v-else-if="['companyName'].includes(item.colNo)"
								:cont-str="scope.row.companyName || scope.row.registeredBusinessName || '公司名未知'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 录音 -->
							<div class="audioPlayer" v-else-if="item.colNo == 'callRecording' && scope.row[item.colNo]">
								<!-- 询盘录音播放器 -->
								<InquiryAudioPlayer :audioUrl="scope.row[item.colNo]" :idid="scope.row.idid" />
							</div>

							<!-- 进入公海方式 -->
							<Tooltips
								v-else-if="['highSeasWay', 'deadSeasWay'].includes(item.colNo)"
								:cont-str="['自动', '手动', '其他'][scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 是否死海 -->
							<el-checkbox
								v-else-if="item.colNo == 'deadSeasFlag'"
								v-model="scope.row.deadSeasFlag"
								:true-label="1"
								:false-label="0"
								@change="updateRow(scope.row, scope.$index)"
							></el-checkbox>

							<!-- 其他 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<u-table-column label="" width="60" align="right">
						<template slot-scope="scope">
							<el-button
								type="text"
								class="el-icon-edit-outline"
								@click="openDetail('修改', scope.row, null, scope.$index)"
							></el-button>
						</template>
					</u-table-column>
				</u-table>
			</div>
		</div>
	</div>
</template>
<script>
import { dateFormat, jointString, sortTableData, debounce, resetValues } from '@/util/tool';
import { mapGetters } from 'vuex';
import { qualityMap, businessQualityMap } from '@/assets/js/inquirySource.js';
import InquiryAudioPlayer from '@/components/InquiryAudioPlayer'; //询盘录音播放器
export default {
	name: 'oceanTable',
	components: { InquiryAudioPlayer },
	props: {
		oceanData: { type: Array, default: () => [] },
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '',
			regionFilter: '', //过滤区域
			companyFilter: '', //过滤公司名
			oldConsultantConsultFilter: '', //过滤原咨询人
			oldConsultantFilter: '', //过滤原业务员
			deadSeaFilter: 0, //过滤死海
			// todayFilter: 1, //过滤今天
			CollectedKeys: [], //已领取的
			VisitedKeys: [], //已查阅的
			filteredData: [],
			//当前点击某项的数据
			currentData: {
				idSet: [],
				maxCollectPerDay: 10,
				maxVisitPerDay: 100,
				ruleMap: {},
				ruleNumber: 0,
				ruleTime: 0,
				todayCollectedItems: [],
				todayVisitedItems: [],
			},
			tableSort: { prop: '', order: '' },
			tableData: [],

			qualityMap, // 询盘质量
			businessQualityMap, // 商机质量
		};
	},
	created() {},
	computed: {
		...mapGetters(['userInfo', 'userRoles']),
		// 是否超级管理员
		isSuperAdmin() {
			return this.userRoles?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
		// 动态表格列
		tableColumn() {
			// 如果是超级管理员，显示是否死海
			return [
				{ colName: '询盘编号', colNo: 'number', align: 'left', width: '100' },
				{ colName: '登记时间', colNo: 'createTime', align: 'center', width: '100' },
				{ colName: '区域', colNo: 'region', align: 'left', width: '150' },
				{ colName: '客户称呼', colNo: 'customerName', align: 'left', width: '' },
				{ colName: '公司名称', colNo: 'companyName', align: 'left', width: '100' },
				{ colName: '原咨询人', colNo: 'oldConsultantConsult', align: 'left', width: '110' },
				{ colName: '原业务员', colNo: 'oldConsultant', align: 'left', width: '110' },
				{ colName: '跟单次数', colNo: 'count', align: 'right', width: '70' },
				{ colName: '询盘质量', colNo: 'quality', align: 'left', width: '80' },
				{ colName: '商机质量', colNo: 'businessOpportunityQuality', align: 'left', width: '80' },
				{ colName: '询盘录音', colNo: 'callRecording', align: 'left', width: '' },
				{ colName: '进入时间', colNo: 'highSeasTime', align: 'center', width: '100' },
				{ colName: '进入方式', colNo: 'highSeasWay', align: 'center', width: '' },
				{ colName: '进入原因', colNo: 'highSeasReason', align: 'left', width: '' },
				{ colName: '进入次数', colNo: 'highSeasCount', align: 'right', width: '' },
				{ colName: '查阅记录', colNo: 'latestVisitorMemo', align: 'left', width: '' },
				...(this.isSuperAdmin
					? [
							{ colName: '是否死海', colNo: 'deadSeasFlag', align: 'center', width: '80' },
							{ colName: '进入死海时间', colNo: 'deadSeasTime', align: 'center', width: '100' },
							{ colName: '进入死海方式', colNo: 'deadSeasWay', align: 'center', width: '' },
						]
					: []),
			];
		},
		// 今日已查阅数量
		visitedNum() {
			return this.currentData.todayVisitedItems?.length || 0;
		},
		// 今日已领取数量
		collectedNum() {
			return this.currentData.todayCollectedItems?.length || 0;
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.currentData = resetValues(this.currentData); //重置对象
				this.tableData = [];
				this.$refs.uTableRef?.reloadData([]);
				this.$emit('close');
			}
		},
	},
	mounted() {},
	destroyed() {
		this.tableData = [];
	},
	methods: {
		// 过滤表格数据
		filterTableData: debounce(function () {
			const filterFn = item => {
				const {
					province,
					city,
					area,
					companyName,
					registeredBusinessName,
					oldConsultantConsult,
					oldConsultant,
					highSeasTime,
					deadSeasFlag,
				} = item;
				// 地区
				const regionMatch =
					!this.regionFilter ||
					province?.includes(this.regionFilter) ||
					city?.includes(this.regionFilter) ||
					area?.includes(this.regionFilter);
				// 公司名
				const companyMatch =
					!this.companyFilter ||
					companyName?.includes(this.companyFilter) ||
					registeredBusinessName?.includes(this.companyFilter);
				// 原咨询人
				const oldConsultantConsultMatch =
					!this.oldConsultantConsultFilter || oldConsultantConsult?.includes(this.oldConsultantConsultFilter);
				// 原业务员
				const oldConsultantMatch = !this.oldConsultantFilter || oldConsultant?.includes(this.oldConsultantFilter);
				// 今天
				// const todayMatch = !highSeasTime
				// 	? true
				// 	: this.todayFilter
				// 		? true // 勾选了"含今天"，所有数据都显示
				// 		: this.$moment(highSeasTime).startOf('day').valueOf() !== this.$moment().startOf('day').valueOf(); // 未勾选"含今天"，排除今天的数据
				// 死海
				const deadSeaMatch = this.deadSeaFilter ? true : deadSeasFlag != 1;
				return regionMatch && companyMatch && oldConsultantMatch && oldConsultantConsultMatch && deadSeaMatch;
			};
			this.filteredData = this.tableData.filter(filterFn);
			const { prop, order } = this.tableSort;
			if (order && prop) {
				const sortedData = sortTableData(this.filteredData, prop, order); // 如果已经排序时
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				return;
			}

			this.$refs.uTableRef?.reloadData(this.filteredData || []); // 加载页面数据
		}),

		// 获取表格数据
		queryTableData: debounce(async function (oceanData) {
			if (!this.showCom) return;
			if (!oceanData || oceanData.length == 0 || !oceanData[5]?.ruleMap || !this.currentData.ruleTime) return;
			const ruleMap = oceanData[5].ruleMap || {}; // 映射数据 ruleMap
			let ruleMapData = []; //根据规则映射的数据
			this.currentData = oceanData.find(item => item.ruleTime == this.currentData.ruleTime);
			if (this.currentData.ruleTime == 1) {
				// 查看全部数据时 不需要再做数据映射
				ruleMapData = Object.keys(ruleMap).map(key => {
					return ruleMap[key];
				});
			} else {
				ruleMapData = this.currentData.idSet.map(id => ruleMap[id]).filter(Boolean);
			}
			// console.log('ruleMap:', ruleMap);
			// console.log('ruleMapData:', ruleMapData);
			this.currentData.todayCollectedItems = this.currentData.todayCollectedItems || [];
			this.currentData.todayVisitedItems = this.currentData.todayVisitedItems || [];
			// this.CollectedKeys = this.currentData.todayCollectedItems?.map(c => c.key) || [];
			this.VisitedKeys = this.currentData.todayVisitedItems?.map(v => v.key) || [];
			// 过滤掉已领取的数据并对已查阅的数据进行标记
			const tableData = ruleMapData?.map(item => {
				item.region = item.province == '其他' ? '其他' : jointString('/', item.province, item.city, item.area);
				if (this.VisitedKeys.includes(item.idid)) item.isVisited = true; //对已查阅的数据进行标记
				return item;
			});
			this.tableData = tableData;
			// if (
			// 	this.regionFilter ||
			// 	this.companyFilter ||
			// 	this.oldConsultantConsultFilter ||
			// 	this.oldConsultantFilter ||
			// 	this.deadSeaFilter
			// ) {
			// 	this.filterTableData();
			// } else {
			// 	this.sortChange(this.tableSort, true);
			// }
			// console.log('getOceanData', this.oceanData);
			// console.log('currentData', this.currentData);
			// console.log('tableData', this.tableData);
			this.filterTableData();
		}),
		// 更新行数据
		updateRow: debounce(function (row, index) {
			const { idid, deadSeasFlag } = row;
			const oldDeadSeasFlag = deadSeasFlag ? 0 : 1; // 原死海状态
			this.$confirm(`是否将该询盘${deadSeasFlag ? '移入' : '移出'}死海？`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'updateDeadSeasFlagByIdid';
					try {
						const res = await this.$axios[API](JSON.stringify({ idid, deadSeasFlag }));
						if (res.data.success) {
							this.$succ(res.data.message);
						} else {
							// 更新失败，恢复原状态
							this.$set(this.tableData, index, { ...this.tableData[index], deadSeasFlag: oldDeadSeasFlag });
							this.$err(res.data.message);
						}
					} catch (error) {
						// 更新失败，恢复原状态
						this.$set(this.tableData, index, { ...this.tableData[index], deadSeasFlag: oldDeadSeasFlag });
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					// 更新失败，恢复原状态
					this.$set(this.tableData, index, { ...this.tableData[index], deadSeasFlag: oldDeadSeasFlag });
					this.$message.info('已取消');
				})
				.finally(() => {
					this.filterTableData();
				});
		}),

		// 调用父组件的询盘详情
		async openDetail(type, row, api, index) {
			// 达到查阅数量或领取数量时 不允许操作
			if (this.collectedNum >= this.currentData.maxCollectPerDay || this.visitedNum >= this.currentData.maxVisitPerDay) {
				return this.$message.warning('已达到今日领取数量或查阅数量上限');
			}

			const { idid, number, companyName, registeredBusinessName } = row;
			const API = 'addSeaItemVisit'; // 公海询盘访问记录（每点击一个公海询盘发送一次该请求用于跟踪）
			try {
				const res = await this.$axios[API](
					JSON.stringify({
						key: idid,
						value: `${number} ${registeredBusinessName || companyName || '公司名未知'}`,
					}),
				);
				if (res.data.success) {
					// this.currentData.todayVisitedItems = res.data.data.todayVisitedItems;
					row.isVisited = true; // 对已查阅的数据进行标记
					this.$set(this.tableData, index, { ...this.tableData[index], isVisited: true }); // 对已查阅的数据进行标记
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}

			this.$emit('openDetail', type, row, 'selectInquiryDocumentaryOne');
		},
		//显示弹窗
		showDetailCom(data, oceanData) {
			const oceanMap = {
				'-1': '昨天',
				'-3': '3天内',
				'-7': '7天内',
				'-30': '30天内',
				'-60': '60天内',
				1: '全部',
			};
			this.showCom = true;
			this.currentData = data;
			this.titleName = oceanMap[data.ruleTime];
			this.queryTableData(oceanData);
		},
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				if (this.regionFilter || this.companyFilter || this.oldConsultantConsultFilter || this.oldConsultantFilter) {
					const sortedData = order && prop ? sortTableData(this.filteredData, prop, order) : this.filteredData;
					this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
					return;
				}
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格文本高光显示
		getRowColor({ row }) {
			return row.isVisited ? 'color-999' : ''; //已查阅
		},
		//日期format
		dateFormat: dateFormat,
		jointString: jointString,
	},
};
</script>
<style lang="scss" scoped>
.oceanTable {
	.border-radius-5 {
		border-radius: 5px;
	}
	.table-wrapper {
		.table-main2 {
			height: calc(100% - 25px) !important;
		}
	}
}
</style>
