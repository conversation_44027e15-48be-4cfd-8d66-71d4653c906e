<template>
	<div id="annualCoefficient">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<BaseLayout>
			<template #header>
				<span class="search-label">年度</span>
				<el-date-picker
					size="small"
					class="w-150"
					v-model="selectTimeYear"
					:default-value="selectTimeYear"
					type="year"
					value-format="yyyy"
					format="yyyy 年"
					placeholder="请选择年份"
					@change="queryTableData"
				>
				</el-date-picker>
				<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar">
					<ExportBtn @trigger="openExport" />
				</div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:data="tableData"
					:height="1200"
					:row-height="45"
					@sort-change="sortChange"
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="['startTime', 'endTime', 'modifyTime'].includes(item.colNo)"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable'; //导出组件
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		ExportBtn,
	},
	name: 'annualCoefficient', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'annualCoefficient', //激活tab页
			selectTimeYear: new Date(),
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableDataCopy: [],
			tableColumn: [
				{ colName: '开发人员', colNo: 'userName', align: 'left', width: '' },
				{ colName: '一月', colNo: '01', align: 'right', width: '' },
				{ colName: '二月', colNo: '02', align: 'right', width: '' },
				{ colName: '三月', colNo: '03', align: 'right', width: '' },
				{ colName: '四月', colNo: '04', align: 'right', width: '' },
				{ colName: '五月', colNo: '05', align: 'right', width: '' },
				{ colName: '六月', colNo: '06', align: 'right', width: '' },
				{ colName: '七月', colNo: '07', align: 'right', width: '' },
				{ colName: '八月', colNo: '08', align: 'right', width: '' },
				{ colName: '九月', colNo: '09', align: 'right', width: '' },
				{ colName: '十月', colNo: '10', align: 'right', width: '' },
				{ colName: '十一月', colNo: '11', align: 'right', width: '' },
				{ colName: '十二月', colNo: '12', align: 'right', width: '' },
			],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.selectTimeYear = this.dateFormat(this.selectTimeYear, 'yyyy');
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			// type && (this.tablePageForm.currentPage = 1);
			const API = 'selectYearResearchDepartment'; //接口
			this.$axios[API](JSON.stringify({ year: this.selectTimeYear }))
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							const tempData = item.selectMonthResearchDepartmentVOS;
							if (tempData && tempData.length > 0) {
								tempData.forEach((obj, index) => {
									if (obj.factor && obj.month) {
										item[obj.month] = obj.factor;
									}
								});
							}
						});
						this.tableData = res.data.data;
						this.tableDataCopy = _.deepClone(this.tableData);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						if (this.tableSort) {
							this.sortChange(this.tableSort);
						}
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableDataCopy, prop, order);
				this.tableData = sortedData;
			} else {
				this.tableData = this.tableDataCopy;
			}
		},

		//数据导出
		openExport: _.debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					year: this.selectTimeYear,
					queryParam: '',
				}), //接口参数
				API: 'exportYearResearchDepartment', //导出接口
				downloadData: '年度系数一览表导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
#annualCoefficient {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
