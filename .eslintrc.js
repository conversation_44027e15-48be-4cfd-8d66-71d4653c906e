// @see: http://eslint.cn
module.exports = {
	root: true,
	env: {
		browser: true,
		node: true,
		es6: true,
	},
	/* 
		常见的解析器：
		Espree：Espree是一个轻量级的、快速的解析器，由ESLint团队开发。它基于Esprima，并与ESLint紧密集成。Espree支持ES5、ES6和ES7等多个ECMAScript版本。
		Babel-ESLint：Babel-ESLint使用Babel进行代码转换，以支持更新的JavaScript语法特性。它可以处理ES6+的代码，并将其转换为ES5语法，然后交由ESLint进行检查。
		vue-eslint-parser：vue-eslint-parser是专门用于解析Vue.js单文件组件的解析器。它能够解析Vue模板、JavaScript和样式块，并提供对Vue特定语法的支持。
		@typescript-eslint/parser：如果项目使用TypeScript，@typescript-eslint/parser是一个用于解析TypeScript代码的解析器。它可以处理TypeScript的语法和类型注解，并与@typescript-eslint插件集成。
	*/
	/* 指定如何解析语法 */
	parser: 'vue-eslint-parser',
	/* 优先级低于 parse 的语法解析配置 */
	parserOptions: {
		// parser: 'espree',
		sourceType: 'module',
		ecmaVersion: 2020, //ECMA 版本 2020 支持新特性如可选链 ?. 在2018前都不支持
		ecmaFeatures: {
			globalReturn: false, //是否允许在全局作用域中使用 return 语句
			impliedStrict: false, //启用隐式严格模式检查。
			jsx: true, //处理 JSX 语法以及与之相关的 ECMAScript 功能
		},
	},

	/* 继承某些已有的规则 
		下面是一些常见的ESLint扩展，可以根据需要选择(使用前请安装相关依赖)：
		plugin:vue/essential: 提供了一组基本的Vue规则，适用于简单的Vue项目。
		plugin:vue/recommended: 提供了一组推荐的Vue规则，适用于大多数项目。
		plugin:vue/strongly-recommended: 提供了一组更严格的Vue规则，适用于对代码质量有更高要求的项目。
		airbnb-base: 基于Airbnb团队的JavaScript风格指南的规则，适用于编写清晰、一致且易于维护的JavaScript代码。
		standard: JavaScript标准风格的规则，适用于编写一致且易于阅读的JavaScript代码。
		google: Google JavaScript风格指南的规则，适用于编写符合Google风格的JavaScript代码
	*/
	extends: ['plugin:vue/strongly-recommended', 'eslint:recommended', 'plugin:prettier/recommended'],
	/*
	 * "off" 或 0    ==>  关闭规则
	 * "warn" 或 1   ==>  打开的规则作为警告（不影响代码执行）
	 * "error" 或 2  ==>  规则作为一个错误（代码不能执行，界面报错）
	 */
	rules: {
		// eslint 相关规则文档 (@see:http://eslint.cn/docs/rules)
		'no-var': 'warn', // 要求使用 let 或 const 而不是 var
		'no-multiple-empty-lines': ['error', { max: 1 }], // 不允许多个空行
		'no-use-before-define': 'warn', // 禁止在 函数/类/变量 定义之前使用它们
		"no-unused-vars": ["warn", 
			{ "varsIgnorePattern": "^map(Getters|State|Mutations|Actions)$" },
		], // 禁止出现未使用过的变量
		'prefer-const': 'warn', // 此规则旨在标记使用 let 关键字声明但在初始分配后从未重新分配的变量，要求使用 const
		'no-irregular-whitespace': 'error', // 禁止不规则的空白
		'no-extra-parens': 'warn', // 禁止出现不必要的括号
		'no-duplicate-case': 'warn', // 禁止出现重复的 case 标签
		'no-func-assign': 'warn', // 禁止对 function 声明重新赋值
		'no-unreachable': 'off', // 禁止在 return、throw、continue 和 break 语句之后出现不可达代码
		'default-case': 'warn', // 要求 switch 语句中有 default 分支
		// vue 相关规则文档 (@see:https://eslint.vuejs.org/rules)
		'vue/no-v-html': 'warn', // 禁止使用 v-html
		'vue/v-slot-style': 'warn', // 强制执行 v-slot 指令样式
		'vue/no-mutating-props': 'off', // 不允许组件 prop的改变
		'vue/custom-event-name-casing': 'off', // 为自定义事件名称强制使用特定大小写
		'vue/attributes-order': 'off', // vue api使用顺序，强制执行属性顺序
		'vue/one-component-per-file': 'off', // 强制每个组件都应该在自己的文件中
		'vue/first-attribute-linebreak': 'off', // 强制首个属性换行
		'vue/multiline-html-element-content-newline': 'off', // 在多行元素的内容之前和之后需要换行符
		'vue/singleline-html-element-content-newline': 'off', // 在单行元素的内容之前和之后需要换行符
		'vue/attribute-hyphenation': 'off',// 对模板中的自定义组件强制执行属性命名样式
		'vue/require-default-prop': 'off', // 此规则要求为每个 prop 为必填时，必须提供默认值
		'vue/multi-word-component-names': 'off', // 要求组件名称始终为 “-” 链接的单词
		"vue/component-definition-name-casing": 'off',  //强制 Vue 组件的 name 属性 按照大驼峰或其他规则
	},
};
