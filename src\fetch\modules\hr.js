/* 人力管理API */

const urlList = [
	/* 能力在线评测 */
	'/background/web/CandidateController/candidateHrResult', //hr填写面试结果
	'/background/web/CandidateController/selectCandidateSituation', //查看候选人面试情况

	'/background/web/CandidateController/addCandidateLabel', //添加/修改候选人标签
	'/background/web/CandidateController/addLabelText', //添加/修改标签
	'/background/web/CandidateController/deleteCandidateLabel', //解绑候选人标签
	'/background/web/CandidateController/deleteLabelText', //删除标签
	'/background/web/CandidateController/selectLabelText', //查看标签
	'/background/web/CandidateController/selectCandidateLabelText', //查看候选人标签

	'/background/web/CandidateQuestionController/addCandidateQuestion', //新增题库
	'/background/web/CandidateQuestionController/selectCandidateQuestion', //查看题库
	'/background/web/CandidateQuestionController/updateCandidateQuestion', //修改题库
	'/background/web/CandidateQuestionController/deleteCandidateQuestion', //修改题库
];

// 重名函数映射
const apiNameMap = {};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['candidateHrResult'].includes(urlName)) {
		timeout = 60000;
	}

	return { urlName, url, timeout };
});

export default apiList;
