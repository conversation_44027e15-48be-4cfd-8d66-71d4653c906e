<template>
	<div id="productionList">
		<!-- 导入弹窗 -->
		<ImportTable ref="ImportTable" @refresh="getTableData" />
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<BaseLayout>
			<template #header>
				<el-input
					class="searchBox"
					size="small"
					clearable
					v-model="queryStr"
					placeholder="请输入产品名称"
					@input="getTableData(1)"
				></el-input>
				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="getTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-toolbar">
					<el-button type="text" class="icon-third-bt_newdoc" @click="openDialog(null, '添加产品')">添加</el-button>
					<ExportBtn @trigger="openExport" />
				</div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="item.colNo == 'bargainTime' || item.colNo == 'protectDeadline'"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<u-table-column label="" width="" align="">
						<template slot-scope="scope">
							<el-button type="text" class="el-icon-edit" @click="openDialog(scope.row, '修改产品')">修改</el-button>
						</template>
					</u-table-column>
					<u-table-column v-for="item in 4" :key="item" label="" width="" align=""> </u-table-column>
				</u-table>
			</template>
		</BaseLayout>

		<!-- 弹窗 -->
		<el-dialog :visible.sync="dialogEdit" width="30%" :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="5vw" label-position="left" ref="editFormRef" :rules="editRules">
				<el-form-item label="产品名称" prop="name">
					<el-input v-model="editForm.name"></el-input>
				</el-form-item>
				<el-form-item label="产品类别" prop="classify">
					<el-input v-model="editForm.classify" placeholder="硬件/软件/定制/续费/其他"></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveEdit">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable';
import ImportTable from '@/components/ImportTable'; //数据导入
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		ImportTable,
		ExportBtn,
	},
	name: 'productionList',
	data() {
		return {
			queryStr: '',
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableDataCopy: [],
			tableHeight: 640,
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '产品', colNo: 'name', align: 'left', width: '' },
				{ colName: '类别', colNo: 'classify', align: 'left', width: '' },
			],

			dialogEdit: false,
			editForm: {
				pid: '',
				classify: '',
				name: '',
			},
			dialogTitle: '客户详情',
			editRules: {
				name: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
				classify: [{ required: true, message: '请输入产品类型', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.getTableData();

		// console.log('userInfos', this.userInfos);
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 数据导入
		openImport: _.debounce(function () {
			const PROPS = {
				API: 'importProduct', //导入接口
				templateName: '产品导入模板', //模板文件名称（下载模板用）
				dataName: '产品', //数据名（提示：成功导入xxx数据xxx条!）
			};
			this.$refs.ImportTable.openImport(PROPS);
		}),

		changeTab() {},

		getTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const str = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				query: this.queryStr,
			});
			this.$axios
				.productList(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tableDataCopy = _.deepClone(this.tableData);
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('productList |' + error);
				});
		}),
		openDialog(row, type) {
			if (row) {
				// console.log('row', row);
				this.editForm.name = row.name;
				this.editForm.pid = row.pid;
				this.editForm.classify = row.classify;
			} else {
				//添加
				this.editForm.name = '';
				this.editForm.pid = '';
				this.editForm.classify = '';
			}
			this.dialogTitle = type;
			this.dialogEdit = true;
		},
		closeDialog() {
			this.dialogEdit = false;
			this.dialogEdit = false;
			// this.editForm.classify = '';
			// this.editForm.checkStatus = '';
			this.$refs.editFormRef.resetFields();
			// this.editForm.agentGroup = ''
		},
		saveEdit() {
			const { classify, name, pid } = this.editForm;
			const str = JSON.stringify({
				classify,
				name,
				pid,
			});
			this.$axios
				.addOrUpdateProduct(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.closeDialog();
						this.getTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addOrUpdateProduct  |' + error);
				});
		},
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.getTableData();
		},
		//日期format
		dateFormat: _.dateFormat,

		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		//数据导出
		openExport: _.debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					pageNum: this.tablePageForm.currentPage,
					pageSize: this.tablePageForm.pageSize,
					query: this.queryStr,
				}), //接口参数
				API: 'exportProduct', //导出接口
				downloadData: '产品导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
#productionList {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
