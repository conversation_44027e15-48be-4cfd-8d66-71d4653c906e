'use strict';
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path');
const devEnv = require('./dev.env');
const prodEnv = require('./prod.env');
module.exports = {
	dev: {
		env: devEnv,
		// 静态资源文件夹
		assetsSubDirectory: 'static',
		// 发布路径
		assetsPublicPath: '/',
		/* 
			代理配置表，在这里可以配置特定的请求代理到对应的API接口 
			例如将'localhost:8080/api/xxx'代理到'https://baidu.cn/api/xxx'
			@see:https://vuejs-templates.github.io/webpack/proxy.html
		*/
		proxyTable: {
			// 匹配所有以 '/dev-api' 开头的请求路径
			'/dev-api': {
				// target: 'http://ops.lightmes.cn/', //生产环境
				// target: 'https://ops.test.base.lightmes.cn/', //测试环境
				target: devEnv.API_HOST.replace(/"/g, ''), //去除双引号
				pathRewrite: { '/dev-api': '' }, // 重写地址，将前缀 '/dev-api' 转为 '/'。
				changeOrigin: true, // 加了这个属性，那后端收到的请求头中的host是目标地址 target
				secure: false, // 如果是https接口，需要配置这个参数
			},
		},

		// Various Dev Server settings
		// host: 'localhost', // can be overwritten by process.env.HOST
		// host: '************', // can be overwritten by process.env.HOST
		host: '0.0.0.0', // 允许通过内网 IP 访问
		port: 8082, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
		autoOpenBrowser: false,
		errorOverlay: true,
		notifyOnErrors: true,
		poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

		/**
		 * Source Maps
		 */

		// https://webpack.js.org/configuration/devtool/#development
		// devtool: 'eval-cheap-module-source-map',
		devtool: 'source-map',

		// If you have problems debugging vue-files in devtools,
		// set this to false - it *may* help
		// https://vue-loader.vuejs.org/en/options.html#cachebusting
		// cacheBusting: true,

		cssSourceMap: true,
	},

	build: {
		env: prodEnv,
		// Template for index.html
		index: path.resolve(__dirname, '../dist/index.html'),

		// Paths
		assetsRoot: path.resolve(__dirname, '../dist'),
		assetsSubDirectory: 'static',

		//上传到服务器后资源访问地址
		assetsPublicPath: '/', //放在服务器根目录下则不需要配置
		// assetsPublicPath: '/admin/', //如果放在服务器其他目录下则需要配置如： http://ops.lightmes.cn/admin/#/xxxxxx

		/**
		 * Source Maps
		 */

		productionSourceMap: false,
		// https://webpack.js.org/configuration/devtool/#production
		devtool: 'nosources-source-map',

		// Gzip off by default as many popular static hosts such as
		// Surge or Netlify already gzip all static assets for you.
		// Before setting to `true`, make sure to:
		// npm install --save-dev compression-webpack-plugin
		productionGzip: true,
		productionGzipExtensions: ['js', 'css'],

		// Run the build command with an extra argument to
		// View the bundle analyzer report after build finishes:
		// `npm run build --report`
		// Set to `true` or `false` to always turn it on or off
		// bundleAnalyzerReport: process.env.npm_config_report
		bundleAnalyzerReport: false, // 各个包的体积可视化分析报告
		buildSpeedReport: false, //查看各个plugins和Loaders过程分析耗时的开关
	},
};
