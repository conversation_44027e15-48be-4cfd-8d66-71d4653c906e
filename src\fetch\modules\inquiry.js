/* 询盘管理API */

const urlList = [
	// 询盘管理
	'/background/web/inquiryDocumentaryController/selectInquiryDocumentaryList', // 查询询盘记录列表
	'/background/web/inquiryDocumentaryController/export', // 导出询盘记录
	'/background/web/inquiryDocumentaryController/addInquiryDocumentary', // 添加询盘记录
	'/background/web/inquiryDocumentaryController/basicInfoUpdate', // 询盘基本信息修改
	'/background/web/inquiryDocumentaryController/checkName', // 询盘基本信息-校验公司工商注册名
	'/background/web/inquiryDocumentaryController/checkPhone', // 询盘基本信息-校验手机号
	'/background/web/inquiryDocumentaryController/feedbackUpdate', // 业务反馈信息修改保存
	'/background/web/inquiryDocumentaryController/selectInquiryDocumentaryOne', // 用于询盘详情弹窗的修改回显
	'/background/web/inquiryDocumentaryController/selectOperationFlow', // 查询工作河流信息
	'/background/web/inquiryDocumentaryController/selectMonthData', // 查询工作河流的个人询盘信息
	'/background/web/inquiryDocumentaryController/exportMonthData', // 导出工作河流的个人询盘信息
	'/background/web/inquiryDocumentaryController/exportOperationFlow', // 导出工作河流信息
	'/background/web/inquiryDocumentaryController/selectDocumentaryDaily', // 查询跟单日报
	'/background/web/inquiryDocumentaryController/selectTrend', // 询盘趋势-询盘趋势列表展示
	'/background/web/inquiryDocumentaryController/exportTrend', // 询盘趋势-询盘趋势导出
	'/background/web/inquiryDocumentaryController/selectSuperUser', // 权限控制-带*号用户查询
	'/background/web/inquiryDocumentaryController/selectMerchandiserDaily', // 查询跟单员日报
	'/background/web/inquiryDocumentaryController/importInquiry', // 询盘批量导入
	'/background/web/inquiryDocumentaryController/deleteCallRecording', // 删除录音
	'/background/web/inquiryDocumentaryController/isByUserChannelName', // 判断当前用户是否只有一个渠道一个分销
	'/background/web/inquiryDocumentaryController/updateConsultingInfo', // 修改咨询详情
	'/background/web/inquiryDocumentaryController/updateConsultingEvaluate', // 修改咨询评价
	'/background/web/inquiryDocumentaryController/inquiryRepurchase', // 询盘复购
	'/background/web/inquiryDocumentaryController/consultingEvaluationReport', // 查询咨询评价报表
	'/background/web/inquiryDocumentaryController/selectIndustryProficiency', // 查询行业熟练度报表
	'/background/web/inquiryDocumentaryController/selectInquiryAllocationStatisticList', // 询盘分配跟踪统计
	'/background/web/inquiryDocumentaryController/updateListenedDurationByIdid', // 修改询盘听录音时长
	'/background/web/inquiryIndustryCaseSearchBoxSnapshotController/insertOrUpdateInquiryIndustryCaseSearchBoxSnapshot', // 新增或更新用户询盘中行业案例搜索框快照
	'/background/web/inquiryIndustryCaseSearchBoxSnapshotController/selectInquiryIndustryCaseSearchBoxSnapshot', // 查询询盘中行业案例搜索框快照

	// 推广素材
	'/background/web/inquiryDocumentaryController/fetchInquiryPromotionalMaterialConfiguration', // 素材配置拉取
	'/background/web/inquiryDocumentaryController/updateInquiryPromotionalMaterialConfiguration', // 素材配置更新
	// 视频号
	'/background/web/inquiryDocumentaryController/fetchInquiryPromotionalVidMap', // 拉取视频号来源映射配置
	'/background/web/inquiryDocumentaryController/updateInquiryPromotionalVidMap', // 更新视频号来源映射配置

	// 流量报工
	'/background/web/traffic/selectReportTableReport', // 查询_流量汇报表
	'/background/web/traffic/selectAdminVideoDayCount', // 查询_流量报工页
	'/background/web/traffic/updatePointByDayCount', // 添加_今日数据数据
	'/background/web/traffic/updateAdminVideoDayCount', // 添加_报工数据
	'/background/web/adminUserCategoryController/selectAdminUserCategory', // 查询类别
	'/background/web/adminUserCategoryController/addAdminUserCategory', // 添加人员类别数据
	'/background/web/adminUserCategoryController/delateAdminUserCategory', // 删除人员类型

	// 公海
	'/background/web/inquiryDocumentaryController/useHighSeas', // 释放公海
	'/background/web/inquiryDocumentaryController/selectRuleSize', // 查询公海数量
	'/background/web/inquiryDocumentaryController/addSeaItemVisit', // 公海询盘访问记录（每点击一个公海询盘发送一次该请求用于跟踪）
	'/background/web/inquiryDocumentaryController/basicInfoUpdateForSharedSeaItem', // 询盘基本信息修改, 公海询盘领取专用
	'/background/web/inquiryDocumentaryController/selectInquiryIntoHighseasUserLoginDetails', // 查看系统自动调入公海用户详情, 公海询盘领取专用
	'/background/web/inquiryDocumentaryController/updateDeadSeasFlagByIdid', // 手动标记询盘进入死海

	// 跟单记录
	'/background/web/documentaryRecordsController/addDocumentaryRecords', // 发布跟单
	'/background/web/documentaryRecordsController/updateNextPlan', // 修改下一步跟单计划
	'/background/web/documentaryRecordsController/selectNextPlan', // 查询下一步计划
	'/background/web/documentaryRecordsController/selectInquiryDocumentary', // 跟单日报-根据跟单id查询对应的询盘记录

	// 渠道
	'/background/web/TeamworkInquiryDocumentaryController/selectTeamworkUser', // 查询渠道所属用户
	'/background/web/TeamworkInquiryDocumentaryController/addOrUpdateJointPerson', // 添加或修改对接人
	'/background/web/TeamworkInquiryDocumentaryController/importTeamWork', // 导入渠道excel表格
	'/background/web/TeamworkInquiryDocumentaryController/selectCurrentUserHoster', // 查询用户所属托管方信息

	// 客户备案
	'/background/web/clientKeepRecordController/selectUserTeamwork', // 查询用户所属渠道
	'/background/web/clientKeepRecordController/addKeepRecordClient', // 添加备案客户
	'/background/web/clientKeepRecordController/checkBusinessName', // 检验客户工商注册名称
	'/background/web/clientKeepRecordController/selectKeepRecordClient', // 查询备案客户
	'/background/web/clientKeepRecordController/deleteKeepRecordClient', // 删除备案客户
	'/background/web/clientKeepRecordController/applyLengthenSpread', // 申请延长展期

	// 展期审核
	'/background/web/exhibitionPeriodCheckController/selectSpreadCheck', // 查询展期审核
	'/background/web/exhibitionPeriodCheckController/approvalSpreadCheck', // 审核展期审核
	'/background/web/exhibitionPeriodCheckController/approvalSpreadCheckBatch', // 批量审核展期审核

	// 成交保护
	'/background/web/clientBargainProtectController/selectBargainProtect', // 查询客户成交保护
	'/background/web/clientBargainProtectController/approvalBargainProtect', // 客户成交保护审核

	// 托管询盘
	'/background/web/TeamworkInquiryDocumentaryController/selectTeamworkInquiryDocumentaryInfo', // 托管询盘-查询合作方询盘信息
	'/background/web/TeamworkInquiryDocumentaryController/selectTeamworkOperationFlow', // 托管询盘-查询合作业务河流信息
	'/background/web/TeamworkInquiryDocumentaryController/export', // 托管询盘-导出
	'/background/web/TeamworkInquiryDocumentaryController/addTeamWork', // 托管询盘-添加合作方信息
	'/background/web/TeamworkInquiryDocumentaryController/deleteTeamWork', // 托管询盘-删除合作方信息
	'/background/web/TeamworkInquiryDocumentaryController/selectAllTeamWork', // 托管询盘-查询出所有合作方
	'/background/web/TeamworkInquiryDocumentaryController/selectAllTeamWorkName', // 托管询盘-查询所有合作方姓名
	'/background/web/TeamworkInquiryDocumentaryController/updateTeamWork', // 托管询盘-更新合作方信息
];

// 重名函数映射
const apiNameMap = {
	'/background/web/TeamworkInquiryDocumentaryController/export': 'exportCooperInquiry',
};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['selectRuleSize'].includes(urlName)) {
		timeout = 60000;
	}
	return { urlName, url, timeout };
});

export default apiList;
