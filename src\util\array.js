/**
 * 数组去重
 * @returns {Array}
 */
Array.prototype.distinct = function () {
	const arr = this,
		result = [],
		len = arr.length;
	arr.forEach(function (v, i, arr) {
		//这里利用map，filter方法也可以实现
		const bool = arr.indexOf(v, i + 1); //从传入参数的下一个索引值开始寻找是否存在重复
		if (bool === -1) {
			result.push(v);
		}
	});
	return result;
};

export function SetList() {
	return new Set();
}

/**
 *  数组存在就删除，不存在就添加的函数
 * @param setList 是数组
 * @param item 数据
 * @returns {*}
 */
export function distinctSet(arrayList, item) {
	const arrSet = new Set(arrayList);

	if (arrSet.length == 0) {
		arrSet.add(item);
	} else {
		if (arrSet.has(item)) {
			arrSet.delete(item);
		} else {
			arrSet.add(item);
		}
	}
	return Array.from(arrSet);
}
/**
 * 添加数组的元素并去重
 * @param arrayList
 * @param item
 * @returns {Array}
 */
export function distinctAddSet(arrayList, item) {
	const arrSet = new Set(arrayList);
	arrSet.add(item);
	return distinctArray(Array.from(arrSet));
}

/**
 * 删除数组的元素并去重
 * @param arrayList
 * @param item
 * @returns {Array}
 */
export function distinctDeleteSet(arrayList, item) {
	const arrSet = new Set(arrayList);
	if (arrSet.length != 0) {
		if (arrSet.has(item)) {
			arrSet.delete(item);
		}
	}
	return distinctArray(Array.from(arrSet));
}

/**
 *  数组去重
 * @param arrayList 数组
 * @returns {Array}
 */
export function distinctArray(arrayList) {
	return Array.from(new Set(arrayList));
}

/**
 * 根据对象的属性id，数组去重
 * @param arr 数组
 * @returns {Array}
 */
export function accordingIdUnique(arr) {
	const res = new Map();
	return arr.filter(item => !res.has(item.id) && res.set(item.id, 1));
}
