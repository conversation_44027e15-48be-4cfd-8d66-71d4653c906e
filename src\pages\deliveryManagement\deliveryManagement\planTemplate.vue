<template>
	<div id="planTemplate">
		<BaseLayout>
			<template #header>
				<!-- 带建议日期 -->
				<label class="search-label">项目类型</label>
				<el-select
					size="small"
					v-model="searchForm.projectType"
					placeholder="请选择项目类型"
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option v-for="item in projectTypeOptions" :key="item.id" :label="item.label" :value="item.id"> </el-option>
				</el-select>

				<!-- 模糊查询 -->
				<el-input
					class="searchBox"
					size="small"
					v-model="searchForm.query"
					placeholder="请输入模板名称"
					@input="queryTableData(1)"
					clearable
				></el-input>

				<div class="ml-auto">
					<el-button type="text" class="icon-third-bt_newdoc" @click="openDialog(null)">添加</el-button>
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
				</div>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar"></div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="['startTime', 'endTime', 'modifyTime'].includes(item.colNo)"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 项目类型 -->
							<Tooltips
								v-else-if="item.colNo == 'projectType'"
								:cont-str="projectTypeMap[scope.row[item.colNo]]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 编号 -->
							<Tooltips
								v-else-if="item.colNo == 'planNo'"
								class="hover-green green"
								@click.native="openDialog(scope.row, '计划详情')"
								:cont-str="scope.row[item.colNo]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 数组 -->
							<Tooltips
								v-else-if="item.colNo == 'planTaskVOS'"
								:cont-str="getArrayStr(scope.row[item.colNo])"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<u-table-column label="" width="150" align="right">
						<template slot-scope="scope">
							<el-button type="text" class="el-icon-edit-outline" @click="openDialog(scope.row)"></el-button>
							<el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>

		<!-- 计划模板弹窗  -->
		<el-dialog
			width="666px"
			:visible.sync="dialogEdit"
			:close-on-click-modal="false"
			:append-to-body="false"
			@close="closeDialog"
		>
			<span slot="title">{{ dialogType }}计划模板</span>
			<el-form :model="editForm" :rules="formRules" label-width="110px" label-position="left" @submit.native.prevent>
				<el-form-item label="项目类型" prop="projectType">
					<el-select v-model="editForm.projectType" placeholder="请选择项目类型" clearable filterable @change="queryTableData(1)">
						<el-option v-for="item in projectTypeOptions" :key="item.id" :label="item.label" :value="item.id"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="计划模板名称" prop="planTemplate">
					<el-input v-model="editForm.planTemplate" placeholder="请输入计划模板名称" clearable></el-input>
				</el-form-item>
				<div class="table-wrapper dialog-table">
					<u-table class="table-main" :height="300" :data="editForm.planTaskVOS">
						<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
						<u-table-column label="计划任务" width="" align="left">
							<template slot-scope="scope">
								<el-input v-model="scope.row.planTask" placeholder="请输入计划任务" size="mini" clearable></el-input>
							</template>
						</u-table-column>
						<u-table-column label="所需天数" width="120" align="left">
							<template slot-scope="scope">
								<el-input v-model="scope.row.needDay" placeholder="所需天数" size="mini" clearable></el-input>
							</template>
						</u-table-column>
						<u-table-column label="行业案例" width="100" align="center">
							<template slot-scope="scope">
								<el-checkbox v-model="scope.row.isIndustryCase" :true-label="1" :false-label="0"></el-checkbox>
							</template>
						</u-table-column>
						<u-table-column label="" width="110" align="right">
							<template #header>
								<el-button type="text" class="el-icon-plus" @click="addRow(editForm.planTaskVOS.length)">添加一行</el-button>
							</template>
							<template slot-scope="scope">
								<el-button type="text" class="el-icon-close" @click="delRow(scope.$index)"></el-button>
								<el-button type="text" class="el-icon-plus" @click="addRow(scope.$index)"></el-button>
							</template>
						</u-table-column>
					</u-table>
				</div>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEdit">保 存</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import { projectTypeOptions, projectTypeMap } from '@/assets/js/contractSource';
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'planTemplate', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			dialogEdit: false,
			dialogType: '',
			editForm: {
				planNo: '',
				planTaskVOS: [],
				planTemplate: '',
				projectType: '',
			},
			formRules: {
				projectType: [{ required: true, message: '请输入项目类型', trigger: 'blur' }],
				planTemplate: [{ required: true, message: '请输入计划模板名称', trigger: 'blur' }],
			},
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '项目类型', colNo: 'projectType', align: 'left', width: '120' },
				{ colName: '计划编号', colNo: 'planNo', align: 'left', width: '120' },
				{ colName: '计划模板名称', colNo: 'planTemplate', align: 'left', width: '250' },
				{ colName: '计划任务模板', colNo: 'planTaskVOS', align: 'left', width: '' },
			],
			// 选择器数据 - 车间/部门/类型等...
			projectTypeOptions,
			projectTypeMap,
			// 查询表单
			searchForm: {
				did: '',
				query: '',
				projectType: '',
				// 其他...
			},
			// 日期相关
			dateSelectObj: {
				endDate: '',
				startDate: '',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 将数组转为字符串
		getArrayStr(arr) {
			if (arr && arr.length > 0) {
				// ${item.isIndustryCase ? '/行业案例' : ''}
				return arr.map(item => `${item.planTask} (${item.needDay}天)`).join(',');
			} else {
				return '';
			}
		},
		// 删除行
		deleteRow(row) {
			const ptid = row.planTaskVOS.map(item => item.ptid);
			this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'deletePlanTemplate';
					try {
						const res = await this.$axios[API](JSON.stringify({ ptid }));
						if (res.data.success) {
							this.queryTableData(1);
							this.$succ(res.data.message);
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		addRow(index) {
			this.editForm.planTaskVOS.splice(index + 1, 0, {
				needDay: 1,
				planTask: '',
				ptid: '',
			});
		},
		delRow(index) {
			this.editForm.planTaskVOS.splice(index, 1);
		},
		openDialog(row) {
			if (row) {
				this.editForm = { ...this.editForm, ..._.deepClone(row) };
			}
			this.dialogType = row ? '编辑' : '添加';
			this.dialogEdit = true;
		},
		closeDialog() {
			this.editForm = _.resetValues(this.editForm);
			this.dialogEdit = false;
		},
		async saveEdit() {
			if (this.editForm.planTaskVOS.length == 0) {
				this.$message.warning('请添加计划任务模板');
				return;
			}

			let isEmpty = false; //  判断空内容
			this.editForm.planTaskVOS?.forEach((item, index) => {
				if (item.needDay == 0 || item.needDay == '' || item.planTask == '') {
					isEmpty = true;
				}
				item.serial = index + 1; //  排序添加 serial
			});

			if (isEmpty) {
				this.$message.warning('请完善计划任务模板(天数或任务名称)');
				return;
			}

			const API = this.dialogType == '编辑' ? 'updatePlanTemplate' : 'addPlanTemplate';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.editForm }));
				if (res.data.success) {
					this.queryTableData();
					this.$succ(res.data.message);
					this.dialogEdit = false;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				
				console.error(`${API} |` + error);
			}
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectPlanTemplate'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.dateSelectObj,
				...this.searchForm,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
	},
};
</script>

<style lang="scss" scoped>
#planTemplate {
	width: 100%;
	overflow: hidden;
	position: relative;
	.dialog-table {
		.table-main {
			height: 300px !important;
		}
	}
}
</style>
