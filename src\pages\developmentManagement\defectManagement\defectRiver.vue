<template>
	<div id="resultsEvaluation">
		<!-- 数据导出弹窗 -->
		<!-- <ExportTable ref="ExportTable" /> -->
		<!-- <taskList ref="taskList" :parentType="'result'" /> -->

		<BaseLayout>
			<template #header>
				<span class="search-label">年度</span>
				<el-date-picker
					size="small"
					class="w-150"
					v-model="selectTime"
					:default-value="selectTime"
					type="year"
					value-format="timestamp"
					format="yyyy 年"
					placeholder="请选择年份"
					:clearable="false"
					@change="changeDateSelect"
				>
				</el-date-picker>
				<el-radio-group v-model="apiType" @change="queryTableData(1)">
					<el-radio :label="1">责任人视图</el-radio>
					<el-radio :label="2">缺陷等级视图</el-radio>
					<el-radio :label="3">缺陷率视图</el-radio>
				</el-radio-group>
				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-toolbar"> </div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					@sort-change="sortChange"
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column label="序号" width="50" align="center" type="index"> </u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						resizable
						sortable="custom"
					>
						<template slot-scope="scope">
							<!-- <div
								class="hover-green"
								@click="openList(item.colNo, scope.row)"
								v-if="item.colNo != 'name' && item.colNo != '12' && scope.row[item.colNo]"
							>
								{{ scope.row[item.colNo] }}
							</div>
							<Tooltips
								v-else-if="item.colNo == '12' && scope.row[item.colNo]"
								class="hover-green"
								@click.native="openList(item.colNo, scope.row)"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/> -->
							<Tooltips
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import ExportTable from '@/components/ExportTable'; //导出组件
// import taskList from './developmentWorkTaskList.vue';
export default {
	name: 'resultsEvaluation',
	// components: { ExportTable, taskList },
	props: {},
	data() {
		return {
			//日期相关
			selectTime: new Date(),
			apiType: 1,
			startTime: '',
			endTime: '',
			searchForm: {
				type: 1,
				statusList: [4],
				projectTurnTest: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableColumn: [
				{ colName: '责任人', colNo: 'name', align: 'left', width: 100 },
				{ colName: '一月', colNo: '0', align: 'right' },
				{ colName: '二月', colNo: '1', align: 'right' },
				{ colName: '三月', colNo: '2', align: 'right' },
				{ colName: '四月', colNo: '3', align: 'right' },
				{ colName: '五月', colNo: '4', align: 'right' },
				{ colName: '六月', colNo: '5', align: 'right' },
				{ colName: '七月', colNo: '6', align: 'right' },
				{ colName: '八月', colNo: '7', align: 'right' },
				{ colName: '九月', colNo: '8', align: 'right' },
				{ colName: '十月', colNo: '9', align: 'right' },
				{ colName: '十一月', colNo: '10', align: 'right' },
				{ colName: '十二月', colNo: '11', align: 'right' },
				{ colName: '合计', colNo: '12', align: 'right' },
			],
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['menuTitle']) },
	// 监控data中的数据变化
	watch: {
		apiType(newVal) {
			const nameMap = {
				1: '责任人',
				2: '缺陷等级',
				3: '缺陷率',
			};
			this.tableColumn[0].colName = nameMap[newVal];
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		const nowYear = new Date().getFullYear();
		this.changeDateSelect(new Date(nowYear, '0', '1').getTime());
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 交付清单
		openList(monthIndex, row) {
			return;
		},

		// 获取业务河流数据
		queryTableData: _.debounce(function (type) {
			const API = {
				1: 'dutyView',
				2: 'bugLevelView',
				3: 'bugRateView',
			}[this.apiType]; //接口
			this.tableData = [];
			const str = JSON.stringify({
				endDate: this.endTime,
				startDate: this.startTime,
			});
			this.$axios[API](str)
				.then(res => {
					if (res.data.success) {
						res.data.data?.map(item => {
							// item.total = item.monthData?.reduce((a, b) => {
							// 	return _.accAdd(a, b, 0);
							// });
							item.monthData.map((monthItem, index) => {
								item[index] = _.accAdd(monthItem, 0, 0);
							});
						});
						this.tableData = res.data.data?.sort((a, b) => b['12'] - a['12']);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');

						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('achievementAssessment |' + error);
				});
		}),

		//数据导出
		openExport: _.debounce(function () {
			const PROPS = {
				DATA: JSON.stringify({
					endTime: this.endTime,
					startTime: this.startTime,
					apiType: this.apiType,
					pageNum: 1,
					pageSize: 100,
				}), //接口参数
				API: 'achievementAssessmentExport', //导出接口
				downloadData: '成果评价导出', //数据报表参数（后台确认字段downloadData）
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),

		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = []; // 合计
			columns.forEach((column, columnIndex) => {
				if (columnIndex === 0) {
					means.push('合计');
				} else if (columnIndex === 1) {
					means.push('');
				} else {
					const values = data?.map(item => {
						return Number(item[column.property]?.amount || item[column.property]);
					});

					// 合计
					if (!values.every(value => isNaN(value))) {
						means[columnIndex] = values.reduce((prev, curr) => {
							const value = Number(curr);
							// console.log(value);
							if (!isNaN(value)) {
								return _.accAdd(prev, curr, 0);
							} else {
								return prev;
							}
						}, 0);
						/* 
								class="hover-green"
									on-click={e => {
										e.stopPropagation();
										this.openList(columnIndex - 1, null);
							}} */
						if (!isNaN(means[columnIndex])) {
							means[columnIndex] = <span>{means[columnIndex]}</span>;
						} else {
							means[columnIndex] = '';
						}
					} else {
						means[columnIndex] = '';
					}
				}
			});
			// console.log(columns);
			// 返回一个二维数组的表尾合计(不要平均值，你就不要在数组中添加)
			return [means];
		},

		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		// 时间选择
		changeDateSelect(date) {
			const year = new Date(date).getFullYear();
			this.startTime = new Date(year, '0', '1').getTime();
			this.endTime = new Date(year + 1, '0', '1').getTime() - 1;
			// console.log(date);
			this.queryTableData();
		},
	},
};
</script>

<style lang="scss" scoped>
#resultsEvaluation {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
