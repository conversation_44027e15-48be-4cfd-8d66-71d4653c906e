/* 
  全局公共样式：文本样式、颜色、内外边距、布局等等（类似于tailwind css原子类在class上写样式）
  与global.scss不同的是这里的样式全局通用没有固定场景，大部分由SASS指令生成，且不需要嵌套固定的class等
  折叠所有区域代码的快捷键：ctrl+k, ctrl+0;
	展开所有折叠区域代码的快捷键：ctrl +k, ctrl+J;
*/

/***********  文本（字体大小等）、颜色（背景颜色等 ***********/
@import './element-variables.scss';

/* 边框boder 使用： class="border-right" */
$border-styles: ('border', 'border-top', 'border-left', 'border-bottom', 'border-right');

@each $style in $border-styles {
	.#{$style} {
		#{$style}: 1px solid #d7d7d7 !important;
	}
}

/* 
	常见颜色和背景色 
	使用： class="white bg-red "   
  添加规则：以颜色英文名命名，同名时按照深浅区分，深：deep-red 浅：light-red
  */
$color-values: (
	'primary': $--color-primary,
	'white': #fff,
	'gray': #f2f2f2,
	'pink': #f14b83,
	'purple': #9c27b0,
	'blue': #2196f3,
	'green': #1e9d6f,
	'light-green': #28d094,
	'selected-green': #e9f5f1, // 选中绿色
	'yellow': #fdd835,
	'orange': #ff9800,
	'brown': #795548,
	'black': #000,
	'red': #f56c6c,

	// 其他项目里常用颜色
	'color-555': #555,
	'color-666': #666,
	'color-999': #999,
);
@each $value, $color in $color-values {
	.bg-#{$value} {
		color: #fff;
		background-color: $color !important;
	}
	
	.#{$value} {
		color: $color !important;
	}

	.border-color-#{$value} {
		border: 1px solid  $color !important;
	}

}
/* 
	常见字体大小 
	使用： class="fs-12 "   
  添加规则：+2
*/
$font-values: (
	'fs-12': 12px,
	'fs-14': 14px,
	'fs-16': 16px,
	'fs-18': 18px,
	'fs-24': 24px,
	'fs-48': 48px,
);

@each $value, $font in $font-values {
	.#{$value} {
		font-size: $font !important;
	}
}


/************* 宽度高度、内外边距 *************/

/* 外边距、内边距全局样式 ，使用： class="mr10" */
@for $i from 0 through 60 {
	.m#{$i} {
		margin: #{$i}px !important;
	}
	.p#{$i} {
		padding: #{$i}px !important;
	}
	.mt#{$i} {
		margin-top: #{$i}px !important;
	}
	.mr#{$i} {
		margin-right: #{$i}px !important;
	}
	.mb#{$i} {
		margin-bottom: #{$i}px !important;
	}
	.ml#{$i} {
		margin-left: #{$i}px !important;
	}
	.pt#{$i} {
		padding-top: #{$i}px !important;
	}
	.pr#{$i} {
		padding-right: #{$i}px !important;
	}
	.pb#{$i} {
		padding-bottom: #{$i}px !important;
	}
	.pl#{$i} {
		padding-left: #{$i}px !important;
	}
}

/* 宽度 width(vw) 使用： class="vw10" */
@for $i from 0 through 30 {
	.vw#{$i} {
		width: #{$i}vw !important;
	}
}

/* 宽度 高度百分比 （5的倍数生效 如W10,W15,W30），使用： class="W100" */
@for $i from 1 through 20 {
	.W#{$i * 5 } {
		width: $i * 5% !important;
	}
	.H#{$i * 5 } {
		height: $i * 5% !important;
	}
}

/* text-align: left/center/right 使用： class="text-left" */
$alignments: (left, center, right);

@each $alignment in $alignments {
	.text-#{$alignment} {
		text-align: $alignment !important;
	}
}

/* 设置宽度/高度（最高宽/高）px 使用： class="w-100"  添加值和对应的像素数 （按需添加，以10的倍数设置最佳）*/
$px-values: (0,10,20, 25,30,35, 40, 50,60, 80, 100, 120, 150, 180, 200,220, 250, 300, 350, 400, 450, 500);

@each $value in $px-values {
	.w-#{$value} {
		width: #{$value}px !important;
	}
	.h-#{$value} {
		height: #{$value}px !important;
	}
	.min-w-#{$value} {
		min-width: #{$value}px !important;
	}
	.min-h-#{$value} {
		min-height: #{$value}px !important;
	}
	.max-w-#{$value} {
		max-width: #{$value}px !important;
	}
	.max-h-#{$value} {
		max-height: #{$value}px !important;
	}

	.z-index-#{$value} {
		z-index: #{$value} !important;
	}
	.sticky-top-#{$value} {
		position: sticky;
		top: #{-$value}px;
	}
	.sticky-bottom-#{$value} {
		position: sticky;
		bottom: #{-$value}px;
	}
}

/* 超出滚动或隐藏 使用： class="overflow-y-hidden overflow-y-auto" */
$overflow-styles: ('overflow', 'overflow-x', 'overflow-y');

@each $style in $overflow-styles {
	.#{$style}-auto {
		#{$style}: auto !important;
	}
	.#{$style}-hidden {
		#{$style}: hidden !important;
	}
}



/* 
	描述：absolute 定位
	使用：@include abs(10px, 10px, 5px, 15px);
*/
// @mixin abs($top: auto, $right: auto, $bottom: auto, $left: auto) {
//   top: $top;
//   right: $right;
//   bottom: $bottom;
//   left: $left;
//   position: absolute;
// }


/* 
	间距设置  使用： class="gap-10 "   
*/
$gap-values: (5, 10, 15, 20, 25, 30, );
@each $value in $gap-values {
	.gap-#{$value} {
		gap: #{$value}px;
	}
}


/* =======以上为SASS指令生成，以下为普通样式 ======= */

/* 
  布局 display、absolute、clearfix 
*/

.inline-block {
	display: inline-block;
}
.block {
	display: block;
}

/* 清除浮动 */
.clearfix {
	&:after {
		visibility: hidden;
		display: block;
		font-size: 0;
		content: ' ';
		clear: both;
		height: 0;
	}
}

/* 字体加粗 */
.bolder {
	font-weight: 600 !important;
}
.normal,
.thin {
	font-weight: 400 !important;
}

/* 光标(小手) */
.pointer {
	cursor: pointer !important;
}

/* 鼠标悬浮绿色 */
.hover-green {
	// color: #1e9d6f;
	&:hover {
		color: #28d094 !important;
		text-decoration: underline;
		cursor: pointer;
	}
}

/* 本文超出省略 */
.ellipsis,
.tdNormal {
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}
/* 本文超出省略2 */
.ellipsis2,
.tdTwoNormal {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: normal !important;
	word-break: break-all !important;
	display: -webkit-box !important;
	-webkit-box-orient: vertical !important;
	-webkit-line-clamp: 2 !important; /* 这里是超出几行省略 */
}

/* 表单必填信息红星 */
.spanRequiredR::after,
.label-required::after {
	content: '*';
	color: #f56c6c;
	margin-left: 5px;
	font-weight: bolder;
}

