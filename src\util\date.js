/**
 * 根据时间戳获取当前月最后一天的时间戳（后续优化将舍弃）
 * @param Number
 * @returns Number
 */
export function getNowMonthEndDay(timestamp) {
	if (!timestamp) return '';
	const currentTime = new Date(timestamp);
	const lastday = new Date(currentTime.getFullYear(), currentTime.getMonth() + 1, 0).getDate();
	const endDay = timestamp + lastday * 1000 * 60 * 60 * 24 - 1;
	return endDay;
}

/**
 * 根据时间戳获取当前年的第一天和最后一天（后续优化将舍弃）
 * @param Number/Date
 * @returns Array
 */
export function getYearFirstDayAndLastDay(timestamp = new Date()) {
	const currentTime = new Date(timestamp);
	const firstDay = new Date(currentTime.getFullYear(), 0, 1).getTime();
	const endDay = new Date(currentTime.getFullYear() + 1, 0, 1).getTime() - 1;
	return [firstDay, endDay];
}

/**
 * 获取本周第一天（后续优化将舍弃）
 * @param string
 * @returns string
 */

export function getNowWeekStart() {
	function formatNumber(n) {
		n = n.toString();
		return n[1] ? n : '0' + n;
	}

	const date = new Date();
	date.setDate(date.getDate() - date.getDay() + 1);
	const year = date.getFullYear();
	const month = date.getMonth() + 1;
	const day = date.getDate();

	return [year, month, day].map(formatNumber).join('-');
}
