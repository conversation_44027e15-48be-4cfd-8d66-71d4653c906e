/* 
自定义指令  - 防抖
<el-button v-debounce:click="{ handler: handleClick, delay: 500 }">Click me</el-button>
<el-select v-debounce:change="{ handler: handleChange, delay: 500 }">
      <!-- el-select 的选项 -->
</el-select>
import debounce from '@/directive/debounce.js'
directives: { debounce },
*/
const debounce = {
	bind: function (el, binding) {
		let timeoutId;
		const delay = binding.value.delay || 300; // 默认防抖时长为 300 毫秒

		const event = binding.arg || "click"; // 默认防抖事件为 click

		el.addEventListener(event, function () {
			clearTimeout(timeoutId);
			timeoutId = setTimeout(() => {
				binding.value.handler();
			}, delay);
		});
	},
	unbind: function (el, binding) {
		const event = binding.arg || "click"; // 默认防抖事件为 click

		el.removeEventListener(event);
	}
};
export default debounce;
