<template>
	<div class="DemandManagementDetail" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}需求</span>
				<div class="flex-align-center">
					<el-button v-show="detailForm.dsid" type="text" class="el-icon-refresh-right" @click="queryDetail(detailForm)">
						刷新
					</el-button>
					<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
				</div>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content">
				<div class="flex">
					<!-- 左边  -->
					<div class="W45 H100 flex-column">
						<div class="H100 flex-column">
							<div class="detail-content-title mt10 mb10 gap-10">
								<span class="min-w-80">需求文档 </span>
								<Tooltips
									class="inline-block max-w-300 fs-12 color-999"
									:cont-str="`${detailForm?.demandDocumentName ? `${detailForm?.demandDocumentName}` : ''}`"
									:cont-width="480"
								/>
								<div class="ml-auto"></div>

								<!-- 上传需求文档 -->
								<el-upload
									v-if="detailForm?.demandDocumentUrl"
									action=""
									accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx"
									:http-request="f => uploadFile(f, '需求文档')"
									:show-file-list="false"
								>
									<el-button type="text" class="el-icon-upload p0 m0" @click="uploadFile"> 更换需求文件 </el-button>
								</el-upload>

								<el-button type="text" class="el-icon-download p0 m0" @click="downloadFile"> 下载 </el-button>
							</div>
							<!-- 需求文档文件  -->
							<FilePreview
								v-if="detailForm?.demandDocumentUrl"
								ref="FilePreview"
								:url="detailForm?.demandDocumentUrl"
								:content="detailForm?.registeredBusinessName"
							/>
							<el-upload
								v-else
								class="upload-dragger"
								action=""
								accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx"
								:http-request="f => uploadFile(f, '需求文档')"
								:show-file-list="false"
								drag
							>
								<div class="flex-column gap-10">
									<div class="red el-icon-plus fs-18"> 请点击/拖拽上传需求文档</div>
									<div class="blue pl20 mr-auto fs-18" @click.stop="downloadTemplate('示例-三阳精密需求说明书20250312.docx')">
										下载模板
									</div>
								</div>
							</el-upload>
						</div>
					</div>
					<div class="W55 flex-column border-left pl10 ml10 pb60">
						<div class="flex-1 table-wrapper">
							<el-collapse v-model="activeName">
								<el-collapse-item v-for="(key, index) in getTableKeys" :key="key + detailForm.dsid + index" :name="Number(key)">
									<template slot="title">
										<div class="flex-align-center max-w-500">
											<p class="detail-content-title"> {{ tableMap[key].title }} </p>
										</div>
									</template>
									<div class="pl10 pr15">
										<component
											:is="detailForm.status == 1 ? 'DemandTableBase' : tableMap[key].component"
											:componentIndex="index"
											:userList="userList"
											:detailForm="detailForm"
											:tableData="detailForm?.selectDemandSupervisionDetailVOS"
											:tableInfo="tableMap[key]"
											:isTechLeader="isTechLeader"
											:isDevManager="isDevManager"
											:showCom="showCom"
											@openProject="$emit('openProject', $event)"
											@refresh="queryDetail($event)"
											@close="showCom = false"
										/>
									</div>
								</el-collapse-item>
							</el-collapse>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { debounce, dateFormat, jointString, deepClone, resetValues } from '@/util/tool';
import { mapGetters } from 'vuex';
import FilePreview from '@/components/FilePreview.vue';
import DemandTable from './DemandTable.vue';
import DemandTableBase from './DemandTableBase.vue'; // 需求信息(新增/编辑)
export default {
	name: 'DemandManagementDetail',
	components: {
		FilePreview,
		DemandTable,
		DemandTableBase,
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			isSave: false, // 是否保存（添加/修改）
			titleName: '',
			activeName: [1, 2, 3, 4, 5], //默认打开的折叠面板
			detailFormCopy: [],
			detailForm: {
				appointDevelopmentManagerName: '',
				contractNo: '',
				demandDocumentUrl: '',
				developmentManagerAcceptDate: '',
				developmentManagerAppointOpinion: '',
				projectCategory: '',
				registeredBusinessName: '',
				reviewDate: '',
				reviewOpinion: '',
				reviewerName: '',
				selectDemandSupervisionDetailVOS: [],
				signDate: '',
				signOpinion: '',
				signerName: '',
				submissionTime: '',
				submitterName: '',
				status: 1,
				testManagerName: '',
			},
		};
	},
	created() {},
	computed: {
		...mapGetters(['userInfos', 'userList']), //当前登录用户信息（含团队/菜单/权限等）
		// 获取表格的key
		getTableKeys() {
			const len = this.detailForm.status || 1;
			return Object.keys(this.tableMap).slice(0, len);
		},
		// 是否超级管理员
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
		// 是否产品总监（标签：产品总监）
		isTechManager() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '产品总监') || false;
		},
		// 研发领导或超级管理(显示研发工时和技术人天
		isTechLeader() {
			return this.isSuperAdmin || this.isTechManager;
		},
		// 是否是开发经理（标签：开发经理/开发经理/技术经理），含产品总监、超级管理
		isDevManager() {
			if (this.isTechLeader) return true;
			return (
				this.userInfos?.adminUserVO.adminUserLabelVOS?.some(
					item => item.remark == '开发经理' || item.remark == '研发经理' || item.remark == '技术经理',
				) || false
			);
		},

		// 表格映射映射
		tableMap() {
			const status = this.detailForm.status || 1;
			return {
				1: {
					title: '需求信息',
					component: 'DemandTable',
					summaryKeys: ['manDay'],
					tableColumn: [
						{ colName: '编号', colNo: 'number', align: 'left', width: '110' },
						{ colName: '需求描述', colNo: 'demandDescription', align: 'left', width: '' },
						{ colName: '需求类别', colNo: 'demandType', align: 'left', width: '120' },
						{ colName: '人天评估', colNo: 'manDay', align: status == 1 ? 'left' : 'right', width: '120' },
						{ colName: '客户要求完成日期', colNo: 'customerRequiredCompletionDate', align: 'center', width: '150' },
					],
				},
				2: {
					title: '技术评审',
					component: 'DemandTable',
					summaryKeys: ['javaDuration', 'webDuration', 'iosDuration', 'andriodDuration', 'totalDuration'],
					isTechLeader: this.isDevManager,
					tableColumn: [
						{ colName: '需求', colNo: 'demandDescription', align: 'left', width: '' },
						...(this.isDevManager
							? [
									{
										colName: 'JAVA/h',
										colNo: 'javaDuration',
										align: status == 2 ? 'left' : 'right',
										width: status == 2 ? '120' : '80',
									},
									{
										colName: 'WEB/h',
										colNo: 'webDuration',
										align: status == 2 ? 'left' : 'right',
										width: status == 2 ? '120' : '80',
									},
									{
										colName: 'iOS/h',
										colNo: 'iosDuration',
										align: status == 2 ? 'left' : 'right',
										width: status == 2 ? '120' : '80',
									},
									{
										colName: 'Andriod/h',
										colNo: 'andriodDuration',
										align: status == 2 ? 'left' : 'right',
										width: status == 2 ? '120' : '100',
									},
									// { colName: '测试/h', colNo: 'testDuration', align: 'left', width: '150' },
									{ colName: '共计/h', colNo: 'totalDuration', align: 'right', width: '100' },
								]
							: []),
					],
				},
				3: {
					title: '需求签署',
					component: 'DemandTable',
					tableColumn: [{ colName: '需求', colNo: 'demandDescription', align: 'left', width: '' }],
				},
				4: {
					title: '开发经理签署',
					component: 'DemandTable',
					tableColumn: [
						{ colName: '需求', colNo: 'demandDescription', align: 'left', width: '' },
						{ colName: '客户要求完成日期', colNo: 'customerRequiredCompletionDate', align: 'center', width: '150' },
						{ colName: '开发承诺完成日期', colNo: 'developmentPromiseCompletionDate', align: 'center', width: '150' },
					],
				},
			};
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.$emit('close');
				this.detailForm = resetValues(this.detailForm); //重置对象
				this.detailForm.status = 1; //重置默认状态
				this.isSave = false;
			}
		},
	},
	mounted() {},
	methods: {
		// 下载模板
		downloadTemplate(fileName) {
			const SERVER_URL =
				process.env.NODE_ENV == 'production' && window.location.origin ? window.location.origin : process.env.API_HOST; //	生产环境为当前服务器地址，开发环境为当前配置的地址
			window.open(SERVER_URL + '/template/' + fileName, '_self');
		},
		// 下载需求文档
		downloadFile() {
			window.open(this.detailForm.demandDocumentUrl, '_blank');
		},
		// 电子合同上传
		uploadFile(file, type) {
			const isLt50M = file.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				this.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', file.file);

			this.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						this.detailForm.demandDocumentName = res.data.data.fileName;
						this.detailForm.demandDocumentUrl = res.data.data.path;
						if (this.detailForm.dsid) {
							this.changeDocUrl();
							return;
						}
						this.$message.success('上传成功！');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.$message.warning(error.message);
				});
		},
		// 修改需求文档
		async changeDocUrl() {
			const API = 'updateDemandDocumentUrl';
			try {
				const res = await this.$axios[API](
					JSON.stringify({
						dsid: this.detailForm.dsid,
						fileName: this.detailForm.demandDocumentName,
						url: this.detailForm.demandDocumentUrl,
					}),
				);
				if (res.data.success) {
					this.isSave = true;
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		//查询详情数据
		queryDetail(data) {
			this.$axios
				.selectDemandSupervisionDetailByDsid(JSON.stringify({ dsid: data.dsid }))
				.then(res => {
					if (res.data.success) {
						this.detailForm = { ...data, ...res.data.data }; //浅拷贝对象达到动态变化的效果
						this.detailFormCopy = deepClone(this.detailForm);
						this.showCom = true;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectDemandSupervisionDetailByDsid |' + error);
				});
		},

		//显示弹窗
		async showDetailCom(item) {
			if (item && item?.dsid) {
				this.titleName = '编辑';
				this.queryDetail(item);
			} else {
				this.detailForm.status = 1;
				this.titleName = '新增';
				this.showCom = true;
				this.activeName = [1, 2, 3, 4, 5];
				// 默认添加一行
				this.detailForm.selectDemandSupervisionDetailVOS.push({
					demandDocumentName: '',
					demandDocumentUrl: '',
					demandDescription: '',
					demandType: '',
					manDay: '',
				});
			}
		},

		//日期format
		dateFormat,
		jointString,
	},
};
</script>
<style lang="scss">
.DemandManagementDetail {
	.el-collapse-item__header {
		height: 40px !important;
		line-height: 40px !important;
	}
	.el-collapse-item__content {
		padding-bottom: 0 !important;
	}

	.min-h-666 {
		min-height: 666px;
	}

	.upload-dragger {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		.el-upload {
			width: 100%;
			height: 100%;
		}
		.el-upload-dragger {
			width: 100%;
			height: 100%;
			min-height: 666px;
			padding: 20px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}
</style>
