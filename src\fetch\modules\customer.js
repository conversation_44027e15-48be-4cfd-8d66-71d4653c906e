/* 客户成功管理API */

const urlList = [
	/* 我的客户 */
	'/background/web/teamController/selectMyClient', //查询我的客户
	'/background/web/teamController/exportSelectMyClient', //我的客户导出
	'/background/web/lostCustomerAnalysis/addLostCustomerAnalysis', //添加失客分析
	'/background/web/lostCustomerAnalysis/deleteLostCustomerAnalysis', //删除团队失客分析
	'/background/web/lostCustomerAnalysis/selectLostCustomerAnalysis', //查询团队失客分析
	'/background/web/lostCustomerAnalysis/updateLostCustomerAnalysis', //修改团队失客分析

	/* 健康评分 */
	'/background/web/healthScopeController/healthScope', //健康评分
	'/background/web/healthScopeController/healthScopeException', //异常说明填写
	'/background/web/healthScopeController/exportHealthScope', //健康评分导出

	/* 到期管理 */
	'/background/web/teamController/selectMaturityManagement', //查询到期管理
	'/background/web/teamController/exportSelectMaturityManagement', //查看到期管理导出

	/* 行业案例 */
	'/background/web/DeliverManagementController/addIndustryCase', //新增行业案例
	'/background/web/DeliverManagementController/deleteIndustryCase', //删除行业案例
	'/background/web/DeliverManagementController/selectIndustryCases', //查看行业案例
	'/background/web/DeliverManagementController/updateIndustryCaseStatus', //修改行业案例发布状态
	'/background/web/DeliverManagementController/updateIndustryCases', //导出行业案例
	'/background/web/DeliverManagementController/updateIndustryCaseLikeStatus', //行业案例点赞状态修改
];

// 重名函数映射
const apiNameMap = {};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['exportSelectMaturityManagement', 'exportSelectMyClient'].includes(urlName)) {
		timeout = 60000;
	}

	return { urlName, url, timeout };
});

export default apiList;
