<template>
	<div id="BoxList">
		<el-tabs v-model="activeTab">
			<el-tab-pane label="安灯手环盒子清单" name="BoxList">
				<BaseLayout>
					<template #header>
						<el-input
							class="searchBox"
							size="small"
							clearable
							v-model="queryStr"
							placeholder="主机地址或注册号"
							@input="queryTableData(1)"
						></el-input>
						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<el-button type="text" class="icon-third-bt_newdoc" @click="openDialogAdd">添加</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column label="主机地址" prop="mac" align="center" sortable="custom"></u-table-column>
							<u-table-column label="注册包" prop="register" sortable="custom"></u-table-column>
							<u-table-column label="创建时间" prop="createTime" align="center" sortable="custom">
								<template slot-scope="scope">
									<div>
										{{ dateFormat(scope.row.createTime, 'line') }}
									</div>
								</template>
							</u-table-column>
							<u-table-column label="状态" prop="status" align="center" sortable="custom">
								<template slot-scope="scope">
									<div>
										{{ scope.row.status == 1 ? '在线' : scope.row.status == 0 ? '离线' : '' }}
									</div>
								</template>
							</u-table-column>
							<u-table-column label="团队姓名" prop="teamName" align="left" sortable="custom"></u-table-column>
							<u-table-column label=" " align="right">
								<template slot-scope="scope">
									<el-button type="text" @click="openDialogUpdate(scope.row)">修改</el-button>
									<el-button type="text" @click="deleteData(scope.row)">删除</el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
		<el-dialog :title="title == 'Add' ? '添加' : '修改'" :visible.sync="dialogFormVisible" @close="close" width="30%">
			<el-form :model="form" label-width="4vw" label-position="left">
				<el-form-item label="主机地址">
					<el-input v-model="form.mac" placeholder="主机地址" size="small"></el-input>
				</el-form-item>
				<el-form-item label="注册包">
					<el-input v-model="form.register" placeholder="注册包" size="small"></el-input>
				</el-form-item>
				<el-form-item label="团队">
					<el-autocomplete
						:debounce="500"
						:fetch-suggestions="queryName"
						placeholder="请选择团队"
						@select="choice"
						width="100%"
						v-model="form.teamName"
					>
					</el-autocomplete>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button @click="editData">确 定</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'BoxList',
	data() {
		return {
			tableSort: { prop: '', order: '' }, //表格排序状态
			queryStr: '',
			choiceData: [],
			options: [],
			title: '',
			dialogFormVisible: false,
			form: {
				mac: '',
				register: '',
				tid: '',
				abbId: '',
				teamName: '',
			},
			rowData: {},
			activeTab: 'BoxList',
			tableData: [],
			tableHeight: 630,
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		editData() {
			if (this.title == 'Add') {
				const str = JSON.stringify({
					mac: this.form.mac,
					register: this.form.register,
					tid: this.form.tid,
				});
				this.$axios
					.insertAndonBraceletBox(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.queryTableData();
							this.dialogFormVisible = false;
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						this.tableData = [];
						console.log('insertAndonBraceletBox |' + error);
					});
			} else {
				const str = JSON.stringify({
					abbId: this.form.abbId,
					mac: this.form.mac,
					register: this.form.register,
					tid: this.form.tid,
				});
				this.$axios
					.updateAndonBraceletBox(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.queryTableData();
							this.dialogFormVisible = false;
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						this.tableData = [];
						console.log('updateAndonBraceletBox |' + error);
					});
			}
		},
		queryName(queryStr, cb) {
			let unitList = [];
			const str = JSON.stringify({
				pageNum: 1,
				pageSize: 20,
				version: [1, 2, 4],
				statusList: [0, 3, 6],
				query: this.form.teamName,
			});
			this.$axios
				.selectTeam(str)
				.then(res => {
					if (res.data.success) {
						if (res.data.data) {
							this.choiceData = res.data.data;
							res.data.data.forEach(item => {
								unitList.push({
									value: item.teamName,
								});
							});

							const obj = {};
							unitList = unitList.reduce(function (item, next) {
								obj[next.value] ? '' : (obj[next.value] = true && item.push(next));
								return item;
							}, []);
							unitList = queryStr
								? unitList.filter(item => {
										return item.value.toLowerCase().indexOf(queryStr.toLowerCase()) > -1;
									})
								: unitList;
						} else {
							unitList = [];
						}
						cb(unitList);
					}
				})
				.catch(error => {
					console.log('selectTeam |' + error);
				});
		},
		choice(val) {
			console.log(val, this.choiceData);
			if (this.choiceData) {
				this.choiceData.forEach(item => {
					if (val.value == item.teamName) {
						this.form.tid = item.tid;
					}
				});
			}
		},
		close() {
			this.form.mac = '';
			this.form.register = '';
			this.form.tid = '';
			this.form.abbId = '';
			this.rowData = {};
			this.form.teamName = '';
			this.dialogFormVisible = false;
		},
		openDialogAdd() {
			this.title = 'Add';
			this.dialogFormVisible = true;
		},
		// 修改数据
		openDialogUpdate(row) {
			this.rowData = row;
			this.title = 'updata';
			this.form.mac = row.mac;
			this.form.register = row.register;
			this.form.tid = row.tid;
			this.form.abbId = row.abbId;
			this.form.teamName = row.teamName;

			this.dialogFormVisible = true;
		},

		deleteData(row) {
			this.$confirm('确定删除该数据？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					const str = JSON.stringify({
						abbId: row.abbId,
					});
					this.$axios
						.deleteAndonBraceletBox(str)
						.then(res => {
							if (res.data.success) {
								this.$succ('删除成功!');
								this.queryTableData();
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							
							console.log('deleteAndonBraceletBox |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const str = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				query: this.queryStr,
			});
			this.$axios
				.selectBraceletBoxList(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.tableData = [];
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.tableData = [];
					console.log('selectBraceletBoxList |' + error);
				});
		}),
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
	},
};
</script>

<style lang="scss">
#BoxList {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
