/* 样式文件入口 */
@charset "UTF-8";
@import './utils.scss';
@import './animation.scss';
@import './element-lightMES.scss';
@import './global.scss';
@import './responsive.scss';
@import './common.scss';
html,
body,
#app {
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0;
	overflow: hidden;
	// Fonts.css -- 跨平台中文字体解决方案 https://zenozeng.github.io/fonts.css/
	font-family: -apple-system, 'Noto Sans', 'Helvetica Neue', Helvetica, 'Nimbus Sans L', Arial, 'Liberation Sans', 'PingFang SC',
		'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Source Han Sans CN', 'Microsoft YaHei', 'Wenquanyi Micro Hei',
		'WenQuanYi Zen Hei', 'ST Heiti', <PERSON>m<PERSON><PERSON>, 'WenQuanYi Zen Hei Sharp', sans-serif;

	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
	text-rendering: optimizelegibility;


	// 特殊标签组件等，同步字体设置
	pre,
	.el-input__inner,
	.el-select__input,
	.el-select__input__inner,
	.el-input-number__input,
	.el-textarea__inner{
		font-family: -apple-system, 'Noto Sans', 'Helvetica Neue', Helvetica, 'Nimbus Sans L', Arial, 'Liberation Sans', 'PingFang SC',
			'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Source Han Sans CN', 'Microsoft YaHei', 'Wenquanyi Micro Hei',
			'WenQuanYi Zen Hei', 'ST Heiti', SimHei, 'WenQuanYi Zen Hei Sharp', sans-serif;
	}
}

/* 内外边距通常让各个浏览器样式的表现位置不同 */
// body, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, input, textarea, p, blockquote, th, td, hr, button, article, aside, details, figcaption, figure, footer, header, menu, nav, section {
//   margin: 0;
//   padding: 0;
// }

/* 重设 HTML5 标签, IE 需要在 js 中 createElement(TAG) */
article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section {
	display: block;
}

/* HTML5 媒体文件跟 img 保持一致 */
audio,
canvas,
video {
	display: inline-block;
}

// 标签保留空白符序列，但是正常地进行换行
pre {
	white-space: pre-wrap; /* 保留空白符序列，但是正常地进行换行 */
	word-wrap: break-word; /* 允许长单词换行到下一行 */
	word-break: break-all; /* 在任意字符间断行 */
}

*,
*:before,
*:after {
	box-sizing: inherit;
}

::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}
/*滚动条里面小方块*/
::-webkit-scrollbar-thumb {
  cursor: pointer;
  background-color: #d3d3d3;
  border-radius: 20px;
  box-shadow: inset 0 0 0 white;
}

::-webkit-scrollbar-thumb:hover {
  background: #a3a3a3; // 鼠标移入滑块变深
  box-shadow: inset 0 0 5px rgba(242, 242, 242, 0.5);
}
/*滚动条里面轨道*/
::-webkit-scrollbar-track {
  cursor: pointer;
  border-radius: 10px; // 轨道圆角
}
::-webkit-scrollbar-track:hover {
  background: #f0f0f0; // 鼠标移入滑块变深
}

/* 解决 h1 标签在 webkit 内核浏览器中文字大小失效问题 */
:-webkit-any(article, aside, nav, section) h1 {
	font-size: 2em;
}

input[type='number'] {
	appearance: textfield;
	-webkit-appearance: textfield;
	-moz-appearance: textfield;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
	-webkit-appearance: none;
	margin: 0;
}
