/* 
自定义指令  - 输入框整数
<input v-inputInteger></input>
import inputInteger from '@/directive/inputInteger.js'
directives: { inputInteger },
*/
const inputInteger = {
  bind: function (el) {
    el.addEventListener('input', function (event) {
      const regex = /^[1-9]\d*$/; // 正整数的正则表达式
      if (!regex.test(event.target.value)) {
        event.target.value = '';
      }
    });
  },
  // update: function (el, binding) {
  //   el.value = binding.value;
  // },
};
export default inputInteger;
