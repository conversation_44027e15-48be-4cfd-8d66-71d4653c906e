# Teleport 组件

Vue2 版本的传送门组件，类似于 Vue3 的 `<teleport>` 功能。允许将组件的内容传送到 DOM 树中的任意位置。

## 功能特点

- 支持将内容传送到任意 DOM 节点
- 支持多种插入位置（首部、尾部、之前、之后）
- 支持相对于指定元素的定位
- 自动处理目标元素动态加载的情况
- 提供禁用选项
- 自动清理和资源回收

## 属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| to | String | - | 是 | 目标元素的选择器（如：'#app' 或 '.container'） |
| disabled | Boolean | false | 否 | 是否禁用传送功能 |
| position | String | 'prepend' | 否 | 插入位置：'prepend'（首部）、'append'（尾部）、'before'（之前）、'after'（之后） |
| relative | String | '' | 否 | 相对定位的元素选择器，仅在 position 为 'before' 或 'after' 时有效 |

## 使用示例

### 基本用法

```vue
<!-- 将内容追加到目标元素末尾 -->
<Teleport to="#app">
  <div>这里是要传送的内容</div>
</Teleport>
```

### 插入到元素开头

```vue
<!-- 将内容插入到目标元素的最前面 -->
<Teleport to="#app" position="prepend">
  <div>这里是要传送的内容</div>
</Teleport>
```

### 相对定位

```vue
<!-- 将内容插入到指定元素之后 -->
<Teleport 
  to="#container" 
  position="after" 
  relative=".header">
  <div>这里是要传送的内容</div>
</Teleport>

<!-- 将内容插入到指定元素之前 -->
<Teleport 
  to="#container" 
  position="before" 
  relative=".footer">
  <div>这里是要传送的内容</div>
</Teleport>
```

### 动态控制

```vue
<!-- 动态控制传送功能 -->
<Teleport 
  :to="targetSelector"
  :disabled="isDisabled"
  :position="position"
  :relative="relativeSelector">
  <div>这里是要传送的内容</div>
</Teleport>
```

## 注意事项

1. 目标元素选择器必须是有效的，否则组件将等待目标元素出现
2. 使用相对定位时，如果找不到相对元素，将退回到默认的追加行为
3. 组件会自动处理清理工作，不需要手动管理 DOM
4. 传送的内容会保持其原有的事件处理和响应式特性
5. 建议在组件销毁时禁用传送功能，以确保正确的清理

## 实现原理

组件使用 `MutationObserver` 监听 DOM 变化，确保在目标元素动态加载时也能正确工作。通过 DOM 操作将内容移动到目标位置，同时保持 Vue 组件的所有功能特性。

## 兼容性

- 支持 Vue 2.x 版本
- 需要浏览器支持 `MutationObserver` API（IE11+） 