# 一个可自定义建议的日期选择器

## 简单的使用(默认建议选项)：

```html
	<DateSelect
			@change="
				dateSelectObj = $event;
				queryTableData(1);
			"
	/>
```
##    变量名不同(传入一个dateKeys)：

```html	
	<DateSelect 
	defaultDate="不限定"
	:dateKeys="['signingEndDate', 'signingStartDate']"
	:dateSelectObj="dateSelectObj2"
	:dateList="['本周', '本月', '本年', '不限定']"
	@change="
		dateSelectObj2 = $event;
		queryTableData(1);
	"
	/>
```
##    变量名不同(或者直接赋值)：

```html	
	<DateSelect 
	  @change="
			searchForm.pointsLogDateBegin = $event.startTime;
			searchForm.pointsLogDateEnd = $event.endTime;
			queryTableData(1);
	"
	/>
```

##     更多使用
```html	
	<DateSelect
		size="small"
		placeholder="请选择日期"
		defaultDate="本月"
		:dateSelectObj="dateSelectObj"
		:dateList="['不限定', '今天', '昨天', '本周', '本月']"
		@change="
			dateSelectObj = $event;
			queryTableData(1);
		"
	/>

```
##     引入组件
```js	
	import DateSelect from '@/components/DateSelect'; //带建议的日期选择器  
	components: {DateSelect,}
```

##     组件说明    
### Attributes 

- size: 尺寸
- dateKeys: 开始和结束时间的key 传入时先start再end
- placeholder: 占位符
- defaultDate: 默认建议选项
- dateList: 建议选项列表（未指定时显示默认建议选项）
- dateSelectObj: 日期对象（一般不用，默认日期请使用defaultDate，当外部需要返回时用 如：本地缓存回传日期）

### Methods
- visibleChange: 选择器选项菜单展开/收起时
- changeDateSelect: 触发日期选择（建议选项）
- changeDatePicker: 触发日期选择（日期范围选择器）

### Events
- change: 选择后触发，返回一个是一个日期对象