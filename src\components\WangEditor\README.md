# WangEditor 组件

基于 wangEditor 5 的富文本编辑器组件，提供了丰富的文本编辑功能，支持图片、视频、附件上传，以及自定义的图片预览功能。

## 功能特点

- 基于 wangEditor 5 封装，保持原有功能的同时提供更便捷的使用方式
- 支持图片上传、压缩和预览
- 支持视频上传和播放
- 支持附件上传和下载
- 可配置的工具栏
- 支持禁用/只读模式
- 支持自定义编辑器高度
- 实时内容更新和双向绑定

## 组件属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| value / v-model | String | '' | 否 | 编辑器内容，HTML 格式 |
| hasToolBar | Boolean | true | 否 | 是否显示工具栏 |
| editorHeight | Number | 300 | 否 | 编辑器高度（像素） |
| disabled | Boolean / Number | false | 否 | 是否禁用编辑器（只读模式） |

## 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| input | 内容变化时触发 | (content: String) |
| change | 内容变化时触发 | (content: String) |

## 使用示例

### 基本用法

```vue
<template>
  <div>
    <wang-editor v-model="content" />
  </div>
</template>

<script>
import WangEditor from '@/components/WangEditor/wangEditor.vue';

export default {
  components: {
    WangEditor
  },
  data() {
    return {
      content: '<p>初始内容</p>'
    }
  }
}
</script>
```

### 自定义配置

```vue
<template>
  <div>
    <wang-editor
      v-model="content"
      :hasToolBar="true"
      :editorHeight="500"
      :disabled="isReadOnly"
      @change="handleContentChange"
    />
  </div>
</template>

<script>
import WangEditor from '@/components/WangEditor/wangEditor.vue';

export default {
  components: {
    WangEditor
  },
  data() {
    return {
      content: '<p>自定义配置的编辑器</p>',
      isReadOnly: false
    }
  },
  methods: {
    handleContentChange(html) {
      console.log('内容已更新:', html);
    }
  }
}
</script>
```

### 只读模式

```vue
<template>
  <div>
    <wang-editor
      v-model="content"
      :disabled="true"
      :hasToolBar="false"
    />
  </div>
</template>

<script>
import WangEditor from '@/components/WangEditor/wangEditor.vue';

export default {
  components: {
    WangEditor
  },
  data() {
    return {
      content: '<p>这是一个只读的内容展示</p>'
    }
  }
}
</script>
```

## 图片功能

组件提供了以下图片相关功能：

1. **上传图片**：支持从本地选择图片上传
2. **图片压缩**：自动压缩大图片，提高加载性能
3. **图片预览**：点击图片可全屏预览
4. **图片尺寸调整**：支持调整图片显示宽度（30%、50%、100%）

## 附件功能

组件支持上传和下载各类附件：

1. **上传附件**：支持上传各种类型的文件
2. **下载附件**：点击附件可直接下载

## 注意事项

1. 编辑器高度不能小于 300px
2. 图片上传大小限制为 10MB
3. 视频上传大小限制为 200MB
4. 附件上传大小限制为 100MB
5. 小于 5KB 的图片会自动转为 base64 格式，不会上传到服务器
6. 使用前确保服务端接口符合 wangEditor 的返回格式要求

## 自定义扩展

组件使用了自定义的图片预览插件 `preViewImage.js`，可以通过类似方式扩展更多功能：

```js
// 注册自定义插件
import { Boot } from '@wangeditor/editor';
import customPlugin from './customPlugin.js';

Boot.registerMenu(customPlugin);
```

## 相关依赖

- @wangeditor/editor-for-vue
- @wangeditor/plugin-upload-attachment

## 实现原理

组件基于 wangEditor 5 封装，使用 Vue 2 的组件化开发模式。通过 `v-model` 实现双向绑定，使用 debounce 优化内容更新频率。自定义了图片预览插件，提供了更好的图片查看体验。 