<template>
	<div class="QandAOperation H100 vw15 min-w-200 flex-column align-center p10 gap-10">
		<div class="operation-item W100 flex-column align-center border-bottom gap-10 pb10">
			<el-button type="primary" class="W90" @click="dialogEdit = true"> 提问</el-button>
		</div>

		<div class="operation-item W100 flex-column align-center border-bottom gap-10 pb10">
			<el-button :type="selectOnly.all ? 'primary' : ''" class="W90 m0" @click="setSelectOnly('all')">🗃️ 全部问题</el-button>
			<el-button :type="selectOnly.answered ? 'primary' : ''" class="W90 m0" @click="setSelectOnly('answered')">
				👨‍🏫 我的回答
			</el-button>
			<el-button :type="selectOnly.self ? 'primary' : ''" class="W90 m0" @click="setSelectOnly('self')">🙋‍♂️ 我的提问</el-button>
		</div>
		<!-- 
		<div class="operation-item W100 flex-column align-center border-bottom gap-10 pb10">
			<div>👑 贡献排行榜</div>
			<div class="fs-12">小伙伴们，快来贡献你的知识吧！</div>

			<div class="overflow-auto W100 text-center">
				<div class="flex-column gap-10 fs-14">
					<div class="flex-1">🥇 虚位以待</div>
					<div class="flex-1">🥈 虚位以待</div>
					<div class="flex-1">🥉 虚位以待</div>
				</div>
			</div>
		</div> -->

		<el-dialog
			width="666px"
			:visible.sync="dialogEdit"
			:close-on-click-modal="false"
			:append-to-body="true"
			@close="dialogEdit = false"
			custom-class="uploadDialog"
		>
			<span slot="title">{{ editForm.qid ? '编辑' : '新增' }}提问</span>
			<el-form
				ref="editFormRef"
				:model="editForm"
				:rules="formRules"
				label-width="300px"
				label-position="top"
				@submit.native.prevent
			>
				<el-form-item label="问题（问题的核心）" prop="subject">
					<el-input v-model="editForm.subject" placeholder="请输入问题" clearable></el-input>
				</el-form-item>
				<el-form-item label="内容（对问题补充说明，可以更快获得解答）" prop="content">
					<el-input
						v-model="editForm.content"
						placeholder="请输入内容"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>
				<el-form-item label="图片" prop="urls">
					<div class="flex-align-center flex-wrap max-h-350 overflow-y-auto">
						<div v-for="(item, index) in editForm.urls" :key="index" class="img-box">
							<el-image class="img-item border mr10" fit="scale-down" :src="item" :preview-src-list="editForm.urls"> </el-image>
							<div class="img-delete el-icon-circle-close pointer" @click.stop="deleteImg(index)"></div>
						</div>
						<el-upload
							v-if="!editForm.urls || editForm.urls.length < 9"
							class="img-uploader flex-1"
							action=""
							accept="image/*"
							:http-request="uploadFile"
							:show-file-list="false"
							:limit="9"
							multiple
							drag
						>
							<i class="el-icon-upload"></i>
							<div class="el-upload__text"><em>拖拽/点击</em>(最多上传9图/可批量)</div>
						</el-upload>
					</div>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="dialogEdit = false">取 消</el-button>
				<el-button type="primary" @click="saveEdit">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { deepClone, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'QandAOperation',
	components: {},
	data() {
		return {
			dialogEdit: false,
			editForm: {
				subject: '',
				content: '',
				urls: [],
			},
			formRules: {
				subject: [{ required: true, message: '请输入问题', trigger: 'blur' }],
				content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
			},
			selectOnly: {
				answered: 0,
				self: 0,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['userInfos']) },
	// 监控data中的数据变化
	watch: {
		dialogEdit(val) {
			if (!val) {
				this.$nextTick(() => {
					this.editForm = resetValues(this.editForm);
					this.editForm.urls = [];
					this.$refs.editFormRef?.resetFields(); //移除该表单项的校验结果
					this.$refs.editFormRef?.clearValidate(); //移除该表单项的校验结果
				});
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 打开提示
		openMsg(msg) {
			return this.$message.warning(`【${msg}】正在开发中，敬请期待！`);
		},
		// 设置查询条件
		setSelectOnly(type) {
			if (type == 'all') {
				this.selectOnly = {
					answered: 0,
					self: 0,
					all: 1,
				};
			} else {
				this.selectOnly = {
					answered: type == 'answered' && !this.selectOnly.answered ? 1 : 0,
					self: type == 'self' && !this.selectOnly.self ? 1 : 0,
					all: 0,
				};
			}
			this.$emit('getSearchForm', this.selectOnly);
		},
		// 打开编辑弹窗
		openEdit(data) {
			this.editForm = { ...this.editForm, ...deepClone(data) };
			this.editForm.urls = data.urls || [];
			this.dialogEdit = true;
		},
		// 保存编辑
		saveEdit: async function () {
			const API = this.editForm.qid ? 'updateQuestion' : 'addQuestion';
			try {
				const res = await this.$axios[API](JSON.stringify(this.editForm));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.$emit('refresh');
					this.dialogEdit = false;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 删除图片
		deleteImg(index) {
			this.editForm.urls.splice(index, 1);
		},

		// 上传文件
		async uploadFile(item) {
			if (this.editForm.urls.length >= 9) {
				this.editForm.urls.length = 9;
				return this.$message.warning('最多上传9张图片!');
			}
			try {
				if (!item.file.type.includes('image')) {
					return this.$message.warning('上传文件只能是图片格式!');
				}
				if (!(item.file.size / 1024 / 1024 < 20)) {
					return this.$message.warning('上传图片大小不能超过 20MB!');
				}

				const formData = new FormData();
				formData.append('file', item.file);
				formData.append('type', 2);
				// type 1-用户头像,2-团队头像,3-团队图片,4-设备图片,5-反馈图片,6-原料图片,7-模具图片,8-产品图片,9-产品工艺图纸图片
				const res = await this.$axios.uploadFile(formData);
				if (res.data.success) {
					this.editForm.urls.push(res.data.data.path);
				} else {
					this.$message.warning(res.data.message);
				}
			} catch (error) {
				this.$message.warning(error.message);
			}
		},
	},
};
</script>

<style lang="scss" scoped>
.QandAOperation {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
}
</style>
<style lang="scss">
.uploadDialog {
	// 文件上传组件样式
	.img-box {
		position: relative;

		.img-item {
			width: 10vw;
			height: 10vh;
			max-width: 200px;
			max-height: 100px;
			position: relative;
		}

		.img-delete {
			position: absolute;
			top: 15px;
			right: 15px;
			font-size: 16px;
			color: #999;
			transform: translateY(-50%);
			opacity: 0;
		}

		&:hover .img-delete {
			opacity: 1;
		}
	}
	.img-uploader {
		.el-upload-dragger {
			width: 10vw;
			height: 10vh;
			max-width: 200px;
			max-height: 100px;
			.el-icon-upload {
				font-size: 50px;
				color: #c0c4cc;
				margin: 25px;
				line-height: 10px;
			}
			.el-upload__text {
				font-size: 12px;
			}
		}
	}
}
</style>
