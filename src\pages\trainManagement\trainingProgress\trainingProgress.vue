<template>
	<div id="trainingProgress">
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="培训进度" name="trainingProgress">
				<BaseLayout>
					<template #header>
						<!-- 模糊查询 -->
						<el-input
							class="searchBox"
							size="small"
							v-model="searchForm.trainNameQuery"
							placeholder="培训计划"
							@input="queryTableData(1)"
							clearable
						></el-input>

						<!-- 模糊查询 -->
						<el-input
							class="searchBox"
							size="small"
							v-model="searchForm.adminUserNameQuery"
							placeholder="姓名"
							@input="queryTableData(1)"
							clearable
						></el-input>
						<el-checkbox
							v-model="searchForm.isShowUnCompletePlanCourse"
							@change="
								searchForm.isShowCompletePlanCourse = false;
								queryTableData(1);
							"
							>仅显示未完成的计划课程
						</el-checkbox>
						<el-checkbox
							v-model="searchForm.isShowCompletePlanCourse"
							@change="
								searchForm.isShowUnCompletePlanCourse = false;
								queryTableData(1);
							"
							>仅显示已完成的计划课程
						</el-checkbox>
						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<!-- <el-button type="text" class="icon-third-bt_newdoc" @click="openDetail('添加培养计划', null)">导出</el-button> -->
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['startTime', 'endTime', 'createTime', 'planEndTime'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<div v-else-if="item.colNo == 'progress'">
										<Tooltips
											class="blue hover-blue"
											:cont-str="scope.row[item.colNo] + '%'"
											:cont-width="scope.column.width || scope.column.realWidth"
										>
										</Tooltips>
										<el-progress
											:percentage="scope.row[item.colNo]"
											:show-text="false"
											:color="'#409eff'"
											:define-back-color="'#999'"
										></el-progress>
									</div>

									<Tooltips
										v-else-if="item.colNo == 'score'"
										class="blue"
										:cont-str="scope.row[item.colNo] || '/'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<Tooltips
										v-else-if="item.colNo == 'points'"
										class="green"
										:cont-str="scope.row[item.colNo] || '/'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="错题集" name="errorQuestion">
				<errorQuestion ref="errorQuestion" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { debounce, dateFormat, sortTableData } from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import errorQuestion from './errorQuestion.vue';
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		errorQuestion,
	},
	name: 'trainingProgress', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'trainingProgress', //激活tab页
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '培养计划', colNo: 'trainPlan', align: 'left', width: '200' },
				{ colName: '姓名', colNo: 'adminUserName', align: 'left', width: '250' },
				{ colName: '课程', colNo: 'courseName', align: 'left', width: '' },
				{ colName: '进度', colNo: 'progress', align: 'left', width: '' },
				{ colName: '课程得分', colNo: 'score', align: 'right', width: '150' },
				{ colName: '培训积分', colNo: 'points', align: 'right', width: '150' },
			],
			// 查询表单
			searchForm: {
				adminUserNameQuery: '',
				isShowCompletePlanCourse: false,
				isShowUnCompletePlanCourse: false,
				trainNameQuery: '',
				// 其他...
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开明细
		openDetail(type, row) {
			this.$refs.DetailComRef.showDetailCom(type, row);
		},
		// 切换tab
		changeTab() {
			if (this.activeTab == 'trainingProgress') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectTrainProgress'; //接口
			const DATA = JSON.stringify({
				...this.searchForm,
				isShowCompletePlanCourse: this.searchForm.isShowCompletePlanCourse ? 1 : null,
				isShowUnCompletePlanCourse: this.searchForm.isShowUnCompletePlanCourse ? 1 : null,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat,
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		deleteRow(node) {
			if (!node.tpid) return;
			this.$confirm(node.trainPlan + '将被删除', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					this.$axios
						.deleteTrainPlan(JSON.stringify({ tpid: node.tpid }))
						.then(res => {
							if (res.data.success) {
								this.$succ();
								this.queryTableData();
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteTrainPlan |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
	},
};
</script>

<style lang="scss" scoped>
#trainingProgress {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
