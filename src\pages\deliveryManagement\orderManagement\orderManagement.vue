<template>
	<div class="orderManagement">
		<!-- 导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="deal"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twid = $event.twidList;
				queryTableData(1);
			"
		/>
		<OrderDetail ref="OrderDetail" @close="queryTableData(1)" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="订单管理" name="orderManagement">
				<BaseLayout>
					<template #header>
						<span class="search-label">订单日期</span>
						<DateSelect
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>
						<SearchHistoryInput
							className="min-w-100 vw8"
							name="salesmanName"
							placeholder="业务顾问"
							v-model.trim="searchForm.saleName"
							@input="queryTableData(1)"
						/>
						<SearchHistoryInput
							name="companyName"
							placeholder="客户工商注册名称"
							v-model="searchForm.queryParam"
							@input="queryTableData(1)"
						/>

						<el-checkbox-group v-model="searchForm.contractFlag" @change="queryTableData(1)">
							<el-checkbox label="0">未收款</el-checkbox>
							<el-checkbox label="1">已收款</el-checkbox>
						</el-checkbox-group>

						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<ExportBtn @trigger="openExport" />
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['createTime', 'complateMonth', 'receiptsDate'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 各种人员 -->
									<Tooltips
										v-else-if="['salesman', 'implement', 'consulting', 'talk'].includes(item.colNo)"
										:cont-str="scope.row[item.colNo]?.userName"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 订单类型 -->
									<Tooltips
										v-else-if="item.colNo == 'type'"
										:cont-str="scope.row[item.colNo] == 1 ? '合同' : scope.row[item.colNo] == 2 ? '直购' : '续费'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 来源 -->
									<Tooltips
										v-else-if="item.colNo == 'channel'"
										:cont-str="
											jointString(
												'/',
												sourceMap[scope.row[item.colNo]],
												scope.row.promotionalVidUserName,
												scope.row.promotionalVid,
											)
										"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 付款标志 -->
									<Tooltips
										v-else-if="item.colNo == 'sign'"
										:cont-str="scope.row[item.colNo] == 1 ? '个人支付' : '对公支付'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 项目阶段 -->
									<Tooltips
										v-else-if="item.colNo == 'stage'"
										v-show="scope.row[item.colNo]"
										:cont-str="'第 ' + scope.row[item.colNo] + ' 阶段'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<u-table-column label="" width="60" align="right">
								<template slot-scope="scope">
									<div class="flex-align-center">
										<el-button type="text" @click="openDialog('编辑订单', scope.row)">修改</el-button>
									</div>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { dateFormat, jointString, debounce, sortTableData } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
import ChannelSelect from '@/components/ChannelSelect.vue';
import ExportTable from '@/components/ExportTable';
import { sourceList, sourceMap } from '@/assets/js/inquirySource.js'; // 来源数据
import OrderDetail from './OrderDetail.vue';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		ExportTable,
		ChannelSelect,
		OrderDetail,
		ExportBtn,
	},
	name: 'orderManagement',
	data() {
		return {
			sourceList,
			sourceMap, // 来源列表
			activeTab: 'orderManagement',
			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '公司工商注册名称', colNo: 'registeredBusinessName', align: 'left', width: '170' },
				{ colName: '来源', colNo: 'channel', align: 'left', width: '80' },
				{ colName: '业务顾问', colNo: 'salesman', align: 'left', width: '' },
				{ colName: '实施顾问', colNo: 'implement', align: 'left', width: '' },
				{ colName: '订单日期', colNo: 'createTime', align: 'center', width: '120' },
				{ colName: '订单号', colNo: 'orderNo', align: 'left', width: ' ' },
				{ colName: '订单类型', colNo: 'type', align: 'center', width: '' },
				{ colName: '合同编号', colNo: 'contractNo', align: 'left', width: '' },
				{ colName: '项目阶段', colNo: 'stage', align: 'center', width: '' },
				{ colName: '应收金额(元)', colNo: 'receivableAmount', align: 'right', width: '' },
				{ colName: '完工日期', colNo: 'complateMonth', align: 'center', width: '120' },
				{ colName: '实收金额(元)', colNo: 'receiptsAmount', align: 'right', width: '' },
				{ colName: '实收日期', colNo: 'receiptsDate', align: 'center', width: '120' },
				{ colName: '付款标志', colNo: 'sign', align: 'center', width: '' },
			],
			searchForm: {
				contractFlag: ['0'],
				twid: [],
				channelName: '',
				queryParam: '',
				saleName: '',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 打开明细
		openDialog(title, row) {
			this.$refs.OrderDetail.openDialog(title, row);
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'orderManagementList'; //接口
			const DATA = JSON.stringify({
				...this.dateSelectObj,
				...this.searchForm,
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		dateFormat, //日期format
		jointString, //字符串拼接
		//自定义排序(新增/覆盖比较逻辑)
		sortChange({ prop, order }, doLayout = false) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop == 'salesman' || prop == 'implement' || prop == 'consulting' || prop == 'talk') {
						if (!a?.userName) return -1;
						if (!b?.userName) return 1;
						return a?.userName.localeCompare(b?.userName, 'zh-CN');
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
			doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
		},
		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					...this.dateSelectObj,
					...this.searchForm,
					pageNum: this.tablePageForm.currentPage,
					pageSize: this.tablePageForm.pageSize,
				}), //接口参数
				API: 'exportOrderManagement', //导出接口
				downloadData: '订单管理导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
.orderManagement {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
