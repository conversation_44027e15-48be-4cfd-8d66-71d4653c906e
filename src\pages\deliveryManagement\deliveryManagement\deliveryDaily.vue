<template>
	<div class="deliveryDaily">
		<BaseLayout>
			<template #header>
				<span class="search-label">周次</span>
				<el-button type="text" class="el-icon-arrow-left search-arrow" @click="changeWeek('reduce')"> </el-button>
				<el-date-picker
					size="small"
					v-model="selectTime"
					type="week"
					format="yyyy 年 第 W 周"
					placeholder="请选择周次"
					:picker-options="{ firstDayOfWeek: 1 }"
					:default-value="selectTime"
					:clearable="false"
					@change="changeSelectTime"
				>
				</el-date-picker>
				<el-button type="text" class="el-icon-arrow-right search-arrow" @click="changeWeek('add')"> </el-button>
				<el-checkbox class="ml10" v-model="overdueFlag" :true-label="1" :false-label="0" @change="queryTableData"
					>查看过期计划
				</el-checkbox>
				<el-checkbox v-model="selectRange" :true-label="1" :false-label="0" @change="queryTableData">仅看自己</el-checkbox>

				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-wrapper">
					<table class="table-main-daily" cellpadding="5" cellspacing="0">
						<!-- 表格标题 -->
						<tr class="sticky-top">
							<th class="W5 text-center">序号</th>
							<th class="W10 text-center">实施顾问</th>
							<th class="W10 text-center" v-for="index in 7" :key="index">
								{{ `周${['一', '二', '三', '四', '五', '六', '日'][index - 1]} (${getWeekDate(index)})` }}
							</th>
						</tr>
						<!-- 表格内容 -->
						<tr v-for="(item, index) in week" :key="index">
							<td class="text-center">{{ index + 1 }}</td>
							<td>{{ item.uname }}</td>
							<td>
								<div v-for="(day, mIndex) in item.monday" :key="mIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											style
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td>
								<div v-for="(day, tIndex) in item.tuesday" :key="tIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td class="ellipsis">
								<div v-for="(day, wIndex) in item.wednesday" :key="wIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td>
								<div v-for="(day, tIndex) in item.thursday" :key="tIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td>
								<div v-for="(day, fIndex) in item.friday" :key="fIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td>
								<div v-for="(day, sIndex) in item.saturday" :key="sIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
							<td>
								<div v-for="(day, sIndex) in item.sunday" :key="sIndex">
									<span v-for="(obj, dIndex) in day" :key="dIndex">
										<span
											v-for="(val, key, oIndex) in obj"
											:key="oIndex"
											class="hover-green"
											:style="{ color: getTextColor(val) }"
											@click="openDetail(key)"
										>
											<Tooltips style="" :cont-str="val" />
										</span>
									</span>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
export default {
	// import引入的组件需要注入到对象中才能使用
	props: {
		salesmanList: Array,
		twidList: Array,
		channelName: Array,
	},
	components: {
		// InquiryDetail,
	},
	name: 'deliveryDaily',
	data() {
		return {
			//日期相关
			selectTime: '',
			dateStart: '',
			dateEnd: '',
			flag: 0,
			overdueFlag: 0,
			selectRange: 0,
			nextPlan: [],
			week: [],

			colorList: {
				'(约)': 'salmon',
				BZ: 'salmon',
				BH: 'dodgerblue',
				BW: 'mediumpurple',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		channelName(newVal) {
			this.queryTableData(1);
		},
		twidList(newVal) {
			this.queryTableData();
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (window.localStorage) {
			this.overdueFlag = JSON.parse(window.localStorage.getItem('overdueFlag')) || 0;
			this.selectRange = JSON.parse(window.localStorage.getItem('selectRange')) || 0;
		}
		this.changeSelectTime();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 获取每周日期
		getWeekDate(index) {
			return this.$moment(this.dateStart)
				.startOf('isoWeek')
				.add(index - 1, 'day')
				.format('MM/DD');
		},
		// 时间上下周切换
		changeWeek(str) {
			this.selectTime = new Date(this.selectTime).getTime();
			if (str == 'add') {
				this.selectTime += 7 * 24 * 60 * 60 * 1000;
				this.flag--;
			} else if (str == 'reduce') {
				// 上一周
				this.selectTime -= 7 * 24 * 60 * 60 * 1000;
				this.flag++;
			}
			this.dateStart = this.$moment(this.selectTime).startOf('isoWeek').valueOf();
			this.dateEnd = this.dateStart + 7 * 24 * 60 * 60 * 1000 - 1;
			this.queryTableData();
		},
		// 时间选择器
		changeSelectTime() {
			if (!this.selectTime) {
				this.selectTime = _.getNowWeekStart();
				this.dateStart = this.$moment(this.selectTime).startOf('isoWeek').valueOf();
				this.dateEnd = this.dateStart + 7 * 24 * 60 * 60 * 1000 - 1;
			} else {
				this.dateStart = this.$moment(this.selectTime).startOf('isoWeek').valueOf();
				this.dateEnd = this.dateStart + 7 * 24 * 60 * 60 * 1000 - 1;
			}
			this.queryTableData();
		},
		// 跟单日报数据
		queryTableData: _.debounce(function (type) {
			const str = JSON.stringify({
				endTime: this.dateEnd,
				startTime: this.dateStart,
				twidList: this.twidList,
				channelName: this.channelName,
				overdueFlag: this.overdueFlag,
				selectRange: this.selectRange,
			});

			this.$axios
				.deliverDaily(str)
				.then(res => {
					if (res.data.success) {
						this.week = [];
						res.data.data.forEach(item => {
							const obj = {
								uname: '',
								auid: '',
								friday: [],
								monday: [],
								saturday: [],
								sunday: [],
								thursday: [],
								tuesday: [],
								wednesday: [],
							};
							const weekMap = {
								0: 'sunday',
								1: 'monday',
								2: 'tuesday',
								3: 'wednesday',
								4: 'thursday',
								5: 'friday',
								6: 'saturday',
							};
							if (!obj.uname.includes(item.uname)) {
								obj.uname = item.uname;
								obj.auid = item.auid;
							}
							item?.overdueweekDeliverDailyVOS?.forEach(val => {
								const weekNo = new Date(val.releaseTime).getDay();
								const dmid = val.dmid;
								const content = '(约)' + val.content;
								obj[weekMap[weekNo]].push([{ [dmid]: content }]);
							});
							item?.weekDeliverDailyVOS?.forEach(val => {
								const weekNo = new Date(val.releaseTime).getDay();
								const dmid = val.dmid;
								const content = val.content;
								obj[weekMap[weekNo]].push([{ [dmid]: content }]);
							});
							this.week.push(obj);
						});
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						window.localStorage?.setItem('overdueFlag', JSON.stringify(this.overdueFlag));
						window.localStorage?.setItem('selectRange', JSON.stringify(this.selectRange));
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('deliverDaily |' + error);
				});
		}),

		// 查询详情数据
		openDetail(dmid) {
			this.$emit('openDetail', { dmid });
		},
		// 判断文本                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            颜色
		getTextColor(str) {
			let color;
			// color = colorList[str];
			if (str.includes('(约)')) {
				color = 'salmon';
			}
			if (str.includes('BZ')) {
				color = 'darkturquoise';
			}
			if (str.includes('BH')) {
				color = 'dodgerblue';
			}
			if (str.includes('BW')) {
				color = 'mediumpurple';
			}
			return color;
		},
		//日期format
		dateFormat: _.dateFormat,
		//数据导出
		openExport() {},
	},
};
</script>

<style lang="scss" scoped>
.deliveryDaily {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;

	.table-wrapper {
		height: calc(100vh - 250px);
	}
	.table-main-daily {
		width: 100%;
		color: #606266;
		border-left: 1px solid #e9e9e9;
		border-bottom: 1px solid #e9e9e9;

		.sticky-top {
			position: sticky;
			top: -10px;
		}
		hover-green {
			&:hover {
				color: #28d094;
				text-decoration: underline;
				cursor: pointer;
			}
		}

		tr {
			th {
				text-align: left;
				font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
				font-weight: 650;
				font-size: 14px;
				color: #666666;
				padding: 15px 0 15px 5px;
				background: #f5f5f5;
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				word-wrap: break-word;
			}

			td {
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				font-size: 12px;
				vertical-align: top;
				max-width: 10vw;
			}
		}
	}
}
</style>
