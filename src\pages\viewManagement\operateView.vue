<template>
	<div id="operateView" class="dataView">
		<div class="header">
			<div class="header-background">
				<span class="header-background-title">树字MES运营看板</span>
				<div class="header-button">
					<button @click="screenFull">全屏</button>
					<button @click="backToHome">首页</button>
				</div>
			</div>
		</div>

		<div class="main">
			<div class="main-left">
				<div class="main-left-userCount">
					<div class="main-title">
						<span>用户量</span>
					</div>
					<div class="main-left-userCount-data" style="height: 27vh">
						<div>
							<div>累计</div>
							<div style="display: flex; align-items: center; letter-spacing: 0.2vw">
								<CountTo
									:startVal="0"
									:endVal="userCount"
									:duration="3000"
									style="color: rgb(53, 214, 218); font-size: 3.5vh; margin-right: 0.5vw"
								/>
								个
							</div>
						</div>
						<div>
							<div>本月新增</div>
							<div style="display: flex; align-items: center; letter-spacing: 0.2vw">
								<CountTo
									:prefix="'+'"
									:startVal="0"
									:endVal="addUserCount"
									:duration="3000"
									style="color: rgb(53, 214, 218); font-size: 3.5vh; margin-right: 0.5vw"
								/>
								个
							</div>
						</div>
					</div>
				</div>
				<div class="main-left-user">
					<div class="main-left-userData">
						<div class="main-title">
							<span>新增用户数</span>
							<span class="main-title-right">
								<span>
									<el-date-picker
										size="mini"
										style="width: 7vw"
										v-model="addUserYear"
										type="year"
										value-format="timestamp"
										popper-append-to-body="false"
										popper-class="view-date-picker"
										format="yyyy 年度 ▼"
										placeholder="请选择年份 ▼"
										@change="resetChartsDb(1)"
									>
									</el-date-picker>
								</span>
							</span>
						</div>
						<div style="height: 27vh">
							<EChart
								ref="user"
								:seriesData="emptyData"
								:extraOption="userOption"
								:activeOption="{ isActive: true, duration: 19 }"
							></EChart>
						</div>
					</div>
				</div>
				<div class="main-left-addEquipment">
					<div class="main-left-addEquipmentData">
						<div class="main-title">
							<span>新增设备数量</span>
							<!-- <span class="main-title-right">
                <span>
                  <el-date-picker size="mini" style="width:7vw;" v-model="selectYear" type="year"
                    value-format="timestamp" popper-append-to-body="false" popper-class="view-date-picker"
                    format="yyyy 年度 ▼" placeholder="请选择年份 ▼" @change="resetChartsDb(2)">
                  </el-date-picker>
                </span>
              </span> -->
						</div>
						<div style="height: 27vh">
							<EChart
								ref="addEquipment"
								:seriesData="emptyData"
								:extraOption="addEquipmentOption"
								:activeOption="{ isActive: true, duration: 28 }"
							></EChart>
						</div>
					</div>
				</div>
			</div>
			<div class="main-center">
				<div class="main-center-userActive">
					<div class="main-title">
						<span
							>用户日活数量分析
							<el-tooltip
								class="item"
								effect="dark"
								content="统计规则：用户登陆系统则该月份活跃+1，同月同用户再次上线不重复纳入计数"
								placement="top-start"
							>
								<i class="el-icon-warning-outline main-title-icon"></i>
							</el-tooltip>
						</span>
						<span class="main-title-right">
							<span>
								<el-date-picker
									size="mini"
									style="width: 7vw"
									v-model="userActiveYear"
									type="year"
									value-format="timestamp"
									popper-append-to-body="false"
									popper-class="view-date-picker"
									format="yyyy 年度 ▼"
									placeholder="请选择年份 ▼"
									@change="resetChartsDb(3)"
								>
								</el-date-picker>
							</span>
						</span>
					</div>
					<div style="height: 56vh">
						<EChart
							ref="userActive"
							:seriesData="emptyData"
							:extraOption="userActiveOption"
							:activeOption="{ isActive: true, duration: 37 }"
						></EChart>
					</div>
				</div>
				<div class="main-center-customerActive">
					<div class="main-title">
						<span
							>客户日活数量分析
							<el-tooltip
								class="item"
								effect="dark"
								content="统计规则：团队任一用户在线则该月份活跃+1，同一团队中其他用户系统在线时不重复纳入计数"
								placement="top-start"
							>
								<i class="el-icon-warning-outline main-title-icon"></i>
							</el-tooltip>
						</span>
						<span class="main-title-right">
							<span>
								<el-date-picker
									size="mini"
									style="width: 7vw"
									v-model="customerActiveYear"
									type="year"
									value-format="timestamp"
									popper-append-to-body="false"
									popper-class="view-date-picker"
									format="yyyy 年度 ▼"
									placeholder="请选择年份 ▼"
									@change="resetChartsDb(4)"
								>
								</el-date-picker>
							</span>
						</span>
					</div>
					<div style="height: 27vh">
						<EChart
							ref="customerActive"
							:seriesData="emptyData"
							:extraOption="customerActiveOption"
							:activeOption="{ isActive: true, duration: 46 }"
						></EChart>
					</div>
				</div>
			</div>
			<div class="main-right">
				<div class="main-right-light">
					<div class="main-title">
						<span>客户量</span>
					</div>
					<div class="main-left-userCount-data" style="height: 27vh">
						<div>
							<div>累计</div>
							<div style="display: flex; align-items: center; letter-spacing: 0.2vw">
								<CountTo
									:startVal="0"
									:endVal="customerCount"
									:duration="3000"
									style="color: rgb(53, 214, 218); font-size: 3.5vh; margin-right: 0.5vw"
								/>
								个
							</div>
						</div>
						<div>
							<div>本月新增</div>
							<div style="display: flex; align-items: center; letter-spacing: 0.2vw">
								<CountTo
									:prefix="'+'"
									:startVal="0"
									:endVal="addCustomerCount"
									:duration="3000"
									style="color: rgb(53, 214, 218); font-size: 3.5vh; margin-right: 0.5vw"
								/>
								个
							</div>
						</div>
					</div>
				</div>
				<div class="main-right-customer">
					<div class="main-title">
						<span>新增客户量</span>
						<!-- <span class="main-title-right">
              <span>
                <el-date-picker size="mini" style="width:7vw;" v-model="selectYear" type="year"
                  value-format="timestamp" popper-append-to-body="false" popper-class="view-date-picker"
                  format="yyyy 年度 ▼" placeholder="请选择年份 ▼" @change="resetChartsDb(5)">
                </el-date-picker>
              </span>
            </span> -->
					</div>
					<div style="height: 27vh">
						<EChart
							ref="customer"
							:seriesData="emptyData"
							:extraOption="addCustomerOption"
							:activeOption="{ isActive: true, duration: 55 }"
						></EChart>
					</div>
				</div>
				<div class="main-right-equipment">
					<div class="main-title">
						<span>在线设备数</span>
						<span class="main-title-right">
							<span>
								<el-date-picker
									size="mini"
									style="width: 7vw"
									v-model="equipmentYear"
									type="year"
									value-format="timestamp"
									popper-append-to-body="false"
									popper-class="view-date-picker"
									format="yyyy 年度 ▼"
									placeholder="请选择年份 ▼"
									@change="resetChartsDb(6)"
								>
								</el-date-picker>
							</span>
						</span>
					</div>
					<div style="height: 27vh">
						<EChart
							ref="equipment"
							:seriesData="emptyData"
							:extraOption="equipmentOption"
							:activeOption="{ isActive: true, duration: 64 }"
						></EChart>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import CountTo from 'vue-count-to';
import { EChart } from '@/components/Chart/index.js';
import * as echarts from 'echarts';
export default {
	name: 'operateView',
	components: { CountTo, EChart },
	props: {
		fullScreenFlag: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			userInfo: {},
			refreshInterval: null, //数据刷新间隔
			selectYear: new Date(),
			addUserYear: new Date(), // 新增用户数
			addCustomerYear: new Date(), // 新增客户数
			addEquipmentYear: new Date(), // 新增设备数量
			userActiveYear: new Date(), // 用户日活数量分析
			customerActiveYear: new Date(), // 客户日活数量分析
			equipmentYear: new Date(), // 在线设备数

			userCount: 0, //用户量
			addUserCount: 0, //新增用户量
			customerCount: 0, //客户量
			addCustomerCount: 0, //新增客户量

			//空数据
			emptyData: [{}],
			// 用户日活数量分析

			userActiveOption: {
				title: {
					// text: '一周内人员出入总数变化图',
					// textStyle: {
					// fontSize: _.fitChartSize(24),
					//   fontWeight: 'normal',
					//   color: '#fff',
					// },
					// x: 'center'
				},
				// backgroundColor: "#05224d",
				tooltip: {
					trigger: 'axis',
					// axisPointer: {
					//   type: 'shadow'
					// },
					backgroundColor: 'rgb(7, 97, 165,.8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					valueFormatter: value => value + '人',
				},
				grid: {
					top: '8%',
					left: '1%',
					right: '3%',
					bottom: '1%',
					containLabel: true,
				},
				legend: {
					itemGap: 50,
					data: [''],
					textStyle: {
						fontSize: _.fitChartSize(24),
						color: '#f9f9f9',
						borderColor: '#fff',
					},
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: false,
						axisLine: {
							//坐标轴轴线相关设置。数学上的x轴
							show: true,
							lineStyle: {
								color: '#f9f9f9',
							},
						},
						axisLabel: {
							//坐标轴刻度标签的相关设置
							interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，
							textStyle: {
								fontSize: _.fitChartSize(30),
								color: '#d1e6eb',
								margin: 15,
							},
						},
						axisTick: {
							show: false,
						},
						data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
					},
					{
						// 第二坐标轴
						position: 'bottom',
						offset: _.fitChartSize(40),
						type: 'category',
						boundaryGap: false,
						axisLine: {
							//坐标轴轴线相关设置。数学上的x轴
							show: false,
							lineStyle: {
								color: '#f9f9f9',
							},
						},
						axisLabel: {
							//坐标轴刻度标签的相关设置
							interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，
							textStyle: {
								fontSize: _.fitChartSize(24),
								color: '#d1e6eb',
								margin: 15,
							},
						},
						axisTick: {
							show: false,
						},
						// data: [2000, 2200, 3100, 1600, 2700, 2300, 1500, 1999, 1245, 2221, 1000, 3000]
						data: [],
					},
				],
				yAxis: [
					{
						name: '人',
						nameTextStyle: {
							color: '#aaa',
							nameLocation: 'start',
							fontSize: _.fitChartSize(30),
						},
						type: 'value',
						min: 0,
						// max: 140,
						minInterval: 1,
						splitNumber: 7,
						splitLine: {
							show: true,
							lineStyle: {
								color: '#0a3256',
							},
						},
						axisLine: {
							show: false,
						},
						axisLabel: {
							margin: 20,
							textStyle: {
								fontSize: _.fitChartSize(30),
								color: '#d1e6eb',
							},
						},
						axisTick: {
							show: false,
						},
					},
				],
				series: [
					{
						// animation: true,
						// animationDuration: 1020,
						// animationDurationUpdate: 1020,
						name: '用户日活数量',
						type: 'line',
						smooth: false, //是否平滑曲线显示
						symbol: 'none', // 默认是空心圆（中间是白色的），改成实心圆 circle
						showAllSymbol: false,
						// symbolSize: 6,
						lineStyle: {
							color: '#66ffff', // 线条颜色
							borderColor: '#f0f',
						},
						label: {
							show: false,
							position: 'top',
							textStyle: {
								fontSize: _.fitChartSize(24),
								color: '#fff',
							},
						},
						itemStyle: {
							color: '#66ffff',
						},
						tooltip: {
							show: true,
						},
						areaStyle: {
							//区域填充样式
							normal: {
								//线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
								color: new echarts.graphic.LinearGradient(
									0,
									0,
									0,
									1,
									[
										{
											offset: 0,
											color: 'rgba(0,154,120,1)',
										},
										{
											offset: 1,
											color: 'rgba(0,0,0, 0)',
										},
									],
									false,
								),
								shadowColor: 'rgba(53,142,215, 0.9)', //阴影颜色
								shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
							},
						},
						// data: [2000, 2200, 3100, 1600, 2700, 2300, 1500, 1999, 1245, 2221, 1000, 3000]
						data: [],
					},
					//, {
					//     name: '最新注册量',
					//     type: 'bar',
					//     barWidth: 20,
					//     tooltip: {
					//         show: false
					//     },
					//      label: {
					//         show: true,
					//         position: 'top',
					//         textStyle: {
					//             color: '#fff',
					//         }
					//     },
					//     itemStyle: {
					//         normal: {
					//             // barBorderRadius: 5,
					//             // color: new echarts.graphic.LinearGradient(
					//             //     0, 0, 0, 1,
					//             //     [{
					//             //             offset: 0,
					//             //             color: '#14c8d4'
					//             //         },
					//             //         {
					//             //             offset: 1,
					//             //             color: '#43eec6'
					//             //         }
					//             //     ]
					//             // )
					//             color: function(params) {
					//                 var colorList = ['#0ec1ff', '#10cdff', '#12daff', '#15ebff', '#17f8ff', '#1cfffb', '#1dfff1'];
					//                 return colorList[params.dataIndex];
					//             }
					//         }
					//     },
					//     data: [200, 382, 102, 267, 186, 315, 316]
					//}
				],
			},
			// 在线设备数

			equipmentOption: {
				title: {
					// text: '一周内人员出入总数变化图',
					// textStyle: {
					// fontSize: _.fitChartSize(24),
					//   fontWeight: 'normal',
					//   color: '#fff',
					// },
					// x: 'center'
				},
				// backgroundColor: "#05224d",
				tooltip: {
					trigger: 'axis',
					// axisPointer: {
					//   type: 'shadow'
					// },
					backgroundColor: 'rgb(7, 97, 165,.8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					valueFormatter: value => value + '台',
				},
				grid: {
					top: '15%',
					left: '1%',
					right: '3%',
					bottom: '1%',
					containLabel: true,
				},
				legend: {
					itemGap: 50,
					data: [''],
					textStyle: {
						fontSize: _.fitChartSize(24),
						color: '#f9f9f9',
						borderColor: '#fff',
					},
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: false,
						axisLine: {
							//坐标轴轴线相关设置。数学上的x轴
							show: true,
							lineStyle: {
								color: '#f9f9f9',
							},
						},
						axisLabel: {
							//坐标轴刻度标签的相关设置
							interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，
							textStyle: {
								fontSize: _.fitChartSize(24),
								color: '#d1e6eb',
								margin: 15,
							},
						},
						axisTick: {
							show: false,
						},
						data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
					},
					{
						// 第二坐标轴
						position: 'bottom',
						offset: _.fitChartSize(30),
						type: 'category',
						boundaryGap: false,
						axisLine: {
							//坐标轴轴线相关设置。数学上的x轴
							show: false,
							lineStyle: {
								color: '#f9f9f9',
							},
						},
						axisLabel: {
							//坐标轴刻度标签的相关设置
							interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，
							textStyle: {
								fontSize: _.fitChartSize(20),
								color: '#d1e6eb',
								margin: 15,
							},
						},
						axisTick: {
							show: false,
						},
						// data: [2000, 2200, 3100, 1600, 2700, 2300, 1500, 1999, 1245, 2221, 1000, 3000],
						data: [],
					},
				],
				yAxis: [
					{
						name: '台',
						nameTextStyle: {
							color: '#aaa',
							nameLocation: 'start',
						},
						type: 'value',
						min: 0,
						// max: 140,
						minInterval: 1,
						splitNumber: 7,
						splitLine: {
							show: true,
							lineStyle: {
								color: '#0a3256',
							},
						},
						axisLine: {
							show: false,
						},
						axisLabel: {
							margin: 20,
							textStyle: {
								fontSize: _.fitChartSize(24),
								color: '#d1e6eb',
							},
						},
						axisTick: {
							show: false,
						},
					},
				],
				series: [
					{
						name: '在线设备数',
						type: 'line',
						smooth: false, //是否平滑曲线显示
						symbol: 'none', // 默认是空心圆（中间是白色的），改成实心圆 circle
						showAllSymbol: false,
						// symbolSize: 6,
						lineStyle: {
							color: '#66ffff', // 线条颜色
							borderColor: '#f0f',
						},
						label: {
							show: false,
							position: 'top',
							textStyle: {
								fontSize: _.fitChartSize(24),
								color: '#fff',
							},
						},
						itemStyle: {
							color: '#66ffff',
						},
						tooltip: {
							show: true,
						},
						areaStyle: {
							//区域填充样式
							normal: {
								//线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
								color: new echarts.graphic.LinearGradient(
									0,
									0,
									0,
									1,
									[
										{
											offset: 0,
											color: 'rgba(0,154,120,1)',
										},
										{
											offset: 1,
											color: 'rgba(0,0,0, 0)',
										},
									],
									false,
								),
								shadowColor: 'rgba(53,142,215, 0.9)', //阴影颜色
								shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
							},
						},
						// data: [2000, 2200, 3100, 1600, 2700, 2300, 1500, 1999, 1245, 2221, 1000, 3000],
						data: [],
					},
					//, {
					//     name: '最新注册量',
					//     type: 'bar',
					//     barWidth: 20,
					//     tooltip: {
					//         show: false
					//     },
					//      label: {
					//         show: true,
					//         position: 'top',
					//         textStyle: {
					//             color: '#fff',
					//         }
					//     },
					//     itemStyle: {
					//         normal: {
					//             // barBorderRadius: 5,
					//             // color: new echarts.graphic.LinearGradient(
					//             //     0, 0, 0, 1,
					//             //     [{
					//             //             offset: 0,
					//             //             color: '#14c8d4'
					//             //         },
					//             //         {
					//             //             offset: 1,
					//             //             color: '#43eec6'
					//             //         }
					//             //     ]
					//             // )
					//             color: function(params) {
					//                 var colorList = ['#0ec1ff', '#10cdff', '#12daff', '#15ebff', '#17f8ff', '#1cfffb', '#1dfff1'];
					//                 return colorList[params.dataIndex];
					//             }
					//         }
					//     },
					//     data: [200, 382, 102, 267, 186, 315, 316]
					//}
				],
			},
			// 客户日活数量分析
			customerActiveOption: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow',
					},
					backgroundColor: 'rgb(7, 97, 165,.8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					// formatter: "{a} <br/>{b} : {c}人",
					valueFormatter: value => value + '客户',
				},
				legend: {
					show: false,
					orient: 'horizontal',
					top: '0%',
					textStyle: {
						fontSize: _.fitChartSize(24),
						color: '#d1e6eb',
						margin: 15,
					},
				},
				grid: {
					left: '1%',
					right: '1%',
					bottom: '3%',
					top: '15%',
					containLabel: true,
				},
				xAxis: [
					{
						type: 'category',
						axisLabel: {
							//坐标轴刻度标签的相关设置
							interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，
							textStyle: {
								fontSize: _.fitChartSize(24),
								color: '#d1e6eb',
								margin: 15,
							},
						},
						axisTick: {
							show: false,
						},
						data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月    '],
					},
				],
				yAxis: [
					{
						name: '客户',
						nameTextStyle: {
							color: '#aaa',
							nameLocation: 'start',
						},
						type: 'value',
						min: 0,
						minInterval: 1,
						splitLine: {
							show: true,
							lineStyle: {
								color: '#6e7079',
							},
						},
						axisTick: {
							show: true,
						},
						axisLine: {
							show: true,
						},
						axisLabel: {
							margin: 20,
							textStyle: {
								// fontSize: _.fitChartSize(24),
								color: '#d1e6eb',
							},
						},
					},
				],
				series: [
					{
						name: '客户日活数量',
						type: 'bar',
						color: '#67def1',
						label: {
							show: true,
							position: 'top',
							textStyle: {
								color: '#fff',
							},
						},
						itemStyle: {
							color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
								{
									offset: 0,
									color: '#addbea',
								},
								{
									offset: 0.5,
									color: '#41ddea',
								},
								{
									offset: 1,
									color: '#00baff',
								},
							]),
						},

						barWidth: '35%',
						// data: [120, 132, 101, 134, 90, 230, 210, 120, 132, 101, 134, 90, 230, 210],
						data: [],
					},
				],
			},

			// 新增用户数
			userOption: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						// 坐标轴指示器，坐标轴触发有效
						type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
					},
					backgroundColor: 'rgb(7, 97, 165,.8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					valueFormatter: value => value + '个',
				},
				grid: {
					top: '15%',
					left: '1%',
					right: '1%',
					bottom: '3%',
					containLabel: true,
				},
				xAxis: {
					data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],

					axisTick: {
						show: false,
					},
					//坐标值标注
					axisLabel: {
						interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，

						textStyle: {
							color: '#fff',
						},
					},
				},
				yAxis: {
					name: '个',
					nameTextStyle: {
						color: '#aaa',
						nameLocation: 'start',
					},
					min: 0,
					minInterval: 1,
					//坐标轴
					axisLine: {
						show: true,
					},
					//坐标值标注
					axisLabel: {
						show: true,
						textStyle: {
							color: '#fff',
						},
					},
					//分格线
					splitLine: {
						show: false,
					},
				},
				series: [
					{
						// 正面
						name: '',
						tooltip: {
							show: true,
							valueFormatter: value => value + '个',
						},
						type: 'bar',
						barWidth: 17,
						color: '#1d1841',
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,],
						data: [],
						barGap: 0,
					},
					{
						// 侧面
						type: 'bar',
						barWidth: 6,
						tooltip: {
							show: false,
						},
						color: '#2a418d',
						barGap: 0,
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,].map(item => item + 5)
						data: [],
					},
					{
						// 顶部
						name: 'b',
						tooltip: {
							show: false,
						},
						label: {
							show: true,
							position: 'top',
							textStyle: {
								color: '#fff',
							},
						},
						type: 'pictorialBar',

						itemStyle: {
							borderWidth: 1,
							borderColor: '#ffff99',
							color: '#ffff99',
						},
						symbol: 'path://M 0,0 l 120,0 l -30,60 l -120,0 z',
						symbolSize: ['20', '9'],
						symbolOffset: ['0', '-8'],
						// symbolRotate: -5,
						symbolPosition: 'end',
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,],
						data: [],
						z: 3,
					},
				],
			},

			// 新增设备数
			addEquipmentOption: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						// 坐标轴指示器，坐标轴触发有效
						type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
					},
					backgroundColor: 'rgb(7, 97, 165,.8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					// valueFormatter: (value) => value + '台',
				},
				grid: {
					top: '15%',
					left: '1%',
					right: '1%',
					bottom: '3%',
					containLabel: true,
				},
				xAxis: {
					data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],

					axisTick: {
						show: false,
					},
					//坐标值标注
					axisLabel: {
						interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，

						textStyle: {
							color: '#fff',
						},
					},
				},
				yAxis: {
					name: '台',
					nameTextStyle: {
						color: '#aaa',
						nameLocation: 'start',
					},
					min: 0,
					minInterval: 1,
					//坐标轴
					axisLine: {
						show: true,
					},
					//坐标值标注
					axisLabel: {
						show: true,
						textStyle: {
							color: '#fff',
						},
					},
					//分格线
					splitLine: {
						show: false,
					},
				},
				series: [
					{
						// 正面
						name: '',
						tooltip: {
							show: true,
							valueFormatter: value => value + '台',
						},
						type: 'bar',
						barWidth: 17,
						color: '#1d1841',
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,],
						data: [],
						barGap: 0,
					},
					{
						// 侧面
						type: 'bar',
						barWidth: 6,
						tooltip: {
							show: false,
						},
						color: '#2a418d',
						barGap: 0,
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,].map(item => item + 3)
						data: [],
					},
					{
						// 顶部
						name: 'b',
						tooltip: {
							show: false,
						},
						label: {
							show: true,
							position: 'top',
							textStyle: {
								color: '#fff',
							},
						},
						type: 'pictorialBar',

						itemStyle: {
							borderWidth: 1,
							borderColor: '#ffff99',
							color: '#ffff99',
						},
						symbol: 'path://M 0,0 l 120,0 l -30,60 l -120,0 z',
						symbolSize: ['20', '9'],
						symbolOffset: ['0', '-8'],
						// symbolRotate: -5,
						symbolPosition: 'end',
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,],
						data: [],
						z: 3,
					},
				],
			},
			// 新增客户数
			addCustomerOption: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						// 坐标轴指示器，坐标轴触发有效
						type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
					},
					backgroundColor: 'rgb(7, 97, 165,.8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					valueFormatter: value => value + '个',
				},
				grid: {
					top: '15%',
					left: '1%',
					right: '1%',
					bottom: '3%',
					containLabel: true,
				},
				xAxis: {
					data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],

					axisTick: {
						show: false,
					},
					//坐标值标注
					axisLabel: {
						interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，

						textStyle: {
							color: '#fff',
						},
					},
				},
				yAxis: {
					name: '个',
					nameTextStyle: {
						color: '#aaa',
						nameLocation: 'start',
					},
					min: 0,
					minInterval: 1,
					//坐标轴
					axisLine: {
						show: true,
					},
					//坐标值标注
					axisLabel: {
						show: true,
						textStyle: {
							color: '#fff',
						},
					},
					//分格线
					splitLine: {
						show: false,
					},
				},
				series: [
					{
						// 正面
						name: '',
						tooltip: {
							tooltip: {
								show: true,
								valueFormatter: value => value + '个',
							},
						},
						type: 'bar',
						barWidth: 17,
						color: '#1d1841',
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,],
						data: [],
						barGap: 0,
					},
					{
						// 侧面
						type: 'bar',
						barWidth: 6,
						color: '#2a418d',
						tooltip: {
							show: false,
						},
						barGap: 0,
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,].map(item => item * 1.05)
						data: [],
					},
					{
						// 顶部
						name: 'b',
						tooltip: {
							show: false,
						},
						label: {
							show: true,
							position: 'top',
							textStyle: {
								color: '#fff',
							},
						},
						type: 'pictorialBar',

						itemStyle: {
							borderWidth: 1,
							borderColor: '#ffff99',
							color: '#ffff99',
						},
						symbol: 'path://M 0,0 l 120,0 l -30,60 l -120,0 z',
						symbolSize: ['20', '9'],
						symbolOffset: ['0', '-8'],
						// symbolRotate: -5,
						symbolPosition: 'end',
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,],
						data: [],
						z: 3,
					},
				],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['menuList']),
	},
	// 监控data中的数据变化
	watch: {
		fullScreenFlag(newVal) {
			const operateView = document.getElementById('operateView');
			if (newVal) {
				operateView.style.position = 'absolute';
			} else {
				operateView.style.position = 'relative';
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.resetChartsDb();
		window.addEventListener('resize', this.resetChartsDb);
		this.$once('hook:beforeDestroy', () => {
			window.removeEventListener('resize', this.resetChartsDb);
		});

		this.refreshInterval = setInterval(() => {
			//数据刷新定时器
			console.log('每30分钟更新数据');
			this.getEchartsData();
		}, 60000 * 30);
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		window.removeEventListener('resize', this.resetChartsDb);
	},
	// 生命周期 - 销毁完成
	destroyed() {
		clearInterval(this.refreshInterval);
		this.refreshInterval = null;
		// setTimeout(() => {
		// 	document.querySelector(".el-main").style.padding = "20px";
		// },)
	},
	// 方法集合
	methods: {
		// 获取图表数据
		getEchartsData() {
			this.userCount = 0;
			this.addUserCount = 0;
			this.customerCount = 0;
			this.addCustomerCount = 0;
			const str = JSON.stringify({
				// "endDate": _.getYearFirstDayAndLastDay(this.selectYear)[1],
				// "startDate": _.getYearFirstDayAndLastDay(this.selectYear)[0],
				// 客户日活
				clientActiveEndDate: _.getYearFirstDayAndLastDay(this.customerActiveYear)[1],
				clientActiveStartDate: _.getYearFirstDayAndLastDay(this.customerActiveYear)[0],
				// 新增用户
				newUserEndDate: _.getYearFirstDayAndLastDay(this.addUserYear)[1],
				newUserStartDate: _.getYearFirstDayAndLastDay(this.addUserYear)[0],
				// 在线设备
				onlineMachineEndDate: _.getYearFirstDayAndLastDay(this.equipmentYear)[1],
				onlineMachineStartDate: _.getYearFirstDayAndLastDay(this.equipmentYear)[0],
				// 用户日活
				userActiveEndDate: _.getYearFirstDayAndLastDay(this.userActiveYear)[1],
				userActiveStartDate: _.getYearFirstDayAndLastDay(this.userActiveYear)[0],
			});
			this.$axios
				.operateKanban(str)
				.then(res => {
					if (res.data.success) {
						const {
							lightmesUserCountVO,
							lightmesClientCountVO,
							lightmesYearDataVOS,
							lightmesAddMachineByYearVOS,
							lightmesAddClientByYearVOS,
							lightmesActiveUserVOS,
							lightmesMahcineOnLineByYearVOS,
							lightmesActiveClientVOS,
						} = res.data.data;
						// 用户量
						this.userCount = lightmesUserCountVO.count;
						this.addUserCount = lightmesUserCountVO.monthCount;
						// 客户量
						this.customerCount = lightmesClientCountVO.count;
						this.addCustomerCount = lightmesClientCountVO.monthCount;

						// 新增用户数 lightmesYearDataVOS
						const userOption = new Array(12).fill({ value: 0, itemStyle: { opacity: 0 } });
						let maxUser = 0;
						lightmesYearDataVOS &&
							lightmesYearDataVOS.length &&
							lightmesYearDataVOS.forEach(item => {
								const index = Number(item.month) - 1;
								userOption[index] = item.monthData;
								if (item.monthData > maxUser) maxUser = item.monthData;
							});
						this.userOption.series[0].data = userOption;
						this.userOption.series[1].data = userOption.map(item => (item = _.accAdd(Number(item), _.accMul(maxUser, 0.045))));
						this.userOption.series[2].data = userOption;

						// 新增设备数量

						const addEquipmentOption = new Array(12).fill({ value: 0, itemStyle: { opacity: 0 } });
						let maxEquipment = 0;
						lightmesAddMachineByYearVOS &&
							lightmesAddMachineByYearVOS.length &&
							lightmesAddMachineByYearVOS.forEach(item => {
								const index = Number(item.month) - 1;
								addEquipmentOption[index] = item.monthData;
								if (item.monthData > maxEquipment) maxEquipment = item.monthData;
							});
						this.addEquipmentOption.series[0].data = addEquipmentOption;
						this.addEquipmentOption.series[1].data = addEquipmentOption.map(
							item => (item = _.accAdd(Number(item), _.accMul(maxEquipment, 0.045))),
						);
						this.addEquipmentOption.series[2].data = addEquipmentOption;

						// 新增客户量

						const addCustomerOption = new Array(12).fill({ value: 0, itemStyle: { opacity: 0 } });
						let maxCustomer = 0;
						lightmesAddClientByYearVOS &&
							lightmesAddClientByYearVOS.length &&
							lightmesAddClientByYearVOS.forEach(item => {
								const index = Number(item.month) - 1;
								addCustomerOption[index] = item.monthData;
								if (item.monthData > maxCustomer) maxCustomer = item.monthData;
							});
						this.addCustomerOption.series[0].data = addCustomerOption;
						this.addCustomerOption.series[2].data = addCustomerOption;
						this.addCustomerOption.series[1].data = addCustomerOption.map(
							item => (item = _.accAdd(Number(item), _.accMul(maxCustomer, 0.045))),
						);

						// 活跃用户

						const userActiveOption = new Array(12).fill(0);
						lightmesActiveUserVOS &&
							lightmesActiveUserVOS.length &&
							lightmesActiveUserVOS.forEach(item => {
								const index = Number(item.month) - 1;
								userActiveOption[index] = item.monthData;
							});
						this.userActiveOption.xAxis[1].data = userActiveOption;
						this.userActiveOption.series[0].data = userActiveOption;

						// 在线设备数 lightmesMahcineOnLineByYearVOS

						const equipmentOption = new Array(12).fill(0);
						lightmesMahcineOnLineByYearVOS &&
							lightmesMahcineOnLineByYearVOS.length &&
							lightmesMahcineOnLineByYearVOS.forEach(item => {
								const index = Number(item.month) - 1;
								equipmentOption[index] = item.monthData;
							});
						this.equipmentOption.xAxis[1].data = equipmentOption;
						this.equipmentOption.series[0].data = equipmentOption;

						// 活跃客户

						const customerActiveOption = new Array(12).fill(0);
						lightmesActiveClientVOS &&
							lightmesActiveClientVOS.length &&
							lightmesActiveClientVOS.forEach(item => {
								const index = Number(item.month) - 1;
								customerActiveOption[index] = item.monthData;
							});
						this.customerActiveOption.series[0].data = customerActiveOption;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('operateKanban |' + error);
				});
		},

		resetChartsDb: _.debounce('resetCharts'),
		resetCharts() {
			//动态更新图表文字大小
			console.log('触发更新');
			this.$nextTick(() => {
				//字体大小重置(没人看懒得搞了)

				//触发图形更新
				this.getEchartsData();
			});
		},
		//全屏
		screenFull() {
			const flag = !this.fullScreenFlag;
			this.$emit('fullScreen', flag);
		},
		// 返回主页
		backToHome() {
			this.$router.replace({
				path: '/welCome',
				query: {
					queryId: this.userInfos?.adminUserVO.phoneNo,
				},
			});
			this.$emit('fullScreen', false);
		},
	},
};
</script>

<style lang="scss" scoped>
#operateView {
	width: 100%;
	// height: 100%;
	background: #04051a;
	color: #ffffff;
	padding: 0.5vw;
	box-sizing: border-box;
	position: relative;
	top: 0;
	left: 0;

	.header {
		text-align: center;
		position: relative;

		&-background {
			height: 5.3vh;
			line-height: 5.3vh;
			width: 100%;
			background: url('@/assets/img/view-title.svg') no-repeat center center;
			background-size: 100% 120%;
			margin: auto;
			position: relative;

			&-title {
				//font-weight: bolder;
				font-size: 3vh;
				letter-spacing: 3px;
				cursor: pointer;
			}
		}
	}

	.main {
		margin-top: 0.5vw;
		display: flex;
		justify-content: space-between;

		& > div {
			display: flex;
			flex-direction: column;
			//justify-content: space-between;
		}

		.main-title {
			display: flex;
			justify-content: space-between;
			height: 3.5vh;
			line-height: 3.5vh;
			padding: 0 0.5vw;
			padding-right: 0;
			font-family: PingFangSC-Regular, 'PingFang SC', sans-serif;
			font-weight: 400;
			font-style: normal;
			color: rgb(102, 255, 255);
			background: linear-gradient(90deg, rgb(2, 119, 198) 0%, rgb(2, 119, 198) 0%, rgb(16, 62, 112) 100%, rgb(16, 62, 112) 100%);
			border: none;
			border-radius: 0px;
			box-shadow: none;

			&-icon {
				cursor: pointer;
				font-size: 14px;
			}

			&-right {
				color: #fff;
				font-size: 16px;
			}
		}

		.main-left {
			margin-top: -2.5vh;
			width: 26.5%;

			& > div {
				min-height: 27vh;
				margin-bottom: 1vh;
				background-color: #040d25;
			}

			&-userCount {
				&-data {
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;
					margin-left: 20%;

					& > div {
						font-family: 'Arial Negreta', 'Arial Normal', Arial, sans-serif;
						font-size: 2vh;
						font-weight: 600;
					}
				}
			}
		}

		.main-center {
			width: 45%;

			& > div {
				min-height: 27vh;
				margin-bottom: 1vh;
				background-color: #040d25;
			}
		}

		.main-right {
			margin-top: -2.5vh;
			width: 26.5%;

			& > div {
				min-height: 27vh;
				margin-bottom: 1vh;
				background-color: #040d25;
			}
		}
	}
}
</style>
