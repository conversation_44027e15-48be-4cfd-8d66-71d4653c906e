/**
 * 路由配置
 */

// 动态导入子路由模块
const requireModule = require.context('./modules', false, /\.js$/);
const moduleRoutes = requireModule
	.keys()
	.map(file => requireModule(file).default)
	.flat();

// 懒加载主要页面组件
const Login = () => import('@/pages/Login'); // 登录
const Home = () => import('@/pages/Home'); // 主页
const welCome = () => import('@/pages/welCome'); // 欢迎页面
const NotFound = () => import('@/pages/NotFound'); // 未找到页面
const NetBreakTip = () => import('@/pages/NetBreakTip.vue'); // 断网提示
const QandA = () => import('@/pages/systemManagement/QandA/QandA.vue'); // 问答
const OnlineSupport = () => import('@/pages/systemManagement/OnlineSupport/OnlineSupport.vue'); // 在线问答系统
const BusinessMap = () => import('@/pages/viewManagement/BusinessMap.vue'); // 地图系统
// 路由配置
export const routes = [
	{
		path: '/login',
		name: 'login',
		component: Login,
		meta: {
			parentTitle: '树字OPS',
			title: '登录',
		},
	},
	{
		path: '/home',
		name: 'home',
		meta: {
			requireAuth: true, // 该路由添加该字段，表示进入该路由需要登陆的(包括子路由)
			parentTitle: '树字OPS',
			title: '首页',
		},
		component: Home,
		children: [
			{
				path: '/welCome',
				name: 'welCome',
				component: welCome,
				meta: {
					parentTitle: '树字OPS',
					title: '首页',
				},
			},
			{
				path: '/QandA',
				name: 'QandA',
				component: QandA,
				meta: {
					parentTitle: '树字OPS',
					title: '问答',
				},
			},
			{
				path: '/OnlineSupport',
				name: 'OnlineSupport',
				component: OnlineSupport,
				meta: {
					parentTitle: '树字OPS',
					title: '在线支持工作台',
				},
			},
			{
				path: '/BusinessMap',
				name: 'BusinessMap',
				component: BusinessMap,
				meta: {
					parentTitle: '树字OPS',
					title: '业务地图',
					noCache: true,
					isView: true,
				},
			},
			{
				path: '/notfound*',
				name: 'notfound',
				component: NotFound,
				meta: {
					parentTitle: '树字OPS',
					title: '页面不存在',
				},
			},
			...moduleRoutes, // 使用动态导入的模块
		],
	},
	{
		path: '/netBreakTip',
		name: 'NetBreakTip',
		component: NetBreakTip,
	},
	{
		path: '/',
		redirect: '/login',
	},
	// 捕获所有未匹配路由
	{
		path: '*',
		redirect: '/notfound',
	},
];

export default routes;
