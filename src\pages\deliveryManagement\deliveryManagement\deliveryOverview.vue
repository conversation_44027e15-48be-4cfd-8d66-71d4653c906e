<template>
	<div id="deliveryOverview" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<BaseLayout>
			<template #header>
				<el-select
					size="small"
					class="w-150"
					v-model="searchForm.projectType"
					placeholder="请选择项目类型"
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option v-for="item in projectTypeOptions" :key="item.id" :label="item.label" :value="item.id"> </el-option>
				</el-select>
				<el-select
					size="small"
					class="ml10 w-150"
					v-model="searchForm.status"
					placeholder="请选择项目状态"
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option label="已完成" :value="1"> </el-option>
					<el-option label="未完成" :value="0"> </el-option>
				</el-select>

				<el-select
					v-model="searchForm.salesman"
					class="ml10 mr10"
					size="small"
					placeholder="请选择业务顾问"
					multiple
					collapse-tags
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option v-for="item in salesmanList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
				</el-select>

				<el-select
					v-model="searchForm.implementList"
					size="small"
					placeholder="请选择实施顾问"
					multiple
					collapse-tags
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option v-for="item in implementList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
				</el-select>

				<el-input
					class="searchBox"
					size="small"
					clearable
					v-model="searchForm.query"
					placeholder="项目名称"
					@input="queryTableData(1)"
				></el-input>

				<el-checkbox-group v-model="searchForm.statusList" @change="queryTableData(1)">
					<el-checkbox v-for="(item, index) in statuss" :key="'color' + index" :label="item.id">
						<span class="fs-20" :class="[item.color]">●</span>
						<span>{{ item.status }}</span>
					</el-checkbox>
				</el-checkbox-group>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
				</div>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar"></div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="['startTime', 'endTime', 'modifyTime'].includes(item.colNo)"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 询盘编号 -->
							<!-- @click.native="openInquiryDetail(scope.row)"  -->
							<Tooltips
								v-else-if="item.colNo == 'number'"
								class="hover-green green"
								@click.native="openDetail(scope.row, '交付详情')"
								:cont-str="scope.row[item.colNo] || '未知'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<!-- 项目名称 -->
							<!-- <Tooltips
								v-else-if="item.colNo == 'projectName'"
								class="hover-green green"
								@click.native="openDetail(scope.row, '交付详情')"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips> -->

							<Tooltips
								v-else-if="item.colNo == 'projectType'"
								:cont-str="projectTypeMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 状态/类型 -->
							<div v-else-if="item.colNo == 'deliveryScheduleStateVOS'" class="project-steps-wrap">
								<el-steps class="project-steps" :space="100" :active="getActive(scope.row.stepsData)">
									<el-step
										v-for="(sItem, index) in scope.row.stepsData"
										:key="index"
										:status="['', 'wait', 'process', 'error', 'success'][sItem.status]"
										:class="(getStepClass(sItem), sItem.dmsiid ? 'steps-plan' : 'steps-stage')"
									>
										<template slot="title">
											<Tooltips class="max-w-100" :cont-str="sItem.stage || sItem.dsmStage || sItem.cfgName" :cont-width="100" />
										</template>

										<template slot="description">
											<!-- 项目阶段 -->
											<div v-if="sItem.dmsid">
												<Tooltips
													v-if="sItem.reportDate"
													:cont-str="dateFormat(sItem.reportDate, 'line')"
													class="max-w-100"
													:cont-width="100"
												/>
												<Tooltips
													v-else-if="sItem.complateMonth"
													:cont-str="dateFormat(sItem.complateMonth, 'YM')"
													class="max-w-100"
													:cont-width="100"
												/>
											</div>
											<!-- 阶段计划 -->
											<div v-else-if="sItem.dmsiid">
												<Tooltips class="max-w-100" :cont-str="dateFormat(sItem.plannedTime, 'MD')" :cont-width="100" />
											</div>
										</template>
									</el-step>
								</el-steps>
							</div>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<!-- <u-table-column label="" width="" align="center">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDialogRow(scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template>
              </u-table-column> -->
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import { projectTypeOptions, projectTypeMap } from '@/assets/js/contractSource';
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'deliveryOverview', //组件名应同路由名(否则keep-alive不生效)
	props: {
		twidList: { type: Array, default: () => [] },
		channelName: { type: Array, default: () => [] },
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			activeTab: 'deliveryOverview', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 25,
				currentPage: 1,
				pageSizes: [25, 50, 100],
			},
			statuss: [
				{ id: 1, status: '待开始', color: '' },
				{ id: 2, status: '正在进行', color: 'orange' },
				{ id: 3, status: '已延误', color: 'red' },
				{ id: 4, status: '已完成', color: 'green' },
			],
			tableColumn: [
				{ colName: '项目编号', colNo: 'number', align: 'left', width: '100' },
				{ colName: '项目名称', colNo: 'projectName', align: 'left', width: '100' },
				{ colName: '软件类型', colNo: 'projectType', align: 'left', width: '100' },
				{ colName: '业务顾问', colNo: 'salesmanName', align: 'left', width: '100' },
				{ colName: '实施顾问', colNo: 'implementName', align: 'left', width: '100' },
				{ colName: '交付情况', colNo: 'deliveryScheduleStateVOS', align: 'left', width: '' },
			],

			// 查询表单
			searchForm: {
				channelName: [],
				dmid: '',
				endDate: '',
				implementList: [],
				projectType: '',
				query: '',
				startDate: '',
				statusList: [],
				twidList: [],
				salesman: [],
				status: '',
			},
			userList: [], //渠道/代理人员列表

			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},

			projectTypeOptions,
			projectTypeMap,

			openMove: false, //打开组件
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）

		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		if (!this.isMoveCom) {
			this.searchForm = JSON.parse(window?.localStorage.getItem(this.$options.name + '_searchForm')) || this.searchForm;
			this.queryTableData('init');
		}
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 获取最后一个完成的状态4 的索引 如果没有则查询最后一个延误3的索引
		getActive(data) {
			const lastIndex3 = data?.findLastIndex(item => item?.status === 3);
			if (lastIndex3 !== -1) {
				return lastIndex3 + 1;
			}
			const lastIndex4 = data?.findLastIndex(item => item?.status === 4);
			return lastIndex4 + 1;
		},
		// 查询详情数据
		openDetail(row) {
			this.$emit('openDetail', row);
		},

		// el-step样式修改
		getStepClass: _.debounce(function () {
			// 步骤图标修改 性能不太行 后续在优化
			const errors = document.getElementsByClassName('el-step__icon-inner is-status el-icon-close');
			Array.from(errors).forEach(item => {
				item.classList.add('icon-third-icon-yanwu');
				item.classList.remove('el-icon-close');
			});
		}),
		// 查询渠道/代理下用户
		queryUserByTwids: _.debounce(function () {
			const str = JSON.stringify({
				twids: this.twidList,
				counselor: '',
			});
			this.$axios
				.selectSalesmanByTwids(str)
				.then(res => {
					if (res.data.success) {
						
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {

					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		// 进行状态
		getStatus(row, plan) {
			const tiemMap = {
				1: 'demandTime',
				2: 'functionTime',
				3: 'planTime',
				4: 'developmentTime',
				5: 'testTime',
				6: 'checkTime',
				7: 'prepareTime',
				8: 'publishTime',
			};
			const planeTime = row[tiemMap[row.status]]; //当前阶段计划时间
			const nowTime = this.$moment().startOf('day').valueOf(); //当前时间
			// && row[tiemMap[row.status]]
			if (row.status >= plan.status) {
				return 'success';
			} else if (row.status + 1 == plan.status) {
				if (planeTime && nowTime > planeTime) {
					return 'error';
				}
				return 'process';
			} else {
				return 'wait';
			}
		},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			window?.localStorage.setItem(this.$options.name + '_searchForm', JSON.stringify(this.searchForm));
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'deliverySchedule'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm, twidList: this.twidList, channelName: this.channelName }))
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(aItem => {
							aItem.stepsData = [];
							aItem.deliveryScheduleStateVOS?.forEach((bItem, bIndex) => {
								bItem.number = bIndex + 1;
								bItem.deliveryScheduleInfoVOS?.sort((a, b) => {
									return a?.sort - b?.sort;
								});

								aItem.stepsData = aItem.stepsData.concat(bItem.deliveryScheduleInfoVOS || []); //阶段计划
								// 1待开始 2正在进行 3已延误 4已完成
								if (bItem.reportDate && bItem.reportName && bItem.reportUrl) {
									bItem.status = 4; // 阶段状态(日期报告都存在时 即完工)
								} else {
									//stageStatus 1 待完成 2 已完成 3 逾期 （后台返回，项目阶段状态和阶段计划状态不一致）
									bItem.status = bItem.stageStatus == 2 ? 4 : bItem.stageStatus;
								}

								aItem.stepsData.push(bItem); //添加项目阶段
							});
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');

						this.sortChange(this.tableSort, true);
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
						(type == 'openMove' || type == 'init') && this.queryUserByTwids(); //初始时查询渠道人员
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? _.sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
				this.$emit('getTableOptions', sortedData); // 传递表格数据用于组件里上下页切换
			});
		},
	},
};
</script>

<style lang="scss" scoped>
#deliveryOverview {
	width: 100%;
	overflow: hidden;
	position: relative;
	&.moveToggle {
		.table-main {
			height: calc(100vh - 245px) !important;
		}
	}
}
</style>
<style lang="scss">
#deliveryOverview {
	.project-steps-wrap {
		// width: max-content;
		overflow-x: scroll;
		.project-steps {
			font-size: 12px !important;

			.el-step__title {
				font-size: 12px;
				line-height: 24px;
			}
			.is-process {
				color: #ff9800;
				border-color: #ff9800;
			}
		}
	}
}
</style>
