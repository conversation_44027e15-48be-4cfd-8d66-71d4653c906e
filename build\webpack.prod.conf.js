// 'use strict'
const path = require('path');
const utils = require('./utils');
const webpack = require('webpack');
const config = require('../config');
const { merge } = require('webpack-merge');
const baseWebpackConfig = require('./webpack.base.conf');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const OptimizeCSSPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const { WebUpdateNotificationPlugin } = require('@plugin-web-update-notification/webpack');

const WebpackBar = require('webpackbar');
const env = require('../config/prod.env');
const cdnConfig = [
	{ js: 'static/<EMAIL>' }, //  2.7版本（vue2最后一个版本）兼容性较好，旧版本有些写法不支持
	{ js: 'static/<EMAIL>' },
	{ js: 'static/<EMAIL>' },
	{ js: 'static/<EMAIL>' },
	{ js: 'static/<EMAIL>' },
	// { js: 'static/<EMAIL>', css: 'static/<EMAIL>' },
	{ js: 'static/<EMAIL>' },
];
const MiniCssExtractPluginConfig = new MiniCssExtractPlugin({
	filename: utils.assetsPath('css/[name].[contenthash].css'),
	chunkFilename: utils.assetsPath('css/[name].[contenthash].css'),
});
let webpackConfig = merge(baseWebpackConfig, {
	mode: 'production',
	stats: 'errors-only', //只在发生错误时输出
	target: ['web', 'es5'],
	module: {
		rules: utils.styleLoaders({
			sourceMap: config.build.productionSourceMap,
			extract: true,
			usePostCSS: true,
		}),
	},
	devtool: config.build.productionSourceMap ? config.build.devtool : false,
	output: {
		path: config.build.assetsRoot,
		filename: utils.assetsPath('js/[name].[chunkhash].js'),
		chunkFilename: utils.assetsPath('js/[id].[chunkhash].js'),
	},
	optimization: {
		minimize: true,
		minimizer: [
			// 使用esbuild进行代码压缩会保留注释，swc没有注释 详细配置看文档
			new TerserPlugin({
				minify: TerserPlugin.swcMinify,
				terserOptions: {},
			}),
			// 压缩CSS文件
			new OptimizeCSSPlugin(),
		],
		runtimeChunk: { name: 'runtime' },
		concatenateModules: true,
		splitChunks: {
			//plitChunks的chunks为 all是对所有的chunk都生效，默认只对async异步有效
			cacheGroups: {
				vendor: {
					test: /[\\/]node_modules[\\/]/,
					name: 'vendor',
					chunks: 'all',
					priority: -10,
				},
				'async-vendors': {
					test: /[\\/]node_modules[\\/]/,
					minChunks: 2,
					chunks: 'async',
					name: 'async-vendors',
				},
			},
		},
		moduleIds: 'deterministic',
	},
	plugins: [
		//用于注入全局变量，一般用在环境变量上。
		new webpack.DefinePlugin({
			'process.env': env,
		}),

		//将css样式提取出来
		MiniCssExtractPluginConfig,

		//将一个页面模板打包到dist目录下，默认都是自动引入js or css
		// see https://github.com/ampedandwired/html-webpack-plugin
		new HtmlWebpackPlugin({
			filename: config.build.index,
			template: 'index.html',
			inject: true,
			cdnConfig: cdnConfig, // cdn配置
			onlyCss: false, //dev下只加载css
			minify: {
				removeComments: true,
				collapseWhitespace: true,
				removeAttributeQuotes: true,
			},
			chunksSortMode: 'auto',
			favicon: path.resolve(__dirname, '../src/assets/szgcfavicon.ico'),
		}),

		//用于将文件拷贝到某个目录下
		new CopyWebpackPlugin({
			patterns: [
				{
					from: path.resolve(__dirname, '../static'),
					to: config.dev.assetsSubDirectory,
					globOptions: {
						dot: true,
						gitignore: true,
						ignore: ['.*'],
					},
				},
			],
		}),

		// 打包的进度条
		new WebpackBar({
			name: env.API_HOST, // 默认为webpack
			// color: "#85d", // 默认green，进度条颜色支持HEX
			basic: false, // 默认true，启用一个简单的日志报告器
			profile: false, // 默认false，启用探查器。
		}),

		// 发布新的web包，提示用户刷新
		new WebUpdateNotificationPlugin({
			versionType: 'build_timestamp',
			// silence: true, //如果这个版本不通知则打开
			logVersion: true, //打印版本
			// checkInterval: 0.5 * 60 * 1000, //轮询间隔 默认10分钟 : 10 * 60 * 1000 ms
			notificationConfig: {
				primaryColor: '#1e9d6f',
				placement: 'topRight', //显示位置
			},
			notificationProps: {
				title: ' 📢 系统升级通知',
				description: '检测到当前系统版本已更新，请您保存重要内容后再刷新浏览器，以确保您的正常使用。',
				buttonText: '立即刷新',
				dismissButtonText: '稍后再手动刷新',
			},
		}),
	],
	externals: {
		// 引入外链拒绝XXX被打包进来，目的是减小打包后的体积
		vue: 'Vue',
		'vue-router': 'VueRouter',
		vuex: 'Vuex',
		axios: 'axios',
		// 'element-ui': 'ELEMENT',
		echarts: 'echarts',
		// 'jquery':'jQuery'
	},
});

// 是否开启gzip
if (config.build.productionGzip) {
	const CompressionWebpackPlugin = require('compression-webpack-plugin');

	webpackConfig.plugins.push(
		new CompressionWebpackPlugin({
			filename: '[path][base].gz[query]',
			algorithm: 'gzip',
			test: new RegExp('\\.(' + config.build.productionGzipExtensions.join('|') + ')$'),
			threshold: 10240,
			minRatio: 0.8,
		}),
	);
}

// 是否开启打包分析的开关,查看各个文件或插件的大小
if (config.build.bundleAnalyzerReport) {
	const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
	webpackConfig.plugins.push(new BundleAnalyzerPlugin());
}

// 是否开启speed分析的开关,查看各个plugins和Loaders过程的耗时
if (config.build.buildSpeedReport) {
	const index = webpackConfig.plugins.findIndex(plugin => {
		return plugin === MiniCssExtractPluginConfig;
	});
	webpackConfig.plugins.splice(index, 1);
	const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
	const smp = new SpeedMeasurePlugin();
	webpackConfig = smp.wrap(webpackConfig);
	webpackConfig.plugins.push(MiniCssExtractPluginConfig);
	const { VueLoaderPlugin } = require('vue-loader');
	webpackConfig.plugins.push(new VueLoaderPlugin());
}

module.exports = webpackConfig;
