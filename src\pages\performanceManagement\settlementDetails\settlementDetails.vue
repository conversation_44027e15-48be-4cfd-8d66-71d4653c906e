<template>
	<div class="settlementDetails">
		<!-- 查询业绩分配明细-收款视图 -->
		<DetailsByRecord
			ref="DetailsByRecord"
			:searchForm="searchForm"
			@openAllocationPlan="openAllocationPlan"
			@close="queryTableData"
		/>
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 合同业绩分配 -->
		<AllocationPlan ref="AllocationPlan" @close="queryTableData" />
		<!-- 结算单打印 -->
		<SettlementPrinter ref="SettlementPrinter" :searchForm="this.searchForm" />

		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="业绩结算明细" name="settlementDetails">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">收款月份</span>
						<el-date-picker
							size="small w-150"
							v-model="searchForm.dateStart"
							type="month"
							value-format="timestamp"
							placeholder="不限"
							@change="queryTableData(1)"
							:default-value="new Date()"
							clearable
						>
						</el-date-picker>

						<span class="search-label">分配对象</span>
						<el-select
							size="small"
							v-model="searchForm.commissionObjectId"
							placeholder="分配对象"
							popper-class="select-column-3"
							filterable
							clearable
							@change="queryTableData(1)"
						>
							<el-option v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
						</el-select>
						<el-checkbox v-model="searchForm.showOnlyManuallyCommission" @change="queryTableData(1)">
							仅显示手工结算记录
						</el-checkbox>
						<div class="ml-auto">
							<el-button v-if="isPerformanceManager" type="text" class="el-icon-s-order" @click="openDetail('queryByRecord')">
								按收款记录检查
							</el-button>
							<el-button v-if="isPerformanceManager" type="text" class="el-icon-s-claim" @click="openAwards">
								生成达标奖励
							</el-button>
							<el-button v-if="isPerformanceManager" type="text" class="el-icon-s-claim" @click="openBpuSettlement">
								BPUL师带徒结算
							</el-button>
							<el-button v-if="isPerformanceManager" type="text" class="el-icon-printer" @click="openPrint">
								合伙人结算打印
							</el-button>
							<el-button v-if="isPerformanceManager" type="text" class="icon-third-bt_newdoc" @click="dialogEdit = true">
								添加结算项
							</el-button>
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<SettlementList ref="SettlementList" :searchForm="searchForm" @openAllocationPlan="openAllocationPlan" />
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>

		<el-dialog width="666px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">添加结算项</span>
			<el-form
				class="multiColumn-form"
				:model="editForm"
				:rules="formRules"
				label-width="100px"
				label-position="left"
				@submit.native.prevent
			>
				<el-form-item label="分配类型" prop="allocateType">
					<el-radio-group v-model="editForm.allocateType" @change="changeAllocateType">
						<el-radio :label="0">本部</el-radio>
						<el-radio :label="1">合伙人</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="分配对象" prop="auid">
					<el-select v-model="editForm.auid" placeholder="请选择分配对象" popper-class="select-column-3" filterable clearable>
						<el-option v-for="item in allocateOptions" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="结算类别" prop="settlementType">
					<el-select
						v-model="editForm.settlementType"
						placeholder="请选择结算类别"
						popper-class="select-column-3"
						filterable
						clearable
					>
						<el-option label="补贴" value="补贴"> </el-option>
						<el-option label="话费" value="话费"> </el-option>
						<el-option label="运费" value="运费"> </el-option>
						<el-option label="其他" value="其他"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="分配提要" prop="allocateSummary">
					<el-input v-model="editForm.allocateSummary" placeholder="请输入分配提要" clearable></el-input>
				</el-form-item>
				<el-form-item label="结算金额" prop="allocateAmount">
					<el-input v-model="editForm.allocateAmount" placeholder="请输入结算金额" clearable></el-input>
				</el-form-item>
				<el-form-item label="结算日期" prop="settlementDate">
					<el-date-picker
						v-model="editForm.settlementDate"
						type="date"
						placeholder="请输入结算日期"
						format="yyyy-MM-dd"
						value-format="timestamp"
					>
					</el-date-picker>
				</el-form-item>
				<el-form-item label="备注" prop="allocateMemo" class="W100 mr0">
					<el-input
						v-model="editForm.allocateMemo"
						placeholder="请输入备注"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>
			</el-form>
			<div v-show="editForm.allocateType == 1" class="red fs-12 text-right">
				* 结算金额【本部向分配对象支出：正值 | 分配对象向本部支出：负值】
			</div>
			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEdit">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable'; //导出组件
import AllocationPlan from '@/pages/deliveryManagement/contractManagement/allocationPlan.vue';
import SettlementList from '../SettlementList';
import DetailsByRecord from './settlementDetailsByRecord';
import SettlementPrinter from './SettlementPrinter';
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		SettlementList,
		AllocationPlan,
		DetailsByRecord,
		SettlementPrinter,
	},
	name: 'settlementDetails', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'settlementDetails', //激活tab页
			userList: [], //用户列表

			// 查询表单
			searchForm: {
				dateEnd: '',
				dateStart: '',

				source: '',
				userName: '',
				commissionObjectId: '',
				commissionObjectName: '',
				showOnlyManuallyCommission: '',
				// 其他...
			},

			// 弹窗相关
			dialogEdit: false,
			editForm: {
				allocateAmount: '',
				allocateMemo: '',
				allocateSummary: '',
				auid: '',
				settlementDate: new Date().getTime(),
				settlementType: '',

				allocateType: '',
			},
			formRules: {
				allocateType: [{ required: true, message: '请选择分配类型', trigger: 'blur' }],
				auid: [{ required: true, message: '请选择分配对象', trigger: 'blur' }],
				allocateAmount: [{ required: true, message: '请输入结算金额', trigger: 'blur' }],
				settlementType: [{ required: true, message: '请输入结算类别', trigger: 'blur' }],
				allocateSummary: [{ required: true, message: '请输入分配提要', trigger: 'blur' }],
				settlementDate: [{ required: true, message: '请输入结算日期', trigger: 'blur' }],
			},
			allocateOptions: [], // 分配对象列表
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 只有业绩管理员可显示
		isPerformanceManager() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '业绩管理员') || false;
		},
		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
		// 合伙人列表(userName 8开头)
		partnerList() {
			return this.userList.filter(item => item.userName.startsWith('8'));
		},
		// 本部业务人员列表
		companyList() {
			return this.userList.filter(
				item => item.userName.startsWith('6') && (item.userLabel?.includes('业务') || item.userLabel?.includes('实施')),
			);
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		// 当前月的第一天
		this.searchForm.dateStart = this.$moment().startOf('month').valueOf();
		this.queryUserByTwids();
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		changeAllocateType() {
			this.allocateOptions = this.editForm.allocateType == 0 ? this.companyList : this.partnerList;
			this.editForm.auid = '';
		},
		// 打印
		async openPrint() {
			// return this.$message.warning('正在开发中，暂未开放...');
			const API = 'selectDeliverStagePartnerSettlementPrintTable';
			try {
				const res = await this.$axios[API](
					JSON.stringify({
						beginDate: this.$moment(this.searchForm.dateStart).startOf('month').valueOf(), //传入的月份第一天 00:00:00
						endDate: this.$moment(this.searchForm.dateStart).endOf('month').valueOf(), //传入的月份最后一天 23:59:59
					}),
				);
				if (res.data.success) {
					// 数据处理
					// const PRINT_DATA = res.data.data.map(item => {
					// 	console.log(item, item['_data']);
					// 	const newRow = item['_data'];
					// 	return newRow;
					// });
					this.$refs.SettlementPrinter.showDetailCom(res.data.data);
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		closeDialog() {
			this.dialogEdit = false;
			this.editForm = _.resetValues(this.editForm);
			this.editForm.settlementDate = this.$moment().valueOf();
		},

		async saveEdit() {
			if (_.checkRequired(this.editForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}
			const API = 'addDeliverStageCommissionDetailForSettlement';
			try {
				const res = await this.$axios[API](JSON.stringify(this.editForm));
				if (res.data.success) {
					this.dialogEdit = false;
					this.queryTableData(1);
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 生成达标奖励
		openAwards() {
			if (!this.searchForm.dateStart) return this.$message.warning('请选择收款月份！');
			const month = this.$moment(this.searchForm.dateStart).format('YYYY年MM月');
			this.searchForm.dateEnd = this.$moment(this.searchForm.dateStart).endOf('month').valueOf(); // 当前月份最后一天时间戳
			this.$confirm(`执行此操作后将会刷新【${month}】的达标奖励, 是否继续?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'refreshMonthlyAwards';
					try {
						const res = await this.$axios[API](JSON.stringify({ ...this.searchForm }));
						if (res.data.success) {
							this.queryTableData(1);
							this.$succ(res.data.message);
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 师带徒结算
		openBpuSettlement() {
			if (!this.searchForm.dateStart) return this.$message.warning('请选择收款月份！');
			const month = this.$moment(this.searchForm.dateStart).format('YYYY年MM月');
			this.searchForm.dateEnd = this.$moment(this.searchForm.dateStart).endOf('month').valueOf(); // 当前月份最后一天时间戳
			this.$confirm(`执行此操作后将会刷新【${month}】的师带徒结算, 是否继续?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'bpulLeadBpuCommissionMonthly';
					try {
						const res = await this.$axios[API](
							JSON.stringify({
								startTime: this.searchForm.dateStart,
								endTime: this.searchForm.dateEnd,
							}),
						);
						if (res.data.success) {
							this.queryTableData(1);
							this.$succ(res.data.message);
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 打开明细（查询业绩分配明细-收款视图）
		openDetail() {
			if (!this.searchForm.dateStart) return this.$message.warning('请选择收款月份！');
			this.$refs.DetailsByRecord.queryTableData(1);
		},
		// 打开明细
		openAllocationPlan(type, row) {
			this.$refs.AllocationPlan.showDetailCom(type, row);
		},
		// 查询渠道/代理下用户
		queryUserByTwids: _.debounce(function () {
			this.userList = this.isSuperAdmin
				? [
						{ auid: 0, userName: '代扣' },
						{ auid: 1, userName: '公司' },
						{ auid: 2, userName: '定制' },
					]
				: []; //除开超级管理员外，其他人不能看到这三项
			const str = JSON.stringify({
				// twids: this.searchForm.twidList,
				twids: [],
				counselor: '',
			});
			this.$axios
				.selectSalesmanByTwids(str)
				.then(res => {
					if (res.data.success) {
						//
						this.userList = res.data.data?.concat(this.userList);
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData(type) {
			this.$refs.SettlementList.queryTableData(type);
		},
	},
};
</script>

<style lang="scss" scoped>
.settlementDetails {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
