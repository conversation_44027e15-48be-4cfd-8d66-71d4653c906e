<!-- 
 	带有上下切换功能的日期选择组件 
	注意：
	BUG：饿了么UI组件库有个bug，显示类型为week，value-format绑定值为timestamp 的时候，绑定的值显示乱码：‘ti0e0tam0p’
		解决方案：显示类型为week 的时候，value-format设为空，返回日期对象格式再转换为timestamp’
-->
<template>
	<div class="SwitchDatePicker">
		<!-- 上一个日期 -->
		<el-button :disabled="isPrevDisabled" type="text" class="el-icon-caret-left switch-btn prev" @click="onSwitch(-1)">
		</el-button>

		<el-date-picker
			ref="datePicker"
			v-model="innerValue"
			:type="type"
			:size="size"
			:format="format"
			:value-format="valueFormat"
			:placeholder="placeholder"
			:clearable="clearable"
			:disabled="disabled"
			:picker-options="pickerOptions"
			@change="onChange"
		>
		</el-date-picker>

		<!-- 下一个日期 -->
		<el-button :disabled="isNextDisabled" type="text" class="el-icon-caret-right switch-btn next" @click="onSwitch(1)">
		</el-button>
	</div>
</template>

<script>
export default {
	name: 'SwitchDatePicker',
	props: {
		// 日期值
		value: {
			type: [Date, String, Number],
			default: null,
		},
		// 日期类型：date/week/month/year
		type: {
			type: String,
			default: 'date',
			validator: value => ['date', 'week', 'month', 'year'].includes(value),
		},
		// 大小
		size: {
			type: String,
			default: 'small',
		},
		// 显示格式
		format: {
			type: String,
			default: 'yyyy-MM-dd',
		},
		// 值格式
		valueFormat: {
			type: String,
			default: 'timestamp',
		},
		// 占位符
		placeholder: {
			type: String,
			default: '请选择',
		},
		// 是否可清空
		clearable: {
			type: Boolean,
			default: true,
		},
		// 是否禁用
		disabled: {
			type: Boolean,
			default: false,
		},
		// 最小日期
		minDate: {
			type: [Date, String, Number],
			default: null,
		},
		// 最大日期
		maxDate: {
			type: [Date, String, Number],
			default: null,
		},
		options: {
			type: Object,
			default: () => ({}),
		},
	},

	data() {
		return {
			innerValue: this.value,
			// 默认选项
			defaultOptions: {
				firstDayOfWeek: 1,
				disabledDate: time => {
					if (this.minDate && this.maxDate) {
						return time.getTime() < this.minDate || time.getTime() > this.maxDate;
					}
					if (this.minDate) {
						return time.getTime() < this.minDate;
					}
					if (this.maxDate) {
						return time.getTime() > this.maxDate;
					}
					return false;
				},
			},
		};
	},

	computed: {
		// 合并默认选项和用户选项
		pickerOptions() {
			return {
				...this.defaultOptions,
				...this.options,
			};
		},

		// 是否禁用上一个日期
		isPrevDisabled() {
			if (!this.innerValue) return true;
			const prevDate = this.switchDate(-1).getTime();
			return this.minDate && prevDate < this.minDate;
		},
		// 是否禁用下一个日期
		isNextDisabled() {
			if (!this.innerValue) return true;
			const nextDate = this.switchDate(1).getTime();
			return this.maxDate && nextDate > this.maxDate;
		},
	},

	watch: {
		value: {
			handler(val) {
				this.innerValue = val;
			},
			immediate: true,
		},
	},

	methods: {
		// 处理日期变化
		onChange(val) {
			// 修复Element UI的bug：当type为year/week且valueFormat为timestamp时可能输出格式错误
			if (this.valueFormat === 'timestamp') {
				// 如果val是日期对象，直接转换为时间戳
				if (val instanceof Date) {
					val = val.getTime();
				}
				// 如果val是字符串(如ISO格式)，先转为日期对象再转为时间戳
				else if (typeof val === 'string' && val) {
					val = new Date(val).getTime();
				}
			}

			this.$emit('input', val);
			this.$emit('change', val);
		},

		// 通用日期切换函数
		switchDate(direction = 1) {
			const date = new Date(this.innerValue);
			const operations = {
				year: () => date.setFullYear(date.getFullYear() + direction),
				month: () => date.setMonth(date.getMonth() + direction),
				week: () => date.setDate(date.getDate() + 7 * direction),
				date: () => date.setDate(date.getDate() + direction),
				default: () => date.setDate(date.getDate() + direction),
			};

			(operations[this.type] || operations.default)();
			return date;
		},

		// 处理日期切换
		onSwitch(direction) {
			const switchName = direction == 1 ? 'Next' : 'Prev';
			if (this[`is${switchName}Disabled`]) return; // 如果禁用，则不进行切换
			this.onChange(this.switchDate(direction));
		},

		// 聚焦日期选择器
		focus() {
			this.$refs.datePicker.focus();
		},
	},
};
</script>

<style lang="scss">
.SwitchDatePicker {
	display: inline-flex;
	align-items: center;
	width: 10vw;
	min-width: 160px;
	.switch-btn {
		padding: 0;
		margin: 0;
		font-size: 18px !important;
		&.prev {
			padding-right: 5px !important;
		}

		// &.next {
		//   margin-left: 5px;
		// }

		&:disabled {
			color: #c0c4cc !important;
			cursor: not-allowed !important;
		}
	}

	// 覆盖 el-date-picker 的样式
	.el-date-editor {
		width: auto;
	}
}
</style>
