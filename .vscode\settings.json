{
	// 启用ESLint作为已验证文件的格式化程序，可方便快捷键
	"eslint.format.enable": true, 
	"editor.formatOnSave": false,
	"stylelint.enable": true,
	"editor.codeActionsOnSave": {
		"source.fixAll.stylelint": "explicit"
	},
	"stylelint.validate": ["css", "less", "postcss", "scss", "vue", "sass", "html"],
	"files.eol": "\n",
	"files.exclude": {	},
	"[vue]": {
		"editor.codeActionsOnSave": {
			"source.fixAll.eslint": "explicit",
			"source.fixAll.stylelint": "explicit"
		},
		"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
	},
	"[typescript]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[json]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[jsonc]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[javascript]": {
		"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
	},
	"[typescriptreact]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[scss]": {
		"editor.defaultFormatter": "rvest.vs-code-prettier-eslint"
	},
	"[html]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[markdown]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[less]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},

	"i18n-ally.localesPaths": ["src/languages"],
	"workbench.colorCustomizations": {
		"activityBar.background": "#2C2E26",
		"titleBar.activeBackground": "#3D4135",
		"titleBar.activeForeground": "#FAFAF9"
	}
}
