<template>
	<div class="ContractStage table-wrapper">
		<p class="detail-content-title">
			<span class="flex-align-center W100">
				<span>项目阶段</span>
				<el-button
					:disabled="isUpdate || !isAllocation"
					type="primary"
					size="small"
					class="ml20"
					@click="$emit('openAllocation', '业绩分配', detailForm)"
				>
					{{ isUpdate ? '请先保存信息' : '业绩分配计划' }}
				</el-button>
				<el-tooltip effect="light" placement="top">
					<div slot="content">
						<p>业绩分配计划说明：</p>
						<p>1、业绩分配时，合同类型不能为空白，请再次点击保存基本信息，系统将会自动生成合同类型</p>
						<p>2、为保证业绩分配准确，请仔细核对阶段合同金额，标品数量等信息与合同文件一致(红框必填)，否则会影响审核</p>
						<p>3、已审核的合同不允许撤回再修改，因此在提交前请仔细核对合同信息，如有问题请联系管理员</p>
					</div>
					<i class="el-icon-warning-outline ml10 pointer"></i>
				</el-tooltip>
			</span>
		</p>

		<div class="flex-align-center W100">
			<div class="red">
				<span v-for="(item, index) in totalMoneyList" :key="index">
					{{ item?.userName }}{{ `（￥${item.commissions ? item.commissions.toFixed(2) : 0.0}）` }}
				</span>
				<span v-show="totalCommissions">共计：{{ `￥${totalCommissions}` }}</span>
			</div>
			<span class="ml-auto" v-show="!isSubmitApprove">
				<el-button v-if="detailForm.projectType && detailForm.dmid" type="text" class="ml-auto p0" @click="importTemplate"
					>添加编辑模板
				</el-button>
				<el-button class="p0" type="text" @click="addRow">添加一行</el-button>
			</span>
		</div>

		<u-table
			class="table-main detail-table W100 input-border-none"
			ref="refTable"
			row-key="dmsid"
			max-height="600px"
			:data="detailForm.deliverStageManagementVOS || []"
			:row-style="{ height: '0' }"
			:cell-style="{ padding: '0 0', borderBottom: '1px solid #e9e9e9' }"
			:header-cell-style="{ border: 'transparent', padding: '10px 0 !important' }"
			show-summary
			:summary-method="summaryMethod"
			show-header-overflow="title"
		>
			<u-table-column label="序号" type="index" align="center" width="50"> </u-table-column>
			<u-table-column
				v-for="item in tableColumn"
				:key="'colCurr' + item.colNo"
				:label="item.colName"
				:prop="item.colNo"
				:align="item.align"
				:width="item.width"
			>
				<template slot-scope="scope">
					<div v-if="item.colNo == 'collectionAmount'">
						<Tooltips :cont-str="scope.row[item.colNo]" :cont-width="scope.column.width || scope.column.realWidth"> </Tooltips>
					</div>
					<div v-else-if="item.colNo == 'customizedServiceAmount'">
						<!-- 合伙人定制金额自动计算 -->
						<Tooltips
							v-if="shouldBP1220"
							:cont-str="scope.row[item.colNo]"
							:cont-width="scope.column.width || scope.column.realWidth"
						>
						</Tooltips>
						<!-- 非合伙人且有定制人天定制金额手动输入 -->
						<el-input
							v-else
							:disabled="!!scope.row.collectionAmount || isSubmitApprove"
							:class="
								detailForm.customizedServiceDevelopDays && scope.row.customizedServiceAmount === '' ? 'input-border-red' : ''
							"
							@input="scope.row[item.colNo] = scope.row[item.colNo] ? scope.row[item.colNo] : 0"
							@change="updateStage(scope.$index)"
							v-model="scope.row[item.colNo]"
							size="small"
							clearable
						>
						</el-input>
					</div>
					<!-- 审批状态 0 提交待批 1 通过 2 驳回 -->
					<div v-else-if="item.colNo == 'complateMonth'" class="flex-align-center">
						<el-date-picker
							:class="scope.row[item.colNo] ? '' : 'input-border-red'"
							:disabled="scope.row.status == 0 || !!scope.row.reportName || isSubmitApprove"
							@change="updateStage(scope.$index)"
							size="small"
							v-model="scope.row[item.colNo]"
							type="month"
							placeholder="选择月"
							:clearable="false"
						>
						</el-date-picker>
						<el-button type="text" size="mini" v-if="scope.row.status == 0" style="color: #f9a825"> 待审 </el-button>
					</div>
					<!-- 完工报告 -->
					<div v-else-if="item.colNo == 'reportName'" class="flex-align-center">
						<FilePopover v-if="scope.row.reportName" trigger="click" :url="scope.row.reportUrl" :content="scope.row.reportName" />
					</div>
					<!-- 支付比例 -->
					<!-- <div v-else-if="item.colNo == 'ratio'" class="input_textR flex-center"> -->
					<div v-else-if="item.colNo == 'ratio'" class="text-right">
						<!-- <el-input
							:disabled="!!scope.row.collectionAmount || isSubmitApprove"
							@input="scope.row[item.colNo] = scope.row[item.colNo] ? scope.row[item.colNo] : 0"
							@change="updateStage(scope.$index)"
							v-model="scope.row[item.colNo]"
							size="small"
							clearable
						>
						</el-input>
						<span class="ml5">%</span> -->

						<Tooltips
							v-if="scope.row.amount && sumAmount"
							:cont-str="bigMul(bigDiv(scope.row.amount, sumAmount, 6), 100, 2) + '%'"
							:cont-width="scope.column.width || scope.column.realWidth"
						>
						</Tooltips>
					</div>
					<!-- 阶段 -->
					<div v-else-if="item.colNo == 'stage'">
						<el-input
							:maxlength="6"
							:disabled="isSubmitApprove"
							:class="scope.row[item.colNo] ? '' : 'input-border-red'"
							@change="updateStage(scope.$index)"
							v-model="scope.row[item.colNo]"
							size="small"
							clearable
						>
						</el-input>
					</div>
					<!-- 标准 -->
					<div v-else-if="item.colNo == 'standard'">
						<el-input
							:disabled="isSubmitApprove"
							:class="scope.row[item.colNo] ? '' : 'input-border-red'"
							type="textarea"
							:autosize="{ minRows: 1, maxRows: 4 }"
							@change="updateStage(scope.$index)"
							v-model="scope.row[item.colNo]"
							size="small"
							clearable
						>
						</el-input>
					</div>
					<div v-else class="input_textR">
						<el-input
							:disabled="!!scope.row.collectionAmount || isSubmitApprove"
							:class="
								!scope.row.collectionAmount && (scope.row[item.colNo] === null || scope.row[item.colNo] === '')
									? 'input-border-red'
									: ''
							"
							@input="scope.row[item.colNo] = scope.row[item.colNo] ? scope.row[item.colNo] : 0"
							@change="updateStage(scope.$index)"
							v-model="scope.row[item.colNo]"
							size="small"
							clearable
						>
						</el-input>
					</div>
				</template>
			</u-table-column>
			<!-- 删除 -->
			<u-table-column label="" width="40" align="center">
				<template slot-scope="scope">
					<el-button
						v-show="!scope.row.reportName && !isSubmitApprove"
						type="text"
						class="el-icon-delete fs-14 color-999"
						@click="delRow(scope.row, scope.$index)"
					></el-button>
				</template>
			</u-table-column>
			<!-- 阶段状态 -->
			<u-table-column label="" width="65" align="">
				<template slot-scope="scope">
					<el-tooltip :content="'阶段状态: ' + (scope.row.closed == 1 ? '关闭' : '开启')" placement="top">
						<el-switch
							v-model="scope.row.closed"
							active-color="#13ce66"
							inactive-color="#ff4949"
							:active-value="0"
							:inactive-value="1"
							@change="updateClose(scope.row)"
						>
						</el-switch>
					</el-tooltip>
				</template>
			</u-table-column>
		</u-table>
	</div>
</template>
<script>
import { deepClone, dateFormat } from '@/util/tool';
import { bigAdd, bigDiv, bigMul } from '@/util/math';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';

export default {
	name: 'ContractStage',
	props: {
		detailForm: Object,
		isUpdate: Boolean,
		isAllocation: Boolean,
	},
	components: { FilePopover },
	data() {
		return {
			totalMoneyList: [], //查询合同交付佣金人员级别汇总
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 绩效合计
		totalCommissions() {
			return this.totalMoneyList?.reduce((a, b) => bigAdd(a, b.commissions, 2), 0) || 0;
		},
		// 根据合同类型对列区分
		tableColumn() {
			const columns = [
				{ colName: '阶段', colNo: 'stage', align: 'left', width: '180' },
				{ colName: '完工标准', colNo: 'standard', align: 'left', width: '380' },
				{ colName: '合同金额(元)', colNo: 'amount', align: 'center', width: '150' },
				{ colName: '支付比率', colNo: 'ratio', align: 'right', width: '120', type: 'text' },
				// { colName: '硬件金额(元)', colNo: 'hardwareAmount', align: 'center', width: '' },
				// { colName: '软件金额(元)', colNo: 'softwareAmount', align: 'center', width: '' },
				// { colName: '标品数量', colNo: 'standardProductCount', align: 'center', width: '120' },
				{ colName: '定制金额(元)', colNo: 'customizedServiceAmount', align: 'right', width: '120', type: 'text' },
				{ colName: '居间费(元)', colNo: 'mediAmount', align: 'center', width: '120' },
				{ colName: '预计完工/收款月份', colNo: 'complateMonth', align: 'left', width: '200' },
				{ colName: '业务完工凭证', colNo: 'reportName', align: 'left', width: '' },
				{ colName: '回款金额(元)', colNo: 'collectionAmount', align: 'right', width: '' },
			];
			if (this.detailForm?.contractType == 1) {
				// 合伙人去掉居间费
				return columns.filter(i => i.colNo != 'mediAmount');
			}
			return columns;
		},

		// 已提交审核（不允许修改）
		isSubmitApprove() {
			return this.detailForm.submitApproveStatus === 1;
		},
		// 合同金额合计
		sumAmount() {
			if (this.detailForm.deliverStageManagementVOS?.length) {
				return this.detailForm.deliverStageManagementVOS?.reduce((a, b) => bigAdd(a, b.amount, 6), 0) || 0;
			}
			return 0;
		},

		// 合伙合同是否应该走 BP1220 分成保底制 结算方案
		shouldBP1220() {
			return this.detailForm.contractType == 1 && this.detailForm.businessPartnerShareRatioMode;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 一键导入展开详情阶段模板
		async importTemplate() {
			if (!this.detailForm.projectType || !this.detailForm.dmid) return;
			const dmid = this.detailForm.dmid;
			// 1 OEE版本（3个阶段） 2 标品MES（3个阶段） 3 标品MES+定制（5个阶段） 4 完全定制（5个阶段） 5硬件
			const templateMap = {
				1: {
					stages: ['合同签署', '项目验收'],
					standards: ['合同签署', '自系统正式被用户使用满 90 天，无障碍性问题存在'],
				},
				2: {
					stages: ['合同签署', '系统上线运行', '项目验收'],
					standards: ['合同签署', '用户开始使用正式的系统满30天', '自系统正式被用户使用满 90 天，无障碍性问题存在'],
				},
				3: {
					stages: ['合同签署', '方案设计', '系统实现', '系统上线运行', '上线支持'],
					standards: [
						'合同签署',
						'原型设计的方案被甲方项目经理签字确认',
						'甲方项目经理签署用户接受测试报告',
						'系统部署到本地，用户开始使用正式的系统满30天',
						'自系统正式被用户使用满 90 天，无障碍性问题存在',
					],
				},
				4: {
					stages: ['合同签署', '方案设计', '系统实现', '系统上线运行', '上线支持'],
					standards: [
						'合同签署',
						'原型设计的方案被甲方项目经理签字确认',
						'甲方项目经理签署用户接受测试报告',
						'系统部署到本地，用户开始使用正式的系统满30天',
						'自系统正式被用户使用满 90 天，无障碍性问题存在',
					],
				},
				5: {
					stages: ['合同签署'],
					standards: ['合同签署'],
				},
			};
			const { stages, standards } = templateMap[this.detailForm.projectType];
			try {
				for (let index = 0; index < stages?.length; index++) {
					const stage = stages[index];
					const standard = standards[index];

					const stageData = {
						amount: '',
						collectionAmount: '',
						complateMonth: this.$moment().startOf('month').valueOf(), //当前月的第一天
						dmid,
						dmsid: '',
						hardwareAmount: '',
						standardProductCount: '',
						mediAmount: 0,
						ratio: '',
						reportName: '',
						reportUrl: '',
						softwareAmount: '',
						customizedServiceAmount: '',
						stage,
						standard,
					};

					const str = JSON.stringify(stageData);
					const res = await this.$axios.addOrUpdateStage(str);
				}
			} catch (error) {
				console.log('addOrUpdateStage |' + error);
			}

			await this.$emit('queryDetailData', this.detailForm.dmid, 'updateStage');
		},
		// 添加行
		addRow() {
			const dmid = this.detailForm.dmid;
			this.detailForm.deliverStageManagementVOS.push({
				amount: '',
				collectionAmount: '',
				complateMonth: this.$moment().startOf('month').valueOf(), //当前月的第一天
				dmid,
				dmsid: '',
				hardwareAmount: '',
				standardProductCount: '',
				mediAmount: 0,
				ratio: '',
				reportName: '',
				reportUrl: '',
				softwareAmount: '',
				customizedServiceAmount: '',
				stage: '',
				standard: '',
				closed: 0, // 阶段是否已关闭，0 未关闭，1 已关闭
				closeMemo: '', //关闭备注
			});
			this.updateStage(this.detailForm.deliverStageManagementVOS?.length - 1, 'add');
		},
		// 删除行
		delRow(row, index) {
			const { dmsid } = this.detailForm.deliverStageManagementVOS[index];
			this.$confirm('您正在执行删除操作,删除后不可恢复！', '重要提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteStage(JSON.stringify({ dmsid }))
						.then(res => {
							if (res.data.success) {
								this.detailForm.deliverStageManagementVOS.splice(index, 1);
								this.$succ('操作成功!');
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteStage |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 新增/修改阶段
		updateStage(index, type) {
			this.$axios
				.addOrUpdateStage(JSON.stringify(this.detailForm.deliverStageManagementVOS[index]))
				.then(res => {
					if (res.data.success) {
						if (type == 'add' || type == 'del') {
							this.$emit('queryDetailData', this.detailForm.dmid, 'updateStage');
						}
					} else {
						this.$emit('queryDetailData', this.detailForm.dmid, 'updateStage');
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addOrUpdateStage |' + error);
				});
		},
		// 开启或关闭合同阶段状态
		async updateClose(row) {
			this.detailFormCopy = deepClone(this.detailForm);
			const API = 'updateDeliverStageManagementCloseStatus';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...row }));
				if (res.data.success) {
					this.$message.success('阶段状态已修改！');
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 查询合同交付佣金人员级别汇总, 如果佣金没有被计算过，则返回空数组
		async queryTotalMoney(dmid) {
			const API = 'selectDeliverCommissionSummaryList';
			try {
				const res = await this.$axios[API](JSON.stringify({ id: dmid }));
				if (res.data.success) {
					// this.$succ(res.data.message);
					this.totalMoneyList = res.data.data;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			const props = ['amount', 'customizedServiceAmount'];
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					return props.includes(column.property) ? Number(item[column.property]) || 0 : 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return bigAdd(prev, curr);
				}, 0); //合计计算
				if (!props.includes(column.property)) {
					means[columnIndex] = '';
				} else if (props.includes(column.property) && Number(sum)) {
					means[columnIndex] = (
						<div class="text-right ellipsis">
							<div>{sum} 元</div>
							<div>{bigDiv(sum, 10000, 6)} 万</div>
						</div>
					);
				}
			}
			return [means];
		},
		dateFormat, //日期format
		bigDiv, // 除法
		bigMul,
	},
};
</script>

<style lang="scss">
.ContractStage {
	width: 100%;
	// height: 100%;
	overflow: hidden;
	position: relative;

	.detail-table {
		min-height: 100px !important;
		height: auto !important;

		.input_textR {
			.el-input__inner {
				border: none;
				text-align: end;
			}
		}
	}
}
</style>
