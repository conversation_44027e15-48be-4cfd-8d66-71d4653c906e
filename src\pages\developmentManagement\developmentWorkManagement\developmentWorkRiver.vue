<template>
	<div id="developmentWorkRiver">
		<taskList ref="taskList" :parentType="'river'" @close="queryTableData" />

		<BaseLayout>
			<template #header>
				<span class="search-label">年度</span>
				<el-date-picker
					size="small"
					class="w-150"
					v-model="selectTime"
					type="year"
					value-format="timestamp"
					format="yyyy 年"
					placeholder="请选择年份"
					:clearable="false"
					@change="changeDateSelect"
				>
				</el-date-picker>

				<el-checkbox-group v-model="searchForm.statusList" @change="queryTableData">
					<el-checkbox v-for="(item, index) in statusList" :label="item.id" :key="index">
						{{ item.status }}
					</el-checkbox>
				</el-checkbox-group>
				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-toolbar">
					<span class="mr-auto">计量单位: 产出工时</span>
				</div>

				<u-table
					ref="uTableRef"
					class="table-main table-main2"
					:height="1200"
					:row-height="45"
					@sort-change="sortChange"
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
					:cell-style="addStyle"
				>
					<u-table-column fixed label="序号" width="50" align="center" type="index"> </u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						resizable
						sortable="custom"
						:fixed="item.fixed"
					>
						<template #header>
							<span v-if="!item.sunday">{{ item.colName }}</span>
							<div v-else class="header-weekNo">{{ item.colName }}</div>
							<span>{{ item.sunday }}</span>
						</template>

						<template slot-scope="scope">
							<div
								class="pointer"
								@click="openList(item.colNo, scope.row)"
								v-if="item.colNo != 'productUname' && item.colNo != 'total' && scope.row[item.colNo]"
							>
								<span class="hover-green"> {{ scope.row[item.colNo] }}</span>
							</div>

							<!-- 合计 -->
							<div
								class="pointer"
								@click="openList(item.colNo, scope.row)"
								v-else-if="item.colNo == 'total' && scope.row[item.colNo]"
							>
								<Tooltips
									class="hover-green"
									:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>

							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>
<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数

import taskList from './developmentWorkTaskList.vue';
export default {
	name: 'developmentWorkRiver',
	components: { taskList },
	data() {
		return {
			//日期相关
			selectTime: new Date(),
			startTime: '',
			endTime: '',
			searchForm: {
				type: 1,
				statusList: [0, 1, 2, 3, 4, 5, 6],
			},
			statusList: [
				{ id: 1, status: '计划中' },
				{ id: 0, status: '开发延误' },
				{ id: 6, status: '测试延误' },
				{ id: 3, status: '已转测' },
				{ id: 5, status: '转测不通过 ' },
				{ id: 4, status: '转测通过' },
			],
			tableSort: { prop: '', order: '' }, //表格排序状态
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableData: [],
			tableColumn: [],
			currentWeekNo: 0,
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['menuTitle']) },
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.searchForm.statusList =
			JSON.parse(window.localStorage.getItem('developmentWorkRiver_statusList')) || this.searchForm.statusList;
		const nowYear = new Date().getFullYear();
		this.changeDateSelect(new Date(nowYear, '0', '1').getTime());
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 交付清单
		openList(weekIndex, row) {
			const getWeekStartAndEndTimestamps = (year, weekNumber) => {
				const getStartOfWeek = date => {
					const dayOfWeek = date.getDay();
					const diff = (dayOfWeek + 6) % 7; // 因为我们希望周的第一天是星期日，所以需要调整
					date.setDate(date.getDate() - diff + (weekNumber - 1) * 7);
					return date;
				};

				const yearStart = new Date(year, 0, 1);
				const firstSunday = getStartOfWeek(new Date(yearStart));

				const weekStart = new Date(firstSunday);
				const weekEnd = new Date(firstSunday);
				weekEnd.setDate(weekEnd.getDate() + 6);

				// 确保周的结束日期不超过当年的最后一天
				const yearEnd = new Date(year, 11, 31);
				if (weekEnd > yearEnd) {
					weekEnd.setDate(yearEnd.getDate());
				}

				return { dateStart: weekStart.getTime(), dateEnd: weekEnd.getTime() };
			};

			console.log({ weekIndex });
			const now = new Date(this.startTime).getFullYear(); // 获取当前年份
			const { dateStart, dateEnd } = getWeekStartAndEndTimestamps(now, weekIndex);
			//任务河流查要求转测日期 ， 成果评价查测试通过日期，
			this.searchForm.startTime = dateStart;
			this.searchForm.endTime = dateEnd;
			this.searchForm.productUname = row?.productUname || '';
			this.$refs.taskList.openTaskList(this.searchForm);
		},

		// 获取业务河流数据
		queryTableData: debounce(function (type) {
			window.localStorage?.setItem('developmentWorkRiver_statusList', JSON.stringify(this.searchForm.statusList));
			this.tableData = [];
			const str = JSON.stringify({
				endTime: this.endTime,
				startTime: this.startTime,
				pageNum: 1,
				pageSize: 100,
				statusList: this.searchForm.statusList,
			});
			this.$axios
				.productTaskRiver(str)
				.then(res => {
					if (res.data.success) {
						res.data.data.map(item => {
							item.total = item.total.toFixed(0);
							item.monthData.map(monthItem => {
								item[monthItem.month] = monthItem.workHour.toFixed(0);
							});
						});
						this.tableData = res.data.data.sort((a, b) => b.total - a.total);

						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
						this.scrollTableToColumn(this.currentWeekNo);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('productTaskRiver |' + error);
				});
		}),

		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = []; // 合计
			columns.forEach((column, columnIndex) => {
				if (columnIndex === 0) {
					const html = '<div class="total-label">合计</div>';
					means.push(html);
				} else if (columnIndex === 1) {
					means.push('');
				} else {
					const values = data?.map(item => {
						return Number(item[column.property]?.workHour || item[column.property]);
					});

					// 合计
					if (!values.every(value => isNaN(value))) {
						means[columnIndex] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return this.accAdd(prev, curr);
							} else {
								return prev;
							}
						}, 0);
						// 平均值 avg
						const filterArr = values.filter(e => e && e != 0);
						const average = means[columnIndex] / filterArr.length;
						if (!isNaN(means[columnIndex])) {
							means[columnIndex] = (
								<span
									on-click={e => {
										e.stopPropagation();
										this.openList(columnIndex - 1, null);
									}}
								>
									<span class="summary ellipsis">{means[columnIndex]}</span>
									<br />
									<span class="summary ellipsis">{average.toFixed(1)}</span>
								</span>
							);
						} else {
							means[columnIndex] = '';
						}
					} else {
						means[columnIndex] = '';
					}
				}
			});
			// console.log(columns);
			// 返回一个二维数组的表尾合计(不要平均值，你就不要在数组中添加)
			return [means];
		},
		// 数值加法精准度设置
		accAdd(arg1, arg2) {
			let r1, r2, m;
			try {
				r1 = arg1.toString().split('.')[1].length;
			} catch (e) {
				r1 = 0;
			}
			try {
				r2 = arg2.toString().split('.')[1].length;
			} catch (e) {
				r2 = 0;
			}
			m = Math.pow(10, Math.max(r1, r2));
			m *= 10;
			return (parseInt(arg1 * m + arg2 * m) / m).toFixed(1);
		},

		addStyle({ row, column, rowIndex, columnIndex }) {
			const rowBackground = {};
			if (!this.currentWeekNo || this.currentWeekNo <= 0) {
				rowBackground.background = '';
				return rowBackground;
			}
			if (columnIndex == this.currentWeekNo + 1) {
				rowBackground.background = '#c3fae8';
			}
			return rowBackground;
		},
		// 初始布局
		setInitCol(year) {
			this.tableColumn = [];
			const tableColumn = [{ colName: '研发人员', colNo: 'productUname', align: 'left', width: 100, fixed: 'left' }];

			// 情况一：包含当年第一周
			const firstDayOfYear = new Date(year, 0, 1);
			const firstMonday = new Date(year, 0, 1 - firstDayOfYear.getDay()); // 找到第一个周一
			const currentSunday = new Date(firstMonday); // 从第一个周一开始计算周日
			let weekNo = 1;
			if (currentSunday.getFullYear() < year) {
				currentSunday.setDate(currentSunday.getDate() + 7);
			}

			while (currentSunday.getFullYear() === year) {
				const month = String(currentSunday.getMonth() + 1).padStart(2, '0');
				const day = String(currentSunday.getDate()).padStart(2, '0');
				tableColumn.push({
					colName: '' + weekNo,
					sunday: `${month}/${day}`,
					colNo: '' + weekNo,
					align: 'center',
					width: '65',
				});

				currentSunday.setDate(currentSunday.getDate() + 7);
				weekNo++;
			}
			// 情况二： 不包含当年第一周
			// const firstDayOfYear2 = new Date(year, 0, 1);
			// const firstSunday = new Date(year, 0, 1 + (7 - firstDayOfYear2.getDay()));

			// let currentSunday = firstSunday;
			// let weekNo = 1;

			// while (currentSunday.getFullYear() === year) {
			// 	const month = String(currentSunday.getMonth() + 1).padStart(2, '0');
			// 	const day = String(currentSunday.getDate()).padStart(2, '0');
			// 	tableColumn.push(
			// 		{
			// 			colName: weekNo, sunday: `${month}/${day}`, colNo: weekNo, align: "center", width: '65'
			// 		}
			// 	);

			// 	currentSunday.setDate(currentSunday.getDate() + 7);
			// 	weekNo++;
			// }

			// 判断当前日期在第几周
			const currentDate = new Date(); // 获取当前日期
			const diffTime = currentDate - firstMonday;
			const diffWeeks = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 7));
			const WeekNo = diffWeeks + 1;
			this.currentWeekNo = WeekNo > 52 ? 0 : WeekNo;
			console.log(this.currentWeekNo);
			this.$nextTick(() => {
				this.tableColumn = tableColumn;
			});
			this.columnWidthCurr = {};
			this.tableColumn.forEach(item => {
				this.columnWidthCurr[item.colNo] = null;
			});
		},
		// 横向滚动到某一列
		scrollTableToColumn(columnIndex) {
			this.$nextTick(() => {
				if (columnIndex < 18) {
					return;
				}
				const tableElement = this.$refs.uTableRef?.$el;
				const tableContainer = tableElement.querySelector('.el-table__body-wrapper');
				tableContainer.scrollLeft = columnIndex * 40;
			});
		},
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 时间选择
		changeDateSelect(date) {
			const year = new Date(date).getFullYear();
			this.startTime = new Date(year, '0', '1').getTime();
			this.endTime = new Date(year + 1, '0', '1').getTime() - 1;
			this.setInitCol(year);
			this.queryTableData();
		},
	},
};
</script>

<style lang="scss" scoped>
#developmentWorkRiver {
	width: 100%;
	overflow: hidden;
	position: relative;

	.header-weekNo {
		font-size: 14px;
		text-align: center;
	}
}
</style>
<style lang="scss">
#developmentWorkRiver {
	.table-wrapper {
		.table-main2 {
			.el-table__body-wrapper {
				width: 100%;
				min-width: 400px;
				height: calc(100% - 145px) !important;
				overflow-y: scroll;
				overflow-x: scroll;
			}
		}
	}
	.total-label {
		height: 50px;
		line-height: 25px;
	}

	.table-wrapper .table-card .el-card__body .table-main .el-table td.is-center {
		padding: 10px 10px 10px 0;
		text-align: right;
	}

	.plTableBox .el-table__fixed-footer-wrapper tbody td {
		background: transparent;
		border: none;
	}

	// .table-wrapper .table-card .el-card__body .table-main .is-leaf:not(:last-child) {
	// 	padding: 16px 0 0 0 !important;
	// }
}
</style>
