<!-- 这是一个递归组件：评论回复树 -->
<template>
	<div class="ReplyTree flex-column gap-10 fs-12">
		<div v-for="(item, index) in replies" :key="item.replyer + item.replyTime + index" class="tree-item flex-column">
			<!-- 回复 -->
			<div class="flex gap-10">
				<!-- 回复信息（用户名、时间） -->
				<div class="flex-column ellipsis w-125">
					<div class="ellipsis" v-if="parent.replyer">
						<span class="flex-column">
							<span class="bolder ellipsis" :title="item.replyer">{{ item.replyer }}：</span>
							<span class="color-999 ellipsis" :title="parent.replyer">回复 @{{ parent.replyer }}</span>
						</span>
					</div>
					<div class="ellipsis" v-else>
						<span class="bolder ellipsis" :title="item.replyer"> {{ item.replyer }}</span>
					</div>
					<span class="color-999 ellipsis"> {{ dateFormat(item.replyTime, 'lineM') }}</span>
				</div>

				<!-- 回复内容 -->
				<div class="flex-column flex-1 flex-justify-between overflow-x-auto">
					<div class="flex-start">
						<pre class="m0 p0">{{ item.replyContent }}</pre>
						<div class="ml-auto" v-show="getEditBtn(item)">
							<el-button type="text" size="mini" class="el-icon-delete color-999 m0 p0" @click="deleteReply(item)"></el-button>
						</div>
					</div>

					<!-- 退回/接收BUG (系统回复、研发工作台、非系统补丁、无菜单回复才显示)-->
					<div
						v-if="
							item.isSystemAutoReply &&
							workbenche.type === '研发' &&
							item.replyContent.includes('@产品中心') &&
							item.menuReplyVOS.length === 0
						"
						class="flex-align-center"
					>
						<el-button type="text" size="mini" class="red" @click="$emit('returnBug', item)">退回</el-button>
						<el-button type="text" size="mini" class="" @click="$emit('acceptBug', item)">接收BUG</el-button>
					</div>

					<!-- 回复按钮 -->
					<div v-else class="flex-align-center gap-10 h-18">
						<el-button v-if="!item.isSystemAutoReply" type="text" size="mini" class="p0 m0" @click="openReply(item)">
							回复
							<i v-show="item.mrrid === editForm.parentRid" class="el-icon-arrow-down"></i>
						</el-button>
						<el-button v-if="item.menuReplyVOS.length" type="text" size="mini" class="p0 m0" @click="openReplyList(item)">
							<span> {{ getOpenState(item) ? '收起' : '展开' }}({{ item.menuReplyVOS.length }})</span>
							<i :class="getOpenState(item) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i>
						</el-button>
					</div>

					<!-- 回复输入框 -->
					<div v-if="item.mrrid === editForm.parentRid" class="flex-column W100">
						<el-input
							size="mini"
							type="textarea"
							class="min-w-300 W50"
							v-model="editForm.replyContent"
							:placeholder="`回复 @${item.replyer || parent.replyer}`"
							:autosize="{ minRows: 4, maxRows: 6 }"
						></el-input>

						<!-- 取消/回复 -->
						<div class="flex-align-center gap-10">
							<el-button class="mt10 W10 min-w-100" size="mini" type="info" @click="editForm.parentRid = ''"> 取消 </el-button>
							<el-button class="mt10 W10 min-w-100" size="mini" type="primary" @click="saveReply"> 回复 </el-button>
						</div>
					</div>
				</div>
			</div>

			<!-- 子级回复（对回答进行回复） -->
			<ReplyTree
				class="child-reply"
				v-show="item.menuReplyVOS.length && getOpenState(item)"
				:parent="item"
				:replies="item.menuReplyVOS"
				:workbenche="workbenche"
				:nowDetail="nowDetail"
				:isChild="true"
				@refresh="$emit('refresh')"
			/>

			<!-- 父级回答的分割线-->
			<div :class="!isChild ? 'border-bottom pt10' : ''"></div>
		</div>
	</div>
</template>

<script>
import { deepClone, dateFormat, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'ReplyTree',
	props: {
		// 工作台
		workbenche: {
			type: Object,
			required: true,
		},
		// 回复列表
		replies: {
			type: Array,
			required: true,
		},
		// 详情表单
		nowDetail: {
			type: Object,
			required: true,
		},
		// 是否是子级回复
		isChild: {
			type: Boolean,
			default: false,
		},
		// 父级回复
		parent: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			editForm: {
				parentRid: '',
				qid: '',
				replyContent: '',
				content: '',
				mrrid: '',
				isOpen: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userInfo', 'userRoles']),
		// 是超级管理
		isSuperAdmin() {
			return this.userRoles?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
	},
	methods: {
		// 获取展开状态
		getOpenState(item) {
			return this.editForm[`isOpen_${item.mrrid}`] || false;
		},
		// 设置展开状态
		setOpenState(mrrid, state) {
			this.$set(this.editForm, `isOpen_${mrrid}`, state);
		},
		// 获取修改/删除按钮
		getEditBtn(item) {
			if (item.isSystemAutoReply) return false; // 系统自动回复不显示修改/删除按钮
			return this.isSuperAdmin || item.replyer == this.userInfo.userName;
		},
		// 删除回复
		deleteReply(item) {
			this.$confirm('此操作将永久删除该回复, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'deleteMenuReplyByRid';
					try {
						const res = await this.$axios[API](JSON.stringify({ mrrid: item.mrrid }));
						if (res.data.success) {
							this.$succ(res.data.message);
							this.$emit('refresh');
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 打开回复
		openReply(item) {
			if (this.editForm.parentRid == item.mrrid) {
				this.editForm.parentRid = ''; // 取消回复
			} else {
				this.editForm.parentRid = item.mrrid; // 回复
			}
		},
		// 打开回复列表
		openReplyList(item) {
			const isOpen = !this.getOpenState(item);
			this.setOpenState(item.mrrid, isOpen);
			if (!isOpen) {
				this.editForm.parentRid = ''; // 取消回复
			}
		},
		// 保存回答
		async saveReply() {
			const { parentRid, mrqid, replyContent } = this.editForm;
			const { replyDept } = this.workbenche; // 获取当前工作台编号 -> 回复部门
			const API = 'addMenuRelateReply';
			try {
				const res = await this.$axios[API](JSON.stringify({ parentRid, mrqid: '', replyContent, replyDept }));
				if (res.data.success) {
					this.editForm.replyContent = '';
					this.editForm.parentRid = '';
					this.setOpenState(parentRid, true);
					this.$succ(res.data.message);
					this.$emit('refresh', this.nowDetail);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		dateFormat, // 日期格式化
	},
};
</script>
<style lang="scss" scoped>
.ReplyTree {
	.h-18 {
		height: 18px !important;
	}
	.w-125 {
		width: 125px !important;
	}
	.max-w-125 {
		max-width: 125px !important;
	}
}

.child-reply {
	margin: 10px 10px 0 10px;
	padding: 5px;
	border-radius: 8px;
	background-color: #f9f9f9;
	border: 1px solid #f0f0f0;
	.tree-item {
		border-left: 1.5px dashed #d7d7d7;
		padding-left: 10px;
	}
}
</style>
