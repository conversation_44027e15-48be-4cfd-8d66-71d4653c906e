<template>
	<div class="header-wrapper flex-align-center W100">
		<!-- 用户信息 -->
		<div class="user-info flex-align-center W10">
			<div> <img :src="userImg" @error="e => (e.target.src = userImg)" class="user-img" /></div>
			<div class="user-name flex-column bolder">
				<Tooltips class="max-w-50 color-999 fs-14" :cont-str="userInfo.no" />
				<Tooltips class="max-w-80 color-666 fs-16" :cont-str="userInfo.name" />
			</div>
		</div>

		<!-- 数据卡片 -->
		<div
			class="card-info text-center border-radius-8 W15"
			:class="dItem.class"
			v-for="(dItem, index) in dataInfoList"
			:key="index"
			@click="openLogList(dItem.name)"
		>
			<div class="card-info-data fs-24 bolder">
				<CountTo v-if="dItem.data" :startVal="0" :endVal="dItem.data" :decimals="dItem.data % 1 !== 0 ? 2 : 0" :duration="3000" />
				<span v-else> {{ dItem.data }}</span>
			</div>
			<Tooltips class="card-info-desc fs-16" :cont-str="dItem.desc" />
		</div>

		<div class="tool-warpper ml-auto">
			<!-- 搜索插槽 -->
			<slot name="search-tool"></slot>
			<div class="tool-buttons">
				<el-button type="text" size="mini" class="el-icon-refresh p0" @click="$emit('refresh')">刷新</el-button>
			</div>
		</div>
	</div>
</template>
<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import userImg from '@/assets/img/user_defaut.webp';
import CountTo from 'vue-count-to';
export default {
	name: 'DashboardCard',
	components: {
		CountTo,
	},
	props: {
		dataInfoList: { type: Array, default: () => [] },
	},
	data() {
		return { userImg };
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		...mapGetters(['dashboardList']), // 驾驶舱数据
		userInfo() {
			const match = this.userInfos?.adminUserVO?.userName.match(/^(\d+)([^\d]+)/);
			if (match) {
				return {
					no: match[1],
					name: match[2].trim(),
				};
			}
			return { no: '', name: this.userInfos?.adminUserVO?.userName || '' }; // 如果没有匹配，返回 ''
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 打开日志列表
		openLogList(name) {
			if (name == 'monthlyPointsSum' || name == 'monthlyDeductPoints') this.$emit('openLogList', name);
		},
	},
};
</script>

<style lang="scss" scoped>
.header-wrapper {
	.border-radius-8 {
		border-radius: 8px;
	}
	// height: 80px;
	position: sticky;
	top: -10px;
	min-height: 80px;
	gap: 8px;
	overflow: hidden;
	overflow-x: auto;
	background-color: #fff;

	.user-info {
		position: sticky;
		left: 0px;

		width: max-content !important;
		height: 70px;
		background-color: #fff;
		.user-img {
			vertical-align: middle;
			width: 70px;
			height: 70px;
			border-radius: 35px;
		}
		.user-name {
			padding: 0 5px 0 15px;
		}
	}

	// .card-info.bg-green {
	// 	// background: linear-gradient(135deg, #42e695, #3bb2b8);
	// 	background: linear-gradient(135deg, #4ac29a, #bdfff3);
	// 	color: #333;
	// }
	// .card-info.bg-blue {
	// 	// background: linear-gradient(135deg, #36d1dc, #5b86e5);
	// 	background: linear-gradient(135deg, #109ce7, #c8ecf3);
	// 	color: #333;
	// }
	// .card-info.bg-red {
	// 	// background: linear-gradient(135deg, #ff9966, #ff5e62);
	// 	background: linear-gradient(135deg, #ef3b36, #e4d3d3);
	// 	color: #333;
	// }
	// .card-info.bg-orange {
	// 	// background: linear-gradient(135deg, #ffc837, #ff8008);
	// 	background: linear-gradient(135deg, #f8b500, #fceabb);
	// 	color: #333;
	// }

	.card-info-data {
		// text-shadow: 2px 0px 1px aliceblue;
		text-shadow: 3px 2px 1px rgba(0, 0, 0, 0.2);
	}

	.card-info {
		height: 70px;
		padding: 0 5px;
		transition: all 0.5s cubic-bezier(0.15, 0.83, 0.66, 1);
		position: relative; /* 添加 position 以相对定位元素 */

		background-image: url('@/assets/img/wave-bg.webp');
		// background-size: 100% 100%;
		background-size: cover; /* 使背景图像根据容器大小适应 */
		background-position: right; /* 将背景图像居右显示 */

		&:hover {
			cursor: pointer;
			transform: scale(1.05);
		}
	}
	.tool-warpper {
		background-color: #fff;
		border-radius: 8px;
		position: sticky;
		right: 0;
		height: 70px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
	.tool-buttons {
		display: flex;
		align-items: flex-end;
		justify-content: flex-end;

		background-color: #fff;
		position: sticky;
		right: 0;
	}
}
</style>
