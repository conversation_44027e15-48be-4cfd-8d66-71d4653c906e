<template>
	<!-- 基础布局组件 -->
	<div class="BaseLayout">
		<div v-if="showHeader" class="table-search">
			<div ref="searchArea" class="search-area">
				<slot name="header">
					<!-- 搜索栏区域 -->
				</slot>

				<!-- 展开按钮 -->
				<el-button
					v-show="showExpandBtn && isOverflow"
					type="text"
					@click="toggleExpand"
					class="expand-btn el-icon-d-arrow-left"
					:title="isExpanded ? '收起' : '展开'"
					:class="{ 'expand-btn-up': isExpanded, 'expand-btn-down': !isExpanded }"
				>
					<!-- {{ isExpanded ? '收起' : '展开' }} -->
				</el-button>
			</div>
		</div>
		<div v-if="showMain" class="table-wrapper">
			<div class="table-card">
				<!-- 背景图 -->
				<img v-if="showBgLogo" class="bg-logo" src="@/assets/img/lightmes-logo.webp" />
				<slot name="main">
					<!-- 报表主体（含工具bar) -->
				</slot>
			</div>
		</div>

		<slot>
			<!-- 匿名插槽  -->
		</slot>
	</div>
</template>
<script>
import { debounce } from '@/util/tool'; //按需引入常用工具函数
export default {
	props: {
		showHeader: { type: Boolean, default: true }, //显示搜索区域
		showMain: { type: Boolean, default: true }, //显示内容区域（表格）
		showExpandBtn: { type: Boolean, default: true }, //是否显示展开按钮
		defaultHeight: { type: Number, default: 68 }, //默认高度
		showBgLogo: { type: Boolean, default: false }, //是否显示背景图
		defaultExpanded: { type: Boolean, default: false }, // 是否默认展开搜索栏
	},
	computed: {
		// 是否添加监听时事件Resize
		isResizeListener() {
			return this.showHeader && this.showExpandBtn;
		},
	},
	data() {
		return {
			isOverflow: false, // 是否超出了最大高度
			isExpanded: this.defaultExpanded, // 是否展开
			resizeObserver: null, // 监听器
		};
	},
	mounted() {
		if (this.isResizeListener) {
			this.checkSearchArea();
			this.resizeObserver = new ResizeObserver(e => {
				this.checkSearchArea(e);
			});
			this.resizeObserver.observe(this.$refs.searchArea);
		}
	},
	beforeDestroy() {
		if (this.resizeObserver) {
			this.resizeObserver.disconnect(); // 断开监听
			this.resizeObserver = null;
		}
	},
	methods: {
		// 计算高度
		checkSearchArea: debounce(function () {
			this.$nextTick(() => {
				const searchArea = this.$refs?.searchArea || null;
				if (!searchArea) return console.log('searchArea is null');
				searchArea.style.height = 'auto'; // 先设为auto，获取实际高度
				const realHeight = searchArea.offsetHeight;
				this.isOverflow = realHeight > this.defaultHeight; // 是否超出默认高度
				this.isExpanded = !this.isOverflow ? this.defaultExpanded : this.isExpanded;
				searchArea.style.height = this.isExpanded ? 'auto' : `${this.defaultHeight}px`;
			});
		}, 666),

		// 触发展开/收起
		toggleExpand: debounce(function () {
			this.isExpanded = !this.isExpanded;
			const searchArea = this.$refs.searchArea;
			searchArea.style.height = this.isExpanded ? 'auto' : `${this.defaultHeight}px`;
		}, 300),
	},
};
</script>
<style lang="scss">
@import '@/styles/element-variables.scss';

/*  表格上面的搜索栏 */
.table-search {
	overflow: hidden;
	.search-area {
		position: relative;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 10px;
		font-size: 14px;
		min-height: 68px;
		line-height: 40px;
		padding: 10px 30px 10px 20px !important;
		background-color: #fff;
		border: 1px solid #d7d7d7;
		border-radius: 8px;
		color: #303133;
		transition: all 0.3s ease-in-out;

		.expand-btn {
			position: absolute;
			left: 50%;
			bottom: -13px;
			transform: translateX(-50%);
			transition: all 0.3s ease-in-out;
			&.expand-btn-up {
				transform: rotate(90deg);
				background: none !important;
			}
			&.expand-btn-down {
				transform: rotate(270deg);
				background: none !important;
			}
		}

		// 固定button大小
		.el-button {
			font-size: 14px;
			span {
				margin-left: 3px;
			}
		}
		// 查询提示文本
		.search-label {
			width: max-content;
			color: #555;
			font-weight: 400;
			font-size: 14px;
		}

		// 箭头样式
		.search-arrow {
			font-weight: 500;
			color: #999;

			&:hover {
				font-weight: 600;
				color: $--color-primary;
			}
		}
		.el-select {
			min-width: 100px;
			max-width: 180px;
			width: 10vw;
		}
		.el-checkbox-group,
		.el-radio-group {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
		}
		.el-checkbox,
		.el-radio {
			line-height: 40px;
			margin-right: 10px;
			&__label {
				font-size: 14px;
			}
		}

		//让 placeholder 更明显
		input {
			&::-webkit-input-placeholder {
				color: #8d8d8d;
			}
			&::-moz-placeholder {
				/* Mozilla Firefox 19+ */
				color: #8d8d8d;
			}
			&:-moz-placeholder {
				/* Mozilla Firefox 4 to 18 */
				color: #8d8d8d;
			}
			&:-ms-input-placeholder {
				/* Internet Explorer 10-11 */
				color: #8d8d8d;
			}
		}
	}
}

.table-wrapper {
	// 卡片容器样式
	.table-card {
		width: 100%;
		height: 100%;
		padding: 5px 20px;
		margin-top: 1vh;
		border: 1px solid #d7d7d7;
		border-radius: 8px;
		box-sizing: border-box;
		z-index: 0;
		position: relative;
		overflow: overlay;
		background-color: #fff;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		color: #303133;
		transition: 0.3s;
	}
	// 背景图
	.bg-logo {
		position: absolute;
		background-size: cover;
		opacity: 0.05;
		top: 50%;
		left: 50%;
		width: 800px;
		transform: translate(-50%, -50%);
		pointer-events: none; // 禁用鼠标事件
	}
}

// 表格上的功能栏：如放大镜查询、添加、打印、导入导出等
.table-toolbar {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	flex-wrap: wrap;
	gap: 10px;
	padding-right: 10px;
	min-height: 40px;

	font-size: 13px;
	color: #555;

	border-bottom: 1px solid #e9e9e9;
	.el-button {
		margin: 0 !important;
		font-size: 13px;
		span {
			margin-left: 3px;
		}
	}
	.el-checkbox-group,
	.el-radio-group {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}
	.el-checkbox,
	.el-radio {
		// line-height: 40px;
		margin-right: 10px;
		&__label {
			font-size: 14px;
		}
	}
}
</style>
