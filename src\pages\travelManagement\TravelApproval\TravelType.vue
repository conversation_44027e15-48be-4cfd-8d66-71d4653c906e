<template>
	<div class="TravelType">
		<BaseLayout>
			<template #header>
				<el-radio-group v-model="searchForm.cfGrouping" @change="queryTableData">
					<el-radio :label="0">差旅类别</el-radio>
					<el-radio :label="1">出差补助类型</el-radio>
				</el-radio-group>
				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
				</div>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar"></div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					@sort-change="sortChange"
					show-header-overflow="title"
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="(item, index) in tableColumn"
						:key="item.colNo + index"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期（默认不显示分秒 lineM ） -->
							<Tooltips
								v-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 状态/类型 -->
							<Tooltips
								v-else-if="item.colNo == 'tripType'"
								:cont-str="tripTypeMap[scope.row[item.colNo]]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 成交是否扣除 -->
							<span v-else-if="item.colNo == 'isDealDeduct'">
								{{ scope.row[item.colNo] == 1 ? '是' : '否' }}
							</span>
							<!-- <el-checkbox
								v-else-if="item.colNo == 'isDealDeduct'"
								v-model="scope.row[item.colNo]"
								:true-label="1"
								:false-label="0"
								@change="updateRow(scope.row)"
							></el-checkbox> -->

							<!-- 差旅费用/天 -->
							<el-input
								v-else-if="['expense'].includes(item.colNo)"
								v-model="scope.row[item.colNo]"
								placeholder="请输入金额"
								clearable
								size="mini"
								@input="scope.row[item.colNo] = getInputNum(scope.row[item.colNo], 2)"
								@change="updateRow(scope.row)"
							></el-input>

							<!-- 成交扣减金额 -->
							<el-input
								v-else-if="scope.row.isDealDeduct && ['dealDeductExpense'].includes(item.colNo)"
								v-model="scope.row[item.colNo]"
								placeholder="金额（元）"
								clearable
								size="mini"
								@input="scope.row[item.colNo] = getInputNum(scope.row[item.colNo], 2)"
								@change="updateRow(scope.row)"
							></el-input>
							<!-- 成交分配比例 -->
							<el-input
								v-else-if="scope.row.isDealDeduct && ['dealWaitAllocateRate'].includes(item.colNo)"
								v-model="scope.row[item.colNo]"
								placeholder="分配比例"
								clearable
								size="mini"
								@input="scope.row[item.colNo] = getInputNum(scope.row[item.colNo], 2)"
								@change="updateRow(scope.row)"
							>
								<template slot="append">%</template></el-input
							>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<u-table-column label="" width="" align="right">
						<!-- <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDetail('编辑',scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template> -->
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { bigMul, bigDiv, getInputNum } from '@/util/math';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { tripTypeMap } from '@/assets/js/contractSource'; // 差旅类别
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
// import DetailCom from './components/baseDetail.vue'; //明细组件
// import btnAuth from '@/mixins/btnAuth';

export default {
	name: 'TravelType', //组件名应同路由名(否则keep-alive不生效)
	// import引入的组件需要注入到对象中才能使用
	components: {},
	// mixins: [btnAuth],
	data() {
		return {
			activeTab: 'TravelType', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},

			// 查询表单
			searchForm: {
				cfGrouping: 0,
			},
			tripTypeMap, //  差旅类别
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 动态列
		tableColumn() {
			const colMap = {
				0: [
					// { colName: '差旅项目名', colNo: 'travelCategoryName', align: 'left', width: '150' },
					{ colName: '差旅类别', colNo: 'travelCategoryName', align: 'left', width: '150' },
					{ colName: '差旅费用（元）/ 天', colNo: 'expense', align: 'left', width: '200' },
					{ colName: '成交是否扣减', colNo: 'isDealDeduct', align: 'center', width: '120' },
					{ colName: '成交扣减金额', colNo: 'dealDeductExpense', align: 'left', width: '200' },
					{ colName: '成交分配比率', colNo: 'dealWaitAllocateRate', align: 'left', width: '200' },
				],
				1: [
					// { colName: '补助项目名', colNo: 'travelAllowanceName', align: 'left', width: '150' },
					{ colName: '补助类型', colNo: 'travelAllowanceName', align: 'left', width: '150' },
					{ colName: '补助金额（元）', colNo: 'expense', align: 'left', width: '200' },
					// { colName: '成交是否扣减费用', colNo: 'isDealDeduct', align: 'center', width: '' },
					// { colName: '成交扣减费用', colNo: 'dealDeductExpense', align: 'right', width: '' },
				],
			};
			return colMap[this.searchForm.cfGrouping];
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		// this.queryTableData();
	},
	activated() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 更新行数据
		updateRow: debounce(async function (row) {
			if (row.isDealDeduct == 0) {
				row.dealDeductExpense = 0;
			}
			const dealWaitAllocateRate = row.dealWaitAllocateRate ? bigDiv(Number(row.dealWaitAllocateRate), 100, 5) : 0;
			const API = 'updateTravelCategoryConfiguration';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...row, dealWaitAllocateRate }));
				if (res.data.success) {
					this.$succ(res.data.message);
				} else {
					this.queryTableData();
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),

		// 切换tab
		changeTab() {
			if (this.activeTab == 'TravelType') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},

		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectTravelCategoryConfiguration'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data.map(i => {
							return { ...i, dealWaitAllocateRate: i.dealWaitAllocateRate ? bigMul(Number(i.dealWaitAllocateRate), 100, 2) : 0 };
						});
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}, 0),

		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},

		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		dateFormat, //日期format
		jointString, //拼接字符串
		getInputNum, //输入正数时保留后几位小数
	},
};
</script>

<style lang="scss" scoped>
.TravelType {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
