<template>
	<BaseLayout :showHeader="false">
		<template #main>
			<div class="videoConfig-wrap overflow-auto">
				<div class="videoConfig-user flex-column mr20 sticky-top">
					<span class="label-title">视频号维护人({{ userList.length }})</span>
					<div class="flex-column">
						<el-button
							type="text"
							v-for="item in userList"
							:key="item.auid"
							class="el-icon-user-solid hover-green text-left m0"
							:class="curEditData.uid == item.auid ? 'green bolder' : 'color-999'"
							@click="onSelectUser(item)"
						>
							{{ getUserName(item) }}
						</el-button>
					</div>
				</div>
				<div class="videoConfig-info flex-1">
					<div class="flex-align-center sticky-top">
						<span class="label-title mr10">
							{{ curEditData.uname ? `${curEditData.uname}维护的` : '所有' }}视频号({{ filterconfigData.length }})
						</span>
						<el-button type="text" class="el-icon-plus p0 m0" @click="openDialog(curEditData, '添加')"> 添加一行 </el-button>
					</div>

					<div v-for="(item, index) in filterconfigData" :key="index" class="flex-align-center">
						<span class="w-120">
							{{ item.uname || item.name }}
						</span>
						<el-input v-model="item.vid" placeholder="视频号" size="small" clearable @change="saveEdit(item, '修改')"></el-input>
						<el-button type="text" class="el-icon-edit-outline ml10" @click="openDialog(item, '修改')"></el-button>
						<el-button type="text" class="el-icon-delete ml10" @click="delRow(item.vid)"></el-button>
					</div>
				</div>
			</div>
		</template>

		<el-dialog width="666px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">{{ dialogTitle }} 视频号配置</span>

			<el-form :model="editForm" :rules="formRules" label-width="100px" label-position="left" @submit.native.prevent>
				<el-form-item label="维护人" prop="uid">
					<el-select v-model="editForm.uid" placeholder="请选择维护人" popper-class="select-column-3" clearable filterable>
						<el-option v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="视频号名称" prop="vid">
					<el-input v-model="editForm.vid" placeholder="请输入视频号名称" clearable></el-input>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEdit(editForm, dialogTitle)">确 定</el-button>
			</span>
		</el-dialog>
	</BaseLayout>
</template>

<script>
import { debounce, checkRequired, deepClone, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'videoConfig', //组件名应同路由名(否则keep-alive不生效)
	props: {
		twidList: Array,
		channelName: Array,
	},
	data() {
		return {
			activeTab: 'videoConfig', //激活tab页

			userList: [],
			configData: [],
			dialogTitle: '',
			dialogEdit: false,

			// 当前编辑数据
			curEditData: {
				uid: '',
				uname: '',
				vid: '',
			},
			// 编辑数据
			editForm: {
				uid: '',
				uname: '',
				vid: '',
			},
			formRules: {
				uid: [{ required: true, message: '请输入维护人姓名', trigger: 'blur' }],
				vid: [{ required: true, message: '请输入视频号名称', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		filterconfigData() {
			if (this.curEditData.uid) {
				return this.configData?.filter(item => item.uid == this.curEditData.uid) || [];
			}
			return this.configData;
		},
	},
	// 监控data中的数据变化
	watch: {
		twidList() {
			this.queryUserByTwids();
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
		this.queryUserByTwids();
	},
	activated() {
		// this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 获取维护人名称+视频号数量
		getUserName(item) {
			const vids = this.configData.filter(rItem => rItem.uid == item.auid);
			return `${item.userName}(${vids.length})`;
		},
		// 选择维护人
		onSelectUser(item) {
			if (item.auid == this.curEditData.uid) {
				this.curEditData.uid = '';
				this.curEditData.uname = '';
				return;
			}
			this.curEditData.uid = item.auid || item.uid || '';
			this.curEditData.uname = item.userName || item.uname || '';
		},
		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			this.$axios
				.selectSalesmanByTwids(JSON.stringify({ twids: this.twidList, counselor: '流量专员' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		openDialog(data, type) {
			if (data) {
				this.editForm = data;
			} else {
				this.editForm.vid = '';
			}
			this.dialogTitle = type;
			this.dialogEdit = true;
		},
		closeDialog() {
			// this.curEditData = resetValues(this.curEditData);
			this.dialogEdit = false;
			this.queryTableData();
		},
		// 删除一行
		delRow(vid) {
			this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					const index = this.configData.findIndex(item => item.vid == vid);
					this.configData.splice(index, 1);
					this.$message.success('删除成功');
					this.saveEdit();
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 保存
		saveEdit: debounce(async function (row, type) {
			if (type == '添加' || type == '修改') {
				if (checkRequired(row, this.formRules)) {
					this.queryTableData();
					return;
				}

				// 检查是否有同样的视频号名称
				const hasSameVid = this.configData.some(item => {
					if (type === '修改') {
						const count = this.configData.filter(item => item.vid == row?.vid).length; // 修改时检查只能有一个
						return count > 1;
					} else {
						return item.vid == row?.vid ? true : false;
					}
				});
				if (hasSameVid) {
					this.$message.warning(`${type == '修改' ? '修改' : '添加'}的视频号${row.vid}已存在，请重新输入!`);
					this.dialogEdit = false;
					this.queryTableData();
					return;
				}

				if (this.dialogEdit) {
					if (!row.uname) {
						row.uname = this.userList.find(item => item.auid === row.uid)?.userName || '';
					}
					if (type === '修改') {
						const index = this.configData.findIndex(item => item.vid == row?.vid && item.uid == row?.uid);
						this.configData[index] = row;
						console.log({ row }, index, this.configData[index], this.configData);
					} else if (type === '添加') {
						this.configData.push(row);
					}
				}
			}

			const API = 'updateInquiryPromotionalVidMap';
			try {
				const res = await this.$axios[API](JSON.stringify(this.configData));
				if (res.data.success) {
					if (this.dialogEdit) {
						this.closeDialog();
						this.queryTableData(1);
					}

					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
					this.queryTableData(1);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),

		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: debounce(function () {
			const API = 'fetchInquiryPromotionalVidMap'; //接口
			this.$axios[API](JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.configData = res.data.data.sort((a, b) => a.uname.localeCompare(b.uname, 'zh-CN'));
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
	},
};
</script>

<style lang="scss">
.videoConfig-wrap {
	display: flex;

	width: 100%;
	height: calc(100vh - 200px);
	overflow: hidden;
	position: relative;
	font-size: 14px;
	color: #666;
	// background-color: #fff;
	// border: solid 1px #d7d7d7;
	// border-radius: 8px;
	// padding: 20px !important;
	// box-sizing: border-box;

	.sticky-top {
		position: sticky;
		top: 0px;
		z-index: 10;
		background-color: #fff;
	}
}
</style>
