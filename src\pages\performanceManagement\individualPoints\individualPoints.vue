<template>
	<div class="IndividualPoints" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<!-- 询盘详情 -->
		<InquiryDetail v-if="showMap.InquiryDetail" ref="InquiryDetail" :inquiryOptions="tableData" @close="queryTableData()" />
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 明细组件 -->
		<!-- <DetailCom ref="DetailComRef" @close="queryTableData(1)" /> -->
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="积分明细" name="PointsDetails">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">期间</span>
						<DateSelect
							defaultDate="今天"
							@change="
								searchForm.pointsLogDateBegin = $event.startTime;
								searchForm.pointsLogDateEnd = $event.endTime;
								queryTableData(1);
							"
							:clearable="false"
						/>

						<el-input
							class="searchBox"
							size="small"
							v-model="searchForm.uname"
							placeholder="姓名"
							clearable
							@input="filterUserTabList()"
						></el-input>

						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<el-tabs class="user-tabs h-40" v-model="activeUser" @tab-click="chengUserTab">
							<el-tab-pane :name="item.auid" v-for="(item, index) in userTabList" :key="item.auid">
								<span slot="label">
									<el-badge
										class="mini-badge pointer pr10"
										:value="item.auditCount"
										:max="99"
										:hidden="item.auditCount ? false : true"
									>
										<div>{{ item.uname }}</div>
									</el-badge>
								</span>
							</el-tab-pane>
						</el-tabs>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							:row-class-name="getRowColor"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							:pagination-show="false"
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="item.colNo.includes('pointsDate')"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<Tooltips
										v-else-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 工作项 -->
									<Tooltips
										v-else-if="item.colNo == 'operation'"
										class="hover-green"
										@click.native="openDetail('修改', scope.row)"
										:cont-str="jointString(' - ', scope.row.operation, scope.row.idNumber)"
										:cont-width="scope.column.realWidth - 20"
									/>

									<!-- 审查状态 0: 无需检查, 1: 待评价, 2: 不合格, 3: 合规带问题, 4: 合格, 5: 优秀, 10 免检 -->
									<div v-else-if="item.colNo == 'auditStatus'" class="fs-12">
										<span v-if="scope.row.auditStatus === 0" class="color-999">无需评价</span>
										<span v-else-if="scope.row.auditStatus === 10" class="color-999">免检</span>

										<el-button
											v-else-if="scope.row.auditStatus === 1"
											class="red p0"
											size="mini"
											type="text"
											@click="openDialog('检查', scope.row)"
											>未评</el-button
										>
										<el-button
											v-else
											class="p5"
											:type="{ 2: 'danger', 3: 'warning', 4: 'primary', 5: 'primary' }[scope.row.auditStatus]"
											size="mini"
											@click="openDialog('检查', scope.row)"
										>
											{{ auditStatusMap[scope.row.auditStatus] }}
										</el-button>
									</div>

									<!-- 详情 -->
									<div v-else-if="item.colNo == 'details'" class="flex-align-center">
										<div
											v-for="(dItem, dIndex) in getOperationDetails(scope.row)"
											:key="dIndex + activeUser"
											class="W50 flex-align-center"
										>
											<!-- 询盘录音播放器 -->
											<InquiryAudioPlayer
												v-if="dItem.type == '咨询录音' && dItem.content"
												:audioUrl="dItem.content"
												:idid="dItem.idid"
											/>

											<Tooltips class="W100 mr10" v-else :cont-str="dItem.content" :cont-width="200" />
										</div>
									</div>

									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDialogRow(scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template>
              </u-table-column> -->
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane v-if="isSuperAdmin" disabled label="积分统计" name="PointsStatistics">
				<PointsStatistics v-if="activeTab === 'PointsStatistics'" />
			</el-tab-pane>
			<el-tab-pane v-if="isSuperAdmin" label="积分规则" name="PointsRules">
				<PointsRules v-if="activeTab === 'PointsRules'" />
			</el-tab-pane>
		</el-tabs>

		<AuditDialog ref="AuditDialog" @update="queryTableData(1)" />
	</div>
</template>

<script>
import { debounce, dateFormat, sortTableData, jointString, deepClone, checkRequired } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import ExportTable from '@/components/ExportTable'; //导出组件
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryAudioPlayer from '@/components/InquiryAudioPlayer'; //询盘录音播放器
import PointsStatistics from './PointsStatistics'; //积分统计
import PointsRules from './PointsRules'; //积分规则
import AuditDialog from './AuditDialog.vue'; //质量检查评价

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		DateSelect,
		PointsStatistics,
		PointsRules,
		InquiryDetail,
		AuditDialog,
		InquiryAudioPlayer,
	},
	name: 'IndividualPoints', //组件名应同路由名(否则keep-alive不生效)
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			openMove: false, //打开组件
			activeTab: 'PointsDetails', //激活tab页
			activeUser: '', //激活tab页
			userTabList: [],
			userTabListCopy: [],
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '时间', colNo: 'pointsDate', align: 'center', width: '120' },
				{ colName: '客户', colNo: 'clientName', align: '', width: '100' },
				{ colName: '工作项', colNo: 'operation', align: 'left', width: '160' },
				{ colName: '积分', colNo: 'points', align: 'right', width: '80' },
				{ colName: '详情', colNo: 'details', align: 'left', width: '' },
				{ colName: '质量检查', colNo: 'auditStatus', align: 'left', width: '100' },
				{ colName: '检查评价', colNo: 'auditMemo', align: 'left', width: '100' },
			],
			// 选择器数据 - 车间/部门/类型等...
			adpidList: [],
			// 查询表单
			searchForm: {
				adpid: '',
				channelName: [],
				pointsLogDateBegin: '',
				pointsLogDateEnd: '',
				twidList: [],
			},
			// 0: 无需检查, 1: 待评价, 2: 不合格, 3: 合规带问题, 4: 合格, 5: 优秀, 10 免检
			auditStatusMap: {
				0: '无需检查',
				1: '待评价',
				2: '不合格',
				3: '合规带问题',
				4: '合格',
				5: '优秀',
				10: '免检',
			},

			showMap: {
				InquiryDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开询盘详情
		openDetail(type, row, api) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom(type, row, api);
			});
		},
		// 根据输入的姓名过滤数据
		filterUserTabList() {
			if (this.searchForm.uname) {
				this.userTabList = this.userTabListCopy.filter(i => i.uname.includes(this.searchForm.uname));
			} else {
				this.userTabList = this.userTabListCopy;
			}

			if (this.userTabList.length) {
				const findIndex = this.userTabList.findIndex(i => i.auid === this.activeUser);
				if (findIndex >= 0) {
					this.chengUserTab({ index: findIndex });
				} else {
					this.activeUser = this.userTabList[0].auid;
					this.chengUserTab({ index: 0 });
				}
			} else {
				this.activeUser = '';
				this.tablePageForm.total = 0;
				this.tableData = [];
				this.sortChange(this.tableSort, true);
			}
		},
		// 获取工作项详情内容
		getOperationDetails(row) {
			const { pointsSource, clientKeepRecord, inquiryDocumentary, documentaryRecords } = row; //业务
			const { deliverStageManagementInfo } = row; //交付
			console.log(pointsSource, { row });
			if (pointsSource.includes('备案') && clientKeepRecord) {
				const { clientNo, registeredBusinessName } = clientKeepRecord;
				return [{ type: '客户备案', content: `${clientNo}：${registeredBusinessName}` }];
			} else if (pointsSource.includes('咨询') && inquiryDocumentary) {
				const { consultingOther, callRecording, idid } = inquiryDocumentary;
				return [
					{ type: '咨询内容', content: consultingOther, idid },
					{ type: '咨询录音', content: callRecording, idid },
				];
			} else if (pointsSource.includes('跟单') && documentaryRecords) {
				const { content, nextStep, nextPlan } = documentaryRecords;
				return [
					{ type: '跟单内容', content: content },
					{ type: '跟单计划', content: `${dateFormat(nextStep)}：${nextPlan}` },
				];
			} else if (pointsSource.includes('计划') && deliverStageManagementInfo) {
				const { stage } = deliverStageManagementInfo;
				return [{ type: '交付计划', content: stage }];
			} else {
				return [];
			}
		},
		// 打开弹窗
		openDialog(type, row) {
			this.$refs.AuditDialog.open(row);
		},

		// 切换tab
		changeTab() {},
		// 切换tab
		chengUserTab({ index }, event) {
			this.$refs.uTableRef?.reloadData([]); // 加载页面数据
			const AUDIT_POINTS = this.userTabList[index]?.auditAdminUserPointsLogVOList || []; //积分记录（待评价）
			const POINTS = this.userTabList[index]?.adminUserPointsLogVOList || []; //积分记录（已评价/不需要评价）

			this.tableData = AUDIT_POINTS.concat(POINTS);
			this.tablePageForm.total = this.tableData.length;
			this.sortChange(this.tableSort, true);
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectAdminUserPointsLogUserViewList'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }), { timeout: 60000 })
				.then(res => {
					if (res.data.success) {
						res.data.data?.sort((a, b) => a.uname.localeCompare(b.uname, 'zh-CN'));
						this.userTabList = res.data.data;
						this.userTabListCopy = res.data.data;
						this.filterUserTabList();

						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格文本高光显示
		getRowColor({ row }) {
			// 标红处理： 未评
			return row?.auditStatus === 1 ? 'red' : ''; //未评
		},
		//数据导出
		openExport: debounce(function () {
			const PROPS = {
				DATA: JSON.stringify({
					...this.searchForm,
				}), //接口参数
				API: 'xxxxxDownload', //导出接口
				downloadData: 'xxxxx明细', //数据报表参数（后台确认字段downloadData）
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),

		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.IndividualPoints {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>

<style lang="scss">
.IndividualPoints {
	.user-tabs {
		.el-tabs__item {
			font-size: 12px;
			padding: 0 10px;
		}
		.el-tabs__active-bar:after {
			width: 50%;
			height: 3px;
			left: 50%;
			margin-left: -30%;
		}
		.mini-badge {
			.el-badge__content {
				z-index: 88;
				zoom: 0.8;
				padding: 0 6px;
				right: 15px;
				top: 10px;
			}
		}
	}
}
</style>
