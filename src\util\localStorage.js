/* @todo：有点乱需要仔细调整 */

// 获取指定名称的cookie
export function getCookie(name) {
	// 创建正则表达式以匹配cookie名称
	const reg = new RegExp('(^| )' + encodeURIComponent(name) + '=([^;]*)(;|$)');

	// 根据正则表达式匹配cookie字符串，若找到则返回值，否则返回null
	const match = document.cookie.match(reg);
	return match ? decodeURIComponent(match[2].trim()) : null; // 返回cookie值或null
}

//设置cookie  默认是1000 * 60 * 60 * 24 = 24小时
export function setCookie(c_name, value, hours = 24) {
	if (typeof c_name !== 'string' || typeof value !== 'string') {
		throw new Error('Cookie name and value must be strings');
	}

	const expiredays = hours ? hours * 60 * 60 * 1000 : 0; // 将小时转为毫秒
	const exdate = new Date();
	exdate.setTime(exdate.getTime() + expiredays);

	// 设置cookie
	let cookieString = `${c_name}=${encodeURIComponent(value)}`;

	if (expiredays > 0) {
		cookieString += `;expires=${exdate.toGMTString()}`;
	}

	document.cookie = cookieString;
}

// 删除指定名称的cookie
export function delCookie(name, path = '/', domain) {
	const exp = new Date();
	exp.setTime(exp.getTime() - 1); // 设置过期时间为过去的时间

	// 构建cookie字符串
	let cookieString = `${encodeURIComponent(name)}=; expires=${exp.toGMTString()}; path=${path};`;

	// 如果指定了域，则添加域到cookie字符串
	if (domain) {
		cookieString += ` domain=${domain};`;
	}

	// 删除cookie
	document.cookie = cookieString;
}

// 设置 localStorage
export function setLocalStorage(key, value) {
	// 检查 key 是否存在
	if (!key) {
		console.error('未设置 key！');
		return;
	}

	try {
		// 将 value 序列化为 JSON 字符串并存入 localStorage
		localStorage.setItem(key, JSON.stringify(value));
	} catch (error) {
		console.error('设置 localStorage 时出错:', error);
	}
}
// 获取 localStorage
export function getLocalStorage(key) {
	// 检查 key 是否存在
	if (!key) {
		console.error('未设置 key！');
		return null; // 返回 null 以表示无效的输入
	}

	const str = localStorage.getItem(key);

	// 检查字符串是否有效
	if (str && str !== 'undefined' && str !== 'null') {
		try {
			return JSON.parse(str); // 尝试解析 JSON
		} catch (error) {
			console.error('解析 localStorage 值时出错:', error);
			return null; // 返回 null 表示解析失败
		}
	}

	return null; // 未找到对应的值时返回 null
}
