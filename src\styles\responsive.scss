/* 
  全局响应式/自适应样式 ：媒体查询、flex等

	320px是最小的移动设备屏幕尺寸，也是最常见的尺寸之一
	480px是另一个常见的移动设备屏幕尺寸。这个尺寸通常用于Android手机和iPhone 6等较小的手机屏幕。
	768px是平板电脑屏幕尺寸的标准尺寸之一。这个尺寸通常用于iPad等平板电脑屏幕。
	1024px是另一个常见的平板电脑屏幕尺寸。这个尺寸通常用于iPad Pro等较大的平板电脑屏幕。
	1280px是另一个常见的平板电脑屏幕尺寸。这个尺寸通常用于13英寸的笔记本电脑屏幕。
	1366px是另一个常见的笔记本电脑屏幕尺寸。这个尺寸通常用于14英寸的笔记本电脑屏幕。
	1440px是常见的高分辨率笔记本电脑屏幕尺寸之一。这个尺寸通常用于15英寸的笔记本电脑屏幕。
	1920px是常见的高分辨率桌面电脑屏幕尺寸之一。这个尺寸通常用于22英寸以上的桌面电脑屏幕。
	2560px是常见的高分辨率桌面电脑屏幕尺寸之一。这个尺寸通常用于27英寸以上的桌面电脑屏幕。
*/

/* 强制横屏  不需要时可以注释下面html标签相关的媒体查询代码*/
// html {
//   position: absolute;
//   overflow: hidden;
// }

// 竖屏
// @media screen and (orientation: portrait) {
//   html {
//     width: 100vh !important;
//     height: 100vw !important;
//     top: calc((100vh - 100vw) / 2);
//     left: calc((100vw - 100vh) / 2);
//     transform: rotate(90deg);
//     transform-origin: 50% 50%;
//   }
// }

// 横屏
// @media screen and (orientation: landscape) {
//   html {
//     width: 100vw !important;
//     height: 100vh !important;
//     top: 0;
//     left: 0;
//     transform: none;
//     transform-origin: 50% 50%;
//   }
// }

/* 在屏幕纵向显示且宽度小于等于 720px  */
// @media screen and (orientation: portrait) and (max-width: 720px) {
//   body {
//     zoom: 0.66 !important;
//   }
//   .table-wrapper .table-card .el-card__body .table-main {
//     height: 100vh !important;
//   }
// }
/* 屏幕横向显示且高度小于等于 820px  */
// @media screen and (orientation: landscape) and (max-height: 820px) {
//   body {
//     zoom: 0.85 !important;
//   }

//   .table-wrapper .table-card .el-card__body .table-main {
//     height: calc(100vh - 180px) !important;
//     max-height: 100vh !important;
//   }
// }
@media screen and  (max-width: 1280px) {
  // 头部、侧边栏、路由
	#homeContainer .TopHeader,.SideMenu,.router-link-wrapper{
		zoom: 0.8;
	}
  // 主体容器
	#homeContainer .main-container{
		height: calc(100% - 48px) !important;
	}
  // tab样式  
	.el-tabs{
		.el-tabs__header{
			margin: 0 0 1vh;
		}
		.el-tabs__item{
			font-size: 15px !important;
			padding: 0 .5vw !important;
		}
	}
  // 容器（操作页面：报表、看板等）
  .el-main{
    zoom: 0.9;
    .table-wrapper .table-main {
      height: calc(100vh - 250px) !important;
    }
  }
  // 驾驶舱/仪表盘
  .dashboard-wrapper {
    height: calc(100vh - 88px) !important;
  }

}
/* flex响应式布局样式预设*/
.flex {
  display: flex;
}
.flex-1 {
  flex: 1; //自动填充剩余空间，并允许根据需要进行收缩
}
.flex-column {
  display: flex;
  flex-direction: column !important; //竖向排列
}
.flex-wrap {
  display: flex;
  flex-wrap: wrap; //超出容器换行
}
.flex-start{
	display: flex;
  align-items: flex-start; //顶部对齐
}
.flex-center {
  //容器内的元素上下左右居中
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-justify-between {
  display: flex;
  justify-content: space-between; //左右两侧排列
}
.flex-justify-end {
  display: flex;
  justify-content: flex-end; //居右排列
}
.flex-align-center {
  display: flex !important;
  align-items: center; //水平对齐（上下居中）
}

.align-center {
  display: flex;
  align-items: center; //flex 布局下 水平对齐（上下居中）
}
.align-end {
  display: flex;
  align-items: flex-end; //flex 布局下 底部对齐
}
.justify-center {
  display: flex;
  justify-content: center; //flex 布局下 垂直对齐（左右居中）
}

// 向左/右靠
.ml-auto {
  margin-left: auto !important;
}
.mr-auto {
  margin-right: auto !important;
}
// 居中
.m-auto{
  margin: auto !important;
}


/* grid响应式布局样式预设*/
// .grid{
// 	display: grid;
// 	grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
// 	gap: 0;
// }