<template>
	<div id="extensionChecked">
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="inquiry"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twidList = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="展期" name="extensionChecked">
				<BaseLayout>
					<template #header>
						<span class="search-label">提交日期</span>
						<DateSelect
							:dateList="['不限定', '最近30天', '最近60天']"
							@change="
								searchForm.startTime = $event.startTime;
								searchForm.endTime = $event.endTime;
								queryTableData(1);
							"
						/>

						<SearchHistoryInput
							name="companyName"
							placeholder="客户工商注册名称"
							v-model.trim="searchForm.query"
							@input="queryTableData(1)"
						/>

						<el-checkbox v-model="searchForm.status" :true-label="0" :false-label="1" @change="queryTableData(1)"
							>仅显示未审核展期记录</el-checkbox
						>

						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
						<el-button
							v-if="isSuperAdmin"
							:disabled="!isBatch"
							type="text"
							class="el-icon-s-check"
							@click="openDialog([selectedData], '批量审核')"
						>
							批量审核
						</el-button>
					</template>
					<template #main>
						<div class="table-toolbar"> </div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							@selection-change="selectedData = $event"
							selectTrClass="selectTr"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column
								width="50"
								type="selection"
								align="center"
								:selectable="row => row.status == 0 && isSuperAdmin"
							></u-table-column>

							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['commitTime', 'protectDeadline'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<Tooltips
										v-else-if="item.colNo == 'spreadTime'"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:class="[scope.row[item.colNo] == 0 ? 'red' : 'green']"
										:cont-str="scope.row[item.colNo] == 0 ? '未审核' : '已审核'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<u-table-column label="" width="300" align="right">
								<template slot-scope="scope">
									<el-button type="text" size="mini" @click="openDialog(scope.row, '展期审核')">
										<span v-show="scope.row.status == 0 && isSuperAdmin">审核</span>
										<span v-show="scope.row.status == 1">
											<Tooltips
												class="color-666 fs-12"
												:cont-str="scope.row.checkRecord"
												:cont-obj="[
													{ title: '审核人', content: scope.row.checkUname },
													{ title: '审核结果', content: scope.row.checkStatus == 0 ? '通过' : '不通过' },
													{ title: '审核意见', content: scope.row.checkOpinion || '无' },
													{ title: '审核时间', content: dateFormat(scope.row.checkDate, 'lineM') },
												]"
												:cont-width="(scope.column.width || scope.column.realWidth) - 20"
											/>
										</span>
									</el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>

		<!-- 展期申请弹窗 -->
		<el-dialog :visible.sync="dialogExtension" width="30%" :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="8vw" label-position="left" ref="editFormRef" :rules="editRules">
				<div v-if="dialogTitle !== '批量审核'">
					<el-form-item label="客户工商注册名称" prop="registeredBusinessName">
						<el-col :span="24">
							<span> {{ editForm.registeredBusinessName }}</span>
						</el-col>
					</el-form-item>
					<el-form-item label="客户简称" prop="abbreviation">
						<span> {{ editForm.abbreviation }}</span>
					</el-form-item>
					<el-form-item label="所在地区" prop="region">
						<span> {{ editForm.region }}</span>
					</el-form-item>
					<el-form-item label="当前保护期" prop="protectDeadline">
						<span> {{ dateFormat(editForm.protectDeadline, 'lineM') }}</span>
					</el-form-item>
					<el-form-item label="展期至" prop="spreadTime">
						<el-date-picker
							v-model="editForm.spreadTime"
							type="date"
							class="W100"
							placeholder="请选择展期结束时间"
						></el-date-picker>
					</el-form-item>
				</div>

				<el-form-item label="审核意见" prop="checkOpinion">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						placeholder="请输入审核意见，不同意时请填写说明"
						v-model="editForm.checkOpinion"
					></el-input>
				</el-form-item>
				<el-form-item label="" prop="checkStatus">
					<el-radio-group
						v-model="editForm.checkStatus"
						class="flex-align-center"
						@change="editForm.checkOpinion = { 0: '同意', 1: '不同意' }[editForm.checkStatus]"
					>
						<el-radio :label="0">同意</el-radio>
						<el-radio :label="1">不同意</el-radio>
					</el-radio-group>
				</el-form-item>
				<div v-if="dialogTitle == '批量审核'" class="text-right red fs-12">
					* 批量审核通过时，将默认延期至对应渠道设置的最大展期天数！
				</div>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="dialogTitle == '批量审核' ? saveBatchEdit() : saveEdit()">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import { debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
import ChannelSelect from '@/components/ChannelSelect.vue';

export default {
	components: {
		DateSelect,
		ChannelSelect,
	},
	name: 'extensionChecked',
	data() {
		return {
			activeTab: 'extensionChecked',
			selectedData: [], //勾选的行

			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '客户编号', colNo: 'clientNo', align: 'left', width: '100' },
				{ colName: '客户简称', colNo: 'abbreviation', align: 'left', width: '100' },
				{ colName: '客户工商注册名称', colNo: 'registeredBusinessName', align: 'left', width: '' },
				{ colName: '备案方', colNo: 'teamworkName', align: 'left', width: '100' },
				{ colName: '展期提出人', colNo: 'userName', align: 'left', width: '100' },
				{ colName: '提交日期', colNo: 'commitTime', align: 'center', width: '120' },
				{ colName: '失效日期', colNo: 'protectDeadline', align: 'center', width: '120' },
				{ colName: '展期到', colNo: 'spreadTime', align: 'center', width: '100' },
				{ colName: '状态', colNo: 'status', align: 'center', width: '80' },
				{ colName: '备注', colNo: 'remark', align: 'left', width: '' },
			],
			searchForm: {
				query: '',
				status: 0,
				twidList: [],
				channelName: '',
			},
			dialogExtension: false,
			editForm: {
				excid: '',
				checkOpinion: '',
				phoneNo: '',
				spreadTime: '',
				protectDeadline: '',
				checkStatus: '',
			},
			dialogTitle: '客户详情',
			editRules: {
				abbreviation: [{ required: true, message: '客户简称', trigger: 'blur' }],
				clientNeed: [{ required: true, message: '客户需求', trigger: 'blur' }],
				linkman: [{ required: true, message: '请输入对接人信息', trigger: 'blur' }],
				linkphone: [{ required: true, message: '请输入对接人联系方式', trigger: 'blur' }],
				region: [{ required: true, message: '客户地址', trigger: 'blur' }],
				regions: [{ required: true, message: '客户地址', trigger: 'blur' }],
				salesman: [{ required: true, message: '请输入业务顾问', trigger: 'blur' }],
				registeredBusinessName: [{ required: true, message: '客户工商注册名称', trigger: 'blur' }],
				protectDeadline: [{ required: true, message: '请输入备案保护日期', trigger: 'blur' }],
				spreadTime: [{ required: true, message: '请输入展期日期', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 是否批量操作
		isBatch() {
			return this.selectedData.length > 0;
		},
		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectSpreadCheck'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item['checkRecord'] = `${item.checkStatus == 0 ? '通过' : '不通过'}，${item.checkUname}，
								${item.checkOpinion || '无'}，${this.dateFormat(item.checkDate, 'lineM')}`;
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		openDialog(row, type) {
			this.dialogTitle = type;
			if (row) {
				this.editForm.region = row.province + row.city + row.area;
				this.editForm.abbreviation = row.abbreviation;
				this.editForm.spreadTime = row.spreadTime;
				this.editForm.excid = row.excid;
				this.editForm.registeredBusinessName = row.registeredBusinessName;
				this.editForm.protectDeadline = row.protectDeadline;
				this.editForm.checkOpinion = row.checkOpinion;
				this.editForm.checkStatus = row.checkStatus == null ? '' : row.checkStatus;
			}
			this.dialogExtension = true;
		},
		closeDialog() {
			this.dialogExtension = false;
			this.$refs.editFormRef?.resetFields();
		},
		// 展期审核
		saveEdit() {
			const { checkOpinion, spreadTime, excid, checkStatus } = this.editForm;
			if (!checkStatus && checkStatus != 0) {
				this.$message.warning('请选择同意/不同意');
				return;
			}
			if (checkStatus == 1 && !checkOpinion) {
				this.$message.warning('请填写申请不通过的原因！');
				return;
			}
			const str = JSON.stringify({ checkOpinion, spreadTime, excid, checkStatus });
			this.$axios
				.approvalSpreadCheck(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功!');
						this.closeDialog();
						this.queryTableData(1);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('approvalSpreadCheck  |' + error);
				});
		},
		// 批量审核
		saveBatchEdit() {
			const { checkOpinion, checkStatus } = this.editForm;
			if (!checkStatus && checkStatus != 0) {
				this.$message.warning('请选择同意/不同意');
				return;
			}
			if (checkStatus == 1 && !checkOpinion) {
				this.$message.warning('请填写申请不通过的原因！');
				return;
			}
			const str = JSON.stringify({ checkOpinion, excidList: this.selectedData.map(item => item.excid), checkStatus });
			this.$axios
				.approvalSpreadCheckBatch(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功!');
						this.closeDialog();
						this.queryTableData(1);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('approvalSpreadCheckBatch  |' + error);
				});
		},

		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat,

		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
	},
};
</script>

<style lang="scss" scoped>
#extensionChecked {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
