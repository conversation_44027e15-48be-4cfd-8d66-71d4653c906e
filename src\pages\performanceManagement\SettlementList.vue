<template>
	<div class="settlementList">
		<!-- 订单明细组件 -->
		<OrderDetail ref="OrderDetail" @close="queryTableData(1)" />
		<u-table
			ref="uTableRef"
			class="table-main"
			:height="1200"
			:row-height="45"
			:total="tablePageForm.total"
			:page-size="tablePageForm.pageSize"
			:current-page="tablePageForm.currentPage"
			:page-sizes="tablePageForm.pageSizes"
			@handlePageSize="handlePageSize"
			@sort-change="sortChange"
			show-header-overflow="title"
			pagination-show
			use-virtual
			stripe
			show-summary
			:summary-method="summaryMethod"
		>
			<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
			<u-table-column
				v-for="item in tableColumn"
				:key="'colCurr' + item.colNo"
				:label="item.colName"
				:prop="item.colNo"
				:align="item.align"
				:width="item.width"
				sortable="custom"
				resizable
			>
				<template slot-scope="scope">
					<!-- 各种日期 -->
					<Tooltips
						v-if="['collectionTime'].includes(item.colNo)"
						:cont-str="dateFormat(scope.row[item.colNo], 'line')"
						:cont-width="(scope.column.width || scope.column.realWidth) - 20"
					/>
					<!-- 项目类型 -->
					<Tooltips
						v-else-if="item.colNo == 'projectType'"
						:cont-str="projectTypeMap[scope.row[item.colNo]]"
						:cont-width="scope.column.width || scope.column.realWidth"
					/>
					<!-- 合同阶段信息-->
					<Tooltips
						v-else-if="item.colNo == 'contractInfo'"
						:cont-str="getContractInfo(scope.row)"
						:cont-width="scope.column.width || scope.column.realWidth"
					/>
					<!-- 对象类别  0 系统托管-代扣， 1 系统托管-公司，10 用户-->
					<Tooltips
						v-else-if="item.colNo == 'commissionType'"
						:cont-str="commissionTypeMap[scope.row[item.colNo]]"
						:cont-width="scope.column.width || scope.column.realWidth"
					/>
					<!-- 金额保留两位小数-->
					<Tooltips
						v-else-if="['stageAmount', 'allocateSystemAmount', 'allocateAmount'].includes(item.colNo)"
						:cont-str="scope.row[item.colNo]?.toFixed(2) || '0.00'"
						:cont-width="scope.column.width || scope.column.realWidth"
					/>

					<!-- 补偿-->
					<Tooltips
						v-else-if="['allocateCompensationAmount', 'allocateMemo'].includes(item.colNo)"
						class="red"
						:cont-str="scope.row[item.colNo] || ''"
						:cont-width="scope.column.width || scope.column.realWidth"
					/>
					<!-- 来源-->
					<Tooltips
						v-else-if="item.colNo == 'allocateSource'"
						class="hover-green"
						:cont-str="scope.row[item.colNo] || ''"
						:cont-width="scope.column.width || scope.column.realWidth"
						@click.native="openDetail('查看合同', scope.row)"
					/>

					<!-- 默认显示 -->
					<Tooltips
						v-else
						:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
						:cont-width="(scope.column.width || scope.column.realWidth) - 20"
					/>
				</template>
			</u-table-column>
			<!-- 其他列/操作 -->
			<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDialogRow(scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template>
              </u-table-column> -->
		</u-table>
	</div>
</template>
<script>
import { dateFormat, jointString, debounce, sortTableData } from '@/util/tool';
import { bigAdd } from '@/util/math';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import OrderDetail from '@/pages/deliveryManagement/orderManagement/OrderDetail.vue';
import { projectTypeOptions, projectTypeMap, commissionTypeMap } from '@/assets/js/contractSource';

export default {
	name: 'settlementList',
	components: { OrderDetail },
	props: {
		searchForm: Object,
	},
	data() {
		return {
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '收款日期', colNo: 'collectionTime', align: 'center', width: '100' },
				{ colName: '收款金额', colNo: 'stageAmount', align: 'right', width: '90' },
				{ colName: '合同阶段', colNo: 'contractInfo', align: 'left', width: '' },
				{ colName: '项目类型', colNo: 'projectType', align: 'left', width: '80' },
				{ colName: '分配类别', colNo: 'commissionType', align: 'center', width: '80' },
				{ colName: '分配对象', colNo: 'commissionObjectName', align: 'left', width: '130' },
				{ colName: '分配说明', colNo: 'allocateSummary', align: 'left', width: '' },
				{ colName: '业绩分配', colNo: 'allocateSystemAmount', align: 'right', width: '90' },
				{ colName: '补偿', colNo: 'allocateCompensationAmount', align: 'right', width: '80' },
				{ colName: '备注', colNo: 'allocateMemo', align: 'left', width: '' },
				{ colName: '个人收入', colNo: 'allocateAmount', align: 'right', width: '90' },
				{ colName: '来源', colNo: 'allocateSource', align: 'left', width: '' },
			],

			projectTypeMap, // 项目类型
			commissionTypeMap, // 提成类别
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['userInfos']) },
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectDeliverStageCommissionDetailList'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.searchForm,
				dateEnd: this.$moment(this.searchForm.dateStart).endOf('month').valueOf(), // 当前月份最后一天时间戳,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data.map(item => {
							if (item.deliverManagementDetailVO) {
								item.projectType = item.deliverManagementDetailVO.projectType;
							}
							return item;
						});
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		// 获取合同阶段信息
		getContractInfo(row) {
			if (row.omid && row.orderManagementDetailVO) {
				const { orderNo, registeredBusinessName, teamDueDate } = row.orderManagementDetailVO;
				return jointString(' | ', registeredBusinessName, `续费订单号：${orderNo}`, `续费至：${this.dateFormat(teamDueDate)}`);
			} else if (row.deliverManagementDetailVO) {
				const { projectName, registeredBusinessName } = row.deliverManagementDetailVO;
				const { stage, standard } = row.deliverStageManagementVO;
				return jointString(' | ', projectName || registeredBusinessName, stage, standard);
			} else {
				return '无';
			}
		},
		// 表尾合计
		summaryMethod({ columns, data }) {
			// 如果是查询这个分配对象 则对收款金额进行合计
			const summaryProp = ['allocateAmount', 'allocateSystemAmount'];

			const means = ['合计']; // 合计
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					if (summaryProp.includes(column.property)) return Number(item[column.property]) || 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? bigAdd(prev, curr, 2) : prev;
				}, 0); //合计计算
				means[columnIndex] = <span class={sum > 0 ? '' : 'red'}>{sum || ''}</span>;
			}
			return [means];
		},
		// 打开明细
		openDetail(type, row) {
			if (row.omid) {
				// 订单详情
				this.$refs.OrderDetail.openDialog('查看订单', row);
			} else if (row.dmid) {
				// 合同业绩分配详情
				this.$emit('openAllocationPlan', type, row);
			} else {
				this.$message.success(`该记录来源于:${row.allocateSource}!`);
			}
		},

		//数据导出
		openExport: debounce(function () {
			const PROPS = {
				DATA: JSON.stringify({
					...this.searchForm,
				}), //接口参数
				API: 'xxxxxDownload', //导出接口
				downloadData: 'xxxxx明细', //数据报表参数（后台确认字段downloadData）
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),

		dateFormat: dateFormat, //日期format
	},
};
</script>

<style lang="scss" scoped>
.settlementList {
	// width: 100%;
	// height: 100%;
	// overflow: hidden;
	// position: relative;
}
</style>
