/**
 * 路由错误处理
 */
import Router from 'vue-router';

/**
 * 修复路由导航重复问题
 */
export const fixNavigationDuplication = () => {
  const originalPush = Router.prototype.push;
  const originalReplace = Router.prototype.replace;

  Router.prototype.push = function push(location, onResolve, onReject) {
    if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject);
    return originalPush.call(this, location).catch(err => err);
  };

  Router.prototype.replace = function replace(location, onResolve, onReject) {
    if (onResolve || onReject) return originalReplace.call(this, location, onResolve, onReject);
    return originalReplace.call(this, location).catch(err => err);
  };
};

export default {
  fixNavigationDuplication
}; 