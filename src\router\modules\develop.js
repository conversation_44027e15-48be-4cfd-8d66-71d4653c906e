/* 开发管理路由 */

const developerManagement = () => import('@/pages/developmentManagement/developerManagement.vue'); //人员管理
const projectManagement = () => import('@/pages/developmentManagement/projectManagement/projectManagement/projectManagement.vue'); //项目管理
const developmentWorkManagement = () =>
	import('@/pages/developmentManagement/developmentWorkManagement/developmentWorkManagement.vue'); //开发管理
const testingWorkManagement = () => import('@/pages/developmentManagement/testingWorkManagement/testingWorkManagement.vue'); //测试管理
const customerStatistics = () => import('@/pages/developmentManagement/customerStatistics.vue'); //上线质量
const defectManagement = () => import('@/pages/developmentManagement/defectManagement/defectManagement.vue'); //缺陷管理
const demandManagement = () => import('@/pages/developmentManagement/demandManagement/DemandManagement.vue'); //需求管理
const routers = [
	{
		//开发人员管理
		path: '/developerManagement',
		name: 'developerManagement',
		component: developerManagement,
		meta: {
			parentTitle: '开发管理',
			title: '基本设置',
		},
	},
	{
		//现网缺陷管理
		path: '/defectManagement',
		name: 'defectManagement',
		component: defectManagement,
		meta: {
			parentTitle: '开发管理',
			title: '现网缺陷管理',
		},
	},
	{
		//项目管理
		path: '/projectManagement',
		name: 'projectManagement',
		component: projectManagement,
		meta: {
			parentTitle: '开发管理',
			title: '项目管理',
		},
	},
	{
		//需求管理
		path: '/demandManagement',
		name: 'demandManagement',
		component: demandManagement,
		meta: {
			parentTitle: '开发管理',
			title: '需求管理',
		},
	},
	{
		//开发任务管理
		path: '/developmentWorkManagement',
		name: 'developmentWorkManagement',
		component: developmentWorkManagement,
		meta: {
			parentTitle: '开发管理',
			title: '开发任务管理',
		},
	},
	{
		//测试任务管理
		path: '/testingWorkManagement',
		name: 'testingWorkManagement',
		component: testingWorkManagement,
		meta: {
			parentTitle: '开发管理',
			title: '测试任务管理',
		},
	},
	{
		//上线质量
		path: '/customerStatistics',
		name: 'customerStatistics',
		component: customerStatistics,
		meta: {
			parentTitle: '开发管理',
			title: '上线质量',
		},
	},
];

export default routers;
