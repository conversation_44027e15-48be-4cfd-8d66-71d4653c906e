<template>
	<div id="OnlineTesting">
		<!-- 明细组件 -->
		<DetailCom ref="DetailComRef" @close="queryTableData(1)" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="能力评测管理" name="OnlineTesting">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">评测日期</span>
						<DateSelect
							defaultDate="本周"
							:dateList="['本周', '本月', '本年', '不限定']"
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>

						<label class="search-label">项目</label>
						<el-select
							class="w-150"
							size="small"
							v-model="searchForm.project"
							placeholder="项目名称"
							filterable
							clearable
							@change="queryTableData(1)"
						>
							<el-option v-for="item in projectOptions" :key="item.ciid" :label="item.project" :value="item.project"> </el-option>
						</el-select>

						<label class="search-label">标签</label>
						<el-select
							class="w-150"
							size="small"
							v-model="searchForm.ltid"
							placeholder="标签"
							filterable
							multiple
							collapse-tags
							clearable
							@change="queryTableData(1)"
						>
							<el-option v-for="item in labelOptions" :key="item.ltid" :label="item.labelText" :value="item.ltid"> </el-option>
						</el-select>

						<!-- 模糊查询 -->
						<SearchHistoryInput
							name="phone_name"
							placeholder="电话/姓名"
							v-model.trim="searchForm.query"
							@input="queryTableData(1)"
						/>
						<el-checkbox v-model="searchForm.evaluationStatus" :true-label="1" :false-label="''" @change="queryTableData(1)"
							>仅显示评测通过人员
						</el-checkbox>
						<el-checkbox v-model="searchForm.firstInterview" true-label="0" :false-label="''" @change="queryTableData(1)"
							>待一面
						</el-checkbox>
						<el-checkbox v-model="searchForm.twoInterview" true-label="0" :false-label="''" @change="queryTableData(1)"
							>待二面
						</el-checkbox>
						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['startTime', 'endTime'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<Tooltips
										v-else-if="['createTime'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 状态/类型 -->
									<div v-else-if="item.colNo == 'evaluationStatus'" class="flex-align-center">
										<Tooltips
											:class="scope.row[item.colNo] ? 'green' : 'red'"
											:cont-str="[' 不通过', ' 通过'][scope.row[item.colNo]]"
											:cont-width="scope.column.width || scope.column.realWidth"
										/>
										<i
											v-if="scope.row.singleGrowUpQuestionLists"
											:class="scope.row[item.colNo] ? 'green' : 'red'"
											class="el-icon-tickets ml5 fs-14 pointer"
											@click="openDetail(scope.row, 'legacy')"
										></i>
										<i
											v-if="scope.row.candidateItemVOS"
											:class="scope.row[item.colNo] ? 'green' : 'red'"
											class="el-icon-tickets ml5 fs-14 pointer"
											@click="openDetail(scope.row, 'new')"
										></i>
									</div>

									<!-- 时间修改 -->
									<el-date-picker
										v-else-if="item.colNo == 'firstInterviewTime' || item.colNo == 'twoInterviewTime'"
										:disabled="
											item.colNo == 'twoInterviewTime' && (!scope.row.firstInterviewTime || scope.row.firstInterview !== 2)
										"
										class="W100"
										size="mini"
										v-model="scope.row[item.colNo]"
										value-format="timestamp"
										type="date"
										placeholder="请选择"
										format="yyyy-MM-dd"
										@change="updateRow(scope.row)"
									></el-date-picker>

									<!-- 结果修改 -->
									<el-radio-group
										size="mini"
										v-else-if="item.colNo == 'firstInterview'"
										:disabled="!scope.row.firstInterviewTime"
										v-model="scope.row[item.colNo]"
										@change="updateRow(scope.row)"
										:class="['no-border', scope.row[item.colNo] == 2 ? 'green' : 'red']"
									>
										<el-radio-button :label="2"><span class="el-icon-success"> 通过</span></el-radio-button>
										<el-radio-button :label="1"><span class="el-icon-error"></span> 不通过</el-radio-button>
									</el-radio-group>
									<el-radio-group
										size="mini"
										v-else-if="item.colNo == 'twoInterview'"
										:disabled="!scope.row.twoInterviewTime"
										v-model="scope.row[item.colNo]"
										@change="updateRow(scope.row)"
										:class="['no-border', scope.row[item.colNo] == 2 ? 'green' : 'red']"
									>
										<el-radio-button :label="2"><span class="el-icon-success"> 通过</span></el-radio-button>
										<el-radio-button :label="1"><span class="el-icon-error"></span> 不通过</el-radio-button>
									</el-radio-group>

									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<u-table-column label="" width="100" align="right">
								<template slot-scope="scope">
									<el-button type="text" class="el-icon-edit-outline" @click="openDialog(scope.row)"></el-button>
									<el-popover
										title="标签管理"
										placement="left"
										width="600"
										trigger="click"
										popper-class="label-popover"
										@show="querySelectedLabels(scope.row.cid)"
										@hide="hidePopover"
									>
										<div>
											<span class="label-title pt10 pb10">
												<span class="flex-align-center">
													<span>可选标签列表</span>
													<el-button type="text" class="el-icon-edit-outline p0" @click="updateInputVisible = !updateInputVisible"
														>{{ updateInputVisible ? '完成' : '编辑' }}
													</el-button>
												</span>
											</span>
											<!-- 可选标签列表内容 -->
											<div class="flex-align-center flex-wrap">
												<div v-if="!updateInputVisible" class="flex-align-center flex-wrap">
													<el-tag
														class="m5 flex-align-center w-fit-content"
														size="small"
														:key="tag.ltid"
														v-for="tag in noSelectedLabels"
														closable
														@click="selectLabel(tag.ltid, scope.row.cid)"
														@close="deleteLabel(tag.ltid)"
													>
														<Tooltips class="max-w-100 inline-block" :cont-str="tag.labelText" />
													</el-tag>

													<!-- 新建标签 -->
													<el-input
														class="w-100"
														v-if="inputVisible"
														v-model="inputValue"
														ref="saveTagInput"
														size="mini"
														@keyup.enter.native.stop="updateLabel('', inputValue, scope.row.cid)"
														@blur.stop="updateLabel('', inputValue, scope.row.cid)"
													>
													</el-input>
													<el-button v-else class="button-new-tag" size="mini" @click.stop="showInput"> + 新建标签 </el-button>
												</div>

												<!-- 编辑状态 -->
												<div v-else>
													<el-input
														:key="tag.ltid"
														v-for="tag in noSelectedLabels"
														class="w-100 mr10 mb10"
														v-model="tag.labelText"
														size="mini"
														@keyup.enter.native.stop="updateLabel(tag.ltid, tag.labelText, scope.row.cid)"
														@blur.stop="updateLabel(tag.ltid, tag.labelText, scope.row.cid)"
													>
													</el-input>
												</div>
											</div>
										</div>

										<div>
											<span class="label-title pt10 pb10">当前已选标签</span>
											<div v-if="!updateInputVisible" class="flex-align-center flex-wrap">
												<el-tag
													class="m5"
													size="small"
													:key="tag.labelText"
													v-for="tag in selectedLabels"
													closable
													effect="dark"
													type="primary"
													@click="
														searchForm.ltid.includes(tag.ltid)
															? searchForm.ltid.splice(searchForm.ltid.indexOf(tag.ltid), 1)
															: searchForm.ltid.push(tag.ltid);
														queryTableData(1);
													"
													@close="closeLabel(tag.ltid, scope.row.cid)"
												>
													<span v-if="searchForm.ltid.includes(tag.ltid)"> ● </span>
													<span>{{ tag.labelText }}</span>
												</el-tag>
											</div>
											<!-- 编辑状态 -->
											<div v-else>
												<el-input
													:key="tag.ltid"
													v-for="tag in selectedLabels"
													class="w-100 mr10 mb10"
													v-model="tag.labelText"
													size="mini"
													@keyup.enter.native.stop="updateLabel(tag.ltid, tag.labelText, scope.row.cid)"
													@blur.stop="updateLabel(tag.ltid, tag.labelText, scope.row.cid)"
												>
												</el-input>
											</div>
										</div>
										<el-button slot="reference" type="text" class="el-icon-price-tag"></el-button>
									</el-popover>
									<!-- <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button> -->
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="项目题库管理" name="TestBankManagement">
				<TestBankManagement v-if="activeTab == 'TestBankManagement'"></TestBankManagement>
			</el-tab-pane>
		</el-tabs>
		<el-dialog width="620px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">面试结果</span>
			<el-form :model="editForm" :rules="formRules" label-width="120px" label-position="left" @submit.native.prevent>
				<el-form-item label="面试得分" prop="interviewScore">
					<el-input v-model="editForm.interviewScore" placeholder="请输入得分" clearable></el-input>
				</el-form-item>
				<el-form-item label="HR处理结果" prop="hrResult">
					<el-input
						v-model="editForm.hrResult"
						placeholder="请输入HR处理结果(如：是否面试通过，通知xx月xx日到岗以及其他备注等)"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEdit">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DetailCom from './components/OnlineTestingDetail.vue'; //明细组件
import TestBankManagement from './TestBankManagement.vue'; //明细组件
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件

// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
export default {
	// import引入的组件需要注入到对象中才能使用
	components: { DetailCom, DateSelect, TestBankManagement },
	name: 'OnlineTesting', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'OnlineTesting', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '项目', colNo: 'project', align: 'left', width: '130' },
				{ colName: '姓名', colNo: 'candidateName', align: 'left', width: '80' },
				{ colName: '联系方式', colNo: 'phoneNo', align: 'left', width: '100' },
				// { colName: '评测日期', colNo: 'createTime', align: 'center', width: '100' },
				{ colName: '评测日期', colNo: 'startTime', align: 'center', width: '120' },
				// { colName: '结束时间', colNo: 'endTime', align: 'center', width: '120' },
				// { colName: '成长能力(分数)', colNo: 'growthThinkingAbility', align: 'right', width: '120' },
				// { colName: '逻辑能力(分数)', colNo: 'logicalThinkingAbility', align: 'right', width: '120' },
				// { colName: '时间加分项(分数)', colNo: 'timeBonus', align: 'right', width: '130' },
				{ colName: '用时(分钟)', colNo: 'userTime', align: 'right', width: '80' },
				{ colName: '评测总分', colNo: 'totalScore', align: 'right', width: '80' },
				{ colName: '评测状态', colNo: 'evaluationStatus', align: 'left', width: '100' },
				{ colName: '一面时间', colNo: 'firstInterviewTime', align: 'center', width: '150' },
				{ colName: '一面结果', colNo: 'firstInterview', align: 'center', width: '120' },
				{ colName: '二面时间', colNo: 'twoInterviewTime', align: 'center', width: '150' },
				{ colName: '二面结果', colNo: 'twoInterview', align: 'center', width: '120' },
				{ colName: '面试得分', colNo: 'interviewScore', align: 'right', width: '80' },
				{ colName: 'HR处理结果', colNo: 'hrResult', align: 'left', width: '' },
			],
			// 查询表单
			searchForm: {
				evaluationStatus: '',
				query: '',
				project: '',
				firstInterview: '',
				twoInterview: '',
				ltid: [],
				// 其他...
			},
			// 弹窗和编辑表单
			dialogEdit: false,
			editForm: {
				interviewScore: '',
				hrResult: '',
			},
			formRules: {
				// interviewScore: [{ required: true, message: '请输入得分', trigger: 'blur' }],
				// hrResult: [{ required: true, message: '请输入HR处理结果', trigger: 'blur' }],
			},
			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			projectOptions: [], //项目
			labelOptions: [], //标签
			noSelectedLabels: [], //未选择的标签
			selectedLabels: [], //已选择的标签
			// 标签相关
			updateInputVisible: false,
			inputVisible: false,
			inputValue: '',
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.searchForm = JSON.parse(window?.localStorage.getItem(this.$options.name + '_searchForm')) || this.searchForm;
		this.queryTableData(1);
		this.queryProjectOptions(1);
		this.queryLabelOptions(1);
	},
	activated() {
		this.queryTableData(1);
		this.queryProjectOptions(1);
		this.queryLabelOptions(1);
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		hidePopover() {
			this.inputVisible = false;
			this.inputValue = '';
			this.noSelectedLabels = [];
			this.selectedLabels = [];
		},
		// 查询已选择的标签(获取可选择的标签)
		async querySelectedLabels(cid) {
			if (!cid) return;
			const API = 'selectCandidateLabelText';
			try {
				const res = await this.$axios[API](JSON.stringify({ cid }));
				if (res.data.success) {
					this.noSelectedLabels =
						this.labelOptions?.filter(aItem => {
							aItem.inputVisible = false;
							return res.data.data?.every(bItem => {
								return aItem.labelText !== bItem.labelText;
							});
						}) || [];
					this.selectedLabels = res.data.data;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 新建/修改标签
		async updateLabel(ltid, labelText, cid) {
			if (!labelText) {
				this.inputVisible = false;
				return;
			}
			const API = 'addLabelText';
			try {
				const res = await this.$axios[API](JSON.stringify({ labelText, ltid }));
				if (res.data.success) {
					this.inputVisible = false;
					this.inputValue = '';

					await this.queryLabelOptions();
					if (ltid) {
						await this.querySelectedLabels(cid); //查询已选择的标签
						this.$succ(res.data.message);
					} else {
						// 新增标签成功并自动添加到已选择的标签中
						const newLtid = this.labelOptions[this.labelOptions?.length - 1]?.ltid;
						await this.selectLabel(newLtid, cid);
					}
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 删除标签
		async deleteLabel(ltid) {
			const API = 'deleteLabelText';
			try {
				const confirmed = await this.$confirm('此操作将永久删除该标签, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				});
				if (confirmed) {
					const res = await this.$axios[API](JSON.stringify({ ltid }));
					if (res.data.success) {
						this.queryLabelOptions();
						this.$succ(res.data.message);
					} else {
						this.$err(res.data.message);
					}
				} else {
					this.$message.info('已取消');
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 候选人添加标签
		async selectLabel(ltid, cid) {
			const API = 'addCandidateLabel';
			try {
				const res = await this.$axios[API](JSON.stringify({ cid, ltid }));
				if (res.data.success) {
					this.querySelectedLabels(cid);
					this.queryTableData();
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 候选人移除标签
		async closeLabel(ltid, cid) {
			const API = 'deleteCandidateLabel';
			try {
				const res = await this.$axios[API](JSON.stringify({ cid, ltid }));
				if (res.data.success) {
					this.querySelectedLabels(cid);
					this.queryTableData();
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 显示新建标签输入框
		showInput() {
			this.inputVisible = true;
			this.$nextTick(() => {
				// this.$refs.saveTagInput.focus();
				this.$refs.saveTagInput.$refs.input.focus();
			});
		},

		// 查询所有标签
		async queryLabelOptions() {
			try {
				const API = 'selectLabelText';
				try {
					const res = await this.$axios[API](JSON.stringify({ labelText: '' }));
					if (res.data.success) {
						this.labelOptions = res.data.data || [];
					} else {
						this.$err(res.data.message);
					}
				} catch (error) {
					console.error(`${API} |` + error);
				}
			} catch (error) {
				console.error(error);
			}
		},

		// 修改一面二面情况
		async updateRow(row) {
			row.firstInterview = row.firstInterviewTime ? row.firstInterview : 0;
			row.twoInterview = row.twoInterviewTime ? row.twoInterview : 0;

			const API = 'candidateHrResult';
			this.$axios[API](JSON.stringify({ ...row }))
				.then(res => {
					if (res.data.success) {
						this.queryTableData(1);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},

		// 打开问卷详情
		openDetail(row, type) {
			this.$refs.DetailComRef.showDetailCom(row, type);
		},

		// 打开弹窗
		openDialog(row) {
			this.editForm = _.deepClone(row);
			this.dialogEdit = true;
		},
		// 关闭弹窗
		closeDialog() {
			this.dialogEdit = false;
			this.editForm = _.resetValues(this.editForm);
		},
		// 保存面试结果
		saveEdit() {
			const API = 'candidateHrResult';
			this.$axios[API](JSON.stringify({ ...this.editForm }))
				.then(res => {
					if (res.data.success) {
						this.closeDialog();
						this.queryTableData(1);
						this.$succ(res.data.message);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
		// 切换tab
		changeTab() {
			if (this.activeTab == 'OnlineTesting') {
				this.queryTableData(1);
				this.queryProjectOptions(1);
			}
		},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			window?.localStorage.setItem(this.$options.name + '_searchForm', JSON.stringify(this.searchForm));

			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectCandidateSituation'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.searchForm,
				...this.dateSelectObj,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item.totalScore = 0;
							const {
								growthThinkingAbility,
								logicalThinkingAbility,
								timeBonus,
								singleGrowUpQuestionLists,
								candidateItemVOS,
								project,
							} = item;
							if (singleGrowUpQuestionLists) {
								item.totalScore = _.accAdd(_.accAdd(growthThinkingAbility, logicalThinkingAbility), timeBonus);
							} else {
								// 合计总分
								item.totalScore = candidateItemVOS?.reduce((total, cItem) => {
									return _.accAdd(total, cItem.actualScore, 0);
								}, 0);

								if (project?.includes('合伙人') || project?.includes('人才选拔')) {
									item.totalScore = _.accAdd(item.totalScore, timeBonus);
								}
							}
						});

						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		// 查询所有项目
		async queryProjectOptions() {
			const API = 'selectCandidateQuestion';
			try {
				const res = await this.$axios[API](JSON.stringify({ pageNum: 1, pageSize: '' }));
				if (res.data.success) {
					this.projectOptions = res.data.data;
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
	},
};
</script>

<style lang="scss" scoped>
#OnlineTesting {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
<style lang="scss">
#OnlineTesting {
	.no-border {
		.el-radio-button__inner {
			border: none !important;
			border-radius: none !important;
			box-shadow: none !important;
			padding: 0 5px;
			background: transparent;
			color: #c0c4cc;
		}
	}
	.green {
		.is-active,
		.is-focus {
			.el-radio-button__inner {
				color: #1e9d6f;
			}
		}
	}
	.red {
		.is-active,
		.is-focus {
			.el-radio-button__inner {
				color: #f56c6c;
			}
		}
	}
}

.label-popover {
	.w-fit-content {
		width: fit-content !important;
	}
}
</style>
