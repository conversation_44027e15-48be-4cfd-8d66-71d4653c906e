/**
 * 请求方法封装模块
 */
import axios from 'axios';
import { Message } from 'element-ui';
import { LoadingInstance, DEFAULT_DELAY } from './loading';

// 重要常量定义
const LOGIN_PATH = '/#/login'; // 登录路径
const TOKEN_EXPIRED_CODE = 8003; // token过期状态码
const AUTH_REQUIRED_CODE = 2001; // 需要登录认证状态码
const USER_INFO_KEY = 'OPS_USER_INFO'; // 用户信息存储key

/**
 * 重定向到登录页面
 * @param {string} redirectPath - 重定向路径
 */
const redirectToLogin = (redirectPath = '') => {
	window.location.href = redirectPath ? `${LOGIN_PATH}?redirect=${redirectPath}` : LOGIN_PATH;
};

/**
 * 处理响应错误
 * @param {Object} error - 错误对象
 */
const handleResponseError = error => {
	LoadingInstance.close();
	return Promise.reject(error);
};

/**
 * 处理响应数据
 * @param {Object} response - 响应对象
 */
const handleResponse = response => {
	LoadingInstance.close();

	if (!response) return;

	const { data } = response;
	const code = data?.code;

	// 处理相关状态码（如果后续多的话要抽出来）
	switch (code) {
		// 需要登录认证
		case AUTH_REQUIRED_CODE: {
			const currentHash = window.location.hash;
			const redirectPath = currentHash.startsWith('#/')
				? currentHash.substring(2, currentHash.indexOf('?') > -1 ? currentHash.indexOf('?') : undefined)
				: ''; // 获取当前路径
			redirectToLogin(redirectPath); // 重定向到登录页面
			break;
		}
		// token过期
		case TOKEN_EXPIRED_CODE: {
			window?.sessionStorage.removeItem(USER_INFO_KEY);
			Message({ type: 'warning', message: 'token过期,请重新登录' });
			redirectToLogin(); // 重定向到登录页面
			break;
		}
		default: {
			// 其他状态码
			break;
		}
	}

	return response;
};

/**
 * 配置请求参数
 * @param {Object} config - 请求配置
 * @returns {Object} - 处理后的配置
 */
const setupRequestConfig = (config = {}) => {
	const { timeout, resType } = config;

	// 设置超时时间
	if (timeout) {
		axios.defaults.timeout = timeout;
	}

	// 设置响应类型
	axios.defaults.responseType = resType || 'json';

	return config;
};

/**
 * 启动加载状态Loading
 * @param {Object} config - 请求配置
 */
const startLoading = (config = {}) => {
	const { timeout } = config;
	// 默认请求超过1.5秒显示 loading，长请求使用较长的延迟
	const loadingDelay = timeout > DEFAULT_DELAY ? timeout : DEFAULT_DELAY;
	LoadingInstance.start({}, loadingDelay);
};

/**
 * POST请求封装
 * @param {string} url - 请求URL
 * @param {Object} data - 请求参数
 * @param {Object} config - 请求配置
 * @returns {Promise} - 请求Promise
 */
export const fetchPost = (url, data, config = {}) => {
	setupRequestConfig(config); // 配置请求参数
	startLoading(config); // 启动加载状态Loading
	return axios.post(url, data, config).then(handleResponse).catch(handleResponseError);
};

/**
 * GET请求封装
 * @param {string} url - 请求URL
 * @param {Object} data - 请求参数
 * @param {Object} config - 请求配置
 * @returns {Promise} - 请求Promise
 */
export const fetchGet = (url, data, config = {}) => {
	setupRequestConfig(config); // 配置请求参数
	startLoading(config); // 启动加载状态Loading

	// 处理不同响应类型
	let headers = {};
	if (config.resType === 'html') {
		// 如果响应类型为html，则设置响应类型为text
		axios.defaults.responseType = 'text';
		// 设置基础URL
		axios.defaults.baseURL = '';
		// 设置请求头为text/plain
		headers = {
			'Content-Type': 'text/plain',
		};
	}

	return axios
		.get(url, { params: data, headers, ...config })
		.then(handleResponse)
		.catch(handleResponseError);
};

// 请求方法映射
export const requestMethods = {
	post: fetchPost,
	get: fetchGet,
};

export default {
	fetchPost,
	fetchGet,
	requestMethods,
};
