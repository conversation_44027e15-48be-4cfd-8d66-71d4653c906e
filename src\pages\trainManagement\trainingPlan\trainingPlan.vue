<template>
	<div id="courseProduction">
		<!-- 明细组件 -->
		<TrainingPlanDetail ref="trainingPlanDetail" @close="queryTableData(1)" />

		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="培养计划" name="courseProduction">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">创建日期</span>
						<DateSelect
							@change="
								searchForm.startDate = $event.startTime;
								searchForm.endDate = $event.endTime;
								queryTableData(1);
							"
						/>
						<!-- 模糊查询 -->
						<el-input
							class="searchBox"
							size="small"
							v-model="searchForm.query"
							placeholder="请输入课程/人员名称"
							@input="queryTableData(1)"
							clearable
						></el-input>

						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button type="text" class="icon-third-bt_newdoc" @click="openDetail('添加培养计划', null)">添加</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['startTime', 'endTime', 'createTime', 'planEndTime'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 使用版本 -->
									<Tooltips
										v-else-if="item.colNo == 'trainPlan'"
										class="green hover-green"
										@click.native="openDetail('修改培养计划', scope.row)"
										:cont-str="scope.row[item.colNo]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<Tooltips
										v-else-if="item.colNo == 'courseCount'"
										class="green hover-green"
										@click.native="openDetail('修改课程', scope.row)"
										:cont-str="scope.row[item.colNo] + '项'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<Tooltips
										v-else-if="item.colNo == 'userCount'"
										class="green hover-green"
										@click.native="openDetail('修改人员', scope.row)"
										:cont-str="scope.row[item.colNo] + '人'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<u-table-column label="" width="" align="right">
								<template slot-scope="scope">
									<el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { debounce, dateFormat, sortTableData } from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import TrainingPlanDetail from './trainingPlanDetail.vue'; //明细组件

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		TrainingPlanDetail,
	},
	name: 'courseProduction', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'courseProduction', //激活tab页
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '培养计划', colNo: 'trainPlan', align: 'left', width: '200' },
				{ colName: '计划说明', colNo: 'planDesc', align: 'left', width: '' },
				{ colName: '课程', colNo: 'courseCount', align: 'right', width: '120' },
				{ colName: '人员', colNo: 'userCount', align: 'right', width: '120' },
				{ colName: '计划结束', colNo: 'planEndTime', align: 'center', width: '150' },
			],
			// 查询表单
			searchForm: {
				channelName: [],
				query: '',
				twidList: [],
				// 其他...
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开明细
		openDetail(type, row) {
			this.$refs.trainingPlanDetail.showDetailCom(type, row);
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectTrainPlan'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: dateFormat,
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		deleteRow(row) {
			if (!row.tpid) return;
			this.$confirm(row.trainPlan + '将被删除', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					this.$axios
						.deleteTrainPlan(JSON.stringify({ tpid: row.tpid }))
						.then(res => {
							if (res.data.success) {
								this.$succ('删除成功');
								this.queryTableData();
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteTrainPlan |' + error);
						});
				})
				.catch(() => {
					this.$message({
						type: 'info',
						message: '已取消',
					});
				});
		},
	},
};
</script>

<style lang="scss" scoped>
#courseProduction {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
