<template>
	<div id="developmentWorkManagement">
		<!-- 任务明细弹窗 -->
		<TaskDetail ref="TaskDetail" @turnTask="removeNode" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="我的待办" name="developmentWorkManagement">
				<div class="taskManagemnet-gantt">
					<div class="gantt-header">
						<el-button
							type="text"
							class="mr30"
							:icon="isExpanded ? 'el-icon-s-fold' : 'el-icon-s-unfold'"
							@click="changeExpanded(isExpanded)"
							>{{ isExpanded ? '全部折叠' : '全部展开' }}
						</el-button>
						<el-checkbox-group size="mini" v-model="searchForm.statusList" @change="getTreeData">
							<el-checkbox v-for="(item, index) in statusList" :label="item.id" :key="index">
								<span class="flex-align-center">
									<span class="circle" :style="{ color: colorMap[item.id] }">●</span>
									<span> {{ item.status }}</span>
								</span>
							</el-checkbox>
						</el-checkbox-group>
					</div>
					<div class="gantt-content">
						<split-pane :min-percent="20" :default-percent="23" split="vertical">
							<template slot="paneL">
								<div class="left">
									<div class="search-box">
										<el-input
											class="searchBox ml20"
											size="small"
											clearable
											v-model="queryStr"
											placeholder="请输入任务编号/任务名查询"
										></el-input>
										<el-button type="text" class="el-icon-refresh-right" @click="getTreeData('refresh')">刷新</el-button>
									</div>
									<div class="taskManagemnet-tree">
										<el-tree
											:data="treeData"
											ref="treeRef"
											node-key="id"
											default-expand-all
											:renderContent="renderContent"
											:props="{ children: 'selectProjectGanttChartVOS', label: 'userName' }"
											@node-expand="clickNodeExpand"
											@node-collapse="clickNodeCollapse"
											:filter-node-method="filterNode"
										></el-tree>
									</div>
								</div>
							</template>
							<!-- 右边表格 -->
							<template slot="paneR">
								<div class="right">
									<div class="date-table-wrap">
										<table class="date-table" cellspacing="0">
											<thead>
												<!-- 日期 -->
												<tr>
													<th
														v-for="(item, index) in dateColList"
														:key="'date' + index"
														:style="{ 'background-color': item.setbKColor }"
														>{{ item.date.includes('/') ? item.date : '' }}
													</th>
												</tr>
												<!-- 星期 -->
												<tr>
													<th
														v-for="(item, index) in weekColList"
														:key="'week' + index"
														:style="{ 'background-color': item.setbKColor }"
													>
														{{ item.week }}
													</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="(task, index) in filteredData" :key="'task' + index">
													<!-- 每一个格子 -->
													<td
														v-for="(item, tdindex) in dateColList"
														:key="tdindex"
														:style="{ 'background-color': item.setbKColor }"
													>
														<!-- 格子容器（设置背景色） -->
														<div
															class="td-task"
															:style="{
																'background-color': index % 2 == 0 ? '' : 'transparent',
																color: task.userName ? '#28D094' : '',
															}"
														>
															<!-- 人名 -->
															<span v-if="task.userName && task.hoursMap[item.date]">
																<Tooltips
																	class="td-task-tooltip pointer"
																	:cont-str="task.hoursMap[item.date]"
																	:cont-obj="[
																		{ title: '姓名', content: task.userName },
																		{ title: '日期', content: item.date.includes('/') ? item.date : '周计' },
																		{ title: '工时', content: task.hoursMap[item.date] + 'h' },
																	]"
																	:cont-width="20"
																/>
															</span>
															<!-- 任务 -->
															<span v-else-if="task.pmtid && task.hoursMap[item.date]" @click="openDetail(task, '执行研发任务')">
																<el-tooltip :content="String(task.popString)" placement="bottom" effect="light">
																	<span> {{ task.hoursMap[item.date] }}</span>
																</el-tooltip>
															</span>
															<span v-else> </span>
														</div>
													</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</template>
						</split-pane>
					</div>
				</div>
			</el-tab-pane>

			<el-tab-pane label="开发任务河流" name="workRiver" id="projectGantt">
				<dev-work-river ref="workRiver" v-if="activeTab == 'workRiver'"></dev-work-river>
			</el-tab-pane>
			<el-tab-pane label="成果评价" name="resultsEvaluation" id="projectGantt">
				<results-evaluation ref="resultsEvaluation" v-if="activeTab == 'resultsEvaluation'"></results-evaluation>
			</el-tab-pane>
			<el-tab-pane label="研发日报" name="developDaily" id="projectGantt">
				<develop-daily ref="developDaily" v-if="activeTab == 'developDaily'"></develop-daily>
			</el-tab-pane>
			<el-tab-pane label="任务明细表" name="workDetail" id="projectGantt">
				<work-detail ref="workDetail" v-if="activeTab == 'workDetail'"></work-detail>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<script>
import { debounce, deepClone, dateFormat } from '@/util/tool';
import folderImg from '@/assets/img/folder.svg';
import developDaily from './developDaily.vue'; //开发日报
import resultsEvaluation from './resultsEvaluation.vue'; //成功评价
import workDetail from './workDetail.vue'; //开发任务明细表
import devWorkRiver from './developmentWorkRiver.vue'; //开发河流
import splitPane from 'vue-splitpane'; //拖拽分割面板组件
import TaskDetail from './components/taskDetailCom'; //任务明细弹窗

export default {
	name: 'developmentWorkManagement',
	components: {
		developDaily,
		resultsEvaluation,
		workDetail,
		devWorkRiver,
		splitPane,
		TaskDetail,
	},

	data() {
		return {
			activeTab: 'developmentWorkManagement',
			queryStr: '',
			treeData: [], //项目树
			// 日期相关
			defaultTime: new Date(),
			searchForm: {
				statusList: [0, 1, 5],
			},

			statusList: [
				{ id: 0, status: '开发延误' },
				{ id: 5, status: '转测不通过' },
				{ id: 1, status: '计划中' },
			],
			colorMap: {
				0: '#ec808d', //红 开发延误
				1: '#d7d7d7', //灰 计划中
				2: '#ab47bc', //紫 开发中
				3: '#2196f3', //蓝 已转测 测试计划中
				4: '#28d094', //绿 完成
				5: '#facd91', //橙 转测不通过
				6: '#ec808d', //红 测试延误
			},
			// 任务状态
			statusMap: {
				0: '延误',
				1: '计划中',
				2: '开发中',
				3: '已转测',
				4: '完成',
				5: '转测不通过',
				6: '测试延误',
			},

			// 是否展开
			isExpanded: true,
			// 甘特图相关
			dateColList: [
				// { date: "09/01", colspan: "1" },
			],
			weekColList: [
				// { week: "一", colspan: "1" },
			],
			hoursMap: {},
			tableData: [],
			tableDataCopy: [],

			weekMap: {
				1: '一',
				2: '二',
				3: '三',
				4: '四',
				5: '五',
				6: '六',
				0: '日',
			},
		};
	},
	watch: {
		// 监听-某行数据
		queryStr(val) {
			this.$refs.treeRef.filter(val);
		},
	},
	computed: {
		filteredData() {
			return this.tableData.filter(item => !item.disable);
		},
	},
	mounted() {
		this.getTreeData();
		this.getColList();
	},
	activated() {
		this.changeTab();
	},
	methods: {
		// 打开任务明细
		openDetail(row, type) {
			this.$refs.TaskDetail.openDetail(row, type);
		},
		// 删除已转测的任务
		removeNode(pmtid, nodes = this.treeData) {
			function removeNodeByPmtid(pmtid, nodes) {
				for (let i = 0; i < nodes.length; i++) {
					const node = nodes[i];
					if (node.pmtid === pmtid) {
						nodes.splice(i, 1); // 删除节点
						return;
					}
					if (node.selectProjectGanttChartVOS && node.selectProjectGanttChartVOS.length > 0) {
						removeNodeByPmtid(pmtid, node.selectProjectGanttChartVOS); // 递归删除子节点
					}
				}
			}
			removeNodeByPmtid(pmtid, nodes);
			// 从 tableData 中找到 pmtid 的节点并删除
			this.tableData = this.tableData.filter(item => item.pmtid !== pmtid);
		},
		// 数值加法精准度设置
		accAdd(arg1, arg2) {
			let r1, r2, m;
			try {
				r1 = arg1.toString().split('.')[1].length;
			} catch (e) {
				r1 = 0;
			}
			try {
				r2 = arg2.toString().split('.')[1].length;
			} catch (e) {
				r2 = 0;
			}
			m = Math.pow(10, Math.max(r1, r2));
			m *= 10;
			return parseInt(arg1 * m + arg2 * m) / m;
		},
		// 切换tab
		changeTab(tab, event) {
			if (this.activeTab == 'developmentWorkManagement') {
				this.getTreeData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 某个时间在某年第几周
		getWeekFalg(date) {
			if (!date) return 0;
			const YEAR = Number(this.$moment(date).format('YYYY'));
			const weekday = new Date(date).getDay() || 7; //星期几
			const temptTime = new Date(date);
			//周1+5天=周六
			temptTime.setDate(temptTime.getDate() - weekday + 1 + 5);
			let firstDay = new Date(temptTime.getFullYear(), 0, 1);
			const dayOfWeek = firstDay.getDay();
			let spendDay = 1;
			if (dayOfWeek != 0) {
				spendDay = 7 - dayOfWeek + 1;
			}
			firstDay = new Date(temptTime.getFullYear(), 0, 1 + spendDay);
			const d = Math.ceil((temptTime.valueOf() - firstDay.valueOf()) / 86400000);
			const result = Math.ceil(d / 7) + 1;
			return YEAR + '' + result;
		},
		// 获取图表列、表头
		getColList() {
			const dateColList = [];
			const weekColList = [];
			const hoursMap = {};
			const colIndex = 0;
			const oneDay = 24 * 60 * 60 * 1000;
			const nowDayOfWeek = new Date().getDay(); //今天本周的第几天
			const startTime = new Date().getTime() - (nowDayOfWeek + 6) * oneDay; // 表格日期范围
			const endTime = startTime + 28 * oneDay;
			const days = (endTime - startTime) / oneDay;
			let setbKColor = ''; //设置颜色
			const nowDay = this.dateFormat(new Date().getTime(), 'MD');
			for (let i = 0; i < days; i++) {
				const date = this.dateFormat(startTime + oneDay * i, 'MD');
				const week = this.weekMap[new Date(startTime + oneDay * i).getDay()];
				const weekFalg = this.getWeekFalg(startTime + oneDay * i);

				if (week == '日') {
					setbKColor = '#ffffbb';
				} else if (date == nowDay) {
					setbKColor = '#e1f5fe';
				} else {
					setbKColor = '';
				}

				dateColList.push({
					colIndex: i,
					date,
					colspan: '1',
					setbKColor,
				});
				weekColList.push({
					colIndex: i,
					week,
					colspan: '1',
					setbKColor,
				});

				if (week == '日') {
					// 插入周计
					hoursMap[weekFalg] = 0;
					dateColList.push({
						colIndex: i,
						date: weekFalg,
						colspan: '1',
						setbKColor: '#e8f5e9',
						isWeekCount: 'true',
					});

					weekColList.push({
						colIndex: i,
						week: '周计',
						colspan: '1',
						setbKColor: '#e8f5e9',
					});
				}

				hoursMap[date] = 0;
			}
			this.dateColList = dateColList;
			this.weekColList = weekColList;
			this.hoursMap = hoursMap;
		},
		// 获取树结构数据
		getTreeData: debounce(function (type) {
			this.treeData = [];
			this.$axios
				.getProductMyAgent(JSON.stringify({ statusList: this.searchForm.statusList }))
				.then(res => {
					if (res.data.success) {
						if (type == 'refresh') {
							this.$message.success('刷新成功，数据已更新！');
							this.getColList();
						}
						// 递归：任务分时
						const totalRecur = arr => {
							arr.forEach(project => {
								project.hoursMap = deepClone(this.hoursMap);
								if (project.userName) {
									if (project.selectMyDayWorkHoursCountVOS) {
										project.selectMyDayWorkHoursCountVOS.forEach(item => {
											const date = [this.dateFormat(item['day'], 'MD')];
											project.hoursMap[date] = item['workingHours'];
											const weekFalg = this.getWeekFalg(item['day']);
											project.hoursMap[weekFalg] = this.accAdd(project.hoursMap[weekFalg], item['workingHours']);
										});
									}
								}

								if (project.selectProjectTaskGanttChartVOList) {
									project.selectProjectTaskGanttChartVOList.forEach(task => {
										task.hoursMap = deepClone(this.hoursMap);
										task.hoursMap[this.dateFormat(task.endTime, 'MD')] = task.productTime;
										task.popString = `【${task.taskNo}】${task.taskName}  要求转测日期:${this.dateFormat(
											task.endTime,
											'MD',
										)} 当前状态：${this.statusMap[task.status]}`;

										const weekFalg = this.getWeekFalg(task.endTime);
										task.hoursMap[weekFalg] = this.accAdd(task.hoursMap[weekFalg], task.productTime);
										// 项目项的工时合计
										for (const key in task.hoursMap) {
											project.hoursMap[key] = this.accAdd(project.hoursMap[key], task.hoursMap[key]);
										}
									});
								}
								// console.log("project", project);
								if (project.selectProjectGanttChartVOS) {
									totalRecur(project.selectProjectGanttChartVOS);
								}
							});
						};
						totalRecur([res.data.data]);

						// 将数据拼接在孩子节点
						const concatTreeNode = array => {
							array.map(item => {
								if (item.selectProjectTaskGanttChartVOList && item.selectProjectTaskGanttChartVOList.length > 0) {
									if (item.selectProjectGanttChartVOS && item.selectProjectGanttChartVOS.length > 0) {
										item.selectProjectGanttChartVOS.push(...item.selectProjectTaskGanttChartVOList);
									} else {
										item.selectProjectGanttChartVOS = item.selectProjectTaskGanttChartVOList;
									}
								}
								// 进入递归循环
								if (item.selectProjectGanttChartVOS && item.selectProjectGanttChartVOS.length > 0) {
									concatTreeNode(item.selectProjectGanttChartVOS);
								}
							});
						};
						concatTreeNode([res.data.data]);
						this.treeData.push(res.data.data);
						if (this.treeData && this.treeData.length > 0) {
							const treeDataCopy = deepClone(this.treeData);
							const result = this.treeToArray(treeDataCopy);
							this.tableData = result;
							this.tableDataCopy = deepClone(this.tableData);
						}
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('getProductMyAgent |' + error);
				});
		}),
		// 渲染树节点
		renderContent(h, { node, data, store }) {
			if (!data.productUid) data.productUid = { arid: '', auid: '', userName: '' };
			if (!data.projectTurnCheck) data.projectTurnCheck = { arid: '', auid: '', userName: '' };
			const {
				taskName,
				status,
				taskNo,
				endTime,
				productTime,
				projectTurnCheck,
				testCheckTime,
				total,
				classifyName,
				userName,
				taskCount,
				workingHours,
			} = data;
			const color = this.colorMap[status];
			return (
				<div class="custom-tree-node">
					{taskName && 
						<div
							class="task-content"
							on-click={e => {
								e.stopPropagation();
								this.openDetail(data, '执行研发任务');
							}}
						>
							<div class="task-info flex-align-center ">
								<span class="task-label" style={{ color }}></span>
								<div class="taskNo ellipsis">
									{taskNo} {taskName}
								</div>
							</div>
							<div class="task-time ellipsis">
								{this.dateFormat(endTime, 'MD')} {productTime || 0}h {projectTurnCheck?.userName || ''}{' '}
								{this.dateFormat(testCheckTime, 'MD')}
							</div>
						</div>
					}
					{!taskName && 
						<div class="folder-content ">
							<div class="folder-title">
								<img src={folderImg} class="folder-img" v-show={!total} />
								<span class="folder-name ellipsis">{classifyName || userName || total}</span>
							</div>
							<div class="folder-time">
								<span>
									{taskCount}项/{workingHours}小时
								</span>
							</div>
						</div>
					}
				</div>
			);
		},
		// 过滤节点
		filterNode(value, data) {
			if (!value) return true;
			const classifyName = data.classifyName || '';
			const taskName = data.taskName || '';
			const taskNo = data.taskNo || '';

			return classifyName.indexOf(value) !== -1 || taskName.indexOf(value) !== -1 || taskNo.indexOf(value) !== -1;
		},
		// 展开节点
		clickNodeExpand(data, node) {
			this.expandAllNode(node, true);
			const dataCopy = this.treeToArray([deepClone(data)]);
			dataCopy.shift();
			const temp = deepClone(this.tableData);
			if (node.isLeaf) return;
			//打开时插入数组
			temp.forEach((item, index) => {
				dataCopy.forEach(value => {
					if (item.pmcid == value.pmcid) item.disable = false;
				});
			});
			this.tableData = temp;
		},
		// 折叠节点
		clickNodeCollapse(data, node) {
			const dataCopy = this.treeToArray([deepClone(data)]);
			dataCopy.shift();
			const temp = deepClone(this.tableData);
			if (node.isLeaf) return;

			//折叠时删除数组
			temp.forEach((item, index) => {
				if (!index) {
					temp[0].expanded = true; //目录
				}
				dataCopy.forEach(value => {
					if (item.pmcid == value.pmcid && (item.pmtid || item.classifyName != data.classifyName)) item.disable = true;
				});
			});

			this.tableData = temp;
		},
		// 全部折叠/展开
		changeExpanded(isExpanded) {
			this.isExpanded = !this.isExpanded;
			if (!this.isExpanded) {
				this.expandAllNode(this.$refs.treeRef.store.root, this.isExpanded);
				const dataCopy = this.tableDataCopy;
				//
				const temp = deepClone(this.tableData);
				//折叠时删除数组
				temp.forEach((item, index) => {
					if (!index) {
						temp[0].expanded = true; //目录
					}
					dataCopy.forEach(value => {
						if (item.pmcid == value.pmcid && (item.pmtid || item.classifyName != dataCopy.classifyName)) item.disable = true;
					});
				});
				this.tableData = temp;
			} else {
				this.expandAllNode(this.$refs.treeRef.store.root, this.isExpanded);
				this.tableData = this.tableDataCopy;
			}
		},
		expandAllNode(node, flag) {
			for (let i = 0; i < node.childNodes.length; i++) {
				node.childNodes[i].expanded = flag;
				if (node.childNodes[i].childNodes.length > 0) {
					this.expandAllNode(node.childNodes[i], flag);
				}
			}
		},

		// 将树扁平化成数组
		treeToArray(source) {
			// 该方法是将树结构扁平化
			const res = [];
			source.forEach(item => {
				res.push(item);
				item.selectProjectGanttChartVOS && res.push(...this.treeToArray(item.selectProjectGanttChartVOS));
			});
			return res.map(item => {
				if (item.selectProjectGanttChartVOS) {
					delete item.selectProjectGanttChartVOS;
				}
				return item;
			});
		},
		//日期format
		dateFormat,
	},
};
</script>

<style lang="scss">
#developmentWorkManagement {
	width: 100%;
	overflow: hidden;
	position: relative;
	.el-tabs__content {
		position: inherit;
	}
}
</style>
