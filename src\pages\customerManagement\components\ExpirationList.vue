<template>
	<div id="ExpirationList" :class="moveToggle ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<BaseLayout>
			<template #header>
				<span class="search-label"> {{ titleName }} 到期客户 </span>
				<!-- <el-date-picker
					class="w-150"
					v-model="dateSelectObj.startDate"
					type="month"
					value-format="timestamp"
					placeholder="不限"
					@change="queryTableData(1)"
					clearable
					size="small"
				>
				</el-date-picker> -->

				<el-input
					class="searchBox"
					size="small"
					clearable
					v-model="searchForm.query"
					placeholder="客户短名称"
					@input="queryTableData(1)"
				></el-input>

				<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
				<el-button type="text" class="el-icon-arrow-left" @click="moveToggle = false">返回</el-button>
			</template>
			<template #main>
				<div class="table-toolbar"> </div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<!-- <u-table-column type="selection" width="30" label=""></u-table-column> -->
					<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="['validFrom', 'validTo'].includes(item.colNo)"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 编号 -->
							<Tooltips
								v-else-if="item.colNo == 'teamCode'"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>
							<!-- 使用版本 -->
							<Tooltips
								v-else-if="item.colNo == 'version'"
								:cont-str="versionMap[scope.row.version]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
							<!-- 状态 -->
							<Tooltips
								v-else-if="item.colNo == 'status'"
								:cont-str="statusMap[scope.row.status]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 剩余天数 -->
							<Tooltips
								v-else-if="item.colNo == 'surplusDay'"
								:class="[scope.row.surplusDay < 10 ? 'red' : '']"
								:cont-str="scope.row.surplusDay"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	props: {
		twidList: Array,
		channelName: Array,
	},
	name: 'ExpirationList',
	data() {
		return {
			rowData: {},
			titleName: '',
			//日期相关
			dateSelectObj: {
				startDate: null,
				endDate: null,
			},
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '团队编号', colNo: 'teamCode', align: 'left', width: '' },
				{ colName: '短名称', colNo: 'teamName', align: 'left', width: '' },
				{ colName: '区域', colNo: 'region', align: 'left', width: '' },
				{ colName: '渠道', colNo: 'channelName', align: 'left', width: '' },
				{ colName: '分销/代理', colNo: 'teamworkName', align: 'left', width: '' },
				{ colName: '业务顾问', colNo: 'salesName', align: 'left', width: '' },
				{ colName: '实施顾问', colNo: 'consultantName', align: 'left', width: '' },
				{ colName: '使用版本', colNo: 'version', align: 'left', width: '' },
				{ colName: '到期日期', colNo: 'validTo', align: 'center', width: '90' },
				{ colName: '剩余天数', colNo: 'surplusDay', align: 'right', width: '90' },
				{ colName: '年费', colNo: 'annualFee', align: 'right', width: '80' },
				{ colName: '最近智造评分', colNo: 'manufacturingRating', align: 'right', width: '' },
			],
			searchForm: {
				query: '', //模糊查询
				teamCode: '',
				teamName: '',
				machineLabel: '',
				productLabel: '',
				region: '',
				version: [1, 2],
				consultantName: '',
				salesName: '',
				channelName: [],
				twidList: [],
				status: [],
			},
			versionMap: {
				1: '标准版',
				2: 'OEE',
				4: '微信版',
			},
			moveToggle: false, //滑动控制
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		channelName(newVal) {
			this.searchForm.channelName = newVal;
		},
		twidList(newVal) {
			this.searchForm.twidList = newVal;
		},
		moveToggle(newVal) {
			if (newVal) {
				this.queryTableData();
			} else {
				this.tableData = [];
				this.tablePageForm.total = 0;
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.uid = this.$route?.query?.queryId || '';
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectMyClient'; //接口
			const DATA = JSON.stringify({
				...this.dateSelectObj,
				...this.searchForm,

				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							this.$refs.uTableRef?.reloadData(this.tableData);
							this.$refs.uTableRef?.doLayout();
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},

		showList(monthIndex, row, searchForm, year) {
			// 查询条件
			this.searchForm.consultantName = searchForm?.consultantName || '';
			this.searchForm.salesName = searchForm?.salesName || '';

			// 获取当前月份第一天时间戳
			const now = new Date(year); // 获取当前年份

			if (monthIndex !== 'totalAnnualFee' && monthIndex < 13) {
				// 非合计
				const firstDayOfMonth = new Date(now.getFullYear(), monthIndex - 1, 1); // 当月的第一天
				this.dateSelectObj.startDate = firstDayOfMonth.getTime(); // 获取时间戳
				this.dateSelectObj.endDate = _.getNowMonthEndDay(this.dateSelectObj.startDate); // 当前月份最后一天时间戳
				this.titleName =
					(this.searchForm.salesName || this.searchForm.consultantName) +
					'  ' +
					this.dateFormat(this.dateSelectObj.startDate, 'YM');
			} else {
				// 合计
				const firstDayOfYear = new Date(now.getFullYear(), 0, 1); // 当年的第一天
				this.dateSelectObj.startDate = firstDayOfYear.getTime(); // 获取时间戳
				const lastDayOfYear = new Date(now.getFullYear(), 11, 31); // 当年的最后一天
				this.dateSelectObj.endDate = lastDayOfYear.getTime() + 86399999; // 获取时间戳
				this.titleName =
					(this.searchForm.salesName || this.searchForm.consultantName) +
					'  ' +
					this.dateFormat(this.dateSelectObj.startDate, 'yyyy');
			}

			this.moveToggle = true;
			this.queryTableData();
		},
	},
};
</script>

<style lang="scss" scoped>
#ExpirationList {
	.table-main {
		height: calc(100vh - 250px) !important;
	}
}
</style>
