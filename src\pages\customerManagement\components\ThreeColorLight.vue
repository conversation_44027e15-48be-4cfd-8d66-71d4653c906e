<template>
	<div id="ThreeColorLight" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<LightDetail ref="LightDetail" @refresh="showDetailCom(null)" />
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }} - 三色灯清单</span>
				<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content table-wrapper">
				<div class="table-toolbar">
					<el-button type="text" class="el-icon-check" @click="openDialogUpgrade">批量升级固件</el-button>
					<el-button type="text" class="icon-third_xiugai" @click="openLightDetail(null)">批量修改</el-button>
					<el-button type="text" class="el-icon-close" @click="deleteAll">批量删除</el-button>
					<el-button type="text" class="icon-third-bt_newdoc" @click="openDialogAdd">添加</el-button>
					<el-button type="text" class="icon-third_searchC" v-popover:thSearch>查找</el-button>
				</div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					@selection-change="selectedData = $event"
					selectTrClass="selectTr"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column type="selection" width="30" label=""></u-table-column>
					<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="['simTime', 'updateTime', 'createTime'].includes(item.colNo)"
								:toolClass="'tdTwoNormal'"
								:cont-str="dateFormat(scope.row[item.colNo], 'ALL')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 编号 -->
							<Tooltips
								v-else-if="item.colNo == 'sim'"
								:toolClass="'tdTwoNormal'"
								class="hover-green green"
								@click.native="openLightDetail(scope.row)"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<!-- 升级状态 -->
							<Tooltips
								v-else-if="item.colNo == 'updateStatu'"
								:cont-str="updateStatusMap[scope.row.updateStatu]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 在线状态 -->
							<div v-else-if="item.colNo == 'onLine'">
								<span v-if="scope.row.onLine">
									<i
										v-if="scope.row.currentState"
										class="icon-third_graysd fs-24"
										:style="{ color: onlineColorMap[scope.row.currentState] }"
									></i>
									<span v-else>{{ onlineStatusMap[scope.row.onLine] }}</span>
								</span>
								<span v-else>{{ onlineStatusMap[scope.row.onLine] }}</span>
							</div>

							<Tooltips
								v-else
								:toolClass="'tdTwoNormal'"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</div>
		</div>
		<!-- 放大镜查询 -->
		<el-popover v-model="searchPopver" ref="thSearch" placement="bottom-end" width="400" :visible-arrow="false">
			<!-- 放大镜搜索表单 -->
			<div class="searchPop p0 fs-12">
				<div class="searchPop-header">
					<span>查找条件</span>
					<i class="searchPop-header-close el-icon-close" @click.stop="searchPopver = false"></i>
				</div>
				<el-form :model="searchForm" label-width="80px" label-position="left" class="W100 pt10 pr20">
					<el-form-item label="三色灯编号" prop="productName">
						<el-input placeholder="请输入三色灯编号" v-model="searchForm.sim" size="mini" @input="queryTableData(1)"></el-input>
					</el-form-item>
					<el-form-item label="产品" prop="productName">
						<el-input placeholder="请输入产品" v-model="searchForm.productName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="芯片" prop="chipName">
						<el-input placeholder="请输入芯片" v-model="searchForm.chipName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="固件版本" prop="firmwareName">
						<el-input placeholder="请输入固件版本" v-model="searchForm.firmwareName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="运营商" prop="operator">
						<el-input placeholder="请输入运营商" v-model="searchForm.operator" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="绑定团队" prop="bindingTeamName">
						<el-input placeholder="请输入绑定团队" disabled v-model="searchForm.bindingTeamName" size="mini"></el-input>
					</el-form-item>
				</el-form>
				<div class="mt20 border-top text-right">
					<el-button type="text" class="color-666" @click.stop="queryTableData(1)">确定</el-button>
				</div>
			</div>
		</el-popover>
		<!-- 批量升级固件版本 -->
		<el-dialog :visible.sync="dialogUpgrade" width="500px" append-to-body :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">批量升级固件版本</el-row>
			<el-form :model="upgradeForm" label-width="7vw" label-position="left">
				<el-form-item label="产品" prop="chipName">
					<el-input placeholder="请输入产品" v-model="upgradeForm.productName"></el-input>
				</el-form-item>
				<el-form-item label="芯片" prop="phoneNo">
					<el-input placeholder="请输入芯片" v-model="upgradeForm.chipName"></el-input>
				</el-form-item>
				<el-form-item label="目标固件版本" prop="ftid">
					<el-select class="W100" v-model="upgradeForm.ftid" placeholder="请选择适用产品">
						<el-option
							v-for="item in firmwareOptions"
							:key="'product' + item.ftid"
							:label="item.firmwareName + ' ' + dateFormat(item.releaseTime, 'line')"
							:value="item.ftid"
						>
						</el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveUpgrade">批量升级</el-button>
			</el-row>
		</el-dialog>
		<!-- 添加三色灯 -->
		<el-dialog :visible.sync="dialogAdd" width="500px" append-to-body :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">添加三色灯</el-row>
			<el-form :model="editLightForm" label-width="6vw" label-position="left" ref="addLightRef" :rules="editRules">
				<el-form-item label="三色灯编号" prop="sim">
					<el-input placeholder="请输入三色灯编号" v-model="editLightForm.sim"></el-input>
				</el-form-item>
				<el-form-item label="产品" prop="productId">
					<el-select clearable class="W100" v-model="editLightForm.productId" placeholder="请选择产品">
						<el-option v-for="item in productList" :key="'product' + item.id" :label="item.productName" :value="item.id">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="芯片" prop="chipName">
					<el-autocomplete
						placeholder="请输入芯片"
						v-model="editLightForm.chipName"
						:fetch-suggestions="queryChipData"
						class="W100"
					>
					</el-autocomplete>
					<!-- <el-input placeholder="请输入芯片" v-model="editLightForm.chipName" ></el-input> -->
				</el-form-item>
				<el-form-item label="运营商" prop="operator">
					<el-input placeholder="移动" v-model="editLightForm.operator"></el-input>
				</el-form-item>
				<el-form-item label="运营商到期时间" prop="newNum">
					<el-date-picker
						v-model="editLightForm.operatorExpiration"
						type="date"
						class="W100"
						placeholder="请选择运营商到期时间"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="渠道" prop="channel">
					<el-select class="W100" v-model="editLightForm.channel" placeholder="请选择渠道">
						<el-option label="未知" :value="0"></el-option>
						<el-option label="预装" :value="1"></el-option>
						<el-option label="改造" :value="2"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="团队" prop="bindingTeamName">
					{{ searchForm.bindingTeamName }}
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveAddLight">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';
import LightDetail from './ThreeColorLightDetail.vue';
export default {
	name: 'materielsDetail',
	directives: {},
	components: { LightDetail },
	props: {
		canEditBtn: Boolean, //修改权限
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐

			titleName: '',
			searchForm: {
				chipName: '',
				firmwareName: '',
				bindingTeamName: '', //绑定团队
				operator: '',
				productName: '',
				sim: '',
				onLine: ['0', '1'],
			},
			selectedData: [], //批量选择的数据
			// 表格相关
			tableData: [],
			tableDataCopy: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '三色灯编号', colNo: 'sim', align: 'left', width: '180' },
				{ colName: '产品', colNo: 'productName', align: 'left', width: '150' },
				{ colName: '固件版本', colNo: 'firmwareName', align: 'left', width: '' },
				{ colName: '目标固件版本', colNo: 'updateFirmwareName', align: 'left', width: '' },
				{ colName: '升级状态', colNo: 'updateStatu', align: 'left', width: '90' },
				{ colName: '升级尝试次数', colNo: 'upgradeCount', align: 'right', width: '120' },
				{ colName: '本地时间', colNo: 'simTime', align: 'left', width: '90' },
				{ colName: '最后心跳', colNo: 'updateTime', align: 'left', width: '90' },
				{ colName: '在线状态', colNo: 'onLine', align: 'center', width: '90' },
			],
			updateStatusMap: {
				0: '待升级',
				1: '升级中',
				2: '升级失败',
				3: '升级成功',
			},
			onlineColorMap: {
				'001': '#28D094',
				'010': '#F7A944',
				100: '#DC143C',
			},
			onlineStatusMap: {
				false: '离线',
				true: '在线',
			},
			searchPopver: false, // 放大镜查询
			dialogUpgrade: false, //控制弹窗显隐
			// 升级表单
			upgradeForm: {
				chipName: '',
				productName: '',
				ftid: '',
				sims: [],
			},
			firmwareOptions: [], //可升级固件选项
			dialogAdd: false, //控制弹窗显隐
			//三色灯编辑
			editLightForm: {
				sim: '',
				productId: '',
				channel: '',
				chipName: '',
				operator: '',
				operatorExpiration: '',
				tid: '',
			},
			editRules: {
				sim: [{ required: true, message: '请输入三色灯编号', trigger: 'blur' }],
				productId: [{ required: true, message: '请选择适用产品', trigger: ['blur', 'change'] }],
				chipName: [{ required: true, message: '请输入芯片', trigger: 'blur' }],
			},
			productList: [], //产品列表
		};
	},
	created() {},
	computed: {},
	watch: {
		showCom(val) {
			if (!val) {
				this.$emit('refresh');
			}
		},
	},
	mounted() {},
	methods: {
		// 批量删除三色灯
		deleteAll() {
			const simList = [],
				tlIdList = [];
			if (this.selectedData && this.selectedData.length > 0) {
				this.$confirm('勾选的三色灯将被删除', '删除三色灯', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.selectedData.forEach(item => {
							simList.push(item.sim);
							tlIdList.push(item.tlId);
						});
						this.$axios
							.batchDeleteTricolourLight(
								JSON.stringify({
									simList: simList,
									tlIdList: tlIdList,
								}),
							)
							.then(res => {
								if (res.data.success) {
									this.$succ('操作成功!');
									this.queryTableData(1);
								} else {
									this.$err(res.data.message);
								}
							})
							.catch(error => {
								console.log('batchDeleteTricolourLight |' + error);
							});
					})
					.catch(() => {
						this.$message.info('已取消');
					});
			} else {
				this.$message.warning('请勾选三色灯后再删除');
			}
		},
		//存储最近输入的芯片
		saveChipInput(str) {
			let chipRecent = null;
			if (window.localStorage) {
				chipRecent = JSON.parse(window.localStorage.getItem('chipInput'));
			} else {
				chipRecent = JSON.parse(_.getCookie('chipInput'));
			}
			if (!chipRecent) {
				chipRecent = [];
				chipRecent.push(str);
				if (window.localStorage) {
					window.localStorage.setItem('chipInput', JSON.stringify(chipRecent));
				} else {
					_.setCookie('chipInput', JSON.stringify(chipRecent));
				}
			} else {
				//存在最近输入,插入最近,并保存
				chipRecent.unshift(str);
				if (chipRecent.length > 3) {
					chipRecent.length = 3;
				}
				if (window.localStorage) {
					window.localStorage.setItem('chipInput', JSON.stringify(chipRecent));
				} else {
					_.setCookie('chipInput', JSON.stringify(chipRecent));
				}
			}
		},
		// 查询最近输入的芯片
		queryChipData(str, cb) {
			//最近输入
			let newChipData = [],
				chipRecent = null;
			if (window.localStorage) {
				chipRecent = JSON.parse(window.localStorage.getItem('chipInput')) || [];
			} else {
				chipRecent = JSON.parse(_.getCookie('chipInput')) || [];
			}
			if (chipRecent.length > 0) {
				newChipData = chipRecent.map(item => {
					return { value: item };
				});
			}
			const results = str
				? newChipData.filter(item => {
						return item.value.indexOf(str) === 0;
					})
				: newChipData;
			cb(results);
		},
		// 查询产品列表
		queryProductList() {
			this.productList = [];
			this.$axios
				.getTricolourProductList(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.productList = res.data.data;
					}
				})
				.catch(error => {
					console.log('getTricolourProductList |' + error);
				});
		},
		// 添加三色灯保存
		saveAddLight() {
			const sim = this.editLightForm.sim,
				proId = this.editLightForm.productId,
				chipName = this.editLightForm.chipName;
			if (!sim) {
				this.$message.warning('请输入三色灯编号');
				return;
			}
			if (!proId) {
				this.$message.warning('请选择适用产品');
				return;
			}
			if (!chipName) {
				this.$message.warning('请输入芯片');
				return;
			}
			const str = JSON.stringify({
				channel: this.editLightForm.channel,
				chipName: this.editLightForm.chipName,
				operator: this.editLightForm.operator,
				operatorExpiration: this.editLightForm.operatorExpiration
					? new Date(this.editLightForm.operatorExpiration).getTime()
					: '',
				productId: proId,
				sim: sim,
				bindingTid: this.editLightForm.bindingTid,
			});
			this.$axios
				.addTricolourLight(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.saveChipInput(this.editLightForm.chipName);
						this.closeDialog();
						this.queryTableData(1);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('addTricolourLight |' + error);
				});
		},
		// 关闭弹窗
		closeDialog() {
			this.upgradeForm = _.resetValues(this.upgradeForm);
			this.editLightForm = _.resetValues(this.editLightForm);
			this.dialogUpgrade = false;
			this.dialogAdd = false;
		},
		// 批量升级弹窗
		openDialogUpgrade() {
			if (this.selectedData && this.selectedData.length > 0) {
				const chipName = this.selectedData[0].chipName;
				const productId = this.selectedData[0].productId;
				const productName = this.selectedData[0].productName;
				const sims = [];
				// 必须是同产品且同芯片的三色灯可以进行批量升级
				const isSameChip = this.selectedData.every(item => {
					sims.push(item.sim);
					return item.chipName == chipName && item.productId == productId;
				});
				if (isSameChip) {
					this.upgradeForm.chipName = chipName;
					this.upgradeForm.productName = productName;
					this.upgradeForm.sims = sims;
					this.$axios.selectUpgradeFirmwareByProductId(JSON.stringify({ productId, chipName })).then(res => {
						if (res.data.success) {
							this.firmwareOptions = res.data.data;
							this.dialogUpgrade = true;
						} else {
							this.$message.warning(res.data.message);
						}
					});
				} else {
					this.$message.warning('必须是同产品且同芯片的三色灯可以进行批量升级');
				}
			} else {
				this.$message.warning('请勾选数据后再进行批量升级');
			}
		},
		// 添加三色灯弹窗
		openDialogAdd() {
			this.dialogAdd = true;
			this.queryProductList();
		},
		// 保存-升级
		saveUpgrade() {
			const { sims, ftid } = this.upgradeForm;
			if (!ftid) {
				this.$message.warning('请选择目标固件版本');
				return;
			}
			this.$axios
				.batchFirmwareUpgrade(JSON.stringify({ sims, ftid }))
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.closeDialog();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('batchFirmwareUpgrade |' + error);
				});
		},
		// 批量修改
		openLightDetail(row) {
			if (row) {
				this.$refs.LightDetail.showDetailCom([row]);
			} else {
				if (this.selectedData.length == 0) {
					this.$message.warning('请先选择数据');
					return;
				}
				// 批量修改
				this.$refs.LightDetail.showDetailCom(this.selectedData);
			}
		},
		//自定义排序
		sortChange({ prop, order }) {
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		// 显示弹窗 获取详情信息
		showDetailCom(rowData) {
			if (rowData) {
				this.titleName = rowData.teamName;
				this.editLightForm.bindingTid = rowData.tid;
				this.searchForm.bindingTeamName = rowData.teamName;
			}
			this.queryTableData();
		},
		queryTableData: _.debounce(function () {
			this.$axios
				.getTricolourLightList(
					JSON.stringify({
						...this.searchForm,
						pageNum: this.tablePageForm.currentPage,
						pageSize: this.tablePageForm.pageSize,
					}),
				)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tableDataCopy = _.deepClone(this.tableData);
						this.tablePageForm.total = res.data.totalItems;
						this.$nextTick(() => {
							this.$refs.uTableRef?.reloadData(this.tableData);
							this.$refs.uTableRef?.doLayout();
						});
						if (this.tableData.length > 0) {
							this.showCom = true;
						} else {
							this.$message.warning('未查询到该团队的三色灯数据，请绑定后再重试！');
						}
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('getTricolourLightList |' + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
	},
};
</script>
<style lang="scss" scoped>
#ThreeColorLight {
	.table-main {
		height: calc(100vh - 223px) !important;
	}
}
</style>
