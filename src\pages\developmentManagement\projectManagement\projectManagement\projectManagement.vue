<template>
	<div id="projectManagement">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 项目详情 -->
		<ProjectDetail ref="ProjectDetail" @close="queryTableData(1)" @openWorkPlan="openWorkPlan($event)" />
		<WorkPlan ref="WorkPlan" @close="queryTableData(1)" />

		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="项目管理" name="projectManagement">
				<BaseLayout>
					<template #header>
						<span class="search-label">项目要求完成日期</span>
						<DateSelect
							@change="
								searchForm.endTime = $event.endTime;
								searchForm.startTime = $event.startTime;
								queryTableData(1);
							"
						/>

						<span class="search-label">完成状态</span>
						<el-select
							v-model="searchForm.status"
							placeholder="请选择"
							size="small"
							clearable
							filterable
							@change="
								searchForm.status == '' && (searchForm.statusList = []);
								searchForm.status == 1 && (searchForm.statusList = [8]);
								searchForm.status == 2 && (searchForm.statusList = [0, 1, 2, 3, 4, 5, 6, 7, 9]);
								searchForm.status == 3 && (searchForm.statusList = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]);
								queryTableData(1);
							"
						>
							<el-option label="已完成" :value="1"> </el-option>
							<el-option label="未完成" :value="2"> </el-option>
							<el-option label="全部" :value="3"> </el-option>
						</el-select>

						<el-input
							class="searchBox w-300"
							size="small"
							clearable
							v-model="searchForm.queryParam"
							placeholder="项目名称/客户名称/项目经理"
							@input="queryTableData(1)"
						></el-input>

						<div class="ml-auto">
							<el-button type="text" class="icon-third-bt_newdoc" @click="openDetail(null)">添加</el-button>
							<ExportBtn @trigger="openExport" />
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
						</div>
					</template>
					<template #main>
						<div class="table-toolbar">
							<el-checkbox-group class="mr-auto" v-model="searchForm.statusList" size="mini" @change="queryTableData(1)">
								<el-checkbox v-for="(status, index) in statusList" :label="index" :key="index">{{ status }}</el-checkbox>
							</el-checkbox-group>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" type="index" align="center"> </u-table-column>

							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable
								resizable
							>
								<template slot-scope="scope">
									<Tooltips
										v-if="item.colNo == 'startTime' || item.colNo == 'endTime'"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<span v-else-if="item.colNo == 'status'" :class="{ red: scope.row.status == 9 }">
										{{ statusMap[scope.row.status] }}
									</span>

									<Tooltips
										v-else-if="['businessPeople', 'projectManager', 'testManager', 'technicalManager'].includes(item.colNo)"
										:cont-str="scope.row[item.colNo]?.userName"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<Tooltips
										v-else-if="item.colNo == 'projectNo'"
										class="hover-green green"
										:class="{ red: scope.row.status == 9 }"
										@click.native="openDetail(scope.row)"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<!-- 项目名称 -->
									<Tooltips
										v-else-if="item.colNo == 'projectName'"
										class="hover-green"
										:class="{ red: scope.row.status == 9 }"
										@click.native="openWorkPlan(scope.row)"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<Tooltips
										v-else-if="item.colNo == 'taskDelay'"
										class="red"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>

							<u-table-column label="" width="88" align="right" fixed="right">
								<template slot-scope="scope">
									<el-button type="text" size="mini" @click="openWorkPlan(scope.row)">任务规划 </el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="研发项目总览" name="projectOverview">
				<project-overview v-if="activeTab == 'projectOverview'" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { debounce, dateFormat, sortTableData } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
import ProjectDetail from './projectDetail'; //明细组件
import WorkPlan from '@/pages/developmentManagement/projectManagement/workPlan/workPlan.vue'; // 工作计划
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
import projectOverview from '@/pages/developmentManagement/projectManagement/projectOverview.vue'; // 项目概览
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		ExportTable,
		projectOverview,
		ProjectDetail,
		WorkPlan,
		ExportBtn,
	},
	name: 'projectManagement',
	data() {
		return {
			activeTab: 'projectManagement',
			statusList: [
				'规划中',
				'需求与方案评审通过',
				'功能分解完成',
				'开发计划完成',
				'开发全部完成',
				'测试全部完成',
				'功能验收通过',
				'发版准备就绪',
				'发版完成',
				'延误',
			],

			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableDataCopy: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 200, 500, 1000],
			},
			tableColumn: [
				{ colName: '项目编号', colNo: 'projectNo', align: 'left', width: '110' },
				{ colName: '项目名称', colNo: 'projectName', align: 'left', width: '200' },
				{ colName: '要求开始日期', colNo: 'startTime', align: 'center', width: '90' },
				{ colName: '要求完成日期', colNo: 'endTime', align: 'center', width: '90' },
				{ colName: '客户简称', colNo: 'customer', align: 'left', width: '' },
				{ colName: '交付项目经理', colNo: 'businessPeople', align: 'left', width: '100' },
				{ colName: '开发项目经理', colNo: 'projectManager', align: 'left', width: '100' },
				{ colName: '测试经理', colNo: 'testManager', align: 'left', width: '100' },
				{ colName: '技术经理', colNo: 'technicalManager', align: 'left', width: '100' },
				{ colName: '项目状态', colNo: 'status', align: 'left', width: '' },
				{ colName: '任务小时数', colNo: 'taskHours', align: 'right', width: '' },
				{ colName: '任务项数', colNo: 'taskNum', align: 'right', width: '' },
				{ colName: '完成项数', colNo: 'taskComplete', align: 'right', width: '' },
				{ colName: '延误项数', colNo: 'taskDelay', align: 'right', width: '' },
				{ colName: '执行中项数', colNo: 'taskInExecution', align: 'right', width: '' },
				{ colName: '会议纪要', colNo: 'conference', align: 'right', width: '' },
			], //当前显示列

			searchForm: {
				status: 2,
				queryParam: '',
				statusList: [0, 1, 2, 3, 4, 5, 6, 7, 9],
			},

			statusMap: {
				0: '规划中',
				1: '需求与方案评审通过',
				2: '功能分解完成',
				3: '开发计划完成',
				4: '开发全部完成',
				5: '测试全部完成',
				6: '功能验收通过',
				7: '发版准备就绪',
				8: '发版完成',
				9: '延误',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.searchForm = JSON.parse(window?.localStorage.getItem(this.$options.name + '_searchForm')) || this.searchForm;
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开明细
		openDetail(row) {
			this.$refs.ProjectDetail.showDetailCom(row);
		},

		//切换tab页
		changeTab(tab, event) {},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			window?.localStorage.setItem(this.$options.name + '_searchForm', JSON.stringify(this.searchForm));
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectProject'; //接口
			this.$axios[API](JSON.stringify(this.searchForm))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		//任务规划弹窗
		openWorkPlan(row) {
			this.$nextTick(() => {
				this.$refs.WorkPlan.showDetailCom(row);
			});
		},

		dateFormat, //日期format
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop == 'businessPeople' || prop == 'projectManager' || prop == 'technicalManager' || prop == 'testManager') {
						if (!a?.userName) return -1;
						if (!b?.userName) return 1;
						return a?.userName.localeCompare(b?.userName, 'zh-CN');
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify(this.searchForm), //接口参数
				API: 'exportProject', //导出接口
				downloadData: '项目列表导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
#projectManagement {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
}
</style>
