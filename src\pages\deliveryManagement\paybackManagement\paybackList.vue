<template>
	<div id="paybackList" :class="moveToggle ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<BaseLayout :showHeader="false">
			<template #main>
				<div class="table-toolbar">
					<span class="mr-auto">
						<el-select
							v-model="selectDecimal"
							placeholder="请选择计量单位"
							size="mini"
							clearable
							filterable
							@change="getTableDataDB"
						>
							<el-option label="计量单位:万元（保留两位）" :value="2"> </el-option>
							<el-option label="计量单位:元" :value="0"> </el-option>
						</el-select>
						<span v-if="searchForm.name" class="el-icon-user"> {{ searchForm.name }}</span>
						<span> 收款清单 </span>
						<span>
							({{ $moment(dateSelectObj.dateStart).format('YYYY-MM-DD') }} -
							{{ $moment(dateSelectObj.dateEnd).format('YYYY-MM-DD') }})
						</span>
					</span>
					<el-button type="text" class="el-icon-arrow-left" @click="moveToggle = false">返回</el-button>
				</div>
				<u-table
					ref="uTableRef"
					id="uTable"
					class="table-main"
					:data="tableData"
					:height="tableHeight"
					:border="false"
					:pagination-show="true"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					stripe
					@sort-change="SortChange"
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in columnListCurr"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="item.colNo == 'createTime' || item.colNo == 'complateMonth' || item.colNo == 'receiptsDate'"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 各种人员 -->

							<Tooltips
								v-else-if="
									item.colNo == 'salesman' || item.colNo == 'implement' || item.colNo == 'consulting' || item.colNo == 'talk'
								"
								:cont-str="scope.row[item.colNo]?.userName"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 订单类型 -->
							<Tooltips
								v-else-if="item.colNo == 'type'"
								:cont-str="scope.row[item.colNo] == 1 ? '合同' : scope.row[item.colNo] == 2 ? '直购' : '续费'"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 状态 -->
							<Tooltips
								v-else-if="item.colNo == 'status'"
								:cont-str="scope.row[item.colNo] == 1 ? '未收款' : '已收款'"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
							<!-- 开票状态  1 无需开票 2 需要开票 -->
							<Tooltips
								v-else-if="item.colNo == 'invoicing'"
								:cont-str="
									scope.row[item.colNo] == 1
										? '无需开票'
										: scope.row['ticketNo']
											? '票号：' + scope.row['ticketNo']
											: '未申请开票'
								"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 客户上传凭证 -->
							<FilePopover
								v-else-if="item.colNo == 'bankReceiptName'"
								:url="scope.row.bankReceiptUrl"
								:content="scope.row.bankReceiptName"
							/>
							<!-- 来源 -->
							<Tooltips
								v-else-if="item.colNo == 'channel'"
								:cont-str="
									jointString('/', sourceMap[scope.row[item.colNo]], scope.row.promotionalVidUserName, scope.row.promotionalVid)
								"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 金额转换 -->
							<Tooltips
								v-else-if="item.colNo == 'receivableAmount' || item.colNo == 'receiptsAmount'"
								:cont-str="convertToMillion(scope.row[item.colNo])"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<u-table-column label="" width="" align="right">
						<template slot-scope="scope">
							<div class="flex-align-center">
								<el-button type="text" @click="openDialog(scope.row, '收款')">收款</el-button>
								<el-button type="text" @click="openDialog(scope.row, '开票')">开票</el-button>
							</div>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>

		<!-- 收款弹窗 -->
		<el-dialog
			:visible.sync="dialogExtension"
			width="35%"
			:append-to-body="true"
			:close-on-click-modal="false"
			@close="closeDialog"
		>
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="6vw" label-position="left" ref="editFormRef">
				<el-form-item label="应收金额（元）" prop="receivableAmount">
					<el-col :span="24">
						<span> {{ editForm.receivableAmount }}</span>
					</el-col>
				</el-form-item>
				<el-form-item label="实收金额（元）" prop="receiptsAmount">
					<el-input class="W100" placeholder="请输入实收金额（元）" v-model="editForm.receiptsAmount"></el-input>
				</el-form-item>
				<el-form-item label="收款日期" prop="receiptsDate">
					<el-date-picker
						v-model="editForm.receiptsDate"
						type="date"
						class="W100"
						value-format="timestamp"
						placeholder="请选收款日期"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="银行凭证号" prop="bankVoucherNo">
					<el-input class="W100" placeholder="请输入银行凭证号" v-model="editForm.bankVoucherNo"></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="applyExtension">保存</el-button>
			</el-row>
		</el-dialog>
		<!-- 开票弹窗 -->
		<el-dialog
			:visible.sync="dialogInvoicing"
			width="600px"
			:close-on-click-modal="false"
			:append-to-body="true"
			@close="closeDialog"
		>
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="100px" label-position="left" ref="editFormRef">
				<el-form-item label="应收金额" prop="receivableAmount">
					<el-col :span="24">
						<span> {{ editForm.receivableAmount }}</span>
					</el-col>
				</el-form-item>
				<el-form-item label="实收金额" prop="receiptsAmount">
					<span> {{ editForm.receiptsAmount }}</span>
				</el-form-item>
				<el-form-item label="开票日期" prop="invoicingDate">
					<el-date-picker
						v-model="editForm.invoicingDate"
						type="date"
						class="W100"
						value-format="timestamp"
						placeholder="请选开票日期"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="票号" prop="ticketNo">
					<el-input class="W100" placeholder="请输入票号" v-model="editForm.ticketNo"></el-input>
				</el-form-item>
				<el-form-item label="快递单号" prop="courierNumber">
					<el-input class="W100" placeholder="请输入快递单号" v-model="editForm.courierNumber"></el-input>
				</el-form-item>
				<el-form-item label="快递单" prop="courierUrl">
					<el-upload
						ref="upload"
						action=""
						accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
						:http-request="uploadFile"
						:show-file-list="false"
					>
						<el-button v-if="!editForm.courierUrl" size="small" type="warning">上传</el-button>
						<FilePopover v-else class="vw5" :url="editForm.courierUrl" :content="editForm.courierUrl" />
					</el-upload>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="applyInvoicing">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

import { sourceMap } from '@/assets/js/inquirySource.js';
import FilePopover from '@/components/FilePopover.vue';

// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'

export default {
	// import引入的组件需要注入到对象中才能使用
	components: { FilePopover },
	props: { twidList: Array, channelName: Array },
	name: 'paybackList',
	data() {
		return {
			selectDecimal: 2,
			sourceMap,
			uid: '',
			regionArr: [],
			activeTab: 'paybackList',
			queryStr: '',
			rowData: {},
			titleName: '',
			openMove: '',
			//日期相关
			dateSelectObj: {
				dateStart: null,
				dateEnd: null,
			},
			tableData: [],
			tableDataCopy: [],
			tableHeight: 640,
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			columnListCurr: [
				{ colName: '公司工商注册名称', colNo: 'registeredBusinessName', align: 'left', width: 200 },
				{ colName: '来源', colNo: 'channel', align: 'left', width: 80 },
				{ colName: '业务顾问', colNo: 'salesman', align: 'left', width: 80 },
				{ colName: '实施顾问', colNo: 'implement', align: 'left', width: 80 },
				{ colName: '订单号', colNo: 'orderNo', align: 'left', width: 100 },
				{ colName: '订单类型', colNo: 'type', align: 'center', width: 80 },
				{ colName: '应收金额', colNo: 'receivableAmount', align: 'right', width: '' },
				{ colName: '实收金额', colNo: 'receiptsAmount', align: 'right', width: '' },
				{ colName: '订单日期', colNo: 'createTime', align: 'center', width: '' },
				{ colName: '实收日期', colNo: 'receiptsDate', align: 'center', width: '' },
				{ colName: '状态', colNo: 'status', align: 'center', width: 80 },
				{ colName: '开票状态', colNo: 'invoicing', align: 'left', width: 80 },
				{ colName: '客户上传凭证', colNo: 'bankReceiptName', align: 'left', width: 120 },
			],
			searchForm: {
				uid: '',
				contractFlag: ['0', '1'],
				twidList: '',
				userType: '',
				channelList: [],
			},

			dialogInvoicing: false,
			dialogExtension: false,
			editForm: {},
			fileList: [],
			btnFlag: false,
			showChekedBtn: true,
			dialogTitle: '开票',

			moveToggle: false, //滑动控制
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		queryStr() {
			this.getTableDataDB();
		},
		twidList() {
			this.getTableDataDB();
		},
		channelName() {
			this.getTableDataDB();
		},
		moveToggle(newVal) {
			if (newVal) {
				this.getTableDataDB();
			} else {
				this.tableData = [];
				this.tableDataCopy = _.deepClone(this.tableData);
				this.tablePageForm.total = 0;
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.uid = this.$route?.query?.queryId || '';
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		closeDialog() {
			this.dialogEdit = false;
			this.dialogExtension = false;
			this.showChekedBtn = true;
			this.dialogInvoicing = false;

			this.editForm = {};
			this.$refs.editFormRef.resetFields();
		},
		applyExtension() {
			const { receivableAmount, bankVoucherNo, receiptsAmount, receiptsDate, omid } = this.editForm;

			if (receiptsAmount != 0 && !receiptsAmount) {
				this.$message.warning('请输入收款金额！');
				return;
			}
			if (receiptsAmount > receivableAmount) {
				this.$message.warning('实收金额不能大于应收金额！');
				return;
			}
			if (!receiptsDate) {
				this.$message.warning('请选择收款日期！');
				return;
			}
			if (!bankVoucherNo) {
				this.$message.warning('请输入银行凭证号');
				return;
			}
			const str = JSON.stringify({
				bankVoucherNo,
				omid,
				receiptsAmount,
				receiptsDate,
			});

			this.$axios
				.collection(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');

						this.closeDialog();
						this.getTableDataDB();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('collection |' + error);
				});
		},
		applyInvoicing() {
			const { invoicingDate, ticketNo, courierNumber, courierUrl, omid } = this.editForm;
			if (!invoicingDate) {
				this.$message.warning('请选择开票日期！');
				return;
			}
			if (!ticketNo) {
				this.$message.warning('请输入票号！');
				return;
			}
			const str = JSON.stringify({
				courierNumber,
				courierUrl,
				invoicingDate,
				omid,
				ticketNo,
			});

			this.$axios
				.invoicing(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');

						this.closeDialog();
						this.getTableDataDB();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('invoicing |' + error);
				});
		},
		downloadFile(url) {
			window.open(url, '_blank');
		},
		delFile() {
			this.editForm.bankReceiptName = '';
			this.editForm.bankReceiptUrl = '';
		},
		handleRemove() {
			//文件移除
			this.fileList = [];
		},
		uploadFail(err, file, fileList) {
			this.fileList = [];
		},
		openDialog(row, title) {
			if (title == '收款') {
				this.editForm = {
					receivableAmount: row.receivableAmount,
					receiptsAmount: row.receiptsAmount || row.receivableAmount,
					receiptsDate: row.receiptsDate || new Date(),
					bankReceiptName: row.bankReceiptName,
					bankReceiptUrl: row.bankReceiptUrl,
					omid: row.omid,
					bankVoucherNo: row.bankVoucherNo,
				};
				// if (!this.editForm.bankReceiptName) {
				//   this.$message.warning("该用户未上传银行凭证，请确认已支付后再操作！")
				// }

				this.dialogTitle = title + '（单位：元）';
				this.dialogExtension = true;
			} else {
				this.editForm = {
					receivableAmount: row.receivableAmount,
					receiptsAmount: row.receiptsAmount,
					invoicingDate: row.invoicingDate || new Date(),
					ticketNo: row.ticketNo,
					courierNumber: row.courierNumber,
					courierUrl: row.courierUrl,
					omid: row.omid,
				};
				this.dialogTitle = title;
				this.dialogInvoicing = true;
			}
		},
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = []; // 合计
			columns.forEach((column, columnIndex) => {
				if (columnIndex === 2) {
					means.push('合计');
				} else if (columnIndex === 7 || columnIndex === 8) {
					const values = data?.map(item => {
						return Number(item[column.property]);
					});

					// 合计
					if (!values.every(value => isNaN(value))) {
						means[columnIndex] = values.reduce((prev, curr) => {
							const value = Number(curr);

							if (!isNaN(value)) {
								return _.accAdd(prev, curr);
							} else {
								return prev;
							}
						}, 0);
						if (!isNaN(means[columnIndex]) && Number(means[columnIndex])) {
							means[columnIndex] = <span class="pr8">{this.convertToMillion(means[columnIndex])}</span>;
						} else {
							means[columnIndex] = '';
						}
					} else {
						means[columnIndex] = '';
					}
				}
			});
			return [means];
		},
		// 快递单
		uploadFile(item) {
			const self = this;
			const isLt50M = item.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				self.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', item.file);

			self.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						self.editForm.courierUrl = res.data.data.path;
					} else {
						self.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					self.$message.warning(error.message);
				});
		},
		// 将金额转成万单位
		convertToMillion(num) {
			if (!num) return;
			if (this.selectDecimal !== 2) {
				return Number(num).toFixed(0);
			}
			return _.convertToMillion(num);
		},
		refreshData() {
			this.getTableDataDB();
			this.$message.success('刷新成功，数据已更新！');
		},
		getTableDataDB: _.debounce('getTableData'),
		getTableData() {
			if (!this.moveToggle) {
				this.tableData = [];
				return;
			}
			const pageNum = this.tablePageForm.currentPage,
				pageSize = this.tablePageForm.pageSize,
				receiptsStartDate = this.dateSelectObj.dateStart,
				receiptsEndDate = this.dateSelectObj.dateEnd;
			const { ignoreOneStage, uid, userType, channels } = this.searchForm;
			const str = JSON.stringify({
				channels,
				contractFlag: [1], //已收款
				endTime: '',
				flag: '',
				ignoreOneStage,
				pageNum,
				pageSize,
				queryParam: this.queryStr,
				receiptsEndDate,
				receiptsStartDate,
				startTime: '',
				twid: this.twidList,
				channelName: this.channelName,
				uid,
				userType,
			});
			this.$axios
				.collectionMonthRiversByUidAndTime(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tableDataCopy = _.deepClone(this.tableData);
						this.tablePageForm.total = res.data.totalItems;
						this.$nextTick(() => {
							this.$refs.uTableRef && this.$refs.uTableRef.doLayout();
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('collectionMonthRiversByUidAndTime |' + error);
				});
		},
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.getTableData();
		},
		dateFormat: _.dateFormat, //日期format
		jointString: _.jointString, //字符串拼接
		//排序
		SortChange({ column, prop, order }) {
			let tableDataPX = _.deepClone(this.tableData);
			// prop：排序的字段、order：升序（ascending）、降序（descending）、取消排序（null）
			if (column.order == 'descending') {
				tableDataPX.sort((a, b) => {
					const atype = typeof a[prop];
					const btype = typeof b[prop];
					if (prop == 'salesman' || prop == 'implement' || prop == 'consulting' || prop == 'talk') {
						if (!a[prop]?.userName) return -1;
						if (!b[prop]?.userName) return 1;
						return a[prop]?.userName.localeCompare(b[prop]?.userName, 'zh-CN');
					}

					if (atype == 'string' && btype == 'string') {
						if (!isNaN(parseFloat(a[prop])) && isFinite(a[prop])) {
							return Number(a[prop]) < Number(b[prop]) ? 1 : -1;
						}

						return a[prop] < b[prop] ? 1 : -1;
					} else if (atype == 'number' && btype == 'number') {
						return b[prop] - a[prop];
					} else if (atype == 'number' && btype == 'string' || atype == 'string' && btype == 'number') {
						a[prop] = atype == 'number' ? a[prop] += '' : a[prop];
						b[prop] = btype == 'number' ? b[prop] += '' : b[prop];
						return a[prop] < b[prop] ? 1 : -1;
					} else if (atype == 'object' && btype !== 'object') {
						return 1;
					} else if (atype !== 'object' && btype == 'object') {
						return -1;
					} else {
						return 0;
					}
				});

				this.tableData = _.deepClone(tableDataPX);

				tableDataPX = null;
			} else if (column.order == 'ascending') {
				tableDataPX.sort((a, b) => {
					const atype = typeof a[prop];
					const btype = typeof b[prop];
					if (prop == 'salesman' || prop == 'implement' || prop == 'consulting' || prop == 'talk') {
						if (!a[prop]?.userName) return 1;
						if (!b[prop]?.userName) return -1;
						return a[prop]?.userName.localeCompare(b[prop]?.userName, 'zh-CN');
					}
					if (atype == 'string' && btype == 'string') {
						if (!isNaN(parseFloat(a[prop])) && isFinite(a[prop])) {
							return Number(a[prop]) > Number(b[prop]) ? 1 : -1;
						}
						return a[prop] > b[prop] ? 1 : -1;
					} else if (atype == 'number' && btype == 'number') {
						return a[prop] - b[prop];
					} else if (atype == 'number' && btype == 'string' || atype == 'string' && btype == 'number') {
						a[prop] = atype == 'number' ? a[prop] += '' : a[prop];
						b[prop] = btype == 'number' ? b[prop] += '' : b[prop];
						return a[prop] > b[prop] ? 1 : -1;
					} else if (atype == 'object' && btype !== 'object') {
						return -1;
					} else if (atype !== 'object' && btype == 'object') {
						return 1;
					} else {
						return 0;
					}
				});

				this.tableData = _.deepClone(tableDataPX);

				tableDataPX = null;
			} else if (column.order == null) {
				this.tableData = _.deepClone(this.tableDataCopy);
			}
		},

		openPaybackList(monthIndex, row, searchForm, year) {
			// console.log(monthIndex, row, searchForm, year);
			// 获取当前月份第一天时间戳
			const now = new Date(year); // 获取当前年份

			if (monthIndex !== 'total' && monthIndex < 13) {
				// 非合计
				const firstDayOfMonth = new Date(now.getFullYear(), monthIndex - 1, 1); // 当月的第一天
				this.dateSelectObj.dateStart = firstDayOfMonth.getTime(); // 获取时间戳
				this.dateSelectObj.dateEnd = _.getNowMonthEndDay(this.dateSelectObj.dateStart); // 当前月份最后一天时间戳
			} else {
				// 合计
				const firstDayOfYear = new Date(now.getFullYear(), 0, 1); // 当年的第一天
				this.dateSelectObj.dateStart = firstDayOfYear.getTime(); // 获取时间戳
				const lastDayOfYear = new Date(now.getFullYear(), 11, 31); // 当年的最后一天
				this.dateSelectObj.dateEnd = lastDayOfYear.getTime() + 86399999; // 获取时间戳
			}

			// 默认查询条件
			this.searchForm.uid = row?.uid || '';
			this.searchForm.userType = searchForm.type;
			this.searchForm.ignoreOneStage = searchForm.ignoreOneStage;
			this.searchForm.channels = searchForm.channelList;
			this.searchForm.name = row?.name || '';
			this.moveToggle = true;
			this.getTableDataDB();
		},
	},
};
</script>

<style lang="scss" scoped>
#paybackList {
	.table-main {
		width: 100%;
		height: 100%;
		min-height: 400px;
		height: calc(100vh - 230px) !important;
	}
}

.moveToggle {
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: 99;
	opacity: 1;
	transition: all 0.3s ease-in-out;
	transform: translateX(0);
	background: #f2f2f2;
}

.moveToggle-hide {
	opacity: 0;
	transform: translateX(110%);
}
</style>
