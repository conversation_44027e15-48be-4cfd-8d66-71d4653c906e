<!-- 询盘列表（公海） -->
<template>
	<div class="InquriyList_Sea" :class="openMove ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<!-- 动态询盘详情：聚心城使用聚心城询盘详情，其他使用默认询盘详情 -->
		<component
			v-if="showMap.InquiryDetail"
			:is="isJuxinCity ? 'InquiryDetail_JXC' : 'InquiryDetail'"
			ref="InquiryDetail"
			:inquiryOptions="tableData"
			@openContract="openContract"
			@close="queryTableData"
		/>

		<!-- 合同详情 -->
		<ContractDetail v-if="showMap.ContractDetail" ref="ContractDetail" :contractOptions="tableData" @close="queryTableData" />
		<!-- 数据导出弹窗 -->
		<ExportTable v-if="showMap.ExportTable" ref="ExportTable" />
		<BaseLayout>
			<template #header>
				<span class="label-title">{{ jointString(' - ', '询盘列表', titleName) }}</span>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh" @click.stop="queryTableData('refresh')">刷新</el-button>
					<el-button type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
				</div>
			</template>
			<template #main>
				<div class="table-toolbar"> </div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					@sort-change="sortChange"
					show-header-overflow="title"
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="50" type="index" align="center"> </u-table-column>

					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="item.colNo == 'lastDate'"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="item.colNo == 'intoHighseasTime' || item.colNo == 'signingDate'"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 预计月份 -->
							<Tooltips
								v-else-if="item.colNo == 'expectedMonth'"
								class="orange"
								:cont-str="dateFormat(scope.row[item.colNo], 'YM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 行业 -->
							<Tooltips
								v-else-if="item.colNo == 'industry'"
								:cont-str="jointString('/', scope.row.industry, scope.row.industryRemark)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 行业熟练程度 -->
							<Tooltips
								v-else-if="['industryProficiency'].includes(item.colNo)"
								:cont-str="[, '不熟（1-5）', '熟练（6-8）', '擅长（9-10）'][scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 来源 -->
							<Tooltips
								v-else-if="item.colNo == 'channel'"
								:cont-str="
									jointString('/', sourceMap[scope.row[item.colNo]], scope.row.promotionalVidUserName, scope.row.promotionalVid)
								"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 询盘质量 -->
							<Tooltips
								v-else-if="item.colNo == 'quality'"
								:cont-str="qualityMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 商机质量 -->
							<Tooltips
								v-else-if="item.colNo == 'businessOpportunityQuality'"
								:cont-str="businessQualityMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 阶段 -->
							<Tooltips
								v-else-if="item.colNo == 'stage'"
								:cont-str="stageMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<Tooltips
								v-else-if="item.colNo == 'estimatedAmount'"
								:cont-str="scope.row[item.colNo]?.toFixed(2)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 合同金额 -->
							<Tooltips
								v-else-if="item.colNo == 'contractAmount' && scope.row.stage == 5"
								:cont-str="scope.row[item.colNo]?.toFixed(2)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 询盘编号 -->
							<Tooltips
								v-else-if="item.colNo == 'number'"
								class="hover-green primary"
								@click.native="openDetail('修改', scope.row)"
								:cont-str="scope.row[item.colNo] || '未知'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>

					<u-table-column label="" align="right">
						<template slot-scope="scope">
							<el-button type="text" class="el-icon-edit-outline" @click="openDetail('修改', scope.row)"></el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>
<script>
import { jointString, dateFormat, bigAdd, sortTableData, debounce, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 自定义组件（询盘详情弹窗）
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryDetail_JXC from '@/components/InquiryDetail_JXC/InquiryDetail.vue'; //询盘详情弹窗
import ContractDetail from '@/pages/deliveryManagement/contractManagement/contractDetailCom.vue';

import { sourceList, sourceMap, stageList, stageMap, qualityMap, businessQualityMap } from '@/assets/js/inquirySource.js';
export default {
	name: 'InquriyList_Sea',
	components: {
		InquiryDetail,
		InquiryDetail_JXC,
		ContractDetail,
	},
	props: {
		twidList: Array, //代理
		channelName: Array, //渠道
	},
	data() {
		return {
			titleName: '公海', //标题名
			openMove: false,
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],

			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'center', width: '150' },
				{ colName: '进入公海时间', colNo: 'intoHighseasTime', align: 'center', width: '150' },
				{ colName: '公司简称', colNo: 'companyName', align: 'left', state: true },
				{ colName: '原咨询人', colNo: 'oldConsultName', align: 'left', width: '' },
				{ colName: '原业务顾问', colNo: 'oldSalesman', align: 'left', state: true },
			],
			searchForm: {},
			qualityMap, // 询盘质量
			businessQualityMap, // 商机质量
			stageList,
			stageMap,
			// 来源列表
			sourceList,
			sourceMap,
			rowData: {},
			showMap: {
				InquiryDetail: false,
				ContractDetail: false,
			},
		};
	},
	watch: {},
	mounted() {},
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['isJuxinCity']), //是否聚心城人员
	},
	methods: {
		// 打开合同
		openContract(data) {
			this.showMap.ContractDetail = true;
			this.$nextTick(() => {
				this.$refs.ContractDetail.showDetailCom(data);
			});
		},
		// 打开询盘详情
		openDetail(type, row) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom(type, row);
			});
		},
		// 打开列表
		openList(data, title) {
			this.titleName = title;
			this.searchForm = data;
			this.queryTableData();
		},
		// 查询表格
		queryTableData: debounce(async function (type) {
			const API = 'selectInquiryIntoHighseasUserLoginDetails';
			try {
				const res = await this.$axios[API](JSON.stringify(this.searchForm));
				if (res.data.success) {
					this.tableData = res.data.data;
					this.sortChange(this.tableSort, true);
					type == 'refresh' && this.$message.success('刷新成功');
					this.openMove = true;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),

		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		dateFormat, //日期format
		jointString, // 拼接字符串
	},
};
</script>
