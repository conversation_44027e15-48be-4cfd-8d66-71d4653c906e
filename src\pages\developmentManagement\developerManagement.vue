<template>
	<div id="developerManagement">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="开发人员管理" name="developerManagement">
				<BaseLayout>
					<template #header>
						<span class="search-label">月份</span>
						<el-button type="text" class="el-icon-arrow-left search-arrow" @click="changeMonth('pre')"> </el-button>
						<el-date-picker
							size="small"
							class="w-150"
							v-model="selectTimeMonth"
							:default-value="selectTimeMonth"
							type="month"
							value-format="yyyy-MM"
							format="yyyy 年 M 月"
							placeholder="请选择月份"
							@change="queryTableData(1)"
						>
						</el-date-picker>
						<el-button type="text" class="el-icon-arrow-right search-arrow" @click="changeMonth('next')"> </el-button>
						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<el-button type="text" class="icon-third-bt_newdoc" @click.native="dialogAdddeveloper = true">添加</el-button>
							<ExportBtn @trigger="openExport" />
						</div>

						<u-table
							ref="uTableRef"
							class="table-main"
							:data="tableData"
							:height="1200"
							:row-height="45"
							@sort-change="sortChange"
							stripe
						>
							<u-table-column label="序号" width="50" align="center" type="index"> </u-table-column>

							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								resizable
								sortable="custom"
							>
								<template slot-scope="scope">
									<!-- 目标系数 -->
									<el-input
										v-if="item.colNo == 'factor'"
										size="mini"
										type="text"
										v-model="scope.row.factor"
										placeholder="目标系数"
										@change="changefactor(scope.row)"
									></el-input>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>

							<u-table-column label=""> </u-table-column>
							<u-table-column label=""> </u-table-column>
							<u-table-column label=""> </u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="年度系数一览表" name="coefficient">
				<AnnualCoefficient v-if="activeTab == 'coefficient'" ref="annualCoefficient"></AnnualCoefficient>
			</el-tab-pane>
		</el-tabs>

		<el-dialog :visible.sync="dialogAdddeveloper" width="25%" close-on-click-modal @close="closeDialog">
			<el-row slot="title">添加开发人员</el-row>
			<el-form :model="addForm" label-width="5vw" label-position="left" ref="addBoxRef" :rules="editRules">
				<el-form-item label="开发人员" prop="userName">
					<el-select
						class="W100"
						v-model="addForm.userName"
						placeholder="请选择开发人员"
						default-first-option
						filterable
						multiple
					>
						<el-option v-for="item in developerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button size="small" type="primary" @click.native="saveAddForm">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable';
import AnnualCoefficient from './annualCoefficient.vue';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	props: {},
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		AnnualCoefficient,
		ExportBtn,
	},
	name: 'developerManagement',
	data() {
		return {
			activeTab: 'developerManagement',
			flag: 0, //箭头切换左右的flag
			dialogAdddeveloper: false,
			selectTimeMonth: new Date(),
			addForm: {
				userName: '',
				jobNumber: '',
				factor: '',
				startTime: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [], //开发人员列表
			tableDataCopy: [], //开发人员列表
			editRules: {
				userName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
				startTime: [{ required: true, message: '请输入入职时间', trigger: 'blur' }],
			},
			tableColumn: [
				{ colName: '工号', colNo: 'jobNumber', align: 'left' },
				{ colName: '姓名', colNo: 'userName', align: 'left' },
				{ colName: '目标系数', colNo: 'factor', align: 'left' },
			],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userList']), //当前登录用户信息（含团队/菜单/权限等）
		//开发人员列表(标签：开发)
		developerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('开发')) || [];
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.selectTimeMonth = this.dateFormat(this.selectTimeMonth, 'YM');
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// tab切换
		changeTab(tab, event) {},
		queryTableData: _.debounce(function (type) {
			// type && (this.tablePageForm.currentPage = 1);
			const str = JSON.stringify({
				month: this.selectTimeMonth,
				queryParam: '',
			});
			this.$axios
				.selectResearchDepartmentList(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tableDataCopy = _.deepClone(this.tableData);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						if (this.tableSort) {
							this.sortChange(this.tableSort);
						}
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectResearchDepartmentList |' + error);
				});
		}),

		closeDialog() {
			this.dialogAdddeveloper = false;
			this.addForm.userName = '';
		},
		saveAddForm() {
			//添加人员
			const str = JSON.stringify({
				uidList: this.addForm.userName,
			});
			this.$axios
				.addResearchDepartment(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.queryTableData();
						this.closeDialog();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addResearchDepartment |' + error);
				});
		},
		// 月份切换
		changeMonth(str) {
			const nowdays = new Date(this.selectTimeMonth);
			let year = nowdays.getFullYear();
			let month = nowdays.getMonth();
			if (str == 'next') {
				month = nowdays.getMonth() + 2;
				if (month > 12) {
					month = 1;
					year = year + 1;
				}
				// console.log("nextmonth", month);
			} else if (str == 'pre') {
				if (month == 0) {
					month = 12;
					year = year - 1;
				}
			}
			month = month > 9 ? month : '0' + month;
			this.selectTimeMonth = year + '-' + month;
			this.queryTableData();
		},

		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableDataCopy, prop, order);
				this.tableData = sortedData;
			} else {
				this.tableData = this.tableDataCopy;
			}
		},

		// 修改系数
		changefactor(row) {
			if (this.btnFlag || !row.factor) {
				this.$message.warning('注意，目标系数不能置为空值！');
				this.queryTableData();
				return;
			}

			const str = JSON.stringify({
				factor: row.factor,
				rdfid: row.rdfid,
				rdid: row.rdid,
				month: this.selectTimeMonth,
			});
			this.$axios
				.udapteResearchDepartment(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
					} else {
						this.queryTableData();

						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('udapteResearchDepartment |' + error);
				});
		},
		//数据导出
		openExport(type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					month: this.selectTimeMonth,
					queryParam: '',
				}), //接口参数
				API: 'exportResearchDepartment', //导出接口
				downloadData: '部门人员信息导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		},

		//日期format
		dateFormat: _.dateFormat,
	},
};
</script>

<style lang="scss" scoped>
#developerManagement {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
