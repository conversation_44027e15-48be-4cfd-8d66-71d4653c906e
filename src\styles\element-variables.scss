/* 
	
	这里是全局变量的scss文件，在需引用这里变量的文件添加 @import "@/styles/element-variables.scss";
	修改主题颜色主要变换以下颜色可覆盖大部分样式，未修改到的样式请抽离出来或者用上这里的变量。

	原色亮度、饱和度设置：color.scale()、color.adjust()
*/
@use "sass:color";

$--color-primary: #1e9d6f; //主题色：树字MES lightMES 绿 （请勿修改 ！！！ 1e9d6f！！！）
// $--color-primary: #096dd9; // 示例：拂晓蓝
// $--color-primary: #722ed1; // 示例：酱紫
// $--color-primary: #eb2f96; // 示例：法式洋红

// 基础主题高光亮色（主要是应用在文本、checkbox、radio、step、button等 #28d094）
$--color-primary-light: color.scale($--color-primary, $lightness: 10%);
//基础主题高光亮色+（主要是应用在菜单等激活时 #24fbb7 ）
$--color-primary-lighter: color.scale($--color-primary, $lightness: 30%, $saturation: 50%);

$--color-success: #13ce66;
$--color-warning: #e6a23c;
$--color-danger: #f56c6c;
$--color-danger-light: #f37575;

$--button-font-weight: 400;

$--border-color-light: #dfe4ed; //二级边框颜色
$--border-color-lighter: #e6ebf5; //三级边框颜色

$--table-border: 1px solid #dfe6ec;
/* 改变 icon 字体路径变量，必需 */
$--font-path: "~element-ui/lib/theme-chalk/fonts";
@import "~element-ui/packages/theme-chalk/src/index";

:export {
	theme: $--color-primary;
}
