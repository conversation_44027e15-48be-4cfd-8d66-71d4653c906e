<template>
	<div class="PointsLogList" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<BaseLayout :showHeader="false">
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar">
					<div class="label-title mr-auto">近一年积分/扣分记录</div>
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
				</div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					:pagination-show="false"
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="{ row, column }">
							<!-- 各种日期（默认不显示分秒 lineM ） -->
							<Tooltips
								v-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
								:cont-str="dateFormat(row[item.colNo], 'line')"
								:cont-width="column.realWidth - 20"
							/>

							<Tooltips
								v-else-if="item.colNo == 'positivePointsSum' && row[item.colNo]"
								class="green pointer"
								@click.native="openDialog(row, '积分')"
								:cont-str="`+${row[item.colNo]}`"
								:cont-width="column.realWidth"
							/>
							<Tooltips
								v-else-if="item.colNo == 'negativePointsSum' && row[item.colNo]"
								class="red"
								@click.native="openDialog(row, '扣分')"
								:cont-str="`${row[item.colNo]}`"
								:cont-width="column.realWidth"
							/>

							<!-- 默认显示 -->
							<Tooltips v-else-if="row[item.colNo]" :cont-str="row[item.colNo]" :cont-width="column.realWidth - 20" />
						</template>
					</u-table-column>
					<u-table-column label="" width="" align="right"></u-table-column>
					<!-- 其他列/操作 -->
					<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="{ row }">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDialogRow(row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(row)"></el-button>
                </template>
              </u-table-column> -->
				</u-table>
			</template>
		</BaseLayout>

		<el-dialog
			width="888px"
			top="5vh"
			:visible.sync="dialogEdit"
			:close-on-click-modal="true"
			:append-to-body="false"
			:modal-append-to-body="false"
			@close="closeDialog"
		>
			<span slot="title">{{ dialogTitle }}明细</span>
			<ExpandTable :label="'工作项记录'" :data="expandTable" :columns="expandColumns">
				<template slot-scope="{ row, prop, columnWidth }">
					<!-- 各种日期 -->
					<Tooltips
						v-if="prop.includes('Time') || prop.includes('Date')"
						:cont-str="dateFormat(row[prop], 'lineM')"
						:cont-width="columnWidth"
					/>

					<!-- 工作项 -->
					<Tooltips
						v-else-if="prop == 'operation'"
						class="hover-green"
						@click.native="onOpenDetail(row)"
						:cont-str="row[prop]"
						:cont-width="columnWidth"
					/>
					<!-- 客户(公司名) -->
					<Tooltips
						v-else-if="prop == 'companyName'"
						:class="row.dmid || row.idid ? 'hover-green' : ''"
						@click.native="
							row.dmid ? openDetail('ContractDetail', row) : row.idid ? openDetail('InquiryDetail', '修改', row) : () => {}
						"
						:cont-str="row.companyName || row.registeredBusinessName || row.customerName"
						:cont-width="columnWidth"
					/>
					<Tooltips v-else :cont-str="row[prop] ? row[prop] + '' : ''" :cont-width="columnWidth" />
				</template>
			</ExpandTable>
			<span slot="footer">
				<el-button type="primary" @click="closeDialog">返 回</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import { debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExpandTable from '@/components/ExpandTable';
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExpandTable,
	},
	name: 'PointsLogList', //组件名应同路由名(否则keep-alive不生效)
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			openMove: false, //打开组件
			activeTab: 'PointsLogList', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '记录时间范围（每周一至周日）', colNo: 'date', align: 'center', width: '280' },
				{ colName: '积分', colNo: 'positivePointsSum', align: 'right', width: '120' },
				{ colName: '扣分', colNo: 'negativePointsSum', align: 'right', width: '120' },
				// { colName: '备注', colNo: 'remark', align: 'center', width: '' },
			],

			expandTable: [],
			expandColumns: [
				{ colName: '工作项', colNo: 'operation', align: 'left', width: '' },
				{ colName: '分数', colNo: 'points', align: 'right', width: '80' },
				{ colName: '记录时间', colNo: 'pointsDate', align: 'center', width: '150' },
			],

			// 查询表单
			searchForm: {
				did: '',
				query: '',
				// 其他...
			},

			dialogEdit: false,
			dialogTitle: '',
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				// this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
			console.log({ newVal });
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		!this.isMoveCom && this.queryTableData();
	},
	activated() {
		!this.isMoveCom && this.queryTableData(1);
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开工作项明细
		onOpenDetail(row) {
			const { tid, dmid, idid, pmtid, pmid } = row;
			if (tid) return this.openDetail('TeamDetail', row); //团队
			if (dmid) return this.openDetail('DeliveryDetail', row); //交付
			if (idid) return this.openDetail('InquiryDetail', '修改', row); //询盘
			if (pmtid) return this.openDetail('TaskDetail', row, '查看研发任务'); //研发任务
			if (pmid) return this.openDetail('ProjectDetail', row); //研发项目
		},
		// 打开明细
		openDetail(ref, ...args) {
			this.$emit('openDetail', ref, ...args);
			this.$emit('getTableOptions', this.tableData);
		},
		// 获取工作项信息
		getOperation(row) {
			const { operation, idid, dmid, tid, pmid, pmtid } = row;
			let infos = '';
			if (tid && row.teamVO) {
				infos = this.jointString(' / ', row.teamVO.teamName, this.dateFormat(row.teamVO.validTo, 'line')); //团队
			} else if (dmid && row.deliverStageManagement) {
				infos = this.jointString(
					' / ',
					row.deliverStageManagement.stage,
					row.deliverStageManagement.standard,
					this.dateFormat(row.deliverStageManagement.complateMonth, 'line'), //交付
				);
			} else if (idid && row.inquiryDocumentary) {
				infos = this.jointString(' / ', row.idNumber, row.inquiryDocumentary.companyName); //询盘
			} else if (pmtid && row.projectManagementTask) {
				infos = this.jointString(' / ', row.projectManagement.projectName, row.projectManagementTask.taskName); //研发任务
			} else if (pmid && row.projectManagement) {
				infos = this.jointString(' / ', row.projectManagement.projectName); //研发项目
			} else if (row.clientKeepRecord) {
				infos = this.jointString(' / ', row.idNumber, row.clientKeepRecord.registeredBusinessName); //客户备案
			} else {
				infos = row.auditMemo || ''; //评价、其他等
			}
			//  row.uname?.replace(/^\d+/, ''),
			// , dateFormat(row.pointsDate, 'MDHM')
			return this.jointString(' ', ` ${operation}-`, infos);
		},

		// 打开积分明细清单
		openDialog(row, type) {
			this.dialogTitle = type;
			const expandTable = type == '积分' ? row.positivePointsList : row.negativePointsList;
			this.expandTable = expandTable.map(item => {
				const deliverManagement = item?.deliverManagement || {}; //交付阶段信息
				const inquiryDocumentary = item?.inquiryDocumentary || {}; //询盘信息
				const projectManagement = item?.projectManagement || {}; //项目信息
				const projectManagementTask = item?.projectManagementTask || {}; //项目任务信息
				const points = item?.points || item?.cfgPoints || '';
				const newItem = { ...deliverManagement, ...inquiryDocumentary, ...projectManagement, ...projectManagementTask, ...item };
				newItem.operation = this.getOperation(newItem);
				newItem.points = points;

				return newItem;
			});
			this.dialogEdit = true;
		},
		closeDialog() {
			this.dialogEdit = false;
			this.expandTable = [];
		},

		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectMonthlyAdminUserPointsLogList'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item.date = jointString(' - ', dateFormat(item.monday, 'lineM'), dateFormat(item.sunday, 'lineM'));
							item.positivePointsSum = item.positivePointsSum ? item.positivePointsSum.toFixed(2) : 0;
							item.negativePointsSum = item.negativePointsSum ? item.negativePointsSum.toFixed(2) : 0;
						});
						this.tableData = res.data.data;

						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);

						this.openMove = true;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},

		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.PointsLogList {
	width: 100%;
	overflow: hidden;
	position: relative;

	.table-main {
		height: calc(100vh - 180px) !important;
	}
}
</style>
