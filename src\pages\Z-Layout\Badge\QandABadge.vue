<template>
	<div id="QandABadge">
		<el-badge
			:value="qAndABadgeNum"
			:hidden="!isShowBadge || !qAndABadgeNum"
			class="QandABadge-badge"
			:class="qAndABadgeNum ? 'mr10' : ''"
		>
			<el-button type="text" class="m0" @click="goTo('/QandA')"> 问答 </el-button>
		</el-badge>
	</div>
</template>
<script>
// import {} from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'QandABadge',
	components: {},
	data() {
		return {};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userLabels', 'qAndABadgeNum']),
		// 用户角色判断
		userRolesMap() {
			const roleList = this.userInfos?.adminUserVO?.adminRoleVOS || [];
			const labelList = this.userInfos?.adminUserVO?.adminUserLabelVOS || [];

			return {
				isSuperAdmin: roleList.some(item => item.arid == 1 || item.roleName == '超级管理员'),
				isSuccessSpecialist: labelList.some(item => item.remark == '成功部专员'),
			};
		},
		// 各角色判断 - 直接使用userRolesMap的结果
		// 超级管理员
		isSuperAdmin() {
			return this.userRolesMap.isSuperAdmin;
		},
		// 成功部专员
		isSuccessSpecialist() {
			return this.userRolesMap.isSuccessSpecialist;
		},
		// 是否显示（超级管理员、成功部专员）
		isShowBadge() {
			return this.isSuperAdmin || this.isSuccessSpecialist;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		console.log('🙋‍♂️ 显示问答工作台：', this.isShowBadge);
		if (this.isShowBadge) {
			this.$poller.create('QandABadge', {
				callback: this.queryBadge,
				interval: 300000, // 5分钟刷新一次
				// debug: process.env.NODE_ENV === 'development', // 开发环境启用调试日志
				idleTimeout: 30000, // 30秒无操作后暂停轮询
				// 错误处理回调
				onError: error => {
					console.error('数据刷新失败:', error);
					throw error; // 抛出错误，轮询器会捕获并处理重试
				},
			});
		}
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		// 组件销毁前清理轮询器
		if (this.isShowBadge) {
			this.$poller.destroy('QandABadge');
		}
	},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 查询badge数
		async queryBadge() {
			const API = 'selectQuestionList';
			const params = { isSelectOnlyAnswered: 0, isSelectOnlySelf: 0, isSelectOnlyWaitAnswer: 1 };
			try {
				const res = await this.$axios[API](JSON.stringify(params, { skipCancel: true }));
				if (res.data.success) {
					const qAndABadgeNum = res.data.data?.length || 0;
					this.$store.commit('setQAndABadgeNum', qAndABadgeNum);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 路由跳转
		goTo(path) {
			const queryId = this.$route.query.queryId || this.userInfos?.adminUserVO.phoneNo;
			this.$router.push({ path, query: { queryId } });
		},
	},
};
</script>

<style lang="scss">
#QandABadge {
	// 未回复消息数bage
	.QandABadge-badge {
		margin: 0;
		padding: 0;
		// margin-right: 15px;
		.el-badge__content {
			z-index: 2;
			zoom: 0.8;
			padding: 0 5px;
			top: 10px;
			right: 5px;
		}
	}
}
</style>
