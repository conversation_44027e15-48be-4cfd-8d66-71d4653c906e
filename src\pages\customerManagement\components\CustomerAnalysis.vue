<template>
	<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}失客分析</span>
				<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content">
				<p class="detail-content-title">基本信息 </p>
				<table class="base-table" cellpadding="5" cellspacing="0">
					<tr>
						<th class="label-required W10">失客日期</th>
						<th class="label-required W10">联系人</th>
						<th class="label-required W20">联系方式</th>
						<th>失客主要原因</th>
					</tr>
					<tr>
						<td>
							<el-date-picker
								v-model="detailForm.lossDate"
								type="date"
								placeholder="选择失客日期"
								value-format="timestamp"
								format="yyyy-MM-dd"
							></el-date-picker>
						</td>
						<td> <el-input placeholder="联系人" v-model.trim="detailForm.lossCustomer"></el-input></td>

						<td> <el-input placeholder="联系方式" v-model.number="detailForm.lossPhone"></el-input></td>
						<td><el-input placeholder="失客主要原因" v-model.number="detailForm.lossPassengers"></el-input> </td>
					</tr>
				</table>
				<p class="detail-content-title">失客详细分析 </p>
				<el-input
					v-model="detailForm.detailedAnalysis"
					placeholder="请输入内容"
					type="textarea"
					:autosize="{ minRows: 8, maxRows: 10 }"
				></el-input>
				<p class="detail-content-title">纠正与预防错误 </p>
				<el-input
					v-model="detailForm.preventiveMeasure"
					placeholder="请输入内容"
					type="textarea"
					:autosize="{ minRows: 8, maxRows: 10 }"
				></el-input>

				<div class="bottom-button">
					<el-button v-if="titleName == '修改'" class="mr20" @click="delDetail">删 除</el-button>
					<el-button @click="saveDetail" type="primary">保 存</el-button>
				</div>

				<!-- <div v-show="titleName !== '新增'">
          <p class="detail-content-title">操作日志
       
          </p>
          <div class="detail-log">
            <div class="detail-log-item" v-for="(item, index) in logList" :key="'oper' + index">
              <span class="mr8">{{ dateFormat(item.createTime, 'MDS') }} </span>
              <span> {{ item.createUname }}</span>
              <pre>{{ item.operationInfo }}</pre>
            </div>
          </div>
        </div> -->
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';
export default {
	name: 'materielsDetail',
	directives: {},
	components: {},
	props: {
		canEditBtn: Boolean, //修改权限
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '明细详情',
			queryStr: '',
			stationList: [],
			logList: [],
			detailFormCopy: [],
			detailForm: {
				//明细详情
				detailedAnalysis: '',
				lcid: '',
				lossCustomer: '',
				lossDate: '',
				lossPassengers: '',
				lossPhone: '',
				preventiveMeasure: '',
				tid: '',
			},
		};
	},
	created() {},
	computed: {},
	watch: {
		showCom(val) {
			if (!val) {
				this.logList = [];
				this.detailForm = _.resetValues(this.detailForm); //重置对象
				this.$emit('refresh');
			}
		},
	},
	mounted() {},
	methods: {
		//获取详情信息
		getDetail({ tid }) {
			this.$axios
				.selectLostCustomerAnalysis(JSON.stringify({ tid }))
				.then(res => {
					if (res.data.success) {
						this.detailForm = {
							...this.detailForm,
							...res.data.data,
						};
						this.detailFormCopy = _.deepClone(this.detailForm);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('selectLostCustomerAnalysis |' + error);
				});
		},
		// 添加/保存信息
		saveDetail(isClose = true) {
			if (!this.detailForm.lossDate) {
				this.$message.warning('请选择失客日期');
				return;
			}
			if (!this.detailForm.lossCustomer) {
				this.$message.warning('请输入失客客户');
				return;
			}
			if (!this.detailForm.lossPhone) {
				this.$message.warning('请输入失客客户联系方式');
				return;
			}
			const API = this.titleName == '新增' ? 'addLostCustomerAnalysis' : 'updateLostCustomerAnalysis';
			this.$axios[API](JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						isClose && (this.showCom = false);
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log(`${API} | ` + error);
				});
		},
		// 删除信息
		delDetail() {
			const lcid = this.detailForm.lcid;
			if (!lcid) {
				return;
			}
			this.$confirm('该数据将被删除，删除后数据不可恢复', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteLostCustomerAnalysis(JSON.stringify({ lcid }))
						.then(res => {
							if (res.data.success) {
								this.showCom = false;
								this.$succ('删除成功！');
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							
							console.log('deleteLostCustomerAnalysis |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		//显示弹窗
		showDetailCom(rowData) {
			if (rowData.isLostCustomerAnalysis) {
				this.getDetail(rowData);
				this.titleName = '修改';
			} else {
				this.titleName = '新增';
				this.detailForm.tid = rowData.tid;
				this.detailFormCopy = _.deepClone(this.detailForm);
			}
			this.showCom = true;
		},

		//点击返回
		closeDetailCom() {
			const isSameData = JSON.stringify(this.detailForm) == JSON.stringify(this.detailFormCopy);
			console.log('isSameData', isSameData);
			if (!isSameData) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.showCom = false;
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.showCom = false;
			}
		},
		//日期format
		dateFormat: _.dateFormat,
	},
};
</script>
<style lang="scss" scoped></style>
