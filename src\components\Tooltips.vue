<template>
	<div class="Tooltips" :class="[toolClass ? `ellipsis ${toolClass}` : 'ellipsis']" @dblclick="copyToClipboard(contStr, '')">
		<!-- 展示tooltip -->
		<el-popover v-if="isShowTooltip" placement="bottom-start" popper-class="tooltips-popper" trigger="hover">
			<template v-if="contList.length > 0">
				<div v-for="(item, index) in contList" :key="`cont-${index}`">
					<span>{{ jointString('：', item.title, item.content) }}</span>
				</div>
			</template>
			<div class="flex-align-center gap-10 max-w-500" v-else>
				<pre class="m0 p0" @dblclick="copyToClipboard(contStr, '')">{{ contStr }}</pre>
				<i
					title="复制(或双击表格的文本可复制到粘贴板)"
					class="el-icon-copy-document ml-auto pointer fs-14"
					@click="copyToClipboard(contStr)"
				></i>
			</div>

			<template slot="reference">
				<span>{{ contStr }}</span>
			</template>
		</el-popover>

		<!-- 不展示tooltip -->
		<span v-else>{{ contStr }}</span>
	</div>
</template>

<script>
import { getPxWidth, copyToClipboard, jointString } from '@/util/tool';

export default {
	name: 'Tooltips',
	props: {
		// 内容
		contStr: {
			type: [String, Number],
			default: '',
		},
		// 类
		toolClass: {
			type: String,
			default: '',
		},
		// 内容列表（可显示 标题：内容 的形式）  待优化
		contObj: {
			type: Array,
			default: () => [],
		},
		// 宽度  用于判断是否显示tooltip  待优化
		contWidth: {
			type: Number,
			default: 100,
		},
	},
	data() {
		return {};
	},
	computed: {
		// 获取内容列表
		contList() {
			return this.contObj;
		},
		// 获取内容宽度
		getWidth() {
			const content = String(this.contStr);
			return getPxWidth(content, 'normal 12px Microsoft YaHei') || this.contWidth;
		},
		// 是否显示tooltip
		isShowTooltip() {
			const content = String(this.contStr);
			if (!content) return false;
			return this.getWidth > this.contWidth - 20;
		},
	},
	created() {},
	methods: {
		copyToClipboard, // 复制文本到剪贴板
		jointString, // 拼接字符串
	},
	watch: {},
};
</script>

<style lang="scss">
.tooltips-popper {
	min-width: 30px !important;
	background: #f2f2f2 !important;
	padding: 5px !important;
	border-radius: 5px !important;
	font-size: 12px !important;
	border-color: #d7d7d7 !important;
	font-family: 'Microsoft YaHei' !important;

	// 箭头样式
	.popper__arrow {
		left: 15px !important;
		&::before {
			content: ' ';
			position: absolute;
			display: block;
			width: 0;
			height: 0;
			border-color: transparent;
			border-style: solid;
			top: -12px;
			margin-left: -6px;
			border-top-width: 0;
			border-bottom-color: #d7d7d7;
			border-width: 9px;
		}

		&::after {
			border-bottom-color: #f2f2f2 !important;
			border-width: 8px !important;
			top: -10px !important;
			margin-left: -5px !important;
		}
	}

	// 统一字体
	pre {
		font-family: 'Microsoft YaHei' !important;
	}
}
</style>
