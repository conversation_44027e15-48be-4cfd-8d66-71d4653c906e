<template>
	<div class="welCome">
		<img
			class="welCome-bg"
			:class="{ 'welCome-bg2': !!activeDashboard && !isJuxinCity }"
			src="@/assets/img/lightmes-logo.webp"
			alt="welcome"
		/>
		<component v-if="activeDashboard && !isJuxinCity" :is="activeDashboard" />
		<!-- 右侧切换按钮 -->
		<div v-if="activeDashboard && !isJuxinCity" class="dashboard-btn-wrapper">
			<div
				v-for="item in dashboardList"
				:key="item.title"
				class="dashboard-btn"
				:class="item.comName === activeDashboard ? 'dashboard-btn-active ' : ''"
				@click.stop="getDashboard(item)"
			>
				<Tooltips :cont-str="item.title" />
			</div>
		</div>
	</div>
</template>
<script>
import { debounce } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import BusinessDashboard from '@/pages/dashboardManagement/BusinessDashboard/BusinessDashboard';
import ManagerDashboard from '@/pages/dashboardManagement/ManagerDashboard/ManagerDashboard';
import DeliveryDashboard from '@/pages/dashboardManagement/DeliveryDashboard/DeliveryDashboard';
import DevelopDashboard from '@/pages/dashboardManagement/DevelopDashboard/DevelopDashboard';
import TestDashboard from '@/pages/dashboardManagement/TestDashboard/TestDashboard';
import DevelopManagerDashboard from '@/pages/dashboardManagement/DevelopManagerDashboard/DevelopManagerDashboard';
export default {
	name: 'welCome',
	components: {
		BusinessDashboard,
		ManagerDashboard,
		DeliveryDashboard,
		DevelopDashboard,
		TestDashboard,
		DevelopManagerDashboard,
	},
	data() {
		return {
			// 标签对应的标题和组件
			remarkMap: {
				管理人员: { index: 0, title: '管理驾驶舱', comName: 'ManagerDashboard' },
				业务顾问: { index: 1, title: '业务驾驶舱', comName: 'BusinessDashboard' },
				实施顾问: { index: 2, title: '交付驾驶舱', comName: 'DeliveryDashboard' },
				开发人员: { index: 3, title: '开发驾驶舱', comName: 'DevelopDashboard' },
				测试人员: { index: 4, title: '测试驾驶舱', comName: 'TestDashboard' },
				研发经理: { index: 5, title: '研发经理驾驶舱', comName: 'DevelopManagerDashboard' },
			},
			dashboardList: [],
			activeDashboard: '',
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userInfo', 'userLabels']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['isJuxinCity']), //是否聚心城人员
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		// this.queryTableData();
		this.getDashboardList();
	},
	activated() {
		// this.queryTableData(1);
		this.getDashboardList();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 根据用户标签获取驾驶舱信息
		getDashboardList() {
			const dashboardList = [];
			this.userLabels?.forEach(item => {
				if (this.remarkMap[item.remark]) {
					const index = this.remarkMap[item.remark].index;
					dashboardList[index] = this.remarkMap[item.remark];
				}
			}); // 根据用户标签获取驾驶舱信息

			// 过滤和设置默认值
			this.dashboardList = dashboardList?.filter(Boolean) || [];
			if (this.dashboardList.length > 0) {
				const activeDashboard = window.localStorage.getItem('activeDashboard_' + this.userInfo?.auid); // 获取本地存储的驾驶舱
				this.activeDashboard = activeDashboard || this.dashboardList[0].comName || ''; //设置默认值
			}

			this.$store.commit('setDashboardList', this.dashboardList); // 存储驾驶舱信息
			console.log(this.dashboardList);
		},

		// 获取当前组件
		getDashboard: debounce(async function (item) {
			this.activeDashboard = item.comName;
			this.$message.success(`操作成功，已进入${item.title}！`);
			// activeDashboard 存储在本地
			window.localStorage.setItem('activeDashboard_' + this.userInfos?.adminUserVO?.auid, item.comName);
		}),
	},
};
</script>
<style lang="scss">
.welCome {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;

	.welCome-bg {
		width: 70%;
		max-width: 1200px;
		margin-bottom: 20px;
	}
	.welCome-bg2 {
		z-index: -1;
		position: absolute;
		opacity: 0.01;
	}

	.dashboard-btn-wrapper {
		position: absolute;
		top: 10px;
		right: -20px;
		z-index: 1000;

		width: 20px;

		.dashboard-btn {
			color: #666;
			padding: 10px 0 7px 0;
			font-size: 12px;
			cursor: pointer;

			writing-mode: vertical-rl; /* 竖向文本 */
			text-orientation: mixed; /* 允许混合文本方向 */
			letter-spacing: 3px; // 增加字母间距以增大竖向文本间隙

			&:hover {
				color: #1e9d6f;
			}
		}
		.dashboard-btn-active {
			position: relative;
			// background: #1e9d6f;
			background: linear-gradient(135deg, #1e9d6f, #00c996);
			color: #fff;
			border-top-right-radius: 5px; // 添加右上椭圆
			border-bottom-right-radius: 5px; // 添加右下椭圆
			&:hover {
				color: #fff;
			}

			// 添加标签图标
			&::after {
				content: '🏷️'; // 添加标签图标
				position: absolute;
				top: -6px;
				right: 10px;
			}
		}
	}
}
</style>
