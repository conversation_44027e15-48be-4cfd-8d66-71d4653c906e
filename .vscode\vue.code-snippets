{
	// @see: https://snippet-generator.app/  代码片段生成器
	// @extend: akamud.vscode-javascript-snippet-pack 代码片段插件
  // 灵感来源 ：ElemeFE.vscode-element-helper
	
	/* pages 页面 */
	"page-init 初始页面（无其他组件等）": {
		"prefix": "page-init",
		"body": [
			"<template>",
			"  <div id=\"page\">",
			"    <el-empty :image-size=\"500\" description=\"正在拼命的开发中！\"></el-empty>",
			"  </div>",
			"</template>",
			"<script>",
			"  import { deepClone, debounce, dateFormat, jointString, sortTableData } from '@/util/tool';",
			"  import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';",
			"",
			"  export default {",
			"    name: 'page',",
			"    components: {},",
			"    data() {",
			"      return {};",
			"    },",
			"    // 监听属性 类似于data概念",
			"    computed: { ...mapGetters(['userInfos']) },",
			"    // 监控data中的数据变化",
			"    watch: {},",
			"    // 生命周期 - 创建完成（可以访问当前this实例）",
			"    created() {},",
			"    // 生命周期 - 挂载完成（可以访问DOM元素）",
			"    mounted() {},",
			"    // 生命周期 - 挂载之前",
			"    beforeMount() {},",
			"    // 生命周期 - 销毁之前",
			"    beforeDestroy() {},",
			"    // 生命周期 - 销毁完成",
			"    destroyed() {},",
			"    // 方法集合",
			"    methods: {},",
			"  };",
			"</script>",
			"",
			"<style lang=\"scss\" scoped>",
			"  #page {",
			"    width: 100%;",
			"    height: 100%;",
			"    overflow: hidden;",
			"    position: relative;",
			"  }",
			"</style>",
			""
		],
		"description": "page-init 初始页面（无其他组件等）"
	},
	// moveCom 滑窗组件

	// axios 接口请求 
	"axios 接口请求函数": {
		"prefix": "axios-func",
		"body": [
			"const API = '${1:API_NAME}';",
			"try {",
			"  const res = await this.\\$axios[API](JSON.stringify({}));",
			"  if (res.data.success) { ",
			"    this.\\$succ(res.data.message);",
			"   } else {",
			"    this.\\$err(res.data.message);",
			"   }",
			"} catch (error) {",
			"  console.error(`\\${API} |` + error);",
			"}"
		],
		"description": "axios-func 接口请求函数"
	},

	// async 函数
	"async 函数": {
		"prefix": "async-func",
		"body": [
			"async asyncFunc() {",
			"  try {",
			"      const res = await this.method();",
			"    } catch (error) {",
			"      console.error(error);",
			"    }",
			"},"
		],
		"description": "async 函数"
	},
	
	// debounce 函数
	"debounce 函数": {
		"prefix": "debounce-func",
		"body": [
			"debounceFuc: _.debounce(async function () { ",
			"  ${1}",
			"}),"
		],
		"description": "debounce 函数"
	},
	
	/* 
	 * document/Browser API
	 * dom和bom的相关API这里暂时先不做
	 * 这个插件提供了很多相关的snippet =>> akamud.vscode-javascript-snippet-pack
	*/
	// setTimeout 定时器
	// setInterval 定时器
	// nextTick 异步
	// log 打印标记
	"log 打印标记": {
		"prefix": "cl",
		"body": [
			" console.log('${1:label} :>> ', ${2:print});"
		],
		"description": "log 打印标记"
	},

	/* 
	 * elementUI 组件片段
	 * 常用组件如 输入框，选择器等
	*/
	// input 输入
	"input-base 基本输入框": {
		"prefix": "ele-input-base",
		"body": [
			"<el-input v-model=\"value\" placeholder=\"请输入内容\" clearable></el-input>"
		],
		"description": "input-base 基本输入框"
	},
	"input-integer 数值输入框(正整数)": {
		"prefix": "ele-input-integer",
		"body": [
			"<el-input v-inputInteger v-model=\"value\" placeholder=\"请输入数值\" clearable></el-input>"
		],
		"description": "input-integer 数值输入框(正整数)"
	},
	"input-textarea 文本域（输入备注等）": {
		"prefix": "ele-input-textarea",
		"body": [
			"<el-input v-model=\"value\" placeholder=\"请输入内容\" type=\"textarea\" :autosize=\"{ minRows: 2, maxRows: 4}\"></el-input>"
		],
		"description": "input-textarea 文本域（输入备注等）"
	},
	// autocomplete 自动补全（带输入建议）
	"autocomplete 自动补全（带输入建议）": {
		"prefix": "ele-autocomplete",
		"body": [
			"<el-autocomplete ref=\"cautocomplete\" v-model=\"value\" placeholder=\"请输入/点击选择\" clearable :debounce=\"500\" :fetch-suggestions=\"querySearch\" @select=\"handleSelect\" @clear=\"\\$refs.cautocomplete.activated = true\">",
			"  <template slot-scope=\"{ item }\">",
			"    <span>{{ item }}</span>",
			"  </template>",
			"</el-autocomplete>",
			"",
			"// querySearch(查询接口:xxx)",
			"querySearch(queryStr, cb) {",
			"  this.\\$axios.methodName(JSON.stringify({query: queryStr,pageNum: 1, pageSize: 30});)",
			"    .then(res => {",
			"    if (res.data.success) {",
			"      cb( res.data.data || [];)",
			"    } else {",
			"      this.\\$message.warning(res.data.message);",
			"    }",
			"    })",
			"    .catch(error => {",
			"      console.log('methodName|' + error);",
			"    })",
			"},",
			"// handleSelect(选择时:xxx) ",
			"handleSelect(item) {},"
		],
		"description": "autocomplete 自动补全（带输入建议）"
	},
	// select 选择器
	"select-single 选择器（单选）": {
		"prefix": "ele-select-single",
		"body": [
			"<el-select v-model=\"value\" placeholder=\"请选择\" clearable filterable @change=\"methodName\">",
			"  <el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"> ",
			"  </el-option>",
			"</el-select>"
		],
		"description": "select 选择器（单选）"
	},
	"selectt-multiple 选择器（基础多选）": {
		"prefix": "ele-select-multiple",
		"body": [
			"<el-select v-model=\"values\" placeholder=\"请选择\" multiple collapse-tags clearable filterable @change=\"methodName\">",
			"  <el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"> ",
			"  </el-option>",
			"</el-select>"
		],
		"description": "select 选择器（基础多选）"
	},
	// checkbox 多选
	"checkbox-single 多选（只有一个选项）": {
		"prefix": "ele-checkbox-single",
		"body": [
			" <el-checkbox v-model=\"checked\" :true-label=\"1\" :false-label=\"0\" @change=\"${1:methodName}\">备选项</el-checkbox>"
		],
		"description": "checkbox 多选（只有一个选项）"
	},
	"checkbox-group 多选组": {
		"prefix": "ele-checkbox-group",
		"body": [
			" <el-checkbox-group v-model=\"checkList\" @change=\"${1:methodName}\">",
			"    <el-checkbox :label=\"${2:example}\"></el-checkbox>",
			"    <el-checkbox :label=\"${3:example}\"></el-checkbox>",
			" </el-checkbox-group>"
		],
		"description": "checkbox 多选组"
	},
	// radio 单选
	"radio-group 单选组": {
		"prefix": "ele-radio-group",
		"body": [
			"  <el-radio-group v-model=\"radio\" @change=\"${1:methodName}\">",
			"    <el-radio :label=\"0\">备选项</el-radio>",
			"    <el-radio :label=\"1\">备选项</el-radio>",
			"  </el-radio-group>"
		],
		"description": "radio 单选组"
	},
	// button 按钮
	"button-base 按钮": {
		"prefix": "ele-button-base",
		"body": [
			"<el-button type=\"${1:text}\" class=\"${2:iconClass}\" @click=\"${3:methodName}\">按钮</el-button>"
		],
		"description": "button 按钮"
	},

	// form 表单(带规则)
	"form-rules 表单(带规则)": {
		"prefix": "ele-form-rules",
		"body": [
			"<el-form :model=\"editForm\" :rules=\"formRules\" label-width=\"100px\" label-position=\"left\" @submit.native.prevent>",
			"  <el-form-item label=\"${1:标签}\" prop=\"name\">",
			"     $0",
			"  </el-form-item>",
			"</el-form>",
			"editForm: {",
			"  name: '',",
			"},",
			"formRules: {",
			" name: [",
			"    { required: true, message: '请输入内容', trigger: 'blur' },",
			"  ],",
			"}"
		],
		"description": "form 表单(带规则)"
	},
	// dialog 弹窗
	"dialog 对话弹窗": {
		"prefix": "ele-dialog",
		"body": [
			"<el-dialog",
			"  width=\"500px\"",
			"  :visible.sync=\"dialogEdit\"",
			"  :close-on-click-modal=\"false\"",
			"  :append-to-body=\"true\"",
			"  @close=\"closeDialog\"",
			">",
			"  <span slot=\"title\">${1:标题}</span>",
			"   $0",
			"  <span slot=\"footer\">",
			"    <el-button @click=\"closeDialog\">取 消</el-button>",
			"    <el-button type=\"primary\" @click=\"saveEdit\">确 定</el-button>",
			"  </span>",
			"</el-dialog>",

			"dialogEdit: false,",
			"openDialog(){",
			"this.dialogEdit= true",
			"},",
			"closeDialog(){",
			"this.dialogEdit= false",
			"},",
			"saveEdit(){",
			"this.dialogEdit= false",
			"},"
		],
		"description": "dialog 对话弹窗"
	},

	// alert 弹窗(没有取消操作) 不常用
	// prompt 弹窗(可输入操作) 不常用
	// confirm 确认弹窗(可确定/取消操作) 
	"confirm 确认弹窗": {
		"prefix": "ele-confirm",
		"body": [
			"this.\\$confirm('此操作将永久删除该文件, 是否继续?', '提示', {",
			"\tconfirmButtonText: '确定',",
			"\tcancelButtonText: '取消',",
			"})",
			"\t.then(() => {",
			"   $0",
			"\t})",
			"\t.catch(() => {",
			"\t\tthis.\\$message.info('已取消');",
			"\t});",
		],
		"description": "confirm 确认弹窗"
	},

	// msg 消息提醒
	"message-warn 消息提醒": {
		"prefix": "ele-msg-warn",
		"body": [
			"this.\\$message.${warning}('${1:请输入内容...}');"
		],
		"description": "message 消息提醒"
	},

  // notify 消息提醒 
	"notify-succ 操作成功": {
		"prefix": "ele-notify-succ",
		"body": [
			"this.\\$succ('${1:操作成功!}');"
		],
		"description": "notify-succ 操作成功"
	},
	"notify-err 操作失败": {
		"prefix": "ele-notify-err",
		"body": [
			"this.\\$err(res.data.message);"
		],
		"description": "notify-err 操作失败"
	},
}
