<!-- BaseTableForm 组件是基于 table 封装的表单组件，用于展示和编辑表单数据，保留公司该场景的原风格。 -->
<template>
	<div class="BaseTableForm">
		<table class="base-table" cellpadding="5" cellspacing="0">
			<tbody v-for="(tItem, index) in formList" :key="index">
				<tr>
					<!-- 表头 -->
					<th v-for="item in tItem" :key="item.prop" :colspan="item.colspan" :class="item.class">
						<!-- 在父组件使用具名插槽：<template v-slot:th-xxx></template>-->
						<slot :name="'th-' + item.prop" :item="item">
							<div class="flex-align-center">
								<span class="title">{{ item.name }}</span>
								<el-tooltip v-if="item.tooltips" class="ml5" placement="top">
									<div slot="content" class="flex-column gap-10">
										<span v-for="tooltip in item.tooltips" :key="tooltip">{{ tooltip }}</span>
									</div>
									<i class="el-icon-question pointer fs-14"></i>
								</el-tooltip>
							</div>
						</slot>
					</th>
				</tr>
				<tr>
					<!-- 表单项 -->
					<td v-for="item in tItem" :key="item.prop" :colspan="item.colspan">
						<!-- 在父组件使用具名插槽：<template v-slot:td-xxx="{ item, formData, updateFormData }"></template>-->
						<slot :name="'td-' + item.prop" :item="item" :formData="formData" :updateFormData="updateFormData">
							<!-- 空格 -->
							<span v-if="item.type === 'empty'"></span>

							<!-- 文本 -->
							<Tooltips class="pl10" v-else-if="item.type === 'text'" :cont-str="formData[item.prop] || ''" />

							<!-- 日期 -->
							<Tooltips
								class="pl10"
								v-else-if="item.type === 'date-text' && formData[item.prop]"
								:cont-str="dateFormat(formData[item.prop], 'lineM')"
							/>

							<!-- 日期选择器 -->
							<el-date-picker
								:disabled="disabled || item.disabled"
								class="w-150"
								v-else-if="item.type == 'date'"
								v-model="formData[item.prop]"
								type="date"
								:placeholder="item.name"
								format="yyyy-MM-dd"
								value-format="timestamp"
							>
							</el-date-picker>

							<!-- 多行文本输入 -->
							<el-input
								:disabled="disabled || item.disabled"
								v-else-if="item.type == 'textarea'"
								v-model="formData[item.prop]"
								:placeholder="item.name"
								type="textarea"
								:autosize="{ minRows: 2, maxRows: 4 }"
							></el-input>

							<!-- 单选框 -->
							<el-radio-group
								v-else-if="item.type === 'radio'"
								:disabled="disabled || item.disabled"
								v-model="formData[item.prop]"
							>
								<el-radio :label="1">开启</el-radio>
								<el-radio :label="0" class="close-radio">关闭</el-radio>
							</el-radio-group>

							<!-- 正数输入 -->
							<el-input
								v-else-if="item.type === 'number'"
								:disabled="disabled || item.disabled"
								v-model="formData[item.prop]"
								:placeholder="item.name"
								clearable
								@input="formData[item.prop] = getInputNum(formData[item.prop], item?.numberOption?.d || 0, item.numberOption)"
								@change="updateFormData"
							></el-input>

							<!-- 默认为输入框 -->
							<el-input
								v-else
								:disabled="disabled || item.disabled"
								v-model="formData[item.prop]"
								:placeholder="item.name"
								clearable
								@change="updateFormData"
							></el-input>
						</slot>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script>
import { dateFormat, getInputNum } from '@/util/tool';
export default {
	name: 'BaseTableForm',

	props: {
		// 表单列表
		formList: {
			type: Array,
			required: true,
		},
		// 表单数据
		detailForm: {
			type: Object,
			required: true,
		},
		// 是否禁用
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 表单数据
		formData() {
			return this.detailForm;
		},

		// formData: {
		//   get() {
		//     console.log('get', this.detailForm);
		//     return this.detailForm;
		//   },
		//   set(value) {
		//     this.$emit('update', { ...value });
		//   },
		// },
	},

	methods: {
		// 发送表单数据到父组件
		updateFormData() {
			this.$emit('update', { ...this.formData });
		},
		dateFormat, //日期format
		getInputNum, //输入正数时保留后几位小数
	},
};
</script>
<style lang="scss" scoped>
.BaseTableForm {
	.base-table {
		width: 100%;
		border-left: 1px solid #e9e9e9;
		border-bottom: 1px solid #e9e9e9;

		// 隐藏全局设置的必填项的*
		.spanRequiredR::after,
		.label-required::after {
			display: none;
		}

		tr {
			border-color: #e9e9e9;
			// 表头
			th {
				min-width: 120px;
				padding: 15px 20px;
				text-align: left;
				color: #666666;
				background: #f5f5f5;
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				.title {
					font-size: 14px;
					color: #666666;
					font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
					font-weight: 650;
					font-style: normal;
				}
			}

			// 表单项
			td {
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				font-size: 14px;
				color: #666;
				height: 48px;
				padding: 5px;
				// .el-input__inner {
				//   border: none;
				// }

				> span {
					font-size: 14px;
					padding: 0 10px;
					color: #666;
					display: inline-block;
					word-break: break-all;
				}

				&-light {
					&:hover {
						text-decoration: underline;
						color: #23b781;
						cursor: pointer;
					}
				}
				// 输入框
				.el-input {
					width: 100%;
				}
				// 下拉框
				.el-select {
					width: 100%;
				}
				// 单选框
				.el-radio-group {
					padding: 0 10px;
				}
				.el-radio {
					margin-right: 10px;
					.el-radio__label {
						font-size: 14px;
					}
				}
			}

			// 必填项的*
			.spanRequiredR,
			.label-required {
				.title::after {
					content: '*';
					color: #f56c6c;
					margin-left: 5px;
					font-weight: bolder;
				}
			}
		}
	}
}
</style>
