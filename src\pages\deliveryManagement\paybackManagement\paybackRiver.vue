<template>
	<div id="paybackRiver">
		<payback-list ref="paybackList" :twidList="twidList" :channelName="channelName"></payback-list>
		<BaseLayout>
			<template #header>
				<span class="search-label">年度</span>
				<el-date-picker
					size="small"
					v-model="selectTime"
					:default-value="selectTime"
					type="year"
					value-format="timestamp"
					format="yyyy 年"
					placeholder="请选择年份"
					:clearable="false"
					@change="changeDateSelect"
				>
				</el-date-picker>

				<span class="search-label">来源</span>
				<el-select
					size="small"
					class="w-200"
					multiple
					collapse-tags
					v-model="searchForm.channelList"
					placeholder="来源"
					clearable
					filterable
					@change="queryTableData"
				>
					<el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>

				<el-radio-group v-model="searchForm.type" @change="queryTableData(1)">
					<el-radio :label="1">实施顾问视图</el-radio>
					<el-radio :label="2">业务顾问视图</el-radio>
				</el-radio-group>

				<el-checkbox v-model="searchForm.ignoreOneStage" :true-label="1" :false-label="0" @change="queryTableData(1)"
					>排除首阶段收款</el-checkbox
				>
				<el-checkbox v-model="searchForm.excludeMiddleAmountAndBPCommission" @change="queryTableData(1)"
					>扣除居间费/合伙人分成</el-checkbox
				>

				<el-input
					class="searchBox"
					v-model="searchForm.userName"
					size="small"
					placeholder="姓名"
					clearable
					@input="queryTableData(1)"
				></el-input>

				<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-toolbar">
					<span class="mr-auto">
						<el-select
							v-model="selectDecimal"
							placeholder="请选择计量单位"
							size="mini"
							clearable
							filterable
							@change="queryTableData"
						>
							<el-option label="计量单位:万元（保留两位）" :value="2"> </el-option>
							<el-option label="计量单位:元" :value="0"> </el-option>
						</el-select>
					</span>
				</div>

				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					@sort-change="sortChange"
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column label="序号" width="50" align="center" type="index"> </u-table-column>
					<!-- 展开内容 -->
					<u-table-column label="" width="20" type="expand">
						<template slot-scope="scope">
							<u-table class="table-main detail-table" :data="scope.row.deliveryRiverCompanyVOS" :show-header="false">
								<u-table-column label="" width="70" align="center"></u-table-column>
								<u-table-column
									v-for="item in tableColumn2"
									:key="'colCurr' + item.colNo"
									:label="item.colName"
									:prop="item.colNo"
									:align="item.align"
									:width="item.width"
									sortable="custom"
									resizable
								>
									<template slot-scope="scope">
										<div v-if="item.colNo != 'companyName' && item.colNo != 'total' && scope.row[item.colNo]">
											<el-tooltip :content="`延期次数：${scope.row[item.colNo].requestQty}`" placement="top">
												<span v-if="scope.row[item.colNo].requestQty" class="red"> ({{ scope.row[item.colNo].requestQty }})</span>
											</el-tooltip>
											<span class="pl20"> {{ scope.row[item.colNo].amount }}</span>
										</div>

										<div v-else>
											<Tooltips :cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''" :cont-width="100" />
										</div>
									</template>
								</u-table-column>
							</u-table>
						</template>
					</u-table-column>

					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						resizable
						sortable="custom"
					>
						<template slot-scope="scope">
							<div
								v-if="item.colNo != 'name' && item.colNo != 'total' && scope.row[item.colNo]"
								class="pointer"
								@click="openList(item.colNo, scope.row)"
							>
								<el-tooltip :content="`延期次数：${scope.row[item.colNo].requestQty}`" placement="top">
									<span v-if="scope.row[item.colNo].requestQty" class="red"> ({{ scope.row[item.colNo].requestQty }})</span>
								</el-tooltip>
								<span class="hover-green"> {{ scope.row[item.colNo].amount }}</span>
							</div>

							<!-- 合计 -->
							<Tooltips
								v-else-if="item.colNo == 'total' && scope.row[item.colNo]"
								class="hover-green"
								@click.native="openList(item.colNo, scope.row)"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

import { sourceList } from '@/assets/js/inquirySource.js';
import paybackList from './paybackList.vue';
export default {
	name: 'paybackRiver',
	components: { paybackList },
	props: { twidList: Array, channelName: Array },
	data() {
		return {
			selectDecimal: 2,
			//日期相关
			sourceList,
			selectTime: new Date(),
			startTime: '',
			endTime: '',
			searchForm: {
				type: 1,
				status: [0, 1],
				ignoreOneStage: 0,
				channelName: '',
				excludeMiddleAmountAndBPCommission: true,
				userName: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableColumn: [
				{ colName: '实施顾问', colNo: 'name', align: 'left', width: 200 },
				{ colName: '一月', colNo: '1', align: 'right' },
				{ colName: '二月', colNo: '2', align: 'right' },
				{ colName: '三月', colNo: '3', align: 'right' },
				{ colName: '四月', colNo: '4', align: 'right' },
				{ colName: '五月', colNo: '5', align: 'right' },
				{ colName: '六月', colNo: '6', align: 'right' },
				{ colName: '七月', colNo: '7', align: 'right' },
				{ colName: '八月', colNo: '8', align: 'right' },
				{ colName: '九月', colNo: '9', align: 'right' },
				{ colName: '十月', colNo: '10', align: 'right' },
				{ colName: '十一月', colNo: '11', align: 'right' },
				{ colName: '十二月', colNo: '12', align: 'right' },
				{ colName: '合计', colNo: 'total', align: 'right' },
			],
			tableColumn2: [
				// 交付信息
				{ colName: '', order: 0, colNo: 'companyName', align: 'left', width: 215 },
				{ colName: '', colNo: '01', align: 'right' },
				{ colName: '', colNo: '02', align: 'right' },
				{ colName: '', colNo: '03', align: 'right' },
				{ colName: '', colNo: '04', align: 'right' },
				{ colName: '', colNo: '05', align: 'right' },
				{ colName: '', colNo: '06', align: 'right' },
				{ colName: '', colNo: '07', align: 'right' },
				{ colName: '', colNo: '08', align: 'right' },
				{ colName: '', colNo: '09', align: 'right' },
				{ colName: '', colNo: '10', align: 'right' },
				{ colName: '', colNo: '11', align: 'right' },
				{ colName: '', colNo: '12', align: 'right' },
				{ colName: '', colNo: '13', align: 'right' },
			],
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['menuTitle']) },
	// 监控data中的数据变化
	watch: {
		twidList(newVal) {
			this.queryTableData(1);
		},
		channelName(newVal) {
			this.queryTableData(1);
		},
		'searchForm.type'(newVal) {
			this.tableColumn[0].colName = newVal == 2 ? '业务顾问' : '实施顾问';
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		const nowYear = new Date().getFullYear();
		this.changeDateSelect(new Date(nowYear, '0', '1').getTime());
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 将金额转成万单位
		convertToMillion(num) {
			if (!num) return;
			if (this.selectDecimal !== 2) {
				return Number(num).toFixed(0);
			}
			return _.convertToMillion(num);
		},
		// 交付清单
		openList(monthIndex, row) {
			this.$refs.paybackList.openPaybackList(monthIndex, row, this.searchForm, this.startTime);
		},

		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			const str = JSON.stringify({
				twidList: this.twidList,
				channelName: this.channelName,
				endTime: this.endTime,
				startTime: this.startTime,
				...this.searchForm,
			});
			this.$axios
				.collectionMonthRivers(str)
				.then(res => {
					if (res.data.success) {
						res.data.data.map(item => {
							item.total = this.convertToMillion(item.total);
							item.deliveryRiverMonthDetailVOS.map(monthItem => {
								const monthObject = {
									amount: this.convertToMillion(monthItem.amount),
									requestQty: monthItem.requestQty,
									status: monthItem.status,
								};
								item[monthItem.month] = monthObject;
							});

							// 展开内容
							item.deliveryRiverCompanyVOS.map(dItem => {
								dItem.deliveryRiverMonthDetailVOS.map(monthItem => {
									const monthObject = {
										companyName: dItem.companyName,
										amount: this.convertToMillion(monthItem.amount),
										requestQty: monthItem.requestQty,
										status: monthItem.status,
									};
									dItem[monthItem.month] = monthObject;
								});
							});
						});
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.tableData = res.data.data.sort((a, b) => b.total - a.total);
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('collectionMonthRivers |' + error);
				});
		}),

		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					if (item[column.property]?.amount || item[column.property])
						return Number(item[column.property]?.amount) || Number(item[column.property]) || 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? _.accAdd(prev, curr, 2) : prev;
				}, 0); //合计计算
				means[columnIndex] =
					sum > 0 ? 
						<span
							class="hover-green"
							on-click={e => {
								e.stopPropagation();
								this.openList(columnIndex - 2, null);
							}}
						>
							{sum}
						</span>
					 : 
						''
					;
			}
			return [means];
		},

		//自定义排序(新增/覆盖比较逻辑)
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop !== 'name' || prop !== 'total') {
						if (!a?.amount) return -1;
						if (!b?.amount) return 1;
						return a.amount - b.amount;
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		// 时间选择
		changeDateSelect(date) {
			const year = new Date(date).getFullYear();
			this.startTime = new Date(year, '0', '1').getTime();
			this.endTime = new Date(year + 1, '0', '1').getTime() - 1;
			// console.log(date);
			this.queryTableData();
		},
	},
};
</script>

<style lang="scss" scoped>
#paybackRiver {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
<style lang="scss">
#paybackRiver {
	.detail-table {
		min-height: 100% !important;
		height: 100% !important;

		// td {
		// 	background: #e6fcf5;
		// 	border-bottom: 1px solid #dfdfdf;
		// }
	}
}
</style>
