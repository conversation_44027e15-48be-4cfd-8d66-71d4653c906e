<template>
	<!-- 任务规划（抽屉组件） -->
	<div id="workPlan">
		<div class="workPlan-wrapper" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
			<div class="detail-wrapper">
				<div class="detail-head">
					<span class="detail-title flex-align-center gap-10">
						<div>任务规划</div>
						<div class="max-w-300 ellipsis">项目：{{ projectForm?.projectName || projectForm.classifyName }}</div>
						<div>状态：{{ projectStatusMap[projectForm?.status] }}</div>
						<div>客户 ：{{ projectForm?.customer }}</div>
						<div v-if="projectForm?.businessPeople?.userName">交付：{{ projectForm?.businessPeople?.userName }}</div>
						<div v-if="projectForm?.projectManager?.userName">开发：{{ projectForm?.projectManager?.userName }}</div>
						<div v-if="projectForm?.testManager?.userName">测试：{{ projectForm?.testManager?.userName }}</div>
						<div v-if="projectForm?.technicalManager?.userName">技术：{{ projectForm?.technicalManager?.userName }}</div>
					</span>
					<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
				</div>
				<!-- 明细详情弹窗 -->
				<div class="detail-content workPlan-wrapper p10">
					<div class="table-wrapper H100 flex-column">
						<div class="workPlan-title gap-10">
							<div class="workPlan-title-name max-w-500 ellipsis">
								<span v-show="projectForm.classifyName">
									<span> {{ projectForm.classifyName }}</span>
									<span class="workPlan-title-hours">{{ projectForm.taskCount }} 项 {{ projectForm.workingHours }} 小时 </span>
								</span>
							</div>
							<el-select
								class="w-150"
								size="small"
								v-model="searchForm.productUid"
								placeholder="开发人员"
								filterable
								clearable
								@change="queryTableData('search')"
							>
								<el-option v-for="item in searchDeveloperList" :key="item.auid" :label="item.userName" :value="item.auid">
								</el-option>
							</el-select>
							<el-select
								class="w-100"
								size="small"
								v-model="searchForm.status"
								placeholder="状态"
								filterable
								clearable
								@change="queryTableData('search')"
							>
								<el-option v-for="item in statusList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
							</el-select>
							<el-input
								class="searchBox"
								size="small"
								clearable
								v-model="searchForm.query"
								placeholder="任务编号/名称"
								@input="queryTableData('search')"
							></el-input>

							<div class="ml-auto min-w-200 text-right">
								<el-button :disabled="!isBatch" @click="dialogBatch = true" type="text">批量修改 </el-button>
								<el-button v-show="projectForm.pmcid" type="text" @click="$refs.TaskDetailCom.open(null)"> + 添加 </el-button>
							</div>
						</div>

						<u-table
							ref="uTableRef"
							class="table-main table-main2"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@selection-change="selectedData = $event"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							big-data-checkbox
							use-virtual
							stripe
						>
							<u-table-column width="30" type="selection" align="center"></u-table-column>
							<u-table-column label="序号" width="70" align="center" type="index"> </u-table-column>

							<u-table-column
								v-for="item in tableColumn"
								:key="item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								resizable
								sortable="custom"
							>
								<template slot-scope="scope">
									<Tooltips
										v-if="item.colNo == 'taskNo'"
										class="hover-green green"
										@click.native="$refs.TaskDetailCom.open(scope.row)"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>
									<!-- 状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:class="colorMap[scope.row[item.colNo]]"
										:cont-str="statusMap[scope.row[item.colNo]]"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 人员姓名 -->
									<Tooltips
										v-else-if="item.colNo == 'mentionUid' || item.colNo == 'productUid' || item.colNo == 'projectTurnCheck'"
										:cont-str="scope.row[item.colNo]?.userName || ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>
									<Tooltips
										v-else-if="item.colNo == 'endTime'"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>

							<u-table-column label="" width="120" align="right">
								<template slot-scope="scope">
									<el-button type="text" size="small" @click="copyRow(scope.row, 'copy')"> 复制 </el-button>
									<el-button type="text" size="small" @click="delRow(scope.row, scope.$index)"> 删除 </el-button>
								</template>
							</u-table-column>
						</u-table>
					</div>
				</div>
			</div>
		</div>

		<!-- 任务明细（抽屉组件） -->
		<TaskDetailCom ref="TaskDetailCom" :tableData="tableData" :projectForm="projectForm" @close="queryTableData($event)" />

		<!-- 批量修改（弹窗） -->
		<el-dialog
			width="666px"
			:visible.sync="dialogBatch"
			:close-on-click-modal="false"
			:append-to-body="true"
			@close="closeDialog"
		>
			<span slot="title">批量修改任务信息</span>
			<el-form
				:model="taskForm"
				size="small"
				label-width="100px"
				label-position="left"
				class="multiColumn-form"
				@submit.native.prevent
			>
				<el-form-item label="开发者" prop="name">
					<el-select v-model="taskForm.productUid.auid" placeholder="请选择开发者" clearable filterable>
						<el-option v-for="item in developerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="开发产出工时" prop="name">
					<el-input type="number" v-model="taskForm.productTime" placeholder="开发产出工时" @change="inputProductTime"></el-input>
				</el-form-item>
				<el-form-item label="测试人" prop="name">
					<el-select v-model="taskForm.projectTurnCheckUid.auid" placeholder="请选择测试人员" clearable filterable>
						<el-option v-for="item in testerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="测试产出工时" prop="name">
					<el-input type="number" v-model="taskForm.testProductTime" placeholder="测试产出工时"></el-input>
				</el-form-item>
				<el-form-item label="要求转测日期" prop="name">
					<el-date-picker
						v-model="taskForm.endTime"
						value-format="timestamp"
						type="date"
						placeholder="选择要求转测日期"
						clearable
						format="yyyy-MM-dd"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="计划测试日期" prop="name">
					<el-date-picker
						v-model="taskForm.planTestTime"
						value-format="timestamp"
						type="date"
						placeholder="选择计划测试日期"
						clearable
						format="yyyy/MM/dd"
					></el-date-picker>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="dialogBatch = false">取 消</el-button>
				<el-button type="primary" @click="saveBatch">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { resetValues, deepClone, debounce, dateFormat, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import TaskDetailCom from './taskDetailCom.vue';

export default {
	name: 'workPlan',
	components: {
		TaskDetailCom,
	},
	props: {},
	data() {
		return {
			showCom: false,
			statusList: [
				{ id: 0, name: '延误' },
				{ id: 1, name: '计划中' },
				{ id: 2, name: '开发中' },
				{ id: 3, name: '已转测' },
				{ id: 4, name: '完成' },
				{ id: 5, name: '转测不通过' },
				{ id: 6, name: '测试延误' },
			],
			projectStatusMap: {
				0: '规划中',
				1: '需求与方案评审通过',
				2: '功能分解完成',
				3: '开发计划完成',
				4: '开发全部完成',
				5: '测试全部完成',
				6: '功能验收通过',
				7: '发版准备就绪',
				8: '发版完成',
				9: '延误',
			},
			statusMap: {
				0: '延误',
				1: '计划中',
				2: '开发中',
				3: '已转测',
				4: '完成',
				5: '转测不通过',
				6: '测试延误',
			},
			colorMap: {
				0: 'red', //红 延误
				1: 'color-999', //灰 计划中
				2: 'purple', //紫 开发中
				3: 'blue', //蓝 已转测
				4: 'green', //绿 完成
				5: 'orange', //橙 转测不通过
				6: 'pink', //红 测试延误
			},
			// 查询表单
			searchForm: {
				productUid: '',
				query: '',
				status: '',
			},
			projectForm: {
				classifyName: '',
				classifyOrder: '',
				pmcid: '',
				pmid: '',
				parentPmcid: '',
			},
			// 任务明细表单
			taskForm: {
				classifyName: '',
				relevancePmtid: '',
				createUserInfo: { arid: null, auid: null, userName: '' },
				relevanceTaskVOs: { arid: null, auid: null, userName: '' },
				actualEndTime: '',
				actualStartTime: '',
				bugProductUid: { auid: '', userName: '' },
				bugTestUid: { auid: '', userName: '' },
				projectTurnCheckUid: { auid: '', userName: '' },
				content: '',
				customer: '',
				difficulty: '1.0',
				endTime: '',
				mentionUid: { auid: '', userName: '' },
				pmcid: '',
				pmid: '',
				pmtid: '',
				productTime: '',
				productUid: { auid: '', userName: '' },
				projectName: '',
				startTime: '',
				status: '',
				taskClassify: 0,
				taskLevel: 1,
				taskName: '',
				testProductTime: '',
				planTestTime: '',
			},

			// 批量修改
			dialogBatch: false,
			selectedData: [], //勾选的数据

			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 100, 500, 1000],
			},
			tableColumn: [
				{ colName: '任务编号', colNo: 'taskNo', align: 'left', width: '130' },
				{ colName: '任务', colNo: 'taskName', align: 'left' },
				{ colName: '开发人员', colNo: 'productUid', align: 'left', width: '150' },
				{ colName: '要求转测日期', colNo: 'endTime', align: 'center', width: '130' },
				{ colName: '产出工时', colNo: 'productTime', align: 'right', width: '100' },
				{ colName: '状态', colNo: 'status', align: 'left', width: '120' },
				{ colName: '提出人', colNo: 'mentionUid', align: 'center', width: '150' },
				{ colName: '来源客户', colNo: 'customer', align: 'left', width: '150' },
			],
			// 查询区域的开发人员列表
			searchDeveloperList: [],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userList']), //当前登录用户信息（含团队/菜单/权限等）
		// 批量操作
		isBatch() {
			return this.selectedData.length !== 0;
		},
		//开发人员列表(标签：开发)
		developerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('开发')) || [];
		},
		//测试人员列表(标签：测试)
		testerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('测试')) || [];
		},
	},
	// 监听-某行数据
	watch: {
		showCom(val) {
			if (!val) {
				this.tableData = [];
				this.projectForm = resetValues(this.projectForm);
				this.taskForm = resetValues(this.taskForm);
				this.searchForm = resetValues(this.searchForm);
				this.$refs.uTableRef.reloadData([]);
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 批量修改
		saveBatch() {
			const pmtids = this.selectedData.map(item => item.pmtid);
			const API = 'updateBathTaskInfoDate';
			this.$axios[API](
				JSON.stringify({
					pmtids,
					// bugTestUid: this.taskForm.bugTestUid.auid,
					endTime: this.taskForm.endTime,
					productTime: this.taskForm.productTime,
					productUid: this.taskForm.productUid.auid,
					testProductTime: this.taskForm.testProductTime,
					// turnTestLimit: this.taskForm.turnTestLimit,
					projectTurnTest: this.taskForm.projectTurnCheckUid.auid,
					planTestTime: this.taskForm.planTestTime,
				}),
			)
				.then(res => {
					if (res.data.success) {
						this.closeDialog();
						this.queryTableData('search');
						this.$succ(res.data.message);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
		// 关闭弹窗
		closeDialog() {
			this.dialogBatch = false;
			this.taskForm = resetValues(this.taskForm);
		},
		// 输入开发产出工时
		inputProductTime() {
			if (this.taskForm.productTime) {
				this.taskForm.testProductTime = Number(this.taskForm.productTime / 4).toFixed(1);
			} else {
				this.taskForm.testProductTime = 0;
			}
		},

		// 添加/修改任务详情
		copyRow: debounce(function (data) {
			const URL = 'addProjectTask';
			const DATA = JSON.stringify({
				...data,
				bugProductUid: data?.bugProductUid?.auid || '',
				bugTestUid: data?.bugTestUid?.auid || '',
				projectTurnTest: data?.projectTurnCheckUid.auid || '',
				mentionUid: data?.mentionUid?.auid || '',
				productUid: data?.productUid?.auid || '',
				relevancePmtid: data.pmtid,
			});
			this.$axios[URL](DATA)
				.then(res => {
					if (res.data.success) {
						this.queryDetailData('copy');
						this.$succ('操作成功!');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addProjectTask |' + error);
				});
		}),

		// 删除任务
		delRow({ pmtid, taskNo, taskName }) {
			this.$confirm(`任务 【${taskNo}】${taskName} 将被删除`, '删除任务', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'deleteProjectTask';
					try {
						const res = await this.$axios[API](JSON.stringify({ pmtid }));
						if (res.data.success) {
							this.queryDetailData();
							this.$succ(res.data.message);
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},

		// 点击节点 查询表格数据
		queryTableData: debounce(function (type) {
			if (!this.projectForm.pmcid) return;
			const API = 'selectTaskInfoByPmcid';
			const DATA = JSON.stringify({ ...this.searchForm, pmcid: this.projectForm.pmcid });
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;

						// 查询条件为空时，初始化时获取开发人员列表（用于筛选）
						if (type == 'init') {
							this.searchDeveloperList = Array.from(new Map(res.data.data.map(item => [item.productUid.auid, item])).values())
								.map(item => ({
									auid: item.productUid.auid,
									userName: item.productUid.userName,
								}))
								.filter(item => item.auid);
						}

						this.$nextTick(() => {
							this.$refs.uTableRef?.reloadData(this.tableData);
							this.$refs.uTableRef?.doLayout();
							// 添加或复制时滚动到底部
							(type == 'add' || type == 'copy') && this.$refs.uTableRef?.scrollBottom();
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addClassify |' + error);
				});
		}),

		// 分页(不通过接口分页)
		handlePageSize({ page, size }) {
			const tableData = this.tableData.slice((page - 1) * size, page * size);
			this.$nextTick(() => {
				this.$refs.uTableRef?.reloadData(tableData);
				this.$refs.uTableRef?.doLayout();
			});
		},

		//排序 (新增/覆盖比较逻辑)
		sortChange({ prop, order }) {
			if (order) {
				const sortedData = sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop == 'mentionUid' || prop == 'productUid') {
						if (!a?.userName) return -1;
						if (!b?.userName) return 1;
						return a?.userName.localeCompare(b?.userName, 'zh-CN');
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs.uTableRef.reloadData(sortedData);
			} else {
				this.$refs.uTableRef.reloadData(this.tableData);
			}
		},

		// 查询项目数据(pmcid)
		queryDetailData: debounce(function (type = 'init') {
			const { pmcid, pmid } = this.projectForm;
			this.$axios
				.selectClassify(JSON.stringify({ pmcid, pmid }))
				.then(res => {
					if (res.data.success) {
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.projectForm = { ...this.projectForm, ...res.data.data[0] };
						this.queryTableData(type);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectClassify |' + error);
				});
		}),

		//显示滑窗
		showDetailCom(row) {
			if (!row.pmid) {
				return this.$message.error('项目信息不存在，请刷新后重试！');
			}
			// 查询项目详情（pmid）
			try {
				this.$axios.selectProjectDetail(JSON.stringify({ pmid: row.pmid })).then(res => {
					if (res.data.success) {
						this.projectForm = { ...this.projectForm, ...deepClone(res.data.data), ...deepClone(row) };
						this.queryDetailData('init'); // 获取项目数据
					}
				});
			} catch (error) {
				this.$message.error('项目信息不存在，请刷新后重试！');
			}

			this.showCom = true;
		},

		dateFormat, //日期format
	},
};
</script>
<style lang="scss" scoped>
#workPlan {
	.workPlan-wrapper {
		position: relative;
		.table-main2 {
			height: 100% !important;
		}
		.workPlan-title {
			height: 40px;
			display: flex;
			align-items: center;
			padding: 0 10px;
			&-name {
				font-size: 16px;
				font-weight: lighter;
			}

			&-hours {
				font-family: fantasy;
			}
		}
	}
}
</style>
