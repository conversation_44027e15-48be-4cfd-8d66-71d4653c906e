/* 开发管理路由 */

const courseProduction = () => import('@/pages/trainManagement/courseProduction/courseProduction.vue'); //课程制作
const trainingPlan = () => import('@/pages/trainManagement/trainingPlan/trainingPlan.vue'); //培养计划
const onlineTraining = () => import('@/pages/trainManagement/onlineTraining/onlineTraining.vue'); //在线培训
const trainingProgress = () => import('@/pages/trainManagement/trainingProgress/trainingProgress.vue'); //培训进度


const routers = [
	{
		//课程制作
		path: '/courseProduction',
		name: 'courseProduction',
		component: courseProduction,
		meta: {
			parentTitle: '培训管理',
			title: '课程制作',
		},
	},
	{
		//培养计划
		path: '/trainingPlan',
		name: 'trainingPlan',
		component: trainingPlan,
		meta: {
			parentTitle: '培训管理',
			title: '培养计划',
		},
	},
	{
		//在线培训
		path: '/onlineTraining',
		name: 'onlineTraining',
		component: onlineTraining,
		meta: {
			parentTitle: '培训管理',
			title: '在线培训',
			noCache: true,
		},
	},
	{
		//培训进度
		path: '/trainingProgress',
		name: 'trainingProgress',
		component: trainingProgress,
		meta: {
			parentTitle: '培训管理',
			title: '培训进度',
		},
	}
];

export default routers;
