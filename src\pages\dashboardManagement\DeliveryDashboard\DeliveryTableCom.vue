<template>
	<div class="DeliveryTableCom">
		<div class="table-title flex-align-center pl2 pr2">
			<el-badge class="mini-badge pointer pr10" :value="tableInfo.badgeNum" :max="99" :hidden="!showBadge">
				<div :class="tableInfo.subTitle ? 'min-w-100' : ''" :title="tableInfo.id">
					<Tooltips class="fs-12 bolder" :cont-str="tableTitle" />
				</div>
			</el-badge>
			<div v-if="tableInfo.subTitle" class="sub-title fs-12 color-999 mr10">{{ tableInfo.subTitle }}</div>
			<div v-if="tableInfo.yesterdayPoints" class="sub-title fs-12 green">昨日积分：{{ tableInfo.yesterdayPoints }}</div>
			<div v-if="tableInfo.yesterdayDeductPoints" class="sub-title fs-12 red">
				昨日扣分：{{ tableInfo.yesterdayDeductPoints }}
			</div>

			<el-button v-if="tableInfo.button" type="text" size="mini" class="p0 ml-auto" @click="openList">
				{{ tableInfo.button }}
			</el-button>

			<el-button
				v-if="tableTitle == '需求清单'"
				type="text"
				size="mini"
				class="ml10 el-icon-plus"
				@click="openDetail('DemandManagementDetail', null)"
			>
				添加
			</el-button>
		</div>
		<!-- 表格主体 -->
		<div class="table-wrapper border border-radius-8 p2">
			<u-table
				ref="uTableRef"
				class="table-main"
				:height="500"
				:row-height="25"
				:row-class-name="getRowColor"
				:empty-text="tableInfo.emptyText || '暂无数据'"
				:total="tablePageForm.total"
				:page-size="tablePageForm.pageSize"
				:current-page="tablePageForm.currentPage"
				:page-sizes="tablePageForm.pageSizes"
				:pagination-show="tableData.length > 50 ? true : false"
				@handlePageSize="handlePageSize"
				show-header-overflow="title"
				use-virtual
				stripe
			>
				<u-table-column :label="' '" width="35" type="index" align="center"></u-table-column>
				<u-table-column
					v-for="item in tableInfo.tableColumn"
					:key="item.colNo"
					:label="item.colName"
					:prop="item.colNo"
					:align="item.align"
					:width="item.width"
					resizable
				>
					<!-- sortable="custom" -->
					<template slot-scope="{ row, column }">
						<!-- 各种日期（默认不显示分秒 lineM ） -->
						<Tooltips
							v-if="item.colName.includes('时间')"
							:cont-str="dateFormat(row[item.colNo], 'MDHM')"
							:cont-width="column.realWidth"
						/>

						<Tooltips
							v-else-if="item.colNo.includes('validTo')"
							:cont-str="dateFormat(row[item.colNo], 'line')"
							:cont-width="column.realWidth"
						/>
						<!-- <Tooltips
							v-else-if="tableTitle === '未清首期款' && item.colNo == 'complateMonth'"
							:cont-str="dateFormat(row.complateMonth, 'MD')"
							:cont-width="column.realWidth"
						/> -->
						<Tooltips
							v-else-if="item.colNo.includes('Month')"
							:cont-str="dateFormat(row[item.colNo], 'YM')"
							:cont-width="column.realWidth"
						/>

						<Tooltips
							v-else-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
							:cont-str="dateFormat(row[item.colNo], 'MD')"
							:cont-width="column.realWidth"
						/>

						<!-- ------------------询盘相关----------------- -->
						<!-- 询盘编号 -->
						<Tooltips
							v-else-if="item.colNo == 'number'"
							class="hover-green green"
							:class="getRowColor({ row })"
							@click.native="openDetail('InquiryDetail', '修改', row)"
							:cont-str="row[item.colNo] || '未知'"
							:cont-width="column.realWidth"
						/>
						<!-- 区域 -->
						<Tooltips
							v-else-if="item.colNo == 'region'"
							:cont-str="jointString('/', row.province, row.city, row.area)"
							:cont-width="column.realWidth"
						/>
						<!-- 计划 -->
						<Tooltips
							v-else-if="item.colNo == 'nextPlan'"
							:cont-str="jointString('：', dateFormat(row.nextStep, 'MD'), row.nextPlan)"
							:cont-width="column.realWidth"
						/>
						<!-- 客户(公司名) -->
						<Tooltips
							v-else-if="item.colNo == 'companyName'"
							:class="row.dmid || row.idid ? 'hover-green' : ''"
							@click.native="
								row.dmid ? openDetail('ContractDetail', row) : row.idid ? openDetail('InquiryDetail', '修改', row) : () => {}
							"
							:cont-str="row.companyName || row.registeredBusinessName || row.customerName"
							:cont-width="column.realWidth"
						/>
						<!-- 客户(团队名) -->
						<Tooltips
							v-else-if="item.colNo == 'teamName'"
							:class="row.tid ? 'hover-green' : ''"
							@click.native="row.tid ? openDetail('TeamDetail', row) : () => {}"
							:cont-str="row.teamName || row.teamFullname"
							:cont-width="column.realWidth"
						/>

						<!-- 阶段 -->
						<Tooltips
							v-else-if="item.colNo == 'stage'"
							:class="row.dmid ? 'hover-green' : ''"
							@click.native="row.dmid ? openDetail('DeliveryDetail', row) : () => {}"
							:cont-str="jointString(' ', dateFormat(row.complateMonth, 'MD'), row.stage, row.standard)"
							:cont-width="column.realWidth"
						/>

						<!-- 金额 -->
						<Tooltips
							v-else-if="tableTitle === '未清首期款' && item.colNo == 'amount' && row.amount"
							:class="row.dmid ? 'hover-green' : ''"
							@click.native="row.dmid ? openDetail('DeliveryDetail', row) : () => {}"
							:cont-str="jointString(' ', dateFormat(row.complateMonth, 'MD'), convertToMillion(row.amount))"
							:cont-width="column.realWidth"
						/>
						<!-- 	<Tooltips
							v-else-if="item.colNo == 'estimatedAmount' && row.estimatedAmount"
							:cont-str="jointString(' ', dateFormat(row.expectedMonth, 'MD'), row.estimatedAmount.toFixed(2))"
							:cont-width="column.realWidth"
						/> -->

						<Tooltips
							v-else-if="item.colNo == 'amount' && row.amount"
							:cont-str="convertToMillion(row.amount)"
							:cont-width="column.realWidth"
						/>
						<Tooltips
							v-else-if="item.colNo == 'estimatedAmount' && row.estimatedAmount"
							:cont-str="row.estimatedAmount.toFixed(2)"
							:cont-width="column.realWidth"
						/>

						<!-- ---------------积分相关----------------- -->
						<!-- 审查状态 0: 无需检查, 1: 待评价, 2: 不合格, 3: 合规带问题, 4: 合格, 5: 优秀, 10 免检 -->
						<div v-else-if="item.colNo == 'auditStatus'" class="fs-12">
							<Tooltips
								:class="
									{ 0: 'color-999', 1: 'red', 2: 'red', 3: 'orange', 4: 'green', 5: 'green', '10': 'color-999' }[row.auditStatus]
								"
								:cont-str="jointString('：', auditStatusMap[row.auditStatus], row.auditMemo)"
								:cont-width="column.realWidth"
							/>
						</div>
						<!-- 工作项 -->
						<Tooltips
							v-else-if="item.colNo == 'operation'"
							class="hover-green"
							@click.native="
								row.tid
									? openDetail('TeamDetail', row)
									: row.dmid
										? openDetail('DeliveryDetail', row)
										: row.idid
											? openDetail('InquiryDetail', '修改', row)
											: () => {}
							"
							:cont-str="getOperation(row)"
							:cont-width="column.realWidth"
						/>

						<!-- 未完成的项目进度 -->
						<div
							v-else-if="item.colNo == 'deliveryScheduleStateVOS'"
							:class="row.dmid ? 'pointer' : ''"
							@click="row.dmid ? openDetail('DeliveryDetail', row) : () => {}"
						>
							<StepsCom :rowData="row" :space="60" />
						</div>

						<!-- 需求 -->
						<Tooltips
							v-else-if="item.colNo == 'demandDocumentName'"
							class="hover-green"
							@click.native="openDetail('DemandManagementDetail', row)"
							:cont-str="row[item.colNo]"
							:cont-width="column.realWidth - 20"
						/>
						<!-- 需求状态 -->
						<Tooltips
							v-else-if="tableTitle == '需求清单' && item.colNo == 'status'"
							:cont-str="demandStatusMap[row[item.colNo]]"
							:cont-width="column.realWidth - 20"
						/>

						<!-- 默认显示 -->
						<Tooltips
							v-else-if="row[item.colNo]"
							:cont-str="row[item.colNo] !== null ? row[item.colNo] : ''"
							:cont-width="column.realWidth - 20"
						/>
					</template>
				</u-table-column>
				<!-- 其他列/操作 -->
				<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDialogRow(row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(row)"></el-button>
                </template>
              </u-table-column> -->
			</u-table>
		</div>
	</div>
</template>

<script>
import { debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import StepsCom from '../DeliveryStepsCom';
import { demandStatusMap } from '@/assets/js/projectSource';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: { StepsCom },
	name: 'DeliveryTableCom', //组件名应同路由名(否则keep-alive不生效)
	props: {
		tableInfo: Object,
	},
	data() {
		return {
			// 0: 无需检查, 1: 待评价, 2: 不合格, 3: 合规带问题, 4: 合格, 5: 优秀, 10 免检
			auditStatusMap: {
				0: '无需检查',
				1: '待评价',
				2: '不合格',
				3: '合规带问题',
				4: '合格',
				5: '优秀',
				10: '免检',
			},
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 100, 500],
			},
			demandStatusMap, // 需求状态
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 表格标题
		tableTitle() {
			return this.tableInfo.title;
		},
		// 显示badge
		showBadge() {
			if (['未来14天计划', '差旅费报销', '未清首期款', '45天内即将到期项目阶段'].includes(this.tableTitle)) {
				return false;
			}
			return this.tableInfo.badgeNum;
		},
	},
	// 监控data中的数据变化
	watch: {
		'tableInfo.data'(newVal) {
			this.tableData = newVal;
			this.tablePageForm = {
				total: newVal.length,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 100, 500],
			};
			// const tableData = newVal?.slice(1, 51);
			const { currentPage: page, pageSize: size } = this.tablePageForm;
			this.handlePageSize({ page, size });
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	activated() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开明细
		openDetail(ref, ...args) {
			this.$emit('openDetail', ref, ...args);
			this.$emit('getTableOptions', this.tableInfo.data);
		},
		// 打开清单
		openList() {
			const { button } = this.tableInfo;
			const buttonActions = {
				询盘管理: 'InquiryTable',
				跟单日报: 'DocumentaryDaily',
				合同管理: 'ContractTable',
				项目总览: 'DeliveryOverview',
				人员计划: 'DeliverySchedule',
				到期管理: 'DeliveryList',
				健康评分: 'ActivityRating',
				费用报销: 'ExpenseReimbursement',
				需求管理: 'DemandManagement',
			};

			if (buttonActions[button]) {
				this.$emit('openList', buttonActions[button], 'openMove');
			} else {
				this.$message.warning(`打开${button}的功能，我们正在开发中！`);
			}
		},
		// 获取工作项信息
		getOperation(row) {
			const { operation, idid, dmid, tid } = row;
			let infos = '';
			if (tid && row.teamVO) {
				infos = this.jointString(' / ', row.teamVO.teamName, this.dateFormat(row.teamVO.validTo, 'line'));
			} else if (dmid && row.deliverStageManagement) {
				infos = this.jointString(
					' / ',
					row.deliverStageManagement.stage,
					row.deliverStageManagement.standard,
					this.dateFormat(row.deliverStageManagement.complateMonth, 'line'),
				);
			} else if (idid) {
				infos = row.idNumber;
			}

			return this.jointString(' - ', operation, infos);
		},
		// 行样式（颜色显示）
		getRowColor({ row }) {
			if (this.tableTitle == '未来14天计划') {
				const expectedMonth = this.$moment(row.expectedMonth).startOf('month').valueOf();
				const nowMonth = this.$moment(new Date()).startOf('month').valueOf(); // 获取当前月第一天的时间戳
				if (expectedMonth < nowMonth) return 'red'; // 预计成交月份小于当前月时显示红色
			} else if (this.tableTitle == '未清首期款') {
				const complateMonth = this.$moment(row.complateMonth).startOf('month').valueOf();
				const nowMonth = this.$moment(new Date()).startOf('month').valueOf(); // 获取当前月第一天的时间戳
				if (complateMonth == nowMonth) return 'blue'; // 预计成交月份等于当前月时显示蓝色
				if (complateMonth < nowMonth) return 'red'; // 预计成交月份小于当前月时显示红色
				if (complateMonth > nowMonth) return ''; // 预计成交月份小于当前月时正常显示
			} else if (this.tableTitle == '45天内即将到期项目阶段' || this.tableTitle == '60天内即将流失的客户') {
				const complateMonth = this.$moment(row.complateMonth).startOf('month').valueOf();
				const nowMonth = this.$moment(new Date()).startOf('month').valueOf(); // 获取当前月第一天的时间戳
				if (complateMonth == nowMonth) return 'orange'; // 预计成交月份等于当前月时显示黄色
				// if (complateMonth < nowMonth) return 'red'; // 预计成交月份小于当前月时显示红色
				if (complateMonth > nowMonth) return ''; // 预计成交月份小于当前月时正常显示
			}

			const keywords = ['不', '期', '失', '跟单'];
			return keywords.some(keyword => this.tableTitle.includes(keyword)) ? 'red' : undefined;
		},

		// 将金额转成万单位
		convertToMillion(value, d = 2) {
			if (!value || Number(value) <= 0) return '';
			// Convert the value to million units
			const valueInMillion = value / 10000;

			// Round to two decimal places
			const resultVal = (Math.round(valueInMillion * 100) / 100).toFixed(2);
			const decimalPart = resultVal.split('.')[1];
			const decimalsToAdd = 2 - decimalPart.length;
			const finalValue = decimalsToAdd > 0 ? resultVal + '0'.repeat(decimalsToAdd) : resultVal;
			return finalValue;
		},
		// 分页(不通过接口分页)
		handlePageSize({ page, size }) {
			const tableData = this.tableData.slice((page - 1) * size, page * size);
			this.$nextTick(() => {
				this.$refs.uTableRef?.reloadData(tableData);
				this.$refs.uTableRef?.doLayout();
			});
		},
		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss">
.DeliveryTableCom {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;

	.border-radius-8 {
		border-radius: 8px;
	}

	.table-title {
		color: #555;
		height: 20px;
		.mini-badge {
			.el-badge__content {
				z-index: 88;
				zoom: 0.75;
				padding: 0px 5px;
				/* right: 12px; */
				top: 6.5px;
			}
		}
	}

	// 表格样式调整
	.table-wrapper {
		width: 100%;
		height: calc(100% - 20px) !important;
		&:hover {
			border-color: #1e9d6f !important;
		}

		// 表格主体
		.table-main {
			width: 100% !important;
			height: 100% !important;
			min-height: 50px !important;
			// height: calc(100% - 22px) !important;

			.el-table__body td {
				height: 25px !important;
				padding: 0px;
			}
			.el-table__body th {
				height: 25px !important;
				padding: 0px;
			}
			.is-leaf:not(:last-child) {
				padding: 0 !important;
			}
			.el-table__empty-block {
				min-height: 40px;
				width: 100% !important;
				.el-table__empty-text {
					line-height: normal;
				}
			}

			// 表格底部线去除
			.el-table::before {
				height: 0px;
				background: transparent;
			}
		}

		// 表格分页
		.myPagination {
			zoom: 0.7;
		}
	}
}
</style>
