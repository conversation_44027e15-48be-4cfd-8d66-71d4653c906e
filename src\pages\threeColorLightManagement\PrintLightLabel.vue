<!-- 打印三色灯标签 -->
<template>
	<div class="canvas-content">
		<canvas id="hideCvs" width="200" height="200"></canvas>
	</div>
</template>
<script>
import { getLodop } from '@/assets/js/LodopFuncs';
import QRCode from 'qrcode';
let LODOP;
export default {
	name: 'PrintLightLabel',
	data() {
		return {};
	},
	mounted() {
		setTimeout(() => {
			LODOP = getLodop();
		}, 3000);
	},
	beforeDestroy() {
		LODOP = null;
	},
	methods: {
		printData(arr) {
			if (LODOP) {
				LODOP.PRINT_INIT('threeLight_Label');
				LODOP.SET_PRINT_PAGESIZE(0, '91.5mm', '30.0mm', 'CreateCustomPage'); //设置三色灯标签纸,如以后换纸需调整
				// LODOP.PRINT_INITA(0, 2, 300, 96);
				if (arr.length > 0) {
					arr.forEach((item, index) => {
						const { sim, mac, pro } = item;
						const isWifi = pro.toLocaleLowerCase().indexOf('wifi') > -1 ? true : false;
						const currLocation = index % 3;
						const qrContent = isWifi
							? `http://www.lightmes.cn?lightid={"type":"3","content":"${sim}","mac":${mac}}`
							: `http://www.lightmes.cn?lightid={"type":"3","content":"${sim}"}`;
						const qrImage = this.barCode(qrContent);
						const qrLeftMap = {
							0: 6,
							1: 131,
							2: 254,
						};
						const textLeftMap = {
							0: -4,
							1: 122,
							2: 245,
						};
						const macTextLeftMap = {
							0: 8,
							1: 134,
							2: 256,
						};
						//LODOP.ADD_PRINT_BARCODE(2, qrLeftMap[currLocation], 104, 125, 'QRCode', qrContent); 大小无法控制,暂不使用
						//LODOP.SET_PRINT_STYLEA(0, 'QRCodeErrorLevel', 'L');
						if (isWifi) {
							LODOP.ADD_PRINT_TEXT(85, macTextLeftMap[currLocation], 84, 20, mac ? mac.replace(/"/g, '') : '');
							LODOP.SET_PRINT_STYLEA(0, 'FontName', '微软雅黑');
							LODOP.SET_PRINT_STYLEA(0, 'FontSize', 6);
							LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2);
							LODOP.ADD_PRINT_TEXT(96, textLeftMap[currLocation], 100, 20, sim);
							LODOP.SET_PRINT_STYLEA(0, 'FontName', '微软雅黑');
							LODOP.SET_PRINT_STYLEA(0, 'FontSize', 5);
							LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2);
						} else {
							LODOP.ADD_PRINT_TEXT(90, textLeftMap[currLocation], 100, 20, sim);
							LODOP.SET_PRINT_STYLEA(0, 'FontName', '微软雅黑');
							LODOP.SET_PRINT_STYLEA(0, 'FontSize', 5);
							LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2);
						}
						if (qrImage) {
							LODOP.ADD_PRINT_IMAGE(3, qrLeftMap[currLocation], 82, 82, qrImage);
							LODOP.SET_PRINT_STYLEA(0, 'Stretch', 1);
						}
						if (index > 0 && currLocation == 2) LODOP.NEWPAGEA();
					});
					LODOP.PREVIEW();
					// LODOP.PRINT();
					// LODOP.PRINT_SETUP();
					this.$succ();
				}
			} else {
				this.$message.warning('插件未加载完成,请稍后再试');
			}
		},
		//生成二维码,条码
		barCode(sn) {
			let codeURL;
			QRCode.toDataURL(sn, { errorCorrectionLevel: 'Q', margin: 0 }, function (err, url) {
				if (!err) {
					codeURL = url;
				} else {
					console.log('二维码生成异常');
				}
			});
			return codeURL;
		},
	},
};
</script>
<style lang="scss">
.canvas-content {
	#hideCvs {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 100;
		display: none;
	}
}
</style>
