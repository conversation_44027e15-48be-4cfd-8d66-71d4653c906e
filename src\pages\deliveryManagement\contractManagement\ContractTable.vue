<template>
	<div id="ContractTable" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<BaseLayout>
			<template #header>
				<label class="search-label">成交月份</label>
				<el-date-picker
					class="min-w-100 vw8"
					size="small"
					v-model="searchForm.selectMonth"
					type="month"
					value-format="timestamp"
					placeholder="不限"
					@change="queryTableData(1)"
					clearable
				>
				</el-date-picker>
				<el-select
					size="small"
					class="min-w-100 vw8"
					v-model="searchForm.projectType"
					placeholder="项目类型"
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option v-for="item in projectTypeOptions" :key="item.id" :label="item.label" :value="item.id"> </el-option>
				</el-select>

				<!-- <el-select
					v-model="searchForm.salesman"
					class="select-column-3 min-w-100 vw8"
					size="small"
					placeholder="业务顾问"
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option v-for="item in salesmanList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
				</el-select> -->

				<el-select
					v-model="searchForm.implement"
					class="select-column-3 min-w-100 vw8"
					size="small"
					placeholder="实施顾问"
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option v-for="item in implementList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
				</el-select>

				<!-- 只有业务助理和超级管理员可看 -->
				<el-select
					v-if="showRecorded"
					v-model="searchForm.recorded"
					class="min-w-100 vw8"
					size="small"
					placeholder="录入状态"
					clearable
					filterable
					@change="queryTableData(1)"
				>
					<el-option label="未录入" :value="0"> </el-option>
					<el-option label="已录入" :value="1"> </el-option>
				</el-select>

				<SearchHistoryInput
					className="min-w-100 vw8"
					name="salesmanName"
					placeholder="业务顾问"
					v-model.trim="searchForm.saleName"
					@input="queryTableData(1)"
				/>

				<SearchHistoryInput
					name="companyName"
					placeholder="客户工商注册名称"
					v-model="searchForm.queryParam"
					@input="queryTableData(1)"
				/>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
				</div>
			</template>
			<template #main>
				<div class="table-toolbar">
					<el-checkbox-group class="mr10" v-model="searchForm.contractFlag" @change="queryTableData(1)">
						<el-checkbox label="0">
							<span class="fs-12">合同已上传</span>
						</el-checkbox>
						<el-checkbox label="1">
							<span class="fs-12">合同未上传</span>
						</el-checkbox>
					</el-checkbox-group>

					<el-checkbox-group class="mr10 fs-12" v-model="searchForm.approveStatus" @change="queryTableData(1)">
						<el-checkbox :label="0">
							<span class="fs-12">未审核</span>
						</el-checkbox>
						<el-checkbox :label="1">
							<span class="fs-12">已审核</span>
						</el-checkbox>
						<el-checkbox :label="2">
							<span class="fs-12">驳回</span>
						</el-checkbox>
					</el-checkbox-group>

					<el-checkbox
						class="mr10 fs-12"
						v-model="searchForm.receiptStatus"
						:true-label="2"
						:false-label="0"
						@change="queryTableData(1)"
					>
						<span class="fs-12">仅显示未付清</span>
					</el-checkbox>
					<el-checkbox
						class="mr10 fs-12"
						v-model="searchForm.submitApproveStatus"
						:true-label="'0'"
						:false-label="''"
						@change="queryTableData(1)"
					>
						<span class="fs-12">仅显示未提交</span>
					</el-checkbox>
					<ExportBtn @trigger="openExport" />
					<el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button>
				</div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:dialog-data="tableColumn"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					@header-dragend="headerDragend"
					@reset="updateColumn(tableColumnCopy)"
					@show-field="updateColumn"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="(item, index) in tableColumn?.filter(item => item.state || item.state === undefined)"
						:key="item.colNo + index"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="{ row, column }">
							<!-- 各种日期 -->
							<Tooltips
								v-if="item.colNo == 'dealMonth'"
								:cont-str="dateFormat(row[item.colNo], 'YM')"
								:cont-width="column.realWidth - 20"
							/>
							<!-- 签单日期 -->
							<Tooltips
								v-else-if="item.colNo == 'signingDate' || item.colNo == 'approveDate'"
								:cont-str="dateFormat(row[item.colNo], 'lineM')"
								:cont-width="column.realWidth - 20"
							/>
							<!-- 询盘编号 -->
							<Tooltips
								v-else-if="item.colNo == 'number'"
								class="hover-green green"
								@click.native="$emit('openInquiryDetail', '修改', row)"
								:cont-str="row[item.colNo] || '未知'"
								:cont-width="column.realWidth - 20"
							/>

							<!-- 项目名称 -->
							<Tooltips
								v-else-if="item.colNo == 'projectName'"
								class="hover-green green"
								@click.native="$emit('openContract', row, '合同详情')"
								:cont-str="row[item.colNo] ? row[item.colNo] + '' : ''"
								:cont-width="column.realWidth - 20"
							/>

							<!-- 成交金额 -->
							<Tooltips
								v-else-if="['dealAmount', 'collectionAmount'].includes(item.colNo)"
								:cont-str="row[item.colNo]?.toFixed(3)"
								:cont-width="column.realWidth - 20"
							/>
							<!-- 回款计划 -->
							<Tooltips
								v-else-if="item.colNo == 'plan' && row[item.colNo]"
								:cont-str="row[item.colNo] + '期'"
								:cont-width="column.realWidth - 20"
							/>
							<!-- 合同状态 0 未关闭，1 已关闭-->
							<Tooltips
								v-else-if="item.colNo == 'closed'"
								:class="{ 0: 'green', 1: 'red' }[row[item.colNo]]"
								:cont-str="{ 0: '开启', 1: '关闭' }[row[item.colNo]]"
								:cont-width="column.realWidth - 20"
							/>
							<!-- 合同 -->
							<div v-else-if="item.colNo == 'contractName'">
								<div v-if="!row[item.colNo]">
									<Tooltips
										:style="{ color: row[item.colNo] ? '#1E9D6F' : '#e57373', cursor: 'pointer' }"
										:cont-str="'未上传'"
										:cont-width="column.realWidth - 20"
									/>
								</div>
								<FilePopover v-else trigger="click" :url="row.contractUrl" :content="row[item.colNo]" />
							</div>
							<!-- 项目类型 -->
							<Tooltips
								v-else-if="item.colNo == 'projectType'"
								:cont-str="projectTypeMap[row[item.colNo]]"
								:cont-width="column.realWidth - 20"
							/>
							<div v-else-if="item.colNo == 'submitApproveStatus'">
								<!-- 合同审核 -->
								<el-button
									v-if="row.submitApproveStatus == 1 && row.approveStatus !== 2"
									type="text"
									size="mini"
									@click="openAllocation('合同审核', row)"
									:class="{ 0: 'color-999', 1: 'green', 2: 'red' }[row.approveStatus]"
								>
									{{ { 0: '未审核', 1: '已审核', 2: '驳回' }[row.approveStatus] }}
								</el-button>
								<el-button v-else type="text" size="mini" class="red" @click="$emit('openContract', row, '合同详情')">{{
									row.approveStatus == 2 ? '驳回' : '未提交'
								}}</el-button>
							</div>
							<div v-else-if="item.colNo == 'recorded'">
								<el-checkbox
									v-if="showRecorded"
									v-model="row[item.colNo]"
									:true-label="1"
									:false-label="0"
									@change="changeRecorded(row)"
								>
								</el-checkbox>
								<span v-else>{{ { 0: '未录入', 1: '已录入' }[row[item.colNo]] }}</span>
							</div>

							<!-- 其他 -->
							<Tooltips v-else :cont-str="row[item.colNo] ? row[item.colNo] + '' : ''" :cont-width="column.realWidth - 20" />
						</template>
					</u-table-column>
					<u-table-column label="" align="right" width="60" fixed="right">
						<template slot-scope="{ row }">
							<el-button type="text" size="mini" @click="$emit('openContract', row, '合同详情')">管理</el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>
<script>
import { deepClone, debounce, dateFormat, sortTableData, bigAdd } from '@/util/tool'; //按需引入常用工具函数
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';
import { projectTypeOptions, projectTypeMap } from '@/assets/js/contractSource';

export default {
	name: 'ContractTable',
	components: {
		FilePopover,
		ExportBtn,
	},
	props: {
		twidList: { type: Array, default: () => [] },
		channelName: { type: Array, default: () => [] },
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 200, 500, 1000],
			},
			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'left', state: true, width: '' },
				{ colName: '项目名称', colNo: 'projectName', align: 'left', state: true, width: '' },
				{ colName: '项目类别', colNo: 'projectType', align: 'left', state: true, width: '' },
				{ colName: '签单日期', colNo: 'signingDate', align: 'center', state: true, width: '120' },
				{ colName: '客户工商注册名称', colNo: 'registeredBusinessName', align: 'left', state: true, width: '150' },
				{ colName: '业务顾问', colNo: 'salesmanName', align: 'left', state: true, width: '' },
				{ colName: '实施顾问', colNo: 'implementName', align: 'left', state: true, width: '' },
				{ colName: '咨询', colNo: 'consultingName', align: 'left', state: false, width: '' },
				// { colName: '谈单', colNo: 'talkName', align: 'left', width: '' },
				{ colName: 'BPUL', colNo: 'businessPartnerUnitName', align: 'left', state: true, width: '' },
				{ colName: '成交月份', colNo: 'dealMonth', align: 'center', state: true, width: '' },
				{ colName: '合同编号', colNo: 'contractNo', align: 'left', state: true, width: '' },
				{ colName: '合同文件', colNo: 'contractName', align: 'left', state: true, width: '' },
				{ colName: '成交金额(万元)', colNo: 'dealAmount', align: 'right', state: true, width: '' },
				{ colName: '已收金额(万元)', colNo: 'collectionAmount', align: 'right', state: false, width: '' },
				{ colName: '硬件', colNo: 'deliverHardwareMemo', align: 'left', state: true, width: '' },
				{ colName: '标品数量', colNo: 'standardProductCount', align: 'left', state: true, width: '' },
				{ colName: '回款计划', colNo: 'plan', align: 'center', state: true, width: '' },
				{ colName: '合同状态', colNo: 'closed', align: 'center', state: true, width: '' },
				{ colName: '审核状态', colNo: 'submitApproveStatus', align: 'left', state: true, width: '' },
				{ colName: '审核时间', colNo: 'approveDate', align: 'center', state: true, width: '' },
				{ colName: '录入状态', colNo: 'recorded', align: 'center', state: true, width: '' },
			],
			tableColumnCopy: [],

			searchForm: {
				submitApproveStatus: '',
				recorded: '',
				receiptStatus: 2,
				approveStatus: [],
				contractFlag: ['0', '1'],
				twid: [],
				channelName: [],
				queryParam: '',
				projectType: '',
				selectMonth: '',
				saleName: '',
			},
			projectTypeOptions,
			projectTypeMap,
			userList: [], //渠道业务顾问
			openMove: '',
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		// 字段数据
		fieldData() {
			// 因tableColumn中的字段名的key为colName，umy-ui需要的是name，后续再考虑是否需要做兼容
			const DATA = this.tableColumn.map(item => {
				return {
					name: item.colName, // 字段名
					state: item.state === undefined ? true : item.state, // 选择状态
					disabled: item.disabled, // 是否禁用
				};
			});
			return DATA;
		},
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
		// 只有合同审核人员才可审核
		isApprove() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '合同审核') || false;
		},
		// 录入状态只有超级管理员和业助可看
		showRecorded() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.roleName == '超级管理员' || item.roleName == '业助');
		},
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.tableColumnCopy = deepClone(this.tableColumn);
		this.tableColumn = getColumn(this.tableColumn, this.$options.name);
	},

	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					if (['dealAmount', 'collectionAmount'].includes(column.property)) return Number(item[column.property]) || 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? bigAdd(prev, curr, 3) : prev;
				}, 0); //合计计算
				means[columnIndex] = sum > 0 ? <span>{sum}</span> : '';
			}
			return [means];
		},
		// 修改录入状态
		async changeRecorded(row) {
			const API = 'updateDeliverManagementRecord';
			try {
				const res = await this.$axios[API](JSON.stringify(row));
				if (res.data.success) {
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
					this.queryTableData(1);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 打开分配计划详情
		openAllocation(type, rowData) {
			if (!this.isApprove) {
				return this.$message.warning('只有合同审核人员才可进入审核页面，请联系管理员授权！');
			}
			this.$emit('openAllocation', type, rowData);
		},

		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			this.$axios
				.selectSalesmanByTwids(JSON.stringify({ twids: this.twidList, counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.$nextTick(() => {
							this.userList = res.data.data || [];
						});
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			this.searchForm.startTime = this.searchForm.selectMonth
				? this.$moment(this.searchForm.selectMonth).startOf('month').valueOf()
				: null;
			this.searchForm.endTime = this.searchForm.selectMonth
				? this.$moment(this.searchForm.selectMonth).endOf('month').valueOf()
				: null;
			const API = 'selectDeliverManagement'; //接口
			const DATA = JSON.stringify({
				...this.searchForm,
				twid: this.twidList,
				channelName: this.channelName,
				flag: 1, //1 合同管理列表 2 ：交付管理列表
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item['salesmanName'] = item?.salesman?.userName || '';
							item['implementName'] = item?.implement?.userName || '';
							item['talkName'] = item?.talk?.userName || '';
							item['businessPartnerUnitName'] = item?.businessPartnerUnit?.userName || '';
							item['consultingName'] = item?.consulting?.userName || '';
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);

						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
						(type == 'openMove' || type == 'init') && this.queryUserByTwids(); //初始时查询渠道人员
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 展示字段
		updateColumn(data) {
			this.tableColumn = updateColumn(this.$options.name, data);
			this.sortChange(this.tableSort, true);
		},
		// 列宽拖动
		headerDragend(newWidth, oldWidth, column) {
			updateColumnWidth(this.$options.name, column.property, newWidth, this.tableColumn);
		},
		//日期format
		dateFormat: dateFormat,
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
				this.$emit('getTableOptions', sortedData); // 传递表格数据用于组件里上下页切换
			});
		},

		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					...this.searchForm,
					twid: this.twidList,
					channelName: this.channelName,
					pageNum: this.tablePageForm.currentPage,
					pageSize: this.tablePageForm.pageSize,
					flag: 1, //1 合同管理列表 2 ：交付管理列表
				}), //接口参数
				API: 'exportContractManager', //导出接口
				downloadData: '合同管理导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$emit('openExport', PROPS);
		}),
	},
};
</script>

<style lang="scss" scoped>
#ContractTable {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	&.moveToggle {
		.table-main {
			height: calc(100vh - 245px) !important;
		}
	}
}
</style>
