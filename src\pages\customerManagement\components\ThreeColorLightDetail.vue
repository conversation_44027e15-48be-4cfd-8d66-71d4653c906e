<template>
	<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}三色灯参数</span>
				<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content">
				<div class="flex align-end">
					<p class="detail-content-title">基本信息 </p>
					<canvas id="QRCode"></canvas>
				</div>
				<table class="base-table" cellpadding="5" cellspacing="0">
					<!-- 第一行 -->
					<tr>
						<th>三色灯编号</th>
						<th>绿灯闪烁间隔(0.1秒)</th>
						<th>黄灯闪烁间隔(0.1秒)</th>
						<th>红灯闪烁间隔(0.1秒)</th>
						<th>离线重启次数(次)</th>
						<th>消抖时长(毫秒)</th>
						<th>MQTT连接方式</th>
					</tr>
					<tr>
						<!-- 三色灯编号 -->
						<td>{{ detailForm.sim }}</td>
						<td> <el-input placeholder="绿灯闪烁间隔(整数)" v-model.number="detailForm.green"></el-input></td>
						<td> <el-input placeholder="黄灯闪烁间隔(整数)" v-model.number="detailForm.yellow"></el-input></td>
						<td><el-input placeholder="红灯闪烁间隔(整数)" v-model.number="detailForm.red"></el-input> </td>
						<td><el-input placeholder="离线重启次数(整数)" v-model.number="detailForm.restartCount"></el-input> </td>
						<td><el-input placeholder="消抖时长(整数)" v-model.number="detailForm.debounce"></el-input> </td>
						<td
							><el-select v-model="detailForm.mqttType" placeholder="MQTT连接方式" clearable filterable>
								<el-option label="阿里云物联网平台" :value="0"> </el-option>
								<el-option label="本地MQTT服务" :value="1"> </el-option>
							</el-select>
						</td>
					</tr>
					<!-- 第二行 -->
					<tr>
						<th>心跳间隔(秒)</th>
						<th>脉冲间隔(秒)</th>
						<th>信号强度</th>
						<th>MAC地址</th>
						<th>消息版本</th>
						<th>数据周期(秒)</th>
						<th>空数据发送</th>
					</tr>
					<tr>
						<td><el-input placeholder="心跳间隔(整数)" v-model.number="detailForm.heartbeat"></el-input></td>
						<td><el-input placeholder="脉冲间隔(整数)" v-model.number="detailForm.circleduration"></el-input></td>
						<!-- 信号强度 -->
						<td>{{ detailForm.signal }}</td>
						<!-- MAC地址 -->
						<td>{{ detailForm.mac }}</td>
						<!-- 消息版本 -->
						<td>{{ detailForm.version }}</td>
						<td><el-input placeholder="计数间隔(秒)" v-model.number="detailForm.countInterval"></el-input></td>
						<td>
							<el-select v-model="detailForm.countZeroReport" placeholder="空数据发送" clearable filterable>
								<el-option label="上传" :value="0"> </el-option>
								<el-option label="不上传" :value="1"> </el-option>
							</el-select>
						</td>
					</tr>
					<!-- 第三行 -->
					<tr>
						<th colspan="2">MQTT连接地址(URL)</th>
						<th>用户名</th>
						<th>密码</th>
						<th>产品ProductKey</th>
						<th>产品唯一标识</th>
						<th>设备密钥</th>
					</tr>
					<td colspan="2">
						<el-autocomplete
							placeholder="MQTT连接地址"
							v-model="detailForm.mqttUrl"
							:fetch-suggestions="
								(queryString, cb) => {
									querySaved(queryString, cb, 'urls');
								}
							"
							@select="handleSelect($event, 'mqttUrl')"
							:debounce="500"
							class="W100"
						>
							<div slot-scope="scope">
								<div>{{ scope.item }}</div>
							</div>
						</el-autocomplete>
					</td>
					<td>
						<el-autocomplete
							placeholder="MQTT用户名"
							v-model="detailForm.mqttUser"
							:fetch-suggestions="
								(queryString, cb) => {
									querySaved(queryString, cb, 'users');
								}
							"
							@select="handleSelect($event, 'mqttUser')"
							:debounce="500"
							class="W100"
						>
							<div slot-scope="scope">
								<div>{{ scope.item }}</div>
							</div>
						</el-autocomplete>
					</td>
					<td>
						<el-autocomplete
							placeholder="MQTT密码"
							v-model="detailForm.mqttPawd"
							:fetch-suggestions="
								(queryString, cb) => {
									querySaved(queryString, cb, 'pawds');
								}
							"
							@select="handleSelect($event, 'mqttPawd')"
							:debounce="500"
							class="W100"
						>
							<div slot-scope="scope">
								<div>{{ scope.item }}</div>
							</div>
						</el-autocomplete>
					</td>
					<td><el-input placeholder="产品roductKey" v-model="detailForm.locaProductKey"></el-input></td>
					<td><el-input placeholder="产品唯一标识" v-model="detailForm.locaDeviceName"></el-input></td>
					<td><el-input placeholder="物联网平台设备密钥" v-model="detailForm.locaDeviceSecret"></el-input></td>
				</table>

				<div :style="{ visibility: detailForm.updated && detailForm.updated != 1 ? 'visible' : 'hidden' }" class="bot_left">
					<span
						>*<span>{{ getUpdateStr(detailForm.updated) }}</span></span
					>
					<el-button type="text" class="el-icon-refresh-right" @click="getDetail(detailForm)">刷新 </el-button>
				</div>
				<div class="bottom-button flex-justify-end">
					<!-- 批量修改切换 -->
					<div v-if="rowDatas.length > 1">
						<el-button
							type="text"
							:disabled="nowIndex == 0"
							@click="
								nowIndex > 0 ? --nowIndex : '';
								getDetail(rowDatas[nowIndex]);
							"
							>上一个</el-button
						>
						<el-button
							type="text"
							:disabled="nowIndex == rowDatas.length - 1"
							@click="
								nowIndex < rowDatas.length - 1 ? ++nowIndex : '';
								getDetail(rowDatas[nowIndex]);
							"
							>下一个</el-button
						>
					</div>

					<el-button @click="saveDetail" type="primary">保 存</el-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';
import QRCode from 'qrcode';

export default {
	name: 'ThreeColorLightDetail',
	directives: {},
	components: {},
	props: {
		canEditBtn: Boolean, //修改权限
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '明细详情',
			queryStr: '',
			rowDatas: [],
			nowIndex: 0,
			logList: [],
			detailFormCopy: [],
			detailForm: {
				circleduration: 0,
				countInterval: 0,
				countZeroReport: 0, //计数为0是否上传
				debounce: 0,
				green: 0,
				heartbeat: 0,
				locaDeviceName: '', //设备在产品内唯一标识
				locaDeviceSecret: '', //物联网平台为设备颁发的设备密钥
				locaProductKey: '', //产品所属ProductKey
				mqttPawd: '',
				mqttType: 0,
				mqttUrl: '',
				mqttUser: '',
				red: 0,
				restartCount: 0,
				sim: '',
				yellow: 0,
				//明细详情
				currentstate: '',
				latitude: '',
				longitude: '',
				mac: '',
				signal: 0,
				simQRCode: '',
				time: '',
				type: '',
				updateTime: '',
				updated: 1,
				version: '',
				versionType: '',
			},
		};
	},
	created() {},
	computed: {},
	watch: {
		showCom(val) {
			if (!val) {
				this.logList = [];
				this.detailForm = _.resetValues(this.detailForm); //重置对象
				this.$emit('refresh');
			}
		},
	},
	mounted() {},
	methods: {
		// 获取更新状态
		getUpdateStr(status) {
			let str = '';
			const nowTime = new Date();
			switch (status) {
				case 1:
					break;
				case 2:
					str = '等待参数同步结果,请检查三色灯是否在线，稍后“刷新”查看同步结果';
					break;
				case 3:
					str = '参数已同步';
					break;
				case 4:
					str = `${this.$moment(nowTime).format('MM/DD HH:mm:ss')}同步参数...  稍后点击“刷新”查看同步结果`;
					break;
				default:
					break;
			}
			return str;
		},
		// 生成二维码
		getQrcode(simQRCode) {
			const canvas = document.getElementById('QRCode');
			QRCode.toCanvas(canvas, simQRCode, error => {
				if (error) {
					console.log('二维码异常', error);
				}
			});
		},
		//获取详情信息
		getDetail({ sim }) {
			this.$axios
				.selectTricolourLightParamBySim(JSON.stringify({ sim }))
				.then(res => {
					if (res.data.success) {
						res.data.data.simQRCode = res.data.data.simQRCode.replace(/\\/, '');
						this.detailForm = {
							...this.detailForm,
							...res.data.data,
						};
						this.detailFormCopy = _.deepClone(this.detailForm);
						this.showCom = true;
						this.$nextTick(() => {
							this.getQrcode(this.detailForm.simQRCode);
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('selectLostCustomerAnalysis |' + error);
				});
		},
		// 添加/保存信息
		saveDetail(isClose = true) {
			const API = 'updateTricolourLightParam';
			this.$axios[API](JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						this.detailForm.updated = 4;
						this.saveInputed(); //保存输入过的数据
						isClose && (this.showCom = false);
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log(`${API} | ` + error);
				});
		},

		//显示弹窗
		showDetailCom(rowDatas) {
			this.rowDatas = rowDatas;
			this.nowIndex = 0;
			this.getDetail(rowDatas[this.nowIndex]);
			this.titleName = '修改';
		},

		//点击返回
		closeDetailCom() {
			const isSameData = JSON.stringify(this.detailForm) == JSON.stringify(this.detailFormCopy);
			console.log('isSameData', isSameData);
			if (!isSameData) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.showCom = false;
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.showCom = false;
			}
		},
		//日期format
		dateFormat: _.dateFormat,

		// 保存输入过的数据
		saveInputed() {
			let urls = [],
				users = [],
				pawds = [];
			// detailForm.mqttUrl detailForm.mqttUser detailForm.mqttPawd
			urls = JSON.parse(window.localStorage?.getItem('lightMain_urls')) || [];
			users = JSON.parse(window.localStorage?.getItem('lightMain_users')) || [];
			pawds = JSON.parse(window.localStorage?.getItem('lightMain_pawds')) || [];

			this.detailForm.mqttUrl &&
				!urls.includes(this.detailForm.mqttUrl) &&
				urls.push(this.detailForm.mqttUrl) &&
				window.localStorage?.setItem('lightMain_urls', JSON.stringify(urls));
			this.detailForm.mqttUser &&
				!users.includes(this.detailForm.mqttUser) &&
				users.push(this.detailForm.mqttUser) &&
				window.localStorage?.setItem('lightMain_users', JSON.stringify(users));
			this.detailForm.mqttPawd &&
				!pawds.includes(this.detailForm.mqttPawd) &&
				pawds.push(this.detailForm.mqttPawd) &&
				window.localStorage?.setItem('lightMain_pawds', JSON.stringify(pawds));
		},
		querySaved(str, cb, type) {
			//获取保存的数据
			const res = JSON.parse(window.localStorage?.getItem(`lightMain_${type}`)) || [];
			const results = str
				? res.filter(item => {
						return item.indexOf(str) === 0;
					})
				: res;
			cb(results);
		},
		handleSelect(item, value) {
			this.detailForm[value] = item;
		},
	},
};
</script>
<style lang="scss" scoped></style>
