/**
 * Axios 基础配置
 */
'use strict';
import axios from 'axios';

// 环境变量
const ENV = process.env.NODE_ENV;
const IS_DEV = ENV === 'development'; // 是否开发环境
const IS_PROD = ENV === 'production'; // 是否生产环境

// 基础Axios配置
const baseConfig = {
	// 根据环境设置baseURL
	baseURL: IS_PROD ? '' : '/dev-api',
	// 默认超时时间（毫秒）
	timeout: 60000,
	// 默认响应类型
	responseType: 'json',
	// 允许跨域请求携带凭证
	withCredentials: false,
	// 请求头设置
	headers: {
		'Content-Type': 'application/json',
		'Cache-Control': 'no-cache',
		'X-Requested-With': 'XMLHttpRequest',
	},
};
/**
 * 启用请求日志记录（仅用于开发环境）
 */
const requestLogging = () => {
	// 记录请求日志
	axios.interceptors.request.use(config => {
		const requestColor = 'color: dodgerblue;';
		config.startTime = new Date().getTime(); // 记录请求时间
		console.groupCollapsed(`%c 🚀 API请求: ${config.url} (${new Date().toLocaleString()})`, requestColor);
		// console.log('请求时间:', `${new Date().toLocaleString()}`);
		console.log('请求方法:', config.method.toUpperCase());
		console.log('请求参数:', config.data || config.params);
		console.groupEnd();
		return config;
	});

	const warnColor = 'color: red;';
	// 记录响应日志
	axios.interceptors.response.use(
		response => {
			const responseColor = 'color: lightseagreen;';
			const responseTime = new Date().getTime() - response.config.startTime;
			const errorMessage = !response.data.success ? response.data.message : ''; // 如果response.data.success为false，则提示错误信息
			console.groupCollapsed(
				`%c ✅ API响应: ${response.config.url} (${responseTime}ms)` + (errorMessage ? ` %c${errorMessage}` : ''),
				responseColor,
				errorMessage ? warnColor : '',
			);
			// console.log('响应时间:', `${responseTime}ms`);
			console.log('响应状态:', response.status);
			console.log('响应数据:', response.data.data);
			console.groupEnd();
			return response;
		},
		error => {
			// 错误信息（除取消请求外）
			if (!axios.isCancel(error)) {
				console.group(`%c ❌ API错误: ${error.config?.url || '未知'}`, warnColor);
				console.error(`%c 错误信息: ${error.message}`, warnColor);
				console.groupEnd();
			}
			return Promise.reject(error);
		},
	);
};

/**
 * 初始化 axios 配置
 * @param {Object} customConfig - 自定义配置（可选）
 */
export const initAxiosConfig = (customConfig = {}) => {
	// 合并基础配置和自定义配置
	const finalConfig = { ...baseConfig, ...customConfig };

	// 应用配置到axios默认值
	Object.keys(finalConfig).forEach(key => {
		// 特殊处理headers，合并而不是覆盖
		if (key === 'headers' && customConfig.headers) {
			axios.defaults.headers = {
				...axios.defaults.headers,
				...finalConfig.headers,
			};
		} else {
			axios.defaults[key] = finalConfig[key];
		}
	});

	// 开发环境下启用请求/响应日志
	if (!IS_PROD && IS_DEV) {
		requestLogging();
	}
};

// 导出axios实例，方便直接使用
export { axios };

export default {
	initAxiosConfig,
	axios,
};
