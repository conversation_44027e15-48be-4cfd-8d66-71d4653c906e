<template>
	<!-- 在线支持清单 -->
	<div class="OnlineSupportList">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 问题详情 -->
		<QuestionDetail ref="QuestionDetail" isMoveCom @closeMove="queryTableData(1)" />
		<BaseLayout>
			<template #header>
				<!-- 带建议日期 -->
				<span class="search-label">提问期间</span>
				<DateSelect
					@change="
						searchForm.questionStartDate = $event.startTime;
						searchForm.questionEndDate = $event.endTime;
						queryTableData(1);
					"
				/>

				<!-- 带搜索历史的输入框 -->
				<SearchHistoryInput
					name="customerName"
					placeholder="客户名称"
					v-model.trim="searchForm.customerNameQuery"
					@input="queryTableData(1)"
				/>

				<el-checkbox v-model="searchForm.isOnlyShowUnfixedBug" :true-label="1" :false-label="0" @change="queryTableData(1)">
					仅显示未修复BUG
				</el-checkbox>

				<!-- 操作按钮 -->
				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<!-- 导出按钮 -->
					<!-- <ExportBtn @trigger="openExport" /> -->
					<el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button>
				</div>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar"></div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:dialog-data="tableColumn"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					@header-dragend="headerDragend"
					@reset="updateColumn(tableColumnCopy)"
					@show-field="updateColumn"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="(item, index) in tableColumn.filter(item => item.state)"
						:key="item.colNo + index"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="item.colNo.includes('Time')"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="item.colNo.includes('Date')"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 编号 -->
							<Tooltips
								v-else-if="item.colNo == 'questionNumber'"
								class="hover-green green"
								:cont-str="scope.row[item.colNo] || ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								@click.native="$refs.QuestionDetail.queryDetail(scope.row, 'openMove')"
							/>
							<!-- 状态/类型 -->
							<Tooltips
								v-else-if="item.colNo == 'isBug'"
								:class="scope.row[item.colNo] == 1 ? 'red' : ''"
								:cont-str="['否', '是'][scope.row[item.colNo]] || '待确认'"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] || ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDetail('编辑',scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template>
              </u-table-column> -->
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData, formatMilliseconds } from '@/util/tool'; //按需引入常用工具函数
import { bigSub, bigDiv } from '@/util/math';
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import ExportTable from '@/components/ExportTable'; //导出组件
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
import QuestionDetail from './QuestionDetail.vue'; //问题详情
// import BaseDetail from './BaseDetail.vue'; //明细组件
// import btnAuth from '@/mixins/btnAuth';

export default {
	name: 'OnlineSupportList', //组件名应同路由名(否则keep-alive不生效)
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		ExportBtn,
		DateSelect,
		QuestionDetail,
		// BaseDetail,
	},
	// mixins: [btnAuth],
	data() {
		return {
			activeTab: 'OnlineSupportList', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [], //表格数据
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '编号', colNo: 'questionNumber', align: 'left', width: '' },
				{ colName: '问题', colNo: 'title', align: 'left', width: '' },
				{ colName: '客户', colNo: 'customer', align: 'left', width: '' },
				{ colName: '用户', colNo: 'userName', align: 'left', width: '' },
				{ colName: '提问时间', colNo: 'questionTime', align: 'center', width: '120' },
				{ colName: '功能模块', colNo: 'functionModule', align: 'left', width: '' },
				{ colName: '功能项', colNo: 'questionSourcePath', align: 'left', width: '' },
				{ colName: '交付人员', colNo: 'deliveryUserName', align: 'left', width: '' },
				{ colName: '回复时间', colNo: 'replyTime', align: 'center', width: '120' },
				{ colName: '交付响应时长(h)', colNo: 'deliveryResponseLong', align: 'right', width: '' },
				{ colName: '成功部人员', colNo: 'successUserName', align: 'left', width: '' },
				{ colName: '成功部响应时长(h)', colNo: 'successResponseLong', align: 'right', width: '' },
				{ colName: '研发人员', colNo: 'devUserName', align: 'left', width: '' },
				{ colName: '研发响应时长(h)', colNo: 'devResponseLong', align: 'right', width: '' },
				{ colName: '是否BUG', colNo: 'isBug', align: 'center', width: '' },
				{ colName: 'BUG修复日期', colNo: 'bugFixDate', align: 'center', width: '' },
			],
			tableColumnCopy: [],

			// 选择器数据 - 车间/部门/类型等...
			didOptions: [],
			// 查询表单
			searchForm: {
				customerNameQuery: '',
				endDate: '',
				isOnlyShowUnfixedBug: 0,
				pageNum: 0,
				pageSize: 0,
				query: '',
				questionEndDate: '',
				questionStartDate: '',
				startDate: '',
				startIndex: 0,
				// 其他...
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.tableColumnCopy = deepClone(this.tableColumn);
		this.tableColumn = getColumn(this.tableColumn, this.$options.name);
		this.queryTableData();
	},
	activated() {
		this.queryTableData(1);
		// this.changeTab();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开明细
		openDetail(type, row) {
			this.$refs.BaseDetail.showDetailCom(type, row);
		},

		// 切换tab
		changeTab() {
			if (this.activeTab == 'OnlineSupportList') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 查询表格数据
		queryTableData: debounce(async function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'onlineSupportList'; //接口
			try {
				const res = await this.$axios[API](JSON.stringify(this.searchForm));
				if (res.data.success) {
					this.tableData = res.data.data.map(item => {
						const { departmentReply } = item;
						const DeliveryReply = departmentReply[0] ? departmentReply[0] : {}; // 获取交付部
						const SuccessReply = departmentReply[1] ? departmentReply[1] : {}; // 获取成功部
						const DevReply = departmentReply[2] ? departmentReply[2] : {}; // 获取研发部

						const replyInfo = {
							deliveryUserName: DeliveryReply.replyUserName || '',
							replyTime: DeliveryReply?.replyTime || '', // 获取交付回复时间
							deliveryResponseLong: DeliveryReply.responseTime,
							successUserName: SuccessReply.replyUserName || '',
							successResponseLong: SuccessReply.responseTime || '',
							devUserName: DevReply.replyUserName || '',
							devResponseLong: DevReply.responseTime || '',
						};
						return { ...item, ...replyInfo };
					});
					this.tablePageForm.total = res.data.totalItems;
					type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					this.sortChange(this.tableSort, true);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 展示字段
		updateColumn(data) {
			this.tableColumn = updateColumn(this.$options.name, data);
			this.sortChange(this.tableSort, true);
		},
		// 列宽拖动
		headerDragend(newWidth, oldWidth, column) {
			updateColumnWidth(this.$options.name, column.property, newWidth, this.tableColumn);
		},

		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({ ...this.searchForm }), //接口参数
				API: 'xxxxxDownload', //导出接口
				downloadData: 'xxxxx明细', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.OnlineSupportList {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
