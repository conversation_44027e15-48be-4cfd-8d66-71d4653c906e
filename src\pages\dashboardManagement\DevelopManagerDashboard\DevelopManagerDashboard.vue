<template>
	<div class="DevelopManagerDashboard" v-loading.lock="isLoading" element-loading-text="正在加载中，请勿中途退出...">
		<!-- 需求管理 -->
		<DemandManagement v-if="showMap.DemandManagement" isMoveCom ref="DemandManagement" @closeMove="queryTableData(1)" />
		<!-- 需求详情 -->
		<DemandManagementDetail
			v-if="showMap.DemandManagementDetail"
			ref="DemandManagementDetail"
			@close="queryTableData(1)"
			@openProject="openDetail('ProjectDetail', $event)"
		/>
		<!-- 项目详情 -->
		<ProjectDetail v-if="showMap.ProjectDetail" ref="ProjectDetail" @close="queryTableData(1)" />

		<!-- 任务详情 -->
		<TaskDetail v-if="showMap.TaskDetail" ref="TaskDetail" :tableOptions="tableOptions" @turnTask="queryTableData(1)" />

		<BaseLayout :showHeader="false">
			<template #main>
				<div class="dashboard-wrapper">
					<!-- 顶部 -->
					<DashboardCard :dataInfoList="dataInfoList" @refresh="queryTableData('refresh')"> </DashboardCard>

					<!-- 主干 -->
					<div class="content-wrapper">
						<component
							ref="componentRef"
							:class="`content-item-${index + 1}`"
							v-for="(tableInfo, index) in tableList"
							:key="tableInfo.id"
							:is="'TableCom'"
							:tableInfo="tableInfo"
							@getTableOptions="tableOptions = $event"
							@openDetail="openDetail"
							@openList="openList"
						/>
					</div>
				</div>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { debounce, dateFormat, jointString, setLocalStorage, getLocalStorage } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
/* 通用 */
import DashboardCard from '../DashboardCard.vue'; //数据卡片
import TableCom from './DevelopManagerTableCom.vue'; //表格

import TaskDetail from '@/pages/developmentManagement/developmentWorkManagement/components/taskDetailCom'; //任务明细
import ProjectDetail from '@/pages/developmentManagement/projectManagement/projectManagement/projectDetail'; //项目明细
/* 需求管理 */
import DemandManagement from '@/pages/developmentManagement/demandManagement/DemandManagement.vue'; //需求管理
import DemandManagementDetail from '@/pages/developmentManagement/demandManagement/DemandManagementDetail.vue'; //需求管理

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DashboardCard,
		TableCom,
		TaskDetail,
		ProjectDetail,

		DemandManagement,
		DemandManagementDetail,
	},
	name: 'DevelopManagerDashboard', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			isLoading: false,
			// 顶部数据卡片
			dataInfoList: [
				{ desc: '本月积分', name: 'monthlyPointsSum', data: 0, class: 'bg-green' },
				{ desc: '年累计积分', name: 'yearlyPointsSum', data: 0, class: 'bg-green' },
				{ desc: '本月扣分', name: 'monthlyDeductPoints', data: 0, class: 'bg-red' },
				{ desc: '本月产出工时', name: 'monthlyWorkHour', data: 0, class: 'bg-blue' },
			],
			// 主干各个表格
			tableList: [
				{
					id: 'pointsLogList',
					title: '积分/扣分记录',
					// subTitle: '昨日',
					badgeNum: 0,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '工作项', colNo: 'operation', align: 'left', width: '' },
						{ colName: '记分', colNo: 'points', align: 'right', width: '50' },
					],
				},
				{
					id: 'developPointsRankVOS',
					title: '部门积分排名',
					// subTitle: '前五名',
					badgeNum: 0,
					// isDataList: true,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '姓名', colNo: 'userName', align: 'left', width: '' },
						{ colName: '积分', colNo: 'points', align: 'right', width: '60' },
					],
				},
				{
					id: 'developDeductPointsRankVOS',
					title: '部门扣分排名',
					// subTitle: '前五名',
					badgeNum: 0,
					// isDataList: true,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '姓名', colNo: 'userName', align: 'left', width: '' },
						{ colName: '扣分', colNo: 'points', align: 'right', width: '60' },
					],
				},
				{
					id: 'developTaskVOS',
					title: '我的开发任务',
					// subTitle: '金额前三',
					badgeNum: 0,
					count: 0,
					// isDataList: true,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '项目', colNo: 'projectName', align: 'left', width: '' },
						{ colName: '任务', colNo: 'taskName', align: 'left', width: '' },
						{ colName: '完成日期', colNo: 'endTime', align: 'center', width: '80' },
						{ colName: '工时', colNo: 'productTime', align: 'right', width: '60' },
					],
				},
				{
					id: 'overdueDevelopTaskVOS',
					title: '部门延误的开发任务',
					// subTitle: '金额前五',
					badgeNum: 0,
					// isDataList: true,
					data: [],
					button: '开发任务管理',
					tableColumn: [
						{ colName: '项目', colNo: 'projectName', align: 'left', width: '' },
						{ colName: '任务', colNo: 'taskName', align: 'left', width: '' },
						{ colName: '完成日期', colNo: 'endTime', align: 'center', width: '80' },
						{ colName: '开发者', colNo: 'developUserName', align: 'left', width: '60' },
						{ colName: '延误', colNo: 'overdueDay', align: 'right', width: '60' },
					],
				},

				{
					id: 'overdueTestTaskVOS',
					title: '部门延误的测试任务',
					// subTitle: '工作中心数前10%',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					// isDataList: true,
					data: [],
					button: '测试任务管理',
					tableColumn: [
						{ colName: '项目', colNo: 'projectName', align: 'left', width: '' },
						{ colName: '任务', colNo: 'taskName', align: 'left', width: '' },
						{ colName: '完成日期', colNo: 'endTime', align: 'center', width: '80' },
						{ colName: '测试者', colNo: 'developUserName', align: 'left', width: '60' },
						{ colName: '延误', colNo: 'overdueDay', align: 'right', width: '60' },
					],
				},
				{
					id: 'developProjectVOS',
					title: '部门进行中的项目',
					badgeNum: 0,
					// isDataList: true,
					data: [],
					button: '',
					tableColumn: [{ colName: '项目进度', colNo: 'developProjectVOS', align: 'left', width: '' }],
				},
				{
					id: 'selectDemandSuperVisionListVOS',
					title: '需求清单',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '需求管理',
					tableColumn: [
						{ colName: '提交人', colNo: 'submissionName', align: 'left', width: '110' },
						{ colName: '提交时间', colNo: 'submissionTime', align: 'left', width: '100' },
						{ colName: '需求', colNo: 'demandDocumentName', align: 'left', width: '' },
						{ colName: '人天合计', colNo: 'manDayTotal', align: 'right', width: '80' },
						{ colName: '状态', colNo: 'status', align: 'left', width: '120' },
					],
				},
				{
					id: 'developWorkHoursVOS',
					title: '本月部门人员负荷（小时）',
					badgeNum: 0,
					// isDataList: true,
					data: [],
					button: '任务河流图',
					tableColumn: [
						{ colName: '姓名', colNo: 'userName', align: 'left', width: '100' },
						// { colName: '', colNo: 'developWeekWorkHoursVOS', align: 'left', width: '' },
						// 动态生成当前月份的周日列
						...Array.from({ length: 4 }, (_, index) => {
							const date = new Date();
							const firstDayOfMonth = new Date(date.getFullYear(), date.getMonth(), 1); // 当前月份的第一天
							const firstSunday = new Date(
								firstDayOfMonth.setDate(firstDayOfMonth.getDate() + (7 - firstDayOfMonth.getDay()) % 7),
							); // 计算第一个周日
							const weekSunday = new Date(firstSunday.setDate(firstSunday.getDate() + index * 7)); // 计算每个周日
							const weekFormatted = weekSunday.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' });
							return { colName: weekFormatted, colNo: `${index + 1}`, align: 'right' };
						}),
					],
				},
			],
			titleName: '',

			tableOptions: [], //表格数据用于组件里上下页切换
			userList: [], //用户选项数据

			showMap: {
				ProjectDetail: false,
				TaskDetail: false,

				DemandManagement: false,
				DemandManagementDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.twidList = getLocalStorage(`dashboard_twidList` + this.userInfos?.adminUserVO?.phoneNo) || [];
		this.queryUserByTwids();
		this.queryTableData('init');
	},
	activated() {
		this.queryTableData(1);
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {
		this.tableList = null;
		this.dataInfoList = null;
	}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 通用打开详情
		openDetail(ref, ...args) {
			this.showMap[ref] = true; // 动态设置状态变量
			this.$nextTick(() => {
				if (!this.$refs[ref]) return this.$message.warning(`${ref}组件未加载完毕，请稍后重试！`);
				// 项目详情特殊处理
				if (ref == 'ProjectDetail') {
					const pmid = args[0]?.pmid;
					this.$refs.ProjectDetail[pmid ? 'showDetailCom' : 'getDetailForm'](args[0]);
					return;
				}
				// 其他组件通用处理
				if (typeof this.$refs[ref].showDetailCom === 'function') {
					this.$refs[ref].showDetailCom(...args);
				} else if (typeof this.$refs[ref].openDetail === 'function') {
					this.$refs[ref].openDetail(...args);
				}
			});
		},
		// 通用打开清单
		openList(ref, ...args) {
			this.showMap[ref] = true; // 动态设置状态变量
			this.$nextTick(() => {
				if (!this.$refs[ref]) return this.$message.warning(`${ref}组件未加载完毕，请稍后重试！`);
				this.$refs[ref].queryTableData(...args);
			});
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			if (type == 'init') {
				this.isLoading = true;
			}
			const API = 'selectDeveloManagerpDashboard'; //接口
			this.$axios[API](JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						const DATA = res.data.data;
						// 顶部数据卡片
						this.dataInfoList.forEach(item => {
							item.data = DATA[item.name] || 0;
							if (item.name == 'monthlyPointsSum') {
								item.desc = `本月积分${DATA.monthlyPointsRank ? '第' + DATA.monthlyPointsRank : '未排'}名`;
							}
							if (item.name == 'yearlyPointsSum') {
								item.desc = `年累计积分${DATA.yearlyPointsRank ? '第' + DATA.yearlyPointsRank : '未排'}名`;
							}
						});

						// 主干各个表格
						this.tableList.forEach(item => {
							if (item.title == '我的开发任务') {
								item.subTitle = jointString(' / ', `${DATA.developTaskCount}项`, `${DATA.developTaskHours}小时`);
							} else if (item.title == '我延误的任务') {
								item.subTitle = jointString(' / ', `${DATA.overdueDevelopTaskCount}项`, `${DATA.overdueDevelopTaskHours}小时`);
							} else if (item.title == '部门延误的开发任务') {
								item.subTitle = jointString(' / ', `${DATA.overdueDevelopTaskCount}项`, `${DATA.overdueDevelopTaskHours}小时`);
							} else if (item.title == '部门延误的测试任务') {
								item.subTitle = jointString(' / ', `${DATA.overdueTestTaskCount}项`, `${DATA.overdueTestTaskHours}小时`);
							} else if (item.title.includes('人员负荷')) {
								item.data =
									DATA[item.id]?.map(row => {
										const newRow = row;
										row.developWeekWorkHoursVOS?.forEach(weekData => {
											newRow[weekData.week] = weekData.workHours;
										});
										return newRow;
									}) || [];
								return;
							}
							item.data = DATA[item.id] || [];
						});
						console.log('CARDS:', this.dataInfoList);
						console.log('TABLES:', this.tableList);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.isLoading = false;
						setLocalStorage(`dashboard_twidList` + this.userInfos?.adminUserVO?.phoneNo, this.twidList);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 通过id获取用户名
		getUserNameByUid(auid) {
			if (!auid) return '';
			return this.userList?.find(i => i.auid == auid)?.userName || '';
		},

		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			this.$axios
				.selectSalesmanByTwids(JSON.stringify({ twids: [], counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),

		dateFormat,
	},
};
</script>
<style lang="scss">
.DevelopManagerDashboard .table-card {
	padding: 5px 15px !important;
}
</style>
<style lang="scss" scoped>
.DevelopManagerDashboard {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	overflow: hidden;
	overflow-y: auto;

	font-size: 14px;
	color: #666;

	.dashboard-wrapper {
		width: 100%;
		height: calc(100vh - 138px);
		padding-bottom: 10px;
		position: relative;

		display: flex;
		flex-direction: column;

		.border-radius-8 {
			border-radius: 8px;
		}

		/* @use: https://cssgrid-generator.netlify.app/ */
		.content-wrapper {
			width: 100%;
			height: calc(100% - 80px);
			display: grid;
			grid-template-columns: repeat(16, 1fr);
			grid-template-rows: repeat(12, 1fr);
			grid-column-gap: 8px;
			grid-row-gap: 8px;
			.content-item-1 {
				grid-area: 1 / 1 / 10 / 5;
			}
			.content-item-2 {
				grid-area: 10 / 1 / 13 / 3;
			}
			.content-item-3 {
				grid-area: 10 / 3 / 13 / 5;
			}
			.content-item-4 {
				grid-area: 1 / 5 / 7 / 9;
			}
			.content-item-5 {
				grid-area: 7 / 5 / 10 / 9;
			}
			.content-item-6 {
				grid-area: 10 / 5 / 13 / 9;
			}
			.content-item-7 {
				grid-area: 1 / 9 / 4 / 17;
			}
			.content-item-8 {
				grid-area: 4 / 9 / 7 / 17;
			}
			.content-item-9 {
				grid-area: 7 / 9 / 13 / 17;
			}
		}
	}
}
</style>
