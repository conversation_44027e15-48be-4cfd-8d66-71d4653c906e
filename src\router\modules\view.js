/* 看板路由 */

const comprehensiveView = () => import('@/pages/viewManagement/comprehensiveView.vue'); //综合看板
const operateView = () => import('@/pages/viewManagement/operateView.vue'); //运行看板
const routers = [
	{
		//综合看板
		path: '/comprehensiveView',
		name: 'comprehensiveView',
		component: comprehensiveView,
		meta: {
			parentTitle: '成交管理',
			title: '综合看板',
			noCache: true,
			isView: true,
		},
	},
	{
		//运行看板
		path: '/operateView',
		name: 'operateView',
		component: operateView,
		meta: {
			parentTitle: '成交管理',
			title: '运行看板',
			noCache: true,
			isView: true,
		},
	},
];

export default routers;
