<template>
	<!-- 数据导入弹窗 -->
	<div :class="openMove ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<!-- 头部标题 -->
			<div class="detail-head">
				<span class="detail-title">数据导入</span>
				<div>
					<el-button type="text" class="el-icon-arrow-left" @click="closeMove">返回</el-button>
				</div>
			</div>

			<!-- 主体内容 -->
			<div class="detail-content import-content">
				<div class="flex-center flex-column p30 H100">
					<!-- 操作步骤进度条 -->
					<el-steps :active="importActive" finish-status="success" align-center class="H20 W90 mt10">
						<el-step title="上传文件" description="根据模板填写后上传"></el-step>
						<el-step title="预览数据" description="预览并检查数据"></el-step>
						<el-step title="执行导入" description="数据导入平台"></el-step>
						<el-step title="导入完成" description="完成数据批量导入"></el-step>
					</el-steps>
					<!-- 0.导入准备页面(下载模板/上传文件) -->
					<div v-if="importActive == 0" class="flex-column align-center H80 W70 mt30">
						<!-- 下载模板 -->
						<div class="flex-align-center border W100 bg-gray mb20">
							<img class="pl20 pr20 w-150" :src="yunDown" />
							<!-- 下载提示 -->
							<div class="flex-1 pl20 border-left bg-white">
								<p class="fs-16 color-555">填写导入信息</p>
								<p class="color-999">请按照数据模板的格式准备导入数据，模板中的表头名称不可更改，表头行不能删除。</p>
								<el-button type="text" @click="downloadTemplate(templateName)">下载模板</el-button>
							</div>
						</div>
						<!-- 上传文件 -->
						<div class="flex-align-center border W100 bg-gray mb20">
							<img class="pl20 pr20 w-150" :src="yunUpload" />
							<!-- 上传提示 -->
							<div class="flex-1 pl20 border-left bg-white">
								<p class="fs-16 color-555">上传填好的数据文件</p>
								<p class="color-999">文件后缀名必须为 xls 或 xlsx （即Excel格式），文件大小不大于10M。</p>
								<el-upload
									action=""
									accept=".xlsx,.xls"
									:http-request="importFile"
									:on-remove="handleRemove"
									:on-error="uploadFail"
									:file-list="excelFileList"
									:show-file-list="false"
								>
									<el-button type="text">
										<i v-show="excelFileList.length > 0" class="el-icon-link"></i>
										{{ btnUpload }}
									</el-button>
									<span v-show="excelFileList.length > 0" class="color-666"
										>({{ uploadSize }}KB)
										<i class="el-icon-close color-66 pointer ml10 fs-12" @click.stop="handleRemove"></i>
									</span>
								</el-upload>
							</div>
						</div>
						<!-- 下一步 -->
						<el-button :disabled="excelFileList.length == 0" class="w-120" type="primary" @click="nextImport()">下一步</el-button>
					</div>
					<!-- 1.导入预览页面(预览文件数据) -->
					<div v-else-if="importActive == 1" class="flex-column align-center H80 W100">
						<div>
							<el-button class="w-120" @click="preImport">重新上传</el-button>
							<el-button class="w-120" type="primary" @click="nextImport()">下一步</el-button>
						</div>
						<!-- 表格(分页) -->
						<div class="table-wrapper W100">
							<u-table
								class="table-main"
								:data="
									tableData.slice(
										(tablePageForm.currentPage - 1) * tablePageForm.pageSize,
										tablePageForm.currentPage * tablePageForm.pageSize,
									)
								"
								:height="500"
								:row-height="45"
								:total="tablePageForm.total"
								:page-size="tablePageForm.pageSize"
								:current-page="tablePageForm.currentPage"
								:page-sizes="tablePageForm.pageSizes"
								@handlePageSize="handlePageSize"
								pagination-show
								stripe
							>
								<u-table-column label="行号" width="60" type="index" align="center"> </u-table-column>
								<u-table-column
									v-for="item in tableColumn"
									:key="item.column"
									:prop="item.column"
									:label="item.name"
									show-overflow-tooltip
								>
									<template slot="header">
										<Tooltips :cont-str="item.name" />
									</template>
									<template slot-scope="scope">
										<Tooltips
											:cont-str="scope.row[item.column]"
											:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										/>
									</template>
								</u-table-column>
							</u-table>
						</div>
					</div>
					<!-- 2.导入等待页面(执行导入中) -->
					<div v-else-if="importActive == 2" class="text-center H80 W70 mt30">
						<!-- 导入进度条 -->
						<el-progress :percentage="ImportPercent" :stroke-width="15"></el-progress>
						<p class="fs-16 mt30">正在导入数据，请勿关闭或刷新页面</p>
					</div>
					<!-- 3.导入结果页面(导入成功/失败/完成) -->
					<div v-else-if="importActive == 3 || importActive == 4" class="flex-column align-center H80 W70 mt30">
						<!-- 导入失败 -->
						<div v-if="importErr" class="text-center">
							<p>
								<el-button type="danger" class="el-icon-close fs-48" circle></el-button>
							</p>
							<p>数据导入失败</p>
							<p v-show="importErrUrl" class="hover-green red" @click="downLoadErr">下载详情 查看原因</p>
							<el-button class="w-120" type="primary" @click="preImport">重新上传</el-button>
						</div>
						<!-- 导入成功 -->
						<div v-else class="text-center">
							<el-button type="success" class="el-icon-check fs-48" circle></el-button>
							<p>数据导入完成</p>
							<p>成功导入{{ dataName }}数据{{ tableData.length }}条</p>
							<el-button @click="closeMove" class="w-120" type="primary">返回</el-button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { debounce, downloadFile } from '@/util/common';
import * as XLSX from 'xlsx';
import yunDown from '@assets/img/yunDown.svg';
import yunUpload from '@assets/img/yunUpload.svg';
export default {
	name: 'ImportTable',
	components: {},
	props: {},
	data() {
		return {
			// 基本数据
			openMove: false, //控制弹窗显隐
			API: '', //导入接口
			templateName: '', //模板文件名称（下载模板用）
			dataName: '', //数据名（提示：成功导入xxx数据xxx条!）
			params: {}, // query 参数

			// 文件相关
			excelFileList: [],
			btnUpload: '上传文件', //按钮文本
			uploadSize: '', //文件大小

			//导入状态相关
			importActive: 0, //导入状态
			importErr: false, //导入失败
			ImportPercent: 0, //导入时百分比进度
			importErrUrl: '', //导入失败原因
			importInterval: null, //导入定时器

			//导入表格相关
			tableColumn: [], //表格列
			tableData: [], //表格数据
			tablePageForm: {
				total: 0,
				pageSize: 20,
				currentPage: 1,
				pageSizes: [20, 50, 100],
			},

			//下载/上传图片
			yunUpload,
			yunDown,
		};
	},
	created() {},
	watch: {},
	mounted() {},
	methods: {
		//显示弹窗
		openImport({ API, templateName, dataName, params, type }) {
			if (!API || !templateName || !dataName) {
				this.$message.warning('导入接口或模板信息缺失！');
				return;
			}
			// 直接下载模板
			if (type == 'download') {
				this.downloadTemplate(templateName);
				return;
			}

			this.API = API;
			this.templateName = templateName;
			this.dataName = dataName;
			this.params = params;
			this.openMove = true;
		},
		// 导入文件
		importFile(file) {
			const self = this;
			const excelfileExtend = '.xls,.xlsx'; //设置文件格式
			const fileExtend = file.file.name.substring(file.file.name.lastIndexOf('.')).toLowerCase();
			if (excelfileExtend.indexOf(fileExtend) <= -1) {
				this.$message.warning('上传文件只能是 xls/xlsx 格式');
				return false;
			}
			const isLt2M = file.file.size / 1024 / 1024 < 10;
			if (!isLt2M) {
				this.$message.warning('上传文件大小不能超过 10MB!');
				return;
			}
			this.btnUpload = file.file.name;
			this.uploadSize = (file.file.size / 1024).toFixed(2);
			this.excelFileList.push(file);
			const reader = new FileReader();
			FileReader.prototype.readAsBinaryString = function (f) {
				let binary = '';
				let wb; // 读取完成的数据
				let outdata; // 你需要的数据
				const reader = new FileReader();
				reader.onload = function () {
					// 读取成Uint8Array，再转换为Unicode编码（Unicode占两个字节）
					const bytes = new Uint8Array(reader.result);
					const length = bytes.byteLength;
					for (let i = 0; i < length; i++) {
						binary += String.fromCharCode(bytes[i]);
					}
					// 接下来就是xlsx了，具体可看api
					wb = XLSX.read(binary, {
						type: 'binary',
					});
					outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]], { header: 1 });
					// 自定义方法向父组件传递数据
					try {
						if (outdata[0].length > 0) {
							outdata[0].forEach((item, index) => {
								const strName = item.split('.')[0],
									strType = item.split('.')[1],
									columnName = outdata[1][index] || '';
								self.tableColumn.push({
									column: strName,
									type: strType,
									name: columnName,
								});
							});
						}
						self.tableData = [];
						outdata.splice(0, 2);
						outdata.forEach((row, rIndex) => {
							if (self.iqcType == 1) {
								if (typeof row[0] == 'string') {
									if (row[0].indexOf('string') !== -1 || row[0].indexOf('data') !== -1 || row[0].indexOf('int') !== -1) {
										if (outdata[rIndex].length > 0) {
											outdata[rIndex].forEach((item, index) => {
												const strName = item.split('.')[0],
													strType = item.split('.')[1],
													columnName = outdata[rIndex + 1][index] || '';
												self.importColumnTwo.push({
													column: strName,
													type: strType,
													name: columnName,
												});
											});
										}

										// 裁剪第二张表
										outdata.splice(rIndex, 2);

										// 第一张表数据
										const tableOne = outdata.slice(0, rIndex);
										// 第二张表数据
										const tableTwo = outdata.slice(rIndex);
										// 第一张表赋值
										tableOne.forEach(item => {
											if (item.length > 0) {
												const tempRow = {};
												for (let i = 0; i < self.tableColumn.length; i++) {
													const column = self.tableColumn[i].column,
														type = self.tableColumn[i].type;
													if (type == 'date') {
														item[i] = self.formatExcelDate(item[i], '/');
													}
													tempRow[column] = row[i] || row[i] == 0 ? row[i] : '';
												}

												self.tableData.push(tempRow);
											}
										});
										// 第二张表数据
										tableTwo.forEach(item => {
											if (item.length > 0) {
												const tempRow = {};
												for (let i = 0; i < self.importColumnTwo.length; i++) {
													const column = self.importColumnTwo[i].column,
														type = self.importColumnTwo[i].type;
													if (type == 'date') {
														item[i] = self.formatExcelDate(item[i], '/');
													}
													tempRow[column] = item[i] ? item[i] : '';
												}

												self.importTableTwo.push(tempRow);
											}
										});
										self.tablePageForm.total = tableTwo.length;
									}
								}
							} else {
								if (row.length > 0) {
									const tempRow = {};
									for (let i = 0; i < self.tableColumn.length; i++) {
										const column = self.tableColumn[i].column,
											type = self.tableColumn[i].type;
										if (type == 'date') {
											row[i] = self.formatExcelDate(row[i], '/');
										}
										tempRow[column] = row[i] ? row[i] : '';
									}
									self.tableData.push(tempRow);
								}
							}
						});

						self.tablePageForm.total = self.tableData.length;
					} catch (error) {
						console.log('error:' + error);
					}
				};
				reader.readAsArrayBuffer(f);
			};
			reader.readAsBinaryString(file.file);
		},
		// 重新上传（上一步）
		preImport() {
			this.importActive = 0;
			this.ImportPercent = 0;
			this.importErr = false;
			this.importErrUrl = '';
			this.tableColumn = [];
			this.tableData = [];
			this.handleRemove();
		},
		// 导入下一步
		nextImport(flag) {
			const self = this;
			if (self.importActive >= 3) {
				self.importActive = 0;
			} else if (self.importActive == 1) {
				const formData = new FormData();
				formData.append('file', self.excelFileList[0].file);
				self.importActive += 1;
				self.importInterval && clearInterval(self.importInterval);
				self.importInterval = setInterval(() => {
					if (self.ImportPercent >= 99) {
						self.importInterval && clearInterval(self.importInterval);
						self.importInterval = null;
					} else {
						self.ImportPercent += 1;
					}
				}, 100);
				setTimeout(() => {
					if (self.$axios[self.API]) {
						self.$axios[self.API](formData, { params: self.params })
							.then(res => {
								if (res.data.success) {
									self.importInterval && clearInterval(self.importInterval);
									self.importInterval = 100;
									self.importErr = false;
									// self.importActive += 1;
									self.importActive += 2;
								} else {
									self.importErr = true;
									self.importActive += 1;
									if (res.data.data) {
										self.importErrUrl = res.data.data;
									} else {
										self.importErrUrl = '';
										self.$message.warning(res.data.message);
									}
								}
							})
							.catch(error => {
								self.importErr = true;
								self.importActive += 1;
								self.importErrUrl = '';
								console.log('importMaterial |' + error);
							});
					}
				}, 500);
			} else {
				this.importActive += 1;
				if (flag) {
					this.importErr = true;
				}
			}
		},
		// 关闭滑窗（清除变量）
		closeMove() {
			this.openMove = false;
			this.importActive = 0;
			this.ImportPercent = 0;
			this.importErr = false;
			this.importErrUrl = '';
			this.tableColumn = [];
			this.tableData = [];
			this.handleRemove();
			this.$emit('refresh', true);
		},
		//下载导入模板
		downloadTemplate: debounce(function (templateName = this.templateName) {
			const SERVER_URL = process.env.NODE_ENV == 'production' ? '' : process.env.API_HOST; // 生产环境为当前服务器地址，开发环境为当前配置的地址
			downloadFile(SERVER_URL, templateName, 'xlsx', '_self');
		}),
		//文件移除
		handleRemove() {
			this.excelFileList = [];
			this.uploadSize = 0;
			this.btnUpload = '上传文件';
		},
		// 分页控制
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
		},
		// 下载错误文件
		downLoadErr() {
			if (this.importErrUrl) window.open(this.importErrUrl, '_self');
		},
		// 上传失败
		uploadFail() {
			this.btnUpload = '上传文件';
			this.excelFileList = [];
		},
		// 日期格式转换
		formatExcelDate(numb, format) {
			if (typeof numb === 'number') {
				const time = new Date((numb - 1) * 24 * 3600000 + 1);
				time.setYear(time.getFullYear() - 70);
				time.setHours(time.getHours() - 8);
				const year = time.getFullYear() + '';
				const month = time.getMonth() + 1 + '';
				const date = time.getDate() - 1 + '';
				if (format && format.length === 1) {
					return year + format + month + format + date;
				}
				return year + (month < 10 ? '0' + month : month) + (date < 10 ? '0' + date : date);
			} else {
				return '';
			}
		},
	},
};
</script>
<style lang="scss" scoped>
.import-content {
	color: #666;
	font-size: 14px;
	.table-main {
		height: calc(65vh - 60px) !important;
	}
}
</style>
