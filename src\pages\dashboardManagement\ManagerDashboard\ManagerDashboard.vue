<template>
	<div class="ManagerDashboard" v-loading.lock="isLoading" element-loading-text="正在加载中，请勿中途退出...">
		<!-- ---------------报表组件----------------- -->
		<!-- 询盘清单 -->
		<!-- userInfos?.adminUserVO.phoneNo -->

		<InquriyList
			v-if="showMap.InquriyList"
			ref="InquriyList"
			titleName="本月预计成交"
			:requestType="1"
			:auid="''"
			:twidList="searchForm.twidList"
			:channelName="[]"
			api="selectMonthData"
			parentComName="业务河流"
			@refresh="queryTableData(1)"
		/>
		<!-- 询盘趋势 -->
		<InquiryTrend v-if="showMap.InquiryTrend" isMoveCom ref="InquiryTrend" @closeMove="queryTableData(1)" />
		<!-- 合同表格 -->
		<ContractTable
			v-if="showMap.ContractTable"
			isMoveCom
			ref="ContractTable"
			@openInquiryDetail="openInquiryDetail"
			@openContract="openContractDetail"
			@getTableOptions="tableOptions = $event"
			@openExport="openExport"
		/>

		<!-- 交付管理 -->
		<DeliveryManagement v-if="showMap.DeliveryManagement" isMoveCom ref="DeliveryManagement" @closeMove="queryTableData(1)" />
		<!-- 交付详情 -->
		<DeliveryDetail
			v-if="showMap.DeliveryDetail"
			ref="DeliveryDetail"
			:userList="userList"
			@close="
				queryTableData(1);
				$refs?.DeliveryOverview?.queryTableData('init');
			"
		/>
		<!-- 交付项目总览 -->
		<DeliveryOverview
			v-if="showMap.DeliveryOverview"
			isMoveCom
			ref="DeliveryOverview"
			@openDetail="openDeliveryDetail"
			@getTableOptions="tableOptions = $event"
			@closeMove="queryTableData(1)"
		/>
		<!-- 回款与开票 -->
		<PaybackManagement v-if="showMap.PaybackManagement" isMoveCom ref="PaybackManagement" @closeMove="queryTableData(1)" />
		<!-- 客户健康度管理 -->
		<ActivityRating v-if="showMap.ActivityRating" isMoveCom ref="ActivityRating" @closeMove="queryTableData(1)" />
		<!-- 到期管理 -->
		<ExpirationManagement
			v-if="showMap.ExpirationManagement"
			isMoveCom
			ref="ExpirationManagement"
			@closeMove="queryTableData(1)"
		/>
		<!-- 出差审批 -->
		<TravelApproval v-if="showMap.TravelApproval" isMoveCom ref="TravelApproval" @closeMove="queryTableData(1)" />
		<!-- 积分明细（质量评价） -->
		<IndividualPoints v-if="showMap.IndividualPoints" isMoveCom ref="IndividualPoints" @closeMove="queryTableData(1)" />
		<!-- 个人积分明细（质量评价） -->
		<PointsDetails v-if="showMap.PointsDetails" isMoveCom ref="PointsDetails" @closeMove="queryTableData(1)" />

		<!-- 团队详情 -->
		<TeamDetail v-if="showMap.TeamDetail" ref="TeamDetail" @refresh="queryTableData(1)" />

		<!-- 需求管理 -->
		<DemandManagement v-if="showMap.DemandManagement" isMoveCom ref="DemandManagement" @closeMove="queryTableData(1)" />
		<!-- 需求详情 -->
		<DemandManagementDetail
			v-if="showMap.DemandManagementDetail"
			ref="DemandManagementDetail"
			@close="queryTableData(1)"
			@openProject="openDetail('ProjectDetail', $event)"
		/>

		<!-- 项目详情 -->
		<ProjectDetail v-if="showMap.ProjectDetail" ref="ProjectDetail" @close="queryTableData(1)" />
		<!-- 任务详情 -->
		<TaskDetail v-if="showMap.TaskDetail" ref="TaskDetail" :tableOptions="tableOptions" @turnTask="queryTableData(1)" />

		<!--  --- 详情组件（注意：组件之间会混合调用重叠或用z-index控制） ---  -->
		<!-- 询盘详情 -->
		<InquiryDetail
			v-if="showMap.InquiryDetail"
			ref="InquiryDetail"
			:inquiryOptions="tableOptions"
			@openContract="openContractDetail"
			@close="queryTableData(1)"
		/>
		<!-- 合同详情 -->
		<ContractDetail
			v-if="showMap.ContractDetail"
			ref="ContractDetail"
			:contractOptions="tableOptions"
			@close="
				queryTableData(1);
				$refs?.ContractTable?.queryTableData('init');
			"
		/>
		<!-- --------------------- 通用组件 --------------------------- -->
		<!-- 数据导出弹窗 -->
		<ExportTable v-if="showMap.ExportTable" ref="ExportTable" />

		<BaseLayout :showHeader="false">
			<template #main>
				<div class="dashboard-wrapper">
					<!-- 顶部 -->
					<DashboardCard :dataInfoList="dataInfoList" @refresh="queryTableData('refresh')">
						<template #search-tool>
							<!-- 分销/代理 -->
							<el-select
								v-if="teamWorkList.length > 1"
								class="w-180"
								size="mini"
								v-model="searchForm.twidList"
								placeholder="分销/代理"
								filterable
								multiple
								collapse-tags
								clearable
								@change="queryTableData"
							>
								<el-option v-for="item in teamWorkList" :key="item.twid" :label="item.twName" :value="item.twid"> </el-option>
							</el-select>
							<div class="w-180 flex-align-center flex-justify-between">
								<span class="el-icon-caret-left fs-18 pointer color-666" @click="changeMonth(-1)"></span>
								<el-date-picker
									size="mini"
									class="w-150 mt2 mb2"
									v-model="searchForm.selectTime"
									type="month"
									value-format="timestamp"
									format="yyyy 年 MM 月"
									placeholder="请选择月份"
									:clearable="false"
									@change="queryTableData"
								>
								</el-date-picker>
								<span class="el-icon-caret-right fs-18 pointer color-666" @click="changeMonth(1)"></span>
							</div>
						</template>
					</DashboardCard>

					<!-- 主干 -->
					<div class="content-wrapper">
						<component
							ref="componentRef"
							:class="`content-item-${index + 1}`"
							v-for="(tableInfo, index) in tableList"
							:key="tableInfo.id"
							:is="'TableCom'"
							:tableInfo="tableInfo"
							@getTableOptions="tableOptions = $event"
							@openDetail="openDetail"
							@openList="openList"
							@openDialog="openDialog"
						/>
					</div>
				</div>
			</template>
		</BaseLayout>

		<AuditDialog ref="AuditDialog" @update="queryTableData(1)" />
		<PointsDialog ref="PointsDialog" :userList="userList" @update="queryTableData(1)" />
	</div>
</template>

<script>
import { debounce, dateFormat, checkRequired, deepClone, setLocalStorage, getLocalStorage, jointString } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
/* 通用 */
import DashboardCard from '../DashboardCard.vue'; //数据卡片
import TableCom from './ManagerTableCom.vue'; //表格
import ExportTable from '@/components/ExportTable'; //导出组件

/* 询盘相关组件 */
import InquriyList from '@/components/InquriyList.vue'; //询盘清单
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryTrend from '@/pages/inquiryManagement/inquiryAndDocumentary/inquiryTrend.vue'; //询盘趋势

/* 合同/交付相关组件 */
import ContractTable from '@/pages/deliveryManagement/contractManagement/ContractTable.vue'; //合同表格
import ContractDetail from '@/pages/deliveryManagement/contractManagement/contractDetailCom.vue'; //合同明细
import DeliveryManagement from '@/pages/deliveryManagement/deliveryManagement/deliveryManagement.vue'; //交付管理
import DeliveryDetail from '@/pages/deliveryManagement/deliveryManagement/components/deliveryDetailCom.vue'; //交付明细
import DeliveryOverview from '@/pages/deliveryManagement/deliveryManagement/deliveryOverview.vue'; //交付项目总览

/* 客户管理/团队/三色灯相关组件 */
import PaybackManagement from '@/pages/deliveryManagement/paybackManagement/paybackManagement.vue'; //回款与开票
import ExpirationManagement from '@/pages/customerManagement/expirationManagement.vue'; //到期管理
import ActivityRating from '@/pages/customerManagement/activityRating.vue'; //健康评分
import TeamDetail from '@/pages/teamManagement/teamDataMain/TeamDetail'; //团队
import TaskDetail from '@/pages/developmentManagement/developmentWorkManagement/components/taskDetailCom'; //任务明细
import ProjectDetail from '@/pages/developmentManagement/projectManagement/projectManagement/projectDetail'; //项目明细

/* 差旅相关组件 */
import TravelApproval from '@/pages/travelManagement/TravelApproval/TravelApproval.vue'; //出差审批
/* 个人积分（质量评价） */
import IndividualPoints from '@/pages/performanceManagement/individualPoints/IndividualPoints.vue'; //个人积分（质量评价）
import PointsDetails from '@/pages/performanceManagement/individualPoints/PointsDetails.vue'; //个人积分（质量评价）
/* 需求管理 */
import DemandManagement from '@/pages/developmentManagement/demandManagement/DemandManagement.vue'; //需求管理
import DemandManagementDetail from '@/pages/developmentManagement/demandManagement/DemandManagementDetail.vue'; //需求管理

/* 弹窗 */
import PointsDialog from './PointsDialog.vue'; //个人积分（质量评价）
import AuditDialog from '@/pages/performanceManagement/individualPoints/AuditDialog.vue'; //质量检查评价

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DashboardCard,
		TableCom,
		ExportTable,

		InquriyList,
		InquiryDetail,
		InquiryTrend,
		ContractTable,
		ContractDetail,

		DeliveryManagement,
		DeliveryDetail,
		DeliveryOverview,

		PaybackManagement,
		ExpirationManagement,
		ActivityRating,
		TeamDetail,

		TravelApproval,
		IndividualPoints,
		PointsDetails,

		TaskDetail,
		ProjectDetail,
		DemandManagement,
		DemandManagementDetail,

		PointsDialog,
		AuditDialog,
	},
	name: 'ManagerDashboard', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			isLoading: false,
			// 顶部数据卡片
			dataInfoList: [
				{ desc: '本月签单(万元)', name: 'monthlyContractAmount', data: 0, class: 'bg-blue' },
				{ desc: '本月收款(万元)', name: 'monthlyCollected', data: 0, class: 'bg-blue' },
				{ desc: '本月新增客数', name: 'monthlyNewClient', data: 0, class: 'bg-blue' },
				{ desc: '本月流失客数', name: 'monthlyClientLost', data: 0, class: 'bg-red' },
				{ desc: '年累计签单(万元)', name: 'yearlyContractAmount', data: 0, class: 'bg-orange' },
				{ desc: '年累计收款(万元)', name: 'yearlyCollected', data: 0, class: 'bg-orange' },
				{ desc: '当前留存客数', name: 'totalActiveClient', data: 0, class: 'bg-orange' },
			],
			// 主干各个表格
			tableList: [
				{
					id: 'abnormalWorks',
					title: '工作不规范',
					subTitle: '近7天',
					badgeNum: 0,
					data: [],
					button: '',
					tableColumn: [
						// { colName: '时间', colNo: 'updateTime', align: 'center', width: '50' },
						// { colName: '人员', colNo: 'uname', align: 'center', width: '50' },
						{ colName: '工作项', colNo: 'operations', align: 'left', width: '' },
						{ colName: '扣分', colNo: 'points', align: 'right', width: '50' },
					],
				},

				{
					id: 'firstDeliverStageNotCollected',
					title: '未清首期款',
					// subTitle: '金额前三',
					badgeNum: 0,
					count: 0,
					isDataList: true,
					data: [],
					button: '合同管理',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '' },
						{ colName: '阶段', colNo: 'stage', align: 'left', width: '70' },
						// { colName: '计划', colNo: 'complateMonth', align: 'center', width: '70' },
						{ colName: '金额(万)', colNo: 'amount', align: 'right', width: '80' },
						{ colName: '责任人', colNo: 'salesName', align: 'left', width: '60' },
					],
				},
				{
					id: 'expectedSigningList',
					title: '本月预计成交',
					// subTitle: '金额前五',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '询盘清单',
					tableColumn: [
						{ colName: '业务', colNo: 'salesmanName', align: 'left', width: '88' },
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '' },
						{ colName: '金额(万)', colNo: 'estimatedAmount', align: 'right', width: '65' },
					],
				},

				{
					id: 'teamClientWillLostWithin60Days',
					title: '60天内即将流失的客户',
					// subTitle: '工作中心数前10%',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '到期管理',
					tableColumn: [
						{ colName: '客户', colNo: 'teamName', align: 'left', width: '' },
						{ colName: '版本', colNo: 'version', align: 'left', width: '75' },
						// { colName: '数量', colNo: 'simLimit', align: 'right', width: '60' },
						{ colName: '到期日', colNo: 'validTo', align: 'right', width: '60' },
						{ colName: '责任人', colNo: 'salesName', align: 'left', width: '60' },
					],
				},

				{
					id: 'tripReimbursementList',
					title: '今日人员外出',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '出差审批',
					tableColumn: [
						{ colName: '姓名', colNo: 'tripUsers', align: 'left', width: '' },
						{ colName: '客户', colNo: 'tripClientName', align: 'left', width: '' },
						{ colName: '天数', colNo: 'tripDays', align: 'right', width: '60' },
					],
				},

				{
					id: 'incompleteConsultingList',
					title: '未完成的咨询',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '',
					tableColumn: [{ colName: '事项', colNo: 'incompleteConsulting', align: 'left', width: '' }],
				},
				{
					id: 'deliverStageExpired',
					title: '已逾期的交付项目阶段',
					// subTitle: '金额前三',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '交付管理',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '' },
						{ colName: '阶段', colNo: 'stage', align: 'left', width: '70' },
						{ colName: '到期日', colNo: 'complateMonth', align: 'center', width: '70' },
						{ colName: '金额(万)', colNo: 'amount', align: 'right', width: '70' },
						{ colName: '责任人', colNo: 'implementName', align: 'left', width: '60' },
					],
				},
				{
					id: 'yesterdaySignedContractList',
					title: '昨日签单',
					// subTitle: '金额前五',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '合同管理',
					tableColumn: [
						{ colName: '业务', colNo: 'salesman', align: 'left', width: '65' },
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '65' },
						{ colName: '区域', colNo: 'region', align: 'left', width: '' },
						{ colName: '金额(万)', colNo: 'dealAmount', align: 'right', width: '65' },
					],
				},

				{
					id: 'teamClientLostInThePast30Days',
					title: '近30天已流失的客户',
					// subTitle: '工作中心数前10%',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '到期管理',
					tableColumn: [
						{ colName: '客户', colNo: 'teamName', align: 'left', width: '' },
						{ colName: '版本', colNo: 'version', align: 'left', width: '75' },
						// { colName: '数量', colNo: 'simLimit', align: 'right', width: '60' },
						{ colName: '到期日', colNo: 'validTo', align: 'right', width: '60' },
						{ colName: '责任人', colNo: 'salesName', align: 'left', width: '60' },
					],
				},

				{
					id: 'teamClientHealthWarnList',
					title: '客户健康度预警',
					// subTitle: '工作中心数前10%',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '客户健康度管理',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '50' },
						{ colName: '版本', colNo: 'version', align: 'left', width: '50' },
						{ colName: '数量', colNo: 'qty', align: 'left', width: '50' },
						{ colName: '分数', colNo: 'scorse', align: 'left', width: '50' },
						{ colName: '责任人', colNo: 'salesName', align: 'left', width: '60' },
					],
				},

				{
					id: 'reviewWorks',
					title: '工作质量待评价',
					subTitle: '近3天',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '质量评价',
					tableColumn: [
						{ colName: '时间', colNo: 'updateTime', align: 'center', width: '88' },
						{ colName: '人员', colNo: 'uname', align: 'left', width: '70' },
						{ colName: '工作项', colNo: 'operation', align: 'left', width: '66' },
						{ colName: '', colNo: 'operation_details', align: 'left', width: '' },
						{ colName: '', colNo: 'operation_audit', align: 'right', width: '30' },
					],
				},
				{
					id: 'inquiryAndExpendVO',
					title: '昨日流量',
					subTitle: '',
					badgeNum: 0,
					isDataList: false,
					data: [],
					button: '询盘趋势',
					tableColumn: [
						{ colName: '来源', colNo: 'sourceName', align: 'left', width: '88' },
						{ colName: '昨日数', colNo: 'inquiryCountDay', align: 'right', width: '60' },
						{ colName: '日单价', colNo: 'unitPriceDay', align: 'right', width: '' },
						{ colName: '昨日消费', colNo: 'inquiryAmountDay', align: 'right', width: '' },
						{ colName: '本月数', colNo: 'inquiryCountMonth', align: 'right', width: '60' },
						{ colName: '月单价', colNo: 'unitPriceMonth', align: 'right', width: '' },
						{ colName: '本月消费', colNo: 'inquiryAmountMonth', align: 'right', width: '' },
					],
				},
				{
					id: 'yesterdayCollectionAmountList',
					title: '昨日收款',
					// subTitle: '金额前五',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '回款与开票',
					tableColumn: [
						{ colName: '业务', colNo: 'salesman', align: 'left', width: '65' },
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '65' },
						{ colName: '区域', colNo: 'region', align: 'left', width: '' },
						{ colName: '金额(万)', colNo: 'receiptsAmount', align: 'right', width: '65' },
					],
				},
				{
					id: 'incompleteDeliverySchedule',
					title: '未完成的项目',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '项目总览',
					tableColumn: [{ colName: '项目进度', colNo: 'deliveryScheduleStateVOS', align: 'left', width: '' }],
				},
				{
					id: 'selectDemandSuperVisionListVOS',
					title: '需求清单',
					badgeNum: 0,
					isDataList: false,
					data: [],
					button: '需求管理',
					tableColumn: [
						{ colName: '提交人', colNo: 'submissionName', align: 'left', width: '110' },
						{ colName: '提交时间', colNo: 'submissionTime', align: 'left', width: '100' },
						{ colName: '需求', colNo: 'demandDocumentName', align: 'left', width: '' },
						{ colName: '人天合计', colNo: 'manDayTotal', align: 'right', width: '80' },
						{ colName: '状态', colNo: 'status', align: 'left', width: '120' },
					],
				},
				{
					id: 'monthlyPointsRank',
					title: '本月积分排名',
					// subTitle: '前五名',
					badgeNum: 0,
					// isDataList: true,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '姓名', colNo: 'uname', align: 'left', width: '' },
						{ colName: '积分', colNo: 'totalPoints', align: 'right', width: '' },
					],
				},
				{
					id: 'monthlyDeductPointsRank',
					title: '本月扣分排名',
					// subTitle: '前五名',
					badgeNum: 0,
					// isDataList: true,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '姓名', colNo: 'uname', align: 'left', width: '' },
						{ colName: '扣分', colNo: 'totalPoints', align: 'right', width: '' },
					],
				},
			],
			titleName: '',

			oceanData: [], //公海数据
			tableOptions: [], //表格数据用于组件里上下页切换
			userList: [], //用户选项数据

			showMap: {
				ImportTable: false,
				ExportTable: false,

				InquriyList: false,
				InquiryDetail: false,
				InquiryTrend: false,

				DeliveryDetail: false,
				TeamDetail: false,
				ContractDetail: false,

				ContractTable: false,
				DeliveryManagement: false,
				DeliveryOverview: false,
				PaybackManagement: false,
				ExpirationManagement: false,
				ActivityRating: false,
				TravelApproval: false,
				IndividualPoints: false,
				PointsDetails: false,

				TaskDetail: false,
				ProjectDetail: false,

				DemandManagement: false,
				DemandManagementDetail: false,
			},

			searchForm: {
				channelName: [],
				selectTime: new Date().getTime(),
				twidList: [],
			},

			scoreConfigList: [], //积分配置
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'teamWorkList']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.searchForm = getLocalStorage(`dashboard_searchForm` + this.userInfos?.adminUserVO?.phoneNo) || this.searchForm;
		// 如果twidList不属于teamWorkList时则去掉
		if (!this.searchForm.twidList?.every(item => this.teamWorkList?.some(ptions => ptions.twid == item))) {
			this.searchForm.twidList = this.searchForm.twidList?.filter(item => this.teamWorkList?.some(ptions => ptions.twid == item));
		}
		this.queryUserByTwids();
		this.queryTableData('init');
	},
	activated() {
		this.queryTableData(1);
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {
		this.tableList = null;
		this.dataInfoList = null;
	}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 通用打开详情
		openDetail(ref, ...args) {
			this.showMap[ref] = true; // 动态设置状态变量
			this.$nextTick(() => {
				if (!this.$refs[ref]) return this.$message.warning(`${ref}组件未加载完毕，请稍后重试！`);
				// 项目详情特殊处理
				if (ref == 'ProjectDetail') {
					const pmid = args[0]?.pmid;
					this.$refs.ProjectDetail[pmid ? 'showDetailCom' : 'getDetailForm'](args[0]);
					return;
				}
				// 其他组件通用处理
				if (typeof this.$refs[ref].showDetailCom === 'function') {
					this.$refs[ref].showDetailCom(...args);
				} else if (typeof this.$refs[ref].openDetail === 'function') {
					this.$refs[ref].openDetail(...args);
				}
			});
		},
		// 通用打开清单
		openList(ref, ...args) {
			this.showMap[ref] = true; // 动态设置状态变量
			this.$nextTick(() => {
				if (!this.$refs[ref]) return this.$message.warning(`${ref}组件未加载完毕，请稍后重试！`);
				// 询盘清单特殊处理
				if (ref == 'InquriyList') {
					const DATA = {
						qualityList: [],
						stage: [0, 1, 2, 3, 4, 7, 8, 9],
						rePurchase: 2,
						startTime: this.$moment().startOf('month').valueOf(), //本月第一天时间戳
						endTime: this.$moment().endOf('month').valueOf(), //本月最后一天时间戳
						pageNum: 1,
						pageSize: 100,
					};
					return this.$refs[ref].openList(DATA);
				}

				this.$refs[ref].queryTableData(...args);
			});
		},
		// 打开询盘详情
		openInquiryDetail(...args) {
			this.openDetail('InquiryDetail', ...args);
		},
		// 打开合同详情
		openContractDetail(...args) {
			this.openDetail('ContractDetail', ...args);
		},
		// 打开交付详情
		openDeliveryDetail(...args) {
			this.openDetail('DeliveryDetail', ...args);
		},

		// 导出
		openExport(...args) {
			this.showMap.ExportTable = true;
			this.$nextTick(() => {
				this.$refs.ExportTable.openExport(...args);
			});
		},

		// 打开弹窗
		openDialog(type, row) {
			if (type == '评价') {
				this.$refs.AuditDialog.open(row);
			} else if (type == '积分') {
				this.$refs.PointsDialog.open();
			}
		},

		// 切换月份
		changeMonth(direction) {
			const currentDate = new Date(this.searchForm.selectTime);
			currentDate.setMonth(currentDate.getMonth() + direction);
			this.searchForm.selectTime = currentDate.getTime(); // 更新为新的时间戳
			this.queryTableData(); // 重新查询数据
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			if (type == 'init') {
				this.isLoading = true;
			}
			const API = 'fetchManagerDashboard'; //接口
			this.$axios[API](
				JSON.stringify({
					channelName: this.searchForm.channelName,
					twidList: this.searchForm.twidList,
					beginDate: this.$moment(this.searchForm.selectTime).startOf('month').valueOf(), //传入的月份第一天 00:00:00
					endDate: this.$moment(this.searchForm.selectTime).endOf('month').valueOf(), //传入的月份最后一天 23:59:59
				}),
			)
				.then(res => {
					if (res.data.success) {
						const DATA = res.data.data;
						// 顶部数据卡片
						this.dataInfoList?.forEach(item => {
							item.data = DATA[item.name] || 0;
						});

						// 主干各个表格
						this.tableList?.forEach(item => {
							if (item.isDataList) {
								// item.count = DATA[item.id]?.count || 0;
								// item.badgeNum = DATA[item.id]?.decimalBadge || 0;
								item.badgeNum = DATA[item.id]?.count || 0;
								item.data =
									DATA[item.id]?.dataList?.map(item => {
										// 特殊处理：获取昨日收款询盘地区等信息
										if (item?.deliverManagementDetailVO?.inquiryDocumentaryListVO) {
											item = {
												...item,
												...item.deliverManagementDetailVO,
											};
										}

										const deliverManagement = item?.deliverManagement || item?.deliverManagementDetailVO || {}; //阶段信息
										const inquiryDocumentary = item?.inquiryDocumentary || item?.inquiryDocumentaryListVO || {}; //询盘信息

										// 特殊处理：后台返回的责任人信息不同，id字符串或者对象
										const { salesman, implement } = deliverManagement || inquiryDocumentary || item;
										item.implementName = item.implementName || this.getUserNameByUid(implement) || ''; //根据id获取用户信息
										item.salesName = item.salesName || this.getUserNameByUid(salesman) || ''; //根据id获取用户信息

										return { ...deliverManagement, ...inquiryDocumentary, ...item };
									}) || [];
							} else {
								item.data = DATA[item.id] || [];
							}
						});
						console.log('DATA:', DATA);
						console.log('CARDS:', this.dataInfoList);
						console.log('TABLES:', this.tableList);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.isLoading = false;
						setLocalStorage(`dashboard_searchForm` + this.userInfos?.adminUserVO?.phoneNo, this.searchForm);
					} else {
						this.isLoading = false;
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.isLoading = false;
					console.log(`${API} |` + error);
				});
		}),

		// 通过id获取用户名
		getUserNameByUid(auid) {
			if (!auid) return '';
			return this.userList?.find(i => i.auid == auid)?.userName || '';
		},

		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			this.$axios
				.selectSalesmanByTwids(JSON.stringify({ twids: [], counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),

		dateFormat,
		jointString,
	},
};
</script>
<style lang="scss">
.ManagerDashboard .table-card {
	padding: 5px 15px !important;
}
</style>
<style lang="scss" scoped>
.ManagerDashboard {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	overflow: hidden;
	// overflow-y: auto;

	font-size: 14px;
	color: #666;

	.dashboard-wrapper {
		width: 100%;
		height: calc(100vh - 138px);
		padding-bottom: 10px;
		position: relative;

		display: flex;
		flex-direction: column;

		.border-radius-8 {
			border-radius: 8px;
		}

		/* @use: https://cssgrid-generator.netlify.app/ */
		.content-wrapper {
			width: 100%;
			height: calc(100% - 80px);
			display: grid;
			grid-template-columns: repeat(20, 1fr);
			grid-template-rows: repeat(12, 1fr);
			grid-column-gap: 8px;
			grid-row-gap: 8px;

			.content-item-1 {
				grid-area: 1 / 1 / 4 / 4;
			}
			.content-item-2 {
				grid-area: 1 / 4 / 4 / 9;
			}
			.content-item-3 {
				grid-area: 1 / 9 / 3 / 13;
			}
			.content-item-4 {
				grid-area: 1 / 13 / 4 / 17;
			}
			.content-item-5 {
				grid-area: 1 / 17 / 4 / 21;
			}
			.content-item-6 {
				grid-area: 4 / 1 / 7 / 4;
			}
			.content-item-7 {
				grid-area: 4 / 4 / 7 / 9;
			}
			.content-item-8 {
				grid-area: 3 / 9 / 5 / 13;
			}
			.content-item-9 {
				grid-area: 4 / 13 / 7 / 17;
			}
			.content-item-10 {
				grid-area: 4 / 17 / 7 / 21;
			}
			.content-item-11 {
				grid-area: 7 / 1 / 10 / 9;
			}
			.content-item-12 {
				grid-area: 13 / 1 / 10 / 9;
			}
			.content-item-13 {
				grid-area: 5 / 9 / 7 / 13;
			}
			.content-item-14 {
				grid-area: 7 / 9 / 10 / 17;
			}
			.content-item-15 {
				grid-area: 10 / 9 / 13 / 17;
			}
			.content-item-16 {
				grid-area: 7 / 17 / 10 / 21;
			}
			.content-item-17 {
				grid-area: 10 / 17 / 13 / 21;
			}
		}
	}
}
</style>
