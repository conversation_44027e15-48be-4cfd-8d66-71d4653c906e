1
<template>
	<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}报销单</span>
				<div>
					<el-button v-if="detailForm.btrid" type="text" class="el-icon-delete" @click="delDetail">删除</el-button>
					<!-- <el-button v-if="detailForm.btrid" type="text" class="icon-third_save" @click="updateDetail"> 保存</el-button> -->
					<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
				</div>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content scrolling-auto overflow-x-hidden">
				<p class="detail-content-title">出差事由 </p>
				<table class="base-table input-border-none" cellpadding="5" cellspacing="0">
					<tbody v-for="(tItem, index) in formList" :key="index">
						<!-- 标题 -->
						<tr>
							<th v-for="item in tItem" :key="item.prop" :colspan="item.colspan" :class="item.class">{{ item.name }} </th>
						</tr>
						<!-- 输入框 -->
						<tr>
							<td v-for="item in tItem" :key="item.prop" :colspan="item.colspan">
								<!-- 只读文本 -->
								<Tooltips v-if="item.type == 'text'" class="pl10" :cont-str="detailForm[item.prop]" />

								<!-- 差旅类别-->
								<div v-else-if="item.prop == 'tripType'" class="pl10">
									<Tooltips :cont-str="tripTypeDesc" :cont-width="100" />
								</div>
								<!-- 费用类型 -->
								<div v-else-if="item.prop == 'expenseParty'">
									<!-- v-if="isApprove || isApproveComplete" -->
									<Tooltips class="pl10" :cont-str="expensePartyMap[detailForm[item.prop]]" :cont-width="100" />
								</div>
								<!-- 日期 -->
								<div v-else-if="item.prop == 'tripBeginDate' || item.prop == 'tripEndDate'" class="pl10">
									<Tooltips :cont-str="dateFormat(detailForm[item.prop], 'line')" :cont-width="100" />
								</div>

								<!-- 默认输入框 -->
								<el-input
									v-else
									class="input-green"
									v-model="detailForm[item.prop]"
									:placeholder="item.name"
									clearable
									@change="updateDetail"
								></el-input>
							</td>
						</tr>
					</tbody>
				</table>
				<div class="bottom-button">
					<el-button v-show="titleName == '新建'" @click="saveDetail" type="primary">新 建</el-button>
				</div>
				<div v-show="titleName !== '新建'">
					<p class="detail-content-title">费用明细 </p>
					<ExpenseDetail ref="ExpenseDetail" :titleName="titleName" :detailForm="detailForm" />
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { resetValues, deepClone, debounce, dateFormat, jointString } from '@/util/tool';
import { bigMul, bigDiv } from '@/util/math';
import { mapGetters } from 'vuex';
import ExpenseDetail from './ExpenseDetail.vue'; //费用明细组件
import { tripTypeMap, expensePartyMap } from '@/assets/js/contractSource'; // 差旅类别 费用承担方
export default {
	name: 'ReimbursementForm',
	components: { ExpenseDetail },
	props: { warehouseOptions: Array },
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '',
			formList: [
				[
					{ name: '询盘编号', prop: 'idNumber', class: ' W10', type: 'text' },
					{ name: '客户', prop: 'tripClientName', class: ' W10', type: 'text' },
					{ name: '目的地', prop: 'tripDestination', class: '  W10', type: 'text' },
					{ name: '开始日期', prop: 'tripBeginDate', class: 'W10' },
					{ name: '结束日期', prop: 'tripEndDate', class: 'W10' },
					{ name: '实际出差天数', prop: 'tripDays', class: 'W10' },
					{ name: '差旅类型', prop: 'tripType', class: 'W10' },
					{ name: '承担方', prop: 'expenseParty', class: 'W10' },
				],
			],
			detailFormCopy: [],
			detailForm: {},
			formRules: {
				transferOrderNumber: [{ required: true, message: '请输入调拨单号！', trigger: 'blur' }],
				materialNo: [{ required: true, message: '请输入物料编码！', trigger: 'blur' }],
				materialName: [{ required: true, message: '请输入物料名称！', trigger: 'blur' }],
				// materialSpec: [{ required: true, message: '请输入物料规格！', trigger: 'blur' }],
				transferQty: [{ required: true, message: '请输入调拨数量！', trigger: 'blur' }],
				// unit: [{ required: true, message: '请输入单位！', trigger: 'blur' }],
			},

			tripTypeMap, // 差旅类别
			expensePartyMap, // 费用承担方
		};
	},
	created() {},
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 进入审核页面
		isApprove() {
			return this.titleName == '审核';
		},
		// 已通过审核
		isApproveComplete() {
			return this.detailForm.approveStatus == 3;
		},
		// 差旅类型配置
		tripTypeConfig() {
			return this.detailForm.selectTravelCategoryConfigurationVO;
		},
		// 差旅类型描述
		tripTypeDesc() {
			if (!this.detailForm.tripType) return '';
			if (this.detailForm.expenseParty !== 3) return this.tripTypeMap[this.detailForm.tripType];
			if (this.tripTypeConfig) {
				const { travelCategoryName, expense, dealDeductExpense, dealWaitAllocateRate } = this.tripTypeConfig; //差旅类别配置
				const expenseStr = expense ? `；${expense}元/人天` : '';
				const dealStr = dealDeductExpense ? `；成交扣减${dealDeductExpense}元/人天` : '';
				const rateStr = dealWaitAllocateRate ? `；分配比率${bigMul(dealWaitAllocateRate, 100, 2)}%` : '';
				return `${travelCategoryName}${expenseStr}${dealStr}${rateStr}`;
			}
			return this.tripTypeMap[this.detailForm.tripType];
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.detailForm = resetValues(this.detailForm); //重置对象
				this.$refs.ExpenseDetail.resetForm(); //清空子组件表单对象
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 删除
		async delDetail() {
			this.$confirm('此操作将永久删除该报销单, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'deleteBusinessTripReimbursement';
					try {
						const res = await this.$axios[API](JSON.stringify({ id: this.detailForm.btrid }));
						if (res.data.success) {
							this.$succ(res.data.message);
							this.showCom = false;
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 新建/保存上面的明细
		async saveDetail() {
			const API = this.titleName == '新建' ? 'addBusinessTripReimbursement' : 'updateBusinessTripReimbursement';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.detailForm }));
				if (res.data.success) {
					if (this.titleName == '新建') {
						this.titleName = '编辑'; //进入编辑
						this.detailForm = { ...this.detailForm, ...res.data.data };
						this.$nextTick(() => {
							this.$refs.ExpenseDetail.resetForm(); //清空子组件表单对象
							this.$refs.ExpenseDetail.queryDetailData(this.detailForm.btrid); //查询报销单明细
						});
					}

					this.detailFormCopy = deepClone(this.detailForm);
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 后台说修改用这个接口
		async updateDetail() {
			const API = 'updateTripsDaysByBtrid';
			try {
				const res = await this.$axios[API](JSON.stringify(this.detailForm));
				if (res.data.success) {
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		//显示弹窗
		showDetailCom: debounce(async function (type, rowData, item) {
			this.titleName = type;
			this.showCom = true;
			if (type == '新建') {
				this.detailForm = { ...this.detailForm, ...rowData };
			} else {
				// 编辑/审核
				this.detailForm = { ...this.detailForm, ...rowData, ...item };
				this.$nextTick(() => {
					this.$refs.ExpenseDetail.queryDetailData(this.detailForm.btrid, 'init'); //查询报销单明细
				});
				console.log(this.detailForm);
			}

			this.detailFormCopy = deepClone(this.detailForm);
		}, 500),

		//日期format
		dateFormat: dateFormat,
		jointString: jointString,
	},
};
</script>
