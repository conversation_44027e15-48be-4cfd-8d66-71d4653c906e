export * from './date.js';
export * from './math.js';
export * from './array.js';
export * from './common.js';
export * from './localStorage.js';
export * from './tableColumns.js';
/**
 * MD5加密
 * @param strMD
 * @returns {*}
 */
export function getMD5(strMD) {
	return setMD5(setMD5(strMD));
}

function setMD5(strMD) {
	const md5 = crypto.createHash('md5');
	md5.update(strMD);
	return md5.digest('hex');
}

/**
 * Base64编码
 * @param strBase
 * @returns {*}
 */
export function setBase(strBase) {
	const Base64 = require('js-base64').Base64;
	return Base64.encode(strBase);
}

/**
 * Base64解码
 * @param strBase
 * @returns {*}
 */
export function getBase(strBase) {
	const Base64 = require('js-base64').Base64;
	return Base64.decode(strBase);
}

// url解析函数
// ?id=111&name=567  => {id:111,name:567}
export function urlParse() {
	const obj = {};
	const reg = /[?&][^?&]+=[^?&%]+/g;
	// let url = window.location.search;//history模式
	const url = window.location.hash; //hash模式
	const arr = url.match(reg);
	if (arr === null) {
		return obj;
	}
	arr.forEach(item => {
		const tempArr = item.substring(1).split('=');
		const key = decodeURIComponent(tempArr[0]);
		const val = decodeURIComponent(tempArr[1]);
		obj[key] = val;
	});
	return obj;
}

/**
 * 获取地址栏参数
 * ?id=111&name=567
 */
export function urlParseName(name) {
	const reg = new RegExp('(^|\\?|&)' + name + '=([^&]*)(\\s|&|$)', 'i');
	if (reg.test(window.location.href)) {
		return unescape(RegExp.$2.replace(/\+/g, ' '));
	}
	return null;
}

/* Echarts图表单位自适应 */
export const fitChartSize = (size, defalteWidth = 1920) => {
	const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
	if (!clientWidth) return size / 2;
	const scale = clientWidth / defalteWidth;
	return Number(((size / 2) * scale).toFixed(3));
};
export const fitChartSizeVh = (size, defalteHeight = 1976) => {
	const clientHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientheight;
	if (!clientHeight) return size / 2;
	const scale = clientHeight / defalteHeight;
	return Number(((size / 2) * scale).toFixed(3));
};

/**
 * 获取文本px宽度
 * @param font{String}: 字体样式
 **/
export function getPxWidth(text, font) {
	const canvas = document.createElement('canvas');
	const context = canvas.getContext('2d');
	if (font) {
		context.font = font;
	}
	const metrics = context.measureText(text);
	return parseInt(metrics.width);
}

/**
 * 计算给定字符串的字节长度。
 *
 * @param {string} str - 输入的字符串。
 * @return {number} 字符串的字节长度。
 */
export function getByteLength(str) {
	let byteLength = 0;
	for (let i = 0; i < str.length; i++) {
		const charCode = str.charCodeAt(i);
		if (charCode <= 0x007f) {
			byteLength += 1;
		} else if (charCode <= 0x07ff) {
			byteLength += 2;
		} else if (charCode <= 0xffff) {
			byteLength += 3;
		} else {
			byteLength += 4;
		}
	}
	return byteLength;
}

/**
 * 毫秒转换器 - 将毫秒转换为更易读的时间格式
 * @param {Number} ms - 毫秒数
 * @param {String} format - 格式化类型: 'short'(简短), 'full'(完整), 'auto'(自动)
 * @returns {String} 格式化后的时间字符串
 */
export function formatMilliseconds(ms, format = 'auto') {
	if (!ms || ms <= 0) return '0ms';

	const seconds = Math.floor(ms / 1000);
	const minutes = Math.floor(seconds / 60);
	const hours = Math.floor(minutes / 60);
	const days = Math.floor(hours / 24);

	const remainingHours = hours % 24;
	const remainingMinutes = minutes % 60;
	const remainingSeconds = seconds % 60;
	const remainingMs = ms % 1000;

	if (format === 'short') {
		// 简短格式: 只显示最大单位, 例如: "5d", "3h", "45m", "30s", "500ms"
		if (days > 0) return `${days}d`;
		if (hours > 0) return `${hours}h`;
		if (minutes > 0) return `${minutes}m`;
		if (seconds > 0) return `${seconds}s`;
		return `${ms}ms`;
	} else if (format === 'full') {
		// 完整格式: 显示所有单位, 例如: "5天3小时45分钟30秒500毫秒"
		let result = '';
		if (days > 0) result += `${days}天`;
		if (remainingHours > 0) result += `${remainingHours}小时`;
		if (remainingMinutes > 0) result += `${remainingMinutes}分钟`;
		if (remainingSeconds > 0) result += `${remainingSeconds}秒`;
		if (remainingMs > 0) result += `${remainingMs}毫秒`;
		return result;
	} else {
		// 自动格式: 根据时间长度自动选择合适的单位, 例如: "5天","3小时","45分钟","30秒","500毫秒"
		if (days > 0) {
			return `${days}天${remainingHours > 0 ? remainingHours + '小时' : ''}`;
		}
		if (hours > 0) {
			return `${hours}小时${remainingMinutes > 0 ? remainingMinutes + '分钟' : ''}`;
		}
		if (minutes > 0) {
			return `${minutes}分钟${remainingSeconds > 0 ? remainingSeconds + '秒' : ''}`;
		}
		if (seconds > 0) {
			return `${seconds}秒`;
		}
		return `${ms}毫秒`;
	}
}
