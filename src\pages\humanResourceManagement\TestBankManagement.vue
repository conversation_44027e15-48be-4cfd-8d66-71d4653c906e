<template>
	<div id="TestBankManagement">
		<BaseLayout>
			<template #header>
				<!-- 模糊查询 -->
				<el-input
					class="searchBox"
					size="small"
					v-model="searchForm.query"
					placeholder="项目名称"
					@input="queryTableData(1)"
					clearable
				></el-input>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<el-button type="text" class="icon-third-bt_newdoc" @click="openDialog('添加', null)">添加项目</el-button>
				</div>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar"></div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="['startTime', 'endTime', 'modifyTime'].includes(item.colNo)"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<el-button
								v-else-if="item.colNo == 'detail'"
								size="mini"
								type="text"
								class="el-icon-tickets"
								@click="openDraw(scope.row)"
							>
								预览题库
							</el-button>

							<div v-else-if="item.colNo == 'url'" class="flex-align-center">
								<a :href="API_HOST + scope.row.project" target="_blank">
									{{ encodeURI(API_HOST + scope.row.project) }}
								</a>
							</div>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<u-table-column label="" width="200" align="right">
						<template slot-scope="scope">
							<el-button type="text" class="el-icon-edit-outline" @click="openDialog('编辑', scope.row)"></el-button>
							<el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
							<el-button type="text" class="el-icon-share" @click="openQrCode(scope.row)"></el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>

		<!-- 题库设置弹窗 -->
		<el-dialog width="800px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">{{ dialogTitle }}项目题库</span>
			<el-form :model="editForm" :rules="formRules" label-width="120px" label-position="left" @submit.native.prevent>
				<!-- <div class="flex-align-center flex-justify-between">
					<el-form-item label="项目编号" prop="projectNo">
						<el-input v-model="editForm.projectNo" placeholder="项目编号" clearable></el-input>
					</el-form-item>
				<el-form-item label="项目名称" prop="project">
					<el-input v-model="editForm.project" placeholder="项目名称" clearable></el-input>
				</el-form-item>
				</div> -->
				<el-form-item label="项目名称" prop="project">
					<el-input v-model="editForm.project" placeholder="项目名称" clearable></el-input>
				</el-form-item>

				<el-form-item label="答题说明" prop="description">
					<el-input
						v-model="editForm.description"
						placeholder="如：树字MES【人力选拔】在线测试，包括成长思维能力9道题与逻辑思维能力4题，两个部分共13题，均为单项选择题，测试时长20分钟，每超时1分钟扣0.5分，每提前1分钟加0.5分。"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>

				<el-form-item label="结束语" prop="conclusion">
					<el-input
						v-model="editForm.conclusion"
						placeholder="如：你的成长思维能力和逻辑思维能力给我们留下了深刻的印象。"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>

				<el-form-item label="通知方式" prop="notice">
					<el-input
						v-model="editForm.notice"
						placeholder="如：我们将在1个工作日内与你联系，请保持电话畅通，谢谢！"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>

				<el-form-item label="试题(JSON)" prop="JSON">
					<div class="flex-column">
						<el-input
							v-model="editForm.JSON"
							placeholder="请确保输入内容是正确的JSON格式！"
							type="textarea"
							:autosize="{ minRows: 4, maxRows: 4 }"
						></el-input>
						<span v-if="editForm.JSON && !isJsonData" class="fs-12 red"> * 请确保输入内容是正确的JSON格式，否则无法生效!</span>
					</div>
				</el-form-item>

				<el-form-item label="" prop="pages">
					<div class="flex-align-center">
						JSON示例：
						<el-button type="text" class="el-icon-document-copy mr10" @click="copyToClipboard(JSON.stringify(Pages, null, 2))">
							复制JSON模板
						</el-button>
						<el-button v-show="isJsonData" type="text" class="el-icon-tickets" @click="openDraw(editForm)">
							预览JSON示例
						</el-button>
					</div>
					<div>JSON在线解析及格式化验证：<a href="https://www.json.cn/" target="_blank">https://www.json.cn/</a></div>
				</el-form-item>
			</el-form>
			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEdit">保 存</el-button>
			</span>
		</el-dialog>

		<!-- 分享弹窗 -->
		<el-dialog
			width="800px"
			:visible.sync="dialogQrCode"
			:close-on-click-modal="true"
			:append-to-body="true"
			@close="closeDialog"
		>
			<span slot="title">分享 “{{ editForm.project }}” 链接/二维码</span>
			<div class="QRCode-wrapper flex-center flex-column p30">
				<canvas class="w-300 h-300" id="QRCode"></canvas>
				<a :href="API_HOST + editForm.project" target="_blank">
					{{ encodeURI(API_HOST + editForm.project) }}
				</a>
			</div>
			<span slot="footer">
				<el-button @click="closeDialog">关 闭</el-button>
				<el-button type="primary" @click="copyToClipboard(encodeURI(API_HOST + editForm.project))">复 制</el-button>
			</span>
		</el-dialog>

		<!-- 题库预览抽屉 -->
		<el-drawer custom-class="json-preview" title="题库预览" size="100%" :visible.sync="drawerPage" @close="closeDraw">
			<div class="drawer-content flex-start">
				<div class="json-content overflow-y-auto W50">
					<!-- <pre>{{ editForm.JSON }}</pre> -->
					<pre>{{ JSON.stringify({ canidateItemVOS: pageInfo }, null, 2) }}</pre>
				</div>
				<div v-if="pageInfo.length > 0" class="preview-content p5 overflow-y-auto W50 border-left">
					<div class="m10" v-for="(aItem, aIndex) in pageInfo" :key="aIndex">
						<div class="project-name fs-16">
							<span class="fs-16 green bolder"> {{ aItem.projectItem }}</span>
							<span class="fs-12 color-666">
								<span>( 出题数：{{ aItem.questionNumber }} 题；</span>
								<span> 总分及格线：{{ aItem.passScore }} 分；</span>
								<span> 选项警示标记：{{ aItem.warnScore }} 分 )</span>
							</span>
						</div>
						<div v-for="(bItem, bIndex) in aItem.candidateQuestionVOS" :key="bIndex">
							<div class="question-name fs-14 mt5 mt5">{{ bIndex + 1 }}.{{ bItem.questionText }}</div>
							<span v-for="(cItem, cIndex) in bItem.questionOptionsVOS" :key="cIndex">
								<span class="question-title fs-12 m5">{{ cItem.optionText }}({{ cItem.score }})</span>
							</span>
						</div>
					</div>
				</div>
				<div v-else class="flex-center border-left"> 请确保输入内容是正确的JSON格式！ </div>
			</div>
		</el-drawer>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import QRCode from 'qrcode';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'TestBankManagement', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			dialogEdit: false, // 弹窗开关
			dialogQrCode: false, // 弹窗开关
			dialogTitle: '',
			drawerPage: false,
			pageInfo: [],
			// 表单
			editForm: {
				canidateItemVOS: [],
				conclusion: '',
				description: '',
				notice: '',
				project: '',
				projectNo: '',
				projectType: '',
				JSON: '',
				ciid: '',
			},
			formRules: {
				project: [{ required: true, message: '请输入内容', trigger: 'blur' }],
				// conclusion: [{ required: true, message: '请输入内容', trigger: 'blur' }],
				// notice: [{ required: true, message: '请输入内容', trigger: 'blur' }],
				JSON: [{ required: true, message: '请输入内容', trigger: 'blur' }],
			},

			//表格相关
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				// { colName: '项目编号', colNo: 'projectNo', align: 'left', width: '120' },
				{ colName: '项目名称', colNo: 'project', align: 'left', width: '200' },
				// { colName: '项目类别', colNo: 'projectType', align: 'left', width: '' },
				{ colName: '项目题库', colNo: 'detail', align: 'center', width: '120' },
				// { colName: '答题二维码', colNo: 'qrcode', align: 'left', width: '100' },
				{ colName: '答题链接', colNo: 'url', align: 'left', width: '' },
				// { colName: '最近修改时间', colNo: 'modifyTime', align: 'center', width: '' },
				// { colName: '修改人', colNo: 'userName', align: 'left', width: '' },
			],

			// 查询表单
			searchForm: {
				projectType: '',
				project: '',
				// 其他...
			},
			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）

		// pageInfo() {
		// 	if (this.editForm.JSON) {
		// 		return JSON.parse(this.editForm?.JSON)?.canidateItemVOS || [];
		// 	}
		// 	return [];
		// },
		isJsonData() {
			try {
				if (this.editForm.JSON) {
					return JSON.parse(this.editForm?.JSON)?.canidateItemVOS || false;
				}
				return false;
			} catch (error) {
				return false;
			}
		},
		API_HOST() {
			if (process.env.API_HOST == 'http://test.m.lightmes.cn/') {
				// 开发/测试环境
				return process.env.API_HOST + 'hr/#/?project=';
			} else {
				// 现网生产环境 process.env.NODE_ENV == 'production';
				return 'http://hr.lightmes.cn/' + 'hr/#/?project=';
			}
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开二维码弹窗
		openQrCode(row) {
			this.editForm = { ...this.editForm, ...row };
			this.dialogQrCode = true;
			this.$nextTick(() => {
				const canvas = document.getElementById('QRCode');
				const content = encodeURI(this.API_HOST + row.project);

				QRCode.toCanvas(canvas, content, error => {
					if (error) {
						console.log('二维码异常', error);
					}
				});
			});
		},
		// 删除行
		deleteRow(row) {
			this.$confirm('该数据将被删除，删除后数据不可恢复', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteCandidateQuestion(JSON.stringify({ project: row.project }))
						.then(res => {
							if (res.data.success) {
								this.queryTableData(1);
								this.$succ('删除成功！');
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteCandidateQuestion |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 打开编辑弹窗
		openDialog(type, row) {
			if (type == '编辑') {
				this.editForm.JSON = JSON.stringify({ canidateItemVOS: row.canidateItemVOS }, null, 2);
				this.editForm = { ...this.editForm, ...row };
			}
			this.dialogTitle = type;
			this.dialogEdit = true;
		},
		// 打开编辑弹窗
		closeDialog() {
			this.editForm = _.resetValues(this.editForm);
			this.dialogEdit = false;
			this.dialogQrCode = false;
		},

		// 打开题库抽屉
		openDraw(row) {
			try {
				const str = row?.JSON || JSON.stringify({ canidateItemVOS: row.canidateItemVOS }, null, 2);
				this.pageInfo = JSON.parse(str)?.canidateItemVOS || [];
				this.drawerPage = true;
			} catch (error) {
				this.$message.warning('请确保输入内容是正确的JSON格式！', error);
				console.error(error);
			}
		},
		// 关闭题库抽屉
		closeDraw() {
			// this.editForm = _.resetValues(this.editForm);
			this.drawerPage = false;
		},

		// 复制JSON
		copyToClipboard: _.copyToClipboard,

		// 保存编辑
		async saveEdit() {
			if (_.checkRequired(this.editForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}

			try {
				this.editForm.canidateItemVOS = JSON.parse(this.editForm.JSON)?.canidateItemVOS || [];
			} catch (error) {
				this.$message.warning('请确保输入内容是正确的JSON格式！', error);
				return;
			}

			const API = this.editForm.ciid ? 'updateCandidateQuestion' : 'addCandidateQuestion';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.editForm }));
				if (res.data.success) {
					this.dialogEdit = false;
					this.queryTableData();
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectCandidateQuestion'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.dateSelectObj,
				...this.searchForm,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							this.$refs.uTableRef?.reloadData(this.tableData);
							this.$refs.uTableRef?.doLayout();
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
	},
};
</script>

<style lang="scss" scoped>
#TestBankManagement {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
<style lang="scss">
.json-preview {
	color: #555;
	.drawer-content {
		.json-content {
			// width: 100%;
			// height: 100vh;
			height: calc(100vh - 45px);
		}
		.preview-content {
			// height: 90vh;
			height: calc(100vh - 45px);
		}
	}
}
</style>
