<template>
	<div id="departmentManagement">
		<div class="left">
			<div class="ml20 flex-align-center">
				<div class="label-title">组织架构</div>
				<el-input
					class="searchBox ml10 mr10"
					size="small"
					clearable
					v-model="searchForm.departmentName"
					placeholder="部门"
					@input="queryTreeData(1)"
				></el-input>
				<el-button type="text" class="el-icon-plus" @click="openDialog('添加公司', null)">添加公司</el-button>
				<el-button type="text" class="el-icon-refresh" @click="queryTreeData">刷新</el-button>
			</div>
			<div class="treeContainer">
				<el-tree
					:data="treeData"
					node-key="adid"
					default-expand-all
					:highlight-current="true"
					:renderContent="renderContent"
					@node-click="clickNode"
				></el-tree>
			</div>
		</div>

		<div class="clearfix"></div>

		<!-- 左边树结构弹窗 -->
		<el-dialog :visible.sync="dialogEdit" width="500px" :close-on-click-modal="false" append-to-body @close="closeDialog('edit')">
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form label-width="100px" label-position="left" :model="editForm" :rules="formRules">
				<el-form-item v-show="editForm.parentDepartmentName" label="上级部门" prop="parentDepartmentName">
					<Tooltips :cont-str="editForm.parentDepartmentName" />
				</el-form-item>
				<el-form-item :label="editForm.parentDepartmentName ? '部门名称' : '公司名称'" prop="departmentName">
					<el-input
						:placeholder="'请输入' + (editForm.parentDepartmentName ? '部门名称' : '公司名称')"
						v-model="editForm.departmentName"
					></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button @click="closeDialog('edit')">取消</el-button>
				<el-button type="primary" @click="saveEdit(dialogTitle)">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import folderImg from '@/assets/img/folder.svg';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { Tooltip } from 'element-ui';
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'departmentManagement',
	data() {
		return {
			activeTab: 'departmentManagement',
			treeData: [], //部门树结构数据
			checkStrictly: false,
			searchForm: {
				adid: '',
				departmentName: '',
				departmentOrder: '',
				organization: '',
				parentAdid: '',
			},
			// 弹窗相关
			dialogTitle: '',
			dialogEdit: false,
			editForm: {
				adid: '',
				departmentName: '', //部门名称
				departmentOrder: '', //部门次序
				organization: '', //机构名称 这里当机构(公司)id用
				parentAdid: '', //后台部门id
			},
			formRules: {
				departmentName: [{ required: true, message: '请输入部门', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTreeData();
	},
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// JSX渲染树结构
		renderContent(h, { node, data, store }) {
			const { departmentName } = data;
			this.$set(data, 'buttonShow', false);
			return (
				<div class="custom-tree-node ">
					<div class="flex-align-center">
						<img class="folder-img" src={folderImg} />
						<span class="ml10 mr10">
							<div class="node-title">{departmentName}</div>
						</span>

						<div class="flex-align-center buttons">
							<el-button
								type="text"
								class="el-icon-plus"
								on-click={e => {
									e.stopPropagation();
									this.openDialog('添加部门', data);
								}}
							></el-button>
							<el-button
								type="text"
								class="el-icon-edit"
								on-click={e => {
									e.stopPropagation();
									this.openDialog('修改部门', data);
								}}
							></el-button>

							{/* <el-button
								type="text"
								class="el-icon-close"
								on-click={e => {
									e.stopPropagation();
									this.deleteNode('删除部门', data);
								}}
							></el-button> */}
						</div>
					</div>
				</div>
			);
		},
		// 查询数据
		queryTreeData: _.debounce(function (type) {
			this.treeData = [];
			this.$axios
				.selectAdminDepartmentList(JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						this.treeData = res.data.data?.reduce((acc, curr) => {
							const order = curr.departmentOrder;
							const path = curr.departmentPath.split('');
							let parent = acc;

							for (let i = 0; i < path.length; i++) {
								const node = parent.find(n => n.departmentOrder === order && n.departmentPath === path.slice(0, i + 1).join(''));
								if (node) {
									parent = node.children || (node.children = []);
								}
							}

							parent.push({
								...curr,
								children: [],
							});

							return acc;
						}, []);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAdminDepartmentList |' + error);
				});
		}),

		// 点击树节点
		clickNode(node) {
			if (!node) return;

			this.editForm = _.deepClone(node);
		},
		// 删除树节点
		deleteNode(type, data) {
			this.$confirm(`此操作将永久${type}, 是否继续?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					const str = JSON.stringify({ id: data.adid });
					this.$axios
						.deleteAdminDepartment(str)
						.then(res => {
							if (res.data.success) {
								this.$succ(res.data.message);
								this.queryTreeData();
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteAdminDepartment |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 添加/修改树结构数据
		openDialog(type, data) {
			if (type == '添加公司') {
				this.editForm = _.resetValues(this.editForm); //数据重置
				this.editForm.departmentOrder = this.treeData.length + 1;
			} else if (type == '添加部门') {
				this.editForm = { ...this.editForm, ...data };
				this.editForm.parentAdid = data.adid;
				this.editForm.parentDepartmentName = data.departmentName;
				this.editForm.organization = data.organization;
				this.editForm.adid = '';
				this.editForm.departmentName = '';
			} else if (type == '修改部门') {
				// this.editForm.parentAdid = data.adid;
				this.editForm = { ...this.editForm, ...data };
			}
			this.dialogTitle = type;
			this.dialogEdit = true;
		},

		/* 取消字段添加 */
		closeDialog() {
			this.dialogEdit = false;
			this.editForm = _.resetValues(this.editForm); //数据重置
		},

		// 保存树结构数据
		saveEdit() {
			const API = 'saveAdminDepartment';

			if (!this.editForm.departmentName) {
				this.$message.warning('保存失败，部门名称不能为空，请补充或完善部门名称');
				return;
			}
			if (this.dialogTitle == '添加公司') {
				// 添加公司
				this.editForm.organization = this.editForm.departmentName;
			}
			this.$axios[API](JSON.stringify({ ...this.editForm }))
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功');
						this.queryTreeData();
						this.closeDialog();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
	},
};
</script>

<style lang="scss" scoped>
#departmentManagement {
	width: 100%;
	position: relative;
	overflow: hidden;
	width: 100%;
	height: calc(100vh - 158px);
	overflow-y: auto;
	box-sizing: border-box;
	border: 1px solid #d7d7d7;
	background: #fff;
	border-radius: 10px;
}
</style>

<style lang="scss">
#departmentManagement {
	.treeContainer {
		height: 95%;
		overflow: auto;
		.folder-img {
			width: 18px;
		}
		.el-tree-node__content {
			height: max-content !important;
			height: 35px !important;
			// display: flex;
			// align-items: flex-start;

			.node-title {
				color: #555;
				// font-weight: 400;
				font-size: 14px;
			}
		}
	}
	.left {
		float: left;
		// width: 25%;
		width: 100%;
		box-sizing: border-box;
		height: 100%;
		padding: 10px;
	}

	// 鼠标移入移出时显隐按钮
	.el-tree {
		.el-tree-node__content {
			.buttons {
				display: none !important;
			}
			&:hover .buttons {
				display: flex !important;
			}
		}
	}
}
</style>
