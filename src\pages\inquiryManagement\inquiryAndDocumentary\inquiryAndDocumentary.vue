<template>
	<div class="inquiryAndDocumentary">
		<!-- 公海 -->
		<OceanTable
			v-if="showMap.OceanTable"
			ref="OceanTable"
			@openDetail="openDetail"
			@close="
				$refs.InquiryTable.queryTableData('init');
				isPublicInquiry = false;
			"
			@refresh="$refs.InquiryTable.queryOceanData('refresh')"
		/>
		<!-- 悬浮球 -->
		<FloatBall @openDetail="openDetail" />

		<!-- 动态询盘详情：聚心城使用聚心城询盘详情，其他使用默认询盘详情 -->
		<component
			v-if="showMap.InquiryDetail"
			:is="isJuxinCity ? 'InquiryDetail_JXC' : 'InquiryDetail'"
			ref="InquiryDetail"
			:isPublicInquiry="isPublicInquiry"
			:inquiryOptions="tableOptions"
			@openContract="openContract"
			@close="refreshTabPane(activeTab)"
		/>

		<!-- 合同详情 -->
		<ContractDetailCom
			v-if="showMap.ContractDetailCom"
			ref="ContractDetailCom"
			:contractOptions="tableOptions"
			@close="$refs.InquiryTable.queryTableData()"
		/>
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="inquiry"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twidList = $event.twidList;
				refreshTabPane(activeTab);
			"
		/>
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 导入弹窗 -->
		<ImportTable ref="ImportTable" @refresh="$refs.InquiryTable.queryTableData()" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="询盘" name="inquiryAndDocumentary">
				<InquiryTable
					ref="InquiryTable"
					:twidList="searchForm.twidList"
					:channelName="searchForm.channelName"
					@openDetail="openDetail"
					@getOceanData="$refs?.OceanTable?.queryTableData($event)"
					@getTableOptions="tableOptions = $event"
					@openOcean="openOcean"
					@openImport="$refs.ImportTable.openImport($event)"
					@openExport="$refs.ExportTable.openExport($event)"
				/>
			</el-tab-pane>

			<el-tab-pane label="询盘分配跟踪" name="allocationTracking">
				<allocation-tracking
					ref="allocationTracking"
					v-if="activeTab == 'allocationTracking'"
					:twidList="searchForm.twidList"
					:channelName="searchForm.channelName"
				></allocation-tracking>
			</el-tab-pane>

			<el-tab-pane label="业务河流" name="workFlow">
				<work-flow
					ref="workFlow"
					v-if="activeTab == 'workFlow'"
					:twidList="searchForm.twidList"
					:channelName="searchForm.channelName"
				></work-flow>
			</el-tab-pane>

			<el-tab-pane label="跟单日报" name="documentaryDaily">
				<documentary-daily
					ref="documentaryDaily"
					v-if="activeTab == 'documentaryDaily'"
					:twidList="searchForm.twidList"
					:channelName="searchForm.channelName"
					@openDetail="openDetail"
				></documentary-daily>
			</el-tab-pane>

			<el-tab-pane v-if="!isPartner" label="询盘趋势" name="inquiryTrend">
				<inquiry-Trend
					ref="inquiryTrend"
					v-if="activeTab == 'inquiryTrend'"
					:twidList="searchForm.twidList"
					:channelName="searchForm.channelName"
				></inquiry-Trend>
			</el-tab-pane>

			<el-tab-pane v-if="isSuperAdmin" label="行业熟练程度" name="industryProficiency">
				<IndustryProficiency
					ref="industryProficiency"
					v-if="activeTab == 'industryProficiency'"
					:twidList="searchForm.twidList"
					:channelName="searchForm.channelName"
					@openDetail="openDetail"
				/>
			</el-tab-pane>

			<el-tab-pane v-if="!isPartner" label="咨询评价" name="inquiryEvaluation">
				<InquiryEvaluation
					ref="inquiryEvaluation"
					v-if="activeTab == 'inquiryEvaluation'"
					:twidList="searchForm.twidList"
					:channelName="searchForm.channelName"
				/>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import ExportTable from '@/components/ExportTable'; //导出组件
import ImportTable from '@/components/ImportTable'; //数据导入
import InquiryTable from './inquiryTable.vue'; //询盘表格
import allocationTracking from './allocationTracking.vue'; //询盘分配跟踪
import WorkFlow from './workFlow.vue'; //业务河流
import DocumentaryDaily from './documentaryDaily.vue'; //跟单日报
import InquiryTrend from './inquiryTrend.vue'; //询盘趋势
import InquiryEvaluation from './inquiryEvaluation.vue'; //咨询评价
import IndustryProficiency from './industryProficiency.vue'; //行业熟练度
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryDetail_JXC from '@/components/InquiryDetail_JXC/InquiryDetail.vue'; //询盘详情（聚心城）
import ChannelSelect from '@/components/ChannelSelect.vue';
import FloatBall from './FloatBall.vue';
import ContractDetailCom from '@/pages/deliveryManagement/contractManagement/contractDetailCom.vue';
import OceanTable from './oceanTable.vue'; //公海列表

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		ImportTable,
		InquiryTable,
		allocationTracking,
		WorkFlow,
		DocumentaryDaily,
		InquiryTrend,
		InquiryEvaluation,
		IndustryProficiency,
		ChannelSelect,
		InquiryDetail,
		InquiryDetail_JXC,
		FloatBall,
		ContractDetailCom,
		OceanTable,
	},
	name: 'inquiryAndDocumentary',
	data() {
		return {
			activeTab: 'inquiryAndDocumentary',
			searchForm: {
				channelName: [],
				twidList: [],
			},
			tableOptions: [], //表格数据用于组件里上下页切换
			isPublicInquiry: false, //公海询盘
			showMap: {
				OceanTable: false,
				InquiryDetail: false,
				ContractDetailCom: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['isJuxinCity']), //是否聚心城人员

		// 合伙人不允许查看业务河流跟单日报等
		isPartner() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.roleName == '合伙人') || false;
		},
		// 标签是推广管理员
		isPromoter() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '推广管理员') || false;
		},
		// 标签是视频号管理
		isVideoManager() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '视频号管理') || false;
		},
		// 标签是流量专员
		isTrafficSpecialist() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '流量专员') || false;
		},

		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {
		this.isPublicInquiry = false;
	}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 打开公海
		openOcean(...args) {
			this.showMap.OceanTable = true;
			this.isPublicInquiry = true;
			this.$nextTick(() => {
				this.$refs.OceanTable.showDetailCom(...args);
			});
		},
		// 打开询盘详情
		openDetail(type, row, api) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom(type, row, api);
			});
		},
		// 打开询盘详情
		openContract(...args) {
			this.showMap.ContractDetailCom = true;
			this.$nextTick(() => {
				this.$refs.ContractDetailCom.showDetailCom(...args);
			});
		},

		// 刷新当前的Tab页面
		refreshTabPane(tab) {
			if (tab == 'inquiryAndDocumentary') {
				this.$refs.InquiryTable?.queryTableData('init');
				this.$refs.InquiryTable?.queryUserByTwids();
			} else {
				this.$refs[tab].queryTableData();
			}
		},
		//切换tab页
		changeTab(tab, event) {
			if (tab.name == 'inquiryAndDocumentary') {
				this.$refs.ChannelSelect.getLocalStorage();
			}
		},
	},
};
</script>
<style lang="scss" scoped>
.inquiryAndDocumentary {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
