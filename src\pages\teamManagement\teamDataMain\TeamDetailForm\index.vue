<template>
	<div class="TeamDetailForm input-border-none">
		<!-- ===================================== 基本信息 ===================================== -->
		<p class="detail-content-title">基本信息 </p>
		<BaseTableForm :detailForm="detailForm" :formList="baseList" @update="detailForm = $event" :disabled="!isSuperAdmin">
			<template #th-xxx></template>
			<!-- 分销/代理 -->
			<template #td-twid="{ item }">
				<el-select
					:disabled="!isTeamManager"
					v-model="detailForm[item.prop]"
					:placeholder="item.name"
					clearable
					filterable
					@change="queryUserByTwid"
				>
					<el-option v-for="i in teamWorkList" :key="i.twid" :label="i.twName" :value="i.twid"> </el-option>
				</el-select>
			</template>

			<!-- 手机号码 -->
			<template #td-phoneNo="{ item }">
				<el-input :disabled="!isTeamManager" v-model.trim="detailForm[item.prop]" :placeholder="item.name" clearable />
			</template>

			<!-- 业务顾问 -->
			<template #td-suid="{ item }">
				<el-select :disabled="!isTeamManager" v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
					<el-option v-for="i in salesmanList" :key="i.auid" :label="i.userName" :value="i.auid"> </el-option>
				</el-select>
			</template>

			<!-- 实施顾问 -->
			<template #td-cuid="{ item }">
				<el-select :disabled="!isTeamManager" v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
					<el-option v-for="i in implementList" :key="i.auid" :label="i.userName" :value="i.auid"> </el-option>
				</el-select>
			</template>
			<!-- 状态 -->
			<template #td-status="{ item }">
				<el-select :disabled="!isTeamManager" v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
					<el-option label="试用" :value="2"></el-option>
					<el-option label="正式运行" :value="1"></el-option>
				</el-select>
			</template>
			<!-- 使用版本 -->
			<template #td-version="{ item }">
				<el-select :disabled="!isSuperAdmin" v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
					<el-option label="OEE" :value="'2'"></el-option>
					<el-option label="标准版" :value="'1'"></el-option>
					<el-option label="微信版" :value="'4'"></el-option>
				</el-select>
			</template>
			<!-- WEB图标 -->
			<template #td-teamImage="{ item }">
				<el-upload
					:disabled="!isTeamManager"
					:http-request="uploadFile"
					:show-file-list="false"
					class="upload-dragger"
					accept="image/*"
					action=""
					drag
				>
					<FilePopover
						v-if="detailForm[item.prop]"
						class="inline-block max-w-150"
						:url="detailForm[item.prop]"
						:content="detailForm[item.prop]"
					/>
					<el-button v-else type="text" class="p0 W100" size="mini">{{ `仅支持小于2MB的图片(可拖拽)` }}</el-button>
				</el-upload>
			</template>
		</BaseTableForm>

		<!-- ===================================== 功能设置 ===================================== -->
		<p class="detail-content-title">功能设置 </p>
		<BaseTableForm :detailForm="detailForm" :formList="functionList" @update="detailForm = $event" :disabled="!isTeamManager">
			<!-- 人工效率统计 -->
			<template #td-artificialEfficiency="{ item }">
				<el-radio-group :disabled="!isSuperAdmin" v-model="detailForm[item.prop]">
					<el-radio :label="1">开启</el-radio>
					<el-radio :label="0" class="close-radio">关闭</el-radio>
				</el-radio-group>
			</template>
			<!-- 设备维保 -->
			<template #td-machineMaintenance="{ item }">
				<el-radio-group :disabled="!isSuperAdmin" v-model="detailForm[item.prop]">
					<el-radio :label="1">开启</el-radio>
					<el-radio :label="0" class="close-radio">关闭</el-radio>
				</el-radio-group>
			</template>

			<!-- 紧后工序生产工序任务 -->
			<template #td-supplierIf="{ item }">
				<el-radio-group :disabled="!isTeamManager" v-model="detailForm[item.prop]">
					<el-radio :label="1">生产</el-radio>
					<el-radio :label="0" class="close-radio">不生产</el-radio>
				</el-radio-group>
			</template>
			<!-- 工单导入模板 -->
			<template #td-workorderTemplateNo="{ item }">
				<el-select :disabled="!isTeamManager" v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
					<el-option label="捷阳模板" :value="'1001'"> </el-option>
					<el-option label="康诚模板" :value="'1002'"> </el-option>
				</el-select>
			</template>
			<!-- 计划提醒 -->
			<template #td-productionAdvice="{ item }">
				<el-radio-group :disabled="!isTeamManager" v-model="detailForm[item.prop]">
					<el-radio :label="1">提醒：精益排产</el-radio>
					<el-radio :label="2">提醒：甘特图排产</el-radio>
					<el-radio :label="0" class="close-radio">不提醒</el-radio>
				</el-radio-group>
			</template>

			<!-- 排产提醒 -->
			<template #td-productionReminds="{ item }">
				<el-checkbox-group :disabled="!isTeamManager" v-model="detailForm[item.prop]" class="pl10">
					<el-checkbox :label="1">前工序未排产</el-checkbox>
					<el-checkbox :label="2">排产下达通知</el-checkbox>
				</el-checkbox-group>
			</template>
			<!-- 配置临时账号 -->
			<template #td-temporaryAccount="{ item }">
				<el-radio-group :disabled="!isTeamManager" v-model="detailForm[item.prop]">
					<el-radio :label="1">允许</el-radio>
					<el-radio :label="0" class="close-radio">不允许</el-radio>
				</el-radio-group>
			</template>

			<!-- 上机打卡校验 -->
			<template #td-clockCheck="{ item }">
				<el-radio-group :disabled="!isTeamManager" v-model="detailForm[item.prop]">
					<el-radio :label="1">校验</el-radio>
					<el-radio :label="0" class="close-radio">不校验</el-radio>
				</el-radio-group>
			</template>
			<!-- 派工控制 注意：后台这里的逻辑取反了 0开启 1关闭 -->
			<template #td-generateProcedure="{ item }">
				<el-radio-group :disabled="!isTeamManager" v-model="detailForm[item.prop]">
					<el-radio :label="0">开启</el-radio>
					<el-radio :label="1" class="close-radio">关闭</el-radio>
				</el-radio-group>
			</template>
		</BaseTableForm>

		<!-- ===================================== 打印模板 ===================================== -->
		<p class="detail-content-title">打印模板 </p>
		<BaseTableForm :detailForm="detailForm" :formList="labelList" @update="detailForm = $event" :disabled="isTeamManager">
			<template #th-xxx></template>
			<!-- 生产批次标签 -->
			<template #td-templateNo="{ item }">
				<el-select :disabled="!isTeamManager" v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
					<el-option v-for="i in templateNos" :key="i.templateNo" :label="i.templateName" :value="i.templateNo"> </el-option>
				</el-select>
			</template>

			<!-- 排产标签 -->
			<template #td-workorderPrintNo="{ item }">
				<el-select :disabled="!isTeamManager" v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
					<el-option v-for="i in workorderPrintNos" :key="i.templateNo" :label="i.templateName" :value="i.templateNo">
					</el-option>
				</el-select>
			</template>
			<!-- 生产任务单 -->
			<template #td-taskPrintNo="{ item }">
				<el-select :disabled="!isTeamManager" v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
					<el-option v-for="i in taskPrintNos" :key="i.templateNo" :label="i.templateName" :value="i.templateNo"> </el-option>
				</el-select>
			</template>
			<!-- 任务标签 -->
			<template #td-taskTemplateNo="{ item }">
				<el-select :disabled="!isTeamManager" v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
					<el-option v-for="i in taskTemplateNos" :key="i.templateNo" :label="i.templateName" :value="i.templateNo"> </el-option>
				</el-select>
			</template>
		</BaseTableForm>
	</div>
</template>
<script>
import { resetValues, deepClone, checkRequired } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import BaseTableForm from '@/components/BaseTableForm';
import FilePopover from '@/components/FilePopover.vue';
import { baseList, functionList } from './formConfig.json';

export default {
	name: 'TeamDetailForm',
	props: {
		// 编辑权限(团队主数据不限制，我的客户等其他页面做对应的限制)
		noLimit: {
			type: Boolean,
			default: false,
		},
	},
	components: { BaseTableForm, FilePopover },
	data() {
		return {
			// 表单数据
			detailForm: {
				//明细详情
				annualFee: '',
				artificialEfficiency: '',
				auid: '',
				cuid: '',
				machineLabel: '',
				machineMaintenance: '',
				pageNum: '',
				pageSize: '',
				phoneNo: '',
				plcid: '',
				productLabel: '',
				productionAdvice: '',
				region: '',
				simLimit: '',
				status: '',
				suid: '',
				teamFullname: '',
				teamImage: '',
				teamIntroduction: '',
				teamName: '',
				teamPic: '',
				templateNo: '',
				tid: '',
				twid: '',
				userName: '',
				validFrom: '',
				validTo: '',
				version: '', //使用版本
				workorderTemplateNo: '', //工单导入模板
				workorderPrintNo: '', //排产打印模板
				taskPrintNo: '', //任务单打印模板
				useMaterial: 0, //投料追溯
				productionReminds: [],
				autoReporting: 0, //自动化配置 0关闭 1开启
				cableParam: 0, //线缆行业工序数量参数配置 0关闭 1开启
				procedureDate: 0, //工序完工日期配置 0关闭 1开启
				autoWorkorderNo: 0, //系统生成工单号 0否 1是
				taskTemplateNo: '', //任务标签模板
				quickApproval: 0, //app快捷审批 0否 1是
				materialCheck: 0, //材料名称校验 0否 1是
				temporaryAccount: 0, //配置临时账号 0不允许 1允许
				sopPassword: '', //SOP密码
				clockCheck: 0, //上机打卡校验 0不校验 1校验
				rejectRemark: 0, //驳回备注 0不开启 1开启
				batchManagement: 0, //批次管理 0不开启 1开启
				generateProcedure: 1, //派工控制  0开启 1关闭
				approvalType: 0, //人员审批 0关闭 1开启
				workstationLimit: 10, //ESOP工位数量 默认10
				fqcLimit: 0, //FQC控制 0关闭 1开启
			},
			detailFormCopy: {},

			// 人员列表
			userList: [],

			// 基本信息 - 从formConfig导入
			baseList,
			// 功能设置管理 - 从formConfig导入
			functionList,
			// 打印模板管理
			labelList: [
				[
					{ name: '排产标签', prop: 'workorderPrintNo', class: ' W10' },
					{ name: '生产批次标签', prop: 'templateNo', class: ' W10' },
					{ name: '生产任务单', prop: 'taskPrintNo', class: ' W10' },
					{ name: '任务标签', prop: 'taskTemplateNo', class: ' W10' },
				],
			],
			// 表单验证规则
			formRules: {
				twid: [{ required: true, message: '请选择分销/代理', trigger: 'blur' }],
				teamName: [{ required: true, message: '请输入团队简称', trigger: 'blur' }],
				teamFullname: [{ required: true, message: '请输入团队全称', trigger: 'blur' }],
				userName: [{ required: true, message: '请输入团队管理员', trigger: 'blur' }],
				phoneNo: [{ required: true, message: '请输入手机号码', trigger: 'blur' }],
				suid: [{ required: true, message: '请选择业务顾问', trigger: 'blur' }],
				cuid: [{ required: true, message: '请选择实施顾问', trigger: 'blur' }],
				version: [{ required: true, message: '请选择使用版本', trigger: 'blur' }],
				status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
				validFrom: [{ required: true, message: '请选择启用日期', trigger: 'blur' }],
				validTo: [{ required: true, message: '请选择到期日期', trigger: 'blur' }],
				simLimit: [{ required: true, message: '请输入工作中心数量', trigger: 'blur' }],
				workstationLimit: [{ required: true, message: '请输入ESOP工位数量', trigger: 'blur' }],
				annualFee: [{ required: true, message: '请输入年费', trigger: 'blur' }],
			},
			// 各种模板列表
			templateNos: [], //生产批次标签
			workorderPrintNos: [], //排产标签模板
			taskPrintNos: [], //生产任务单打印模板
			taskTemplateNos: [], //任务标签模板
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'teamWorkList']), //当前登录用户信息（含团队/菜单/权限等）
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},

		// 是超级管理 可编辑所有内容
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || this.noLimit || false;
		},
		// 团队管理员或业务顾问或实施顾问可编辑部分内容
		isTeamManager() {
			// 是否为团队管理员
			const isTeamManager = this.userInfos?.adminUserVO?.phoneNo == this.detailForm.phoneNo;

			// 是否为业务顾问或实施顾问
			const isConsultant =
				this.detailForm.cuid == this.userInfos?.adminUserVO?.auid || this.detailForm.suid == this.userInfos?.adminUserVO?.auid;

			return isTeamManager || isConsultant || this.isSuperAdmin || this.noLimit;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		this.detailForm = null;
		this.detailFormCopy = null;
	},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 添加/保存信息（父组件调用）
		saveDetail() {
			if (checkRequired(this.detailForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}

			const API = this.detailForm.tid ? 'updateTeam' : 'createTeam';
			this.$axios[API](JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						this.detailFormCopy = deepClone(this.detailForm);
						this.detailForm.tid && this.$emit('close');
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},
		//获取详情信息（父组件调用）
		queryDetail(rowData) {
			this.$axios
				.selectTeamByTid(JSON.stringify({ tid: rowData.tid }))
				.then(res => {
					if (res.data.success) {
						const productionReminds = res.data.data?.productionReminds || rowData?.productionReminds || [];
						this.detailForm = {
							...rowData,
							...res.data.data,
							productionReminds,
						};
						this.detailFormCopy = deepClone(this.detailForm);
						this.queryUserByTwid(this.detailForm.twid);
						this.getInitData();
						console.log('team：', this.detailForm);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTeamByTid |' + error);
				});
		},
		// 清空数据（父组件调用）
		clearData() {
			this.detailForm = resetValues(this.detailForm); //重置对象
			// 默认值
			this.detailForm.simLimit = 1;
			this.detailForm.workstationLimit = 10;
			this.detailFormCopy = deepClone(this.detailForm);
		},
		// 检查数据是否修改 （父组件调用）
		checkUpdate() {
			return JSON.stringify(this.detailForm) == JSON.stringify(this.detailFormCopy);
		},

		// 修改分销/代理 - 查询业务人员
		queryUserByTwid(twid) {
			this.userList = [];
			if (!twid) {
				return;
			}
			this.$axios
				.selectSalesmanByTwid(JSON.stringify({ twid, counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwid |' + error);
				});
		},

		// 上传图片
		uploadFile(item) {
			const isIMAGE = item.file.type === 'image/jpeg' || 'image/jpg' || 'image/png';
			const isLt2M = item.file.size / 1024 / 1024 < 2;

			if (!isIMAGE) {
				this.$message.warning('上传文件只能是图片格式!');
				return;
			}
			if (!isLt2M) {
				this.$message.warning('上传头像图片大小不能超过 2MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', item.file);
			formData.append('type', 2);
			// type 1-用户头像,2-团队头像,3-团队图片,4-设备图片,5-反馈图片,6-原料图片,7-模具图片,8-产品图片,9-产品工艺图纸图片
			this.$axios
				.uploadWebPic(formData)
				.then(res => {
					if (res.data.success) {
						this.detailForm.teamImage = res.data.data.path;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.$message.warning(error.message);
				});
		},

		// 初始化各种模板数据
		getInitData() {
			this.templateNos.length == 0 && this.queryTemplateNos();
			this.workorderPrintNos.length == 0 && this.queryWorkorderPrintNos();
			this.taskPrintNos.length == 0 && this.queryTaskPrintNos();
			this.taskTemplateNos.length == 0 && this.queryTaskTemplateNos();
		},
		/* ============== 查询各种模板 ============== */
		// 查询生产批次标签模板
		async queryTemplateNos() {
			const res = await this.$axios.selectAllPrintTemplateConfig(JSON.stringify({}));
			if (res.data.success) {
				this.templateNos = res.data.data;
			} else {
				this.templateNos = [];
				console.error('查询生产批次标签模板失败', res.data.message);
			}
		},
		// 查询排产标签模板
		async queryWorkorderPrintNos() {
			const res = await this.$axios.selectAllWorkorderTemplateConfig(JSON.stringify({}));
			if (res.data.success) {
				this.workorderPrintNos = res.data.data;
			} else {
				this.workorderPrintNos = [];
				console.error('查询排产标签模板失败', res.data.message);
			}
		},
		//查询生产任务单模板
		async queryTaskPrintNos() {
			const res = await this.$axios.selectTaskTemplateConfig(JSON.stringify({}));
			if (res.data.success) {
				this.taskPrintNos = res.data.data;
			} else {
				this.taskPrintNos = [];
				console.error('查询生产任务单模板失败', res.data.message);
			}
		},
		//查询任务标签模板
		async queryTaskTemplateNos() {
			const res = await this.$axios.selectTaskPrintConfig(JSON.stringify({}));
			if (res.data.success) {
				this.taskTemplateNos = res.data.data;
			} else {
				this.taskTemplateNos = [];
				console.error('查询任务标签模板失败', res.data.message);
			}
		},
	},
};
</script>

<style lang="scss">
.TeamDetailForm {
	// 关闭状态的单选框
	.close-radio.is-checked {
		.el-radio__input.is-checked .el-radio__inner {
			border-color: #909399;
			background: #909399;
		}
		.el-radio__label {
			color: #909399;
		}
	}
}
</style>
