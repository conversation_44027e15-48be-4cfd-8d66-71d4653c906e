<template>
	<div class="allocationTracking">
		<!-- 询盘列表 -->
		<InquriyList
			ref="InquriyList"
			:titleName="titleName"
			:twidList="twidList"
			:channelName="channelName"
			parentComName="询盘分配"
			@refresh="queryTableData(1)"
		/>
		<!-- 询盘列表（公海） -->
		<InquriyList_Sea ref="InquriyList_Sea" :twidList="twidList" :channelName="channelName" @close="queryTableData(1)" />
		<BaseLayout>
			<template #header>
				<span class="search-label">月份</span>
				<el-date-picker
					class="w-150"
					size="small"
					v-model="selectTime"
					:default-value="selectTime"
					type="month"
					value-format="timestamp"
					format="yyyy 年 MM 月"
					placeholder="请选择月份"
					:clearable="false"
					@change="queryTableData(1)"
				>
				</el-date-picker>
				<!-- :picker-options="{ disabledDate(time) { return time.getTime() > Date.now() - 8.64e7 * (new Date().getDate() - 1); }, }" -->

				<el-select
					class="w-200"
					size="small"
					v-model="searchForm.inquirySourceChannel"
					placeholder="来源"
					clearable
					filterable
					multiple
					collapse-tags
					@change="queryTableData(1)"
				>
					<el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>

				<SearchHistoryInput
					name="salesmanName"
					placeholder="姓名"
					v-model.trim="searchForm.userName"
					@input="queryTableData(1)"
				/>

				<el-radio-group v-model="searchForm.userType" @change="queryTableData(1)">
					<el-radio :label="1">咨询人员分配</el-radio>
					<el-radio :label="2">业务顾问分配</el-radio>
				</el-radio-group>

				<el-checkbox v-model="searchForm.firstAllocateView" :true-label="1" :false-label="0" @change="queryTableData(1)"
					>业务顾问首次分配视图
				</el-checkbox>

				<el-checkbox
					v-model="searchForm.isSelectUserAutoIntoHighSeaCase"
					:true-label="1"
					:false-label="0"
					@change="queryTableData(1)"
					>系统自动释放公海视图
				</el-checkbox>

				<el-checkbox
					v-model="searchForm.isSelectRePurchaseOrManuallyAddInquiry"
					:true-label="1"
					:false-label="0"
					@change="queryTableData(1)"
					>复购/手动添加询盘
				</el-checkbox>

				<el-tooltip effect="dark" placement="top-start">
					<div slot="content">
						<p>红色标记计数说明：</p>
						<p>咨询人员：分配的询盘中您还有未有完成咨询的询盘，即该询盘阶段为空/未联系上/没有咨询时间（已签单除外）。</p>
						<p
							>业务顾问：完成咨询的询盘中您还有未有跟单记录的询盘，即跟单次数为0（阶段：未联系上/已签单，质量：无效，复购
							以上除外）。</p
						>
					</div>
					<i type="text" class="el-icon-warning-outline pointer"></i>
				</el-tooltip>

				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-toolbar"> </div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					@sort-change="sortChange"
					show-header-overflow="title"
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column fixed label="序号" width="50" align="center" type="index"> </u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						:fixed="item.fixed"
						resizable
						sortable="custom"
					>
						<template slot-scope="scope">
							<div v-if="item.colNo != 'uaname' && item.colNo != 'sum' && scope.row[item.colNo]">
								<el-tooltip effect="dark" placement="top-start">
									<div slot="content">
										<div v-if="!scope.row[item.colNo].notFollowUp">
											{{
												`${searchForm.isSelectUserAutoIntoHighSeaCase ? '已释放' : '已分配'}的询盘数量：` +
												scope.row[item.colNo].allocated
											}}
										</div>
										<div v-else-if="searchForm.userType == 1">
											{{ '待咨询的询盘数量：' + scope.row[item.colNo].notFollowUp }}
										</div>
										<div v-else-if="searchForm.userType == 2">
											{{ '待跟进的询盘数量：' + scope.row[item.colNo].notFollowUp }}
										</div>
									</div>
									<el-badge
										class="mini-badge pointer pr10"
										:value="scope.row[item.colNo].notFollowUp"
										:max="99"
										:hidden="!scope.row[item.colNo].notFollowUp"
										@click.native="openList(Number(item.colNo), scope.row)"
									>
										<div>{{ scope.row[item.colNo].allocated || '' }}</div>
									</el-badge>
								</el-tooltip>
							</div>
							<!-- 合计 -->
							<Tooltips
								v-else-if="item.colNo == 'sum' && scope.row[item.colNo]"
								class="hover-green"
								@click.native="openList(Number(item.colNo), scope.row)"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<u-table-column label="" width="" align="center"> </u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { bigAdd, sortTableData, debounce, jointString } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import InquriyList from '@/components/InquriyList.vue';
import InquriyList_Sea from './InquriyList_Sea.vue';
import { sourceList } from '@/assets/js/inquirySource.js';

export default {
	name: 'allocationTracking',
	components: {
		InquriyList,
		InquriyList_Sea,
	},
	props: {
		twidList: Array,
		channelName: Array,
	},
	data() {
		return {
			sourceList,
			selectTime: new Date(),
			titleName: '',
			tabName: '', //用于切换不同的询盘清单查询方式
			//日期相关
			startTime: '',
			endTime: '',
			totalStatisticMap: [],
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			searchForm: {
				userName: '',
				userType: 1, //1 为咨询顾问，2 为业务顾问
				inquirySourceChannel: [],
				firstAllocateView: 0,
				isSelectUserAutoIntoHighSeaCase: 0,
				isSelectRePurchaseOrManuallyAddInquiry: 0,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）

		// 列数据
		tableColumn() {
			// 使用moment根据当前月份共多少天,然后按天数显示列
			const daysInMonth = this.$moment(this.selectTime).daysInMonth();
			const tableColumn = [
				{
					colName: this.searchForm.userType == 1 ? '咨询人员' : '业务顾问',
					colNo: 'uaname',
					align: 'left',
					width: 123,
					fixed: 'left',
				},
			];

			for (let i = 1; i <= daysInMonth; i++) {
				tableColumn.push({ colName: `${'' + i}`, colNo: `${'' + i}`, width: i > 9 ? 55 : 50, align: 'right' });
			}
			tableColumn.push({ colName: `${'合计'}`, colNo: `${'' + 32}`, width: 60, align: 'right', fixed: 'right' });
			return tableColumn;
		},

		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
	},
	// 监控data中的数据变化
	watch: {
		twidList() {
			this.queryTableData(1);
		},
		channelName() {
			this.queryTableData(1);
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData(1);
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 表尾合计（	前端计算）
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => Number(item[column.property]?.allocated));
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? bigAdd(prev, curr, 0) : prev;
				}, 0); //合计计算
				const summaryClass = 'hover-green pr10';
				means[columnIndex] =
					sum > 0 ? 
						<span
							class={summaryClass}
							on-click={e => {
								e.stopPropagation();
								this.openList(columnIndex - 1, null);
							}}
						>
							{sum}
						</span>
					 : 
						''
					;
			}
			return [means];
		},
		// 打开清单
		openList(index, row) {
			// 非(超级管理员/业务范围内/当前人员)不可查看
			if (
				!(
					this.isSuperAdmin ||
					this.userInfos?.adminUserVO.auid == row?.uaid ||
					this.userInfos?.adminUserVO?.salesmanUid?.includes(row?.uaid) ||
					this.userInfos?.adminUserVO?.salesmanUid?.split(',').find(i => i == '888')
				)
			) {
				return this.$message.warning(`抱歉，您没有查看${row?.uaname ? row.uaname : '合计'}询盘的权限！`);
			}

			//日期时间戳
			let startDate, endDate;
			if (index == 32) {
				// 一整月合计
				startDate = this.$moment(this.selectTime).startOf('month').valueOf(); //获取某月的第1天0点的时间戳
				endDate = this.$moment(this.selectTime).endOf('month').valueOf();
			} else {
				// 某月某天
				startDate = this.$moment(this.selectTime)
					.startOf('month')
					.add(index - 1, 'days')
					.valueOf(); //获取某月的第几天0点的时间戳
				endDate = this.$moment(startDate).add(1, 'day').startOf('day').valueOf() - 1;
			}

			//标题
			const name = row?.uaname ? row.uaname : '合计';
			const date = this.$moment(startDate).format(index == 32 ? 'yyyy-MM' : 'yyyy-MM-DD');
			this.titleName = `${name}(${date})`;

			// 根据不同的用户类型传查询的参数
			const userType = this.searchForm.userType;
			const auid = row?.uaid ? row.uaid : '';
			const consult = userType == 1 ? auid : '';
			const salesman = userType == 2 ? auid : '';
			const DATA = { consult, salesman, auid, startDate, endDate, userType };

			if (this.searchForm.isSelectUserAutoIntoHighSeaCase) {
				this.$refs.InquriyList_Sea.openList(DATA, '系统自动释放进入公海 ' + this.titleName);
			} else {
				this.$refs.InquriyList.openList(DATA);
			}
		},

		//自定义排序(新增/覆盖比较逻辑)
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop !== 'uaname') {
						if (!a?.allocated) return -1;
						if (!b?.allocated) return 1;
						return a.allocated - b.allocated;
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		// 查询数据
		queryTableData: debounce(function (type) {
			const str = JSON.stringify({
				searchYearMonth: this.$moment(this.selectTime).startOf('month').format('yyyy-MM-DD'),
				twidList: this.twidList,
				channelName: this.channelName,
				...this.searchForm,
				firstAllocateView: !!this.searchForm.firstAllocateView, //后台需要传布尔值
			});
			this.$axios
				.selectInquiryAllocationStatisticList(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data.filter(item => {
							if (item.uaname === '汇总' || item.uaname === '统计所有用户每天自动掉入公海询盘数量') {
								this.totalStatisticMap = item.statisticMap;
								return false;
							}

							item['32'] = { notFollowUp: 0, allocated: 0 };
							item?.statisticMap?.forEach((sItem, sIndex) => {
								item[sIndex + 1] = { notFollowUp: 0, allocated: 0, ...sItem };
								if (this.searchForm.isSelectUserAutoIntoHighSeaCase) {
									// 系统自动释放公海视图
									item[sIndex + 1].allocated = Number(sItem?.userMonthlyOneDayInquiryAutoIntoHighSeasCount) || 0;
									item['32'].allocated += Number(sItem?.userMonthlyOneDayInquiryAutoIntoHighSeasCount) || 0;
								} else {
									// 咨询/业务人员分配
									item['32'].notFollowUp += Number(sItem?.notFollowUp) || 0;
									item['32'].allocated += Number(sItem?.allocated) || 0;
								}
							});
							return true;
						});
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.tablePageForm.total = res.data.totalItems;

						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectInquiryAllocationStatisticList |' + error);
				});
		}),
		jointString, //拼接字符串
	},
};
</script>
<style lang="scss">
.allocationTracking {
	width: 100%;
	overflow: hidden;
	position: relative;
	.plTableBox .el-table__fixed-footer-wrapper tbody td {
		background-color: transparent !important;
	}
	.table-wrapper .table-main td .cell {
		overflow: visible;
	}
	.table-wrapper .table-main td {
		height: 45.28px !important; //高度被badge撑开，需要调整
	}
	.mini-badge {
		.el-badge__content {
			z-index: 88;
			zoom: 0.8;
			padding: 0 6px;
			right: 10px;
		}
	}
}
</style>
