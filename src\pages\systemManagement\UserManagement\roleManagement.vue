<template>
	<div id="roleManagement">
		<div class="left">
			<div class="ml20 flex-align-center">
				<div class="label-title">系统角色</div>
				<div class="ml-auto">
					<el-button v-if="showBtn" type="text" class="el-icon-plus" @click="openDialog('添加', null)">添加角色</el-button>
					<el-button type="text" class="el-icon-refresh" @click="queryRoleTreeData">刷新</el-button>
				</div>
			</div>
			<div class="treeContainer">
				<el-tree
					:data="roleTreeData"
					node-key="id"
					default-expand-all
					:highlight-current="true"
					:renderContent="renderContent"
					@node-click="clickNode"
				></el-tree>
			</div>
		</div>
		<div class="right">
			<div v-if="!rightTitle" class="flex-center H90">
				<div class="empty-content">
					<img src="@/assets/img/none.webp" />
					<p clss="empty-text">请选择左侧的角色</p>
				</div>
			</div>

			<div v-if="rightTitle" class="W100 H100 pt10 pb10">
				<div class="ml10 mb10 flex-align-center">
					<div class="label-title">{{ rightTitle }} - 功能设置</div>
					<el-button type="primary" size="mini" class="ml20" @click="saveEditTree('保存菜单')">保存</el-button>
				</div>
				<div class="treeContainer">
					<el-tree
						:data="menuTreeData"
						ref="menuTree"
						node-key="arcid"
						default-expand-all
						:highlight-current="true"
						:props="{ children: 'adminResourceVOS', label: 'resourceName' }"
						:check-strictly="checkStrictly"
						show-checkbox
						@check="editForm.arcid = $refs.menuTree.getHalfCheckedKeys().concat($refs.menuTree.getCheckedKeys())"
					></el-tree>
				</div>
			</div>
		</div>
		<div class="clearfix"></div>
		<!-- 左边树结构弹窗 -->
		<el-dialog :visible.sync="dialogEdit" width="500px" :close-on-click-modal="false" append-to-body @close="closeDialog('edit')">
			<el-row slot="title">{{ dialogTitle }}角色</el-row>
			<el-form label-width="100px" label-position="left" :model="editForm" :rules="formRules">
				<!-- 修改角色 -->
				<el-form-item label="角色名" prop="roleName">
					<el-input placeholder="请输入角色名" v-model="editForm.roleName"></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button @click="closeDialog('edit')">取消</el-button>
				<el-button type="primary" @click="saveEditTree(dialogTitle)">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import folderImg from '@/assets/img/folder.svg';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'roleManagement',
	data() {
		return {
			activeTab: 'roleManagement',
			roleTreeData: [], //角色树结构数据
			menuTreeData: [], //菜单树结构数据
			checkStrictly: false,
			rightTitle: '', //表格标题

			// 弹窗相关
			dialogTitle: '',
			dialogEdit: false,
			editForm: {
				roleName: '', //角色名称
				arid: '', //角色ID
				arcid: [], //角色菜单资源
			},
			formRules: {
				roleName: [{ required: true, message: '请输入角色', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）

		showBtn() {
			return this.roleTreeData?.length > 0;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {
		this.queryMenuTreeData();
		this.queryRoleTreeData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// JSX渲染树结构
		renderContent(h, { node, data, store }) {
			const { roleName } = data;
			return (
				<div class="custom-tree-node ">
					<div class="flex-align-center">
						<img class="folder-img" src={folderImg} />
						<span class="ml10 mr10">
							<div class="node-title">{roleName}</div>
						</span>
						<div>
							{/* <el-button
								type="text"
								class="el-icon-edit"
								on-click={e => {
									e.stopPropagation();
									this.openDialog('修改', data);
								}}
							></el-button>

							<el-button
								type="text"
								class="el-icon-close"
								on-click={e => {
									e.stopPropagation();
									this.deleteNode('删除', data);
								}}
							></el-button> */}
						</div>
					</div>
				</div>
			);
		},
		// 获取角色树结构
		queryRoleTreeData: _.debounce(function (type) {
			if (type == 'refresh') {
				this.rightTitle = '';
				this.$message.success('刷新成功，数据已更新！');
			}
			this.roleTreeData = [];
			this.$axios
				.selectAdminRoleAll(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.roleTreeData = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAdminRole |' + error);
				});
		}),
		// 获取菜单树结构
		queryMenuTreeData: _.debounce(function () {
			this.$axios
				.selectAdminResource(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.menuTreeData = res.data.data;
						// this.menuTreeData = this.userInfos.adminMenuAndButtonVO.menuList;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAdminResource |' + error);
				});
		}),
		// 点击树节点
		clickNode(node) {
			if (!node) return;
			const { roleName, arcid } = node;
			this.rightTitle = roleName; //右边标题
			this.editForm = _.deepClone(node);
			this.checkStrictly = true;
			this.$nextTick(() => {
				this.$refs.menuTree?.setCheckedKeys(arcid || []);
				this.checkStrictly = false;
			});
		},
		// 删除树节点
		deleteNode(type, data) {
			if (data.arid == 1) {
				this.$message.warning('超级管理员角色不允许删除!');
				return;
			}
			this.$confirm(`此操作将永久${type}该角色, 是否继续?`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					const str = JSON.stringify({ arid: data.arid });
					this.$axios
						.deleteAdminRole(str)
						.then(res => {
							if (res.data.success) {
								this.$succ(res.data.message);
								this.queryRoleTreeData();
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteAdminRole |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 添加/修改树结构数据
		openDialog(type, data) {
			if (!data) {
				this.editForm.roleName = '';
			} else {
				if (type == '添加') {
					this.editForm.roleName = '';
				} else {
					this.editForm = { ...this.editForm, ...data };
				}
			}
			this.dialogTitle = type;
			this.dialogEdit = true;
			// console.log(this.editForm);
		},

		/* 取消字段添加 */
		closeDialog() {
			this.dialogEdit = false;
			this.editForm = _.resetValues(this.editForm); //数据重置
		},

		// 保存树结构数据
		saveEditTree(type) {
			const API = type == '添加' ? 'insertAdminRole' : 'updateAdminRole';

			if (!this.editForm.roleName) {
				this.$message.warning('保存失败，角色名称不能为空，请补充或完善角色名称');
				return;
			}

			const str = JSON.stringify({
				roleName: this.editForm.roleName,
				arid: this.editForm.arid,
				arcid: this.editForm.arcid,
			});
			this.$axios[API](str)
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功');
						this.queryRoleTreeData();
						type !== '保存菜单' && this.closeDialog();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
	},
};
</script>

<style lang="scss" scoped>
#roleManagement {
	width: 100%;
	position: relative;
	overflow: hidden;
	width: 100%;
	height: calc(100vh - 158px);
	overflow-y: auto;
	box-sizing: border-box;
	border: 1px solid #d7d7d7;
	background: #fff;
	border-radius: 10px;
}
</style>

<style lang="scss">
#roleManagement {
	.treeContainer {
		height: 95%;
		overflow: auto;
		.folder-img {
			width: 18px;
		}
		.el-tree-node__content {
			height: max-content !important;
			height: 35px !important;
			// display: flex;
			// align-items: flex-start;

			.node-title {
				color: #555;
				// font-weight: 400;
				font-size: 14px;
			}
		}
	}
	.left {
		float: left;
		width: 25%;
		box-sizing: border-box;
		height: 100%;
		padding: 10px;
	}
	.right {
		float: left;
		width: 75%;
		box-sizing: border-box;
		height: 100%;
		border-left: 1px solid #d7d7d7;
		padding: 10px;
		.empty-content {
			text-align: center;
			.empty-text {
				font-size: 16px;
				color: #999;
			}
		}
	}
}
</style>
