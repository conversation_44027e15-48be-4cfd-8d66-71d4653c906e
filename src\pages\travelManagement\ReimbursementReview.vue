<template>
	<div class="ReimbursementReview">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 明细组件 -->
		<ReimbursementForm ref="ReimbursementForm" @close="queryTableData(1)" />
		<!-- 询盘详情 -->
		<InquiryDetail v-if="showMap.InquiryDetail" ref="InquiryDetail" :inquiryOptions="tableData" @close="queryTableData(1)" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="报销审核" name="ReimbursementReview">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">申请日期</span>
						<DateSelect
							@change="
								searchForm.applyDateBegin = $event.startTime;
								searchForm.applyDateEnd = $event.endTime;
								queryTableData(1);
							"
						/>
						<!-- 模糊查询 -->
						<SearchHistoryInput
							name="reimbursementNo"
							placeholder="报销单"
							v-model.trim="searchForm.reimbursementNo"
							@input="queryTableData(1)"
						/>
						<SearchHistoryInput
							name="reimbursementUser"
							placeholder="报销人"
							v-model.trim="searchForm.reimbursementUser"
							@input="queryTableData(1)"
						/>
						<SearchHistoryInput
							name="applyUserQuery"
							placeholder="申请人"
							v-model.trim="searchForm.applyUserQuery"
							@input="queryTableData(1)"
						/>

						<el-checkbox v-model="searchForm.onlyNotApproveItems" @change="queryTableData(1)">仅显示未审核记录</el-checkbox>
						<el-checkbox v-model="searchForm.onlyShowSettlementRequire" @change="queryTableData(1)">
							仅显示需要结算的数据
						</el-checkbox>
						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<ExportBtn @trigger="openExport" />
							<el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button>
							<el-button :disabled="!isBatch" type="text" class="el-icon-s-claim" @click="openSettlement(selectedData)">
								批量结算
							</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:dialog-data="tableColumn"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							:row-class-name="getRowColor"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							@header-dragend="headerDragend"
							@reset="updateColumn(tableColumnCopy)"
							@show-field="updateColumn"
							@selection-change="selectedData = $event"
							selectTrClass="selectTr"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column
								width="50"
								type="selection"
								align="center"
								:selectable="row => row.approveStatus == 3"
							></u-table-column>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="(item, index) in tableColumn.filter(item => item.state)"
								:key="item.colNo + index"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['reimbursementDate', 'applyDate'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 询盘编号 -->
									<Tooltips
										v-else-if="item.colNo == 'idNumber'"
										class="hover-green green"
										:class="getRowColor(scope)"
										@click.native="openInquiryDetail('修改', scope.row)"
										:cont-str="scope.row[item.colNo] || '未知'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>
									<!-- 金额 -->
									<Tooltips
										v-else-if="
											['expenseAmount', 'companyTripAmount', 'customerTripAmount', 'tripSubsidy', 'applicantTripAmount'].includes(
												item.colNo,
											) && scope.row[item.colNo]
										"
										:cont-str="scope.row[item.colNo]?.toFixed(2)"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 出差天数 -->
									<Tooltips
										v-else-if="item.colNo == 'tripDays' && scope.row[item.colNo]"
										:cont-str="scope.row[item.colNo] + '天'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 差旅类别-->
									<Tooltips
										v-else-if="item.colNo == 'tripType'"
										:cont-str="tripTypeMap[scope.row[item.colNo]]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 费用承担方 -->
									<Tooltips
										v-else-if="item.colNo == 'expenseParty'"
										:cont-str="expensePartyMap[scope.row.expenseParty]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 审核状态 0: 未提交，1已提交(待审核)，3 已审核，4，被退回修改 -->
									<Tooltips
										v-else-if="item.colNo == 'approveStatus'"
										class="hover-green"
										:class="['color-999', 'red', '', 'green', 'orange'][scope.row[item.colNo]]"
										:cont-str="
											jointString(
												' ',
												['未提交', '已提交(待审核)', '', '已审核', '被退回修改'][scope.row[item.colNo]],
												dateFormat(scope.row.approveDate, 'lineM'),
												scope.row.approveUserName,
												scope.row.approveMemo,
											)
										"
										:cont-width="scope.column.width || scope.column.realWidth"
										@click.native="openDetail('审核', scope.row, item)"
									/>
									<!-- 报销单号 -->
									<Tooltips
										v-else-if="item.colNo == 'reimbursementNo'"
										class="hover-green"
										:cont-str="scope.row[item.colNo]"
										:cont-width="scope.column.width || scope.column.realWidth"
										@click.native="openDetail('审核', scope.row, item)"
									/>
									<!-- 申请单号 -->
									<Tooltips
										v-else-if="item.colNo == 'applyNo'"
										class="hover-green"
										:cont-str="scope.row[item.colNo]"
										:cont-width="scope.column.width || scope.column.realWidth"
										@click.native="$refs.ApplyDialog.openDialog(scope.row)"
									/>
									<!-- 结算类型 -->
									<Tooltips
										v-else-if="item.colNo == 'settlementType'"
										:cont-str="settlementTypeMap[scope.row[item.colNo]]"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 结算状态 -->
									<Tooltips
										v-else-if="item.colNo == 'settlementStatus'"
										class="pointer"
										:class="['color-999', 'green'][scope.row[item.colNo]]"
										:cont-str="
											jointString(
												' / ',
												{ 0: '未结算', 1: '已结算' }[scope.row[item.colNo]],
												dateFormat(scope.row.settlementDate, 'lineM'),
											)
										"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										@click.native="
											scope.row.settlementStatus == 0 && scope.row.settlementType !== 0 ? openSettlement([scope.row]) : null
										"
									/>
									<!-- 成交待扣减结算 (差旅类型为申请人承担) -->
									<span v-else-if="item.colNo == 'inquiryPaymentStatus' && item.colName == '成交待扣减结算'">
										<el-button
											v-if="scope.row?.selectTravelCategoryConfigurationVO?.isDealDeduct && scope.row.inquiryPaymentStatus"
											type="text"
											size="mini"
											@click="openDeal(scope.row)"
										>
											结算
										</el-button>
										<span v-else>无需结算</span>
									</span>
									<!-- 询盘是否收款 -->
									<span v-else-if="item.colNo == 'inquiryPaymentStatus'">
										{{ scope.row.inquiryPaymentStatus == 1 ? '是' : '否' }}
									</span>
									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDialogRow(scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template>
              </u-table-column> -->
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>

		<!-- 报销单结算 -->
		<el-dialog width="400px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">报销单结算</span>
			<el-form :model="editForm" :rules="formRules" label-width="100px" label-position="left" @submit.native.prevent>
				<el-form-item label="结算日期" prop="settlementDate">
					<el-date-picker
						v-model="editForm.settlementDate"
						type="date"
						placeholder="结算日期"
						format="yyyy-MM-dd"
						value-format="timestamp"
					>
					</el-date-picker>
				</el-form-item>
				<!-- <el-form-item label="备注" prop="allocateMemo">
					<el-input
						v-model="editForm.allocateMemo"
						placeholder="请输入备注"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item> -->
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="openSettlement(editForm.rows, 'save')">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 出差申请弹窗 -->
		<ApplyDialog ref="ApplyDialog" />
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData, checkRequired, resetValues } from '@/util/tool'; //按需引入常用工具函数
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import ReimbursementForm from './components/ReimbursementForm.vue'; //明细组件
import ApplyDialog from './components/ApplyDialog.vue'; //出差申请弹窗
import ExportTable from '@/components/ExportTable'; //导出组件
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import { tripTypeMap, settlementTypeMap, expensePartyMap } from '@/assets/js/contractSource'; // 结算类型 差旅类别 费用承担方
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		ReimbursementForm,
		ApplyDialog,
		ExportTable,
		InquiryDetail,
		ExportBtn,
	},
	name: 'ReimbursementReview', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'ReimbursementReview', //激活tab页
			tripTypeMap, //差旅类别
			settlementTypeMap, // 结算类型
			expensePartyMap, // 费用承担方
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '报销单号', colNo: 'reimbursementNo', align: 'center', width: '120' },
				{ colName: '报销人', colNo: 'reimbursementUserName', align: 'left', width: '' },
				{ colName: '报销日期', colNo: 'reimbursementDate', align: 'center', width: '' },
				{ colName: '差旅类型', colNo: 'tripType', align: 'center', width: '' },
				{ colName: '承担方', colNo: 'expenseParty', align: 'left', width: '' },
				{ colName: '报销金额(元)', colNo: 'expenseAmount', align: 'right', width: '100' },
				{ colName: '公司承担差旅', colNo: 'companyTripAmount', align: 'right', width: '100' },
				{ colName: '客户承担差旅', colNo: 'customerTripAmount', align: 'right', width: '100' },
				{ colName: '差旅补助', colNo: 'tripSubsidy', align: 'right', width: '100' },
				{ colName: '申请人承担差旅', colNo: 'applicantTripAmount', align: 'right', width: '100' },
				{ colName: '客户', colNo: 'tripClientName', align: 'left', width: '' },
				{ colName: '询盘号', colNo: 'idNumber', align: 'left', width: '100' },
				{ colName: '审核人', colNo: 'approveUserName', align: 'left', width: '' },
				{ colName: '审核状态', colNo: 'approveStatus', align: 'left', width: '' },
				{ colName: '出差申请', colNo: 'applyNo', align: 'center', width: '' },
				{ colName: '申请人', colNo: 'applyUName', align: 'left', width: '' },
				{ colName: '结算类型', colNo: 'settlementType', align: 'center', width: '80' },
				{ colName: '结算状态', colNo: 'settlementStatus', align: 'left', width: '80' },
				{ colName: '询盘是否收款 ', colNo: 'inquiryPaymentStatus', align: 'center', width: '' },
				{ colName: '收款总金额', colNo: 'inquiryPaymentAmount', align: 'right', width: '' },
				{ colName: '成交待扣减结算', colNo: 'inquiryPaymentStatus', align: 'center', width: '' },
			],
			tableColumnCopy: [],

			// 查询表单
			searchForm: {
				applyDateBegin: '',
				applyDateEnd: '',
				onlyNotApproveItems: true,
				onlyShowSettlementRequire: false,
				reimbursementNo: '',
				reimbursementUser: '',
				// 其他...
			},
			showMap: {
				InquiryDetail: false,
			},

			// 弹窗相关
			dialogEdit: false,
			editForm: {
				btridList: [],
				settlementDate: '',
			},
			formRules: {
				settlementDate: [{ required: true, message: '请输入结算日期', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 是否批量操作
		isBatch() {
			return this.selectedData.length > 0;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.tableColumnCopy = deepClone(this.tableColumn);
		this.tableColumn = getColumn(this.tableColumn, this.$options.name);
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
		// this.changeTab();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开成交待扣减结算
		async openDeal(row) {
			this.$confirm('正在进行成交待扣减结算, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'bpuDealWaitDeductManualSettlement';
					try {
						const res = await this.$axios[API](JSON.stringify({ ...row }));
						if (res.data.success) {
							this.$succ(res.data.message);
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},

		// 打开结算
		async openSettlement(rows, type = 'edit') {
			if (type == 'edit') {
				this.editForm.rows = rows;
				this.dialogEdit = true;
				return;
			} else if (type == 'save' && checkRequired(this.editForm, this.formRules)) {
				return; // 验证
			}
			const API = 'updateBusinessTripReimbursementBatchSettlement';
			try {
				const res = await this.$axios[API](
					JSON.stringify({
						btridList: rows.map(item => item.btrid),
						settlementDate: this.editForm.settlementDate,
					}),
				);
				if (res.data.success) {
					this.queryTableData(1);
					this.closeDialog();
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		closeDialog() {
			this.dialogEdit = false;
			this.editForm = resetValues(this.editForm);
		},
		// 打开明细
		openDetail(type, row, item) {
			if (row.approveStatus == 0) {
				return this.$message.warning('当前未提交的报销单无法进行审核操作！');
			}
			this.$refs.ReimbursementForm.showDetailCom(type, row, item);
		},

		// 打开询盘详情
		openInquiryDetail(type, row, api) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom(type, row, api);
			});
		},
		// 表格文本高光显示
		getRowColor({ row }) {
			if (row.approveStatus == 0) {
				return 'color-999'; //未提交
			}
			// 标红处理：未联系上，阶段为空
			return row.approveStatus == 1 ? 'red' : ''; //未联系上 标红显示
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectBusinessTripReimbursementList'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data.map(item => {
							return { ...item.businessTripApplicationVO, ...item };
						});
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 展示字段
		updateColumn(data) {
			this.tableColumn = updateColumn(this.$options.name, data);
			this.sortChange(this.tableSort, true);
		},
		// 列宽拖动
		headerDragend(newWidth, oldWidth, column) {
			updateColumnWidth(this.$options.name, column.property, newWidth, this.tableColumn);
		},
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},

		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({ ...this.searchForm }), //接口参数
				API: 'exportTripReimbursement', //导出接口
				downloadData: '差旅报销单审核列表', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.ReimbursementReview {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
