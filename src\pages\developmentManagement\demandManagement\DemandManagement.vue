<template>
	<div class="DemandManagement" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<!-- 明细组件 -->
		<DemandManagementDetail ref="DemandManagementDetail" @close="queryTableData(1)" @openProject="openProject" />
		<ProjectDetail ref="ProjectDetailRef" @close="queryTableData(1)" @openWorkPlan="$refs.workPlan.showDetailCom($event)" />
		<workPlan ref="workPlan" />

		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="需求文档" name="DemandManagement">
				<BaseLayout showBgLogo>
					<template #header>
						<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">
							{{ tablePageForm.total }} 需求
						</el-button>
						<!-- 模糊查询 -->
						<SearchHistoryInput
							name="demandDocumentName"
							placeholder="需求文件名"
							v-model.trim="searchForm.demandDocumentName"
							@input="queryTableData(1)"
						/>
						<SearchHistoryInput
							width="120"
							name="submissionName"
							placeholder="提交人姓名"
							v-model.trim="searchForm.submissionName"
							@input="queryTableData(1)"
						/>

						<!-- 1--未提交 2--待评审 3--待签署 4--待开发经理确认 5--开发经理已确认 -->
						<el-checkbox-group v-model="searchForm.status" @change="queryTableData(1)">
							<el-checkbox v-for="key in Object.keys(demandStatusMap)" :key="key" :label="Number(key)">
								{{ demandStatusMap[key] }}
							</el-checkbox>
						</el-checkbox-group>

						<el-checkbox v-model="searchForm.isProjectCompleted" :true-label="1" :false-label="0" @change="queryTableData(1)">
							已实现
						</el-checkbox>

						<div class="ml-auto">
							<el-button type="text" class="icon-third-bt_newdoc" @click="openDocument">添加</el-button>
							<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
						</div>
					</template>
					<template #main>
						<div class="card-list-container">
							<div class="card-list">
								<div
									class="card-item"
									:class="getCardClass(item.status)"
									v-for="item in tableData"
									:key="item.dsid"
									@click="openDocument(item)"
								>
									<div class="card-item-title">
										<Tooltips class="min-w-150 vw10 text-center" :cont-str="item.demandDocumentName" :cont-width="150" />
									</div>
									<div class="card-item-content">
										<span>{{ item.submissionName || item.demandSupervisionCreateName }}</span>
										<span>{{ dateFormat(item.submissionTime || item.demandSupervisionTime) }}</span>
									</div>
									<div class="card-item-footer">
										<div class="card-item-type">{{ projectCategoryMap[item.projectCategory] }}</div>
										<div class="card-item-status"> {{ getStautsStr(item) }}</div>
									</div>
								</div>
							</div>
							<!-- 分页 -->
							<div class="text-right pr10">
								<el-pagination
									@size-change="handleSizeChange"
									@current-change="handleCurrentChange"
									:current-page="searchForm.pageNum"
									:page-sizes="[50, 100, 500]"
									:page-size="searchForm.pageSize"
									layout="total, sizes, prev, pager, next, jumper"
									:total="tablePageForm.total"
								>
								</el-pagination>
							</div>
						</div>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="需求清单" name="DemandList">
				<DemandList ref="DemandList" @openDocument="openDocument" @openProject="openProject" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DemandList from './DemandList.vue'; //需求清单
import DemandManagementDetail from './DemandManagementDetail.vue'; //明细组件
import ProjectDetail from '@/pages/developmentManagement/projectManagement/projectManagement/projectDetail'; //明细组件
import WorkPlan from '@/pages/developmentManagement/projectManagement/workPlan/workPlan.vue'; //明细组件
// import btnAuth from '@/mixins/btnAuth';
import { demandStatusMap, projectCategoryMap } from '@/assets/js/projectSource';

export default {
	name: 'demandManagement', //组件名应同路由名(否则keep-alive不生效)
	// import引入的组件需要注入到对象中才能使用
	components: {
		DemandList,
		DemandManagementDetail,
		ProjectDetail,
		WorkPlan,
	},
	// mixins: [btnAuth],
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			activeTab: 'DemandManagement', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			// 查询表单
			searchForm: {
				demandDocumentName: '',
				pageNum: 1,
				pageSize: 100,
				status: [],
				submissionName: '',
				isProjectCompleted: 0,
				// 其他...
			},
			demandStatusMap, // 需求状态
			projectCategoryMap, // 需求项目类别
			openMove: false, //打开组件
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		// this.queryTableData(1);
		this.changeTab();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 获取状态字符串
		getStautsStr({ status, appointDevelopmentManagerName }) {
			const developmentManagerName =
				appointDevelopmentManagerName == '/' ? '' : appointDevelopmentManagerName?.replace(/^\d+/, '') || '';
			switch (status) {
				case 4:
					return `待开发经理${developmentManagerName}确认`;
				default:
					return demandStatusMap[status];
			}
		},
		// 获取卡片样式
		getCardClass(status) {
			return {
				'bg-gray ': status == 1,
				'bg-yellow': status == 2,
				'bg-pink': status == 3,
				'bg-orange': status == 4,
				'bg-green': status == 5,
				'bg-pruple': status == 6,
			};
		},
		// 下载模板
		downloadTemplate(fileName) {
			const SERVER_URL =
				process.env.NODE_ENV == 'production' && window.location.origin ? window.location.origin : process.env.API_HOST; //	生产环境为当前服务器地址，开发环境为当前配置的地址
			window.open(SERVER_URL + '/template/' + fileName, '_self');
		},
		// 打开明细
		openDocument(item) {
			this.$refs.DemandManagementDetail.showDetailCom(item);
		},
		// 打开项目
		openProject(item) {
			this.$refs.ProjectDetailRef[item.pmid ? 'showDetailCom' : 'getDetailForm'](item);
		},

		// 切换tab
		changeTab() {
			if (this.activeTab == 'DemandManagement') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectDemandSuperVisionList'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		// 分页
		handleSizeChange(size) {
			this.searchForm.pageSize = size;
			this.queryTableData();
		},
		// 当前页
		handleCurrentChange(page) {
			this.searchForm.pageNum = page;
			this.queryTableData();
		},
		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.DemandManagement {
	width: 100%;
	overflow: hidden;
	position: relative;

	.card-list-container {
		display: flex;
		flex-direction: column;
		color: #666;
		font-size: 14px;
		height: calc(100vh - 260px);
	}
	.card-list {
		flex: 1;
		overflow-y: auto;
		padding: 15px 10px 10px 0;
		display: grid;
		grid-template-columns: repeat(6, 1fr);
		grid-template-rows: repeat(auto-fill, minmax(15vh, 1fr));
		grid-gap: 10px;

		.card-item {
			z-index: 2;
			background-color: #dfdfdf;
			border: 1px solid #f2f2f2;
			border-radius: 10px;
			padding: 10px;
			width: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-evenly;
			align-items: center;
			cursor: pointer;

			&:hover {
				box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
			}
			.card-item-title {
				font-size: 14px;
				font-weight: bold;
			}
			.card-item-content {
				font-size: 13px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				height: 10vh;
				// height: 50%;
			}
			.card-item-footer {
				width: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 12px;
			}
		}

		.bg-gray {
			color: #555;
			background-color: #d7d7d7 !important;
			border: 1px solid #c5c5c5 !important;
		}
		.bg-yellow {
			color: #555;
			background-color: #ffff80 !important;
			border: 1px solid #e6e673 !important;
		}
		.bg-pink {
			color: #555;
			background-color: #fbe5e8 !important;
			border: 1px solid #ddcbce !important;
		}
		.bg-orange {
			color: #555;
			background-color: #facd91 !important;
			border: 1px solid #ddb682 !important;
		}
		.bg-green {
			color: #555;
			background-color: #caf982 !important;
			border: 1px solid #b1da73 !important;
		}
		.bg-pruple {
			color: #555;
			background-color: #f3e5f5 !important;
			border: 1px solid #c8bdca !important;
		}
	}
}
</style>
