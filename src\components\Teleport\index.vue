<template>
	<div v-show="false">
		<slot></slot>
	</div>
</template>

<script>
export default {
	name: 'Teleport',
	props: {
		// 目标元素的选择器 如：#app 或 .el-main
		to: {
			type: String,
			required: true,
		},
		// 是否禁用 teleport 组件
		disabled: {
			type: Boolean,
			default: false,
		},
		// 插入位置 如：prepend 或 append
		position: {
			type: String,
			default: 'prepend', // 'prepend', 'append', 'after', 'before'
			validator: function (value) {
				return ['prepend', 'append', 'after', 'before'].indexOf(value) !== -1;
			},
		},
		// 相对定位的选择器，用于after和before
		relative: {
			type: String,
			default: '', // 相对定位的选择器，用于after和before
		},
	},
	data() {
		return {
			// 目标元素
			target: null,
			// 占位元素
			placeholder: null,
			// 观察器
			observer: null,
		};
	},
	mounted() {
		this.placeholder = this.$el;
		this.target = document.querySelector(this.to);

		if (this.target) {
			this.mount();
		}

		// 创建MutationObserver以监听目标元素的出现
		this.observer = new MutationObserver((mutations, observer) => {
			const target = document.querySelector(this.to);
			if (target && !this.target) {
				this.target = target;
				this.mount();
			}
		});

		// 开始观察document.body的子树变化
		this.observer.observe(document.body, {
			childList: true,
			subtree: true,
		});
	},
	beforeDestroy() {
		this.unmount(); // 卸载
		if (this.observer) {
			this.observer.disconnect(); // 断开观察器
			this.observer = null;
		}
	},
	methods: {
		mount() {
			if (this.disabled) {
				this.unmount();
				return;
			}

			const nodes = this.$slots.default; // 获取默认插槽
			if (!nodes || !this.target) return; // 如果没有节点或没有目标元素，则返回

			nodes.forEach(vnode => {
				if (!vnode.elm) return; // 如果节点没有元素，则返回

				switch (this.position) {
					case 'prepend':
						// 插入到目标元素的第一个子元素位置
						if (this.target.firstChild) {
							this.target.insertBefore(vnode.elm, this.target.firstChild);
						} else {
							// 如果目标元素没有第一个子元素，则插入到目标元素末尾
							this.target.appendChild(vnode.elm);
						}
						break;

					case 'after':
					case 'before':
						if (this.relative) {
							// 查找相对定位的元素
							const relativeEl = this.target.querySelector(this.relative);
							if (relativeEl) {
								if (this.position === 'after') {
									relativeEl.parentNode.insertBefore(vnode.elm, relativeEl.nextSibling);
								} else {
									relativeEl.parentNode.insertBefore(vnode.elm, relativeEl);
								}
								break;
							}
						}
					// 如果没有找到相对元素，退回到默认的append行为

					case 'append':
					default:
						// 默认追加到目标元素末尾
						this.target.appendChild(vnode.elm);
						break;
				}
			});
		},
		unmount() {
			const nodes = this.$slots.default;
			if (!nodes) return;

			nodes.forEach(vnode => {
				if (vnode.elm && vnode.elm.parentNode) {
					vnode.elm.parentNode.removeChild(vnode.elm);
					if (this.placeholder) {
						this.placeholder.appendChild(vnode.elm);
					}
				}
			});
		},
	},
	watch: {
		to(newValue) {
			this.unmount();
			this.target = document.querySelector(newValue);
			if (this.target) {
				this.mount();
			}
		},
		disabled(newValue) {
			if (newValue) {
				this.unmount();
			} else {
				this.mount();
			}
		},
		position() {
			if (this.target) {
				this.unmount();
				this.mount();
			}
		},
		relative() {
			if (this.target && (this.position === 'after' || this.position === 'before')) {
				this.unmount();
				this.mount();
			}
		},
	},
};
</script>
