<template>
	<!-- 问题详情 -->
	<div id="QuestionDetail" :class="isMoveCom ? [openMove ? 'moveToggle' : 'moveToggle moveToggle-hide', 'isMoveCom'] : ''">
		<Teleport v-if="workbenche.type == '研发'" to="#OnlineSupport">
			<!-- 任务规划-任务明细-BUG -->
			<TaskDetailCom ref="TaskDetailCom" isBug @save="acceptBug($event)" />
		</Teleport>
		<!-- 提问详情 -->
		<section class="H50 flex-column gap-10 border-bottom pb10">
			<header class="h-40 flex-align-center">
				<div class="label-title bolder fs-16">提问详情</div>
				<el-button
					v-show="nowDetail.mrqid && workbenche.type !== '研发'"
					type="danger"
					size="small"
					class="ml-auto"
					@click="sumbitBug"
				>
					🐞 提交BUG
				</el-button>
				<el-button v-if="isMoveCom" type="primary" size="small" class="el-icon-arrow-left" @click.stop="openMove = false">
					返回
				</el-button>
			</header>
			<main class="H100">
				<div v-show="nowDetail.mrqid" class="H100 flex-column gap-10">
					<!-- 标题 -->
					<div class="fs-14 max-h-200 overflow-y-auto">
						<span class="p0 m0 pre-wrap">{{ nowDetail.problem }}</span>
					</div>

					<!-- 图片 -->
					<div class="flex-align-center gap-10 flex-wrap max-h-350 overflow-y-auto">
						<div v-for="(item, index) in nowDetail.imageUrls" :key="index" class="img-box">
							<el-image lazy class="w-50 h-50 border" fit="scale-down" :src="item" :preview-src-list="nowDetail.imageUrls">
							</el-image>
						</div>
					</div>

					<!-- 日期和路径 -->
					<div class="flex-align-center gap-10 fs-12 color-999">
						<div class="el-icon-user"> {{ nowDetail.questioner }}</div>
						<div class="el-icon-time"> {{ dateFormat(nowDetail.questionDate, 'lineM') }}</div>
						<Tooltips class="el-icon-location-information" :cont-str="` ${nowDetail.questionSourcePath}` || ''"></Tooltips>
					</div>

					<!-- 回答输入框 -->
					<div class="text-right">
						<el-input
							v-model="nowDetail.replyContent"
							placeholder="请输入内容"
							type="textarea"
							:autosize="{ minRows: 3, maxRows: 4 }"
						></el-input>
						<el-button class="mt10 w-80" size="small" type="primary" @click="saveReply"> 回 答 </el-button>
					</div>
				</div>
				<div v-show="!nowDetail.mrqid" class="H100 flex-center">
					<div class="fs-16 color-999">👈 请在左侧提问清单选择一个问题</div>
				</div>
			</main>
		</section>
		<!-- 回答清单 -->
		<section class="H50 flex-column gap-10">
			<header class="h-40 flex-align-center flex-justify-between">
				<div class="label-title bolder fs-16">回答 · {{ replyList.length }}</div>
			</header>
			<main class="H100 overflow-y-auto">
				<!-- 回复树 -->
				<ReplyTree
					class="H100"
					v-if="replyList.length && nowDetail.mrqid"
					:replies="replyList"
					:workbenche="workbenche"
					:nowDetail="nowDetail"
					@returnBug="returnBug"
					@acceptBug="openTaskDetail"
					@refresh="refreshDetail()"
				/>
				<div v-else class="H100 flex-center">
					<div class="fs-16 color-999">
						{{ nowDetail.mrqid ? `👨‍🏫 等待 @${nowDetail.consultantName} 回答` : '👈 请在左侧提问清单选择一个问题' }}
					</div>
				</div>
			</main>
		</section>
	</div>
</template>
<script>
import { deepClone, debounce, dateFormat, jointString, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import Teleport from '@/components/Teleport';
import TaskDetailCom from '@/pages/developmentManagement/projectManagement/workPlan/taskDetailCom'; //任务规划-任务明细

import ReplyTree from './ReplyTree';

export default {
	name: 'QuestionDetail',
	components: { ReplyTree, TaskDetailCom, Teleport },
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
		// 资源数据
		resData: {
			type: Object,
			default: () => {},
		},
		// 工作台
		workbenche: {
			type: Object,
			default: () => ({
				type: '',
			}),
		},
	},
	data() {
		return {
			openMove: false, //打开组件
			nowDetail: {},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		// 回复列表
		replyList() {
			return this.nowDetail?.menuReplyVOS || [];
		},
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.clearDetail(); // 清空详情
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 刷新清单和详情
		refreshDetail() {
			this.$emit('refresh');
			this.queryDetail(this.nowDetail);
		},
		// 查询详情
		async queryDetail(item, type) {
			if (!item?.mrqid && !this.nowDetail?.mrqid) return;
			const API = 'selectDetailByQuestionId';
			try {
				const res = await this.$axios[API](JSON.stringify({ mrqid: item?.mrqid }));
				if (res.data.success) {
					this.nowDetail = { ...this.nowDetail, ...item, ...this.resData, ...res.data.data };
					this.$emit('getNowDetail', this.nowDetail);
					type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 清空详情
		clearDetail() {
			this.nowDetail = resetValues(this.nowDetail);
			this.$emit('getNowDetail', this.nowDetail);
		},
		// 保存回答
		saveReply: debounce(async function () {
			const { parentRid, mrqid, replyContent } = this.nowDetail;
			const { replyDept } = this.workbenche; // 获取当前工作台编号 -> 回复部门
			const API = 'addMenuRelateReply';
			try {
				const res = await this.$axios[API](JSON.stringify({ parentRid, mrqid, replyContent, replyDept }));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.nowDetail.replyContent = '';
					this.refreshDetail();
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		// 提交BUG
		sumbitBug: debounce(async function () {
			this.$confirm('是否确认该问题为BUG,并提交给研发中心?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'submitBug';
					try {
						const res = await this.$axios[API](JSON.stringify({ mrqid: this.nowDetail.mrqid }));
						if (res.data.success) {
							this.$succ(res.data.message);
							this.refreshDetail();
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		}),
		// 退回BUG
		returnBug: debounce(async function ({ mrrid }) {
			this.$confirm('是否确认将该问题非BUG，并退回该BUG反馈?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'returnBug';
					try {
						const res = await this.$axios[API](JSON.stringify({ parentRid: mrrid }));
						if (res.data.success) {
							this.$succ(res.data.message);
							this.refreshDetail();
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		}),
		// 打开任务规划-任务明细
		openTaskDetail(data) {
			const initData = { ...deepClone(this.nowDetail), ...deepClone(data) };
			// 初始化一些任务信息（如任务名、提出人等信息
			initData.taskName = initData.problem.length > 500 ? initData.problem.slice(0, 500) : initData.problem; // 任务名(截取前500个字符)
			initData.taskLevel = 0; // 优先级：0-高
			initData.taskClassify = 2; // 任务分类：2-BUG
			// 富文本内容
			initData.content = `
					<p><strong>实施顾问：</strong> ${initData.consultantName}</p>
					<p><strong>问题来源：</strong> ${initData.questionSourcePath}</p>
					<p><strong>问题描述：</strong> ${initData.problem}</p>
					<p><strong>问题截图：</strong> ${initData.imageUrls?.map(item => `<p><img src="${item}" /></p>`).join('')}</p>
				`.replace(/\n\s*\t*/g, '');

			console.log({ initData }, initData.content);
			this.$refs.TaskDetailCom.open(initData);
		},
		// 接收BUG
		acceptBug: debounce(async function (data) {
			const API = 'acceptBug';
			try {
				const res = await this.$axios[API](JSON.stringify({ parentRid: data.mrrid, addProjectManagementTaskDTO: data }));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.refreshDetail();
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		jointString, // 拼接字符串
		dateFormat, // 日期格式化
	},
};
</script>

<style lang="scss" scoped>
#QuestionDetail {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
}

#QuestionDetail.isMoveCom {
	padding: 10px;
	background-color: #fff;
	border: 1px solid #e9e9e9;
	border-radius: 8px;
	box-shadow: 0 2px 12px 0 rgba(30, 157, 111, 0.2); // 增强阴影效果

	transition: all 0.3s ease-in-out;
}
</style>
