<template>
	<div v-if="isShow" id="OnlineSupportBadge">
		<el-badge :value="unReplyCount" :hidden="!unReplyCount" class="unReplyCount-badge" :class="unReplyCount ? 'mr10' : ''">
			<el-button type="text" class="m0" @click="goTo('/OnlineSupport')"> 在线支持工作台 </el-button>
		</el-badge>
	</div>
</template>
<script>
// import {} from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'OnlineSupportBadge',
	components: {},
	data() {
		return {};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'unReplyCount', 'userLabels']),
		// 用户角色判断
		userRolesMap() {
			const roleList = this.userInfos?.adminUserVO?.adminRoleVOS || [];
			const labelList = this.userInfos?.adminUserVO?.adminUserLabelVOS || [];

			return {
				isSuperAdmin: roleList.some(item => item.arid == 1 || item.roleName == '超级管理员'),
				isSuccessSpecialist: labelList.some(item => item.remark == '成功部专员'),
				isDeliveryTeacher: labelList.some(item => item.remark == '实施顾问'),
				isDevManager: labelList.some(item => ['开发经理', '研发经理', '技术经理'].includes(item.remark)),
				isTestManager: labelList.some(item => ['测试经理'].includes(item.remark)),
			};
		},
		// 优先级为交付老师、成功部专员、研发经理、测试经理
		API() {
			const { isSuperAdmin, isSuccessSpecialist, isDeliveryTeacher, isDevManager, isTestManager } = this.userRolesMap;
			if (isDeliveryTeacher) return 'selectConsultantTeamAndUnReplyCount';
			if (isSuperAdmin) return 'selectCurrentTeamMenuAndUnReadCount';
			if (isSuccessSpecialist) return 'selectCurrentTeamMenuAndUnReadCount';
			if (isDevManager || isTestManager) return 'onlineSupportWorkBenchOfRDMenuInfo';
			return '';
		},
		// 是否显示（超级管理员、交付老师、成功部专员、研发经理、测试经理）
		isShow() {
			const { isSuperAdmin, isSuccessSpecialist, isDeliveryTeacher, isDevManager, isTestManager } = this.userRolesMap;
			return isSuperAdmin || isSuccessSpecialist || isDeliveryTeacher || isDevManager || isTestManager;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		console.log('👨‍💻 显示在线支持工作台：', this.isShow);
		if (this.isShow) {
			this.$poller.create('OnlineSupport', {
				callback: this.queryUnReplyCount,
				interval: 300000, // 5分钟刷新一次
				// debug: process.env.NODE_ENV === 'development', // 开发环境启用调试日志
				idleTimeout: 30000, // 30秒无操作后暂停轮询
				// 错误处理回调
				onError: error => {
					console.error('数据刷新失败:', error);
					throw error; // 抛出错误，轮询器会捕获并处理重试
				},
			});
		}
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		// 组件销毁前清理轮询器
		if (this.isShow) {
			this.$poller.destroy('OnlineSupport');
		}
	},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 查询未回复数
		async queryUnReplyCount() {
			const API = this.API;
			try {
				const res = await this.$axios[API](JSON.stringify({ customerName: '' }));
				if (res.data.success) {
					// 不记录 topRcid == -1 的资源
					const unReplyCount = res.data.data
						?.filter(item => item.topRcid != -1)
						.reduce((acc, cur) => acc + (cur.unReplyCount || cur.count), 0);
					this.$store.commit('setUnReplyCount', unReplyCount);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 路由跳转
		goTo(path) {
			const queryId = this.$route.query.queryId || this.userInfos?.adminUserVO.phoneNo;
			this.$router.push({ path, query: { queryId } });
		},
	},
};
</script>

<style lang="scss">
#OnlineSupportBadge {
	// 未回复消息数bage
	.unReplyCount-badge {
		margin: 0;
		padding: 0;
		// margin-right: 15px;
		.el-badge__content {
			z-index: 2;
			zoom: 0.8;
			padding: 0 5px;
			top: 10px;
			right: 5px;
		}
	}
}
</style>
