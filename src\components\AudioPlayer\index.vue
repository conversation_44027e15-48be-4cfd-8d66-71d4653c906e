<template>
	<div class="audio-player" :style="{ visibility: isVisible ? 'visible' : 'hidden' }" @contextmenu.prevent="togglePlayerInfo">
		<div v-if="audioUrl && isResourceLoaded" class="audio-container">
			<!-- 音频控制栏 -->
			<div class="audio-controls">
				<!-- 播放/暂停按钮 -->
				<i :class="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'" @click="togglePlay" class="control-btn"></i>

				<!-- 进度条 -->
				<div class="progress-container" @click="seek">
					<div class="progress-bar" :style="{ width: progress + '%' }"></div>
				</div>

				<!-- 时间显示 -->
				<span v-if="showTime" class="time-display"> {{ currentTime }}/{{ duration }} </span>

				<!-- 显示更多选项按钮 -->
				<el-tooltip content="更多选项（或右键点击显示）" placement="top" effect="light">
					<i class="el-icon-more control-panel-toggle" @click="togglePlayerInfo"></i>
				</el-tooltip>
			</div>

			<!-- 当前播放器信息 -->
			<div class="player-info" v-if="isPlayerInfoVisible">
				<div class="player-item">
					<span class="player-label">播放时长:</span>
					<span class="player-time">{{ formatTime(playDuration + currentSessionDuration) }}</span>
				</div>
				<!-- <div class="player-item">
					<span class="player-label">本次累计播放次数:</span>
					<span class="player-count">{{ playCount }}</span>
				</div> -->

				<!-- 自定义其他内容 -->
				<slot name="player-info"></slot>

				<!-- 播放倍速选择 -->
				<div v-if="showPlaybackRate" class="player-item playback-rate">
					<span class="player-label">播放速度：</span>
					<el-dropdown trigger="click" @command="changePlaybackRate">
						<span class="el-dropdown-link"> {{ playbackRate }}x <i class="el-icon-arrow-down el-icon--right"></i> </span>
						<el-dropdown-menu slot="dropdown">
							<el-dropdown-item v-for="rate in playbackRates" :key="rate" :command="rate">{{ rate }}x</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
				</div>

				<!-- 下载按钮 -->
				<div v-if="showDownload" class="player-item download-btn-container">
					<span class="player-label">下载：</span>
					<i class="el-icon-download download-btn" @click="downloadAudio"></i>
				</div>
			</div>

			<!-- 音频元素 -->
			<audio
				ref="audioElement"
				:src="audioUrl"
				@timeupdate="updateProgress"
				@loadedmetadata="onLoadMetadata"
				@ended="onEnded"
				@error="onError"
				@play="onPlay"
				@pause="onPause"
				preload="metadata"
			>
				<source :src="audioUrl" type="audio/mpeg" />
				<source :src="audioUrl" type="audio/wav" />
				<source :src="audioUrl" type="audio/ogg" />
				当前浏览器不支持播放音频，请更换其他浏览器或升级版本！
			</audio>
		</div>
		<!-- 懒加载占位符 -->
		<div v-else-if="audioUrl && !isResourceLoaded" class="audio-placeholder"> <i class="el-icon-loading"></i> 音频加载中... </div>
		<!-- 无音频URL时显示提示 -->
		<div v-else class="no-audio"> <i class="el-icon-warning-outline"></i> 无可用音频 </div>
	</div>
</template>

<script>
/**
 * AudioPlayer 组件
 *
 * 一个功能完善的音频播放器组件，支持播放/暂停、进度条控制、倍速播放、下载功能
 * 以及播放时长统计和事件通知
 *
 * 优化功能:
 * - 懒加载: 只有在组件可见时才加载音频资源
 * - 资源释放: 组件不可见时释放音频资源
 * - 性能优化: 减少不必要的渲染和事件监听
 */
export default {
	name: 'AudioPlayer',
	props: {
		/**
		 * 音频文件URL
		 */
		audioUrl: {
			type: String,
			default: '',
			required: true,
		},
		/**
		 * 是否自动播放
		 * 注意：由于浏览器安全策略，自动播放可能会被阻止
		 */
		autoplay: {
			type: Boolean,
			default: false,
		},
		/**
		 * 组件宽度
		 */
		width: {
			type: [String, Number],
			default: '100%',
		},
		/**
		 * 下载时的文件名
		 */
		fileName: {
			type: String,
			default: '音频文件',
		},

		/**
		 * 是否启用懒加载
		 * 设为true时，只有在组件可见时才加载音频资源
		 */
		lazyLoad: {
			type: Boolean,
			default: true,
		},
		/**
		 * 是否自动释放资源
		 * 设为true时，组件不可见时会释放音频资源
		 */
		autoReleaseResource: {
			type: Boolean,
			default: true,
		},
		/**
		 * 组件可见性阈值
		 * 当组件与视口的交叉比例超过此值时，认为组件可见
		 */
		visibilityThreshold: {
			type: Number,
			default: 0.1,
		},
		/**
		 * 是否显示播放时长信息
		 */
		showTime: {
			type: Boolean,
			default: true,
		},
		/**
		 * 是否显示播放倍速选择
		 */
		showPlaybackRate: {
			type: Boolean,
			default: true,
		},
		/**
		 * 播放倍速选择
		 */
		playbackRates: {
			type: Array,
			default: () => [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
		},
		/**
		 * 是否显示下载按钮
		 */
		showDownload: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			// 播放器状态
			isPlaying: false, // 是否正在播放
			progress: 0, // 播放进度百分比
			currentTime: '00:00', // 当前播放时间
			duration: '00:00', // 音频总时长
			audio: null, // 音频元素引用
			playbackRate: 1.0, // 当前播放速度
			isPlayerInfoVisible: false, // 播放信息是否可见

			// 播放记录相关数据
			playStartTime: null, // 当前会话开始播放的时间戳
			playDuration: 0, // 累计播放时长(秒)
			playCount: 0, // 播放次数
			lastPlayedAt: null, // 最后一次播放的时间戳
			playHistory: [], // 播放历史记录

			// 当前会话播放时长
			currentSessionDuration: 0, // 当前会话播放时长(秒)

			// 更新当前会话播放时长的定时器
			playerTimer: null, // 定时器引用

			// 资源优化相关
			isVisible: false, // 组件是否在视口中可见
			isResourceLoaded: false, // 音频资源是否已加载
			intersectionObserver: null, // 交叉观察器
			resourceLoadTimer: null, // 资源加载延迟定时器
			lastPlayPosition: 0, // 上次播放位置，用于资源释放后恢复

			// 播放器信息自动隐藏定时器
			playerInfoTimer: null, // 播放器信息自动隐藏定时器
		};
	},
	computed: {
		/**
		 * 提供给父组件的播放统计信息
		 * @returns {Object} 包含播放统计数据的对象
		 */
		playStats() {
			return {
				audioDuration: this.duration, // 音频总时长 单位秒
				playCount: this.playCount, // 播放次数
				playDuration: this.playDuration + this.currentSessionDuration, // 播放时长 单位秒
				currentSessionDuration: this.currentSessionDuration, // 当前会话播放时长 单位秒
				lastPlayedAt: this.lastPlayedAt, // 最后一次播放的时间戳
				playHistory: [...this.playHistory], // 播放历史记录
				isPlaying: this.isPlaying, // 是否正在播放
				currentProgress: this.progress, // 当前播放进度 百分比
				currentPlaybackRate: this.playbackRate, // 当前播放速度
			};
		},
	},
	watch: {
		/**
		 * 监听音频URL变化
		 * 当URL变化时重置播放器并根据autoplay属性决定是否自动播放
		 */
		audioUrl(newUrl) {
			if (newUrl) {
				// 重置资源加载状态
				this.isResourceLoaded = !this.lazyLoad;

				// 如果不需要懒加载或组件已可见，则加载资源
				if (!this.lazyLoad || this.isVisible) {
					this.loadResource();
				}
			} else {
				this.isResourceLoaded = false;
				this.resetPlayer();
			}
		},

		/**
		 * 监听组件可见性变化
		 * 当组件变为可见时加载资源，变为不可见时释放资源
		 */
		isVisible(visible) {
			if (visible) {
				if (this.audioUrl && !this.isResourceLoaded) {
					this.loadResource();
				}
			} else {
				if (this.autoReleaseResource && this.isResourceLoaded) {
					this.releaseResource();
				}
			}
		},
	},
	mounted() {
		// 初始化交叉观察器，用于检测组件可见性
		this.initIntersectionObserver();

		// 如果不需要懒加载，则直接加载资源
		if (!this.lazyLoad && this.audioUrl) {
			this.isResourceLoaded = true;
			this.$nextTick(() => {
				this.audio = this.$refs.audioElement;
				if (this.autoplay) {
					this.play();
				}
			});
		}

		// 添加页面可见性变化监听
		document.addEventListener('visibilitychange', this.handleVisibilityChange);
	},
	beforeDestroy() {
		// 正在播放时被销毁，通知父组件播放结束
		this.isPlaying &&
			this.$emit('playEnded', {
				...this.playStats,
				endedAt: new Date(),
			});

		// 组件销毁前停止播放并清理资源
		this.releaseResource();

		// 清除定时器
		this.clearSessionTimer();
		if (this.resourceLoadTimer) {
			clearTimeout(this.resourceLoadTimer);
		}
		if (this.playerInfoTimer) {
			clearTimeout(this.playerInfoTimer);
		}

		// 断开交叉观察器
		if (this.intersectionObserver) {
			this.intersectionObserver.disconnect();
			this.intersectionObserver = null;
		}

		// 移除页面可见性监听
		document.removeEventListener('visibilitychange', this.handleVisibilityChange);
	},
	methods: {
		/**
		 * 切换播放器信息显示状态
		 */
		togglePlayerInfo() {
			this.isPlayerInfoVisible = !this.isPlayerInfoVisible;

			// 如果显示了播放器信息，设置自动隐藏定时器
			// if (this.isPlayerInfoVisible) {
			// 	this.setPlayerInfoAutoHide();
			// } else if (this.playerInfoTimer) {
			// 	clearTimeout(this.playerInfoTimer);
			// }
		},

		/**
		 * 设置播放器信息自动隐藏
		 * 10秒后自动隐藏播放器信息
		 */
		// setPlayerInfoAutoHide() {
		// 	if (this.playerInfoTimer) {
		// 		clearTimeout(this.playerInfoTimer);
		// 	}

		// 	this.playerInfoTimer = setTimeout(() => {
		// 		this.isPlayerInfoVisible = false;
		// 	}, 10000); // 10秒后自动隐藏
		// },

		/**
		 * 初始化交叉观察器
		 * 用于检测组件是否在视口中可见
		 */
		initIntersectionObserver() {
			// 检查浏览器是否支持IntersectionObserver
			if ('IntersectionObserver' in window) {
				this.intersectionObserver = new IntersectionObserver(
					entries => {
						const isIntersecting = entries[0].isIntersecting;
						const intersectionRatio = entries[0].intersectionRatio;

						// 当交叉比例超过阈值时，认为组件可见
						if (isIntersecting && intersectionRatio >= this.visibilityThreshold) {
							this.isVisible = true;
						} else {
							this.isVisible = false;
						}
					},
					{
						threshold: [0, this.visibilityThreshold, 1.0],
						rootMargin: '50px',
					},
				);

				// 开始观察当前组件元素
				this.$nextTick(() => {
					this.intersectionObserver.observe(this.$el);
				});
			} else {
				// 如果浏览器不支持IntersectionObserver，则始终认为组件可见
				this.isVisible = true;
			}
		},

		/**
		 * 处理页面可见性变化
		 * 当页面不可见时暂停播放，避免资源浪费
		 */
		handleVisibilityChange() {
			if (document.hidden && this.isPlaying) {
				this.pause();
			}
		},

		/**
		 * 加载音频资源
		 * 延迟100ms加载，避免频繁加载/卸载资源
		 */
		loadResource() {
			// 清除可能存在的加载定时器
			if (this.resourceLoadTimer) {
				clearTimeout(this.resourceLoadTimer);
			}

			// 延迟加载资源，避免快速滚动时的频繁加载
			this.resourceLoadTimer = setTimeout(() => {
				this.isResourceLoaded = true;

				this.$nextTick(() => {
					this.audio = this.$refs.audioElement;

					// 恢复之前的播放位置
					if (this.lastPlayPosition > 0 && this.audio) {
						this.audio.currentTime = this.lastPlayPosition;
					}

					// 如果设置了自动播放，则开始播放
					if (this.autoplay && !document.hidden) {
						this.play();
					}
				});
			}, 100);
		},

		/**
		 * 释放音频资源
		 * 保存当前播放位置，然后释放资源
		 */
		releaseResource() {
			// 如果正在播放，则先暂停
			if (this.isPlaying && this.audio) {
				this.pause();
			}

			// 保存当前播放位置
			if (this.audio) {
				this.lastPlayPosition = this.audio.currentTime || 0;
				this.audio = null;
			}

			// 标记资源为未加载
			this.isResourceLoaded = false;
		},

		/**
		 * 切换播放/暂停状态
		 */
		togglePlay() {
			if (!this.audio && this.isResourceLoaded) {
				this.audio = this.$refs.audioElement;
			}

			if (!this.audio) return;

			if (this.isPlaying) {
				this.pause();
			} else {
				this.play();
			}
		},

		/**
		 * 开始播放
		 */
		play() {
			if (!this.audio && this.isResourceLoaded) {
				this.audio = this.$refs.audioElement;
			}

			if (!this.audio) return;

			this.audio
				.play()
				.then(() => {
					this.isPlaying = true;
					// 播放开始时间在onPlay事件中处理
				})
				.catch(err => {
					console.error('播放失败:', err);
					this.$message.error('音频播放失败，可能是浏览器限制或音频格式不支持');
				});
		},

		/**
		 * 暂停播放
		 */
		pause() {
			if (!this.audio) return;

			this.audio.pause();
			this.isPlaying = false;
			// 播放暂停在onPause事件中处理
		},

		/**
		 * 跳转到指定位置
		 * @param {Event} event - 点击事件对象
		 */
		seek(event) {
			if (!this.audio) return;

			const container = event.currentTarget;
			const clickPosition = event.offsetX / container.offsetWidth;
			const seekTime = this.audio.duration * clickPosition;

			if (!isNaN(seekTime)) {
				this.audio.currentTime = seekTime;
				// 记录跳转操作
				this.recordPlayAction('seek', { position: seekTime });
			}
		},

		/**
		 * 更新播放进度
		 * 由音频元素的timeupdate事件触发
		 */
		updateProgress() {
			if (!this.audio) return;

			const currentTime = this.audio.currentTime;
			const duration = this.audio.duration;

			if (!isNaN(duration) && duration > 0) {
				this.progress = (currentTime / duration) * 100;
				this.currentTime = this.formatTime(currentTime);

				// 更新最后播放位置，用于资源释放后恢复
				this.lastPlayPosition = currentTime;
			}
		},
		/**
		 * 主动更新当前会话时长
		 * @param {Number} duration - 当前会话时长(秒)
		 * @returns {void}
		 */
		updatePlayDuration(duration, currentTime) {
			if (!this.audio || isNaN(duration) || duration <= 0) return;
			this.audio.currentTime = currentTime;
			// this.currentSessionDuration += duration;
			// this.playDuration += this.currentSessionDuration;
			this.playDuration += duration;
			console.log(this.playDuration, this.currentSessionDuration);
		},

		/**
		 * 音频元数据加载完成事件处理
		 * 获取音频总时长
		 */
		onLoadMetadata() {
			if (!this.audio) return;

			if (!isNaN(this.audio.duration)) {
				this.duration = this.formatTime(this.audio.duration);
			}
			this.$emit('loadMetadata', {
				...this.playStats,
				duration: this.audio.duration,
			});
		},

		/**
		 * 音频播放结束事件处理
		 */
		onEnded() {
			this.isPlaying = false;
			this.progress = 0;
			this.currentTime = '00:00';
			if (this.audio) {
				this.audio.currentTime = 0;
			}

			// 重置最后播放位置
			this.lastPlayPosition = 0;

			// 停止会话计时器
			this.clearSessionTimer();

			// 累加当前会话时长到总时长
			this.playDuration += this.currentSessionDuration;
			this.currentSessionDuration = 0;

			// 记录播放结束
			this.recordPlayAction('ended');

			// 通知父组件播放结束
			this.$emit('playEnded', {
				...this.playStats,
				endedAt: new Date(),
			});
		},

		/**
		 * 音频加载错误事件处理
		 */
		onError() {
			console.error('音频加载失败，请检查链接是否有效');
			this.isPlaying = false;

			// 停止会话计时器
			this.clearSessionTimer();

			// 记录播放错误
			this.recordPlayAction('error');
		},

		/**
		 * 播放开始事件处理
		 * 由音频元素的play事件触发
		 */
		onPlay() {
			this.playStartTime = new Date();
			this.playCount++;
			this.lastPlayedAt = this.playStartTime;

			// 开始计时当前会话播放时长
			this.startSessionTimer();

			// 记录播放开始
			this.recordPlayAction('start');

			// 通知父组件播放开始
			this.$emit('playStart', {
				...this.playStats,
				startedAt: this.playStartTime,
			});
		},

		/**
		 * 播放暂停事件处理
		 * 由音频元素的pause事件触发
		 */
		onPause() {
			if (this.playStartTime) {
				const pauseTime = new Date();

				// 停止会话计时器
				this.clearSessionTimer();

				// 累加当前会话时长到总时长
				this.playDuration += this.currentSessionDuration;

				// 记录播放暂停
				this.recordPlayAction('pause', {
					duration: this.currentSessionDuration,
				});

				// 通知父组件播放暂停
				this.$emit('playPause', {
					...this.playStats,
					pausedAt: pauseTime,
				});

				// 重置当前会话时长
				this.playStartTime = null;
				this.currentSessionDuration = 0;
			}
		},

		/**
		 * 开始会话计时器
		 * 每秒更新一次当前会话播放时长
		 */
		startSessionTimer() {
			// 清除可能存在的旧定时器
			this.clearSessionTimer();

			// 重置当前会话时长
			this.currentSessionDuration = 0;

			// 创建新的定时器，每秒更新一次当前会话时长
			this.playerTimer = setInterval(() => {
				if (this.isPlaying && this.playStartTime) {
					const now = new Date();
					this.currentSessionDuration = (now - this.playStartTime) / 1000; // 转换为秒

					// 通知父组件播放进行中
					this.$emit('playProgress', {
						...this.playStats,
						currentTime: now,
					});
				}
			}, 1000);
		},

		/**
		 * 清除会话计时器
		 */
		clearSessionTimer() {
			if (this.playerTimer) {
				clearInterval(this.playerTimer);
				this.playerTimer = null;
			}
		},

		/**
		 * 记录播放动作
		 * @param {String} action - 动作类型
		 * @param {Object} data - 附加数据
		 */
		recordPlayAction(action, data = {}) {
			const record = {
				action,
				timestamp: new Date(),
				audioUrl: this.audioUrl,
				...data,
			};

			this.playHistory.push(record);

			// 限制历史记录长度，避免内存占用过多
			if (this.playHistory.length > 10) {
				this.playHistory.shift();
			}

			// 发送播放状态变更事件
			this.$emit('playStateChange', {
				...this.playStats,
				action,
				actionData: data,
			});
		},

		/**
		 * 重置播放器状态
		 */
		resetPlayer() {
			this.isPlaying = false;
			this.progress = 0;
			this.currentTime = '00:00';
			this.duration = '00:00';

			// 停止会话计时器
			this.clearSessionTimer();
			this.currentSessionDuration = 0;

			if (this.audio) {
				this.audio.currentTime = 0;
			}

			// 不重置播放统计信息，因为可能需要累计记录
		},

		/**
		 * 格式化时间
		 * @param {Number} seconds - 秒数
		 * @returns {String} 格式化后的时间字符串 (MM:SS)
		 */
		formatTime(seconds) {
			if (isNaN(seconds)) return '00:00';

			const mins = Math.floor(seconds / 60);
			const secs = Math.floor(seconds % 60);
			return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
		},

		/**
		 * 转换为秒
		 * @param {String} time - 时间字符串 (MM:SS)
		 * @returns {Number} 秒数
		 */
		formatTimeToSeconds(time) {
			if (isNaN(time)) return 0;

			const [mins, secs] = time.split(':').map(Number);
			return mins * 60 + secs;
		},

		/**
		 * 改变播放速度
		 * @param {Number} rate - 播放速度倍率
		 */
		changePlaybackRate(rate) {
			if (!this.audio) return;

			this.playbackRate = rate;
			this.audio.playbackRate = rate;

			// 记录播放速度变更
			this.recordPlayAction('rateChange', { rate });

			// 通知父组件播放速度变更
			this.$emit('playbackRateChange', {
				...this.playStats,
				rate,
			});
		},

		/**
		 * 下载音频
		 * 创建一个临时链接并触发下载
		 */
		downloadAudio() {
			if (!this.audioUrl) return;

			try {
				// 创建一个隐藏的a标签
				const link = document.createElement('a');
				link.href = this.audioUrl;

				// 从URL中提取文件名，如果没有则使用props中的fileName或默认名称
				let filename = this.fileName;
				if (this.audioUrl.includes('/')) {
					const urlParts = this.audioUrl.split('/');
					const urlFilename = urlParts[urlParts.length - 1];
					if (urlFilename && urlFilename.includes('.')) {
						filename = urlFilename;
					}
				}

				// 设置下载属性和文件名
				link.setAttribute('download', filename);
				link.setAttribute('target', '_blank');
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);

				// 记录下载操作
				this.recordPlayAction('download', { filename });

				// 通知父组件下载操作
				this.$emit('audioDownload', {
					...this.playStats,
					filename,
				});
			} catch (error) {
				console.error('下载失败:', error);
				this.$message.error('下载失败，请检查音频链接是否支持直接下载');

				// 记录下载失败
				this.recordPlayAction('downloadError', { error: error.message });
			}
		},

		/**
		 * 提供给父组件调用的方法，获取播放统计信息
		 * @returns {Object} 播放统计信息
		 */
		getPlayStats() {
			return this.playStats;
		},
	},
};
</script>

<style lang="scss" scoped>
/* 音频播放器样式 */
.audio-player {
	width: 100%;
	min-width: 200px;

	/* 音频容器 */
	.audio-container {
		background-color: #f5f7fa;
		border-radius: 8px;
		padding: 0 5px;
		/* 控制栏样式 */
		.audio-controls {
			display: flex;
			align-items: center;

			/* 播放/暂停按钮 */
			.control-btn {
				font-size: 16px;
				cursor: pointer;
				color: #409eff;

				&:hover {
					color: #66b1ff;
				}
			}

			/* 进度条容器 */
			.progress-container {
				flex: 1;
				height: 6px;
				background-color: #e4e7ed;
				border-radius: 3px;
				position: relative;
				cursor: pointer;
				margin: 0 10px;

				/* 进度条 */
				.progress-bar {
					position: absolute;
					height: 100%;
					background-color: #409eff;
					border-radius: 3px;
				}
			}

			/* 时间显示 */
			.time-display {
				font-size: 12px;
				color: #606266;
				min-width: 80px;
				text-align: right;
			}

			/* 控制面板切换按钮 */
			.control-panel-toggle {
				font-size: 14px;
				color: #606266;
				cursor: pointer;
				margin-left: 10px;

				&:hover {
					color: #409eff;
				}
			}
		}

		/* 播放信息显示 */
		.player-info {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			gap: 10px;
			padding: 5px;
			font-size: 12px;

			/* 标签样式 */
			.player-label {
				color: #909399;
			}

			/* 时间和计数显示 */
			.player-time,
			.player-count {
				color: #606266;
				font-weight: 500;
				margin: 0 5px;
			}

			/* 播放速率选择器 */
			.playback-rate {
				display: flex;
				align-items: center;

				.el-dropdown-link {
					font-size: 12px;
					display: inline-flex;
					align-items: center;
					cursor: pointer;
					color: #409eff;

					&:hover {
						color: #66b1ff;
					}
				}
			}

			/* 下载按钮 */
			.download-btn-container {
				display: flex;
				align-items: center;
				.download-btn {
					font-size: 13px;
					color: #409eff;
					cursor: pointer;
					margin-left: 5px;
				}
			}
		}
	}

	/* 加载占位符 */
	.audio-placeholder {
		display: flex;
		align-items: center;
		justify-content: center;
		color: #909399;
		font-size: 12px;
		background-color: #f5f7fa;
		border-radius: 4px;

		i {
			margin-right: 5px;
		}
	}

	/* 无音频时的提示 */
	.no-audio {
		color: #909399;
		font-size: 12px;
		text-align: center;
		padding: 8px;
	}
}
</style>
