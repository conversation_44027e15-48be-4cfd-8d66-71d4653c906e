<template>
	<!-- 跟单记录卡片 -->
	<div class="follow-card-wrapper">
		<div class="follow-card border flex" v-for="item in newDocumentaryRecordsList" :key="item.drid">
			<!-- 跟单内容 -->
			<div class="follow-card-item W60" :class="{ W50: item.nextPlan || item.nextStep }">
				<div class="follow-card-title flex-align-center">
					<div class="follow-card-name fs-16 bolder">
						{{ item.salesmanName }}
					</div>
					<div class="follow-card-time color-999 ml15 mr15">
						{{ dateFormat(item.documentaryTime, 'lineM') }}
					</div>
					<i class="el-icon-edit-outline hover-green" @click="openDialog('跟单', item)"></i>

					<!-- 跟单方式 -->
					<div class="ml10 fs-12 flex-align-center">
						<span v-if="item.recordType">跟单方式：{{ item.recordType }}</span>
						<div v-if="item.facilitatorMap" class="ml10 fs-12 flex-align-center">
							<span>协助人：</span>
							<Tooltips class="max-w-200" :cont-str="Object.values(item.facilitatorMap).join(',')" :cont-width="180" />
						</div>
						<div v-if="item.recordAttachment" class="ml10 flex-align-center">
							<span>附件：</span>
							<FilePopover class="inline-block max-w-200 fs-12 pointer" :url="item.recordAttachment" :isIcon="true" />
						</div>
					</div>
				</div>
				<pre class="follow-card-content mt10 mb0">{{ item.content }}</pre>
			</div>

			<!-- 下一步内容 -->
			<div v-if="item.nextPlan || item.nextStep" class="follow-card-item ml20 W40">
				<div class="flex-align-center">
					<!-- 计划信息 -->
					<div class="follow-card-title flex-align-center" :class="{ 'cancel-plan': item.planStatus }">
						<div class="follow-card-name fs-16 bolder"> 下一步计划</div>
						<div class="follow-card-time color-999 ml15 mr15">
							{{ dateFormat(item.nextStep, 'lineM') }}
						</div>
						<i class="el-icon-edit-outline hover-green" @click="openDialog('计划', item)"></i>
					</div>
				</div>
				<pre class="follow-card-content mt10 mb0" :class="{ 'cancel-plan': item.planStatus }">{{ item.nextPlan }}</pre>
			</div>
		</div>

		<!-- 跟单时间/计划弹窗 -->
		<el-dialog width="666px" :visible.sync="dialogEdit" :close-on-click-modal="false" append-to-body @close="closeDialog">
			<span slot="title">修改{{ dialogTitle }}信息</span>

			<el-form :model="planForm" :rules="formRules" label-width="100px" label-position="left" @submit.native.prevent>
				<div v-if="dialogTitle == '跟单'">
					<el-form-item label="跟单时间" prop="documentaryTime">
						<el-date-picker
							class="W100"
							v-model="planForm.documentaryTime"
							value-format="timestamp"
							type="datetime"
							format="yyyy-MM-dd HH:mm"
							placeholder="请选择日期时间"
							:clearable="false"
							:default-value="new Date()"
						></el-date-picker>
					</el-form-item>
					<el-form-item label="跟单内容" prop="content">
						<el-input
							disabled
							type="textarea"
							:autosize="{ minRows: 2, maxRows: 4 }"
							v-model="planForm.content"
							placeholder="请输入跟单内容..."
						></el-input>
					</el-form-item>
				</div>

				<div v-else>
					<el-form-item label="计划日期" prop="nextStep">
						<el-date-picker
							class="W100"
							v-model="planForm.nextStep"
							value-format="timestamp"
							type="datetime"
							format="yyyy-MM-dd HH:mm"
							placeholder="选择日期后再输入内容"
							:clearable="false"
							:default-value="$moment(new Date()).endOf('day').valueOf()"
							default-time="23:59:59"
						></el-date-picker>
					</el-form-item>

					<el-form-item label="计划内容" prop="nextPlan">
						<el-input
							type="textarea"
							:autosize="{ minRows: 2, maxRows: 4 }"
							v-model="planForm.nextPlan"
							placeholder="请输入以何种方式联系谁达成什么目标,如：微信联系张总了解其对报价单的看法"
						></el-input>
					</el-form-item>

					<el-form-item label="计划状态" prop="planStatus">
						<el-radio-group v-model="planForm.planStatus">
							<el-radio :label="0">进行</el-radio>
							<el-radio :label="1">关闭</el-radio>
						</el-radio-group>
					</el-form-item>
				</div>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="savePlan('保存', planForm)">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';

export default {
	name: 'FollowRecord',
	components: { FilePopover },
	props: {
		// 询盘主键
		idid: { type: [String, Number], default: '' },
		// 明细表单
		detailForm: { type: Object, default: () => {} },
		// 跟单记录
		newDocumentaryRecordsList: { type: Array, default: () => [] },
	},

	data() {
		return {
			dialogEdit: false,
			dialogTravel: false,
			dialogTitle: '',

			// 跟单/计划弹窗表单
			planForm: {
				content: '',
				drid: '',
				nextPlan: '',
				nextStep: '',
				planStatus: 0,
				documentaryTime: '',
			},
			// 出差申请弹窗表单
			travelForm: {
				applyMemo: '',
				applySource: '',
				btaid: '',
				dmid: '',
				dmsid: '',
				dmsiid: '',
				drid: '',
				expenseParty: '',
				idid: '',
				tripBeginDate: '',
				tripClientName: '',
				tripDays: '',
				tripDestination: '',
				tripType: '',
				tripReason: '',
				tripUids: '',
				tripUidsArray: [], //用于转换为el-select的value
				twid: '',
			},
			formRules: {
				documentaryTime: [{ required: true, message: '请选择跟单日期', trigger: 'change' }],
				content: [{ required: true, message: '请输入跟单内容', trigger: 'blur' }],
				nextStep: [{ required: true, message: '请选择计划日期', trigger: 'change' }],
				nextPlan: [{ required: true, message: '请输入计划内容', trigger: 'blur' }],
				planStatus: [{ required: true, message: '请输入计划状态', trigger: 'change' }],
			},
			travelFormRules: {
				expenseParty: [{ required: true, message: '请输入承担方', trigger: 'change' }],
				tripDestination: [{ required: true, message: '请输入目的地', trigger: 'change' }],
				tripReason: [{ required: true, message: '请输入事由', trigger: 'blur' }],
				tripBeginDate: [{ required: true, message: '请输入开始日期', trigger: 'blur' }],
				tripDays: [{ required: true, message: '请输入出差天数', trigger: 'blur' }],
				tripUidsArray: [{ required: true, message: '请输入出差人', trigger: 'blur' }],
				tripUids: [{ required: true, message: '请输入出差人', trigger: 'blur' }],
			},
			destinationList: [], // 目的地列表
			tripUserList: [], // 出差人列表
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 保存修改 - 修改跟单/计划
		savePlan: _.debounce(async function (type, planForm) {
			if (type === '关闭计划') {
				if (!planForm.idid || !planForm.drid) {
					return this.$message.warning('未找到该计划，请重新刷新后再操作！');
				}
				planForm.planStatus = 1; //关闭计划
			}
			if (this.dialogTitle == '跟单') {
				if (!planForm.documentaryTime || !planForm.content) {
					this.$message.warning('请完善跟单时间/跟单内容');
					return;
				}
			} else {
				// 修改计划
				if (!planForm.nextPlan || !planForm.nextStep) {
					this.$message.warning('请完善计划时间/计划内容');
					return;
				}
			}

			const API = 'updateNextPlan';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...planForm }));
				if (res.data.success) {
					this.closeDialog();
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		// 打开跟单/计划弹窗
		openDialog(title, item) {
			this.dialogTitle = title;

			// 跟单/计划弹窗表单
			this.planForm = {
				content: item.content,
				planStatus: item.planStatus,
				idid: this.detailForm.idid || this.idid,
				drid: item.drid,
				nextPlan: item.nextPlan,
				nextStep: item.nextStep,
				documentaryTime: item.documentaryTime,
			};
			this.dialogEdit = true;
		},
		// 关闭弹窗
		closeDialog() {
			this.dialogEdit = false;
			this.planForm = _.resetValues(this.planForm);
		},

		dateFormat: _.dateFormat,
		jointString: _.jointString,
	},
};
</script>

<style lang="scss" scoped>
.follow-card-wrapper {
	color: #555;
	// max-height: 80vh;
	// overflow-y: auto;
	.follow-card {
		background-color: #f5f5f5;
		border-color: #e9e9e9;
		border-radius: 3px;
		margin: 10px 0;
		padding: 10px;
		.follow-card-content {
			white-space: break-spaces;
		}
		.cancel-plan {
			// 关闭计划
			text-decoration: line-through;
			color: #999;
		}
		&:hover {
			border-color: #1e9d6f !important;
		}
	}
}
</style>
