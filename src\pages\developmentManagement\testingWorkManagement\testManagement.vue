<template>
	<!-- 狗屎一坨 -->
	<div id="testManagement">
		<div class="taskManagemnet-gantt">
			<el-row class="gantt-header">
				<el-button
					type="text"
					class="mr30"
					:icon="isExpanded ? 'el-icon-s-fold' : 'el-icon-s-unfold'"
					@click="isExpanded = !isExpanded"
				>
					{{ isExpanded ? '全部折叠' : '全部展开' }}
				</el-button>
				<el-checkbox-group size="mini" v-model="searchForm.statusList" @change="getTreeData">
					<el-checkbox v-for="(item, index) in statusList" :label="item.id" :key="index">
						<span class="flex-align-center">
							<span class="circle" :style="{ color: colorMap[item.id] }">●</span>{{ item.status }}
						</span>
					</el-checkbox>
				</el-checkbox-group>

				<div class="ml-auto flex-justify-end align-center">
					<span class="fs-16">任务日期：</span>
					<DateSelect
						defaultDate="本月"
						:dateList="['上周', '本周', '上月', '本月', '不限定']"
						@change="
							dateSelectObj = $event;
							getColList();
						"
					/>
				</div>
			</el-row>
			<div class="gantt-content">
				<split-pane :min-percent="20" :default-percent="22" split="vertical">
					<template slot="paneL">
						<div class="left">
							<div>
								<div class="search-area" style="position: relative; width: 18vw; height: 47px; line-height: 47px">
									<el-input
										class="searchBox"
										size="small"
										clearable
										v-model="queryStr"
										placeholder="请输入任务编号/任务名/测试人员查询"
									></el-input>
									<el-button type="text" class="el-icon-refresh-right" @click="getTreeData('refresh')">刷新</el-button>
								</div>
								<div class="taskManagemnet-tree">
									<el-tree
										:data="treeData"
										ref="workTreeRef"
										node-key="id"
										:highlight-current="true"
										:default-expand-all="false"
										:renderContent="renderContent"
										:props="{ children: 'selectProjectGanttChartVOS', label: 'userName' }"
										@node-expand="clickNodeExpand"
										@node-collapse="clickNodeCollapse"
										:filter-node-method="filterNode"
									></el-tree>
								</div>
							</div>
						</div>
					</template>
					<template slot="paneR">
						<div class="right">
							<div class="GantTableBox">
								<div class="tableContent">
									<table class="gantTable" cellspacing="0">
										<thead>
											<!-- 日期 -->
											<tr>
												<th
													class="row_head"
													v-for="(item, index) in dateColList"
													:key="'date' + index"
													:style="{ 'background-color': item.setbKColor }"
													:colspan="item.colspan"
												>
													{{ item.date.includes('/') ? item.date : '' }}
												</th>
											</tr>
											<!-- 星期 -->
											<tr>
												<th
													class="row_head"
													v-for="(item, index) in weekColList"
													:key="'week' + index"
													:style="{ 'background-color': item.setbKColor }"
												>
													{{ item.week }}
												</th>
											</tr>
											<!-- 合计 -->
											<tr>
												<th
													class="row_head_sum"
													v-for="(item, index) in dateColList"
													:key="'date' + index"
													:style="{ 'background-color': item.setbKColor }"
													>{{ totalRow.hoursMap[item.date] }}
												</th>
											</tr>
										</thead>
										<tbody class="row_body">
											<tr v-for="(task, index) in filteredData" :key="'task' + index">
												<td
													class="td_body"
													v-for="(item, tdindex) in dateColList"
													:key="tdindex"
													:style="{ 'background-color': item.setbKColor }"
												>
													<!-- 设置填充颜色 -->
													<div
														class="td-box-back"
														:style="{
															'background-color': task.pmtid ? '' : task.isProject ? '#edf7f3' : 'transparent',
															color: task.userName ? '#28D094' : task.isProject ? '#cfd8dc' : '',
														}"
													>
														<!-- 用户行 -->
														<span class="span-box" v-if="task.userName && task.hoursMap[item.date]">
															<span> {{ task.hoursMap[item.date] }}</span>
														</span>
														<!-- 项目行 -->
														<span class="span-box-project" v-else-if="task.isProject && task.hoursMap[item.date]">
															<span> {{ task.hoursMap[item.date] }}</span>
														</span>
														<!-- 任务行 -->
														<span
															class="span-box-task"
															v-else-if="task.pmtid && task.hoursMap[item.date]"
															@click="openDetail(task, '执行测试任务')"
															:style="{ 'background-color': item.date.includes('/') ? colorMap2[task.status] : '' }"
														>
															<el-tooltip :content="String(task.popString)" placement="bottom" effect="light">
																<span> {{ task.hoursMap[item.date] }}</span>
															</el-tooltip>
														</span>
														<span class="span-box" v-else> </span>
													</div>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</template>
				</split-pane>
			</div>
		</div>
		<!-- 任务明细弹窗 -->
		<div id="taskDialog">
			<el-dialog
				:title="dialogTitleName"
				width="75%"
				top="5vh"
				:fullscreen="true"
				custom-class="taskDialog"
				:visible.sync="dialogDetail"
				:modal-append-to-body="false"
				:modal="false"
				@close="closeDialog"
			>
				<div class="dialogContent mt20">
					<el-row>
						<el-col :span="10">
							<span style="width: 100px">任务</span>
							<el-input
								:disabled="!canEditBtn"
								v-model="detForm.taskName"
								@change="updatePlanDetailDb"
								size="small"
								style="width: 76%"
							></el-input>
						</el-col>
						<el-col :span="8"
							><span style="width: 80px">分类</span>
							<div class="colBorder">
								<el-radio-group :disabled="!canEditBtn" @change="updatePlanDetailDb" v-model="detForm.taskClassify">
									<el-radio :label="0">需求</el-radio>
									<el-radio :label="1">优化</el-radio>
									<el-radio :label="2">Bug</el-radio>
									<el-radio :label="3">杂项</el-radio>
								</el-radio-group>
							</div>
						</el-col>
						<el-col :span="6"
							><span style="width: 100px">优先级</span>
							<div class="colBorder">
								<!-- v-model="inquiryData.stage" -->
								<el-radio-group :disabled="!canEditBtn" @change="updatePlanDetailDb" v-model="detForm.taskLevel">
									<el-radio :label="0">高</el-radio>
									<el-radio :label="1">中</el-radio>
									<el-radio :label="2">低</el-radio>
								</el-radio-group>
							</div>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="3"
							><span style="width: 100px">开发产出工时</span>
							<el-input
								:disabled="!canEditBtn"
								@change="inputProductTime"
								v-model="detForm.productTime"
								size="small"
								style="width: 32%"
							></el-input>
						</el-col>
						<el-col :span="3"
							><span>难度系数</span>
							<el-input
								:disabled="!canEditBtn"
								@change="updatePlanDetailDb"
								v-model="detForm.difficulty"
								size="small"
								style="width: 42%"
							></el-input>
						</el-col>
						<el-col :span="4"
							><span>要求转测日期</span>
							<el-date-picker
								:disabled="!canEditBtn"
								@change="updatePlanDetailDb"
								style="width: 42%"
								v-model="detForm.endTime"
								value-format="timestamp"
								type="date"
								placeholder="选择要求转测日期"
								align="right"
								clearable
								editable
								size="small"
								format="MM/dd"
							></el-date-picker>
						</el-col>
						<el-col :span="4"
							><span style="width: 80px">开发者</span>
							<el-select
								:disabled="!canEditBtn"
								@change="updatePlanDetailDb"
								v-model="detForm.productUid.auid"
								size="small"
								style="width: 53%"
								placeholder="请选择开发者"
								clearable
								filterable
							>
								<el-option v-for="item in developerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
							</el-select>
						</el-col>
						<el-col :span="4"
							><span>测试产出工时</span>
							<el-input
								:disabled="!canEditBtn"
								@change="updatePlanDetailDb"
								v-model="detForm.testProductTime"
								size="small"
								style="width: 32%"
							></el-input>
						</el-col>
						<el-col :span="6"
							><span style="width: 100px">计划测试日期</span>
							<el-date-picker
								:disabled="!canEditBtn"
								@change="updatePlanDetailDb"
								style="width: 62%"
								v-model="detForm.planTestTime"
								value-format="timestamp"
								type="date"
								placeholder="选择计划测试日期"
								align="right"
								clearable
								editable
								size="small"
								format="yyyy/MM/dd"
							></el-date-picker>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="4">
							<span style="width: 100px">提出人</span>
							<el-select
								:disabled="!canEditBtn"
								@change="updatePlanDetailDb"
								v-model="detForm.mentionUid.auid"
								size="small"
								style="width: 50%"
								placeholder="请选择提出人"
								clearable
								filterable
							>
								<el-option v-for="item in implementList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
							</el-select>
						</el-col>
						<el-col :span="6"
							><span>来源客户</span>
							<el-input
								:disabled="!canEditBtn"
								@change="updatePlanDetailDb"
								v-model="detForm.customer"
								size="small"
								style="width: 35%"
							></el-input>
						</el-col>
						<el-col :span="8"
							><span style="width: 80px">Bug负责人</span>
							<el-select
								:disabled="!canEditBtn"
								@change="updatePlanDetailDb"
								v-model="detForm.bugProductUid.auid"
								size="small"
								style="width: 34%; margin-right: 0.5vw"
								placeholder="请选择开发人员"
								clearable
								filterable
							>
								<el-option v-for="item in developerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
							</el-select>
							<el-select
								:disabled="!canEditBtn"
								@change="updatePlanDetailDb"
								v-model="detForm.bugTestUid.auid"
								size="small"
								style="width: 34%"
								placeholder="请选择测试人员"
								clearable
								filterable
							>
								<el-option v-for="item in testerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
							</el-select>
						</el-col>
						<el-col :span="3"
							><span style="width: 48px">项目</span>
							<el-cascader
								:disabled="!canEditBtn"
								size="small"
								ref="elCascader"
								clearable
								style="width: 60%"
								v-model="detForm.pmcid"
								@change="changeProject"
								:options="projectList"
								:show-all-levels="false"
								:props="{
									emitPath: false,
									checkStrictly: true,
									expandTrigger: 'hover',
									children: 'selectProjectGanttChartVOS',
									label: 'classifyName',
									value: 'pmcid',
								}"
								@visible-change="elCascaderOnlick('elCascader')"
								@expand-change="elCascaderOnlick('elCascader')"
							></el-cascader>
						</el-col>
						<el-col :span="3"
							><span style="width: 35px">状态</span>
							<span
								:style="{
									'background-color': colorMap2[detForm.status],
									'line-height': '16px',
									width: '48%',
									'text-align': 'left',
									padding: '5px',
									'font-size': '14px',
								}"
							>
								{{ statusMap[detForm.status] }}
							</span>
						</el-col>
					</el-row>
					<el-row>
						<el-col :span="18">
							<span style="width: 100px">关联信息</span>
							<span>{{ relForm.relevanceTask }}</span>
						</el-col>
						<el-col :span="6">
							<span style="width: 48px">测试人</span>
							<el-select
								v-model="detForm.projectTurnCheckUid.auid"
								size="small"
								style="width: 30%"
								placeholder="请选择测试人员"
								clearable
								filterable
								:disabled="detForm.status == '4'"
								@change="updatePlanDetailDb"
							>
								<el-option v-for="item in testerList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
							</el-select>
						</el-col>
					</el-row>
					<el-row v-if="!detForm.isCompleted">
						<el-collapse accordion>
							<el-collapse-item>
								<template slot="title">
									<span style="color: #28d094; margin-right: 0.5vw; width: 95vw"
										>查看任务详情 <i class="header-icon el-icon-info"></i
									></span>
								</template>
								<wang-editor class="W100" v-if="dialogDetail" :disabled="!canEditBtn" v-model="detForm.content"></wang-editor>
							</el-collapse-item>
						</el-collapse>
					</el-row>
					<el-row v-if="!detForm.isCompleted">
						<span style="display: inline-block; font-size: 14px">
							{{ dateFormat(detForm.projectTurnTime, 'lineM') }} {{ detForm.productUid.userName }} 第{{
								detForm.sumbitNum ? detForm.sumbitNum : 1
							}}次提交转测 要求测试完成日期：{{ dateFormat(detForm.turnTestLimit, 'MD') }}
						</span>
					</el-row>
					<el-row>
						<wang-editor
							class="W100"
							v-if="!detForm.isCompleted && dialogDetail"
							v-model="trunForm.sumbitContent"
							:editorHeight="460"
						></wang-editor>
					</el-row>
					<el-row v-if="!detForm.isCompleted" style="justify-content: end; margin: 1.5vh">
						<el-col :span="6"
							><span>测试结论</span>
							<div class="colBorder colBorderMax">
								<!-- v-model="inquiryData.stage" -->
								<el-radio-group v-model="trunForm.status">
									<el-radio :label="4">测试通过</el-radio>
									<el-radio :label="5">测试不通过</el-radio>
								</el-radio-group>
							</div>
						</el-col>
						<el-col :span="4"
							><span>Bug数量</span>
							<el-input :disabled="isDisable" style="width: 50%" placeholder="单位(个)" v-model="trunForm.bugNum"> </el-input>
						</el-col>
						<el-col :span="4"
							><span>测试实际用时</span>
							<el-input style="width: 50%" placeholder="单位(h)" v-model="trunForm.actualTestTime"> </el-input>
						</el-col>

						<el-button type="primary" style="margin-left: 1vw; background-color: #28d094" @click="submitTurnTestDb"
							><span v-if="detForm.sumbitNum">第{{ detForm.sumbitNum }}次</span>提交测试报告</el-button
						>
						<el-button
							v-if="canEditBtn"
							type="warning"
							style="margin-left: 1vw; background-color: #f9a825"
							@click="updatePlanDetailDb"
							><span>修改任务详情</span>
						</el-button>
					</el-row>
					<!-- 测试报告详情 -->
					<el-row>
						<el-collapse accordion>
							<el-collapse-item v-if="detForm.isCompleted">
								<template slot="title">
									<span style="margin-right: 0.5vw; color: #28d094; font-weight: 600; width: 70vw"
										>亲爱的树字er辛苦了，该任务已测试通过，点击即可查看任务详情！</span
									>
								</template>
								<wang-editor class="W100" v-if="dialogDetail" :disabled="true" v-model="detForm.content"></wang-editor>
							</el-collapse-item>
							<el-collapse-item v-for="(item, index) in detForm.projectManagementTaskNgVOS" :key="index">
								<template slot="title">
									<span style="margin-right: 0.5vw">{{ dateFormat(item.projectTurnTime, 'lineM') }}</span>
									<span style="margin-right: 0.5vw">{{ detForm.productUid.userName }}</span>
									<span style="margin-right: 0.5vw">第{{ detForm.projectManagementTaskNgVOS.length - index }}次提交转测</span>
									<span style="margin-right: 0.5vw">{{ item.projectTurnCheck ? item.projectTurnCheck.userName : '' }}</span>
									<span style="margin-right: 0.5vw"> {{ dateFormat(item.testCheckTime, 'lineM') }} 完成测试</span>
									<span> 测试结果：</span>
									<span
										:style="{ 'margin-right': '.5vw', 'font-weight': '600', color: item.result == 4 ? '#28d094' : '#ec808d' }"
										>{{ item.result == 4 ? '通过 ' : '不通过 ' }}</span
									>
									<span>BUG数量：</span>
									<span :style="{ 'font-weight': '600', color: item.result == 4 ? '#28d094' : '#ec808d' }">{{
										item.bugNum ? item.bugNum : '0'
									}}</span>
									<span style="margin: 0 0.5vw"> 测试报告详情 <i class="header-icon el-icon-info"></i></span>
								</template>
								<wang-editor class="W100" v-if="dialogDetail" :disabled="true" v-model="item.sumbitContent"></wang-editor>
							</el-collapse-item>
						</el-collapse>
					</el-row>
				</div>
			</el-dialog>
		</div>
	</div>
</template>
<script>
import { debounce, dateFormat, deepClone, getCookie } from '@/util/tool';
import { mapGetters } from 'vuex';
import folderImg from '@/assets/img/folder.svg';

import wangEditor from '@/components/WangEditor/wangEditor';
import splitPane from 'vue-splitpane'; //拖拽分割面板组件
import DateSelect from '@/components/DateSelect/DateSelect'; //日期选择

export default {
	name: 'testManagement',
	components: {
		wangEditor,
		splitPane,
		DateSelect,
	},
	props: {
		userList: Array,
	},
	data() {
		return {
			activeTab: 'testManagement',
			queryStr: '',
			userFlag: '',
			rowData: {},
			isDisable: false,
			treeData: [],
			loadTreeList: [],
			projectList: [],
			totalRow: {
				total: '合计',
				hoursMap: '',
				workingHours: '',
				taskCount: '',
				disable: true,
			},
			// 弹窗相关
			TitleType: '',
			isShowCom: false,
			dialogDetail: false,
			dialogUpdate: false,
			dialogTitleName: '',
			showWorkPlan: false,
			showProGantt: false,
			projectNo: '',
			projectName: '',
			iconFlag: false,
			canEditBtn: false,
			// 日期相关
			defaultTime: new Date(),
			searchForm: {
				statusList: [3, 4, 5, 6],
			},
			//日期相关
			dateSelectObj: {
				startTime: null,
				endTime: null,
			},
			// 转测表单
			trunForm: {
				actualTestTime: '',
				bugNum: '',
				status: '',
				sumbitContent: '',
				remark: '',
			},
			// 明细表单
			detForm: {
				classifyName: '',
				planTestTime: '',
				actualEndTime: '',
				actualStartTime: '',
				bugProductUid: {
					auid: 0,
					userName: '',
				},
				bugTestUid: {
					auid: 0,
					userName: '',
				},
				projectTurnCheckUid: {
					auid: 0,
					userName: '',
				},
				content: '',
				customer: '',
				difficulty: 0,
				endTime: '',
				mentionUid: {
					auid: 0,
					userName: '',
				},
				pmcid: 0,
				pmid: 0,
				pmtid: 0,
				productTime: 0,
				productUid: {
					auid: 0,
					userName: '',
				},
				projectManagementTaskNgVOS: [
					{
						bugNum: 0,
						pmid: 0,
						pmtid: 0,
						pmtnid: 0,
						projectTurnCheck: {
							auid: 0,
							userName: '',
						},
						projectTurnTime: '',
						sumbitContent: '',
						testCheckTime: '',
					},
				],
				projectName: '',
				startTime: '',
				status: 0,
				taskClassify: 0,
				taskLevel: 0,
				taskName: '',
			},
			// 任务关联表单
			relForm: {
				pmid: '',
				pmtid: '',
				queryParam: '',
				relevancePmtid: '',
				relevanceTaskNo: '',
				relevanceTask: '',
			},
			editRules: {
				classifyName: [{ required: true, message: '请输入文件名', trigger: 'blur' }],
			},
			statusList: [
				{ id: 6, status: '测试延误' },
				{ id: 5, status: '测试不通过' },
				{ id: 3, status: '测试计划中' },
				{ id: 4, status: '测试通过 ' },
			],
			colorMap: {
				3: '#d7d7d7', //灰  测试计划中
				4: '#28d094', //绿 完成
				5: '#facd91', //橙 转测不通过
				6: '#ec808d', //红 测试延误
			},
			// 任务状态
			statusMap: {
				0: '延误',
				1: '计划中',
				2: '开发中',
				3: '已转测',
				4: '完成',
				5: '转测不通过',
				6: '测试延误',
			},
			colorMap2: {
				0: '#ffc9c9', //红 延误
				1: '#ebeceb', //灰 计划中
				2: '#ab47bc', //紫 开发中
				3: '#d0ebff', //蓝 已转测
				4: '#c3fae8', //绿 完成
				5: '#facd91', //橙 转测不通过
				6: '#ffc9c9', //红 测试延误
			},
			// 是否展开
			isExpanded: false,

			// 甘特图相关
			dateColList: [
				// { date: "09/01", colspan: "1" },
			],
			weekColList: [
				// { week: "一", colspan: "1" },
			],
			hoursMap: {},
			gantTableData: [],
			gantTableDataCopy: [],
			loadTableData: [],
			loadTableDataCopy: [],
			weekMap: {
				1: '一',
				2: '二',
				3: '三',
				4: '四',
				5: '五',
				6: '六',
				0: '日',
			},
		};
	},
	watch: {
		// 监听-某行数据
		queryStr(val) {
			this.$refs.workTreeRef.filter(val);
		},

		trunForm: {
			handler(oldVal, newVal) {
				if (newVal.status == '4') {
					this.isDisable = true;
				} else {
					this.isDisable = false;
				}
			},
			deep: true,
		},
		isExpanded(val) {
			this.changeExpanded(val);
		},
	},
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		filteredData() {
			return this.gantTableData.filter(item => !item.disable);
		},

		//开发人员列表(标签：开发)
		developerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('开发')) || [];
		},
		//测试人员列表(标签：测试)
		testerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('测试')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
	},
	mounted() {
		if (window.localStorage) {
			this.searchForm.statusList = JSON.parse(window.localStorage.getItem('workManagement_statusList')) || [0, 1, 2, 3, 4, 5];
		} else {
			this.searchForm.statusList = getCookie('workManagement_statusList') || [0, 1, 2, 3, 4, 5];
		}
		this.userInfos && this.getEditBtn();
	},
	methods: {
		// 修改项目
		changeProject() {
			this.$nextTick(() => {
				const nodeData = this.$refs['elCascader'].getCheckedNodes();
				if (!nodeData) return;
				this.detForm.pmcid = nodeData[0]?.data.pmcid || '';
				this.detForm.pmid = nodeData[0]?.data.pmid || '';
				this.detForm.projectName = nodeData[0]?.pathLabels[0] || '';
				this.updatePlanDetailDb();
			});
		},
		// 某个时间在某年第几周
		getWeekFalg(date) {
			if (!date) return 0;
			const YEAR = Number(this.$moment(date).format('YYYY'));
			const weekday = new Date(date).getDay() || 7; //星期几
			const temptTime = new Date(date);
			//周1+5天=周六
			temptTime.setDate(temptTime.getDate() - weekday + 1 + 5);
			let firstDay = new Date(temptTime.getFullYear(), 0, 1);
			const dayOfWeek = firstDay.getDay();
			let spendDay = 1;
			if (dayOfWeek != 0) {
				spendDay = 7 - dayOfWeek + 1;
			}
			firstDay = new Date(temptTime.getFullYear(), 0, 1 + spendDay);
			const d = Math.ceil((temptTime.valueOf() - firstDay.valueOf()) / 86400000);
			const result = Math.ceil(d / 7) + 1;
			return YEAR + '' + result;
		},
		getColList() {
			const dateColList = [];
			const weekColList = [];
			const hoursMap = {};
			const colIndex = 0;
			const oneDay = 24 * 60 * 60 * 1000;
			const startTime = this.dateSelectObj.startTime; // 表格日期范围
			const endTime = this.dateSelectObj.endTime;
			const days = (endTime - startTime) / oneDay;

			let setbKColor = ''; //设置颜色

			const nowDay = this.dateFormat(new Date().getTime(), 'MD');
			for (let i = 0; i < days; i++) {
				const timestamp = startTime + oneDay * i;
				const date = this.dateFormat(timestamp, 'MD'); //日期
				const week = this.weekMap[new Date(timestamp).getDay()]; //周几
				const weekFalg = this.getWeekFalg(timestamp);

				// 设置背景颜色
				if (week == '日') {
					setbKColor = '#ffffbb';
				} else if (date == nowDay) {
					//当前日期
					setbKColor = '#e1f5fe';
				} else {
					setbKColor = '';
				}

				dateColList.push({
					colIndex: i,
					date,
					timestamp,
					colspan: '1',
					setbKColor,
				});
				weekColList.push({
					colIndex: i,
					week,
					colspan: '1',
					setbKColor,
				});

				if (week == '日') {
					// 插入周计
					hoursMap[weekFalg] = 0;
					dateColList.push({
						colIndex: i,
						date: weekFalg,
						colspan: '1',
						setbKColor: '#e8f5e9',
						isWeekCount: 'true',
					});

					weekColList.push({
						colIndex: i,
						week: '周计',
						colspan: '1',
						setbKColor: '#e8f5e9',
					});
				}

				hoursMap[date] = 0;
			}
			this.dateColList = dateColList;
			this.weekColList = weekColList;
			this.hoursMap = hoursMap;
			this.getTreeData();
		},
		getEditorContent(content) {
			this.trunForm.sumbitContent = content;
			// console.log("getEditorContent", content); // onChange 时获取编辑器最新内容
		},
		// 获取树结构数据
		getTreeData: debounce(function (type) {
			this.treeData = [];
			const str = JSON.stringify({
				...this.dateSelectObj,
				statusList: this.searchForm.statusList,
			});
			this.$axios
				.getTestTaskManagerList(str)
				.then(res => {
					if (res.data.success) {
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.userFlag = '';
						// 递归：任务时间转换以及标识
						const totalRecur = arr => {
							arr.forEach(project => {
								project.hoursMap = deepClone(this.hoursMap);
								if (project.userName) {
									this.userFlag = project.userName;
									project.disable = false;
									if (project.selectMyDayWorkHoursCountVOS) {
										project.selectMyDayWorkHoursCountVOS.forEach(item => {
											const date = [this.dateFormat(item['day'], 'MD')];
											project.hoursMap[date] = item['workingHours'];

											// 周计
											const weekFalg = this.getWeekFalg(item['day']);
											project.hoursMap[weekFalg] = this.accAdd(project.hoursMap[weekFalg], item['workingHours']);
										});
									}
								} else {
									project.isProject = true;
									project.disable = true;
								}
								project.userFlag = this.userFlag;

								if (project.selectProjectTaskGanttChartVOList) {
									project.selectProjectTaskGanttChartVOList.forEach(task => {
										task.hoursMap = deepClone(this.hoursMap);
										task.userFlag = this.userFlag;
										task.disable = true;

										// 周计 turnTestLimit 要求测试日期
										const weekFalg = this.getWeekFalg(task.turnTestLimit);
										task.hoursMap[weekFalg] = this.accAdd(task.hoursMap[weekFalg], task.testProductTime);

										task.hoursMap[this.dateFormat(task.turnTestLimit, 'MD')] = task.testProductTime;
										task.popString = `【${task.taskNo}】${task.taskName} 要求测试日期:${this.dateFormat(
											task.turnTestLimit,
											'MD',
										)} 当前状态：${this.statusMap[task.status]}`;
										// 项目项的工时合计
										for (const key in task.hoursMap) {
											project.hoursMap[key] = this.accAdd(project.hoursMap[key], task.hoursMap[key]);
										}
									});
								}

								if (project.selectProjectGanttChartVOS) {
									totalRecur(project.selectProjectGanttChartVOS);
								}
							});
						};
						totalRecur(res.data.data);

						// 递归：数据拼接(将数据拼接在同一个孩子节点 selectProjectGanttChartVOS
						const concatTreeNode = array => {
							array.map(item => {
								if (item.selectProjectTaskGanttChartVOList && item.selectProjectTaskGanttChartVOList.length > 0) {
									if (item.selectProjectGanttChartVOS && item.selectProjectGanttChartVOS.length > 0) {
										item.selectProjectGanttChartVOS.push(...item.selectProjectTaskGanttChartVOList);
									} else {
										item.selectProjectGanttChartVOS = item.selectProjectTaskGanttChartVOList;
									}
								}
								// 进入递归循环
								if (item.selectProjectGanttChartVOS && item.selectProjectGanttChartVOS.length > 0) {
									concatTreeNode(item.selectProjectGanttChartVOS);
								}
							});
						};
						concatTreeNode(res.data.data);
						const total = {
							total: '合计',
							hoursMap: deepClone(this.hoursMap),
							workingHours: '',
							taskCount: '',
							disable: true,
						};
						res.data.data.forEach(user => {
							for (const key in user.hoursMap) {
								total.hoursMap[key] = this.accAdd(total.hoursMap[key], user.hoursMap[key]);
							}
							total.workingHours = this.accAdd(total.workingHours, user.workingHours);
							total.taskCount = this.accAdd(total.taskCount, user.taskCount);
						});
						this.totalRow = total;
						res.data.data.unshift(total);
						this.treeData = res.data.data;
						if (this.treeData && this.treeData.length > 0) {
							const treeDataCopy = deepClone(this.treeData);
							const result = this.treeToArray(treeDataCopy);
							this.$nextTick(() => {
								this.gantTableData = result;
								this.gantTableDataCopy = deepClone(this.gantTableData);
							});
						}
						// console.log("treeData", this.treeData);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('getTestTaskManagerList |' + error);
				});
		}),
		// 树型控件渲染
		renderContent(h, { node, data, store }) {
			if (!data.productUid) data.productUid = { arid: '', auid: '', userName: '' };
			if (!data.projectTurnCheck) data.projectTurnCheck = { arid: '', auid: '', userName: '' };
			const {
				taskName,
				status,
				taskNo,
				testProductTime,
				productUid,
				turnTestLimit,
				total,
				classifyName,
				userName,
				taskCount,
				workingHours,
				isProject,
			} = data;
			const color = this.colorMap[status];
			if (taskName) {
				return (
					<div class="custom-tree-node">
						<div
							class="task-content"
							on-click={e => {
								e.stopPropagation();
								this.openDetail(data, '执行测试任务');
							}}
						>
							<div class="task-info flex-align-center ">
								<span class="task-label" style={{ color }}></span>
								<div class="taskNo ellipsis">
									{taskNo} {taskName}
								</div>
							</div>
							<div class="task-time ellipsis">
								{this.dateFormat(turnTestLimit, 'MD')} {testProductTime || 0}h {productUid?.userName || ''}
							</div>
						</div>
					</div>
				);
			} else {
				return (
					<div class="custom-tree-node">
						<div class="folder-content space-between">
							<div class="folder-title">
								<img src={folderImg} class="folder-img" v-show={!total} />
								<span class="folder-name ellipsis">{classifyName || userName || total}</span>
							</div>
							<div class="folder-time">
								<span>
									{taskCount}项/{workingHours}小时
								</span>
							</div>
						</div>
					</div>
				);
			}
		},
		// 搜索框过滤节点
		filterNode(value, data) {
			console.log(value, data);
			if (!value) return true;
			const userName = data.userName || '';
			const taskName = data.taskName || '';
			const taskNo = data.taskNo || '';

			return userName.indexOf(value) !== -1 || taskName.indexOf(value) !== -1 || taskNo.indexOf(value) !== -1;
		},
		// 树型控件点击事件
		clickNodeExpand(data, node) {
			const dataCopy = this.treeToArray([deepClone(data)]);
			const temp = deepClone(this.gantTableData);
			if (node.isLeaf) return;
			//打开时展示
			temp.forEach((item, index) => {
				dataCopy.forEach(value => {
					if (dataCopy[0].userName) {
						value.isProject && item.userFlag == value.userFlag && item.pmcid == value.pmcid && (item.disable = false);
					} else {
						if (item.pmtid == value.pmtid && item.userFlag == value.userFlag) item.disable = false;
					}
				});
			});
			this.$nextTick(() => {
				this.gantTableData = temp;
			});
		},
		clickNodeCollapse(data, node) {
			this.expandAllNode(node, false);
			const dataCopy = this.treeToArray([deepClone(data)]);
			dataCopy.shift();

			const temp = deepClone(this.gantTableData);
			if (node.isLeaf) return;

			//折叠隐藏
			temp.forEach((item, index) => {
				dataCopy.forEach(value => {
					if (
						item.pmcid == value.pmcid &&
						item.userFlag == value.userFlag &&
						(item.pmtid || item.classifyName != data.classifyName)
					)
						item.disable = true;
				});
			});

			this.$nextTick(() => {
				this.gantTableData = temp;
			});
		},
		changeExpanded(flag) {
			this.expandAllNode(this.$refs.workTreeRef.store.root, flag);
			const dataCopy = this.gantTableDataCopy;
			const temp = deepClone(this.gantTableData);
			if (!flag) {
				//折叠时删除数组
				temp.forEach((item, index) => {
					dataCopy.forEach(value => {
						if (
							item.pmcid == value.pmcid &&
							item.userFlag == value.userFlag &&
							(item.pmtid || item.classifyName != dataCopy.classifyName)
						)
							item.disable = true;
					});
				});
				this.$nextTick(() => {
					this.gantTableData = temp;
				});
			} else {
				//打开时插入数组
				temp.forEach((item, index) => {
					if (!index) {
						temp[0].expanded = false; //目录
					}
					dataCopy.forEach(value => {
						if (item.pmcid == value.pmcid && item.userFlag == value.userFlag) item.disable = false;
					});
				});
				this.$nextTick(() => {
					this.gantTableData = temp;
				});
			}
		},
		// 打开或收缩所有节点
		expandAllNode(node, flag) {
			for (let i = 0; i < node.childNodes.length; i++) {
				node.childNodes[i].expanded = flag;
				if (node.childNodes[i].childNodes.length > 0) {
					this.expandAllNode(node.childNodes[i], flag);
				}
			}
		},
		// 树扁平化数组
		treeToArray(source) {
			// 该方法是将树结构扁平化
			const res = [];
			source.forEach(item => {
				res.push(item);
				item.selectProjectGanttChartVOS && res.push(...this.treeToArray(item.selectProjectGanttChartVOS));
			});
			return res.map(item => {
				if (item.selectProjectGanttChartVOS) {
					delete item.selectProjectGanttChartVOS;
				}
				return item;
			});
		},
		// 获取项目列表
		getProjectList() {
			if (this.projectList.length > 0) {
				return;
			}
			this.projectList = [];
			const str = JSON.stringify({
				statusList: [0, 1, 2, 3, 4, 5, 6, 7, 9],
			});
			this.$axios
				.selectAllONGoingProject(str)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item.selectProjectGanttChartVOS[0].classifyName = item.projectName;
							this.projectList.push(item.selectProjectGanttChartVOS[0]);
						});
						// this.projectList = res.data.data;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectProjectGanttChart |' + error);
				});
		},
		// 数值加法精准度设置
		accAdd(arg1, arg2) {
			let r1, r2, m;
			try {
				r1 = arg1.toString().split('.')[1].length;
			} catch (e) {
				r1 = 0;
			}
			try {
				r2 = arg2.toString().split('.')[1].length;
			} catch (e) {
				r2 = 0;
			}
			m = Math.pow(10, Math.max(r1, r2));
			m *= 10;
			// if (this.requestType == 1) {
			return parseInt(arg1 * m + arg2 * m) / m;
			// } else {
			//   return parseInt(arg1 * m + arg2 * m) / m;
			// }
		},
		// 获取修改权限
		getEditBtn() {
			const roles = this.userInfos?.adminUserVO.adminRoleVOS;
			const canEdit = ['1', '10', '12', '13', '14']; // 权限 1超级管理员 10测试员 12项目经理 13技术经理 14测试经理
			// this.userInfos?.adminUserVO.adminRoleVOS?.forEach(item => {
			//   roles.push(item.arid)
			// })
			// if (canEdit.find(item => roles.includes(item))) {
			//   this.canEditBtn = true;
			// }

			const flag = roles.find(({ arid }) => canEdit.includes(arid));
			this.canEditBtn = flag ? true : false;
			//console.log({ roles }, { canEdit }, this.canEditBtn);
		},
		// 打开任务明细
		openDetail(data, type) {
			const str = JSON.stringify({
				pmtid: data.pmtid,
			});
			this.$axios
				.selectTaskDetailByPmtid(str)
				.then(res => {
					if (res.data.success) {
						this.detForm = deepClone(res.data.data);
						// this.canEditBtn = !res.data.data.taskAuth;
						this.trunForm.actualTestTime = res.data.data.testProductTime;
						this.trunForm.remark = res.data.data.remark;
						if (this.detForm.projectManagementTaskNgVOS && this.detForm.projectManagementTaskNgVOS.length > 0) {
							this.detForm.sumbitNum = this.detForm.projectManagementTaskNgVOS.length + 1;
						} else {
							this.detForm.sumbitNum = 0;
						}
						// 项目完成状态
						if (data.status == '4') this.detForm.isCompleted = true;
						// 创建人信息(标题)
						if (this.detForm.createUserInfo && this.detForm.createUserInfo.userName) {
							const taskNo = this.detForm.taskNo;
							const taskName = this.detForm.taskName;
							const createUser = this.detForm.createUserInfo.userName;
							this.dialogTitleName = `【任务】${taskNo} | ${taskName} 【创建人】${createUser}`;
						} else {
							this.dialogTitleName = type;
						}

						// 关联信息
						if (this.detForm.relevanceTaskVOs && this.detForm.relevanceTaskVOs.length > 0) {
							const taskList = [];
							this.detForm.relevanceTaskVOs.forEach(item => {
								taskList.push(
									`${item.productUid.userName} | 
      ${item.taskName}|${this.dateFormat(item.endTime)} |当前状态：${this.statusMap[item.status]}`,
								);
							});
							this.relForm.relevanceTask = taskList.toString();
							this.relForm.relevanceTaskNo = this.detForm.relevanceTaskVOs[0].taskNo;
						}
						this.dialogDetail = true;
						this.dialogTitleName = type;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTaskDetailByPmtid |' + error);
				});
		},
		// 提交测试任务
		submitTurnTestDb: debounce('submitTurnTest'),
		submitTurnTest() {
			const str = JSON.stringify({
				actualTestTime: this.trunForm.actualTestTime,
				pmid: this.detForm.pmid,
				pmtid: this.detForm.pmtid,
				bugNum: this.trunForm.bugNum,
				status: this.trunForm.status,
				sumbitContent: this.trunForm.sumbitContent,
			});
			this.$axios
				.taksTestSumbit(str)
				.then(res => {
					if (res.data.success) {
						this.trunForm.sumbitContent = '';
						this.$succ('操作成功!');
						this.closeDialog();

						this.getTreeData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('taksTestSumbit |' + error);
				});
		},
		// 输入开发产出工时
		inputProductTime() {
			if (this.detForm.productTime) {
				this.detForm.testProductTime = Number(this.detForm.productTime).toFixed(1);
				this.detForm.testProductTime = Number(this.detForm.productTime / 4).toFixed(1);
				this.updatePlanDetailDb();
			}
		},
		// 添加/修改任务详情
		updatePlanDetailDb: debounce('updatePlanDetail'),
		updatePlanDetail() {
			// if (!this.canEditBtn) {
			// 	this.$message.warning("抱歉，您暂无权限对此任务进行修改!");
			// 	return;
			// }
			const str = JSON.stringify({
				actualEndTime: this.detForm.actualEndTime,
				actualStartTime: this.detForm.actualStartTime,
				bugProductUid: this.detForm.bugProductUid.auid,
				bugTestUid: this.detForm.bugTestUid.auid,
				projectTurnTest: this.detForm.projectTurnCheckUid.auid,
				content: this.detForm.content,
				customer: this.detForm.customer,
				difficulty: this.detForm.difficulty,
				endTime: this.detForm.endTime,
				mentionUid: this.detForm.mentionUid.auid,
				pmcid: this.detForm.pmcid,
				pmid: this.detForm.pmid,
				pmtid: this.detForm.pmtid,
				productTime: this.detForm.productTime,
				productUid: this.detForm.productUid.auid,
				projectName: this.detForm.projectName,
				startTime: this.detForm.startTime,
				status: this.detForm.status,
				taskClassify: this.detForm.taskClassify,
				taskLevel: this.detForm.taskLevel,
				taskName: this.detForm.taskName,
				testProductTime: this.detForm.testProductTime,
				planTestTime: this.detForm.planTestTime,
				relevancePmtid: this.detForm.relevancePmtid,
			});
			this.$axios
				.updateProjectTask(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						// this.closeDialog();
						// this.clickNode(0); //无点击操作
						// this.getTreeData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addProjectTask |' + error);
				});
		},
		// 关闭弹窗
		closeDialog() {
			this.dialogUpdate = false;
			this.dialogDetail = false;
			// this.getTreeData();
			setTimeout(() => {
				this.detForm = {
					classifyName: '',
					content: '',
					actualEndTime: '',
					actualStartTime: '',
					bugProductUid: '',
					bugTestUid: '',
					projectTurnCheckUid: '',
					content: '',
					customer: '',
					difficulty: '',
					endTime: '',
					mentionUid: '',
					pmcid: '',
					pmid: '',
					pmtid: '',
					productTime: '',
					productUid: '',
					projectName: '',
					startTime: '',
					status: 0,
					taskClassify: 0,
					taskLevel: 0,
					taskName: '',
					testTime: '',
				};
				this.trunForm = {
					actualTestTime: '',
					bugNum: '',
					status: '',
					sumbitContent: '',
					remark: '',
				};
				this.relForm = {
					pmid: '',
					pmtid: '',
					queryParam: '',
					relevancePmtid: '',
					relevanceTaskNo: '',
					relevanceTask: '',
				};
			}, 300);
		},
		// 联级选择器配置
		elCascaderOnlick(refObj, flag) {
			const that = this;
			if (flag === undefined) {
				setTimeout(function () {
					document.querySelectorAll('.el-cascader-node__label').forEach(el => {
						el.onclick = function () {
							this.previousElementSibling.click();
							that.$refs[refObj].dropDownVisible = false;
						};
					});
					document.querySelectorAll('.el-cascader-panel .el-radio').forEach(el => {
						el.onclick = function () {
							that.$refs[refObj].dropDownVisible = false;
						};
					});
				}, 100);
			} else {
			}
		},

		//日期format
		dateFormat: dateFormat,
	},
};
</script>

<style lang="scss">
#testManagement {
	width: 100%;
	overflow: hidden;
	position: inherit;
	.el-input.is-disabled .el-input__inner {
		color: #1e9d6f;
	}

	.el-checkbox {
		margin-right: 10px;
	}

	.el-checkbox__label {
		display: inline-block;
		padding-left: 5px;
		line-height: initial;
		font-size: 14px;
	}

	.dialogContent {
		font-size: 16px;
		// max-height: 77vh;
		overflow-y: auto;

		.el-row {
			display: flex;
			align-items: center;
			margin-bottom: 10px;
			margin-left: 14px;
		}

		.el-col {
			display: flex;
			align-items: center;

			> span {
				margin-right: 0.5vw;
			}
		}

		.colBorder {
			display: flex;
			align-items: center;
			border-radius: 4px;
			border: 1px solid #dcdfe6;
			padding: 6px 8px;

			.el-radio-button__inner,
			.el-radio-group {
				font-size: 16px;
				display: inline-block;
				line-height: none;
				vertical-align: middle;
			}
		}
	}

	.el-dialog__footer {
		padding: 0px 45px 20px;
		text-align: right;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
	}
	.left {
		width: 100%;
		box-sizing: border-box;
		padding: 0.5vw 0;
	}

	.right {
		overflow-x: auto;
		width: 100%;
		box-sizing: border-box;
		min-height: 74vh;
		// border-left: 1px solid #d7d7d7;
		padding: 0.5vw;

		.GantTableBox {
			width: 100%;
			height: 100%;
			// padding-top: .5vh;
			box-sizing: border-box;
			position: relative;

			.tableContent {
				width: 100%;
				height: 100%;
			}

			.gantTable {
				.td_body {
					height: 28px;
					text-align: center;

					.td-box-back {
						border: 1px solid #f2f2f2;
						border-radius: 10px;
						background-color: #f2f2f2;
						color: #7f7f7f;
						font-size: 13px;
						font-weight: 600;

						.span-box {
							display: inline-block;
							height: inherit;
						}

						.span-box-add {
							display: inline-block;
							height: inherit;
							color: #f2f2f2;

							&:hover {
								background: #2ba075;
								width: 100%;
								border: 1px solid #f2f2f2;
								border-radius: 10px;
								cursor: pointer;
							}
						}

						.span-box-updateTime {
							display: inline-block;
							height: inherit;
							color: #e2e2e2;

							&:hover {
								background: #2ba075;
								width: 100%;
								border: 1px solid #f2f2f2;
								border-radius: 10px;
								cursor: pointer;
							}
						}

						.span-box-project {
							display: inline-block;
							height: inherit;
							width: 100%;
							border: 1px solid #f2f2f2;
							border-radius: 10px;
							//   &:hover {
							//    cursor: pointer;
							//color: #1e9d6f;
							//}
						}

						.span-box-task {
							display: inline-block;
							height: inherit;
							width: 100%;
							border: 1px solid #f2f2f2;
							border-radius: 10px;

							&:hover {
								cursor: pointer;
								color: #1e9d6f;
							}
						}
					}
				}

				//固定头部
				table-layout: fixed;

				tr:first-child th {
					border-top: 1px solid #d7d7d7;
				}

				th {
					border-left-width: 0;
					border-right: 1px solid #d7d7d7;
					border-bottom: 1px solid #d7d7d7;
					height: 22px;
					empty-cells: show;
					line-height: 22px;
					padding: 0 5px 0 5px;
					background-color: #f2f2f2;
					vertical-align: middle;
					overflow: hidden;
					outline-width: 0;
					white-space: nowrap;
					border-collapse: separate;
					border-spacing: 0;
					box-sizing: content-box;
				}

				th:first-child {
					border-left: 1px solid #d7d7d7;
				}

				.row_head {
					background-color: #f2f2f2;
					color: #7f7f7f;
					font-size: 13px;
					min-width: 30px;
				}

				.row_head_sum {
					background-color: #f2f2f2;
					color: #7f7f7f;
					font-size: 13px;
					min-width: 30px;
					height: 26px !important;
				}
			}
		}
	}
}
</style>
