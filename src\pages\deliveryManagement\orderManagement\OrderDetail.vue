<template>
	<div class="OrderDetail">
		<el-dialog
			:visible.sync="dialogEdit"
			width="1000px"
			top="10vh"
			append-to-body
			:close-on-click-modal="false"
			@close="closeDialog"
		>
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" :rules="editRules" label-width="100px" label-position="left">
				<el-form-item label="分销/代理" prop="twid" label-width="130px">
					<el-select
						class="W30"
						v-model="editForm.twid"
						placeholder="分销/代理"
						@change="queryUserList('clean')"
						clearable
						filterable
					>
						<el-option v-for="item in teamWorkList" :key="item.twid" :label="item.twName" :value="item.twid"> </el-option>
					</el-select>
				</el-form-item>
				<div class="flex-align-center">
					<el-form-item class="W65 mr20" label="公司工商注册名称" label-width="130px" prop="registeredBusinessName">
						<el-input placeholder="客户工商注册名称" v-model="editForm.registeredBusinessName"></el-input>
					</el-form-item>
					<el-form-item label="来源" label-width="50px" prop="channel">
						<el-select class="W100" v-model="editForm.channel" placeholder="来源" clearable filterable>
							<el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
						</el-select>
					</el-form-item>
				</div>

				<el-row>
					<el-col :span="10">
						<el-form-item label="订单类型" prop="type">
							<el-radio-group v-model="editForm.type">
								<el-radio :label="1">合同</el-radio>
								<el-radio :label="2">直购</el-radio>
								<el-radio :label="3">续费</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col v-if="editForm.type == 3" :span="7">
						<el-form-item label="续费日期至" prop="teamDueDate">
							<el-date-picker
								class="W90"
								v-model="editForm.teamDueDate"
								value-format="timestamp"
								type="date"
								placeholder="选择日期"
							></el-date-picker>
						</el-form-item>
					</el-col>
					<el-col :span="editForm.type == 3 ? 7 : 14">
						<el-form-item label="上传文件" prop="contractFile">
							<el-upload
								v-if="!editForm.contractFile"
								action=""
								accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
								:show-file-list="false"
								:http-request="uploadFile"
							>
								<el-button type="primary" size="medium" class="el-icon-upload"> 上传</el-button>
							</el-upload>
							<FilePopover v-else :url="editForm.contractFile" :content="editForm.contractFile" />
						</el-form-item>
					</el-col>
				</el-row>

				<el-form-item label="付款标志" prop="sign">
					<el-radio-group v-model="editForm.sign">
						<el-radio :label="1">个人</el-radio>
						<el-radio :label="2">对公</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-row :gutter="20">
					<el-col :span="6">
						<span class="label-required">业务顾问</span>
						<el-select class="mt10" v-model="editForm.salesman" placeholder="请选择业务顾问" clearable filterable>
							<el-option disabled v-show="!editForm.twid" label="请选择对应分销/代理" value=" "></el-option>
							<el-option
								v-show="editForm.twid"
								v-for="item in salesmanList"
								:key="item.auid"
								:label="item.userName"
								:value="item.auid"
							>
							</el-option>
						</el-select>
					</el-col>
					<el-col :span="6">
						<span class="label-required">实施顾问</span>
						<el-select class="mt10" v-model="editForm.implement" placeholder="请选择实施顾问" clearable filterable>
							<el-option disabled v-show="!editForm.twid" label="请选择对应分销/代理" value=" "></el-option>
							<el-option
								v-show="editForm.twid"
								v-for="item in implementList"
								:key="item.auid"
								:label="item.userName"
								:value="item.auid"
							>
							</el-option>
						</el-select>
					</el-col>
					<el-col :span="6">
						<span>咨询</span>
						<el-select class="mt10" v-model="editForm.consult" placeholder="请选择咨询员" clearable filterable>
							<el-option disabled v-show="!editForm.twid" label="请选择对应分销/代理" value=" "></el-option>
							<el-option
								v-show="editForm.twid"
								v-for="item in consultList"
								:key="item.auid"
								:label="item.userName"
								:value="item.auid"
							>
							</el-option>
						</el-select>
					</el-col>
					<el-col :span="6"> </el-col>
				</el-row>

				<el-row :gutter="20" style="margin-top: 2vh">
					<el-col :span="6"><span>产品</span></el-col>
					<el-col :span="6"><span>数量</span></el-col>
					<el-col :span="6"><span>金额(元)</span></el-col>
				</el-row>
				<el-row :gutter="20" v-for="(stageItem, index) in editForm.stageCompleteInfoDTOS" :key="index" class="mt10">
					<el-col :span="6">
						<el-select v-model="stageItem.name" placeholder="请选择产品" clearable filterable class="W100">
							<el-option
								v-for="productionItem in productionList"
								:key="productionItem.value"
								:label="productionItem.name"
								:value="productionItem.name"
							>
								<span style="float: left">{{ productionItem.name }}</span>
								<span style="float: right; color: #8492a6; font-size: 13px">{{ productionItem.classify }}</span>
							</el-option>
						</el-select>
					</el-col>
					<el-col :span="6"><el-input placeholder="请输入" v-model="stageItem.qty"></el-input></el-col>
					<el-col :span="6"><el-input placeholder="请输入" v-model="stageItem.amount"></el-input></el-col>
					<el-col :span="4">
						<el-button type="text" @click="deleteRow(editForm.stageCompleteInfoDTOS, index, stageItem)">删除</el-button>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="7">
						<el-button type="text" @click="addRow(editForm.stageCompleteInfoDTOS)">+增加一行</el-button>
					</el-col>
					<el-col :span="7">
						<span style="line-height: 40px">合计：</span>
					</el-col>
					<el-col :span="7">
						<span style="line-height: 40px">{{ getTotalAmount(editForm.stageCompleteInfoDTOS) }}</span>
					</el-col>
				</el-row>

				<el-form-item label="备注" prop="remark" label-width="50px">
					<el-input
						v-model="editForm.remark"
						placeholder="请输入内容"
						type="textarea"
						:autosize="{ minRows: 3, maxRows: 6 }"
					></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveEdit">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';
import { sourceList, sourceMap } from '@/assets/js/inquirySource.js'; // 来源数据

export default {
	name: 'OrderDetail',
	components: {
		FilePopover,
	},
	props: {},
	data() {
		return {
			sourceList,
			sourceMap, // 来源列表
			dialogEdit: false,
			editForm: {
				twid: '',
				registeredBusinessName: '',
				type: '',
				sign: '',
				salesman: '',
				implement: '',
				consult: '',
				talktrade: '',
				stageCompleteInfoDTOS: [],
				teamDueDate: '',
				contractFile: '',
				remark: '',
				channel: '',
			},
			editRules: {
				// twid: [{ required: true, message: '分销/代理', trigger: 'change' }],
			},
			productionList: [], //产品列表

			userList: [],
			dialogTitle: '详情',
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'teamWorkList']), //当前登录用户信息（含团队/菜单/权限等）
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
		//咨询人员列表(标签：咨询)
		consultList() {
			return this.userList?.filter(user => user?.userLabel?.includes('咨询')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryProductionList();
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 打开弹窗
		openDialog(title, { omid }) {
			this.$axios
				.selectOrderManagementDetail(JSON.stringify({ omid }))
				.then(res => {
					if (res.data.success) {
						this.editForm = { ...this.editForm, ...res.data.data };
						this.editForm.stageCompleteInfoDTOS = res.data.data.stageCompleteInfoVOS;
						this.editForm.salesman = res.data.data?.salesman?.auid || '';
						this.editForm.implement = res.data.data?.implement?.auid || '';
						this.editForm.consult = res.data.data?.consult?.auid || '';
						this.editForm.talktrade = res.data.data?.talktrade?.auid || '';

						// console.log(this.editForm);
						this.queryUserList();

						this.dialogEdit = true;
						this.dialogTitle = title;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectOrderManagementDetail |' + error);
				});
		},
		// 关闭
		closeDialog() {
			this.dialogEdit = false;
			this.editForm = _.resetValues(this.editForm);
			this.$emit('close');
		},
		// 保存
		saveEdit() {
			if (!this.editForm.salesman) {
				this.$message.warning('请选择业务顾问后再保存！');
				return;
			}
			if (!this.editForm.implement) {
				this.$message.warning('请选择实施顾问后再保存！');
				return;
			}
			// dmsid = this.editForm.omid;
			this.$axios
				.addOrderManagement(JSON.stringify({ ...this.editForm }))
				.then(res => {
					if (res.data.success) {
						this.updateProductionList();
						this.closeDialog();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addOrderManagement |' + error);
				});
		},
		// 添加产品
		addRow(stageCompleteInfoDTOS) {
			let stageCompleteInfo = {
				amount: '',
				name: '',
				qty: '',
			};
			if (this.dialogTitle == '编辑订单') {
				stageCompleteInfo = {
					amount: '',
					name: this.productionList[0].name ? this.productionList[0].name : '无',
					sciid: '',
					dmsid: this.editForm.dmsid || this.editForm.omid,
					qty: '',
				};
			}
			stageCompleteInfoDTOS.push(stageCompleteInfo);
		},
		// 删除产品
		deleteRow(stageCompleteInfoDTOS, index, item) {
			this.deleteProductionList(item);
			stageCompleteInfoDTOS.splice(index, 1);
		},
		deleteProductionList(item) {
			if (!(item && item.sciid && this.dialogTitle == '编辑订单')) {
				return;
			}

			this.editForm.twid = this.teamWorkList.length == 1 ? this.teamWorkList[0].twid : this.editForm.twid;
			const str = JSON.stringify({
				amount: item.amount,
				dmsid: this.editForm.dmsid,
				name: item.name,
				qty: item.qty,
				sciid: item.sciid,
			});
			this.$axios
				.deleteStageCompleteInfo(str)
				.then(res => {
					if (res.data.success) {
						this.openDialog(this.editForm.omid, '编辑订单');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('deleteStageCompleteInfo |' + error);
				});
		},
		// 修改产品列表
		updateProductionList() {
			this.editForm.twid = this.teamWorkList.length == 1 ? this.teamWorkList[0].twid : this.editForm.twid;
			const dmsid = this.editForm.dmsid || this.editForm.omid;

			const str = JSON.stringify({
				dmsid,
				stageCompleteDetailDTOS: this.editForm.stageCompleteInfoDTOS,
				twid: this.editForm.twid,
			});
			this.$axios
				.uploadStageCompleteInfo(str)
				.then(res => {
					if (res.data.success) {
						// this.openDialog(this.editForm.omid, '编辑订单')
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('uploadStageCompleteInfo |' + error);
				});
		},
		// 电子合同上传
		uploadFile(item) {
			const self = this;
			const isLt10M = item.file.size / 1024 / 1024 < 10;
			if (!isLt10M) {
				self.$message.warning('上传文件的大小不能超过 10MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', item.file);

			self.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						self.editForm.contractFile = res.data.data.path;
					} else {
						self.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					self.$message.warning(error.message);
				});
		},
		// 查询用户清单
		queryUserList() {
			this.editForm.twid = this.teamWorkList.length == 1 ? this.teamWorkList[0].twid : this.editForm.twid;
			if (!this.editForm.twid) return;
			const str = JSON.stringify({ twid: this.editForm.twid, counselor: '' });
			this.$axios
				.selectTeamworkUser(str)
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTeamworkUser |' + error);
				});
		},
		// 获取产品列表
		queryProductionList() {
			const str = JSON.stringify({
				pageNum: '',
				pageSize: '',
				query: '',
			});
			this.$axios
				.productList(str)
				.then(res => {
					if (res.data.success) {
						this.productionList = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('productList |' + error);
				});
		},
		// 合计
		getTotalAmount(stageCompleteInfoDTOS) {
			let totalAmount = 0;
			if (stageCompleteInfoDTOS) {
				stageCompleteInfoDTOS.map(item => {
					totalAmount = _.accAdd(totalAmount, item.amount);
				});
			}
			return totalAmount;
		},
	},
};
</script>
