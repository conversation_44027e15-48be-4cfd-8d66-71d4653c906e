<template>
	<div class="DeliveryDashboard">
		<!-- 积分记录 -->
		<PointsLogList
			v-if="showMap.PointsLogList"
			ref="PointsLogList"
			isMoveCom
			@getTableOptions="tableOptions = $event"
			@openDetail="openDetail"
		/>
		<!-- ---------------报表组件----------------- -->
		<!-- 询盘表格 -->
		<InquiryTable
			v-if="showMap.InquiryTable"
			isMoveCom
			ref="InquiryTable"
			@openDetail="openInquiryDetail"
			@getOceanData="oceanData = $event"
			@getTableOptions="tableOptions = $event"
			@closeMove="queryTableData(1)"
			@openOcean="openOceanTable"
			@openImport="openImport"
			@openExport="openExport"
		/>

		<!-- 公海 -->
		<OceanTable
			v-if="showMap.OceanTable"
			ref="OceanTable"
			:oceanData="oceanData"
			@openDetail="openInquiryDetail"
			@close="
				$refs.InquiryTable.queryTableData();
				isPublicInquiry = false;
			"
		/>
		<!-- 合同表格 -->
		<ContractTable
			v-if="showMap.ContractTable"
			isMoveCom
			ref="ContractTable"
			@openInquiryDetail="openInquiryDetail"
			@openContract="openContractDetail"
			@getTableOptions="tableOptions = $event"
			@openExport="openExport"
		/>
		<!-- 交付详情 -->
		<DeliveryDetail
			v-if="showMap.DeliveryDetail"
			ref="DeliveryDetail"
			:userList="userList"
			@close="
				queryTableData(1);
				$refs?.DeliveryOverview?.queryTableData('init');
			"
		/>
		<!-- 交付项目总览 -->
		<DeliveryOverview
			v-if="showMap.DeliveryOverview"
			isMoveCom
			ref="DeliveryOverview"
			@openDetail="openDeliveryDetail"
			@getTableOptions="tableOptions = $event"
			@closeMove="queryTableData(1)"
		/>
		<!-- 到期管理 -->
		<DeliveryList
			v-if="showMap.DeliveryList"
			isMoveCom
			ref="DeliveryList"
			@openDetail="openDeliveryDetail"
			@closeMove="queryTableData(1)"
		/>
		<!-- 人员计划 -->
		<DeliverySchedule v-if="showMap.DeliverySchedule" isMoveCom ref="DeliverySchedule" @closeMove="queryTableData(1)" />

		<!-- 客户健康度管理 -->
		<ActivityRating v-if="showMap.ActivityRating" isMoveCom ref="ActivityRating" @closeMove="queryTableData(1)" />
		<!-- 差旅报销 -->
		<ExpenseReimbursement
			v-if="showMap.ExpenseReimbursement"
			isMoveCom
			ref="ExpenseReimbursement"
			@closeMove="queryTableData(1)"
		/>
		<!-- 团队详情 -->
		<TeamDetail v-if="showMap.TeamDetail" ref="TeamDetail" @refresh="queryTableData(1)" />

		<!-- 需求管理 -->
		<DemandManagement v-if="showMap.DemandManagement" isMoveCom ref="DemandManagement" @closeMove="queryTableData(1)" />
		<!-- 需求详情 -->
		<DemandManagementDetail
			v-if="showMap.DemandManagementDetail"
			ref="DemandManagementDetail"
			@close="queryTableData(1)"
			@openProject="openDetail('ProjectDetail', $event)"
		/>
		<!-- 项目详情 -->
		<ProjectDetail v-if="showMap.ProjectDetail" ref="ProjectDetail" @close="queryTableData(1)" />

		<!--  --- 详情组件（注意：组件之间会混合调用重叠或用z-index控制） ---  -->
		<!-- 询盘详情 -->
		<InquiryDetail
			v-if="showMap.InquiryDetail"
			ref="InquiryDetail"
			:isPublicInquiry="isPublicInquiry"
			:inquiryOptions="tableOptions"
			@openContract="openContractDetail"
			@close="
				queryTableData(1);
				$refs?.InquiryTable?.queryTableData('init');
			"
		/>
		<!-- 合同详情 -->
		<ContractDetail
			v-if="showMap.ContractDetail"
			ref="ContractDetail"
			:contractOptions="tableOptions"
			@close="
				queryTableData(1);
				$refs?.ContractTable?.queryTableData('init');
			"
		/>

		<!-- --------------------- 通用组件 --------------------------- -->
		<!-- 数据导出弹窗 -->
		<ExportTable v-if="showMap.ExportTable" ref="ExportTable" />
		<!-- 导入弹窗 -->
		<ImportTable v-if="showMap.ImportTable" ref="ImportTable" @refresh="$refs.InquiryTable.queryTableData()" />

		<BaseLayout :showHeader="false">
			<template #main>
				<div class="dashboard-wrapper">
					<!-- 顶部 -->
					<DashboardCard :dataInfoList="dataInfoList" @openLogList="openLogList" @refresh="queryTableData('refresh')" />

					<!-- 主干 -->
					<div class="content-wrapper">
						<component
							:class="`content-item-${index + 1}`"
							v-for="(tableInfo, index) in tableList"
							:key="tableInfo.id"
							:is="'TableCom'"
							:tableInfo="tableInfo"
							@getTableOptions="tableOptions = $event"
							@openDetail="openDetail"
							@openList="openList"
						/>
					</div>
				</div>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
/* 通用 */
import DashboardCard from '../DashboardCard.vue'; //数据卡片
import TableCom from './DeliveryTableCom.vue'; //表格
import ExportTable from '@/components/ExportTable'; //导出组件
import ImportTable from '@/components/ImportTable'; //数据导入
import PointsLogList from '../PointsLogList.vue'; //积分记录

/* 询盘相关组件 */
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryTable from '@/pages/inquiryManagement/inquiryAndDocumentary/inquiryTable'; //询盘表格
import OceanTable from '@/pages/inquiryManagement/inquiryAndDocumentary/oceanTable.vue'; //公海列表

/* 合同/交付相关组件 */
import ContractTable from '@/pages/deliveryManagement/contractManagement/ContractTable.vue'; //合同表格
import ContractDetail from '@/pages/deliveryManagement/contractManagement/contractDetailCom.vue'; //合同明细
import DeliveryDetail from '@/pages/deliveryManagement/deliveryManagement/components/deliveryDetailCom.vue'; //交付明细
import DeliveryOverview from '@/pages/deliveryManagement/deliveryManagement/deliveryOverview.vue'; //交付项目总览
import DeliveryList from '@/pages/deliveryManagement/deliveryManagement/deliveryList.vue'; //交付项目总览
import DeliverySchedule from '@/pages/deliveryManagement/deliveryManagement/deliverySchedule'; //人员计划

/* 客户管理/团队/三色灯相关组件 */
import ActivityRating from '@/pages/customerManagement/activityRating.vue'; //健康评分
import TeamDetail from '@/pages/teamManagement/teamDataMain/TeamDetail'; //团队

/* 差旅相关组件 */
import ExpenseReimbursement from '@/pages/travelManagement/ExpenseReimbursement.vue'; //差旅报销

/* 需求管理 */
import DemandManagement from '@/pages/developmentManagement/demandManagement/DemandManagement.vue'; //需求管理
import DemandManagementDetail from '@/pages/developmentManagement/demandManagement/DemandManagementDetail.vue'; //需求管理
/* 项目管理 */
import ProjectDetail from '@/pages/developmentManagement/projectManagement/projectManagement/projectDetail'; //项目明细

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DashboardCard,
		TableCom,
		ExportTable,
		ImportTable,
		PointsLogList,

		InquiryDetail,
		InquiryTable,
		OceanTable,

		ContractTable,
		ContractDetail,
		DeliveryDetail,
		DeliveryOverview,
		TeamDetail,

		DeliveryList,
		DeliverySchedule,
		ActivityRating,
		ExpenseReimbursement,

		DemandManagement,
		DemandManagementDetail,
		ProjectDetail,
	},

	name: 'DeliveryDashboard', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			// 顶部数据卡片
			dataInfoList: [
				{ desc: '本月积分未排名📄', name: 'monthlyPointsSum', data: 0, class: 'bg-green' },
				{ desc: '本月已扣分📑', name: 'monthlyDeductPoints', data: 0, class: 'bg-red' },
				{ desc: '本月新增项目(万元)', name: 'monthlyNewDeliverAmount', data: 0, class: 'bg-blue' },
				{ desc: '本月完成项目(万元)', name: 'monthlyCompleteDeliverAmount', data: 0, class: 'bg-blue' },
				{ desc: '项目池总额(万元)', name: 'incompleteDeliverAmount', data: 0, class: 'bg-blue' },
				{ desc: '本月新增客户数', name: 'monthlyNewClient', data: 0, class: 'bg-blue' },
				{ desc: '本月流失客数', name: 'monthlyClientLost', data: 0, class: 'bg-red' },
				{ desc: '当前留存客数', name: 'totalActiveClient', data: 0, class: 'bg-blue' },
				{ desc: '本月收入(元)', name: 'monthlyCommissionAmount', data: 0, class: 'bg-orange' },
			],
			// 主干各个表格
			tableList: [
				{
					id: 'pointsLogList',
					title: '积分/扣分记录',
					// subTitle: '近三天',
					badgeNum: 0,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '检查时间', colNo: 'pointsDate', align: 'center', width: '90' },
						{ colName: '工作项', colNo: 'operation', align: 'left', width: '' },
						{ colName: '记分', colNo: 'points', align: 'right', width: '50' },
						// { colName: '质量评价', colNo: 'auditStatus', align: 'left', width: '' },
						// { colName: '', colNo: 'xxxNo', align: 'left', width: '' },
					],
				},

				{
					id: 'incompleteConsultingList',
					title: '未完成的咨询',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '询盘管理',
					tableColumn: [
						{ colName: '询盘编号', colNo: 'number', align: 'left', width: '' },
						{ colName: '客户称呼', colNo: 'customerName', align: 'left', width: '' },
						{ colName: '时间', colNo: 'createTime', align: 'center', width: '' },
						{ colName: '区域', colNo: 'region', align: 'left', width: '' },
					],
				},
				{
					id: 'incompleteDeliverySchedule',
					title: '未完成的项目',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '项目总览',
					tableColumn: [{ colName: '项目进度', colNo: 'deliveryScheduleStateVOS', align: 'left', width: '' }],
				},
				{
					id: 'selectDemandSuperVisionListVOS',
					title: '需求清单',
					badgeNum: 0,
					isDataList: false,
					data: [],
					button: '需求管理',
					tableColumn: [
						{ colName: '提交人', colNo: 'submissionName', align: 'left', width: '110' },
						{ colName: '提交时间', colNo: 'submissionTime', align: 'left', width: '100' },
						{ colName: '需求', colNo: 'demandDocumentName', align: 'left', width: '' },
						{ colName: '人天合计', colNo: 'manDayTotal', align: 'right', width: '80' },
						{ colName: '状态', colNo: 'status', align: 'left', width: '120' },
					],
				},

				{
					id: 'deliverStagePlanIn14Days',
					title: '未来14天计划',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '人员计划',
					tableColumn: [
						{ colName: '项目编号', colNo: 'number', align: 'left', width: '80' },
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '' },
						{ colName: '计划任务', colNo: 'stage', align: 'right', width: '' },
					],
				},
				{
					id: 'deliverStagePlanExpired',
					title: '逾期计划任务',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '人员计划',
					tableColumn: [
						{ colName: '项目编号', colNo: 'number', align: 'left', width: '80' },
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '80' },
						{ colName: '计划任务', colNo: 'stage', align: 'left', width: '' },
					],
				},

				{
					id: 'deliverStageExpiringWithin45Days',
					title: '45天内即将到期项目阶段',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '到期管理',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '80' },
						{ colName: '到期日', colNo: 'stage', align: 'center', width: '' },
						// { colName: '', colNo: 'xxxNo', align: 'left', width: '' },
					],
				},
				{
					id: 'deliverStageExpired',
					title: '已逾期的项目阶段',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '70' },
						{ colName: '阶段', colNo: 'stage', align: 'left', width: '' },
						// { colName: '到期日', colNo: 'dueDate', align: 'center', width: '' },
					],
				},
				{
					id: 'teamClientLostInThePast30Days',
					title: '近30天已流失的客户',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '到期管理',
					tableColumn: [
						{ colName: '客户', colNo: 'teamName', align: 'left', width: '' },
						{ colName: '到期日', colNo: 'validTo', align: 'center', width: '' },
						// { colName: '', colNo: 'xxxNo', align: 'left', width: '' },
					],
				},

				{
					id: 'uncommitDeliverExampleList',
					title: '未提交的客户案例',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '' },
						{ colName: '完工日期', colNo: 'dealMonth', align: 'center', width: '80' },
						{ colName: '剩余天数', colNo: 'days', align: 'center', width: '80' },
					],
				},

				{
					id: 'teamClientHealthWarnList',
					title: '客户健康度预警',
					badgeNum: 0,
					data: [],
					button: '健康评分',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '' },
						{ colName: '最近得分', colNo: 'points', align: 'right', width: '100' },
						{ colName: '活跃用户', colNo: 'qty', align: 'left', width: '' },
					],
				},
				{
					id: 'teamClientWillLostWithin60Days',
					title: '60天内即将流失的客户',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '客户', colNo: 'teamName', align: 'left', width: '' },
						{ colName: '到期日', colNo: 'validTo', align: 'center', width: '100' },
					],
				},
				{
					id: 'tripReimbursementList',
					title: '差旅费报销',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '费用报销',
					tableColumn: [
						{ colName: '出差日期', colNo: 'tripBeginDate', align: 'center', width: '80' },
						{ colName: '客户', colNo: 'tripClientName', align: 'left', width: '' },
						{ colName: '天数', colNo: 'tripDays', align: 'right', width: '80' },
						// { colName: '', colNo: 'xxxNo', align: 'left', width: '' },
					],
				},

				{
					id: 'vacationApplyList',
					title: '请假',
					// emptyText: '今日暂无待办跟单任务 Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '请假申请',
					tableColumn: [
						{ colName: '日期', colNo: 'number', align: 'left', width: '100' },
						{ colName: '天数', colNo: 'companyName', align: 'left', width: '80' },
						{ colName: '事由', colNo: 'nextPlan', align: 'left', width: '' },
					],
				},
			],
			titleName: '',

			isPublicInquiry: false, //公海询盘
			oceanData: [], //公海数据
			tableOptions: [], //表格数据用于组件里上下页切换
			userList: [], //用户选项数据

			showMap: {
				ImportTable: false,
				ExportTable: false,
				PointsLogList: false,

				InquiryDetail: false,
				InquiryTable: false,
				OceanTable: false,
				ContractTable: false,
				ContractDetail: false,
				DeliveryOverview: false,
				DeliveryDetail: false,
				TeamDetail: false,
				DeliveryList: false,
				DeliverySchedule: false,
				ActivityRating: false,
				ExpenseReimbursement: false,

				DemandManagement: false,
				DemandManagementDetail: false,
				ProjectDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
		this.queryUserByTwids();
	},
	activated() {
		this.queryTableData(1);
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {
		this.tableList = null;
		this.dataInfoList = null;
	}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 通用打开详情
		openDetail(ref, ...args) {
			this.showMap[ref] = true; // 动态设置状态变量
			this.$nextTick(() => {
				if (!this.$refs[ref]) return this.$message.warning(`${ref}组件未加载完毕，请稍后重试！`);
				// 项目详情特殊处理
				if (ref == 'ProjectDetail') {
					const pmid = args[0]?.pmid;
					this.$refs.ProjectDetail[pmid ? 'showDetailCom' : 'getDetailForm'](args[0]);
					return;
				}
				// 其他组件通用处理
				if (typeof this.$refs[ref].showDetailCom === 'function') {
					this.$refs[ref].showDetailCom(...args);
				} else if (typeof this.$refs[ref].openDetail === 'function') {
					this.$refs[ref].openDetail(...args);
				}
			});
		},
		// 通用打开清单
		openList(ref, ...args) {
			this.showMap[ref] = true; // 动态设置状态变量

			this.$nextTick(() => {
				if (!this.$refs[ref]) return this.$message.warning(`${ref}组件未加载完毕，请稍后重试！`);
				if (ref === 'DeliveryList') {
					const monthIndex = new Date().getMonth() + 1; //当前月份
					const phoneNo = this.userInfos.adminUserVO.phoneNo;
					this.$refs[ref].openDeliverList(monthIndex, { phoneNo }, this.searchForm, new Date());
					return;
				}
				this.$refs[ref].queryTableData(...args);
			});
		},
		// 打开询盘详情
		openInquiryDetail(...args) {
			this.openDetail('InquiryDetail', ...args);
		},
		// 打开合同详情
		openContractDetail(...args) {
			this.openDetail('ContractDetail', ...args);
		},
		// 打开交付详情
		openDeliveryDetail(...args) {
			this.openDetail('DeliveryDetail', ...args);
		},
		// 打开公海
		openOceanTable(...args) {
			this.showMap.OceanTable = true;
			this.isPublicInquiry = true;
			this.$nextTick(() => {
				this.$refs.OceanTable.showDetailCom(...args);
			});
		},
		// 导入
		openImport(...args) {
			this.showMap.ImportTable = true;
			this.$nextTick(() => {
				this.$refs.ImportTable.openImport(...args);
			});
		},
		// 导出
		openExport(...args) {
			this.showMap.ExportTable = true;
			this.$nextTick(() => {
				this.$refs.ExportTable.openExport(...args);
			});
		},
		// 打开积分记录
		openLogList(...args) {
			this.showMap.PointsLogList = true;
			this.$nextTick(() => {
				this.$refs.PointsLogList.queryTableData(...args);
			});
		},

		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			const API = 'fetchImplementDashboard'; //接口
			this.$axios[API](JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						const DATA = res.data.data;
						// 顶部数据卡片
						this.dataInfoList.forEach(item => {
							item.data = DATA[item.name] || 0;
							if (item.name == 'monthlyPointsSum') {
								item.desc = `本月积分${DATA.monthlyPointsRank ? '第' + DATA.monthlyPointsRank : '未排'}名📄`;
							}
							if (item.name == 'yearlyPointsSum') {
								item.desc = `年累计积分${DATA.yearlyPointsRank ? '第' + DATA.yearlyPointsRank : '未排'}名`;
							}
						});

						// 主干各个表格
						this.tableList.forEach(item => {
							item.title === '积分记录' && (item.yesterdayPoints = DATA.yesterdayPoints);
							item.title === '工作不规范(扣分)' && (item.yesterdayDeductPoints = DATA.yesterdayDeductPoints);
							if (item.isDataList) {
								item.badgeNum = DATA[item.id]?.count || 0;
								item.data =
									DATA[item.id]?.dataList.map(item => {
										const deliverStageManagement = item?.deliverStageManagement || {}; //阶段信息
										const deliverManagement = item?.deliverManagement || {}; //阶段信息
										const inquiryDocumentary = item?.inquiryDocumentary || {}; //询盘信息
										return { ...deliverManagement, ...deliverStageManagement, ...inquiryDocumentary, ...item };
									}) || [];
							} else {
								item.data = DATA[item.id] || [];
							}
						});
						console.log('CARDS:', this.dataInfoList);
						console.log('TABLES:', this.tableList);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			this.$axios
				.selectSalesmanByTwids(JSON.stringify({ twids: [], counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),
	},
};
</script>
<style lang="scss">
.DeliveryDashboard .table-card {
	padding: 5px 15px !important;
}
</style>
<style lang="scss" scoped>
.DeliveryDashboard {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	overflow: hidden;
	overflow-y: auto;

	font-size: 14px;
	color: #666;

	.dashboard-wrapper {
		width: 100%;
		height: calc(100vh - 138px);
		padding-bottom: 10px;
		position: relative;

		display: flex;
		flex-direction: column;

		.border-radius-8 {
			border-radius: 8px;
		}

		/* @use: https://cssgrid-generator.netlify.app/ */
		.content-wrapper {
			width: 100%;
			height: calc(100% - 80px);
			display: grid;
			grid-template-columns: repeat(17, 1fr);
			grid-template-rows: repeat(10, 1fr);
			grid-column-gap: 8px;
			grid-row-gap: 8px;
			.content-item-1 {
				grid-area: 1 / 1 / 9 / 5;
			}
			.content-item-2 {
				grid-area: 9 / 1 / 11 / 5;
			}
			.content-item-3 {
				grid-area: 1 / 5 / 5 / 14;
			}
			.content-item-4 {
				grid-area: 5 / 5 / 7 / 14;
			}
			.content-item-5 {
				grid-area: 7 / 5 / 9 / 8;
			}
			.content-item-6 {
				grid-area: 9 / 5 / 11 / 8;
			}
			.content-item-7 {
				grid-area: 7 / 8 / 9 / 11;
			}
			.content-item-8 {
				grid-area: 9 / 8 / 11 / 11;
			}
			.content-item-9 {
				grid-area: 7 / 11 / 9 / 14;
			}
			.content-item-10 {
				grid-area: 9 / 11 / 11 / 14;
			}
			.content-item-11 {
				grid-area: 1 / 14 / 5 / 18;
			}
			.content-item-12 {
				grid-area: 5 / 14 / 7 / 18;
			}
			.content-item-13 {
				grid-area: 7 / 14 / 9 / 18;
			}
			.content-item-14 {
				grid-area: 9 / 14 / 11 / 18;
			}
		}
	}
}
</style>
