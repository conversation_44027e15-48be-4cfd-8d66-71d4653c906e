<template>
	<!-- 成功部在线支持工作台 - 资源清单 -->
	<div id="ResourceList">
		<header class="h-40 flex-align-center flex-justify-between pr10">
			<div class="label-title bolder fs-16">功能</div>
			<!-- <SearchHistoryInput
				:showLabel="false"
				className="vw6"
				name="functionPointSearch"
				placeholder="功能名称"
				v-model.trim="searchForm.functionPointSearch"
				@input="queryList"
			/> -->
			<el-checkbox v-model="searchForm.isOnlyShowUnReply" :true-label="1" :false-label="0" @change="queryList">
				待办
			</el-checkbox>
		</header>

		<main class="H100 overflow-y-auto">
			<div v-if="listData.length" class="resource-list mr10">
				<div
					v-for="item in listData"
					:key="'rcid' + item.rcid + item.unReplyCount"
					class="resource-list-item"
					:class="topRcid == item.rcid ? 'active' : ''"
					@click="getTopRcid(item.rcid)"
				>
					<div class="flex-align-center gap-10">
						<img :src="folder" />
						<el-badge :value="item.unReplyCount" :max="99" :hidden="!item.unReplyCount" class="mini-badge">
							<span>{{ item.resource }}</span>
						</el-badge>
					</div>
				</div>
			</div>
			<div v-else class="H100 flex-center">
				<div class="fs-16 color-999">暂无数据</div>
			</div>
		</main>
	</div>
</template>
<script>
import { jointString, dateFormat } from '@/util/tool';
import { mapGetters } from 'vuex';
import folder from '@/assets/img/folder.svg';

export default {
	name: 'ResourceList',
	components: {},
	props: {},
	data() {
		return {
			folder,
			listData: [], // 资源列表
			topRcid: '',
			searchForm: {
				isOnlyShowUnReply: 0,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['userInfos']) },
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 获取顶部资源id
		getTopRcid(rcid) {
			this.topRcid = rcid == this.topRcid ? '' : rcid;
			this.$emit('getTopRcid', this.topRcid);
		},
		// 查询清单
		async queryList() {
			const API = 'selectCurrentTeamMenuAndUnReadCount';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.searchForm }));
				if (res.data.success) {
					this.listData = res.data.data || [];
					const unReplyCount = res.data.data?.reduce((acc, cur) => acc + cur.unReplyCount, 0);
					this.$store.commit('setUnReplyCount', unReplyCount);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 清空数据
		cleanData() {
			this.listData = [];
			this.topRcid = '';
		},
		// 日期格式化
		dateFormat: dateFormat,
	},
};
</script>

<style lang="scss">
#ResourceList {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	color: #666;
	font-size: 14px;
	display: flex;
	flex-direction: column;
	gap: 10px;
	.resource-list {
		font-size: 14px;
		// height: 100%;
		// overflow-y: auto;
		display: flex;
		flex-direction: column;
		&-item {
			padding: 10px;
			transition: all 0.3s;
			cursor: pointer;
			display: flex;
			flex-direction: column;
			gap: 10px;
			&:hover {
				border-color: #d7d7d7;
				background: #e9f5f1;
				border-color: #1e9d6f;
				z-index: 2;
			}
			&.active {
				position: sticky;
				top: 0;
				bottom: 0;
				z-index: 2;
				border-color: #1e9d6f;
				background: #e9f5f1;
			}
		}
	}

	.mini-badge {
		.el-badge__content {
			z-index: 2;
			zoom: 0.8;
			padding: 0 5px;
			right: 0;
		}
	}
}
</style>
