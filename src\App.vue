<template>
	<div id="app" class="app-container">
		<div v-if="!network" class="network-alert">
			<h4 class="network-alert-message">
				<span class="network-alert-text">无网络连接，请点击按钮刷新页面</span>
				<el-button type="text" class="el-icon-refresh-right" @click="onRefresh">刷新</el-button>
			</h4>
		</div>
		<router-view />
	</div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
	name: 'App',
	computed: {
		...mapGetters(['network']),
	},
	methods: {
		onRefresh() {
			this.$store.commit('changeNetwork', true);
			window.location.reload();
		},
	},
};
</script>
<style lang="scss">
.app-container {
	height: 100%;
}

.network-alert {
	position: fixed;
	width: 100%;
	top: 0%;
	z-index: 9999;
}

.network-alert-message {
	display: flex;
	align-items: center;
	text-align: center;
	justify-content: center;
}

.network-alert-text {
	color: #e4393c;
	margin: 0 1vw;
}
</style>
