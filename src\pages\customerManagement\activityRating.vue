<template>
	<div id="activityRating" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<!-- 团队详情 -->
		<TeamDetail ref="TeamDetail" @refresh="queryTableData(1)" />
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="team"
			@change="
				searchForm.channlList = $event.channelName;
				searchForm.proxyList = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="健康评分" name="activityRating">
				<BaseLayout>
					<template #header>
						<span class="search-label">周次</span>
						<el-button type="text" class="el-icon-arrow-left search-arrow" @click="changeWeek('reduce')"> </el-button>
						<el-date-picker
							size="small"
							v-model="selectTime"
							type="week"
							format="yyyy 年 第 W 周"
							placeholder="请选择周次"
							:picker-options="pickerOptions"
							:clearable="false"
							@change="changeSelectTime"
						>
						</el-date-picker>
						<el-button type="text" class="el-icon-arrow-right search-arrow" @click="changeWeek('add')" :disabled="noNextWeek">
						</el-button>
						<!-- 模糊查询 -->

						<SearchHistoryInput
							name="companyName"
							placeholder="客户短名称"
							v-model.trim="searchForm.query"
							@input="queryTableData(1)"
						/>
						<!-- <el-checkbox-group v-model="searchForm.status" @change="queryTableData(1)">
							<el-checkbox :label="1">正式运行</el-checkbox>
							<el-checkbox :label="2">试用</el-checkbox>
						</el-checkbox-group> -->
						<el-radio-group v-model="searchForm.status" @change="queryTableData(1)">
							<el-radio :label="1">正式运行</el-radio>
							<el-radio :label="2">试用</el-radio>
						</el-radio-group>

						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar">
							<div class="mr-auto">
								<span>数据统计日期：</span>
								<span>
									{{ dateFormat(searchForm.startTime, 'line') }} -
									{{ dateFormat(searchForm.endTime, 'line') }}
								</span>
							</div>
							<ExportBtn @trigger="openExport" />
						</div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['validFrom', 'validTo'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 编号 -->
									<Tooltips
										v-else-if="item.colNo == 'teamCode'"
										class="hover-green green"
										@click.native="openTeam(scope.row)"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>
									<!-- 使用版本 -->
									<Tooltips
										v-else-if="item.colNo == 'version'"
										:cont-str="versionMap[scope.row.version]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:cont-str="statusMap[scope.row.status]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 剩余天数 -->
									<Tooltips
										v-else-if="item.colNo == 'healthScope'"
										:class="[scope.row.healthScope < 70 ? 'red' : '']"
										:cont-str="scope.row.healthScope"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 异常说明 -->
									<div v-else-if="item.colNo == 'exceptionExplain'" @click="openDialog(scope.row)">
										<el-button size="small" v-if="scope.row.exception && !scope.row.exceptionExplain" type="text">
											必须填写
										</el-button>
										<Tooltips
											v-else
											:cont-str="scope.row.exceptionExplain"
											:cont-width="scope.column.width || scope.column.realWidth"
										/>
									</div>
									<!-- 剩余天数 -->
									<Tooltips
										v-else-if="item.colNo == 'cuInfo' || item.colNo == 'suInfo'"
										:cont-str="scope.row[item.colNo]?.userName || ''"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>

		<!-- 异常说明弹窗 -->
		<el-dialog width="600px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">异常说明填写</span>
			<el-form :model="editForm" :rules="formRules" label-width="100px" label-position="top" @submit.native.prevent>
				<el-form-item label="异常说明" prop="exceptionExplain">
					<el-input
						v-model="editForm.exceptionExplain"
						placeholder="请输入异常说明"
						type="textarea"
						:autosize="{ minRows: 4, maxRows: 6 }"
					></el-input>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEdit">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable'; //导出组件
import ChannelSelect from '@/components/ChannelSelect.vue';
import TeamDetail from '@/pages/teamManagement/teamDataMain/TeamDetail'; //团队
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		TeamDetail,
		ChannelSelect,
		ExportBtn,
	},
	name: 'activityRating', //组件名应同路由名(否则keep-alive不生效)
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			openMove: false, //打开组件
			activeTab: 'activityRating', //激活tab页
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '团队编号', colNo: 'teamCode', align: 'left', width: '' },
				{ colName: '团队名称', colNo: 'teamName', align: 'left', width: '' },
				{ colName: '业务顾问', colNo: 'suInfo', align: 'left', width: '' },
				{ colName: '实施顾问', colNo: 'cuInfo', align: 'left', width: '' },
				{ colName: '使用版本', colNo: 'version', align: 'left', width: '' },
				{ colName: '异常原因数', colNo: 'lightWaitReasonCount', align: 'right', width: '' },
				{ colName: '安灯类型数', colNo: 'andonTypeCount', align: 'right', width: '' },
				{ colName: '产线数', colNo: 'lineCount', align: 'right', width: '' },
				{ colName: '工作中心数', colNo: 'workcenterCount', align: 'right', width: '' },
				{ colName: '异常上报记录数', colNo: 'lightWaitCount', align: 'right', width: '' },
				{ colName: '安灯请求记录数', colNo: 'andonRecordCount', align: 'right', width: '' },
				{ colName: '日均报工记录数', colNo: 'workorderTaskActionCount', align: 'right', width: '' },
				{ colName: '日均活跃用户数', colNo: 'userKeepaliveCount', align: 'right', width: '' },
				{ colName: '日均设备点检记录数', colNo: 'spotInspectionCount', align: 'right', width: '' },
				{ colName: '日均首检记录数', colNo: 'qualityInspectionCount', align: 'right', width: '' },
				{ colName: '健康评分', colNo: 'healthScope', align: 'right', width: '' },
				{ colName: '异常', colNo: 'exception', align: 'left', width: '' },
				{ colName: '异常说明', colNo: 'exceptionExplain', align: 'left', width: '' },
			],
			selectTime: '',
			pickerOptions: {
				firstDayOfWeek: 1,
				disabledDate: time => {
					// 获取当前日期
					const currentDate = new Date();

					// 设置当前日期为一周前的日期
					currentDate.setDate(currentDate.getDate() - 7);

					// 禁用本周日之后的时间
					return time.getTime() > this.$moment(currentDate).endOf('isoWeek').valueOf();
				},
			},
			searchForm: {
				query: '', //模糊查询
				channlList: '',
				startTime: '',
				proxyList: [],
				status: 1,
			},

			versionMap: {
				1: '标准版',
				2: 'OEE',
				4: '微信版',
			},

			// 异常说明弹窗和表单
			dialogEdit: false,
			editForm: { exceptionExplain: '', whcId: '' },
			formRules: { exceptionExplain: [{ required: true, message: '请输入异常说明', trigger: 'blur' }] },
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）

		noNextWeek() {
			const flag = this.searchForm.startTime >= this.$moment().subtract(1, 'week').startOf('isoWeek').valueOf();
			return flag;
		},
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.changeSelectTime();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 打开团队
		openTeam(row) {
			this.$refs.TeamDetail.showDetailCom(row);
		},
		openDialog(row) {
			this.editForm = JSON.parse(JSON.stringify(row));
			this.dialogEdit = true;
		},
		closeDialog() {
			this.editForm = _.resetValues(this.editForm);
			this.dialogEdit = false;
		},
		saveEdit() {
			if (_.checkRequired(this.editForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}
			const API = 'healthScopeException';
			this.$axios[API](JSON.stringify({ ...this.editForm }))
				.then(res => {
					if (res.data.success) {
						this.queryTableData();
						this.closeDialog();
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
		// 时间上下周切换
		changeWeek(str) {
			this.selectTime = new Date(this.selectTime).getTime();
			if (str == 'add') {
				this.selectTime += 7 * 24 * 60 * 60 * 1000;
			} else if (str == 'reduce') {
				// 上一周
				this.selectTime -= 7 * 24 * 60 * 60 * 1000;
			}
			this.searchForm.startTime = this.$moment(this.selectTime).startOf('isoWeek').valueOf();
			this.searchForm.endTime = this.$moment(this.selectTime).endOf('isoWeek').valueOf();
			this.queryTableData();
		},
		// 时间选择器
		changeSelectTime() {
			if (!this.selectTime) {
				// 上一周
				this.selectTime = this.$moment().subtract(1, 'week').startOf('isoWeek').valueOf();
				this.searchForm.startTime = this.$moment().subtract(1, 'week').startOf('isoWeek').valueOf();
				this.searchForm.endTime = this.$moment().subtract(1, 'week').endOf('isoWeek').valueOf();
			} else {
				this.searchForm.startTime = this.$moment(this.selectTime).startOf('isoWeek').valueOf();
				this.searchForm.endTime = this.$moment(this.selectTime).endOf('isoWeek').valueOf();
			}
			this.queryTableData();
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'healthScope'; //接口
			const DATA = JSON.stringify({
				...this.searchForm,
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序(新增/覆盖比较逻辑)
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop == 'cuInfo' || prop == 'suInfo') {
						if (!a?.userName) return -1;
						if (!b?.userName) return 1;
						return a?.userName.localeCompare(b?.userName, 'zh-CN');
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		//数据导出
		openExport: _.debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					...this.searchForm,
				}), //接口参数
				API: 'exportHealthScope', //导出接口
				downloadData: '健康评分导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
#activityRating {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
