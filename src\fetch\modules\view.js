/* 看板API */

const urlList = [
	// 看板
	'/background/web/KanbanController/comprehensiveKanban', //综合看板
	'/background/web/KanbanController/operateKanban', //运营看板
];

// 重名函数映射
const apiNameMap = {};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['comprehensiveKanban', 'operateKanban'].includes(urlName)) {
		timeout = 60000;
	}
	return { urlName, url, timeout };
});

export default apiList;
