<template>
	<div id="inquiryTable" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<BaseLayout>
			<template #header>
				<label class="search-label">询盘日期</label>
				<DateSelect
					class="min-w-100 vw5"
					defaultDate="不限定"
					:dateKeys="['startDate', 'endDate']"
					:dateSelectObj="dateSelectObj"
					:dateList="['不限定', '今天', '昨天', '本周', '上周', '本月', '上月', '本年']"
					@change="
						dateSelectObj = $event;
						queryTableData(1);
					"
				/>

				<label class="search-label">成交日期</label>
				<DateSelect
					class="min-w-100 vw5"
					defaultDate="不限定"
					:dateKeys="['signingStartDate', 'signingEndDate']"
					:dateSelectObj="dateSelectObj2"
					:dateList="['本周', '本月', '本年', '不限定']"
					@change="
						dateSelectObj2 = $event;
						queryTableData(1);
					"
				/>

				<!-- <el-select
					v-show="salesmanList.length > 1"
					class="min-w-100 vw6"
					size="small"
					v-model="searchForm.salesman"
					placeholder="业务顾问"
					filterable
					clearable
					popper-class="select-column-3"
					@change="queryTableData(1)"
				>
					<el-option v-for="item in salesmanList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
				</el-select> -->

				<el-select
					v-show="consultList.length > 1"
					class="min-w-100 vw6"
					size="small"
					v-model="searchForm.consult"
					placeholder="咨询人员"
					filterable
					clearable
					popper-class="select-column-3"
					@change="queryTableData(1)"
				>
					<el-option v-for="item in consultList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
				</el-select>

				<el-select
					v-show="marketingList.length > 1"
					class="min-w-100 vw6"
					size="small"
					v-model="searchForm.marketingDivision"
					placeholder="营销分管"
					filterable
					clearable
					popper-class="select-column-3"
					@change="queryTableData(1)"
				>
					<el-option v-for="item in marketingList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
				</el-select>

				<el-select
					class="w-200"
					size="small"
					multiple
					collapse-tags
					v-model="searchForm.channel"
					placeholder="来源"
					clearable
					filterable
					popper-class="select-column-3"
					@change="queryTableData(1)"
				>
					<el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>

				<el-select
					class="w-150"
					size="small"
					multiple
					collapse-tags
					v-model="searchForm.quality"
					placeholder="询盘质量"
					clearable
					@change="queryTableData(1)"
				>
					<el-option v-for="key in Object.keys(qualityMap)" :key="key" :label="qualityMap[key]" :value="Number(key)"> </el-option>
				</el-select>

				<el-select
					class="w-150"
					size="small"
					multiple
					collapse-tags
					v-model="searchForm.businessOpportunityQuality"
					placeholder="商机质量"
					clearable
					@change="queryTableData(1)"
				>
					<el-option
						v-for="key in Object.keys(businessQualityMap)"
						:key="key"
						:label="businessQualityMap[key]"
						:value="Number(key)"
					>
					</el-option>
				</el-select>

				<SearchHistoryInput
					v-show="salesmanList.length > 1"
					width="120"
					name="salesmanName"
					placeholder="业务顾问"
					v-model.trim="searchForm.salesmanName"
					@input="queryTableData(1)"
				/>
				<SearchHistoryInput
					width="200"
					name="no_phone_company_region"
					placeholder="询盘编号/手机号/公司/地区"
					v-model.trim="searchForm.queryParam"
					@input="queryTableData(1)"
				/>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
				</div>
			</template>

			<template #main>
				<div class="table-toolbar">
					<el-checkbox-group class="mr-auto" v-model="searchForm.stage" @change="queryTableData(1)">
						<el-checkbox v-for="item in stageList" :label="item.value" :key="item.value">
							<span class="fs-12">{{ item.label }}</span>
						</el-checkbox>
					</el-checkbox-group>

					<el-select
						class="min-w-100 vw6 p0"
						size="mini"
						v-model="searchForm.rePurchase"
						placeholder="客户类型"
						@change="queryTableData(1)"
					>
						<!-- 0:新客户,1:复购，2：全部 -->
						<el-option label="全部客户" :value="2"> </el-option>
						<el-option label="新客户" :value="0"> </el-option>
						<el-option label="复购" :value="1"> </el-option>
					</el-select>

					<el-badge :value="badgeNum" :max="99" :hidden="!badgeNum" class="p0 mr5">
						<el-button class="p5" size="mini" v-popover:oceanPopver>公海</el-button>
					</el-badge>

					<el-button type="text" class="icon-third-bt_newdoc" @click="$emit('openDetail', '添加', null)">添加</el-button>

					<!-- 导入按钮 -->
					<ImportBtn @trigger="openImport" />
					<!-- 导出按钮 -->
					<ExportBtn v-if="isSuperAdmin" @trigger="openExport" />

					<el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button>
					<el-button type="text" class="icon-third_searchC" v-popover:thSearch>查找</el-button>
				</div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:dialog-data="tableColumn"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					:row-class-name="getRowColor"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					@header-dragend="headerDragend"
					@reset="updateColumn(tableColumnCopy)"
					@show-field="updateColumn"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<!-- show-header-overflow -->
					<u-table-column label="序号" width="50" type="index" align="center"> </u-table-column>
					<!-- 展开行 -->
					<u-table-column label="" width="20" type="expand">
						<template slot-scope="scope">
							<div class="p_expand" v-for="(item, index) in scope.row.documentaryRecordsList" :key="index + item.documentaryTime">
								<span>{{ dateFormat(item.documentaryTime, 'lineM') }} </span>
								<span> {{ item.salesmanName ? item.salesmanName : '跟单员' }}</span>
								<span>：{{ item.content }} </span>
								<span v-show="item.nextPlan || item.nextStep"> | 计划安排：</span>
								<span
									:style="{
										display: item.nextPlan ? 'content' : 'none',
										'text-decoration': item.planStatus == 1 ? 'line-through' : '',
									}"
								>
									{{ dateFormat(item.nextStep, 'MD') }} {{ item.nextPlan }}
								</span>
							</div>

							<div class="p_expand" v-show="!scope.row.documentaryRecordsList">
								<span>当前暂无跟单记录</span>
							</div>
						</template>
					</u-table-column>

					<u-table-column
						v-for="(item, index) in tableColumn.filter(item => item.state)"
						:key="item.colNo + index"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="item.colNo == 'lastDate'"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="item.colNo == 'createTime' || item.colNo == 'signingDate'"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 预计月份 -->
							<Tooltips
								v-else-if="item.colNo == 'expectedMonth'"
								class="orange"
								:cont-str="dateFormat(scope.row[item.colNo], 'YM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 行业 -->
							<Tooltips
								v-else-if="item.colNo == 'industry'"
								:cont-str="jointString('/', scope.row.industry, scope.row.industryRemark)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 来源 -->
							<Tooltips
								v-else-if="item.colNo == 'channel'"
								:cont-str="
									jointString('/', sourceMap[scope.row[item.colNo]], scope.row.promotionalVidUserName, scope.row.promotionalVid)
								"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 询盘质量 -->
							<Tooltips
								v-else-if="item.colNo == 'quality'"
								:cont-str="qualityMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 商机质量 -->
							<Tooltips
								v-else-if="item.colNo == 'businessOpportunityQuality'"
								:cont-str="businessQualityMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 行业熟练程度 -->
							<Tooltips
								v-else-if="['industryProficiency'].includes(item.colNo)"
								:cont-str="[, '不熟（1-5）', '熟练（6-8）', '擅长（9-10）'][scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 阶段 -->
							<Tooltips
								v-else-if="item.colNo == 'stage'"
								:cont-str="stageMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<Tooltips
								v-else-if="item.colNo == 'estimatedAmount'"
								:cont-str="scope.row[item.colNo]?.toFixed(2)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 合同金额 -->
							<Tooltips
								v-else-if="item.colNo == 'contractAmount' && scope.row.stage == 5"
								:cont-str="scope.row[item.colNo]?.toFixed(2)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- rePurchase 1 复购 #de873d  status 0 已备案  status 1 未备案 #ff1744-->
							<Tooltips
								v-else-if="item.colNo == 'companyName' && scope.row.rePurchase"
								:class="getRowColor(scope) || 'blue'"
								:cont-str="(scope.row[item.colNo] || '未知') + '（复购）'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<Tooltips
								v-else-if="item.colNo == 'companyName' && !scope.row.rePurchase && scope.row.status"
								:class="getRowColor(scope) || 'red'"
								:cont-str="(scope.row[item.colNo] || '未知') + '（未备案）'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 询盘编号 -->
							<Tooltips
								v-else-if="item.colNo == 'number'"
								class="hover-green green"
								:class="getRowColor(scope)"
								@click.native="$emit('openDetail', '修改', scope.row)"
								:cont-str="scope.row[item.colNo] || '未知'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							/>

							<!-- 录音 -->
							<div v-else-if="item.colNo == 'callRecording' && scope.row[item.colNo]">
								<!-- 询盘录音播放器 -->
								<InquiryAudioPlayer :audioUrl="scope.row[item.colNo]" :idid="scope.row.idid" />
							</div>
							<!-- 是否代理 -->
							<span v-else-if="item.colNo == 'isProxy'">
								{{ scope.row[item.colNo] ? '是' : '否' }}
							</span>
							<!-- 是否复购 -->
							<span v-else-if="item.colNo == 'rePurchase'">
								{{ scope.row[item.colNo] ? '是' : '否' }}
							</span>
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>

					<u-table-column label="" width="" align="right">
						<template slot-scope="scope">
							<el-button type="text" class="el-icon-edit-outline" @click="$emit('openDetail', '修改', scope.row)"></el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>

		<!-- 放大镜查询 -->
		<el-popover v-model="searchPopver" ref="thSearch" placement="bottom-end" width="400" :visible-arrow="false">
			<!-- 放大镜搜索表单 -->
			<div class="searchPop p0 fs-12">
				<div class="searchPop-header">
					<span>查找条件</span>
					<i class="searchPop-header-close el-icon-close" @click.stop="searchPopver = false"></i>
				</div>
				<el-form :model="searchForm" label-width="80px" size="mini" label-position="left" class="W100 pt10 pr20">
					<el-form-item label="联系人" prop="customerName">
						<el-input @input="queryTableData(1)" placeholder="请输入联系人" v-model="searchForm.customerName"></el-input>
					</el-form-item>

					<el-form-item label="产品" prop="keyword">
						<el-input @input="queryTableData(1)" placeholder="请输入产品" v-model="searchForm.keyword"></el-input>
					</el-form-item>
					<el-form-item label="业务顾问" prop="salesman">
						<el-select class="W100" v-model="searchForm.salesman" placeholder="请选择业务顾问" filterable clearable>
							<el-option v-for="item in salesmanList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="营销分管" prop="marketingDivision">
						<el-select class="W100" v-model="searchForm.marketingDivision" placeholder="请选择营销分管" filterable clearable>
							<el-option v-for="item in marketingList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="来源" prop="channel">
						<el-select class="W100" multiple collapse-tags v-model="searchForm.channel" placeholder="来源" clearable filterable>
							<el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
						</el-select>
					</el-form-item>
				</el-form>
				<div class="mt20 border-top text-right">
					<el-button type="text" class="color-666" @click.stop="queryTableData(1)">确定</el-button>
				</div>
			</div>
		</el-popover>

		<!-- 公海泡泡 -->
		<el-popover v-model="oceanPopver" ref="oceanPopver" placement="bottom-end" width="100">
			<div v-for="i in oceanData" :key="i.ruleTime" class="flex-align-center hover-green fs-14" @click="openOcean(i)">
				<span class="el-icon-menu mr5"> </span>
				<span class="w-50"> {{ oceanMap[i.ruleTime] }}</span>
				<span class="ml-auto"> {{ i.ruleNumber }} 条</span>
			</div>
			<div v-if="oceanData.length == 0"> 暂无数据... </div>
		</el-popover>
	</div>
</template>
<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';

import { sourceList, sourceMap, stageList, stageMap, qualityMap, businessQualityMap } from '@/assets/js/inquirySource.js'; // 来源数据
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect'; //日期选择
import ExportBtn from '@/components/ExportTable/ExportBtn';
import ImportBtn from '@/components/ImportTable/ImportBtn';
import InquiryAudioPlayer from '@/components/InquiryAudioPlayer'; //询盘录音播放器
export default {
	name: 'inquiryTable',
	components: { DateSelect, ExportBtn, ImportBtn, InquiryAudioPlayer },
	props: {
		twidList: { type: Array, default: () => [] },
		channelName: { type: Array, default: () => [] },
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			userList: [], //渠道/代理人员列表
			oceanData: [], // 公海数据
			badgeNum: 0, // 通知数
			oceanMap: {
				'-1': '昨天',
				'-3': '3天内',
				'-7': '7天内',
				'-30': '30天内',
				'-60': '60天内',
				1: '全部',
			},
			// 表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 200, 500, 1000],
			},
			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'center', state: true, width: '120' },
				{ colName: '登记日期', colNo: 'createTime', align: 'center', state: true, width: '120' },
				{ colName: '登记人', colNo: 'createName', align: 'left', state: true, width: '80' },
				{ colName: '咨询人', colNo: 'consultName', align: 'left', state: true, width: '80' },
				{ colName: '业务顾问', colNo: 'salesmanName', align: 'left', state: true },
				{ colName: '营销分管', colNo: 'marketingDivisionName', align: 'left', state: true, width: '80' },
				{ colName: '来源', colNo: 'channel', align: 'left', state: true, width: '80' },
				{ colName: '推广素材来源', colNo: 'promotionalMaterialSource', align: 'left', state: true, width: '' },
				{ colName: '区域', colNo: 'region', align: 'left', state: true },
				{ colName: '客户称呼', colNo: 'customerName', align: 'left', state: true },
				{ colName: '公司简称', colNo: 'companyName', align: 'left', state: true },
				{ colName: '所属行业', colNo: 'industry', align: 'left', state: true },
				{ colName: '分销/代理', colNo: 'twidName', align: 'left', state: true, width: '90' },
				{ colName: '咨询情况', colNo: 'consultingCase', align: 'left', state: true },
				{ colName: '产品', colNo: 'keyword', align: 'left', state: true },
				// { colName: '业务反馈', colNo: 'feedback', align: 'left', state: true },
				{ colName: '询盘质量', colNo: 'quality', align: 'left', state: true, width: '65' },
				{ colName: '商机质量', colNo: 'businessOpportunityQuality', align: 'left', state: true, width: '65' },
				{ colName: '行业熟练程度', colNo: 'industryProficiency', align: 'left', state: true, width: '65' },
				{ colName: '跟进频次', colNo: 'followUpFrequency', align: 'left', state: true, width: '' },
				{ colName: '跟进次数', colNo: 'count', align: 'right', state: true, width: '65' },
				{ colName: '最后跟进日期', colNo: 'lastDate', align: 'center', state: true, width: '78' },
				{ colName: '静置天数', colNo: 'restDays', align: 'right', state: true, width: '65' },
				{ colName: '预计成交月份', colNo: 'expectedMonth', align: 'center', state: true, width: '75' },
				{ colName: '预计成交金额（万元）', colNo: 'estimatedAmount', align: 'right', state: true, width: '100' },
				{ colName: '阶段', colNo: 'stage', align: 'left', state: true, width: '70' },
				{ colName: '录音', colNo: 'callRecording', align: 'left', state: true, width: '55' },
				{ colName: '成交时间', colNo: 'signingDate', align: 'center', state: true },
				{ colName: '合同金额（万元）', colNo: 'contractAmount', align: 'right', state: true, width: '100' },
				{ colName: '是否复购', colNo: 'rePurchase', align: 'center', state: true },
				{ colName: '是否代理', colNo: 'isProxy', align: 'center', state: true },
			],
			tableColumnCopy: [],
			// 询盘日期
			dateSelectObj: {
				endDate: '',
				startDate: '',
			},
			// 成交日期
			dateSelectObj2: {
				signingEndDate: '',
				signingStartDate: '',
			},
			searchForm: {
				queryParam: '',
				channelName: [],
				customerName: '',
				consult: '',
				salesman: '',
				salesmanName: '',
				contactInfo: '',
				keyword: '',
				region: '',
				channel: [],
				quality: [], //询盘质量（0：1星，1：2星，2：3星，3：4星，4：5星）
				businessOpportunityQuality: [],
				stage: [],
				twidList: [],
				rePurchase: 2, //是否复购 0 否 1 是 2全部
				marketingDivision: '', //营销分管
			},
			qualityMap, // 询盘质量
			businessQualityMap, // 商机质量
			// 阶段列表
			stageList,
			stageMap,
			// 来源列表
			sourceList,
			sourceMap,

			searchPopver: false, //放大镜搜索
			oceanPopver: false, //公海数量清单
			openMove: false, //打开组件
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		// 字段数据
		fieldData() {
			// 因tableColumn中的字段名的key为colName，umy-ui需要的是name，后续再考虑是否需要做兼容
			const DATA = this.tableColumn.map(item => {
				return {
					name: item.colName, // 字段名
					state: item.state === undefined ? true : item.state, // 选择状态
					disabled: item.disabled, // 是否禁用
				};
			});
			return DATA;
		},
		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
		//咨询人员列表(标签：咨询)
		consultList() {
			return this.userList?.filter(user => user?.userLabel?.includes('咨询')) || [];
		},
		//营销分管人员列表(标签：营销)
		marketingList() {
			return this.userList?.filter(user => user?.userLabel?.includes('营销')) || [];
		},
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.tableColumnCopy = deepClone(this.tableColumn);
		this.tableColumn = getColumn(this.tableColumn, this.$options.name);
		this.dateSelectObj = JSON.parse(window?.localStorage.getItem(this.$options.name + '_dateSelectObj')) || this.dateSelectObj;
		this.dateSelectObj2 = JSON.parse(window?.localStorage.getItem(this.$options.name + '_dateSelectObj2')) || this.dateSelectObj2;
		this.searchForm = JSON.parse(window?.localStorage.getItem(this.$options.name + '_searchForm')) || this.searchForm;
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {
		this.oceanData = [];
	},
	// 方法集合
	methods: {
		// 表格文本高光显示
		getRowColor({ row }) {
			if (row.quality == 5) {
				return 'color-999'; //无效
			}
			// 标红处理：未联系上，阶段为空
			return row.stage === 7 || row.stage === null ? 'red' : ''; //未联系上 标红显示
		},

		// 打开公海
		openOcean(data) {
			this.oceanPopver = false;
			this.$emit('openOcean', data, this.oceanData);
		},
		// 查询公海数据(角标和数据id集合)
		queryOceanData: debounce(function (type) {
			// 如果已查询过且非手动刷新，十分钟内不再自动重复查询
			if (this.oceanData.length > 0 && type !== 'refresh') {
				const now = new Date().getTime();
				const lastQueryTime = this.oceanData[0]?.lastQueryTime || 0;
				if (now - lastQueryTime < 10 * 60 * 1000) {
					return console.log('⚠️ 公海数据已查询过且非手动刷新，十分钟内不再自动重复查询');
				}
			}
			const API = 'selectRuleSize'; //接口
			this.$axios[API](JSON.stringify({ twidList: this.twidList, channelName: this.channelName }))
				.then(res => {
					if (res.data.success) {
						if (res.data.data.length > 0) {
							res.data.data[0].lastQueryTime = new Date().getTime();
							this.badgeNum = res.data.data[0]?.ruleNumber || 0;
							this.oceanData = res.data.data || [];
						}

						this.$emit('getOceanData', this.oceanData);

						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 查询表格数据
		queryTableData: debounce(function (type) {
			window?.localStorage.setItem(this.$options.name + '_dateSelectObj', JSON.stringify(this.dateSelectObj));
			window?.localStorage.setItem(this.$options.name + '_dateSelectObj2', JSON.stringify(this.dateSelectObj2));
			window?.localStorage.setItem(this.$options.name + '_searchForm', JSON.stringify(this.searchForm));
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectInquiryDocumentaryList'; //接口
			const DATA = JSON.stringify({
				...this.dateSelectObj2,
				...this.dateSelectObj,
				...this.searchForm,
				twidList: this.twidList,
				channelName: this.channelName,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item.region = item.province == '其他' ? '其他' : jointString('/', item.province, item.city, item.area);
						});

						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);

						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
						(type == 'openMove' || type == 'init') && this.queryUserByTwids(); //初始时查询渠道人员
						(type == 'openMove' || type == 'init' || type == 'refresh') && this.queryOceanData('init'); //初始时查询公海
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			this.$axios
				.selectSalesmanByTwids(JSON.stringify({ twids: this.twidList, counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		//数据导出
		openExport(type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					...this.dateSelectObj2,
					...this.dateSelectObj,
					...this.searchForm,
					twidList: this.twidList,
					channelName: this.channelName,
				}), //接口参数
				API: 'export', //导出接口
				downloadData: '询盘记录', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$emit('openExport', PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		},

		// 数据导入
		openImport(type = 'import') {
			const PROPS = {
				API: 'importInquiry', //导入接口
				templateName: '询盘导入模板', //模板文件名称（下载模板用）
				dataName: '询盘', //数据名（提示：成功导入xxx数据xxx条!）
				type, // 导出或查看导出记录
			};
			this.$emit('openImport', PROPS);
		},

		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 展示字段
		updateColumn(data) {
			this.tableColumn = updateColumn(this.$options.name, data);
			this.sortChange(this.tableSort, true);
		},
		// 列宽拖动
		headerDragend(newWidth, oldWidth, column) {
			updateColumnWidth(this.$options.name, column.property, newWidth, this.tableColumn);
		},
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
				this.$emit('getTableOptions', sortedData); // 传递表格数据用于组件里上下页切换
			});
		},

		dateFormat: dateFormat, //日期format
		jointString: jointString,
	},
};
</script>

<style lang="scss" scoped>
#inquiryTable {
	width: 100%;
	overflow: hidden;
	position: relative;
	&.moveToggle {
		.table-main {
			height: calc(100vh - 245px) !important;
		}
	}
	.p_expand {
		margin: 5px 20px;
		border: 1px solid #f5f5f5;
		padding: 10px;
		width: max-content;
		background: aliceblue;
		font-weight: 400;
	}
}
</style>
