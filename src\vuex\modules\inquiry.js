/* 询盘相关 */
const state = {
	inquiryList: [], //询盘清单
	inquiryDetailMap: {}, //询盘明细
};
const actions = {};
const getters = {
	inquiryList: state => state.inquiryList,
	inquiryDetailMap: state => state.inquiryDetailMap,
};
const mutations = {
	// 插入询盘
	setInquiryList(state, res) {
		const inquiryList = state.inquiryList;
		// 根据idid判断res是不是存在于inquiryList中，如果不存在就添加
		if (inquiryList.findIndex(item => item.idid === res.idid) === -1) {
			res.time = new Date().getTime();
			inquiryList.unshift(res);
		} else {
			// 如果存在则移动到第一个位置
			const index = inquiryList.findIndex(item => item.idid === res.idid);
			inquiryList[index].time = new Date().getTime(); // 更新最新时间
			// inquiryList = [...inquiryList.slice(index, index + 1), ...inquiryList.slice(0, index), ...inquiryList.slice(index + 1)];
		}
		// 最多保存30条并且.时间不超过一周.按照时间排序
		state.inquiryList = inquiryList
			.slice(0, 30)
			.filter(item => new Date().getTime() - item?.time < 604800000)
			.sort((a, b) => b.time - a.time);
	},
	// 移除询盘
	removeInquiry(state, res) {
		// 找到对应的询盘并移除
		const index = state.inquiryList.findIndex(item => item.idid === res.idid);
		state.inquiryList.splice(index, 1);
	},

	// 添加询盘详情
	setInquiryDetail(state, res) {
		// state.inquiryDetailMap[key] = res
	},
	removeInquiryDetail(state, res) {
		// state.inquiryDetailMap[key] = {}
	},
};

export default {
	state,
	actions,
	getters,
	mutations,
};
