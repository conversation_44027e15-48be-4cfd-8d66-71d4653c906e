<template>
	<div class="contractDetailCom" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<!-- 业绩分配 -->
		<AllocationPlan
			ref="AllocationPlan"
			@close="
				queryDetailData(detailForm.dmid, 'updateStage');
				$refs?.ContractStage?.queryTotalMoney(detailForm.dmid);
			"
		/>
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}</span>
				<div class="flex-align-center">
					<!-- 翻页 -->
					<div class="flex-align-center" v-if="showPage">
						<el-button
							type="text"
							:disabled="nowIndex == 0"
							@click="
								nowIndex > 0 ? --nowIndex : '';
								queryDetailData(filteredContractOptions[nowIndex].dmid, 'change');
							"
						>
							上一页
						</el-button>
						<el-select
							class="ml10 mr10"
							v-model="detailForm.dmid"
							placeholder="请选择"
							size="mini"
							filterable
							@change="queryDetailData(detailForm.dmid, 'change')"
						>
							<el-option
								v-for="(item, index) in filteredContractOptions"
								:key="index"
								:label="jointString(' ', item.number, item.projectName || item.registeredBusinessName)"
								:value="item.dmid"
							>
								{{ jointString(' ', item.number, item.projectName || item.registeredBusinessName) }}
							</el-option>
						</el-select>
						<el-button
							type="text"
							:disabled="nowIndex == filteredContractOptions.length - 1"
							@click="
								nowIndex < filteredContractOptions.length - 1 ? ++nowIndex : '';
								queryDetailData(filteredContractOptions[nowIndex].dmid, 'change');
							"
							>下一页
						</el-button>
					</div>
					<el-button type="text" class="el-icon-arrow-left ml10" @click="closeCom">返回</el-button>
				</div>
			</div>
			<!-- 详情弹窗 -->
			<div class="detail-content">
				<p class="detail-content-title">
					<span class="flex-align-center W100">
						<span>基本信息</span>
						<span class="flex-align-center ml10 color-666 fs-14" v-show="teamWorkList.length > 1">
							<span>分销/代理：</span>
							<el-select
								:disabled="isSubmitApprove"
								class="w-150"
								size="small"
								v-model="detailForm.twid"
								placeholder="分销/代理"
								@change="queryUserList('clean')"
								clearable
								filterable
							>
								<el-option v-for="item in teamWorkList" :key="item.twid" :label="item.twName" :value="item.twid"> </el-option>
							</el-select>
						</span>
						<div class="ml-auto flex-align-center gap-10">
							<div class="flex-align-center gap-10">
								<span class="fs-12">合同状态: </span>
								<el-tooltip :content="'合同状态: ' + (detailForm.closed == 1 ? '关闭' : '开启')" placement="top">
									<el-switch
										v-model="detailForm.closed"
										active-color="#13ce66"
										inactive-color="#ff4949"
										:active-value="0"
										:inactive-value="1"
										@change="updateClose(detailForm)"
									>
									</el-switch>
								</el-tooltip>
							</div>
							<div>
								<span v-if="detailForm.submitApproveStatus == 0 && detailForm.approveStatus !== 2" class="red fs-12">
									未提交审核
								</span>
								<!-- 审核状态 approveStatus 0 未审核， 1 已审核， 2 已驳回 -->
								<span v-else class="ml-auto fs-12" :class="{ 0: 'orange', 1: '', 2: 'pink' }[detailForm.approveStatus]">
									{{ { 0: '审核中...', 1: '已审核', 2: '已驳回' }[detailForm.approveStatus] }}
								</span>
								<!-- "合同提交审核状态 submitApproveStatus 0 未提交， 1 已提交审核" -->
								<el-button
									v-show="detailForm.submitApproveStatus == 0 && detailForm.approveStatus !== 2"
									:disabled="isUpdate || !isAllocation"
									class="ml10"
									type="primary"
									size="small"
									@click="submitApprove"
								>
									{{ isUpdate ? '请先保存信息' : '提交审核' }}
								</el-button>
								<!-- 已审核，且不是管理员时，不允许撤回 -->
								<el-button
									:title="isApprove && !isSuperAdmin ? '已审核的合同不允许撤回再修改，如有问题请联系管理员!' : ''"
									:disabled="isApprove && !isSuperAdmin"
									v-show="!(detailForm.submitApproveStatus == 0 && detailForm.approveStatus !== 2)"
									class="ml10 p0"
									type="text"
									size="small"
									@click="submitApprove"
								>
									{{ detailForm.approveStatus == 2 ? '重新提交' : '撤回' }}
								</el-button>
							</div>
							<div v-if="detailForm.approveStatus == 2" class="fs-12 text-right normal color-999">
								驳回原因：{{ detailForm.approveMemo || '未填写' }}
							</div>
						</div>
					</span>
				</p>
				<table class="base-table input-border-none" cellpadding="5" cellspacing="0">
					<tr>
						<th class="W5">询盘单号</th>
						<th class="W20 label-required" :colspan="2">公司工商注册名称</th>
						<th class="W10 label-required">业务顾问</th>
						<th class="W10 label-required">实施顾问</th>
						<th class="W10">咨询</th>
						<th class="W10">BPUL</th>
						<th class="W10">BPUT</th>
						<th class="W10">合同类型</th>
					</tr>
					<tr>
						<td>
							<el-autocomplete
								:disabled="titleName == '合同详情' || !detailForm.twid"
								v-model="detailForm.number"
								:debounce="500"
								class="autocomplete-popper"
								:popper-append-to-body="false"
								:fetch-suggestions="(queryStr, cb) => querySearch(queryStr, cb, '询盘')"
								:placeholder="!detailForm.twid ? '请先选择分销/代理' : '请输入询盘编号查找'"
								clearable
								@select="handleSelect($event, '询盘')"
							>
								<template slot-scope="{ item }">
									<Tooltips :cont-str="jointString(' | ', item.number, item.registeredBusinessName)" :cont-width="300">
									</Tooltips>
								</template>
							</el-autocomplete>
						</td>
						<td :colspan="2">
							<el-input
								:disabled="isSubmitApprove"
								v-model="detailForm.registeredBusinessName"
								placeholder="公司工商注册名称"
								clearable
							></el-input>
						</td>
						<td>
							<el-select
								:disabled="isSubmitApprove"
								v-model="detailForm.salesman"
								placeholder="请选择业务顾问"
								clearable
								filterable
							>
								<el-option disabled v-show="!detailForm.twid" label="请选择对应分销/代理" value=" "></el-option>
								<el-option
									v-show="detailForm.twid"
									v-for="item in salesmanList"
									:key="item.auid"
									:label="item.userName"
									:value="item.auid"
								>
								</el-option>
							</el-select>
						</td>
						<td>
							<el-select
								:disabled="isSubmitApprove"
								v-model="detailForm.implement"
								placeholder="请选择实施顾问"
								clearable
								filterable
							>
								<el-option disabled v-show="!detailForm.twid" label="请选择对应分销/代理" value=" "></el-option>
								<el-option
									v-show="detailForm.twid"
									v-for="item in implementList"
									:key="item.auid"
									:label="item.userName"
									:value="item.auid"
								>
								</el-option>
							</el-select>
						</td>
						<td>
							<!-- 咨询 -->
							<el-select
								:disabled="!isSuperAdmin || isSubmitApprove"
								v-model="detailForm.consult"
								placeholder="请选择咨询人"
								clearable
								filterable
							>
								<el-option disabled v-show="!detailForm.twid" label="请选择对应分销/代理" value=" "></el-option>
								<el-option
									v-show="detailForm.twid"
									v-for="item in consultList"
									:key="item.auid"
									:label="item.userName"
									:value="item.auid"
								>
								</el-option>
							</el-select>
						</td>
						<td>
							<!-- BPUL -->
							<el-select
								:disabled="isSubmitApprove"
								v-model="detailForm.businessPartnerUnitUid"
								placeholder="请选择BPUL"
								clearable
								filterable
							>
								<el-option disabled v-show="!detailForm.twid" label="请选择对应分销/代理" value=" "></el-option>
								<el-option
									v-show="detailForm.twid"
									v-for="item in BPUList"
									:key="item.auid"
									:label="item.userName"
									:value="item.auid"
								>
								</el-option>
							</el-select>
						</td>
						<td>
							<!-- BPUT -->
							<el-select
								:disabled="isSubmitApprove"
								v-model="detailForm.businessPartnerUnitTechnician"
								placeholder="请选择BPUT"
								clearable
								filterable
							>
								<el-option disabled v-show="!detailForm.twid" label="请选择对应分销/代理" value=" "></el-option>
								<el-option
									v-show="detailForm.twid"
									v-for="item in implementList"
									:key="item.auid"
									:label="item.userName"
									:value="item.auid"
								>
								</el-option>
							</el-select>
						</td>
						<td
							><span>{{ { 0: '直销', 1: '合伙' }[detailForm.contractType] }}</span></td
						>
					</tr>

					<!-- ================================ ================================ -->

					<tr>
						<th class="W10 label-required">成交月份</th>
						<th class="W10 label-required">成交金额（万元）</th>
						<th class="label-required">合同编号</th>
						<th class="label-required">合同日期</th>
						<th :colspan="2">电子合同文件</th>
						<th>年费起算日期</th>
						<th>团队ID</th>
						<th>团队简称</th>
					</tr>
					<tr>
						<td>
							<el-date-picker
								class="W100"
								:disabled="isSubmitApprove"
								v-model="detailForm.dealMonth"
								type="month"
								placeholder="选择月"
								value-format="timestamp"
							>
							</el-date-picker>
						</td>
						<td>
							<!-- @input="detailForm.dealAmount = detailForm.dealAmount.indexOf('.') > -1 ? detailForm.dealAmount.slice(0, detailForm.dealAmount.indexOf('.') + 4) : detailForm.dealAmount" -->
							<el-input
								:disabled="isSubmitApprove"
								:class="detailForm.dmid && !isEqual ? 'input-border-red' : ''"
								placeholder="成交金额"
								v-model="detailForm.dealAmount"
							></el-input>
						</td>

						<td>
							<el-input
								:disabled="isSubmitApprove"
								type="text"
								clearable
								v-model="detailForm.contractNo"
								placeholder="合同编号"
							></el-input>
						</td>
						<td>
							<el-date-picker
								:disabled="isSubmitApprove"
								v-model="detailForm.contractDate"
								type="date"
								placeholder="合同日期"
								class="W100 min-w-150"
								value-format="timestamp"
							>
							</el-date-picker>
						</td>
						<td :colspan="2">
							<div v-if="detailForm.contractName" class="flex-align-center gap-10">
								<FilePopover
									class="inline-block w-300"
									trigger="click"
									:url="detailForm.contractUrl"
									:content="detailForm.contractName"
								/>
								<el-button
									v-show="isSuperAdmin"
									type="text"
									class="fs-12 el-icon-circle-close icon-close icon-remove color-999"
									@click.stop="detailForm.contractName = ''"
								>
								</el-button>
							</div>
							<el-upload
								v-else
								action=""
								accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
								:http-request="f => uploadFile(f, '电子合同文件')"
								:show-file-list="false"
							>
								<el-button :disabled="isSubmitApprove" class="inline-block w-300" type="text">上传</el-button>
							</el-upload>
						</td>
						<td>
							<el-date-picker
								:disabled="isSubmitApprove"
								v-model="detailForm.feeCalculateTime"
								value-format="timestamp"
								type="date"
								placeholder="年费起算日期"
								class="W100 min-w-150"
							>
							</el-date-picker>
						</td>
						<td>
							<!-- 团队id -->
							<!-- <el-input type="text" clearable v-model="detailForm.teamCode" placeholder="团队id"></el-input> -->
							<el-autocomplete
								:disabled="!detailForm.twid || isSubmitApprove"
								v-model="detailForm.teamCode"
								:debounce="500"
								class="autocomplete-popper"
								:popper-append-to-body="false"
								:fetch-suggestions="(queryStr, cb) => querySearch(queryStr, cb, '团队')"
								:placeholder="!detailForm.twid ? '请先选择分销/代理' : '请输入团队编号查找'"
								clearable
								@select="handleSelect($event, '团队')"
							>
								<template slot-scope="{ item }">
									<Tooltips :cont-str="jointString(' | ', item.teamCode, item.teamName)" :cont-width="300"> </Tooltips>
								</template>
							</el-autocomplete>
						</td>
						<td
							><span>{{ detailForm.teamName }}</span></td
						>
					</tr>

					<!-- ================================ ================================ -->

					<tr>
						<th class="label-required">项目名称</th>
						<th class="label-required">项目类型</th>
						<th class="label-required">辅导天数</th>
						<th class="label-required" :colspan="2">差旅费用承担方</th>
						<th class="label-required"> 硬件 </th>
						<th class="label-required"> 标品数量 </th>
						<th class=""> 定制人天数 </th>
						<th class=""> 附件 </th>
					</tr>
					<tr>
						<td
							><el-input
								:disabled="isSubmitApprove"
								:maxlength="4"
								v-model="detailForm.projectName"
								placeholder="项目名称"
							></el-input
						></td>
						<td>
							<el-select :disabled="isSubmitApprove" v-model="detailForm.projectType" placeholder="项目类型" clearable filterable>
								<el-option v-for="item in projectTypeOptions" :key="item.id" :label="item.label" :value="item.id"> </el-option>
							</el-select>
						</td>
						<td>
							<el-input
								:disabled="isSubmitApprove"
								type="text"
								clearable
								v-model.number="detailForm.tutoringDays"
								placeholder="辅导天数"
							></el-input>
						</td>
						<td :colspan="2">
							<el-radio-group :disabled="isSubmitApprove" v-model="detailForm.travelExpensesParty">
								<el-radio v-for="[key, value] in Object.entries(expensePartyMap)" :key="key" :label="Number(key)">
									{{ value }}
								</el-radio>
								<!-- <el-radio :label="0">公司承担</el-radio>
								<el-radio :label="1">客户承担</el-radio>
								<el-radio :label="2">无</el-radio> -->
							</el-radio-group>
						</td>
						<td>
							<el-input
								:disabled="isSubmitApprove"
								type="text"
								clearable
								v-model="detailForm.deliverHardwareMemo"
								placeholder="硬件（需具体型号）"
							></el-input>
						</td>
						<td>
							<el-input
								:disabled="isSubmitApprove"
								type="text"
								clearable
								v-model="detailForm.standardProductCount"
								placeholder="标品数量"
							></el-input>
						</td>
						<td>
							<el-input
								:disabled="isSubmitApprove"
								type="text"
								clearable
								v-model.number="detailForm.customizedServiceDevelopDays"
								placeholder="定制人天数"
								@change="detailForm.customizedServiceDevelopDays = Number(detailForm.customizedServiceDevelopDays)"
							></el-input>
						</td>
						<td>
							<div class="fs-12 flex-align-center gap-10">
								<span>定制人天数：</span>
								<div v-if="detailForm.customizedServiceDevelopDaysFile" class="flex-center gap-10 ml-auto">
									<FilePopover isIcon trigger="click" :url="detailForm.customizedServiceDevelopDaysFile" />
									<i
										class="fs-12 el-icon-circle-close icon-close icon-remove"
										@click.prevent="detailForm.customizedServiceDevelopDaysFile = ''"
									></i>
								</div>

								<el-upload
									v-else
									class="ml-auto"
									action=""
									accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
									:http-request="f => uploadFile(f, '定制人天数附件')"
									:show-file-list="false"
								>
									<el-button :disabled="isSubmitApprove" class="p0" size="mini" type="text">上传</el-button>
								</el-upload>
							</div>
							<div class="fs-12 flex-align-center gap-10">
								<span>人天单价审批：</span>
								<div v-if="detailForm.customizedServiceDevelopCostAuditFile" class="flex-center gap-10 ml-auto">
									<FilePopover isIcon trigger="click" :url="detailForm.customizedServiceDevelopCostAuditFile" />
									<i
										class="fs-12 el-icon-circle-close icon-close icon-remove"
										@click.prevent="detailForm.customizedServiceDevelopCostAuditFile = ''"
									></i>
								</div>

								<el-upload
									v-else
									class="ml-auto"
									action=""
									accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
									:http-request="f => uploadFile(f, '人天单价审批附件')"
									:show-file-list="false"
								>
									<el-button :disabled="isSubmitApprove" class="p0" size="mini" type="text">上传</el-button>
								</el-upload>
							</div>
						</td>
					</tr>

					<!-- ================================ ================================ -->
					<tr>
						<th>其他货款（万元）</th>
						<th :colspan="8">
							<div class="flex-align-center">
								<span class="ellipsis mr10">问题与需求清单</span>
								<a
									v-show="detailForm.deliverIssuesUrl"
									class="ellipsis max-w-200"
									:href="detailForm.deliverIssuesUrl"
									target="_blank"
								>
									{{ detailForm.deliverIssuesUrl }}
								</a>
							</div>
						</th>
					</tr>
					<tr>
						<td>
							<el-input
								:disabled="isSubmitApprove"
								clearable
								v-model="detailForm.otherGoodsAmount"
								placeholder="其他货款"
							></el-input>
						</td>
						<td :colspan="8">
							<el-input
								:disabled="isSubmitApprove"
								clearable
								v-model="detailForm.deliverIssuesUrl"
								placeholder="问题与需求清单"
							></el-input>
						</td>
					</tr>
				</table>
				<div class="bottom-button">
					<el-button v-show="!isSubmitApprove" :type="isUpdate ? 'primary' : ''" @click="confirmSave">保存信息</el-button>
				</div>

				<!-- 项目阶段 -->
				<ContractStage
					ref="ContractStage"
					:isUpdate="isUpdate"
					:isAllocation="isAllocation"
					:detailForm="detailForm"
					@queryDetailData="queryDetailData"
					@openAllocation="openAllocation"
				/>
			</div>
		</div>
	</div>
</template>
<script>
import { debounce, deepClone, dateFormat, jointString, resetValues, checkRequired } from '@/util/tool';
import { bigAdd, bigDiv } from '@/util/math';
import { mapGetters } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';
import AllocationPlan from './allocationPlan.vue';
import ContractStage from './ContractStage.vue';
import { projectTypeOptions, expensePartyMap } from '@/assets/js/contractSource';

export default {
	name: 'contractDetailCom',
	components: { FilePopover, AllocationPlan, ContractStage },
	props: {
		contractOptions: {
			// 合同列表
			type: Array,
			default: function () {
				return [];
			},
		},
	},

	data() {
		return {
			showCom: '', //控制弹窗显隐
			titleName: '', //控制弹窗显隐
			nowIndex: -1,
			userList: [],

			headerRow: {
				'background-color': '',
				fontSize: '13px',
				color: '#666',
			},
			detailForm: {
				//详情
				businessPartnerUnitUid: '',
				businessPartnerUnitTechnician: '',
				contractType: '',
				estimatedAmount: '',
				expectedMonth: '',
				idid: '',
				implementName: '',
				implementUid: '',
				number: '',
				registeredBusinessName: '',
				salesmanName: '',
				salesmanUid: [],
				teamCode: '',
				feeCalculateTime: '',
				twid: '',
				twidName: '',
				salesman: '',
				implement: '',
				consult: '',
				talktrade: '',
				contractDate: '',
				annuity: '',
				contractName: '',
				contractNo: '',
				contractUrl: '',
				dealAmount: 0,
				dealMonth: '',
				receivedAmount: 0,
				tid: '',

				dmid: '',
				dmsid: '',
				deliverProcessDTOS: [], //交付过程
				deliverStageManagementVOS: [], //项目阶段、

				projectName: '',
				projectType: '',

				tutoringDays: '', //辅导天数
				travelExpensesParty: '', //差旅费用承担方
				deliverIssuesUrl: '', //问题与需求清单
				deliverHardwareMemo: '', //硬件
				standardProductCount: '', //标品数量
				customizedServiceDevelopDays: 0, //定制人天数
				customizedServiceDevelopDaysFile: '', //定制人天数附件
				customizedServiceDevelopCostAuditFile: '', //人天单价审批附件
				otherGoodsAmount: 0, //其他货款

				closed: 0, //是否已关闭 0 未关闭，1 已关闭
			},
			detailFormCopy: {},
			formRules: {
				registeredBusinessName: [{ required: true, message: '请输入公司工商注册名称', trigger: 'blur' }],
				salesman: [{ required: true, message: '请输入业务顾问', trigger: 'blur' }],
				implement: [{ required: true, message: '请输入实施顾问', trigger: 'blur' }],
				dealMonth: [{ required: true, message: '请输入成交月份', trigger: 'blur' }],
				dealAmount: [{ required: true, message: '请输入成交金额', trigger: 'blur' }],
				contractNo: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
				contractDate: [{ required: true, message: '请输入合同日期', trigger: 'blur' }],
				projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
				projectType: [{ required: true, message: '请输入项目类型', trigger: 'blur' }],
				tutoringDays: [{ required: true, message: '请输入辅导天数', trigger: 'blur' }],
				travelExpensesParty: [{ required: true, message: '请输入差旅费用承担方', trigger: 'blur' }],
				deliverHardwareMemo: [{ required: true, message: '请输入硬件信息', trigger: 'blur' }],
				standardProductCount: [{ required: true, message: '请输入标品数量', trigger: 'blur' }],
			},

			projectTypeOptions, //项目类型
			expensePartyMap, //承担方
		};
	},
	created() {},
	computed: {
		...mapGetters(['userInfos', 'teamWorkList']), //当前登录用户信息（含团队/菜单/权限等）
		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
		// 是否修改
		isUpdate() {
			const FLAG = JSON.stringify(this.detailForm) != JSON.stringify(this.detailFormCopy);
			return FLAG;
		},
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
		//咨询人员列表(标签：咨询)
		consultList() {
			return this.userList?.filter(user => user?.userLabel?.includes('咨询')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},

		//BPUL人员列表(标签：BPUL)
		BPUList() {
			return this.userList?.filter(user => user?.userLabel?.includes('BPUL')) || [];
		},
		// 金额是否相等
		isEqual() {
			const contractAmount = this.detailForm?.deliverStageManagementVOS?.reduce((prev, curr) => {
				return bigAdd(prev, curr.amount, 6);
			}, 0);

			return Number(bigDiv(contractAmount, 10000, 6)) == Number(this.detailForm.dealAmount);
		},

		// 允许进行业绩分配/提交审核
		isAllocation() {
			if (this.detailForm?.contractType === null) return false; // 未有合同类型
			if (!this.detailForm?.deliverStageManagementVOS.length) return false; // 未填写项目阶段
			if (this.checkEmptyContent) return false; // 未填写项目阶段金额
			if (!this.isEqual) return false; // 金额不一致
			return true;
		},

		// 展示上下页切换
		showPage() {
			return this.titleName !== '添加' && this.filteredContractOptions.length > 1 && this.nowIndex >= 0;
		},
		filteredContractOptions() {
			return this.contractOptions.filter(item => item.dmid); // 去除空id项
		},

		// 如果存在阶段未回款项目阶段的合同金额/标品数量/定制金额/居间费（可以为0）
		checkEmptyContent() {
			return this.detailForm.deliverStageManagementVOS?.some(item => {
				if (!item.collectionAmount) {
					const isAmount = item.amount === null || item.amount === '';
					const isMediAmount = this.detailForm?.contractType == 0 && (item.mediAmount === null || item.mediAmount === '');
					return isAmount || isMediAmount;
				}
			});
		},
		// 已提交审核（不允许修改）
		isSubmitApprove() {
			return this.detailForm.submitApproveStatus === 1;
		},
		// 已审核
		isApprove() {
			return this.detailForm.approveStatus === 1;
		},
	},
	watch: {},
	mounted() {},
	destroyed() {
		this.detailForm = null;
		this.detailFormCopy = null;
		// this.contractOptions = null;
	},
	methods: {
		// 开启或关闭合同状态
		async updateClose(form) {
			this.detailFormCopy = deepClone(this.detailForm);
			const API = 'updateDeliverManagementCloseStatus';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...form }));
				if (res.data.success) {
					this.$message.success('合同状态已修改！');
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 提交合同审核申请
		async submitApprove() {
			const API = 'deliverManagementSubmitApprove';
			try {
				const res = await this.$axios[API](
					JSON.stringify({
						dmid: this.detailForm.dmid,
						submitApproveStatus: this.detailForm.submitApproveStatus ? 0 : 1,
					}),
				);
				if (res.data.success) {
					this.queryDetailData(this.detailForm.dmid, 'init');
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 打开分配计划详情
		openAllocation(type, rowData) {
			if (this.isApprove && !this.isSuperAdmin) {
				// 已审核且非管理权限，只能查看合同
				this.$refs?.AllocationPlan?.showDetailCom('查看合同', rowData);
			} else {
				this.$refs?.AllocationPlan?.showDetailCom(type, rowData);
			}
		},

		// 电子合同上传
		uploadFile(file, type) {
			const isLt50M = file.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				this.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', file.file);

			this.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						if (type == '电子合同文件') {
							this.detailForm.contractName = res.data.data.fileName;
							this.detailForm.contractUrl = res.data.data.path;
						} else if (type == '定制人天数附件') {
							this.detailForm.customizedServiceDevelopDaysFile = res.data.data.path;
						} else if (type == '人天单价审批附件') {
							this.detailForm.customizedServiceDevelopCostAuditFile = res.data.data.path;
						}
						this.$message.success('上传成功！');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.$message.warning(error.message);
				});
		},

		// 输入框查询
		querySearch(queryStr, cb, type) {
			if (!queryStr) {
				this.handleSelect({}, type);
			}
			let str = '';
			let api = '';
			if (type == '询盘') {
				str = JSON.stringify({
					twid: this.detailForm.twid,
					number: queryStr,
					pageNum: 1,
					pageSize: 30,
				});
				api = 'selectInquiryDocumentaryDetail';
			} else if (type == '团队') {
				str = JSON.stringify({
					teamCode: queryStr,
					twidList: [this.detailForm.twid],
					statusList: [0, 3, 6],
					channelName: [],
					version: [1, 2],
					pageNum: 1,
					pageSize: 30,
				});
				api = 'selectTeam';
			}

			this.$axios[api](str)
				.then(res => {
					if (res.data.success) {
						const result = res.data.data;
						cb(result);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${api} | ` + error);
				});
		},

		handleSelect(item, type) {
			if (type == '询盘') {
				this.detailForm.idid = item?.idid || '';
				this.detailForm.number = item?.number || '';
				this.detailForm.registeredBusinessName = item?.registeredBusinessName || '';
				this.detailForm.dealMonth = item?.expectedMonth || '';
				this.detailForm.dealAmount = item?.estimatedAmount || '';

				this.detailForm.implement = item?.implementUid || '';
				this.detailForm.salesman = item?.salesmanUid || '';
				this.detailForm.consult = item?.consultUid || '';
				this.detailForm.talktrade = item?.talktradeUid || '';
				this.detailForm.businessPartnerUnitUid = item?.businessPartnerUnitUid || '';
				this.detailForm.businessPartnerUnitTechnician = item?.businessPartnerUnitTechnician || '';
			} else if (type == '团队') {
				this.detailForm.teamCode = item?.teamCode || '';
				this.$set(this.detailForm, 'teamName', item?.teamName || '');
			}
		},

		// 查询用户选项数据
		queryUserList(type) {
			if (type == 'clean') {
				this.detailForm.salesman = '';
				this.detailForm.implement = '';
				this.detailForm.talktrade = '';
				this.detailForm.businessPartnerUnitUid = '';
				this.detailForm.businessPartnerUnitTechnician = '';
				this.detailForm.consult = '';
				this.detailForm.idid = '';
				this.detailForm.number = '';
				this.detailForm.registeredBusinessName = '';
				// this.detailForm.dealMonth = '';
			}

			if (!this.detailForm.twid) {
				return;
			}
			const str = JSON.stringify({ twid: this.detailForm.twid, counselor: '' });
			this.$axios
				.selectTeamworkUser(str)
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTeamworkUser |' + error);
				});
		},
		// 保存前确认
		confirmSave() {
			if (checkRequired(this.detailForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}

			if (this.detailForm.dmid) {
				if (!this.detailForm.deliverStageManagementVOS || !this.detailForm.deliverStageManagementVOS.length) {
					return this.$message.warning('请补充项目阶段后再保存！');
				}

				if (this.checkEmptyContent) {
					return this.$message.warning('请完善未回款项目阶段的合同金额/标品数量/居间费(可为0，但不得为空)');
				}

				if (!this.isEqual) {
					return this.$message.warning('阶段金额之和必须等于成交金额！'); //检查金额是否相等
				}
			}

			const NOW_MONTH = this.$moment(new Date()).startOf('month').format('YYYY-MM');
			const EXPECTED_MONTH = this.$moment(this.detailForm.dealMonth).startOf('month').format('YYYY-MM');
			// console.log(NOW_MONTH !== EXPECTED_MONTH, { NOW_MONTH }, { EXPECTED_MONTH });
			if (NOW_MONTH !== EXPECTED_MONTH) {
				this.$confirm(
					`成交月份【${EXPECTED_MONTH}】与当前月份【${NOW_MONTH}】不一致，当前合同金额会计入【${EXPECTED_MONTH}】月份，是否继续保存?`,
					'提示',
					{
						confirmButtonText: '我已知晓',
						cancelButtonText: '返回修改',
						type: 'warning',
					},
				)
					.then(() => {
						this.saveDetail();
					})
					.catch(() => {
						this.$message.info('已取消');
					});

				return;
			}

			this.saveDetail(); // 确认保存
		},
		// 添加/修改合同
		saveDetail() {
			// 如果有人天数，则需要上传附件
			if (this.detailForm.customizedServiceDevelopDays) {
				if (!this.detailForm.customizedServiceDevelopDaysFile) {
					return this.$message.warning('请上传定制人天数附件！');
				}
				// if (!this.detailForm.customizedServiceDevelopCostAuditFile) {
				// 	return this.$message.warning('请上传人天单价审批附件！');
				// }
			}

			this.detailForm.dealAmount = +this.detailForm.dealAmount;
			this.detailForm.contractAmount = +this.detailForm.dealAmount;
			this.$axios
				.addDeliverManagement(JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						!this.detailForm.dmid && this.clearDetailData();
						this.detailForm.dmid && this.queryDetailData(this.detailForm.dmid, 'init');
						this.$succ('操作成功!');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addDeliverManagement |' + error);
				});
		},
		//获取明细信息
		queryDetailData: debounce(async function (dmid, type) {
			const str = JSON.stringify({ dmid });
			this.$axios
				.selectDeliverManagementDetail(str)
				.then(res => {
					if (res.data.success) {
						if (type == 'init' || type == 'change') {
							this.detailForm = { ...this.detailForm, ...res.data.data };
							this.$set(this.detailForm, 'salesmanName', res.data.data.salesman?.userName || '');
							this.$set(this.detailForm, 'implementName', res.data.data.implement?.userName || '');
							this.$set(this.detailForm, 'talkName', res.data.data.talk?.userName || '');
							this.$set(this.detailForm, 'businessPartnerUnitName', res.data.data.businessPartnerUnit?.userName || '');
							this.$set(this.detailForm, 'consultName', res.data.data.consulting?.userName || '');

							this.$set(this.detailForm, 'salesman', res.data.data.salesman?.auid || '');
							this.$set(this.detailForm, 'implement', res.data.data.implement?.auid || '');
							this.$set(this.detailForm, 'talktrade', res.data.data.talk?.auid || '');
							this.$set(this.detailForm, 'businessPartnerUnitUid', res.data.data.businessPartnerUnit?.auid || '');
							this.$set(this.detailForm, 'consult', res.data.data.consulting?.auid || '');

							// 定制人天数 默认0
							this.$set(this.detailForm, 'customizedServiceDevelopDays', res.data.data.customizedServiceDevelopDays || 0);

							this.detailForm.twid && this.queryUserList();
							this.$refs?.ContractStage?.queryTotalMoney(this.detailForm.dmid);
							this.detailFormCopy = deepClone(this.detailForm);
						} else if (type == 'updateStage') {
							// 只更新阶段
							this.detailForm.deliverStageManagementVOS = res.data.data.deliverStageManagementVOS;
						}

						this.$nextTick(() => {
							if (this.filteredContractOptions?.length > 1) {
								this.nowIndex = this.filteredContractOptions?.findIndex(item => item?.dmid == this.detailForm.dmid); //当前选中项下标
							}
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectDeliverManagementDetail |' + error);
				});
		}, 300),
		//显示弹窗
		showDetailCom(rowData) {
			if (this.teamWorkList?.length == 1) {
				this.detailForm.twid = this.teamWorkList[0].twid;
				this.detailFormCopy = deepClone(this.detailForm);
				this.queryUserList();
			}
			if (rowData) {
				this.detailForm = { ...this.detailForm, ...deepClone(rowData) };
				this.queryDetailData(rowData.dmid, 'init');
				this.titleName = '合同详情';
			} else {
				this.titleName = '添加合同';
				this.detailFormCopy = deepClone(this.detailForm);
			}

			this.showCom = true;
		},
		// 关闭组件并做修改判断
		closeCom() {
			// let isSameData = true;
			// isSameData = JSON.stringify(this.detailForm) == JSON.stringify(this.detailFormCopy);
			// console.log('isSameData', isSameData);
			if (this.isUpdate) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定退出',
					cancelButtonText: '取消退出',
				})
					.then(() => {
						this.clearDetailData(); // 关闭并清空数据
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.clearDetailData(); // 关闭并清空数据
			}
		},
		//点击返回
		clearDetailData() {
			this.showCom = false;
			this.$emit('close');
			this.detailForm = resetValues(this.detailForm);
			this.$refs.ContractStage.totalMoneyList = [];
		},

		dateFormat, //日期format
		jointString, //字符串拼接
	},
};
</script>
