<template>
	<div class="deliveryStage table-wrapper">
		<p class="detail-content-title">项目阶段 </p>
		<u-table
			class="table-main detail-table W100 th-bg-gray"
			ref="refTable"
			row-key="dmsid"
			:max-height="600"
			:data="detailForm.deliverStageManagementVOS"
			:border="false"
			:row-height="45"
			:row-style="{ height: '0' }"
			:cell-style="{ padding: '0 0', borderBottom: '1px solid #e9e9e9' }"
			:header-cell-style="{ border: 'transparent', padding: '10px 0 !important' }"
			@expand-change="expandChange"
			:expand-row-keys="expandKeys"
			show-summary
			:summary-method="summaryMethod"
			show-header-overflow="title"
		>
			<u-table-column label="序号" type="index" align="center" width="50"></u-table-column>
			<!-- 展开内容 -->
			<u-table-column label="" width="20" type="expand">
				<template slot-scope="scope">
					<deliveryStagePlan ref="deliveryStagePlan" :userList="userList" @update="queryExpandData" />
				</template>
			</u-table-column>
			<u-table-column
				v-for="item in tableColumn"
				:key="'colCurr' + item.colNo"
				:label="item.colName"
				:prop="item.colNo"
				:align="item.align"
				:width="item.width"
			>
				<template slot-scope="scope">
					<div v-if="item.colNo == '123'">
						<Tooltips :cont-str="scope.row[item.colNo]" :cont-width="scope.column.width || scope.column.realWidth"> </Tooltips>
					</div>
					<div v-else-if="item.colNo == 'complateMonth'" class="flex-align-center">
						<el-date-picker
							:class="scope.row.stageStatus == 3 ? 'input-border-red' : ''"
							disabled
							size="small"
							v-model="scope.row[item.colNo]"
							type="month"
							placeholder="选择月"
						>
						</el-date-picker>
						<!-- 审批状态 0 提交延期待批 1 通过 2 驳回 -->
						<el-button
							size="small"
							v-if="!scope.row.stageStatus != 2 || scope.row.status == 0 || scope.row.status == 2"
							class="p8"
							:class="[scope.row.status == 0 ? 'button-warning' : scope.row.status == 2 ? 'button-error' : '']"
							@click="openDialogDelay(scope.row, scope.$index)"
						>
							<span v-if="scope.row.status != 2">{{ scope.row.status == 0 ? '待审' : '延期' }}</span>
							<Tooltips
								v-else
								:cont-str="'驳回'"
								:cont-obj="[
									{ title: '审核人', content: scope.row.approvalUname },
									{ title: '审核结果', content: '不通过' },
									{ title: '审核意见', content: scope.row.approvalOpinion },
									{ title: '审核时间：', content: dateFormat(scope.row.approvalDate, 'lineM') },
								]"
								:cont-width="20"
							>
							</Tooltips>
							<!-- 审核中  -->
						</el-button>
					</div>
					<!-- 完工报告 -->
					<div v-else-if="item.colNo == 'reportName'" class="flex-align-center">
						<div v-if="scope.row.reportName" class="flex w-150">
							<FilePopover
								class="inline-block max-w-100"
								trigger="click"
								:url="scope.row.reportUrl"
								:content="scope.row.reportName"
							/>
							<!-- <i
								class="el-icon-circle-close pointer"
								@click.stop="
									removeReport(scope.$index);
									updateStage(scope.$index, 'delReport');
								"
							></i> -->
						</div>
						<!-- 非待审批、非已完成时上传 -->
						<el-button
							v-show="scope.row.status != 0 && !scope.row.stageStatus != 2"
							@click="openDialogReport(scope.row, scope.$index)"
							:type="['info', 'info', 'primary', 'danger'][scope.row.stageStatus]"
							size="small"
							class="p8"
						>
							<!-- stageStatus 1 待完成 2 已完成 3 逾期 -->
							<span>{{ ['待完成', '待完成', '已完成', '已逾期'][scope.row.stageStatus] || '待完成' }} </span>
						</el-button>
					</div>
					<!-- 支付比例 -->
					<div v-else-if="item.colNo == 'ratio' && scope.row[item.colNo]" class="input_textR">
						<Tooltips :cont-str="scope.row[item.colNo] + '%'" :cont-width="scope.column.width || scope.column.realWidth">
						</Tooltips>
					</div>

					<div v-else class="input_textR">
						<Tooltips :cont-str="scope.row[item.colNo]" :cont-width="scope.column.width || scope.column.realWidth"> </Tooltips>
					</div>
				</template>
			</u-table-column>
		</u-table>

		<!-- 延期弹窗 -->
		<el-dialog
			:visible.sync="dialogDelay"
			width="600px"
			:append-to-body="true"
			:close-on-click-modal="false"
			@close="closeDialog"
		>
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="120px" label-position="left" ref="editFormRef" :rules="formRules">
				<!-- <el-form-item label="计划初始完工月份" prop="initialComplateMonth">
          <span>{{ dateFormat(editForm.initialComplateMonth, 'YM') }}</span>
        </el-form-item> -->
				<el-form-item label="计划完工月份" prop="complateMonth">
					<span>{{ dateFormat(editForm.complateMonth, 'YM') }}</span>
				</el-form-item>
				<el-form-item label="延期到" prop="complateMonthApply">
					<el-date-picker
						v-model="editForm.complateMonthApply"
						type="month"
						class="W100"
						value-format="timestamp"
						placeholder="请选择延期到"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						placeholder="请输入备注"
						v-model="editForm.remark"
					></el-input>
				</el-form-item>
				<el-form-item label="" prop="postpone">
					<el-checkbox v-model="editForm.postpone" :true-label="1" :false-label="0">后续阶段顺延</el-checkbox>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="applyForExtension">申请延期</el-button>
			</el-row>
		</el-dialog>

		<!-- 阶段完工弹窗 -->
		<el-dialog
			:visible.sync="dialogReport"
			width="600px"
			:append-to-body="true"
			:close-on-click-modal="false"
			@close="closeDialog"
			v-loading.lock="isUpLoading"
			element-loading-text="正在上传文件中，请勿中途退出..."
		>
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="8vw" label-position="left" ref="editFormRef" :rules="formRules">
				<el-form-item label="阶段完工日期" prop="reportDate">
					<el-date-picker
						v-model="editForm.reportDate"
						type="date"
						style="width: 80%"
						value-format="timestamp"
						@change="changeReportDate"
						placeholder="请选择完工日期"
						:disabled="hasFinished"
						:clearable="false"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="完工确认报告" prop="reportName">
					<div v-if="editForm.reportName" class="flex">
						<FilePopover trigger="click" :url="editForm.reportUrl" :content="editForm.reportName" />
						<i class="el-icon-circle-close icon-close icon-remove ml10" @click.prevent="removeReport(currIndex)"></i>
					</div>

					<el-upload
						v-else
						action=""
						accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
						:http-request="f => uploadFile2(f, editForm, '阶段')"
						:show-file-list="false"
					>
						<el-button size="small" type="primary">上传（上传报告后即为完工）</el-button>
					</el-upload>
				</el-form-item>
				<!-- 标题 -->
				<el-row style="font-size: 15px; color: #555; margin-bottom: 0.5vh">
					<el-col :span="10"> 产品</el-col>
					<el-col :span="6"> <span class="label-required">数量</span></el-col>
					<el-col :span="6"><span class="label-required">金额（元 ）</span></el-col>
					<el-col :span="2"> </el-col>
				</el-row>
				<!-- 列表 -->
				<el-row
					class="production-list"
					v-for="(item, index) in editForm.stageCompleteInfoVOS"
					:key="index"
					style="margin-bottom: 0.5vh; display: flex; align-items: center"
				>
					<el-col :span="10">
						<el-select
							size="small"
							v-model="item.name"
							placeholder="请选择产品"
							clearable
							filterable
							class="W90"
							:disabled="hasFinished"
						>
							<el-option v-for="item in productionList" :key="item.value" :label="item.name" :value="item.name">
								<span style="float: left">{{ item.name }}</span>
								<span style="float: right; color: #8492a6; font-size: 13px">{{ item.classify }}</span>
							</el-option>
						</el-select>
					</el-col>
					<el-col :span="6">
						<el-input
							class="input_textR W90"
							size="small"
							placeholder="请输入数量"
							v-model="item.qty"
							:disabled="hasFinished"
						></el-input>
					</el-col>
					<el-col :span="6">
						<el-input
							class="input_textR W90"
							size="small"
							placeholder="请输入金额"
							@change="inputAmount"
							v-model="item.amount"
							:disabled="hasFinished"
						></el-input>
					</el-col>
					<el-col :span="2">
						<el-button v-if="!hasFinished" type="text" @click="delProduction(item, index)">删除</el-button>
					</el-col>
				</el-row>
				<!-- 添加/合计 -->
				<el-row style="font-size: 15px; color: #555; margin-bottom: 0.5vh; display: flex; align-items: center">
					<el-col :span="10">
						<el-button v-if="!hasFinished" type="text" @click="addProduction">+ 增加一行</el-button>
					</el-col>
					<el-col :span="6"> 合计： </el-col>
					<el-col :span="6" style="text-align: end; padding: 0 1.5vw">{{ this.editForm.totalAmount }}</el-col>
					<el-col :span="2"> </el-col>
				</el-row>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="updateStage(currIndex, 'finish')">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';
import deliveryStagePlan from './deliveryStagePlan.vue';
export default {
	name: 'deliveryStage',
	props: {
		detailForm: Object,
		userList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	components: { FilePopover, deliveryStagePlan },
	data() {
		return {
			isUpLoading: false,
			dialogReport: false, //完工弹窗
			dialogDelay: false, //延期弹窗
			hasFinished: false,
			hasReceived: false,

			dialogTitle: '', // 弹窗标题
			currIndex: '', //阶段索引

			editForm: {
				initialComplateMonth: '',
				complateMonth: '',
				complateMonthApply: '',
				postpone: '0',
				remark: '',
				dmsid: '',
				sciid: '',
				fileName: '',
				fileUrl: '',
				reportName: '',
				reportUrl: '',
				stage: '',
				reportDate: '',
				stageCompleteInfoVOS: [],
				totalAmount: 0,
			},
			formRules: {
				initialComplateMonth: [{ required: true, message: '请输入初始完工月份', trigger: 'blur' }],
				complateMonth: [{ required: true, message: '请输入当前完工月份', trigger: 'blur' }],
				complateMonthApply: [{ required: true, message: '请选择延期到指定月份', trigger: 'change' }],
				reportName: [{ required: true, message: '请上传完工报告', trigger: 'blur' }],
				reportDate: [{ required: true, message: '请输入当前完工日期', trigger: 'blur' }],
			},

			productionList: [], //产品列表
			expandTable: [], //展开表格
			expandKeys: [], //展开id
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）

		// 根据合同类型对列区分
		tableColumn() {
			const columns = [
				{ colName: '阶段', colNo: 'stage', align: 'left', width: '' },
				{ colName: '完工标准', colNo: 'standard', align: 'left', width: '' },
				{ colName: '合同金额(元)', colNo: 'amount', align: 'center', width: '' },
				{ colName: '支付比率', colNo: 'ratio', align: 'center', width: '' },
				// { colName: '硬件金额(元)', colNo: 'hardwareAmount', align: 'center', width: '' },
				// { colName: '软件金额(元)', colNo: 'softwareAmount', align: 'center', width: '' },
				// { colName: '标品数量', colNo: 'standardProductCount', align: 'center', width: '' },
				{ colName: '定制金额(元)', colNo: 'customizedServiceAmount', align: 'center', width: '' },
				{ colName: '居间费(元)', colNo: 'mediAmount', align: 'center', width: '' },
				{ colName: '预计完工/收款月份', colNo: 'complateMonth', align: 'left', width: '200' },
				{ colName: '业务完工凭证', colNo: 'reportName', align: 'left', width: '200' },
				{ colName: '回款金额(元)', colNo: 'collectionAmount', align: 'right', width: '' },
			];
			if (this.detailForm?.contractType == 1) {
				// 合伙人去掉居间费
				return columns.filter(i => i.colNo != 'mediAmount');
			}
			return columns;
		},
	},
	// 监控data中的数据变化
	watch: {
		editForm: {
			handler(oldVal, newVal) {
				//stageStatus 1 待完成 2 已完成 3 逾期
				this.hasFinished = newVal.reportUrl && newVal.stageStatus == 2 ? true : false;
				this.hasReceived = newVal.collectionAmount ? true : false;
			},
			deep: true,
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 是否有存在项目[阶段详细计划]没有规划完成
		async checkPlans() {
			const API = 'selectDeliverManagementByStages';
			let isExist = 0;
			try {
				const res = await this.$axios[API](JSON.stringify({ dmid: this.detailForm.dmid }));
				isExist = res.data.success;
			} catch (error) {
				console.error(`${API} |` + error);
			}
			return isExist;
		},
		// 查看展开阶段详情
		async expandChange(row) {
			if (!row.complateMonth) {
				this.expandKeys = [];
				this.expandTable = [];
				this.$message.warning('请先在合同管理填写预计完工/收款月份!');
				return;
			}
			this.expandKeys = this.expandKeys?.indexOf(row.dmsid) == -1 ? [row.dmsid] : [];
			if (this.expandKeys.length > 0) {
				this.queryExpandData(row);
			}
		},
		// 查询展开阶段详情
		async queryExpandData(row) {
			const API = 'selectDeliverManagementInfo';
			try {
				const res = await this.$axios[API](JSON.stringify({ dmsid: row.dmsid }));
				if (res.data.success) {
					this.expandTable = res.data.data || [];
					this.$nextTick(() => {
						const refObj = {
							expandTable: _.deepClone(this.expandTable),
							detailForm: _.deepClone(this.detailForm),
							stageForm: _.deepClone(row),
						};
						this.$refs.deliveryStagePlan?.getInitData(refObj);
					});
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 修改阶段完工日期
		changeReportDate() {
			// 完工日期不得超过预计完工/收款月份
			const complateLastDate = this.$moment(this.detailForm.complateMonth).endOf('month').valueOf();
			if (complateLastDate && this.editForm.reportDate > complateLastDate) {
				this.editForm.reportDate = '';
				this.$message.warning('完工日期不得超过预计完工/收款月份!');
			}
			this.detailForm.deliverStageManagementVOS[this.currIndex].reportDate = this.editForm.reportDate;
		},
		// 交付过程文件上传
		uploadFile(item) {
			const self = this;
			const isLt50M = item.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				self.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', item.file);

			self.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						self.editForm.fileName = res.data.data.fileName;
						self.editForm.fileUrl = res.data.data.path;
						self.saveProcessFile();
					} else {
						self.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					self.$message.warning(error.message);
				});
		},

		// 更新阶段
		updateStage(index, type) {
			if (type == 'finish' && (!this.editForm.reportDate || !this.editForm.reportUrl)) {
				// 确认完工
				this.$message.warning('请选择完工日期并上传文件!');
				return;
			}

			const { amount } = this.detailForm.deliverStageManagementVOS[index];
			if (type !== 'delReport' && amount != this.editForm.totalAmount) {
				console.log(amount, this.editForm.totalAmount);
				this.$message('当前合计金额与收款金额不相等，请填写完善后再交付！');
				return;
			}

			// 2024年7月18日14:04:00 新增 operation 操作标识字段用于 stage-complete 标识来自阶段交付完工操作
			const str = JSON.stringify({ ...this.detailForm.deliverStageManagementVOS[index], operation: 'stage-complete' });
			this.$axios
				.addOrUpdateStage(str)
				.then(res => {
					if (res.data.success) {
						this.currIndex = index;
						this.updateProductionList();
						this.closeDialog();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addOrUpdateStage |' + error);
				});
		},
		// 打开延期
		openDialogDelay(row, index) {
			if (row.status == 0) {
				this.$message.warning('延期待审阶段，请勿重复操作！');
				return;
			}
			this.currIndex = index;
			const str = JSON.stringify({ dmsid: row.dmsid });
			this.$axios
				.extensionQuery(str)
				.then(res => {
					if (res.data.success) {
						this.dialogTitle = `第${res.data.data.requestQty + 1}次申请延期`;
						this.editForm.dmsid = row.dmsid;
						this.editForm.complateMonth = res.data.data.complateMonth;
						this.editForm.initialComplateMonth = res.data.data.initialComplateMonth;
						this.editForm.postpone = 1; //是否顺延 默认是
						this.dialogDelay = true;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('extensionQuery |' + error);
				});
		},
		// 申请延期
		applyForExtension() {
			const str = JSON.stringify({
				complateMonth: this.editForm.complateMonthApply,
				postpone: this.editForm.postpone,
				dmsid: this.editForm.dmsid,
				remark: this.editForm.remark,
			});
			this.$axios
				.extensionRequest(str)
				.then(res => {
					if (res.data.success) {
						this.closeDialog();
						// this.updateStage(this.currIndex);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('extensionRequest |' + error);
				});
		},
		// 获取产品列表
		getProductionList() {
			const str = JSON.stringify({
				pageNum: '',
				pageSize: '',
				query: '',
			});
			this.$axios
				.productList(str)
				.then(res => {
					if (res.data.success) {
						this.productionList = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('productList |' + error);
				});
		},
		// 打开完工信息
		async openDialogReport(row, index) {
			/* 
				存在多个阶段时，在首期点击完工时，检查所有阶段是否存在[阶段详细计划]，如果除首期外的任一阶段[阶段详细计划]不存在，
				则提示: 项目[阶段详细计划]没有规划完成，不能操作完工！
			*/
			if (this.detailForm.deliverStageManagementVOS.length > 1 && index == 0) {
				const isExist = await this.checkPlans();
				if (!isExist) {
					this.$alert('存在项目【阶段详细计划】没有规划完成，不能操作完工！', '提示', {
						confirmButtonText: '我已知晓',
					})
						.then(() => {
							//
						})
						.catch(() => {
							this.$message.info('已取消');
						});
					return;
				}
			}
			this.editForm.dmsid = row.dmsid;
			this.editForm.stage = row.stage;
			this.editForm.stageStatus = row.stageStatus;
			this.editForm.collectionAmount = row.collectionAmount;
			this.editForm.amount = row?.amount || '';
			this.currIndex = index;
			this.productionList.length == 0 && this.getProductionList();
			this.getFinishWorkInfo(row.dmsid);
		},
		// 获取完工信息
		getFinishWorkInfo(dmsid) {
			const str = JSON.stringify({ dmsid: this.editForm.dmsid || dmsid });
			this.$axios
				.selectStageComplete(str)
				.then(res => {
					if (res.data.success) {
						this.dialogTitle = `${this.editForm.stage} 完工信息 ${this.editForm.amount ? '合同金额：' + this.editForm.amount : ''} ${this.hasReceived ? '（已回款）' : ''}`;
						this.editForm.reportDate = res.data.data.reportDate; //完工日期
						this.editForm.reportUrl = res.data.data.reportUrl;
						this.editForm.reportName = res.data.data.reportName;
						this.editForm.totalAmount = 0; //合计
						res.data.data.stageCompleteInfoVOS.map(item => {
							this.editForm.totalAmount = _.accAdd(+this.editForm.totalAmount, +item.amount);
						});
						this.editForm.stageCompleteInfoVOS = res.data.data.stageCompleteInfoVOS;
						this.dialogReport = true;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectStageComplete |' + error);
				});
		},
		// 保存交付过程文件
		saveProcessFile() {
			const str = JSON.stringify({
				fid: this.editForm.dpid,
				name: this.editForm.fileName,
				url: this.editForm.fileUrl,
			});
			this.$axios
				.releaseProcessFileSave(str)
				.then(res => {
					if (res.data.success) {
						this.$emit('update');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('releaseProcessFileSave |' + error);
				});
		},
		delProcessFile(dfid) {
			const str = JSON.stringify({
				dfid: dfid,
			});
			this.$axios
				.releaseProcessFileDelete(str)
				.then(res => {
					if (res.data.success) {
						this.$emit('update');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('releaseProcessFileDelete |' + error);
				});
		},
		// 阶段/展开业务完工凭证上传
		uploadFile2(file, item, type) {
			const isLt50M = file.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				this.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}
			const formData = new FormData();
			formData.append('file', file.file);
			this.isUpLoading = true;
			this.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						this.isUpLoading = false;
						if (type == '阶段') {
							this.detailForm.deliverStageManagementVOS[this.currIndex].reportName = res.data.data.fileName;
							this.detailForm.deliverStageManagementVOS[this.currIndex].reportUrl = res.data.data.path;
							this.editForm.reportName = res.data.data.fileName;
							this.editForm.reportUrl = res.data.data.path;
							// this.updateStage(this.currIndex);
						} else {
							item.file = res.data.data.path;
							this.isUpLoading = false;
							this.updateExpandStage();
						}
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.$message.warning(error.message);
				});
		},
		// 删除完工报告
		removeReport(index) {
			this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.detailForm.deliverStageManagementVOS[index].reportName = '';
					this.detailForm.deliverStageManagementVOS[index].reportUrl = '';
					this.editForm.reportUrl = '';
					this.editForm.reportName = '';
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		inputAmount() {
			this.editForm.totalAmount = 0;
			this.editForm.stageCompleteInfoVOS.map(item => {
				this.editForm.totalAmount = _.accAdd(+this.editForm.totalAmount, +item.amount);
			});
		},
		// 删除产品（完工）
		delProduction(production, index) {
			if (!production.sciid) {
				this.editForm.stageCompleteInfoVOS.splice(index, 1);
				this.editForm.totalAmount = 0;
				this.editForm.stageCompleteInfoVOS.map(item => {
					this.editForm.totalAmount = _.accAdd(+this.editForm.totalAmount, +item.amount);
				});
				return;
			}
			const str = JSON.stringify({ ...production });
			this.$axios
				.deleteStageCompleteInfoDeliver(str)
				.then(res => {
					if (res.data.success) {
						this.editForm.stageCompleteInfoVOS.splice(index, 1);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('deleteStageCompleteInfoDeliver |' + error);
				});
		},
		// 更新产品列表（完工）
		updateProductionList() {
			const dmsid = this.editForm.dmsid || this.detailForm.deliverStageManagementVOS[this.currIndex].dmsid;
			const str = JSON.stringify({
				dmsid,
				stageCompleteDetailDTOS: this.editForm.stageCompleteInfoVOS || [],
				twid: this.detailForm.twid,
			});
			this.$axios
				.uploadStageCompleteInfoDeliver(str)
				.then(res => {
					if (res.data.success) {
						// this.getFinishWorkInfo(dmsid);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('uploadStageCompleteInfo |' + error);
				});
		},
		// 添加产品（完工）
		addProduction() {
			const production = {
				amount: '',
				name: this.productionList[0].name ? this.productionList[0].name : '无',
				sciid: '',
				dmsid: this.editForm.dmsid,
				qty: '',
			};
			this.editForm.stageCompleteInfoVOS.push(production);
			// this.updateProductionList(production);
		},
		closeDialog() {
			this.$emit('update');
			this.editForm.complateMonthApply = '';
			this.editForm.postpone = '0';
			this.editForm.remark = '';
			this.processForm = _.resetValues(this.processForm);

			this.dialogProcess = false;
			this.dialogDelay = false;
			this.dialogReport = false;
		},
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = [];
			columns.forEach((column, columnIndex) => {
				if (columnIndex === 0) {
					means[columnIndex] = '合计';
				} else if (columnIndex !== 4) {
					means[columnIndex] = '';
				} else {
					const values = data?.map(item => {
						return Number(item[column.property]);
					});

					if (values.some(value => !isNaN(value))) {
						means[columnIndex] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return _.accAdd(prev, curr, 0);
							} else {
								return prev;
							}
						}, 0);
						if (!isNaN(means[columnIndex])) {
							means[columnIndex] = 
								<div class="text-right">
									<div>{means[columnIndex]} 元</div>
									<div>{_.accDiv(means[columnIndex], 10000, 5)} 万</div>
								</div>
							;
						} else {
							means[columnIndex] = '';
						}
					} else {
						means[columnIndex] = '';
					}
				}
			});
			return [means];
		},
		dateFormat: _.dateFormat, //日期format
	},
};
</script>

<style lang="scss">
.deliveryStage {
	width: 100%;
	// height: 100%;
	overflow: hidden;
	position: relative;

	.detail-table {
		min-height: 100px !important;
		height: auto !important;
	}

	.production-list {
		.input_textR {
			.el-input__inner {
				text-align: end;
			}
		}
	}

	.icon-close {
		font-size: 12px;
		top: -5px;
		right: -5px;
		position: absolute;
		color: #999;

		&:hover {
			font-weight: 600;
			background-color: #fff;
			color: #ff1744;
			border-color: #ff1744;
			cursor: pointer;
		}
	}

	.icon-remove {
		left: 10vw;
		right: 0;
	}
}
</style>
