# ImportTable 组件使用文档

## 组件简介

ImportTable 是一个数据导入管理组件，提供了文件上传、数据预览和导入功能。组件以弹窗形式展示，包含导入向导步骤，支持Excel文件上传、数据预览、执行导入和查看导入结果。

## 功能特点

- 完整的导入工作流程（四步导入向导）
- 支持下载导入模板
- 支持Excel文件(.xls/.xlsx)上传和验证
- 文件数据预览功能
- 导入进度实时展示
- 导入结果反馈（成功/失败）
- 导入错误详情下载

## 使用方法

### 基本引入

```javascript
import ImportTable from '@/components/ImportTable';
import ImportBtn from '@/components/ImportTable/ImportBtn';

export default {
  components: {
    ImportTable,
    ImportBtn
  }
}
```

### 模板调用

```html
<!-- 导入按钮组件 -->
<ImportBtn @trigger="handleImport" />

<!-- 导入表格组件 -->
<ImportTable ref="ImportTable" />
```

### 触发导入

```javascript
// 处理导入按钮点击
handleImport(type) {
  if (type === 'import') {
    // 打开导入弹窗
    this.$refs.ImportTable.openImport({
      API: 'importApiName', // 导入接口名称
      templateName: 'template_name.xlsx', // 模板文件名称
      dataName: '数据名称', // 如"客户"、"产品"等
      params: { /* 附加参数 */ } // 可选的请求参数
    });
  } else if (type === 'download') {
    // 仅下载模板
    this.$refs.ImportTable.openImport({
      templateName: 'template_name.xlsx',
      dataName: '数据名称',
      type: 'download'
    });
  }
}
```

## API 说明

### ImportTable 方法

| 方法名 | 说明 | 参数 |
| ----- | ---- | ---- |
| openImport | 打开导入弹窗或下载模板 | `{API, templateName, dataName, params, type}` |
| downloadTemplate | 下载导入模板 | `templateName`: 模板文件名称(可选) |
| closeMove | 关闭导入弹窗 | - |

### openImport 参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ----- | ---- | ---- | ---- |
| API | String | 是(除了type为'download') | 导入接口名称 |
| templateName | String | 是 | 模板文件名称 |
| dataName | String | 是 | 数据标识名称，如"客户"、"产品"等 |
| params | Object | 否 | 附加的请求参数 |
| type | String | 否 | 操作类型，不传则打开导入弹窗，'download'仅下载模板 |

### ImportBtn 属性

| 参数名 | 类型 | 默认值 | 说明 |
| ----- | ---- | ---- | ---- |
| show | Boolean | true | 是否显示导入按钮 |
| buttonType | String | 'text' | 按钮类型，同Element UI按钮类型 |
| buttonClass | String | 'icon-third_pl' | 按钮自定义类名 |
| buttonText | String | '导入' | 按钮文本 |
| iconClass | String | 'el-icon-arrow-down m0' | 图标类名 |
| dropdownItems | Array | 默认两项 | 下拉菜单项配置 |

### ImportBtn 事件

| 事件名 | 说明 | 回调参数 |
| ----- | ---- | ---- |
| trigger | 点击下拉菜单项触发 | type: 'import'或'download' |

## 注意事项

1. 上传文件必须是Excel格式(.xls或.xlsx)
2. 文件大小限制为10MB以内
3. 导入模板中的表头名称不可更改，表头行不能删除
4. 导入过程中不要关闭或刷新页面

## 示例代码

```javascript
// 示例：客户数据导入
<template>
  <div>
    <ImportBtn @trigger="handleImportCustomer" />
    <ImportTable ref="ImportTable" />
  </div>
</template>

<script>
import ImportTable from '@/components/ImportTable';
import ImportBtn from '@/components/ImportTable/ImportBtn';

export default {
  components: {
    ImportTable,
    ImportBtn
  },
  methods: {
    handleImportCustomer(type) {
      if (type === 'import') {
        // 打开导入弹窗
        this.$refs.ImportTable.openImport({
          API: 'importCustomerList',
          templateName: 'customer_template.xlsx',
          dataName: '客户',
          params: { customerId: this.customerId }
        });
      } else if (type === 'download') {
        // 仅下载模板
        this.$refs.ImportTable.openImport({
          templateName: 'customer_template.xlsx',
          dataName: '客户',
          type: 'download'
        });
      }
    }
  }
}
</script>
```

## 依赖项

组件依赖于以下工具/组件：
- Element UI
- u-table 表格组件
- Tooltips 提示组件
- XLSX 用于Excel文件解析
- debounce 防抖函数 