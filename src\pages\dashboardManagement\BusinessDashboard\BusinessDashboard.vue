<template>
	<div class="BusinessDashboard">
		<!-- 积分记录 -->
		<PointsLogList
			v-if="showMap.PointsLogList"
			ref="PointsLogList"
			isMoveCom
			@getTableOptions="tableOptions = $event"
			@openDetail="openDetail"
		/>
		<!-- ---------------报表组件----------------- -->
		<!-- 询盘表格 -->
		<InquiryTable
			v-if="showMap.InquiryTable"
			isMoveCom
			ref="InquiryTable"
			@openDetail="openInquiryDetail"
			@getOceanData="oceanData = $event"
			@getTableOptions="tableOptions = $event"
			@closeMove="queryTableData(1)"
			@openOcean="openOceanTable"
			@openImport="openImport"
			@openExport="openExport"
		/>
		<!-- 公海 -->
		<OceanTable
			v-if="showMap.OceanTable"
			ref="OceanTable"
			:oceanData="oceanData"
			@openDetail="openInquiryDetail"
			@close="
				$refs.InquiryTable.queryTableData();
				isPublicInquiry = false;
			"
		/>

		<!-- 跟单日报 -->
		<DocumentaryDaily
			v-if="showMap.DocumentaryDaily"
			isMoveCom
			ref="DocumentaryDaily"
			@openDetail="openInquiryDetail"
			@closeMove="queryTableData(1)"
		/>

		<!-- 合同表格 -->
		<ContractTable
			v-if="showMap.ContractTable"
			isMoveCom
			ref="ContractTable"
			@openInquiryDetail="openInquiryDetail"
			@openContract="openContractDetail"
			@getTableOptions="tableOptions = $event"
			@openExport="openExport"
		/>

		<!-- 交付详情 -->
		<DeliveryDetail
			v-if="showMap.DeliveryDetail"
			ref="DeliveryDetail"
			:userList="userList"
			@close="
				queryTableData(1);
				$refs?.DeliveryOverview?.queryTableData('init');
			"
		/>
		<!-- 交付项目总览 -->
		<DeliveryOverview
			v-if="showMap.DeliveryOverview"
			isMoveCom
			ref="DeliveryOverview"
			@openDetail="openDeliveryDetail"
			@getTableOptions="tableOptions = $event"
			@closeMove="queryTableData(1)"
		/>

		<!-- 我的客户 -->
		<CustomerList v-if="showMap.CustomerList" isMoveCom ref="CustomerList" @closeMove="queryTableData(1)" />
		<!-- 客户健康度管理 -->
		<ActivityRating v-if="showMap.ActivityRating" isMoveCom ref="ActivityRating" @closeMove="queryTableData(1)" />
		<!-- 差旅报销 -->
		<ExpenseReimbursement
			v-if="showMap.ExpenseReimbursement"
			isMoveCom
			ref="ExpenseReimbursement"
			@closeMove="queryTableData(1)"
		/>

		<!-- 团队详情 -->
		<TeamDetail v-if="showMap.TeamDetail" ref="TeamDetail" @refresh="queryTableData(1)" />

		<!-- 需求管理 -->
		<DemandManagement v-if="showMap.DemandManagement" isMoveCom ref="DemandManagement" @closeMove="queryTableData(1)" />
		<!-- 需求详情 -->
		<DemandManagementDetail
			v-if="showMap.DemandManagementDetail"
			ref="DemandManagementDetail"
			@close="queryTableData(1)"
			@openProject="openDetail('ProjectDetail', $event)"
		/>
		<!-- 项目详情 -->
		<ProjectDetail v-if="showMap.ProjectDetail" ref="ProjectDetail" @close="queryTableData(1)" />
		<!--  --- 详情组件（注意：组件之间会混合调用重叠或用z-index控制） ---  -->
		<!-- 询盘详情 -->
		<InquiryDetail
			v-if="showMap.InquiryDetail"
			ref="InquiryDetail"
			:isPublicInquiry="isPublicInquiry"
			:inquiryOptions="tableOptions"
			@openContract="openContractDetail"
			@close="
				queryTableData(1);
				$refs?.InquiryTable?.queryTableData('init');
			"
		/>
		<!-- 合同详情 -->
		<ContractDetail
			v-if="showMap.ContractDetail"
			ref="ContractDetail"
			:contractOptions="tableOptions"
			@close="
				queryTableData(1);
				$refs?.ContractTable?.queryTableData('init');
			"
		/>
		<!-- 课件详情 -->
		<CourseDetail v-if="showMap.CourseDetail" ref="CourseDetail" @close="queryTableData(1)" />
		<!-- --------------------- 通用组件 --------------------------- -->
		<!-- 数据导出弹窗 -->
		<ExportTable v-if="showMap.ExportTable" ref="ExportTable" />
		<!-- 导入弹窗 -->
		<ImportTable v-if="showMap.ImportTable" ref="ImportTable" @refresh="$refs.InquiryTable.queryTableData()" />

		<BaseLayout :showHeader="false">
			<template #main>
				<div class="dashboard-wrapper">
					<!-- 顶部 -->
					<DashboardCard :dataInfoList="dataInfoList" @openLogList="openLogList" @refresh="queryTableData('refresh')" />

					<!-- 主干 -->
					<div class="content-wrapper">
						<component
							:class="`content-item-${index + 1}`"
							v-for="(tableInfo, index) in tableList"
							:key="tableInfo.id"
							:is="'TableCom'"
							:tableInfo="tableInfo"
							@getTableOptions="tableOptions = $event"
							@openDetail="openDetail"
							@openList="openList"
						/>
					</div>
				</div>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
/* 通用 */
import DashboardCard from '../DashboardCard.vue'; //数据卡片
import TableCom from './BusinessTableCom.vue'; //表格
import ExportTable from '@/components/ExportTable'; //导出组件
import ImportTable from '@/components/ImportTable'; //数据导入
import PointsLogList from '../PointsLogList.vue'; //积分记录

/* 询盘相关组件 */
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryTable from '@/pages/inquiryManagement/inquiryAndDocumentary/inquiryTable'; //询盘表格
import DocumentaryDaily from '@/pages/inquiryManagement/inquiryAndDocumentary/documentaryDaily'; //跟单日报
import OceanTable from '@/pages/inquiryManagement/inquiryAndDocumentary/oceanTable.vue'; //公海列表

/* 合同/交付相关组件 */
import ContractTable from '@/pages/deliveryManagement/contractManagement/ContractTable.vue'; //合同表格
import ContractDetail from '@/pages/deliveryManagement/contractManagement/contractDetailCom.vue'; //合同明细
import DeliveryDetail from '@/pages/deliveryManagement/deliveryManagement/components/deliveryDetailCom.vue'; //交付明细
import DeliveryOverview from '@/pages/deliveryManagement/deliveryManagement/deliveryOverview.vue'; //交付项目总览

/* 客户管理/团队/三色灯相关组件 */
import CustomerList from '@/pages/customerManagement/customerList.vue'; //客户成功
import ActivityRating from '@/pages/customerManagement/activityRating.vue'; //健康评分
import TeamDetail from '@/pages/teamManagement/teamDataMain/TeamDetail'; //团队

/* 差旅相关组件 */
import ExpenseReimbursement from '@/pages/travelManagement/ExpenseReimbursement.vue'; //差旅报销
/* 需求管理 */
import DemandManagement from '@/pages/developmentManagement/demandManagement/DemandManagement.vue'; //需求管理
import DemandManagementDetail from '@/pages/developmentManagement/demandManagement/DemandManagementDetail.vue'; //需求管理
/* 项目管理 */
import ProjectDetail from '@/pages/developmentManagement/projectManagement/projectManagement/projectDetail'; //项目明细
/* 课件管理 */
import CourseDetail from '@/pages/trainManagement/onlineTraining/CourseDetail.vue'; //课件详情
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DashboardCard,
		TableCom,
		ExportTable,
		ImportTable,
		PointsLogList,

		InquiryDetail,
		InquiryTable,
		DocumentaryDaily,
		OceanTable,

		ContractTable,
		ContractDetail,
		DeliveryDetail,
		DeliveryOverview,
		TeamDetail,

		CustomerList,
		ActivityRating,
		ExpenseReimbursement,

		DemandManagement,
		DemandManagementDetail,
		ProjectDetail,

		CourseDetail,
	},
	name: 'BusinessDashboard', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			// 顶部数据卡片
			dataInfoList: [
				{ desc: '本月积分未排名📄', name: 'monthlyPointsSum', data: 0, class: 'bg-green' },
				// { desc: '年累计积分未排名', name: 'yearlyPointsSum', data: 0, class: 'bg-green' },
				{ desc: '本月已扣分📑', name: 'monthlyDeductPoints', data: 0, class: 'bg-red' },
				{ desc: '本月签单(万元)', name: 'monthlyContractAmount', data: 0, class: 'bg-blue' },
				// { desc: '年累计签单(万元)', name: 'yearlyContractAmount', data: 0, class: 'bg-blue' },
				{ desc: '本月新增客数', name: 'monthlyNewClient', data: 0, class: 'bg-blue' },
				{ desc: '本月流失客数', name: 'monthlyClientLost', data: 0, class: 'bg-red' },
				{ desc: '当前留存客数', name: 'totalActiveClient', data: 0, class: 'bg-blue' },
				{ desc: '本月收款(万元)', name: 'monthlyCollected', data: 0, class: 'bg-orange' },
				// { desc: '年累计收款(万元)', name: 'yearlyCollected', data: 0, class: 'bg-orange' },
				{ desc: '本月收入(元)', name: 'monthlyCommissionAmount', data: 0, class: 'bg-orange' },
				// { desc: '年累计收入(万元)', name: 'yearlyCommissionAmount', data: 0, class: 'bg-orange' },
			],
			// 主干各个表格
			tableList: [
				{
					id: 'abnormalWorks',
					title: '工作不规范(扣分)',
					subTitle: '近三天',
					badgeNum: 0,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '时间', colNo: 'pointsDate', align: 'center', width: '90' },
						{ colName: '工作项', colNo: 'operation', align: 'left', width: '' },
						{ colName: '扣分', colNo: 'points', align: 'right', width: '50' },
					],
				},
				{
					id: 'incompleteConsultingList',
					title: '未完成的咨询',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '询盘管理',
					tableColumn: [
						{ colName: '询盘编号', colNo: 'number', align: 'left', width: '100' },
						{ colName: '客户称呼', colNo: 'customerName', align: 'left', width: '80' },
						{ colName: '时间', colNo: 'createTime', align: 'center', width: '' },
						{ colName: '区域', colNo: 'region', align: 'left', width: '' },
					],
				},
				{
					id: 'expectedSigningList',
					title: '本月预计成交',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '询盘管理',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '' },
						{ colName: '金额(万)', colNo: 'estimatedAmount', align: 'right', width: '' },
					],
				},
				{
					id: 'teamClientWillLostWithin60Days',
					title: '60天内即将流失的客户',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '我的客户',
					tableColumn: [
						{ colName: '客户', colNo: 'teamName', align: 'left', width: '80' },
						{ colName: '到期日', colNo: 'validTo', align: 'center', width: '' },
						// { colName: '', colNo: 'xxxNo', align: 'left', width: '' },
					],
				},

				{
					id: 'tripReimbursementList',
					title: '差旅费报销',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '费用报销',
					tableColumn: [
						{ colName: '出差日期', colNo: 'tripBeginDate', align: 'center', width: '80' },
						{ colName: '客户', colNo: 'tripClientName', align: 'left', width: '' },
						{ colName: '天数', colNo: 'tripDays', align: 'right', width: '80' },
						// { colName: '', colNo: 'xxxNo', align: 'left', width: '' },
					],
				},

				{
					id: 'reviewedWorks',
					title: '工作质量评价',
					subTitle: '近三天',
					badgeNum: 0,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '检查时间', colNo: 'auditDate', align: 'center', width: '90' },
						{ colName: '工作项', colNo: 'operation', align: 'left', width: '' },
						{ colName: '质量评价', colNo: 'auditStatus', align: 'left', width: '' },
						// { colName: '', colNo: 'xxxNo', align: 'left', width: '' },
					],
				},
				{
					id: 'documentaryTodoList',
					title: '今日跟单任务',
					// emptyText: '今日暂无待办跟单任务 Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '跟单日报',
					tableColumn: [
						{ colName: '询盘编号', colNo: 'number', align: 'left', width: '100' },
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '80' },
						{ colName: '计划事项', colNo: 'nextPlan', align: 'left', width: '' },
					],
				},
				{
					id: 'firstDeliverStageNotCollected',
					title: '未清首期款',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '项目总览',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '' },
						{ colName: '金额(万)', colNo: 'amount', align: 'right', width: '' },
						// { colName: '到期日', colNo: 'complateMonth', align: 'center', width: '65' },
					],
				},
				{
					id: 'teamClientLostInThePast30Days',
					title: '近30天已流失的客户',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '我的客户',
					tableColumn: [
						{ colName: '客户', colNo: 'teamName', align: 'left', width: '' },
						{ colName: '到期日', colNo: 'validTo', align: 'center', width: '' },
						// { colName: '', colNo: 'xxxNo', align: 'left', width: '' },
					],
				},
				{
					id: 'teamClientHealthWarnList',
					title: '客户健康度预警',
					badgeNum: 0,
					data: [],
					button: '健康评分',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '' },
						{ colName: '最近得分', colNo: 'points', align: 'right', width: '100' },
						{ colName: '活跃用户', colNo: 'qty', align: 'left', width: '' },
					],
				},
				{
					id: 'pointsLogList',
					title: '积分记录',
					subTitle: '近三天',
					badgeNum: 0,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '时间', colNo: 'pointsDate', align: 'center', width: '90' },
						{ colName: '工作项', colNo: 'operation', align: 'left', width: '' },
						{ colName: '积分', colNo: 'points', align: 'right', width: '50' },
					],
				},
				{
					id: 'selectHealthDegreeOfBPVOS',
					title: 'BP健康度',
					subTitle: '',
					badgeNum: 0,
					data: [],
					button: '',
					tableColumn: [
						{ colName: 'BP', colNo: 'bpname', align: 'left', width: '' },
						{ colName: '咨询率', colNo: 'consultRate', align: 'right', width: '60' },
						{ colName: '电话时长（平均）', colNo: 'averageCallDuration', align: 'right', width: '' },
						{ colName: '合格率', colNo: 'qualifiedRate', align: 'right', width: '60' },
						{ colName: '健康度', colNo: 'healthDegree', align: 'right', width: '60' },
					],
				},
				{
					id: 'onlineTrainingList',
					title: '在线培训',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '培训计划', colNo: 'trainPlan', align: 'left', width: '70' },
						{ colName: '课程', colNo: 'course', align: 'left', width: '' },
						{ colName: '进度', colNo: 'progress', align: 'right', width: '50' },
						// { colName: '剩余', colNo: 'remainingTime', align: 'right', width: '50' },
						{ colName: '到期时间', colNo: 'planEndTime', align: 'center', width: '95' },
					],
				},
				{
					id: 'documentaryLackList',
					title: '跟单缺失',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '询盘管理',
					tableColumn: [
						{ colName: '询盘编号', colNo: 'number', align: 'left', width: '100' },
						{ colName: '客户称呼', colNo: 'customerName', align: 'left', width: '80' },
						{ colName: '时间', colNo: 'createTime', align: 'center', width: '' },
						{ colName: '区域', colNo: 'region', align: 'left', width: '' },
					],
				},
				{
					id: 'deliverStageExpiringWithin45Days',
					title: '45天内即将到期项目阶段',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '项目总览',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '70' },
						{ colName: '阶段', colNo: 'stage', align: 'left', width: '' },
						// { colName: '到期日', colNo: 'complateMonth', align: 'center', width: '' },
					],
				},

				{
					id: 'incompleteDeliverySchedule',
					title: '未完成的项目',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '项目总览',
					tableColumn: [{ colName: '项目进度', colNo: 'deliveryScheduleStateVOS', align: 'left', width: '' }],
				},
				{
					id: 'selectDemandSuperVisionListVOS',
					title: '需求清单',
					badgeNum: 0,
					isDataList: false,
					data: [],
					button: '需求管理',
					tableColumn: [
						{ colName: '提交人', colNo: 'submissionName', align: 'left', width: '110' },
						{ colName: '提交时间', colNo: 'submissionTime', align: 'left', width: '100' },
						{ colName: '需求', colNo: 'demandDocumentName', align: 'left', width: '' },
						{ colName: '人天合计', colNo: 'manDayTotal', align: 'right', width: '80' },
						{ colName: '状态', colNo: 'status', align: 'left', width: '120' },
					],
				},
				{
					id: 'documentaryExpiredList',
					title: '逾期跟单计划',
					//emptyText: 'Very Good !',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '跟单日报',
					tableColumn: [
						{ colName: '询盘编号', colNo: 'number', align: 'left', width: '100' },
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '80' },
						{ colName: '计划事项', colNo: 'nextPlan', align: 'left', width: '' },
					],
				},
				{
					id: 'stagingContractList',
					title: '未提交合同',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '合同管理',
					tableColumn: [
						{ colName: '询盘编号', colNo: 'number', align: 'left', width: '100' },
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '' },
						{ colName: '签单月份', colNo: 'dealMonth', align: 'center', width: '80' },
					],
				},
				{
					id: 'deliverStageExpired',
					title: '已逾期的项目阶段',
					badgeNum: 0,
					isDataList: true,
					data: [],
					button: '',
					tableColumn: [
						{ colName: '客户', colNo: 'companyName', align: 'left', width: '70' },
						{ colName: '阶段', colNo: 'stage', align: 'left', width: '' },
						// { colName: '到期日', colNo: 'dueDate', align: 'center', width: '' },
					],
				},
			],
			titleName: '',

			isPublicInquiry: false, //公海询盘
			oceanData: [], //公海数据
			tableOptions: [], //表格数据用于组件里上下页切换
			userList: [], //用户选项数据

			showMap: {
				ImportTable: false,
				ExportTable: false,
				PointsLogList: false,

				InquiryDetail: false,
				InquiryTable: false,
				OceanTable: false,
				DocumentaryDaily: false,
				ContractTable: false,
				ContractDetail: false,
				DeliveryOverview: false,
				DeliveryDetail: false,
				TeamDetail: false,
				CustomerList: false,
				ActivityRating: false,
				ExpenseReimbursement: false,

				DemandManagement: false,
				DemandManagementDetail: false,
				ProjectDetail: false,
				CourseDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
		this.queryUserByTwids();
	},
	activated() {
		this.queryTableData(1);
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {
		this.tableList = null;
		this.dataInfoList = null;
	}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 通用打开详情
		openDetail(ref, ...args) {
			this.showMap[ref] = true; // 动态设置状态变量
			this.$nextTick(() => {
				if (!this.$refs[ref]) return this.$message.warning(`${ref}组件未加载完毕，请稍后重试！`);
				// 项目详情特殊处理
				if (ref == 'ProjectDetail') {
					const pmid = args[0]?.pmid;
					this.$refs.ProjectDetail[pmid ? 'showDetailCom' : 'getDetailForm'](args[0]);
					return;
				}
				// 其他组件通用处理
				if (typeof this.$refs[ref].showDetailCom === 'function') {
					this.$refs[ref].showDetailCom(...args);
				} else if (typeof this.$refs[ref].openDetail === 'function') {
					this.$refs[ref].openDetail(...args);
				}
			});
		},
		// 通用打开清单
		openList(ref, ...args) {
			this.showMap[ref] = true; // 动态设置状态变量
			this.$nextTick(() => {
				if (!this.$refs[ref]) return this.$message.warning(`${ref}组件未加载完毕，请稍后重试！`);
				this.$refs[ref].queryTableData(...args);
			});
		},
		// 打开询盘详情
		openInquiryDetail(...args) {
			this.openDetail('InquiryDetail', ...args);
		},
		// 打开合同详情
		openContractDetail(...args) {
			this.openDetail('ContractDetail', ...args);
		},
		// 打开交付详情
		openDeliveryDetail(...args) {
			this.openDetail('DeliveryDetail', ...args);
		},
		// 打开公海
		openOceanTable(...args) {
			this.showMap.OceanTable = true;
			this.isPublicInquiry = true;
			this.$nextTick(() => {
				this.$refs.OceanTable.showDetailCom(...args);
			});
		},
		// 导入
		openImport(...args) {
			this.showMap.ImportTable = true;
			this.$nextTick(() => {
				this.$refs.ImportTable.openImport(...args);
			});
		},
		// 导出
		openExport(...args) {
			this.showMap.ExportTable = true;
			this.$nextTick(() => {
				this.$refs.ExportTable.openExport(...args);
			});
		},
		// 打开积分记录
		openLogList(...args) {
			this.showMap.PointsLogList = true;
			this.$nextTick(() => {
				this.$refs.PointsLogList.queryTableData(...args);
			});
		},

		// 查询表格数据
		queryTableData: debounce(function (type) {
			const API = 'fetchSalesmanDashboard'; //接口
			this.$axios[API](JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						const DATA = res.data.data;
						// 顶部数据卡片
						this.dataInfoList.forEach(item => {
							item.data = DATA[item.name] || 0;
							if (item.name == 'monthlyPointsSum') {
								item.desc = `本月积分${DATA.monthlyPointsRank ? '第' + DATA.monthlyPointsRank : '未排'}名📄`;
							}
							if (item.name == 'yearlyPointsSum') {
								item.desc = `年累计积分${DATA.yearlyPointsRank ? '第' + DATA.yearlyPointsRank : '未排'}名`;
							}
						});

						// 主干各个表格
						this.tableList.forEach(item => {
							item.title === '积分记录' && (item.yesterdayPoints = DATA.yesterdayPoints);
							item.title === '工作不规范(扣分)' && (item.yesterdayDeductPoints = DATA.yesterdayDeductPoints);
							if (item.isDataList) {
								item.badgeNum = DATA[item.id]?.count || 0;
								item.data =
									DATA[item.id]?.dataList.map(item => {
										const deliverManagement = item?.deliverManagement || {}; //阶段信息
										const inquiryDocumentary = item?.inquiryDocumentary || {}; //询盘信息
										return { ...deliverManagement, ...inquiryDocumentary, ...item };
									}) || [];
							} else {
								item.data = DATA[item.id] || [];
							}
						});
						console.log('CARDS:', this.dataInfoList);
						console.log('TABLES:', this.tableList);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			this.$axios
				.selectSalesmanByTwids(JSON.stringify({ twids: [], counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),
	},
};
</script>
<style lang="scss">
.BusinessDashboard .table-card {
	padding: 5px 15px !important;
}
</style>
<style lang="scss" scoped>
.BusinessDashboard {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	overflow: hidden;
	overflow-y: auto;

	font-size: 14px;
	color: #666;

	.dashboard-wrapper {
		width: 100%;
		height: calc(100vh - 138px);
		padding-bottom: 10px;
		position: relative;

		display: flex;
		flex-direction: column;

		.border-radius-8 {
			border-radius: 8px;
		}

		/* @use: https://cssgrid-generator.netlify.app/ */
		.content-wrapper {
			width: 100%;
			height: calc(100% - 80px);
			display: grid;
			grid-template-columns: repeat(14, 1fr);
			grid-template-rows: repeat(11, 1fr);
			grid-column-gap: 8px;
			grid-row-gap: 8px;
			.content-item-1 {
				grid-area: 1 / 1 / 3 / 4;
			}
			.content-item-2 {
				grid-area: 1 / 4 / 3 / 8;
			}
			.content-item-3 {
				grid-area: 1 / 8 / 3 / 10;
			}
			.content-item-4 {
				grid-area: 1 / 10 / 3 / 12;
			}
			.content-item-5 {
				grid-area: 1 / 12 / 3 / 15;
			}
			.content-item-6 {
				grid-area: 3 / 1 / 6 / 4;
			}
			.content-item-7 {
				grid-area: 3 / 4 / 6 / 8;
			}
			.content-item-8 {
				grid-area: 3 / 8 / 6 / 10;
			}
			.content-item-9 {
				grid-area: 3 / 10 / 6 / 12;
			}
			.content-item-10 {
				grid-area: 3 / 12 / 6 / 15;
			}
			.content-item-11 {
				grid-area: 6 / 1 / 8 / 4;
			}
			.content-item-12 {
				grid-area: 8 / 1 / 10 / 4;
			}
			.content-item-13 {
				grid-area: 10 / 1 / 12 / 4;
			}
			.content-item-14 {
				grid-area: 6 / 4 / 8 / 8;
			}
			.content-item-15 {
				grid-area: 6 / 8 / 10 / 10;
			}
			.content-item-16 {
				grid-area: 6 / 10 / 9 / 15;
			}
			.content-item-17 {
				grid-area: 9 / 10 / 12 / 15;
			}
			.content-item-18 {
				grid-area: 8 / 4 / 10 / 8;
			}
			.content-item-19 {
				grid-area: 10 / 4 / 12 / 8;
			}
			.content-item-20 {
				grid-area: 10 / 8 / 12 / 10;
			}
		}
	}
}
</style>
