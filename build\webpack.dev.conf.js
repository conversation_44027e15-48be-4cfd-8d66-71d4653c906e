'use strict';
const utils = require('./utils');
const webpack = require('webpack');
const config = require('../config');
const { merge } = require('webpack-merge');
const path = require('path');
const baseWebpackConfig = require('./webpack.base.conf');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { codeInspectorPlugin } = require('code-inspector-plugin');
const portfinder = require('portfinder');
const FriendlyErrorsWebpackPlugin = require('friendly-errors-webpack-plugin');
const HOST = process.env.HOST;
const PORT = process.env.PORT && Number(process.env.PORT);

//在开发环境下不要使用压缩版本，不然你就失去了所有常见错误相关的警告!
const cdnConfig = [
	// { js: 'static/<EMAIL>' },
	// { js: 'static/<EMAIL>' },
	// { js: 'static/<EMAIL>' },
	// { js: 'static/<EMAIL>' },
	// { js: 'static/<EMAIL>' },
	// { js: 'static/<EMAIL>' },
	// { js: 'static/<EMAIL>', css: 'static/<EMAIL>' },
	// { js: 'static/<EMAIL>' },
];

const devWebpackConfig = merge(baseWebpackConfig, {
	mode: 'development',
	stats: 'errors-warnings', //只在发生错误或有警告时输出
	devtool: config.dev.devtool,
	// these devServer options should be customized in /config/index.js
	devServer: {
		hot: true,
		compress: true,
		host: HOST || config.dev.host,
		port: PORT || config.dev.port,
		open: config.dev.autoOpenBrowser,
		client: {
			logging: 'error',
			overlay: config.dev.errorOverlay ? { warnings: false, errors: true } : false,
		},
		static: {
			publicPath: config.dev.assetsPublicPath,
		},
		proxy: config.dev.proxyTable,
	},
	cache: true,
	plugins: [
		//用于注入全局变量，一般用在环境变量上。
		new webpack.DefinePlugin({
			'process.env': require('../config/dev.env'),
		}),

		//将一个页面模板打包到dist目录下，默认都是自动引入js or css
		// https://github.com/ampedandwired/html-webpack-plugin
		new HtmlWebpackPlugin({
			filename: 'index.html',
			template: 'index.html',
			inject: true,
			favicon: path.resolve(__dirname, '../src/assets/szgcfavicon.ico'),
			cdnConfig: cdnConfig, // cdn配置
			onlyCss: false, //dev下只加载css
		}),
		//用于将文件拷贝到某个目录下
		new CopyWebpackPlugin({
			patterns: [
				{
					from: path.resolve(__dirname, '../static'),
					to: config.dev.assetsSubDirectory,
					globOptions: {
						dot: true,
						gitignore: true,
						ignore: ['.*'],
					},
				},
			],
		}),
		//错误提示插件
		new FriendlyErrorsWebpackPlugin({
			compilationSuccessInfo: {
				messages: [`✨ Your application is running here ：http://localhost:${config.dev.port}/ ✅ \n`],
			},
			clearConsole: true,
		}),
		// 点击页面上的 DOM，自动打开你 IDE 并将光标定位到 DOM 对应的源代码位置
		codeInspectorPlugin({
			bundler: 'webpack',
			hideDomPathAttr: true, //是否在浏览器控制台中隐藏 DOM 元素上的 data-insp-path 属性
		}),
	],
});

module.exports = new Promise((resolve, reject) => {
	portfinder.basePort = process.env.PORT || config.dev.port;
	portfinder.getPort((err, port) => {
		if (err) {
			reject(err);
		} else {
			// publish the new Port, necessary for e2e tests
			process.env.PORT = port;
			// add port to devServer config
			devWebpackConfig.devServer.port = port;

			// Add FriendlyErrorsPlugin
			// devWebpackConfig.plugins.push(
			// 	new FriendlyErrorsPlugin({
			// 		compilationSuccessInfo: {
			// 			messages: [`Your application is running here: http://${devWebpackConfig.devServer.host}:${port}`]
			// 		},
			// 		onErrors: config.dev.notifyOnErrors ? utils.createNotifierCallback() : undefined
			// 	})
			// );
			resolve(devWebpackConfig);
		}
	});
});
