<template>
	<div v-if="showRouterBar" class="router-link-wrapper flex-align-center">
		<div class="router-link-main">
			<router-link
				v-for="(tag, index) in routerTags"
				:key="tag.path"
				:to="{ path: tag.path, query: { queryId: userInfos?.adminUserVO.phoneNo } }"
			>
				<el-tag
					type="info"
					:class="cacheTags.includes(tag.name) ? 'cacheTag' : ''"
					size="small"
					closable
					@close.prevent.stop="handleCloseTag(tag, index)"
				>
					{{ tag.title }}
				</el-tag>
			</router-link>
			<!-- 更多选项 -->
			<el-dropdown v-show="this.routerTags.length > 1" class="router-link-more" size="mini" placement="top-start">
				<el-tag type="info" class="el-icon-arrow-down tag-more"> 更多</el-tag>
				<el-dropdown-menu slot="dropdown">
					<div class="flex-column">
						<el-dropdown-item @click.native="closeTags('current')">
							<span class="el-icon-circle-close"> 关闭当前标签页</span>
						</el-dropdown-item>
						<el-dropdown-item @click.native="closeTags('other')">
							<span class="el-icon-remove-outline"> 关闭其他标签页</span>
						</el-dropdown-item>
						<el-dropdown-item divided @click.native="closeTags('left')">
							<span class="el-icon-d-arrow-left"> 关闭左侧标签页</span>
						</el-dropdown-item>
						<el-dropdown-item @click.native="closeTags('right')">
							<span class="el-icon-d-arrow-right"> 关闭右侧标签页</span>
						</el-dropdown-item>
						<el-dropdown-item divided @click.native="closeTags('all')">
							<span class="el-icon-help"> 关闭所有标签页</span>
						</el-dropdown-item>
					</div>
				</el-dropdown-menu>
			</el-dropdown>
		</div>
	</div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
	name: 'RouterTags', //该组件主要用于展示顶部路由标签，包括多方式的关闭，计划陆续增加重置刷新，局部全屏等功能
	components: {},
	data() {
		return {
			routerTags: [], //访问过的路由
			cacheTags: [], //需要缓存的路由
			showRouterBar: false, // 导航栏显示控制
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		routerTags(newValue) {
			// 监听tags变化，并保存到本地
			window?.localStorage.setItem('OPS_RouterTags', JSON.stringify(newValue));
			this.$store.commit('setRouterTags', newValue);

			// 移除不在 routerTags 中的 cacheTags
			this.cacheTags = this.cacheTags.filter(tag => this.routerTags.some(routerTag => routerTag.name === tag));
			this.$store.commit('setCacheTags', this.cacheTags);
		},
		// 监听路由变化 保存routerTags 判断是否进入全屏看板
		'$route.path': {
			immediate: true,
			handler(newPath) {
				// console.log(this.$route);

				// 当前路由的信息
				const routePath = this.$route.path; //当前路由
				const routeName = this.$route.name; //当前路由对应的组件名
				const routeTitle = this.$route.meta.title; //当前路由的标题
				const noCache = this.$route.meta.noCache; //是否缓存
				const isView = this.$route.meta.isView; //是否看板

				// 设置浏览器标签页标题 document.title
				// document.title = routeTitle + ' | OPS';

				// 判断 routerTags 数组中是否存在与 routePath 匹配的 path
				const existingTag = this.routerTags.find(tag => tag.path === routePath);
				// 如果routerTags不存在该路由，则将当前路由的信息添加到routerTags中
				if (!existingTag) {
					this.routerTags.push({
						path: routePath,
						name: routeName,
						title: routeTitle,
						noCache,
						// id: this.findResourceID(routeTitle), // 添加资源id
						timestamp: Date.now(), // 添加时间戳
					});
				} else {
					// 如果routerTags存在该路由，则更新该路由的时间戳
					existingTag.timestamp = Date.now();
				}
				// 保留最多n个n天前的标签，先进先出
				this.limitRouterTags(15, 7);

				// 缓存最近操作的路由
				if (!noCache) {
					//  先去重再保留最新的5条
					this.cacheTags = Array.from(new Set([routeName, ...this.cacheTags])).slice(0, 5);
				}

				// 判断是否进入全屏看板（如果是看板，则去掉padding且不显示路由标签页bar）
				this.$nextTick(() => {
					// 1. 判断是否进入全屏看板（如果是看板，则去掉padding且不显示路由标签页bar）
					this.showRouterBar = !isView;
					const elMain = document.querySelector('.el-main');
					if (!elMain) return;
					if (isView) {
						elMain.style.padding = '0';
						console.log('进入看板：' + newPath);
					} else {
						elMain.style.padding = '0 20px'; // 恢复为默认的 padding 值
					}
					// 2.自动登录时若资源id不存在则根据当前路由查找资源id并保存menuTitle（权限相关）
					// if (!this.menuTitle.id) {
					// 	this.findResourceID(routeTitle);
					// }
				});
			},
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.routerTags = JSON.parse(window?.localStorage.getItem('OPS_RouterTags')) || [
			{ path: '/welCome', name: 'welCome', title: '首页' },
		];
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {
		this.routerTags = null;
	},
	// 方法集合
	methods: {
		// 关闭标签页（多操作）
		closeTags(type) {
			const routeName = this.$route.name; //当前路由对应的组件名
			const findIndex = this.routerTags.findIndex(tag => tag.name === routeName);
			const actions = {
				current: () => {
					// this.routerTags = this.routerTags?.filter(tag => tag.name !== routeName); // 关闭当前标签页
					this.handleCloseTag(this.routerTags[findIndex], findIndex);
				},
				other: () => {
					this.routerTags = this.routerTags?.filter(tag => tag.name === routeName); // 关闭其他标签页（除当前外）
				},
				left: () => this.routerTags.splice(0, findIndex), // 关闭左侧标签页
				right: () => this.routerTags.splice(findIndex + 1), // 关闭右侧标签页
				all: () => {
					// 关闭所有的tag，返回首页
					this.routerTags = [];
					this.$router.replace({
						path: '/welCome',
						query: {
							queryId: this.userInfos?.adminUserVO.phoneNo,
						},
					});
				},
			};
			actions[type] && actions[type]();
		},
		// 关闭选中的tag，返回上一个tag
		handleCloseTag(tag, index) {
			if (this.routerTags.length == 1) return; //剩下最后一个不允许关闭
			if (tag.name == this.$route.name) {
				this.$router.replace({
					path: this.routerTags[index === 0 ? index + 1 : index - 1].path,
					query: {
						queryId: this.userInfos?.adminUserVO.phoneNo,
					}, //删除当前页面后自动进入上一个页面
				});
			}
			this.routerTags.splice(this.routerTags.indexOf(tag), 1);
		},
		// 保留最多n个n天前的标签，先进先出
		limitRouterTags(limit = 10, days = 7) {
			if (this.routerTags.length >= limit) {
				const sevenDaysAgo = Date.now() - days * 24 * 60 * 60 * 1000; // 计算n天前的时间戳
				let earliestTag = null; // 用于存储最早的标签
				this.routerTags = this.routerTags.filter(tag => {
					if (tag.timestamp && tag.timestamp >= sevenDaysAgo) {
						// 只处理有时间戳且在n天内的标签
						if (!earliestTag || tag.timestamp < earliestTag.timestamp) {
							earliestTag = tag; // 更新最早的标签
						}
						return true; // 保留该标签
					}
					return false; // 过滤掉没有时间戳或超过n天的标签
				});
				if (earliestTag) {
					this.routerTags = this.routerTags.filter(tag => tag !== earliestTag); // 移除最早的标签
				}
			}
		},
		// 查找资源id
		// findResourceID(title) {
		// 	function findNodeByResourceName(nodes, resourceName) {
		// 		for (const node of nodes) {
		// 			if (node.resourceName === resourceName && !node.resources) {
		// 				return node; // 找到匹配的节点，返回该节点
		// 			}
		// 			if (node.resources) {
		// 				const foundNode = findNodeByResourceName(node.resources, resourceName); // 递归查找子节点
		// 				if (foundNode) {
		// 					return foundNode; // 如果在子节点中找到匹配的节点，返回该节点
		// 				}
		// 			}
		// 		}
		// 		return null; // 如果未找到匹配的节点，返回null
		// 	}

		// 	const menuArr = this.userInfos?.adminMenuAndButtonVO?.menuList || [];
		// 	const foundNode = findNodeByResourceName(menuArr, title);

		// 	if (foundNode?.rcid) {
		// 		this.menuTitle.id = foundNode.rcid;
		// 		this.$store.commit('setMenuTitle', this.menuTitle);
		// 	} else {
		// 		console.log('🚨 未匹配到到相应节点，可能会影响权限控制请排查原因！', foundNode);
		// 	}
		// },
	},
};
</script>

<style lang="scss">
@import '@/styles/element-variables.scss';

.router-link-wrapper {
	.router-link-main {
		position: relative;
		background: #f2f2f2;
		width: 100%;
		height: 32px;
		overflow-x: scroll;
		overflow-y: hidden;
		display: flex;
		align-items: flex-start;

		::-webkit-scrollbar {
			width: 3px !important;
			height: 3px !important;
		}

		&::-webkit-scrollbar-thumb {
			/*滚动条里面小方块*/
			border-radius: 3px !important;
			background: #e2e1e1;
		}

		&::-webkit-scrollbar-track {
			/*滚动条里面轨道*/
			border-radius: 3px !important;
			background: #f2f2f2(48, 65, 86, 0.9);
		}

		/*  routerlink 预设样式 类名在路由里设置*/
		.linkActive {
			.el-tag {
				font-weight: 400;
				color: $--color-primary !important;
				background: #f2f2f2;
				border-bottom: none;
				zoom: 1.1;
				&:before {
					content: '●';
					margin-right: 5px;
					zoom: 1.1;
					line-height: initial;
				}
			}
		}
		.cacheTag {
			color: #888;
			&:before {
				content: '●';
				margin-right: 2px;
				line-height: initial;
			}
		}
	}

	.router-link-more {
		position: sticky;
		right: 0;
		.tag-more {
			height: 25px;
			line-height: 25px;
			font-size: 12px;
		}
	}
}
</style>
