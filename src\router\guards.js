/**
 * 路由守卫
 */
import { isUserLoggedIn } from './auth';
import { cancelAllPendingRequests } from '@/fetch/cancel';

/**
 * 路由前置守卫
 * @param {Object} router - 路由实例
 */
export const setupBeforeEachGuard = router => {
	router.beforeEach((to, from, next) => {
		// 路由变化时取消未完成的请求
		cancelAllPendingRequests('🚨 路由已发生变化，未完成的请求已取消！');

		// 设置页面标题
		if (to.meta.title) {
			// document.title = `${to.meta.parentTitle} - ${to.meta.title}`;
			document.title = `${to.meta.title} | OPS`;
		}

		// 检查路由是否需要登录权限
		const requiresAuth = to.matched.some(record => record.meta.requireAuth);

		// 如果是进入登录页，直接放行
		if (to.path === '/login') {
			next();
			return;
		}

		// 检查是否已登录
		const loggedIn = isUserLoggedIn();

		// 需要登录但未登录，重定向到登录页
		if (requiresAuth && !loggedIn) {
			next({
				path: '/login',
				query: { redirect: to.fullPath },
			});
			return;
		}

		// 已登录时访问根路径或登录页，重定向到首页
		if (loggedIn && (to.path === '/' || to.path === '/login')) {
			next({ path: '/welCome' });
			return;
		}

		// 已登录且路由存在，正常导航
		if (loggedIn && to.matched.length) {
			next();
			return;
		}

		// 路由不存在，导航到404页面
		if (to.matched.length === 0) {
			next({ path: '/notfound' });
			return;
		}

		// 默认情况，正常导航
		next();
	});
};

/**
 * 路由后置守卫
 * @param {Object} router - 路由实例
 */
export const setupAfterEachGuard = router => {
	router.afterEach(() => {
		// 路由跳转后滚动到顶部
		window.scrollTo(0, 0);
	});
};

export default {
	setupBeforeEachGuard,
	setupAfterEachGuard,
};
