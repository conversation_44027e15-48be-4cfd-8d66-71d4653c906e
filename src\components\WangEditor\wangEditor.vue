<template>
	<div class="wangEditor">
		<div class="border">
			<!-- 工具栏 -->
			<Toolbar class="border-bottom" v-if="hasToolBar" :editor="editor" :defaultConfig="toolbarConfig" />
			<!-- 编辑器 -->
			<Editor
				:style="{ height: editorHeight + 'px', 'overflow-y': 'hidden' }"
				:defaultConfig="editorConfig"
				v-model="html"
				@onChange="onChange"
				@onCreated="onCreated"
				@customPaste="customPaste"
			/>
		</div>
	</div>
</template>

<script>
// 富文本编辑器 wangEditor 5 文档： https://www.wangeditor.com/ 使用理由：国人出品，具备有中文生态的天然优势 ，有问题搜搜看解决方案或github上的issue
// 如果不满足需求，可以考虑使用： https://www.tiny.cloud/docs/tinymce/6/
import { mapGetters } from 'vuex';
import { Boot } from '@wangeditor/editor';
import attachmentModule from '@wangeditor/plugin-upload-attachment';
import preViewImage from './preViewImage.js'; //hoverbar 图片预览
// 注册插件，要在创建编辑器之前注册，且只能注册一次，不可重复注册。
Boot.registerModule(attachmentModule);
Boot.registerMenu(preViewImage);

import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { debounce } from '@/util/common';

// 服务器地址:生产环境为当前服务器地址，开发环境为dev.env配置的地址
const SERVER_HOST =
	process.env.NODE_ENV == 'production' && window.location.origin ? `${window.location.origin}/` : process.env.API_HOST;
// 上传文件接口地址
const UPLOAD_API = SERVER_HOST + 'background/web/ProjectManagementTaskController/contextUploadFile';

export default {
	name: 'wangEditor',
	components: { Editor, Toolbar },
	props: {
		// v-model
		value: {
			type: String,
			default: '',
		},
		// 是否显示工具栏
		hasToolBar: {
			type: Boolean,
			default: true,
		},
		// 编辑器高度
		editorHeight: {
			type: Number,
			default: 300, //编辑器高度不能小于300px
		},

		// 是否禁用编辑器
		disabled: [Boolean, Number],
	},
	data() {
		return {
			editor: null,
			html: this.value, //编辑器内容（为html格式 如：<p>hello&nbsp;world</p>）
			progressMessage: null, // 添加进度消息引用
			toolbarConfig: {
				// toolbarKeys: [ /* 显示哪些菜单，如何排序、分组 */ ],
				// excludeKeys: [ /* 隐藏哪些菜单 */ ],
				insertKeys: {
					index: 33, // 自定义插入的位置
					keys: ['uploadAttachment'], // "上传附件"菜单
				},
			},
			editorConfig: {
				placeholder: '请输入内容...',
				autoFocus: false, //自动聚焦
				// 在编辑器中，点击选中"附件"节点时，要弹出的菜单
				hoverbarKeys: {
					attachment: {
						menuKeys: ['downloadAttachment'], // "下载附件"菜单
					},
					image: {
						menuKeys: [
							'imageWidth30',
							'imageWidth50',
							'imageWidth100',
							// '|',
							// 'editImage',
							// 'viewImageLink',
							// 'deleteImage',
							'|',
							'preViewImage', //预览图片
						],
					},
				},
				// 所有的菜单配置，都要在 MENU_CONF 属性下
				MENU_CONF: {
					// 配置上传图片
					uploadImage: {
						server: UPLOAD_API,
						// 【特别注意】服务端 response body 格式要求如下：
						// 上传成功的返回格式：
						// {
						//     "errno": 0, // 注意：值是数字，不能是字符串
						//     "data": {
						//         "url": "xxx", // 图片 src ，必须
						//         "alt": "yyy", // 图片描述文字，非必须
						//         "href": "zzz" // 图片的链接，非必须
						//     }
						// }
						// 上传失败的返回格式：
						// {
						//     "errno": 1, // 只要不等于 0 就行
						//     "message": "失败
						// }

						// form-data 参数名 ，默认值 'wangeditor-uploaded-image'
						fieldName: 'file',

						// 单个文件的最大体积限制，默认为 2M
						maxFileSize: 10 * 1024 * 1024, // 10M

						// 小于该值就插入 base64 格式（而不上传），默认为 0
						base64LimitSize: 5 * 1024, // 5kb

						// 最多可上传几个文件，默认为 100
						maxNumberOfFiles: 10,

						// 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
						allowedFileTypes: [],

						// 将 meta 拼接到 url 参数中，默认 false
						metaWithUrl: false,

						// 自定义增加 http  header
						headers: {
							// Accept: "text/x-json",
							// otherKey: "xxx",
							token: '',
							'Cache-Control': 'no-cache',
							'Access-Control-Allow-Origin': '*',
						},

						// 超时时间，默认为 10 秒
						timeout: 60 * 1000, // 60 秒

						// 其他变量和钩子请查看文档
					},

					// 配置上传视频
					uploadVideo: {
						server: UPLOAD_API,
						// form-data 参数名 ，默认值 'wangeditor-uploaded-image'
						fieldName: 'file',

						// 单个文件的最大体积限制，默认为 2M
						maxFileSize: 200 * 1024 * 1024, // 200M

						// 最多可上传几个文件，默认为 100
						maxNumberOfFiles: 5,

						// 选择文件时的类型限制，默认为 ['video/*'] 。如不想限制，则设置为 []
						allowedFileTypes: [],

						// 将 meta 拼接到 url 参数中，默认 false
						metaWithUrl: false,

						// 自定义增加 http  header
						headers: {
							// Accept: "text/x-json",
							// otherKey: "xxx",
							token: '',
						},

						// 超时时间，默认为 10 秒
						timeout: 60 * 1000, // 60 秒

						// 其他变量和钩子请查看文档
					},

					// "上传附件"菜单的配置
					uploadAttachment: {
						server: UPLOAD_API,

						// 超时时间，默认为 10 秒
						timeout: 60 * 1000, // 60 秒

						fieldName: 'file',
						// meta: { token: 'xxx', a: 100 }, // 请求时附加的数据
						// metaWithUrl: false, // meta 拼接到 url 上
						headers: {
							token: '',
						},

						maxFileSize: 100 * 1024 * 1024, // 100M

						// 其他变量和钩子请查看文档
					},
				},
			},
		};
	},
	methods: {
		onCreated(editor) {
			this.editor = Object.seal(editor); // 【注意】一定要用 Object.seal() 否则会报错
			// 从父组件拿到的内容
			this.$nextTick(() => {
				// this.html = this.value;
				this.disabled && this.editor?.disable(); //禁用编辑器，设置为只读
				!this.disabled && this.editor?.enable(); //取消禁用，取消只读
			});
		},

		// onChange 实时获取编辑器最新内容
		onChange: debounce(function (editor) {
			const content = editor.getHtml();
			this.$emit('input', content);
			this.$emit('change', content);
			console.log('当前内容为：', editor.getHtml());
		}),

		// 压缩图片
		async compressImage(file, options = {}) {
			return new Promise(resolve => {
				const {
					maxWidth = 1920, // 最大宽度
					maxHeight = 1080, // 最大高度
					quality = 0.8, // 压缩质量
				} = options;

				const reader = new FileReader();
				reader.readAsDataURL(file);
				reader.onload = e => {
					const img = new Image();
					img.src = e.target.result;
					img.onload = () => {
						const canvas = document.createElement('canvas');
						let width = img.width;
						let height = img.height;

						// 等比例缩放
						if (width > maxWidth) {
							height = (maxWidth / width) * height;
							width = maxWidth;
						}
						if (height > maxHeight) {
							width = (maxHeight / height) * width;
							height = maxHeight;
						}

						canvas.width = width;
						canvas.height = height;

						const ctx = canvas.getContext('2d');
						ctx.drawImage(img, 0, 0, width, height);

						// 转换为blob
						canvas.toBlob(
							blob => {
								resolve(new File([blob], file.name, { type: file.type }));
							},
							file.type,
							quality,
						);
					};
				};
			});
		},

		// 显示上传进度
		showUploadProgress(progress) {
			const h = this.$createElement;
			if (!this.progressMessage) {
				this.progressMessage = this.$message({
					type: 'primary',
					message: h('div', { style: 'width: 520px;' }, [
						h(
							'div',
							{
								class: 'upload-progress-message-title',
							},
							[
								h('span', { class: 'el-icon-loading' }, ['']),
								h('span', { class: 'upload-progress-message-title-text' }, ['正在上传图片']),
								h('span', { class: 'upload-progress-message-percentage' }, [`${Math.round(progress)}%`]),
							],
						),
						h('el-progress', {
							props: {
								percentage: Math.round(progress),
								status: progress === 100 ? 'success' : progress >= 50 ? 'warning' : '',
								strokeWidth: 12,
								textInside: true,
								format: () => '',
							},
							style: {
								width: '100%',
							},
						}),
					]),
					duration: 0,
					customClass: 'upload-progress-message',
				});
			} else {
				// 更新进度
				const progressBar = this.progressMessage.$el.querySelector('.el-progress');
				const percentageText = this.progressMessage.$el.querySelector('span:last-child');
				if (progressBar && percentageText) {
					const component = progressBar.__vue__;
					if (component) {
						component.percentage = Math.round(progress);
						percentageText.textContent = `${Math.round(progress)}%`;
						if (progress === 100) {
							component.status = 'success';
							// 延迟关闭消息
							setTimeout(() => {
								this.progressMessage.close();
								this.progressMessage = null;
							}, 1500);
						}
					}
				}
			}
		},

		// 自定义粘贴。可阻止编辑器的默认粘贴，实现自己的粘贴逻辑。(可以实现复制粘贴 word ，有图片)
		customPaste(editor, event, callback) {
			let html = event.clipboardData.getData('text/html');
			const rtf = event.clipboardData.getData('text/rtf');

			if (!html || !rtf) return true;

			html = html.replace(/text-indent:-(.*?)pt/gi, '');
			const imgSrcs = this.extractImagesFromHtml(html);

			if (!imgSrcs?.length) {
				editor.dangerouslyInsertHtml(html);
				event.preventDefault();
				return false;
			}

			const rtfImages = this.extractImagesFromRtf(rtf);
			if (!rtfImages?.length) {
				editor.dangerouslyInsertHtml(html);
				event.preventDefault();
				return false;
			}

			let uploadedCount = 0;
			Promise.all(
				rtfImages.map(async (image, index) => {
					try {
						const file = await this.createImageFile(image, index); // 创建图片文件
						const compressedFile = await this.compressImage(file); // 压缩图片
						const url = await this.uploadFile(compressedFile); // 上传图片

						uploadedCount++;
						this.showUploadProgress((uploadedCount / rtfImages.length) * 100);

						return url;
					} catch (error) {
						console.error('Upload image failed:', error);
						this.$message.error(`图片上传失败: ${error.message}`);
						return null;
					}
				}),
			).then(urls => {
				const validUrls = urls.filter(Boolean); // 过滤掉无效的图片源
				html = this.replaceImageSources(html, imgSrcs, validUrls); // 替换图片源
				editor.dangerouslyInsertHtml(html); // 插入图片
			});

			event.preventDefault();
			return false;
		},

		// 从HTML中提取图片源
		extractImagesFromHtml(html) {
			const matches = html.match(/<img[^>]+src="([^"]+)"[^>]*>/g);
			if (!matches) return [];

			return matches.map(img => img.match(/src="([^"]+)"/)?.[1]).filter(Boolean);
		},

		// 从RTF中提取图片数据
		extractImagesFromRtf(rtf) {
			if (!rtf) return [];

			const pictureHeader = /{\\pict[\s\S]+?({\\\*\\blipuid\s?[\da-fA-F]+)[\s}]*/;
			const pictureRegex = new RegExp(`(?:(${pictureHeader.source}))([\\da-fA-F\\s]+)\\}`, 'g');
			const images = rtf.match(pictureRegex);

			if (!images) return [];

			return images
				.map(image => {
					const type = image.includes('\\pngblip') ? 'image/png' : image.includes('\\jpegblip') ? 'image/jpeg' : null;

					if (!type) return null;

					return {
						hex: image.replace(pictureHeader, '').replace(/[^\da-fA-F]/g, ''),
						type,
					};
				})
				.filter(Boolean);
		},

		// 创建图片文件
		async createImageFile(imageData, index) {
			const base64Data = `data:${imageData.type};base64,${this.convertHexToBase64(imageData.hex)}`;
			const blob = this.base64ToBlob(base64Data, imageData.type);
			return new File([blob], `paste_image_${index}.${imageData.type.split('/')[1]}`, { type: imageData.type });
		},

		// 上传文件
		async uploadFile(file) {
			const formData = new FormData();
			formData.append('file', file);

			const response = await fetch(UPLOAD_API, {
				method: 'POST',
				headers: {
					token: this.userInfos?.token,
				},
				body: formData,
			});

			const result = await response.json();
			return result.errno === 0 ? result.data.url : null;
		},

		// 替换图片源
		replaceImageSources(html, oldSources, newSources) {
			if (oldSources.length !== newSources.length) return html;

			return oldSources.reduce((acc, oldSrc, i) => acc.replace(oldSrc, newSources[i]), html);
		},

		// 十六进制转base64
		convertHexToBase64(hex) {
			const pairs = hex.match(/\w{2}/g) || [];
			const chars = pairs.map(pair => String.fromCharCode(parseInt(pair, 16)));
			return btoa(chars.join(''));
		},

		// 新增：将base64转换为Blob对象
		base64ToBlob(base64Data, type) {
			const parts = base64Data.split(';base64,');
			const contentType = type || parts[0].split(':')[1];
			const raw = window.atob(parts[1]);
			const rawLength = raw.length;
			const uInt8Array = new Uint8Array(rawLength);

			for (let i = 0; i < rawLength; ++i) {
				uInt8Array[i] = raw.charCodeAt(i);
			}

			return new Blob([uInt8Array], { type: contentType });
		},
	},
	watch: {
		value: {
			handler(val) {
				this.html = val;
			},
			immediate: true,
		},

		disabled(newVal) {
			newVal && this.editor?.disable(); //禁用编辑器，设置为只读
			!newVal && this.editor?.enable(); //取消禁用，取消只读
		},
	},
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},

	mounted() {
		const USER_TOKEN = this.userInfos?.token; // 获取用户token

		// 设置上传接口的token
		this.editorConfig['MENU_CONF'].uploadImage.headers.token = USER_TOKEN;
		this.editorConfig['MENU_CONF'].uploadVideo.headers.token = USER_TOKEN;
		this.editorConfig['MENU_CONF'].uploadAttachment.headers.token = USER_TOKEN;
	},
	beforeDestroy() {
		const editor = this.editor;
		if (editor == null) return;
		editor.destroy(); // 组件销毁时，及时销毁 editor ，重要！！！
	},
};
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
<style lang="scss">
.w-e-full-screen-container {
	z-index: 2;
}

.upload-progress-message {
	min-width: 340px !important;
	padding: 14px 20px !important;
	.upload-progress-message-title {
		color: #1e9d6f;
		margin-bottom: 8px;
		font-size: 14px;
		display: flex;
		align-items: center;
		gap: 10px;
	}
	// .upload-progress-message-percentage {
	// 	color: #999;
	// }
	.el-message__content {
		text-align: left;
	}

	.el-progress-bar__inner {
		transition: width 0.2s ease;
	}

	.el-progress--success {
		.el-progress-bar__inner {
			background-color: #67c23a;
		}
	}
}
</style>
