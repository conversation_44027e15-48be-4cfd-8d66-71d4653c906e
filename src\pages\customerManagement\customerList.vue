<template>
	<div id="customerList" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<!-- 团队详情 -->
		<TeamDetail ref="TeamDetail" @refresh="queryTableData(1)" />
		<!-- 三色灯详情 -->
		<ThreeColorLight ref="ThreeColorLight" @refresh="queryTableData(1)" />
		<!-- 失客分析 -->
		<CustomerAnalysis ref="CustomerAnalysis" @refresh="queryTableData(1)" />
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="team"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twidList = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="我的客户" name="customerList">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">到期日</span>
						<DateSelect
							:dateList="['本月', '下月', '上月', '不限定']"
							@change="
								searchForm.startDate = $event.startTime;
								searchForm.endDate = $event.endTime;
								queryTableData(1);
							"
						/>
						<!-- 模糊查询 -->
						<SearchHistoryInput
							name="companyName"
							placeholder="客户短名称"
							v-model.trim="searchForm.query"
							@input="queryTableData(1)"
						/>

						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar">
							<ExportBtn @trigger="openExport" />
						</div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['validFrom', 'validTo'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 编号 -->
									<Tooltips
										v-else-if="item.colNo == 'teamCode'"
										class="hover-green green"
										@click.native="openTeam(scope.row)"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>
									<!-- 使用版本 -->
									<Tooltips
										v-else-if="item.colNo == 'version'"
										:cont-str="versionMap[scope.row.version]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:cont-str="statusMap[scope.row.status]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 剩余天数 -->
									<Tooltips
										v-else-if="item.colNo == 'surplusDay'"
										:class="[scope.row.surplusDay < 10 ? 'red' : '']"
										:cont-str="scope.row.surplusDay"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<u-table-column label="" width="180" align="center">
								<template slot-scope="scope">
									<el-button type="text" size="mini" @click="openLight(scope.row)">三色灯管理</el-button>
									<el-button
										type="text"
										size="mini"
										:class="{ 'color-999': scope.row.isLostCustomerAnalysis }"
										@click="openAnalysis(scope.row)"
										>失客分析</el-button
									>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import ExportTable from '@/components/ExportTable'; //导出组件
import ChannelSelect from '@/components/ChannelSelect.vue';
import CustomerAnalysis from './components/CustomerAnalysis.vue'; //失客分析
import ThreeColorLight from './components/ThreeColorLight.vue'; //三色灯
import TeamDetail from '@/pages/teamManagement/teamDataMain/TeamDetail'; //团队
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		ChannelSelect,
		DateSelect,
		CustomerAnalysis,
		ThreeColorLight,
		TeamDetail,
		ExportBtn,
	},
	name: 'customerList', //组件名应同路由名(否则keep-alive不生效)
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			activeTab: 'customerList', //激活tab页
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '团队编号', colNo: 'teamCode', align: 'left', width: '' },
				{ colName: '短名称', colNo: 'teamName', align: 'left', width: '' },
				{ colName: '区域', colNo: 'region', align: 'left', width: '' },
				{ colName: '渠道', colNo: 'channelName', align: 'left', width: '' },
				{ colName: '分销/代理', colNo: 'teamworkName', align: 'left', width: '' },
				{ colName: '业务顾问', colNo: 'salesName', align: 'left', width: '' },
				{ colName: '实施顾问', colNo: 'consultantName', align: 'left', width: '' },
				{ colName: '使用版本', colNo: 'version', align: 'left', width: '' },
				{ colName: '到期日期', colNo: 'validTo', align: 'center', width: '90' },
				{ colName: '站点', colNo: 'simLimit', align: 'right', width: '90' },
				{ colName: '剩余天数', colNo: 'surplusDay', align: 'right', width: '90' },
				{ colName: '年费', colNo: 'annualFee', align: 'right', width: '80' },
				{ colName: '最近智造评分', colNo: 'manufacturingRating', align: 'right', width: '' },
			],
			searchForm: {
				query: '', //模糊查询
				teamCode: '',
				teamName: '',
				machineLabel: '',
				productLabel: '',
				region: '',
				version: [1, 2],
				consultantName: '',
				salesName: '',
				channelName: '',
				twidList: [],
				status: [],
			},
			// 日期相关
			dateSelectObj: {
				startDate: null,
				endDate: null,
			},
			versionMap: {
				1: '标准版',
				2: 'OEE',
				4: '微信版',
			},

			openMove: false, //打开组件
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 打开团队
		openTeam(row) {
			this.$refs.TeamDetail.showDetailCom(row);
		},
		// 打开分析
		openAnalysis(row) {
			this.$refs.CustomerAnalysis.showDetailCom(row);
		},
		// 打开三色灯
		openLight(row) {
			this.$refs.ThreeColorLight.showDetailCom(row);
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据

		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectMyClient'; //接口
			this.$axios[API](JSON.stringify(this.searchForm))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,

		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? _.sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
				this.$emit('getTableOptions', sortedData); // 传递表格数据用于组件里上下页切换
			});
		},

		//数据导出
		openExport: _.debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					...this.dateSelectObj,
					...this.searchForm,
				}), //接口参数
				API: 'exportSelectMyClient', //导出接口
				downloadData: '我的客户导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
#customerList {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
