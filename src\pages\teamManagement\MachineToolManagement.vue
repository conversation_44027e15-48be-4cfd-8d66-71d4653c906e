<template>
	<div id="teamDataMain">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<el-tabs v-model="activeTab">
			<el-tab-pane label="售服团队管理" name="teamDataMain">
				<BaseLayout>
					<template #header>
						<span class="search-label">开始日期</span>
						<DateSelect
							:dateKeys="['startDate', 'endDate']"
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>
						<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新 </el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<el-button type="text" class="icon-third-bt_newdoc" @click="addTeam">添加</el-button>
							<ExportBtn @trigger="openExport" />
							<el-button type="text" class="icon-third_searchC" v-popover:thSearch>查找</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column label="售服方编号" prop="factoryCode" sortable></u-table-column>
							<u-table-column label="简称" prop="factoryName" sortable>
								<template slot-scope="scope">
									<Tooltips :cont-str="scope.row.factoryName" :cont-width="(scope.column.width || scope.column.realWidth) - 20" />
								</template>
							</u-table-column>
							<u-table-column label="全称" prop="factoryFullname" sortable>
								<template slot-scope="scope">
									<Tooltips
										:cont-str="scope.row.factoryFullname"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<u-table-column label="开始时间" prop="validFrom" sortable>
								<template slot-scope="scope">
									<div>
										{{ dateFormat(scope.row.validFrom, 'line') }}
									</div>
								</template>
							</u-table-column>
							<u-table-column label="团队管理员" prop="userName" sortable></u-table-column>
							<u-table-column label="业务顾问" prop="salesName" sortable></u-table-column>
							<u-table-column label="代理人" prop="agentName" sortable></u-table-column>
							<u-table-column label="实施顾问" prop="consultantName" sortable></u-table-column>
							<u-table-column label="设备标签" prop="machineLabel" sortable></u-table-column>
							<u-table-column label="产品标签" prop="productLabel" sortable></u-table-column>
							<u-table-column label="区域" prop="region" sortable></u-table-column>
							<u-table-column label="登记时间" prop="createTime" width="100" sortable>
								<template slot-scope="scope">
									<div>
										<p class="uTd_p">
											{{ dateFormat(scope.row.createTime, 'line') }}
										</p>
										<p class="uTd_p">
											{{ dateFormat(scope.row.createTime, 'HMS') }}
										</p>
									</div>
								</template>
							</u-table-column>
							<u-table-column label="登记人" prop="createName" sortable></u-table-column>
							<u-table-column label="状态" prop="status" sortable>
								<template slot-scope="scope">
									<div>
										<span>{{ workOrderStatusMap[scope.row.status] }}</span>
									</div>
								</template>
							</u-table-column>
							<u-table-column label="" width="100">
								<template slot-scope="scope">
									<div class="flex-align-center">
										<el-button type="text" class="el-icon-edit-outline" @click="editTeam(scope.row)">修改 </el-button>
										<el-button type="text" class="el-icon-close" @click="delTeam(scope.row, scope.$index)"> </el-button>
									</div>
								</template>
							</u-table-column>
						</u-table>

						<el-popover v-model="thPopVisibal" ref="thSearch" placement="bottom-end" width="400" :visible-arrow="false">
							<div style="padding: 0.5vh 0.5vw; font-size: 12px" class="searchPop">
								<div style="position: relative; border-bottom: 1px solid #d7d7d7; padding: 1vh 0.5vw 1.5vh 0.2vw">
									<span>查找条件</span>
									<i
										class="el-icon-close"
										style="position: absolute; top: 0.2vh; right: 0.2vw; font-size: 16px; font-weight: 700; cursor: pointer"
										@click.stop="closepop('thSearch')"
									></i>
								</div>
								<el-form :model="searchForm" label-width="4vw" label-position="left" style="width: 90%; margin-top: 1vh">
									<el-form-item label="编号" prop="teamCode">
										<!-- <input type="text"> -->
										<el-input placeholder="请输入编号" v-model="searchForm.teamCode" size="mini"></el-input>
									</el-form-item>
									<el-form-item label="机床厂名称" prop="factoryName">
										<el-input placeholder="请输入机床厂名称" v-model="searchForm.factoryName" size="mini"></el-input>
									</el-form-item>
									<el-form-item label="区域" prop="region">
										<el-input placeholder="请输入区域" v-model="searchForm.region" size="mini"></el-input>
									</el-form-item>
									<el-form-item label="设备标签" prop="machineLabel">
										<el-input placeholder="请输入设备标签" v-model="searchForm.machineLabel" size="mini"></el-input>
									</el-form-item>
									<el-form-item label="产品标签" prop="productLabel">
										<el-input placeholder="请输入产品标签" v-model="searchForm.productLabel" size="mini"></el-input>
									</el-form-item>
									<el-form-item label="实施顾问" prop="consultantName">
										<el-input placeholder="请输入实施顾问" v-model="searchForm.consultantName" size="mini"></el-input>
									</el-form-item>
									<el-form-item label="业务顾问" prop="salesName">
										<el-input placeholder="业务顾问" v-model="searchForm.salesName" size="mini"></el-input>
									</el-form-item>
								</el-form>
								<div style="margin-top: 3vh; border-top: 1px solid #d7d7d7; text-align: right">
									<el-button type="text" @click.stop="searchQuery" style="font-size: 12px; color: #7f7f7f">确定</el-button>
								</div>
							</div>
						</el-popover>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
		<div id="workProgressToggle">
			<div
				style="
					border-radius: 15px 15px 0 0;
					width: 100%;
					border: 1px solid #e9e9e9;
					background: #f9f9f9;
					border-bottom: 1px solid transparent;
					box-sizing: border-box;
				"
			>
				<el-row style="padding: 10px; box-sizing: border-box">
					<el-col :span="8" style="font-size: 18px; line-height: 40px; font-weight: 500">
						<span style="margin-left: 0.5vw">{{ isEditTeam ? '编辑团队' : '新建团队' }}</span>
					</el-col>
					<el-col :span="8" :offset="8" style="text-align: right">
						<el-button type="text" @click="clearWorkObj"><i class="el-icon-arrow-left"></i>返回</el-button>
					</el-col>
				</el-row>
			</div>
			<div style="background: #fff; border: 1px solid #e9e9e9; box-sizing: border-box; min-height: 82.2vh">
				<div style="padding-left: 2vw; margin-top: 5vh">
					<p class="p_row">
						<span class="p_label"></span>
						<span class="p_text">基本信息</span>
					</p>
					<table class="workCtable" cellpadding="5" cellspacing="0" style="margin-left: 0.2vw">
						<tr>
							<th style="width: 10%">售服方编号</th>
							<th style="width: 9%" class="label-required">简称</th>
							<th style="width: 12%" class="label-required">全称</th>
							<th style="width: 10%" class="label-required">售服团队管理员</th>
							<th style="width: 10%" class="label-required">手机号码</th>
							<th style="width: 10%">设备标签</th>
							<th style="width: 10%">产品标签</th>
							<th style="width: 10%">区域</th>
							<th style="width: 10%" class="label-required">开始日期</th>
						</tr>
						<tr>
							<td>
								<el-input disabled placeholder="创建后自动生成" v-model="workOrderObj.teamNo"></el-input>
							</td>
							<td>
								<el-input placeholder="机床厂名称" v-model="workOrderObj.factoryName"></el-input>
							</td>
							<td>
								<el-input placeholder="机床厂全称" v-model="workOrderObj.factoryFullname"></el-input>
								<!-- <el-select  placeholder="加急" v-model="workOrderObj.expedited">
                                            <el-option v-for="item in expedityList" :key="'expedi'+item.id" :label="item.exName" :value="item.id"></el-option>
                                        </el-select> -->
							</td>
							<td>
								<el-input placeholder="售服团队管理员" v-model="workOrderObj.userName"></el-input>
							</td>
							<td>
								<el-input placeholder="手机号码" v-model="workOrderObj.phoneNo"></el-input>
							</td>
							<td>
								<el-input placeholder="设备标签" v-model="workOrderObj.macLabel"></el-input>
							</td>
							<td>
								<el-input placeholder="产品标签" v-model="workOrderObj.prodcutLabel"></el-input>
							</td>
							<td>
								<el-input placeholder="区域" v-model="workOrderObj.region"></el-input>
							</td>
							<td>
								<el-date-picker v-model="workOrderObj.registerDate" type="date" style="width: 100%" placeholder="开始日期">
								</el-date-picker>
							</td>
						</tr>
						<tr>
							<th style="width: 15%">有效期</th>
							<th style="width: 10%">业务顾问</th>
							<th style="width: 15%">代理人</th>
							<th style="width: 10%">实施顾问</th>
							<th style="width: 10%">机床厂头像</th>
							<th style="width: 10%" class="label-required">状态</th>

							<th style="width: 10%">资源模板</th>
							<th></th>
							<th></th>
						</tr>
						<tr>
							<td>
								<el-date-picker v-model="workOrderObj.validPeriod" type="date" style="width: 100%" placeholder="有效期">
								</el-date-picker>
							</td>
							<td>
								<el-autocomplete
									placeholder="选择业务顾问"
									v-model="workOrderObj.saleMan"
									:debounce="500"
									:fetch-suggestions="querySaleMans"
									@select="selectSaleMan"
								>
									<template slot-scope="{ item }">
										<div>
											<span>{{ item.salesName }}</span>
										</div>
									</template>
								</el-autocomplete>
							</td>
							<td>
								<el-autocomplete
									placeholder="选择代理人"
									v-model="workOrderObj.agent"
									:debounce="500"
									:fetch-suggestions="queryAgentMans"
									@select="selectAgentMan"
								>
									<template slot-scope="{ item }">
										<div>
											<span>{{ item.agentName }}</span>
										</div>
									</template>
								</el-autocomplete>
							</td>
							<td>
								<el-autocomplete
									placeholder="选择实施顾问"
									v-model="workOrderObj.ConsultantName"
									:debounce="500"
									:fetch-suggestions="queryConsultantMans"
									@select="selectConsultantMan"
								>
									<template slot-scope="{ item }">
										<div>
											<span>{{ item.consultantName }}</span>
										</div>
									</template>
								</el-autocomplete>
							</td>
							<td>
								<el-upload
									action=""
									accept="image/jpeg,image/jpg,image/png"
									:http-request="uploadFile"
									:on-remove="handleRemove"
									:on-error="uploadFail"
									:file-list="excelFileList"
									:show-file-list="false"
								>
									<el-button
										type="text"
										style="max-width: 135px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis"
										>{{ workOrderObj.factoryPic ? workOrderObj.factoryPic : '上传' }}</el-button
									>
								</el-upload>
							</td>
							<td>
								<el-select placeholder="状态" v-model="workOrderObj.status" ref="workOrderCus">
									<el-option
										v-for="item in teamStatusList"
										:key="'workCus' + item.id"
										:label="item.statusName"
										:value="item.id"
									></el-option>
								</el-select>
							</td>
							<td>
								<el-select :disabled="isEditTeam" placeholder="资源模板" v-model="resourceTemplate" ref="resourceTemplate">
									<el-option
										v-for="item in resourceTemplateList"
										:key="'workCus' + item.templateNo"
										:label="item.templateName"
										:value="item.templateNo"
									></el-option>
								</el-select>
							</td>
							<td></td>
						</tr>
					</table>
					<el-row style="width: 98%; margin: 1vh 0">
						<el-col :span="24" style="text-align: right">
							<!-- <el-button  @click="delEditWork" style="width:100px;border-color:#DCDFE6 !important;background:#fff;color:#555">删除</el-button> -->
							<el-button
								@click="saveEditTeam"
								style="
									width: 100px;
									background-color: #28d094 !important;
									color: #fff !important;
									border-color: #dcdfe6 !important;
								"
								>保存</el-button
							>
						</el-col>
					</el-row>
					<div v-if="isEditTeam">
						<p class="p_row">
							<span class="p_label"></span>
							<span class="p_text">分配资源</span>
						</p>
						<div class="ResourcesTree">
							<div class="WebTree">
								<el-tree
									:data="WebTreeList"
									ref="webTree"
									node-key="rcid"
									:default-expanded-keys="WebDfExpand"
									show-checkbox
									@check="treeChange"
								></el-tree>
							</div>
							<div class="AppTree">
								<el-tree
									:data="AppTreeList"
									ref="appTree"
									node-key="rcid"
									:default-expanded-keys="AppDfExpand"
									show-checkbox
									@check="treeChange"
								></el-tree>
							</div>
						</div>
					</div>
					<div v-if="isEditTeam" style="padding-bottom: 2vh">
						<p class="p_row" style="margin-bottom: 2vh">
							<span class="p_label"></span>
							<span class="p_text">操作日志</span>
						</p>
						<div class="p_nor" v-for="(item, index) in workOrderObj.workorderOperationLogList" :key="'oper' + index">
							<span>{{ dateFormat(item.updateTime, 'MDS') }}</span>
							<pre>{{ item.operationInfo }}</pre>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import Sortable from 'sortablejs';
import ExportTable from '@/components/ExportTable';
import DateSelect from '@/components/DateSelect/DateSelect';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		ExportTable,
		ExportBtn,
	},
	name: 'teamDataMain',
	data() {
		return {
			resourceTemplateList: [],
			resourceTemplate: '',
			activeTab: 'teamDataMain',
			//日期相关
			dateSelectObj: {
				startDate: null,
				endDate: null,
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableHeight: 630,
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			searchForm: {
				query: '',

				teamCode: '',
				factoryName: '',
				machineLabel: '',
				productLabel: '',
				region: '',
				staffStatus: ['1', '2'],
				consultantName: '',
				salesName: '',
			},
			multipleWorkOrder: [],
			columnSort: [
				{
					columnNo: 'factoryCode',
					columnName: '售服方编号',
					sortType: '3',
				},
				{
					columnNo: 'factoryName',
					columnName: '简称',
					sortType: '3',
				},
				{
					columnNo: 'factoryFullname',
					columnName: '全称',
					sortType: '3',
				},
				{
					columnNo: 'machineLabel',
					columnName: '设备标签',
					sortType: '3',
				},
				{
					columnNo: 'productLabel',
					columnName: '产品标签',
					sortType: '3',
				},
			],
			columnSortList: ['teamCode', 'teamName', 'region', 'machineLabel', 'productLabel'],
			workOrderStatusMap: {
				1: '正式运行',
				0: '禁用',
				2: '试用',
			},
			thPopVisibal: false,
			thSortVisibal: false,
			dialogEditWorkOrder: false,
			//业务顾问修改
			eidtWorkForm: {
				staffName: '',
				phoneNo: '',
				region: '',
				status: '1',
				enterDate: '',
				leaveDate: '',
			},
			btnFlag: false,
			// isEdit:false,
			isEditTeam: false,
			//团队修改
			workOrderObj: {
				factoryFullname: '',
				userName: '',
				teamNo: '',
				teamPreName: '',
				factoryName: '',
				teamAdmin: '',
				phoneNo: '',
				macLabel: '',
				factoryPic: '',
				prodcutLabel: '',
				region: '',

				registerDate: '',
				validPeriod: '',
				saleMan: '',
				suid: '',
				agent: '',
				auid: '',
				ConsultantName: '',
				cuid: '',
				teamImg: '',
				status: '',
				workorderOperationLogList: [],
				templateNo: '',
			},
			//上传图片
			excelFileList: [],
			versionList: [
				{
					id: '2',
					versionName: 'OEE',
				},
				{
					id: '1',
					versionName: '标准版',
				},
				{
					id: '4',
					versionName: '微信版',
				},
			],
			teamStatusList: [
				{ id: 2, statusName: '试用' },
				{ id: 1, statusName: '正式运行' },
			],
			versionMap: {
				1: '标准版',
				2: 'OEE',
				4: '微信版',
			},
			salesManList: [],
			agentList: [],
			impConsultantList: [],
			//资源列表
			fidCurr: '',
			WebTreeList: [],
			AppTreeList: [],
			WebDfExpand: [],
			webDfChecks: [],
			AppDfExpand: [],
			appDfChecks: [],
			moveTrue: false, //控制导出弹窗
			exportName: '团队', //控制导出什么数据
			printTemps: [],
		};
	},
	// 监听属性 类似于data概念
	computed: {},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {
		// this.getVersion()
	},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.getPrintConfig();
		this.queryTableData();
		// this.queryResourceTemplate()
	},
	activated() {
		this.getPrintConfig();
		this.queryTableData();
		// this.queryResourceTemplate()
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},

		getPrintConfig() {
			this.printTemps = [];
			this.$axios
				.selectResourceTemplateConfig(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.resourceTemplateList = res.data.data;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAllPrintTemplateConfig |' + error);
				});
		},

		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const str = JSON.stringify({
				...this.dateSelectObj,
				...this.searchForm,
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
			});
			this.$axios
				.selectFactory(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.tableData = [];
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.tableData = [];
					console.log('selectFactory |' + error);
				});
		}),
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		workOrderSelectionChange(val) {
			this.multipleWorkOrder = val;
		},
		toggleAside(value) {
			//侧边栏滑出
			const workDiv = document.getElementById('workProgressToggle');
			const outDiv = document.getElementById('teamDataMain');
			if (workDiv) {
				let newValue = 0;
				if (value) {
					newValue = 0;
					outDiv.style.height = 'auto';
					outDiv.style.overflow = 'initial';
				} else {
					newValue = '110%';
					outDiv.style.height = '100%';
					outDiv.style.overflow = 'hidden';
				}
				workDiv.style.left = newValue;
			}
		},
		openpop(val) {
			// this.$refs[val].doShow()
			if (val == 'thSort') {
				// this.thSortVisibal = true;
				setTimeout(() => {
					this.sortColumnDrop();
				}, 500);
			}
		},
		closepop(val) {
			// this.$refs[val].doClose()
			document.body.click();
		},
		searchSort() {
			this.closepop('thSort');
			this.queryTableData();
		},
		searchQuery() {
			this.closepop('thSearch');
			this.queryTableData();
		},
		cancelEditWork() {
			this.dialogEditWorkOrder = false;
			this.eidtWorkForm.staffName = '';
			this.eidtWorkForm.phoneNo = '';
			this.eidtWorkForm.enterDate = '';
			this.eidtWorkForm.leaveDate = '';
			this.eidtWorkForm.region = '';
			this.eidtWorkForm.status = '1';
		},
		saveEditWork() {
			this.cancelEditWork();
		},
		//日期format
		dateFormat: _.dateFormat,
		//拖拽排序列
		sortColumnDrop() {
			const tbody = document.querySelector('.workSortTable tbody');
			if (!tbody) return;
			const _this = this;
			const sortColTable = Sortable.create(tbody, {
				animation: 300,
				delay: 30,
				handle: '.icon-third_drag',
				draggable: '.moveItem',
				dataIdAttr: 'data-no',
				onEnd({ newIndex, oldIndex }) {
					const arr = sortColTable.toArray();
					_this.columnSortList = arr;
				},
			});
		},
		addTeam() {
			this.isEditTeam = false;
			this.toggleAside(1);
		},
		editTeam(row) {
			if (!row) {
				return;
			}
			const str = JSON.stringify({ fid: row.fid });
			this.$axios
				.selectFactoryDetail(str)
				.then(res => {
					if (res.data.success) {
						this.workOrderObj.teamNo = res.data.data.factoryCode;
						this.fidCurr = res.data.data.fid;
						this.resourceTemplate = res.data.data.resourceTemplate;
						this.workOrderObj.userName = res.data.data.userName;
						this.workOrderObj.factoryFullname = res.data.data.factoryFullname;

						this.workOrderObj.teamPreName = res.data.data.teamName;
						this.workOrderObj.factoryName = res.data.data.factoryName;
						this.workOrderObj.teamAdmin = res.data.data.userName;
						this.workOrderObj.phoneNo = res.data.data.phoneNo;
						this.workOrderObj.macLabel = res.data.data.machineLabel;
						this.workOrderObj.prodcutLabel = res.data.data.productLabel;
						this.workOrderObj.region = res.data.data.region;
						this.workOrderObj.registerDate = res.data.data.validFrom;
						this.workOrderObj.validPeriod = res.data.data.validTo;
						this.workOrderObj.saleMan = res.data.data.salesName;
						this.workOrderObj.suid = res.data.data.suid;
						this.workOrderObj.agent = res.data.data.agentName;
						this.workOrderObj.auid = res.data.data.auid;
						this.workOrderObj.ConsultantName = res.data.data.consultantName;
						this.workOrderObj.cuid = res.data.data.cuid;
						this.workOrderObj.teamImg = res.data.data.teamImage;
						this.workOrderObj.status = res.data.data.status;
						this.workOrderObj.templateNo = res.data.data.templateNo;
						this.getTeamTree(this.fidCurr);
						this.isEditTeam = true;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectFactoryDetail |' + error);
				});
		},
		delTeam(row, index) {
			const fid = row.fid;
			this.$confirm('确定删除机床厂？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					const str = JSON.stringify({
						fid,
					});
					this.$axios
						.deleteFactory(str)
						.then(res => {
							if (res.data.success) {
								this.$succ('操作成功!');
								this.tableData.splice(index, 1);
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteTeam |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		saveEditTeam() {
			// console.log('nowSave',this.workOrderObj)
			let str;
			const factoryName = this.workOrderObj.factoryName,
				factoryFullname = this.workOrderObj.factoryFullname,
				phoneNo = this.workOrderObj.phoneNo,
				userName = this.workOrderObj.userName,
				status = this.workOrderObj.status,
				templateNo = this.workOrderObj.templateNo;
			if (!factoryName) {
				this.$message.warning('请输入机床厂名称');
				return;
			}
			if (!factoryFullname) {
				this.$message.warning('请输入机床厂全称');
				return;
			}
			if (!userName) {
				this.$message.warning('请输入团队管理员');
				return;
			}
			if (!phoneNo) {
				this.$message.warning('请输入手机号码');
				return;
			}

			if (!status) {
				this.$message.warning('请选择状态');
				return;
			}
			if (this.isEditTeam) {
				//修改
				str = JSON.stringify({
					auid: this.workOrderObj.auid ? this.workOrderObj.auid : null,
					cuid: this.workOrderObj.cuid ? this.workOrderObj.cuid : null,
					machineLabel: this.workOrderObj.macLabel,
					phoneNo: phoneNo,
					productLabel: this.workOrderObj.prodcutLabel,
					region: this.workOrderObj.region,
					status: status,
					suid: this.workOrderObj.suid ? this.workOrderObj.suid : null,
					factoryFullname: factoryFullname,
					teamImage: this.workOrderObj.teamImg,
					factoryName: factoryName,
					resourceTemplate: this.resourceTemplate,
					// "teamPic": "",

					fid: this.fidCurr,
					userName: userName,
					validFrom: this.workOrderObj.registerDate ? new Date(this.workOrderObj.registerDate).getTime() : '',
					validTo: this.workOrderObj.validPeriod ? new Date(this.workOrderObj.validPeriod).getTime() : '',
					templateNo: templateNo,
				});
				this.$axios
					.updateFactory(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.clearWorkObj();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('updateFactory |' + error);
					});
			} else {
				//新建
				str = JSON.stringify({
					auid: this.workOrderObj.auid ? this.workOrderObj.auid : null,
					cuid: this.workOrderObj.cuid ? this.workOrderObj.cuid : null,
					machineLabel: this.workOrderObj.macLabel,
					phoneNo: phoneNo,
					productLabel: this.workOrderObj.prodcutLabel,
					region: this.workOrderObj.region,
					status: status,
					suid: this.workOrderObj.suid ? this.workOrderObj.suid : null,
					factoryFullname: factoryFullname,
					teamImage: this.workOrderObj.teamImg,
					factoryName: factoryName,
					resourceTemplate: this.resourceTemplate,
					factoryPic: '',
					userName: userName,
					validFrom: this.workOrderObj.registerDate ? new Date(this.workOrderObj.registerDate).getTime() : '',
					validTo: this.workOrderObj.validPeriod ? new Date(this.workOrderObj.validPeriod).getTime() : '',

					templateNo: templateNo,
				});
				this.$axios
					.createFactory(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.fidCurr = res.data.data.fid;
							this.workOrderObj.teamNo = res.data.data.teamCode;
							this.isEditTeam = true;
							this.getTeamTree(this.fidCurr, true);
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('createTeam |' + error);
					});
			}
		},
		uploadFile(item) {
			const self = this;
			const isIMAGE = item.file.type === 'image/jpeg' || 'image/jpg' || 'image/png';
			const isLt2M = item.file.size / 1024 / 1024 < 2;

			if (!isIMAGE) {
				self.$message.warning('上传文件只能是图片格式!');
				return;
			}
			if (!isLt2M) {
				self.$message.warning('上传头像图片大小不能超过 2MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', item.file);
			//formData.append("token", user_token);
			formData.append('type', 2);
			// type 1-用户头像,2-团队头像,3-团队图片,4-设备图片,5-反馈图片,6-原料图片,7-模具图片,8-产品图片,9-产品工艺图纸图片
			console.log('aaa', formData);
			self.$axios
				.uploadWebPic(formData)
				.then(res => {
					if (res.data.success) {
						self.workOrderObj.factoryPic = res.data.data.path;
					} else {
						self.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					self.$message.warning(error.message);
				});
		},
		handleRemove() {
			//文件移除
			this.excelFileList = [];
		},
		uploadFail(err, file, fileList) {
			this.excelFileList = [];
		},
		clearWorkObj() {
			this.toggleAside(0);
			this.workOrderObj.teamNo = '';
			this.workOrderObj.teamPreName = '';
			this.workOrderObj.factoryName = '';
			this.workOrderObj.teamAdmin = '';
			this.workOrderObj.phoneNo = '';
			this.workOrderObj.macLabel = '';
			this.workOrderObj.prodcutLabel = '';
			this.workOrderObj.region = '';
			this.workOrderObj.factoryFullname = '';
			this.workOrderObj.registerDate = '';
			this.workOrderObj.validPeriod = '';
			this.workOrderObj.saleMan = '';
			this.workOrderObj.agent = '';
			this.workOrderObj.ConsultantName = '';
			this.workOrderObj.teamImg = '';
			this.workOrderObj.status = '';
			this.workOrderObj.templateNo = '';
			this.workOrderObj.userName = '';
			this.resourceTemplate = '';
			this.WebTreeList = [];
			this.AppTreeList = [];
			this.WebDfExpand = [];
			this.AppDfExpand = [];
			this.webDfChecks = [];
			this.appDfChecks = [];
			this.queryTableData();
		},
		/* 业务顾问、代理人、实施顾问相关 */
		querySaleMans(queryStr, cb) {
			let str = JSON.stringify({
					name: queryStr ? queryStr : '',
				}),
				result = [];
			this.$axios
				.selectLikeSalesName(str)
				.then(res => {
					if (res.data.success) {
						result = res.data.data;
						cb(result);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('queryCraftHours |' + error);
				});
		},
		queryAgentMans(queryStr, cb) {
			let str = JSON.stringify({
					name: queryStr ? queryStr : '',
				}),
				result = [];
			this.$axios
				.selectLikeAgentName(str)
				.then(res => {
					if (res.data.success) {
						result = res.data.data;
						cb(result);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectLikeAgentName |' + error);
				});
		},
		queryConsultantMans(queryStr, cb) {
			let str = JSON.stringify({
					name: queryStr ? queryStr : '',
				}),
				result = [];
			this.$axios
				.selectLikeConsultantName(str)
				.then(res => {
					if (res.data.success) {
						result = res.data.data;
						cb(result);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectLikeConsultantName |' + error);
				});
		},
		selectSaleMan(item) {
			this.workOrderObj.saleMan = item.salesName;
			this.workOrderObj.suid = item.suid;
		},
		selectAgentMan(item) {
			this.workOrderObj.agent = item.agentName;
			this.workOrderObj.auid = item.auid;
		},
		selectConsultantMan(item) {
			this.workOrderObj.ConsultantName = item.consultantName;
			this.workOrderObj.cuid = item.cuid;
		},
		getTeamTree(fid, type) {
			//加载资源列表
			let webTree, appTree;
			this.WebTreeList = [];
			this.AppTreeList = [];
			this.WebDfExpand = [];
			this.AppDfExpand = [];
			this.webDfChecks = [];
			this.appDfChecks = [];
			function getTreeAndCheck(treeList, deList, ckList, tree, flag) {
				if (flag) {
					tree.push({
						rcid: treeList.resourceName == 'web' ? -1 : -2,
						label: treeList.resourceName,
						children: [],
					});
					deList.push(treeList.resourceName == 'web' ? -1 : -2);
					getTreeAndCheck(treeList.resources, deList, ckList, tree[0].children);
				} else {
					//非根节点
					if (treeList && treeList.length > 0) {
						treeList.forEach((treeItem, treeIndex) => {
							if (treeItem && !treeItem.parentRcid) {
								deList.push(treeItem.rcid);
							}
							if (treeItem.beHave) {
								ckList.push(treeItem.rcid);
							}
							if (treeItem.resources) {
								tree.push({
									rcid: treeItem.rcid,
									label: treeItem.resourceName,
									children: [],
								});
								getTreeAndCheck(treeItem.resources, deList, ckList, tree[treeIndex].children);
							} else {
								tree.push({
									rcid: treeItem.rcid,
									label: treeItem.resourceName,
								});
							}
						});
					}
				}
			}
			this.$axios
				.MacgetAllResources(JSON.stringify({ fid: fid }))
				.then(res => {
					if (res.data.success) {
						if (res.data.data) {
							webTree = res.data.data.web;
							appTree = res.data.data.app;
							getTreeAndCheck(webTree, this.WebDfExpand, this.webDfChecks, this.WebTreeList, true);
							getTreeAndCheck(appTree, this.AppDfExpand, this.appDfChecks, this.AppTreeList, true);
							setTimeout(() => {
								this.$refs.webTree.setCheckedKeys(this.webDfChecks);
								this.$refs.appTree.setCheckedKeys(this.appDfChecks);
								// console.log('ddd',this.webDfChecks,this.appDfChecks)
								if (!type) {
									this.toggleAside(1);
								}
							}, 500);
						}
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('getAllResources |' + error);
				});
		},
		getVersion() {
			this.searchForm.version = [];
			this.$axios
				.selectTeamType(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						if (res.data.data && res.data.data.length > 0) {
							res.data.data.forEach(item => {
								this.searchForm.version.push(item.code);
							});
							this.queryTableData(1);
						}
					} else {
						// this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTeamType |' + error);
				});
		},
		treeChange(node, treeObj) {
			const rcid = node.rcid,
				fid = this.fidCurr;
			if (treeObj.checkedKeys.indexOf(rcid) > -1) {
				//选中节点
				this.$axios
					.addFactoryResources(
						JSON.stringify({
							fid: fid,
							rcid: rcid,
						}),
					)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('addFactoryResources |' + error);
					});
			} else {
				//删除节点
				this.$axios
					.deleteFactoryResources(
						JSON.stringify({
							fid: fid,
							rcid: rcid,
						}),
					)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('deleteFactoryResources |' + error);
					});
			}
		},
		//数据导出
		openExport: _.debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					...this.dateSelectObj,
					...this.searchForm,
					surplusDay: this.surplusDay,
				}), //接口参数
				API: 'factoryDownload', //导出接口
				downloadData: '机床厂', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss">
#teamDataMain {
	width: 100%;
	overflow: hidden;
	position: relative;

	#workOrderTable {
		// border-top:1px solid #d7d7d7;
		margin-left: -1px;

		.el-table__expand-icon > .el-icon-arrow-right {
			display: none;
		}

		.expanded {
			> td {
				border-bottom: 1px solid transparent;
				padding: 10px 0 4px 0;
			}
		}

		.el-table--striped .el-table__body tr.el-table__row--striped.expanded + tr > td.el-table__expanded-cell {
			background-color: #fafafa;
		}

		.el-table__body tr.hover-row.current-row + tr > td.el-table__expanded-cell,
		.el-table__body tr.hover-row.el-table__row--striped.current-row + tr > td.el-table__expanded-cell,
		.el-table__body tr.hover-row.el-table__row--striped + tr > td.el-table__expanded-cell,
		.el-table__body tr.hover-row + tr > td.el-table__expanded-cell {
			background-color: #f5f7fa;
		}
	}

	#workProgressToggle {
		position: absolute;
		width: 100%;
		min-height: 100%;
		top: 0;
		left: 110%;
		z-index: 66;
		transition: left 0.3s linear;
		background: #f2f2f2;
		overflow-y: overlay;
		overflow-x: hidden;

		.p_row {
			padding: 0;
			display: flex;
			align-items: center;

			.p_label {
				width: 8px;
				height: 16px;
				display: inline-block;
				background-color: #23b781;
			}

			.p_text {
				margin-left: 0.3vw;
				font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
				font-weight: 650;
				color: #666666;
			}
		}

		.p_nor {
			padding: 0 0.5vw;
			margin: 0.2vh 0;
			display: flex;
			align-items: center;

			span {
				color: #999;
				font-size: 12px;
			}

			pre {
				color: #999;
				font-family: 'Microsoft YaHei';
				font-size: 12px;
				margin: 0;
				margin-left: 0.5vw;
				display: inline-block;
			}
		}

		.workCtable {
			width: 99%;
			border-left: 1px solid #e9e9e9;
			border-bottom: 1px solid #e9e9e9;

			tr {
				border-color: #e9e9e9;

				th {
					text-align: left;
					font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
					font-weight: 650;
					font-style: normal;
					font-size: 14px;
					color: #666666;
					padding: 15px 0 15px 5px;
					background: #f5f5f5;
					border-right: 1px solid #e9e9e9;
					border-top: 1px solid #e9e9e9;
					word-wrap: break-word;
				}

				td {
					border-right: 1px solid #e9e9e9;
					border-top: 1px solid #e9e9e9;
					font-size: 12px;

					.norSpan {
						font-size: 14px;
						margin-left: 0.2vw;
						color: #666;
					}
				}
			}
		}

		/* 资源列表 */
		.ResourcesTree {
			width: 99%;
			display: flex;
			box-sizing: border-box;
			padding: 5px 10px;

			.WebTree,
			.AppTree {
				width: 30%;

				.el-tree {
					padding: 0.2vh 0 1vh 0;
				}

				.tree_p {
					font-size: 12px;
					padding: 0;
					margin: 0;
				}

				.treeP_gray {
					color: #999999;
				}

				.el-tree-node__content {
					height: 30px !important;
				}
			}
		}
	}
}
</style>
