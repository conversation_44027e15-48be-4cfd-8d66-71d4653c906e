<template>
	<div id="industryProficiency">
		<BaseLayout>
			<template #header>
				<!-- 带建议日期 -->
				<span class="search-label">日期</span>
				<DateSelect
					:dateList="['不限定', '本周', '本月', '上月']"
					:dateKeys="['startDate', 'endDate']"
					@change="
						dateSelectObj = $event;
						queryTableData(1);
					"
				/>
				<span class="search-label">行业熟练程度</span>
				<el-select
					v-model="searchForm.industryProficiency"
					size="small"
					placeholder="行业熟练程度"
					clearable
					@change="queryTableData(1)"
				>
					<el-option label="不熟（1-5）" :value="1"> </el-option>
					<el-option label="熟练（6-8）" :value="2"> </el-option>
					<el-option label="擅长（9-10）" :value="3"> </el-option>
				</el-select>
				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
				</div>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar"></div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="['startTime', 'endTime', 'modifyTime'].includes(item.colNo)"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 询盘编号 -->
							<Tooltips
								v-else-if="item.colNo == 'number'"
								class="hover-green green"
								@click.native="openDetail('修改', scope.row)"
								:cont-str="scope.row[item.colNo] || '未知'"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<!-- 行业熟练程度 -->
							<Tooltips
								v-else-if="item.colNo == 'industryProficiency'"
								:cont-str="[, '不熟（1-5）', '熟练（6-8）', '擅长（9-10）'][scope.row[item.colNo]]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<u-table-column label="" width="" align="right">
						<template slot-scope="scope">
							<el-button type="text" class="el-icon-edit-outline" @click="openDetail('修改', scope.row)"></el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
	},
	name: 'industryProficiency', //组件名应同路由名(否则keep-alive不生效)
	props: {
		twidList: Array,
		channelName: Array,
	},
	data() {
		return {
			activeTab: 'industryProficiency', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'center', width: '150' },
				{ colName: '客户称呼', colNo: 'customerName', align: 'left' },
				{ colName: '区域', colNo: 'region', align: 'left' },
				{ colName: '咨询人', colNo: 'consultName', align: 'left', width: '' },
				{ colName: '行业熟练程度', colNo: 'industryProficiency', align: 'left', width: '' },
				{ colName: '所属行业', colNo: 'industry', align: 'left' },
				{ colName: '行业备注', colNo: 'industryRemark', align: 'left' },
			],

			// 查询表单
			searchForm: {
				industryProficiency: '',
				// 其他...
			},
			// 日期相关
			dateSelectObj: {
				endDate: '',
				startDate: '',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		channelName() {
			this.queryTableData(1);
		},
		twidList() {
			this.queryTableData();
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 时间字符串转换成时间戳
		getDateTime(date, time) {
			if (date !== null) {
				const DATE = Number(new Date(date).getTime());
				if (time !== null) {
					const hour = String(time).split(':')[0] - 8;
					const min = String(time).split(':')[1];
					// let sec = String(time).split(":")[2]; + Number(sec)
					const TIME = (Number(hour * 60 * 60) + Number(min * 60)) * 1000;
					return DATE + TIME;
				}
			}
		},
		// 打开明细
		openDetail(type, row, api) {
			this.$emit('openDetail', type, row, api);
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectIndustryProficiency'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.dateSelectObj,
				...this.searchForm,
				twidList: this.twidList,
				channelName: this.channelName,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							if (item.idTime) {
								item.idTime = item.idTime.replace('-', ':');
								item.idDateTime = this.getDateTime(item.idDate, item.idTime);
							}
							if (item.province == '其他') {
								item.region = '其他';
							} else {
								item.region = _.jointString('/', item.province, item.city, item.area);
							}
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
	},
};
</script>

<style lang="scss" scoped>
#industryProficiency {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
