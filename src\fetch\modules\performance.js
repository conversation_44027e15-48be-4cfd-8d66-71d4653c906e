/* 业务顾问管理API */

const urlList = [
	// 业务顾问
	'/background/web/salesUserController/selectSalesUser', //查询列表
	'/background/web/salesUserController/insertSalesUser', //添加业务顾问
	'/background/web/salesUserController/updateSalesUser', //修改业务顾问
	'/background/web/salesUserController/selectLikeSalesName', //模糊查询业务顾问
	'/background/web/salesUserController/deleteSalesUser', //删除业务顾问
	'/background/web/salesUserController/salesUserDownload', //导出业务顾问
	// 代理人
	'/background/web/agentUserController/selectAgentUser', //查询列表
	'/background/web/agentUserController/insertAgentUser', //添加代理人
	'/background/web/agentUserController/updateAgentUser', //修改代理人
	'/background/web/agentUserController/selectLikeAgentName', //模糊查询代理人
	'/background/web/agentUserController/deleteAgentUser', //删除代理人
	'/background/web/agentUserController/agentUserDownload', //导出代理人
	// 实施顾问
	'/background/web/consultantUserController/selectConsultantUser', //查询列表
	'/background/web/consultantUserController/insertConsultantUser', //添加
	'/background/web/consultantUserController/updateConsultantUser', //修改
	'/background/web/consultantUserController/selectLikeConsultantName', //模糊查询
	'/background/web/consultantUserController/deleteConsultantUser', //删除
	'/background/web/consultantUserController/consultantUserDownload', //导出实施顾问
	// 添加询盘与业绩分配规则
	'/background/web/inquiryAllocationPerformanceIndicatorController/addInquiryAllocationPerformanceIndicator', //添加询盘与业绩分配规则
	'/background/web/inquiryAllocationPerformanceIndicatorController/addInquiryEvaluateAllocationRule', //添加咨询评价分配规则
	'/background/web/inquiryAllocationPerformanceIndicatorController/addOpsGlobalConfiguration', //添加ops全局配置

	'/background/web/inquiryAllocationPerformanceIndicatorController/deleteInquiryAllocationPerformanceIndicator', //删除询盘与业绩分配规则
	'/background/web/inquiryAllocationPerformanceIndicatorController/deleteInquiryEvaluateAllocationRule', //删除咨询评价分配规则
	'/background/web/inquiryAllocationPerformanceIndicatorController/deleteOpsGlobalConfiguration', //删除ops全局配置

	'/background/web/inquiryAllocationPerformanceIndicatorController/selectInquiryAllocationPerformanceIndicatorList', //查询询盘与业绩分配规则
	'/background/web/inquiryAllocationPerformanceIndicatorController/selectInquiryEvaluateAllocationRuleList', //查询咨询评价分配规则
	'/background/web/inquiryAllocationPerformanceIndicatorController/selectOpsGlobalConfigurationList', //查询ops全局配置

	'/background/web/inquiryAllocationPerformanceIndicatorController/updateInquiryAllocationPerformanceIndicator', //更新询盘与业绩分配规则
	'/background/web/inquiryAllocationPerformanceIndicatorController/updateInquiryEvaluateAllocationRule', //更新咨询评价分配规则
	'/background/web/inquiryAllocationPerformanceIndicatorController/updateOpsGlobalConfiguration', //更新ops全局配置

	'/background/web/inquiryAllocationPerformanceIndicatorController/fetchArea', //地区匹配查询

	// 积分明细
	'/background/web/AdminUserPointsController/selectAdminUserPointsLogList', //搜索积分日志明细数据
	'/background/web/AdminUserPointsController/updateAdminUserPointsAudit', //更新积分审核状态
	'/background/web/AdminUserPointsController/selectAdminUserPointsLogUserViewList', //更新积分审核状态
	'/background/web/AdminUserPointsController/createPointManually', //手动录入积分
	'/background/web/AdminUserPointsController/selectMonthlyAdminUserPointsLogList', //获取登录后台用户积分周统计数据(近一年)
	// 积分规则
	'/background/web/AdminUserPointsController/addAdminUserPointsConfiguration', //添加 后台用户积分配置项
	'/background/web/AdminUserPointsController/deleteAdminUserPointsConfiguration', //删除 后台用户积分配置项
	'/background/web/AdminUserPointsController/selectAdminUserPointsConfigurationList', //搜索 后台用户积分配置列表
	'/background/web/AdminUserPointsController/updateAdminUserPointsConfiguration', //更新 后台用户积分配置项
	'/background/web/AdminUserPointsController/selectMonthlyAdminUserPointsLogListByUserId', //搜索后台用户当月积分日志
	// BP健康度
	'/background/web/healthDegreeMonitoringOfBPController/selectHealthDegreeOfBP', //查询BP健康度
	'/background/web/healthDegreeMonitoringOfBPController/exportHealthDegreeOfBP', //BP健康表导出
	'/background/web/healthDegreeMonitoringOfBPController/selectHealthDetailOfBP', //查询BP健康详情
	'/background/web/healthDegreeMonitoringOfBPController/healthDegreeOfBPStatistics', //BPUL健康统计
];

// 重名函数映射
const apiNameMap = {};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['exportHealthDegreeOfBP'].includes(urlName)) {
		timeout = 60000;
	}

	return { urlName, url, timeout };
});

export default apiList;
