<template>
	<div id="lightProData">
		<BaseLayout :showHeader="false">
			<template #main>
				<u-table ref="uTableRef" class="table-main" :data="proTableData" :height="1200" stripe>
					<u-table-column label="行号" width="50" type="index"></u-table-column>
					<u-table-column label="产品" prop="productName" width="200">
						<template slot-scope="scope">
							<Tooltips :cont-str="scope.row.productName" :cont-width="(scope.column.width || scope.column.realWidth) - 20" />
						</template>
					</u-table-column>
					<u-table-column label="型号" prop="typeName" width="200"></u-table-column>
					<u-table-column label="分类" prop="classCategory" width="200"></u-table-column>
					<u-table-column label="备注" prop="description">
						<template slot-scope="scope">
							<Tooltips :cont-str="scope.row.description" :cont-width="(scope.column.width || scope.column.realWidth) - 20" />
						</template>
					</u-table-column>
					<u-table-column label="" align="right" width="200">
						<template slot="header" slot-scope="scope">
							<el-button type="text" class="icon-third-bt_newdoc" @click="addProLight">添加</el-button>
						</template>
						<template slot-scope="scope">
							<el-button type="text" class="el-icon-edit-outline" @click="editLightPro(scope.row)">修改 </el-button>
							<el-button type="text" class="el-icon-close" @click="delLightPro(scope.row, scope.$index)">删除</el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>

		<el-dialog :visible.sync="dialogEditPro" width="30%" :close-on-click-modal="false" @close="cancelEditWork">
			<el-row slot="title">{{ isEdit ? '修改产品' : '添加产品' }}</el-row>
			<el-form :model="editProLightForm" label-width="4vw" label-position="left">
				<el-form-item label="版本" prop="productName">
					<el-input placeholder="请输入版本" v-model="editProLightForm.productName"></el-input>
				</el-form-item>
				<el-form-item label="型号" prop="classType">
					<el-select class="W100" v-model="editProLightForm.classType" placeholder="请选择型号">
						<el-option v-for="item in classTypeList" :key="'classType' + item.id" :label="item.typeName" :value="item.id">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="分类" prop="classCategory">
					<el-select class="W100" v-model="editProLightForm.classCategory" placeholder="请选择分类">
						<el-option v-for="item in NetTypeList" :key="'netType' + item" :label="item" :value="item"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="备注" prop="description">
					<el-input placeholder="请输入备注" v-model="editProLightForm.description"></el-input>
				</el-form-item>
				<el-form-item label="云key" prop="description">
					<el-input placeholder="请输入云key" :disabled="isEdit" v-model="editProLightForm.productKey"></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveEditWork">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'lithgtProData',
	data() {
		return {
			proTableData: [],
			// workPageForm:{
			//   total:0,
			//   pageSize:100,
			//   currentPage:1,
			//   pageSizes:[100,500,1000]
			// },
			dialogEditPro: false,
			//产品信息修改
			editProLightForm: {
				proId: '',
				productName: '',
				classType: '',
				description: '',
				classCategory: '',
				productKey: '',
			},
			btnFlag: false,
			isEdit: false,
			//产品列表
			NetTypeList: ['4G', 'WIFI'],
			classTypeList: [],
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['menuTitle']) },
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {
		this.queryProData();
		this.getClassType();
	},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		queryProData() {
			this.proTableData = [];
			this.$axios
				.getTricolourProductList(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.proTableData = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {

					console.log('getTricolourProductList |' + error);
				});
		},
		addProLight() {
			this.dialogEditPro = true;
			this.isEdit = false;
		},
		getClassType() {
			this.classTypeList = [];
			this.$axios
				.getClassType(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.classTypeList = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('getClassType |' + error);
				});
		},
		cancelEditWork() {
			this.dialogEditPro = false;
			this.isEdit = false;
			this.editProLightForm.productName = '';
			this.editProLightForm.classType = '';
			this.editProLightForm.classCategory = '';
			this.editProLightForm.description = '';
			(this.editProLightForm.proId = ''), (this.editProLightForm.productKey = '');
		},
		saveEditWork() {
			let str;
			if (this.isEdit) {
				//修改产品
				str = JSON.stringify({
					classCategory: this.editProLightForm.classCategory,
					classType: this.editProLightForm.classType,
					description: this.editProLightForm.description,
					productName: this.editProLightForm.productName,
					id: this.editProLightForm.proId,
				});
				this.$axios
					.updateTricolourProduct(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.cancelEditWork();
							this.queryProData();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('updateTricolourProduct |' + error);
					});
			} else {
				//添加产品
				str = JSON.stringify({
					classCategory: this.editProLightForm.classCategory,
					classType: this.editProLightForm.classType,
					description: this.editProLightForm.description,
					productName: this.editProLightForm.productName,
					productKey: this.editProLightForm.productKey,
				});
				this.$axios
					.addTricolourProduct(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.cancelEditWork();
							this.queryProData();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('addTricolourProduct |' + error);
					});
			}
		},
		editLightPro(row) {
			this.editProLightForm.proId = row.id;
			this.editProLightForm.productName = row.productName;
			this.editProLightForm.classCategory = row.classCategory;
			this.editProLightForm.classType = row.classType;
			this.editProLightForm.description = row.description;
			this.editProLightForm.productKey = row.productKey;
			this.isEdit = true;
			this.dialogEditPro = true;
		},
		delLightPro(row, index) {
			//删除产品
			let id = row.id,
				msg;
			if (!row) {
				return;
			}
			msg = '产品【' + row.productName + '】将被删除';
			const str = JSON.stringify({
				id: id,
			});

			this.$confirm(msg, '删除产品', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteTricolourProduct(str)
						.then(res => {
							if (res.data.success) {
								this.$succ('操作成功!');
								this.proTableData.splice(index, 1);
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteTricolourProduct |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
	},
};
</script>
<style lang="scss" scoped>
#lightProData {
	.table-wrapper {
		.table-main {
			height: calc(100vh - 190px) !important;
		}
	}
}
</style>
