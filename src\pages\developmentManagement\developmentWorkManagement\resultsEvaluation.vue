<template>
	<div id="resultsEvaluation">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<taskList ref="taskList" :parentType="'result'" />

		<BaseLayout>
			<template #header>
				<span class="search-label">年度</span>
				<el-date-picker
					size="small"
					class="w-150"
					v-model="selectTime"
					:default-value="selectTime"
					type="year"
					value-format="timestamp"
					format="yyyy 年"
					placeholder="请选择年份"
					:clearable="false"
					@change="changeDateSelect"
				>
				</el-date-picker>
				<el-radio-group v-model="userStatus" @change="queryTableData(1)">
					<el-radio :label="1">开发人员</el-radio>
					<el-radio :label="0">测试人员</el-radio>
				</el-radio-group>
				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-toolbar">
					<span class="mr-auto">计量单位: {{ userStatus == 1 ? '开发' : '测试' }}绩效工时</span>
					<!-- 导出按钮 -->
					<ExportBtn @trigger="openExport" />
				</div>
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					@sort-change="sortChange"
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column label="序号" width="50" align="center" type="index"> </u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						resizable
						sortable="custom"
					>
						<template slot-scope="scope">
							<div
								class="hover-green"
								@click="openList(item.colNo, scope.row)"
								v-if="item.colNo != 'productUname' && item.colNo != 'total' && scope.row[item.colNo]"
							>
								{{ scope.row[item.colNo] }}
							</div>
							<!-- 合计 -->
							<Tooltips
								v-else-if="item.colNo == 'total' && scope.row[item.colNo]"
								class="hover-green"
								@click.native="openList(item.colNo, scope.row)"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>
<script>
import { debounce, dateFormat, sortTableData, getNowMonthEndDay, accAdd } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import ExportTable from '@/components/ExportTable'; //导出组件
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮

import taskList from './developmentWorkTaskList.vue';
export default {
	name: 'resultsEvaluation',
	components: { ExportTable, ExportBtn, taskList },
	props: {},
	data() {
		return {
			//日期相关
			selectTime: new Date(),
			userStatus: 1,
			startTime: '',
			endTime: '',
			searchForm: {
				type: 1,
				statusList: [4],
				projectTurnTest: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableColumn: [
				{ colName: '研发人员', colNo: 'productUname', align: 'left', width: 100 },
				{ colName: '一月', colNo: '1', align: 'right' },
				{ colName: '二月', colNo: '2', align: 'right' },
				{ colName: '三月', colNo: '3', align: 'right' },
				{ colName: '四月', colNo: '4', align: 'right' },
				{ colName: '五月', colNo: '5', align: 'right' },
				{ colName: '六月', colNo: '6', align: 'right' },
				{ colName: '七月', colNo: '7', align: 'right' },
				{ colName: '八月', colNo: '8', align: 'right' },
				{ colName: '九月', colNo: '9', align: 'right' },
				{ colName: '十月', colNo: '10', align: 'right' },
				{ colName: '十一月', colNo: '11', align: 'right' },
				{ colName: '十二月', colNo: '12', align: 'right' },
				{ colName: '合计', colNo: 'total', align: 'right' },
			],
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['menuTitle']) },
	// 监控data中的数据变化
	watch: {
		userStatus(newVal) {
			this.tableColumn[0].colName = newVal ? '研发人员' : '测试人员';
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		const nowYear = new Date().getFullYear();
		this.changeDateSelect(new Date(nowYear, '0', '1').getTime());
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 交付清单
		openList(monthIndex, row) {
			// 获取当前月份第一天时间戳
			const now = new Date(this.startTime) || new Date(this.selectTime); // 获取当前年份
			let dateStart = '';
			let dateEnd = '';
			if (monthIndex !== 'total' && monthIndex < 13) {
				// 非合计
				const firstDayOfMonth = new Date(now.getFullYear(), monthIndex - 1, 1); // 当月的第一天
				dateStart = firstDayOfMonth.getTime(); // 获取时间戳
				dateEnd = getNowMonthEndDay(dateStart); // 当前月份最后一天时间戳
			} else {
				// 合计
				const firstDayOfYear = new Date(now.getFullYear(), 0, 1); // 当年的第一天
				dateStart = firstDayOfYear.getTime(); // 获取时间戳
				const lastDayOfYear = new Date(now.getFullYear(), 11, 31); // 当年的最后一天
				dateEnd = lastDayOfYear.getTime() + 86399999; // 获取时间戳
			}
			//任务河流查要求转测日期 ， 成果评价查测试通过日期，
			this.searchForm.testPassStartTime = dateStart;
			this.searchForm.testPassEndTime = dateEnd;
			this.searchForm.queryParam = this.userStatus === 1 ? row?.productUname || '' : '';
			this.searchForm.projectTurnTest = this.userStatus === 1 ? '' : row?.productUname || '';
			this.$refs.taskList.openTaskList(this.searchForm);
		},

		// 获取业务河流数据
		queryTableData: debounce(function (type) {
			this.tableData = [];
			const str = JSON.stringify({
				endTime: this.endTime,
				startTime: this.startTime,
				userStatus: this.userStatus,
				pageNum: 1,
				pageSize: 100,
			});
			this.$axios
				.achievementAssessment(str)
				.then(res => {
					if (res.data.success) {
						res.data.data?.map(item => {
							item.total = accAdd(item.total, 0, 2);
							item.monthData.map(monthItem => {
								item[monthItem.month] = accAdd(monthItem.workHour, 0, 2);
							});
						});
						this.tableData = res.data.data?.sort((a, b) => b.total - a.total);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						console.log(this.tableData);
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('achievementAssessment |' + error);
				});
		}),

		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					endTime: this.endTime,
					startTime: this.startTime,
					userStatus: this.userStatus,
					pageNum: 1,
					pageSize: 100,
				}), //接口参数
				API: 'achievementAssessmentExport', //导出接口
				downloadData: '成果评价导出', //数据报表参数（后台确认字段downloadData）
				type,
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),

		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = []; // 合计
			columns.forEach((column, columnIndex) => {
				if (columnIndex === 0) {
					means.push('合计');
				} else if (columnIndex === 1) {
					means.push('');
				} else {
					const values = data?.map(item => {
						return Number(item[column.property]?.amount || item[column.property]);
					});

					// 合计
					if (!values.every(value => isNaN(value))) {
						means[columnIndex] = values.reduce((prev, curr) => {
							const value = Number(curr);
							// console.log(value);
							if (!isNaN(value)) {
								return this.accAdd(prev, curr);
							} else {
								return prev;
							}
						}, 0);
						if (!isNaN(means[columnIndex])) {
							means[columnIndex] = 
								<span
									class="hover-green"
									on-click={e => {
										e.stopPropagation();
										this.openList(columnIndex - 1, null);
									}}
								>
									{means[columnIndex]}
								</span>
							;
						} else {
							means[columnIndex] = '';
						}
					} else {
						means[columnIndex] = '';
					}
				}
			});
			// console.log(columns);
			// 返回一个二维数组的表尾合计(不要平均值，你就不要在数组中添加)
			return [means];
		},
		// 数值加法精准度设置
		accAdd(arg1, arg2) {
			let r1, r2, m;
			try {
				r1 = arg1.toString().split('.')[1].length;
			} catch (e) {
				r1 = 0;
			}
			try {
				r2 = arg2.toString().split('.')[1].length;
			} catch (e) {
				r2 = 0;
			}
			m = Math.pow(10, Math.max(r1, r2));
			m *= 10;
			return (parseInt(arg1 * m + arg2 * m) / m).toFixed(2);
		},

		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		// 时间选择
		changeDateSelect(date) {
			const year = new Date(date).getFullYear();
			this.startTime = new Date(year, '0', '1').getTime();
			this.endTime = new Date(year + 1, '0', '1').getTime() - 1;
			// console.log(date);
			this.queryTableData();
		},
	},
};
</script>

<style lang="scss" scoped>
#resultsEvaluation {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
