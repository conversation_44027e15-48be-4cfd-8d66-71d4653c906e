import SmartPoller from './smartPoller';

/**
 * 全局轮询管理器
 * 管理多个轮询实例，提供统一的控制接口
 */
class PollerManager {
	constructor() {
		this.pollers = new Map();
	}

	/**
	 * 创建并注册一个新的轮询器
	 * @param {String} id - 轮询器唯一标识
	 * @param {Object} options - 轮询器配置
	 * @returns {SmartPoller} 创建的轮询器实例
	 */
	create(id, options) {
		if (!id) {
			throw new Error('轮询器ID不能为空');
		}
		if (this.pollers.has(id)) {
			console.warn(`🚨 Poller  "${id}" 已经存在， 销毁旧的轮询器。`);
			this.destroy(id);
		}

		const poller = new SmartPoller(id, options);
		this.pollers.set(id, poller);
		return poller;
	}

	/**
	 * 获取轮询器实例
	 * @param {String} id - 轮询器唯一标识
	 * @returns {SmartPoller|null} 轮询器实例或null
	 */
	get(id) {
		return this.pollers.get(id) || null;
	}

	/**
	 * 销毁轮询器实例
	 * @param {String} id - 轮询器唯一标识
	 * @returns {Boolean} 是否成功销毁
	 */
	destroy(id) {
		const poller = this.pollers.get(id);
		if (poller) {
			poller.destroy();
			this.pollers.delete(id);
			return true;
		}
		return false;
	}

	/**
	 * 暂停所有轮询器
	 */
	pauseAll() {
		this.pollers.forEach(poller => poller.pause());
	}

	/**
	 * 恢复所有轮询器
	 */
	resumeAll() {
		this.pollers.forEach(poller => poller.resume());
	}

	/**
	 * 销毁所有轮询器
	 */
	destroyAll() {
		this.pollers.forEach(poller => poller.destroy());
		this.pollers.clear();
	}

	/**
	 * 获取所有轮询器ID
	 * @returns {Array} 轮询器ID数组
	 */
	getAllIds() {
		return Array.from(this.pollers.keys());
	}

	/**
	 * 获取轮询器数量
	 * @returns {Number} 轮询器数量
	 */
	count() {
		return this.pollers.size;
	}
}

// 创建单例实例
const pollerManager = new PollerManager();

export default pollerManager;
