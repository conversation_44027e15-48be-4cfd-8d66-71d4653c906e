<template>
	<div class="ExpensePrinter" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}报销单</span>
				<div>
					<el-button type="text" class="el-icon-printer" @click="printOrder">打印</el-button>
					<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
				</div>
			</div>
			<!-- 明细详情弹窗 -->
			<div id="PrintWrapper">
				<div class="detail-content pr30 pl30" id="PrintBody" v-for="(printData, pIndex) in printDatas" :key="pIndex">
					<div class="order-title flex-algin-center">
						<h2></h2>
						<h2>差旅报销单</h2>
						<!-- 条形码 -->
						<svg class="w-200 h-100 ml-auto barcode" :id="'barcode-' + pIndex"></svg>
					</div>

					<p class="order-info" v-for="(aItem, aIndex) in formList" :key="aIndex">
						<span class="order-info-item" :class="item.class" v-for="item in aItem" :key="item.prop">
							<span class="label">{{ item.name }}:</span>
							<!-- 期间 -->
							<span v-if="item.prop == 'dateRange'" class="text">
								{{ dateFormat(printData.formInfo.tripBeginDate, 'line') }}
								~
								{{ dateFormat(printData.formInfo.tripEndDate, 'line') }}
								共 {{ printData.formInfo.tripDays }} 天
							</span>
							<!-- 差旅类型 -->
							<span v-else-if="item.prop == 'tripType'" class="text">{{ tripTypeMap[printData.formInfo[item.prop]] }}</span>
							<!-- 承担方 -->
							<span v-else-if="item.prop == 'expenseParty'" class="text">{{
								expensePartyMap[printData.formInfo[item.prop]]
							}}</span>
							<!-- 结算类型 -->
							<span v-else-if="item.prop == 'settlementType'" class="text">
								{{ settlementTypeMap[printData.formInfo['settlementType_apply']] }}
								/
								{{ settlementTypeMap[printData.formInfo['settlementType_reimbursement']] }}
							</span>
							<!-- 报销日期 -->
							<span v-else-if="item.prop == 'applyDate'" class="text">
								{{ dateFormat(printData.formInfo[item.prop], 'line') }}
							</span>
							<span v-else class="text">{{ printData.formInfo[item.prop] }}</span>
						</span>
					</p>

					<p>费用明细</p>
					<div class="print-table">
						<table cellpadding="5" cellspacing="0">
							<!-- 表头 -->
							<thead>
								<tr>
									<th>序号</th>
									<th v-for="(item, index) in tableColumn" :key="index" :class="item.align">{{ item.colName }}</th>
								</tr>
							</thead>
							<!-- 表体 -->
							<tbody>
								<tr
									v-for="(item, index) in printData.tableData.length < 10
										? printData.tableData.concat(new Array(10 - printData.tableData.length).fill({}))
										: printData.tableData"
									:key="index"
									class="table-row"
								>
									<td class="center">{{ index < printData.tableData.length ? index + 1 : '' }}</td>
									<!-- <td class="center">{{ index + 1 }}</td> -->
									<td class="center">{{ dateFormat(item.expenseDate, 'line') }}</td>
									<td>{{ expenseTypeMap[item.expenseType] }}</td>
									<td>{{ item.expenseMemo }}</td>
									<td class="right">{{ item.amount?.toFixed(2) || '' }}</td>
								</tr>
							</tbody>

							<!-- 合计行 -->
							<tr>
								<td colspan="5" class="right">合计：{{ getTotalAmount(printData.tableData) }} </td>
							</tr>
							<!-- 表尾 -->
							<tfoot>
								<tr>
									<td colspan="3">
										<p>报销人：{{ printData.formInfo.reimbursementUserName }}</p>
									</td>
									<td colspan="1">
										<p>部门审批：</p>
									</td>
									<td colspan="1">
										<p>领导审批：</p>
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';
import printJS from 'print-js';
import JsBarcode from 'jsbarcode';
import { tripTypeMap, settlementTypeMap, expensePartyMap } from '@/assets/js/contractSource'; // 结算类型 差旅类别 费用承担方
export default {
	name: 'ExpensePrinter',
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '',
			printDatas: [],
			formInfo: {},
			tableData: [],

			tableColumn: [
				{ colName: '日期', colNo: 'expenseDate', align: 'center', width: '150' },
				{ colName: '费用类别', colNo: 'expenseType', align: 'left', width: '100' },
				{ colName: '费用说明', colNo: 'expenseMemo', align: 'left', width: '' },
				{ colName: '报销金额', colNo: 'amount', align: 'right', width: '150' },
			],
			formList: [
				[
					{ name: '业务顾问', prop: 'salesmanName', class: 'W30' },
					{ name: '出差申请人', prop: 'applyUName', class: 'W40' },
					{ name: '申请/报销结算', prop: 'settlementType', class: 'W30' },
				],
				[
					{ name: '客户', prop: 'tripClientName', class: 'W30', type: 'text' },
					{ name: '出差地', prop: 'tripDestination', class: '  W40', type: 'text' },
					{ name: '承担方', prop: 'expenseParty', class: '  W30', type: 'text' },
				],

				[
					{ name: '差旅类型', prop: 'tripType', class: 'W30' },
					{ name: '期间', prop: 'dateRange', class: 'W40' },
					{ name: '报销日期', prop: 'applyDate', class: 'W30' },
				],
			],
			expenseTypeMap: {
				1: '用车费',
				2: '停车费',
				3: '过路费',
				10: '交通费',
				20: '出差补助',
				30: '住宿费',
				50: '餐费',
				127: '其他',
			},
			tripTypeMap, //差旅类别
			settlementTypeMap, // 结算类型
			expensePartyMap, // 费用承担方
		};
	},
	created() {},
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 计算合计金额
		getTotalAmount(tableData) {
			return tableData.reduce((total, item) => {
				return _.accAdd(total, Number(item.amount), 2);
			}, 0);
		},
		// 生成条形码
		generateBarcode(barcodeValue, index) {
			JsBarcode(`#barcode-${index}`, barcodeValue);
		},
		printOrder() {
			const style = `
				@page {
						size: auto;
						margin:0 15mm;
				}
        @media print{
					.barcode {
						margin-left: auto;
						width: 200px;
						height: 100px;
					}
					#PrintBody {
						font-size: 9pt;
						box-sizing: border-box;
						width: 100%;
						padding: 1vh 0;
					}
					#PrintBody .order-title {
						display: flex;
						align-items: center;
					}
					#PrintBody h2 {
						display: inline-block;
						margin-left: auto;
						padding-left: 1vw;
					}

					#PrintBody .order-info .order-info-item {
						width: 30%;
						display: inline-block;
						white-space: nowrap;            /* 不换行 */  
						overflow: hidden;               /* 超出部分隐藏 */  
						text-overflow: ellipsis;        /* 超出部分用省略号表示 */ 
					}

					#PrintBody .print-table table {
						width: 100%;
						table-layout: fixed;
					}

					#PrintBody .print-table table th {
						text-align: left;               /* 左对齐文本 */  
						white-space: nowrap;            /* 不换行 */  
						overflow: hidden;               /* 超出部分隐藏 */  
						text-overflow: ellipsis;        /* 超出部分用省略号表示 */  
						border-collapse: collapse;      /* 表格合并边框 */  
						border-top: 1px solid gray;     /* 上边框 */  
						border-bottom: 1px solid gray;  /* 下边框 */ 
					}
					#PrintBody .print-table table td {
						font-size: 8pt;
						text-align: left;
						word-break: break-all;
						padding: 0 5px !important;
					}

					#PrintBody .print-table table tbody {
						height: 195px;
					}
					#PrintBody .print-table table tbody tr {
						height: 20px !important; /* 设置固定行高，使用 !important */
					}
				
					#PrintBody .print-table table tfoot td{
						border-top: 1px solid gray;
					}

					#PrintBody .right {
						text-align: right !important;
					}
					#PrintBody .center {
						text-align: center !important;
					}
					#PrintBody .W20 {
						width: 20% !important;
					}
					#PrintBody .W25 {
						width: 25% !important;
					}
					#PrintBody .W30 {
						width: 30% !important;
					}
					#PrintBody .W40 {
						width: 40% !important;
					}

					#PrintBody .print-table table th:first-child {
						width: 5% !important;
					}
					#PrintBody .print-table table th:nth-child(2) {
						width: 15% !important;
					}
					#PrintBody .print-table table th:nth-child(3) {
						width: 15% !important;
					}
					#PrintBody .print-table table th:nth-child(5) {
						width: 20% !important;
					}
        }      
        `;
			printJS({
				printable: 'PrintWrapper',
				type: 'html',
				// css: './PrinterStyle.css',
				documentTitle: '差旅报销单',
				scanStyles: false,
				style: style,
				targetStyles: ['*'],
				font: 'Microsoft YaHei',
			});
		},

		//显示弹窗
		showDetailCom: _.debounce(async function (type, printDatas) {
			this.titleName = type;
			// this.showCom = true; //不打开弹窗直接打印
			this.printDatas = printDatas;
			this.$nextTick(() => {
				this.printDatas.forEach((item, index) => {
					this.generateBarcode(item.formInfo.reimbursementNo, index);
				});
				this.printOrder();
			});
			console.log({ printDatas });
		}, 500),

		//点击返回
		closeDetailCom() {
			this.showCom = false;
		},
		//日期format
		dateFormat: _.dateFormat,
		jointString: _.jointString,
	},
};
</script>
<style lang="scss" scoped>
.ExpensePrinter {
	/* 在此处调完之后打印样式后再复制到printJS的style字符串中（注意是css样式） */
	#barcode {
		margin-left: auto;
		width: 200px;
		height: 100px;
	}
	#PrintBody {
		font-size: 9pt;
		box-sizing: border-box;
		width: 100%;
		padding: 1vh 0;
	}
	#PrintBody .order-title {
		display: flex;
		align-items: center;
	}
	#PrintBody h2 {
		display: inline-block;
		margin-left: auto;
		padding-left: 1vw;
	}

	#PrintBody .order-info .order-info-item {
		width: 30%;
		display: inline-block;
		white-space: nowrap; /* 不换行 */
		overflow: hidden; /* 超出部分隐藏 */
		text-overflow: ellipsis; /* 超出部分用省略号表示 */
	}

	#PrintBody .print-table table {
		width: 100%;
		table-layout: fixed;
	}

	#PrintBody .print-table table th {
		text-align: left; /* 左对齐文本 */
		white-space: nowrap; /* 不换行 */
		overflow: hidden; /* 超出部分隐藏 */
		text-overflow: ellipsis; /* 超出部分用省略号表示 */
		border-collapse: collapse; /* 表格合并边框 */
		border-top: 1px solid gray; /* 上边框 */
		border-bottom: 1px solid gray; /* 下边框 */
	}
	#PrintBody .print-table table td {
		font-size: 8pt;
		text-align: left;
		word-break: break-all;
		padding: 0 5px !important;
	}
	#PrintBody .print-table table tbody {
		height: 200px;
	}
	#PrintBody .print-table table tbody tr {
		height: 20px !important; /* 设置固定行高，使用 !important */
	}
	#PrintBody .print-table table tfoot td {
		border-top: 1px solid gray;
	}

	#PrintBody .right {
		text-align: right !important;
	}
	#PrintBody .center {
		text-align: center !important;
	}
	#PrintBody .W20 {
		width: 20% !important;
	}
	#PrintBody .W40 {
		width: 40% !important;
	}

	#PrintBody .print-table table th:first-child {
		width: 5% !important;
	}
	#PrintBody .print-table table th:nth-child(2) {
		width: 15% !important;
	}
	#PrintBody .print-table table th:nth-child(3) {
		width: 15% !important;
	}
	#PrintBody .print-table table th:nth-child(5) {
		width: 20% !important;
	}
}
</style>
