<template>
	<div id="lightAutoRegistData">
		<!-- 三色灯标签打印 -->
		<PrintLightLabel ref="PrintLightLabelRef" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="三色灯列表" name="lightAutoRegistData">
				<BaseLayout>
					<template #header>
						<span class="search-label">创建时间</span>
						<DateSelect
							@change="
								searchForm.createStartTime = $event.startTime;
								searchForm.createEndTime = $event.endTime;
								queryTableData(1);
							"
						/>

						<SearchHistoryInput name="sim" placeholder="三色灯编号" v-model.trim="searchForm.sim" @input="queryTableData(1)" />
						<SearchHistoryInput
							name="printSize"
							placeholder="打印次数"
							v-model.trim="searchForm.printSize"
							@input="queryTableData(1)"
						/>

						<el-radio-group v-model="searchForm.checkLable" @change="queryTableData(1)">
							<el-radio :label="0">未贴标签</el-radio>
							<el-radio :label="1">已贴标签</el-radio>
						</el-radio-group>

						<el-checkbox-group v-model="searchForm.onLine" @change="queryTableData(1)">
							<el-checkbox label="0">离线</el-checkbox>
							<el-checkbox label="1">在线</el-checkbox>
						</el-checkbox-group>

						<el-radio-group v-model="searchForm.classCategory" @change="queryTableData(1)">
							<el-radio label="4G">4G</el-radio>
							<el-radio label="WiFi">WiFi</el-radio>
						</el-radio-group>

						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新 </el-button>
						<el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<!-- <el-button type="text" class="icon-third_xiugai" @click="editLightAll">批量修改</el-button> -->
							<!-- <el-button type="text" class="el-icon-close" @click="deleteAll">批量删除</el-button> -->

							<el-button type="text" class="el-icon-printer" @click="printAll">批量打印</el-button>
							<el-button type="text" class="icon-third_searchC" v-popover:thSearch>查找</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:dialog-data="tableColumn"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							@header-dragend="headerDragend"
							@reset="updateColumn(tableColumnCopy)"
							@selection-change="selectionData = $event"
							@show-field="updateColumn"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column type="selection" width="30" label=""></u-table-column>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="(item, index) in tableColumn.filter(item => item.state)"
								:key="item.colNo + index"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['productionDate', 'invoiceTime', 'bindingTime', 'operatorExpiration'].includes(item.colNo)"
										:toolClass="'tdTwoNormal'"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<Tooltips
										v-else-if="['simTime', 'updateTime', 'createTime'].includes(item.colNo)"
										:toolClass="'tdTwoNormal'"
										:cont-str="dateFormat(scope.row[item.colNo], 'ALL')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 编号 -->
									<Tooltips
										v-else-if="item.colNo == 'sim'"
										:toolClass="'tdTwoNormal'"
										class="hover-green green"
										@click.native="openLightDetail(scope.row.sim, null, true)"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<!-- 出厂检查 -->
									<Tooltips
										v-else-if="item.colNo == 'lightType'"
										:cont-str="lightTypeMap[scope.row.lightType]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 是否已贴标签 -->
									<Tooltips
										v-else-if="item.colNo == 'checkLable'"
										:cont-str="scope.row.checkLable == 1 ? '已贴' : '未贴'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 在线状态 -->
									<div v-else-if="item.colNo == 'onLine'">
										<span v-if="scope.row.onLine">
											<i
												v-if="scope.row.currentState"
												class="icon-third_graysd fs-24"
												:style="{ color: onlineColorMap[scope.row.currentState] }"
											></i>
											<span v-else>{{ onlineStatusMap[scope.row.onLine] }}</span>
										</span>
										<span v-else>{{ onlineStatusMap[scope.row.onLine] }}</span>
									</div>

									<Tooltips
										v-else
										:toolClass="'tdTwoNormal'"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
		<div id="lightMainToggle">
			<div style="padding: 0">
				<div
					style="
						border-radius: 15px 15px 0 0;
						width: 100%;
						border: 1px solid #e9e9e9;
						background: #f9f9f9;
						border-bottom: 1px solid transparent;
						box-sizing: border-box;
					"
				>
					<el-row class="orgManageHead" style="padding: 10px; box-sizing: border-box">
						<el-col :span="8" style="font-size: 18px; line-height: 40px; font-weight: 500">
							<span style="margin-left: 0.5vw">{{ lightEdit ? '修改三色灯参数' : '查看三色灯参数' }}</span>
						</el-col>
						<el-col :span="8" :offset="8" style="text-align: right">
							<el-button type="text" v-show="!lightEdit" class="icon-third_xiugai" @click="lightEdit = true">修改</el-button>
							<el-button type="text" class="el-icon-arrow-left" @click="cleartLight">返回</el-button>
						</el-col>
					</el-row>
					<div
						style="
							background: #fff;
							border-top: 1px solid #d7d7d7;
							padding: 0 10px;
							box-sizing: border-box;
							min-height: 80vh;
							overflow: auto;
						"
					>
						<div style="padding-left: 1vw">
							<p class="p_row" style="align-items: flex-end">
								<span style="display: flex-inline; align-items: center">
									<span class="p_label"></span>
									<span class="p_text">基本信息</span>
								</span>
								<canvas id="qrcodeLight" style="margin-bottom: -13px"></canvas>
							</p>
							<table class="workCtable" cellpadding="5" cellspacing="0">
								<tr>
									<th>三色灯编号</th>
									<th>绿灯闪烁间隔(0.1秒)</th>
									<th>黄灯闪烁间隔(0.1秒)</th>
									<th>红灯闪烁间隔(0.1秒)</th>
									<th>离线重启次数(次)</th>
									<th>消抖时长(毫秒)</th>
									<th>MQTT连接方式</th>
								</tr>
								<tr>
									<td>
										<span>{{ lightInfos.sim }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入绿灯闪烁间隔"
											v-model="lightInfos.greenInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.greenInterval }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入黄灯闪烁间隔"
											v-model="lightInfos.yellowInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.yellowInterval }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入红灯闪烁间隔"
											v-model="lightInfos.redInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.redInterval }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入离线重启次数"
											v-model="lightInfos.offlineReset"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.offlineReset }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入消抖时长"
											v-model="lightInfos.debounceTime"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.debounceTime }}</span>
									</td>
									<td>
										<el-select type="number" v-show="lightEdit" placeholder="请选择MQTT连接方式" v-model="lightInfos.mqttType">
											<el-option
												v-for="key in Object.keys(mqttTypeMap)"
												:key="key"
												:label="mqttTypeMap[key]"
												:value="Number(key)"
											>
											</el-option>
										</el-select>
										<span v-show="!lightEdit">{{ mqttTypeMap[lightInfos.mqttType] }}</span>
									</td>
								</tr>
								<tr>
									<th>心跳间隔(秒)</th>
									<th>脉冲间隔(秒)</th>
									<th>信号强度</th>
									<th>MAC地址</th>
									<th>消息版本</th>
									<th>数据周期(秒)</th>
									<th>空数据发送</th>
								</tr>
								<tr>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入心跳间隔"
											v-model="lightInfos.heartInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.heartInterval }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入脉冲间隔"
											v-model="lightInfos.pulseInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.pulseInterval }}</span>
									</td>
									<td>
										<span>{{ lightInfos.signalStrength }}</span>
									</td>
									<td>
										<span>{{ lightInfos.macUrl }}</span>
									</td>
									<td>
										<span>{{ lightInfos.messageVersion }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="lightEdit"
											placeholder="请输入计数间隔(秒)"
											v-model="lightInfos.countInterval"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.countInterval }}</span>
									</td>
									<td>
										<el-select
											type="number"
											v-show="lightEdit"
											placeholder="请选择空数据发送"
											v-model="lightInfos.countZeroReport"
										>
											<el-option
												v-for="item in noDataList"
												:key="'nd' + item.id"
												:label="item.label"
												:value="item.id"
											></el-option>
										</el-select>
										<span v-show="!lightEdit">{{ noDataMap[lightInfos.countZeroReport] }}</span>
									</td>
								</tr>
								<tr>
									<th colspan="2">URL</th>
									<th>用户名</th>
									<th>密码</th>
									<th>产品ProductKey</th>
									<th>产品唯一标识</th>
									<th>设备密钥</th>
								</tr>
								<tr>
									<td colspan="2">
										<el-input
											type="text"
											v-show="lightEdit"
											placeholder="请输入MQTT连接地址"
											v-model="lightInfos.mqttUrl"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.mqttUrl }}</span>
									</td>
									<td>
										<el-input
											type="text"
											v-show="lightEdit"
											placeholder="请输入MQTT用户名"
											v-model="lightInfos.mqttUser"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.mqttUser }}</span>
									</td>
									<td>
										<el-input
											type="password"
											v-show="lightEdit"
											placeholder="请输入MQTT密码"
											v-model="lightInfos.mqttPawd"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.mqttPawd }}</span>
									</td>
									<td>
										<el-input
											type="text"
											v-show="lightEdit"
											placeholder="请输入产品roductKey"
											v-model="lightInfos.locaProductKey"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.locaProductKey }}</span>
									</td>
									<td>
										<el-input
											type="text"
											v-show="lightEdit"
											placeholder="请输入产品唯一标识"
											v-model="lightInfos.locaDeviceName"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.locaDeviceName }}</span>
									</td>
									<td>
										<el-input
											type="text"
											v-show="lightEdit"
											placeholder="请输入物联网平台设备密钥"
											v-model="lightInfos.locaDeviceSecret"
										></el-input>
										<span v-show="!lightEdit">{{ lightInfos.locaDeviceSecret }}</span>
									</td>
								</tr>
							</table>
							<div :style="{ visibility: lightInfos.updated && lightInfos.updated != 1 ? 'visible' : 'hidden' }" class="bot_left">
								<span
									>*<span>{{ getUpdateStr(lightInfos.updated) }}</span></span
								>
								<el-button
									type="text"
									class="el-icon-refresh-right"
									@click="openLightDetail(lightInfos.sim, true, !lightEdit)"
									style="margin-left: 2vw"
									>刷新</el-button
								>
							</div>
							<div style="text-align: right; margin-top: 0.5vh">
								<el-button
									type="text"
									style="margin-right: 2vw"
									v-show="multipleLights.length > 1"
									:disabled="mulIndex == 0"
									@click="openLight('pre')"
									>上一个</el-button
								>
								<el-button
									type="text"
									style="margin-right: 8vw"
									v-show="multipleLights.length > 1"
									:disabled="mulIndex == multipleLights.length - 1"
									@click="openLight('next')"
									>下一个</el-button
								>
								<el-button
									type="text"
									@click="saveThreeClight"
									v-show="lightEdit"
									style="
										width: 100px;
										margin-right: 1.5vw;
										background-color: #28d094 !important;
										color: #fff !important;
										border-color: #dcdfe6 !important;
									"
									>保存</el-button
								>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 放大镜查询 -->
		<el-popover v-model="searchPopver" ref="thSearch" placement="bottom-end" width="400" :visible-arrow="false">
			<!-- 放大镜搜索表单 -->
			<div class="searchPop p0 fs-12">
				<div class="searchPop-header">
					<span>查找条件</span>
					<i class="searchPop-header-close el-icon-close" @click.stop="searchPopver = false"></i>
				</div>
				<el-form :model="searchForm" label-width="80px" label-position="left" class="W100 pt10 pr20">
					<el-form-item label="三色灯编号" prop="productName">
						<el-input placeholder="请输入三色灯编号" v-model="searchForm.sim" size="mini" @input="queryTableData(1)"></el-input>
					</el-form-item>
					<el-form-item label="产品" prop="productName">
						<el-input placeholder="请输入产品" v-model="searchForm.productName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="芯片" prop="chipName">
						<el-input placeholder="请输入芯片" v-model="searchForm.chipName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="固件版本" prop="firmwareName">
						<el-input placeholder="请输入固件版本" v-model="searchForm.firmwareName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="运营商" prop="operator">
						<el-input placeholder="请输入运营商" v-model="searchForm.operator" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="绑定团队" prop="bindingTeamName">
						<el-input placeholder="请输入绑定团队" v-model="searchForm.bindingTeamName" size="mini"></el-input>
					</el-form-item>
				</el-form>
				<div class="mt20 border-top text-right">
					<el-button type="text" class="color-666" @click.stop="queryTableData(1)">确定</el-button>
				</div>
			</div>
		</el-popover>
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData, setCookie, getCookie } from '@/util/tool'; //按需引入常用工具函数
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';
import { mapGetters } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
import PrintLightLabel from '@/pages/threeColorLightManagement/PrintLightLabel'; //打印标签
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import QRCode from 'qrcode';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		PrintLightLabel,
	},
	name: 'lightAutoRegistData',
	data() {
		return {
			activeTab: 'lightAutoRegistData',

			// 表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '三色灯编号', colNo: 'sim', align: 'left', width: '167' },
				{ colName: '产品', colNo: 'productName', align: 'left', width: '140' },
				{ colName: '创建时间', colNo: 'createTime', align: 'left', width: '135' },
				{ colName: '固件版本', colNo: 'firmwareName', align: 'left', width: '' },
				{ colName: '芯片', colNo: 'chipName', align: 'left', width: '' },
				// { colName: '是否贴标签', colNo: 'checkLable', align: 'center', width: '90' },
				{ colName: '打印次数', colNo: 'printSize', align: 'right', width: '90' },
				{ colName: 'MAC', colNo: 'mac', align: 'left', width: '135' },
				{ colName: '本地时间', colNo: 'simTime', align: 'left', width: '135' },
				{ colName: '最后心跳', colNo: 'updateTime', align: 'left', width: '135' },
				{ colName: '在线状态', colNo: 'onLine', align: 'center', width: '80' },
				{ colName: '离线小时', colNo: 'offlineDuration', align: 'right', width: '90' },
				// { colName: '出厂检查', colNo: 'lightType', align: 'left', width: '80' },
				// { colName: '绑定团队', colNo: 'bindingTeamName', align: 'left', width: '' },
			],
			searchForm: {
				chipName: '',
				firmwareName: '',
				bindingTeamName: '', //绑定团队

				operator: '',
				productName: '',
				sim: '',
				onLine: ['0', '1'],
				autoRegister: 1,
				printSize: '',
				checkLable: 0,
				classCategory: '4G',
			},
			selectionData: [],

			onlineStatusMap: {
				false: '离线',
				true: '在线',
			},
			onlineColorMap: {
				'001': '#28D094',
				'010': '#F7A944',
				100: '#DC143C',
			},
			lightTypeMap: {
				0: '未测试',
				1: '测试未通过',
				2: '待出库',
				3: '已经发货',
				4: '退货',
				5: '报废',
			},
			updateStatusMap: {
				0: '待升级',
				1: '升级中',
				2: '升级失败',
				3: '升级成功',
			},
			searchPopver: false,
			lightInfos: {
				qrCode: '',
				sim: '',
				greenInterval: 0,
				yellowInterval: 0,
				redInterval: 0,
				offlineReset: 0,
				debounceTime: 0,
				heartInterval: '',
				pulseInterval: '',
				signalStrength: '',
				macUrl: '',
				messageVersion: '',
				updated: 1,
				//物联网平台设置
				mqttType: 0,
				countInterval: '',
				countZeroReport: 0, //计数为0是否上传
				mqttUrl: '',
				mqttUser: '',
				mqttPawd: '',
				locaProductKey: '', //产品所属ProductKey
				locaDeviceName: '', //设备在产品内唯一标识
				locaDeviceSecret: '', //物联网平台为设备颁发的设备密钥
			},
			multipleLights: [],
			mulIndex: '',
			lightEdit: false,

			mqttTypeMap: {
				0: '阿里云物联网平台',
				1: '本地MQTT服务',
				2: '3A精益华为云',
			},
			noDataList: [
				{
					id: 0,
					label: '上传',
				},
				{
					id: 1,
					label: '不上传',
				},
			],
			noDataMap: {
				0: '上传',
				1: '不上传',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.tableColumnCopy = deepClone(this.tableColumn);
		this.tableColumn = getColumn(this.tableColumn, this.$options.name);
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		//切换tab页
		changeTab(tab) {
			if (tab.name == 'lightAutoRegistData') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},

		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			this.$axios
				.getTricolourLightList(JSON.stringify(JSON.stringify(this.searchForm)))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('getTricolourLightList |' + error);
				});
		}),
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 展示字段
		updateColumn(data) {
			this.tableColumn = updateColumn(this.$options.name, data);
			this.sortChange(this.tableSort, true);
		},
		// 列宽拖动
		headerDragend(newWidth, oldWidth, column) {
			updateColumnWidth(this.$options.name, column.property, newWidth, this.tableColumn);
		},
		toggleAside(value, type) {
			const workDiv = document.getElementById('lightMainToggle');
			const outDiv = document.getElementById('lightAutoRegistData');
			if (workDiv) {
				let newValue = 0;
				if (value) {
					newValue = 0;
					outDiv.style.height = 'auto';
					outDiv.style.overflow = 'initial';
				} else {
					newValue = '110%';
					outDiv.style.height = '100%';
					outDiv.style.overflow = 'hidden';
				}
				workDiv.style.left = newValue;
			}
		},
		//日期format
		dateFormat: dateFormat,

		editLightAll() {
			this.multipleLights = [];
			if (this.selectionData && this.selectionData.length > 0) {
				this.selectionData.forEach(item => {
					this.multipleLights.push(item.sim);
				});
				this.mulIndex = 0;
				this.openLightDetail(this.multipleLights[this.mulIndex]);
			} else {
				this.$message.warning('请勾选三色灯后再修改参数');
			}
		},
		cleartLight() {
			this.toggleAside(0);
			this.lightInfos.sim = '';
			this.lightInfos.qrCode = '';
			this.lightInfos.greenInterval = 0;
			this.lightInfos.yellowInterval = 0;
			this.lightInfos.redInterval = 0;
			this.lightInfos.offlineReset = 0;
			this.lightInfos.debounceTime = 0;
			this.lightInfos.heartInterval = 0;
			this.lightInfos.pulseInterval = 0;
			this.lightInfos.signalStrength = '';
			this.lightInfos.macUrl = '';
			this.lightInfos.messageVersion = '';
			this.lightInfos.updated = 1;
			this.lightInfos.countInterval = '';
			this.lightInfos.countZeroReport = 0;
			this.lightInfos.mqttUrl = '';
			this.lightInfos.mqttUser = '';
			this.lightInfos.mqttPawd = '';
			this.lightInfos.mqttType = 0;
			this.lightInfos.locaProductKey = '';
			this.lightInfos.locaDeviceName = '';
			this.lightInfos.locaDeviceSecret = '';
			this.mulIndex = 0;
			this.multipleLights = [];
			this.$refs.uTableRef.clearSelection();
		},
		openLightDetail(id, flag, edit) {
			if (!id) {
				this.$message.warning('数据异常');
				return;
			}
			const str = JSON.stringify({ sim: id });
			this.$axios
				.selectTricolourLightParamBySim(str)
				.then(res => {
					if (res.data.success) {
						this.lightInfos.sim = id;
						this.lightInfos.qrCode = res.data.data.simQRCode.replace(/\\/, '');
						this.lightInfos.greenInterval = res.data.data.green;
						this.lightInfos.yellowInterval = res.data.data.yellow;
						this.lightInfos.redInterval = res.data.data.red;
						this.lightInfos.offlineReset = res.data.data.restartCount;
						this.lightInfos.debounceTime = res.data.data.debounce;
						this.lightInfos.heartInterval = res.data.data.heartbeat;
						this.lightInfos.pulseInterval = res.data.data.circleduration;
						this.lightInfos.signalStrength = res.data.data.signal;
						this.lightInfos.macUrl = res.data.data.mac;
						this.lightInfos.messageVersion = res.data.data.version;
						this.lightInfos.updated = res.data.data.updated;
						this.lightInfos.mqttType = res.data.data.mqttType || 0;
						this.lightInfos.countInterval = res.data.data.countInterval;
						this.lightInfos.countZeroReport = res.data.data.countZeroReport || 0;
						this.lightInfos.mqttUrl = res.data.data.mqttUrl;
						this.lightInfos.mqttUser = res.data.data.mqttUser;
						this.lightInfos.mqttPawd = res.data.data.mqttPawd;
						this.lightInfos.locaProductKey = res.data.data.locaProductKey;
						this.lightInfos.locaDeviceName = res.data.data.locaDeviceName;
						this.lightInfos.locaDeviceSecret = res.data.data.locaDeviceSecret;
						if (!flag) {
							this.toggleAside(1, 'threeClight');
							setTimeout(() => {
								this.getQrcode(this.lightInfos.qrCode);
							}, 500);
						} else {
							this.getQrcode(this.lightInfos.qrCode);
						}
						if (!edit) {
							this.lightEdit = true;
						} else {
							this.lightEdit = false;
						}
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTricolourLightParamBySim |' + error);
				});
		},
		saveThreeClight() {
			let str,
				sim = this.lightInfos.sim;
			const green = this.lightInfos.greenInterval + '',
				yellow = this.lightInfos.yellowInterval + '',
				red = this.lightInfos.redInterval + '';
			if (!sim) {
				return;
			}
			if (green.indexOf('.') > -1) {
				this.$message.warning('绿灯闪烁间隔不能包含小数');
				return;
			}
			if (yellow.indexOf('.') > -1) {
				this.$message.warning('黄灯闪烁间隔不能包含小数');
				return;
			}
			if (red.indexOf('.') > -1) {
				this.$message.warning('红灯闪烁间隔不能包含小数');
				return;
			}
			str = JSON.stringify({
				circleduration: this.lightInfos.pulseInterval,
				debounce: this.lightInfos.debounceTime,
				green: this.lightInfos.greenInterval,
				heartbeat: this.lightInfos.heartInterval,
				red: this.lightInfos.redInterval,
				restartCount: this.lightInfos.offlineReset,
				sim: sim,
				yellow: this.lightInfos.yellowInterval,
				mqttType: this.lightInfos.mqttType,
				mqttUrl: this.lightInfos.mqttUrl,
				mqttUser: this.lightInfos.mqttUser,
				mqttPawd: this.lightInfos.mqttPawd,
				locaProductKey: this.lightInfos.locaProductKey,
				locaDeviceName: this.lightInfos.locaDeviceName,
				locaDeviceSecret: this.lightInfos.locaDeviceSecret,
				countZeroReport: this.lightInfos.countZeroReport,
				countInterval: this.lightInfos.countInterval,
			});
			this.$axios
				.updateTricolourLightParam(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.lightInfos.updated = 4;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('updateTricolourLightParam |' + error);
				});
		},
		openLight(type) {
			let sim;
			if (type == 'pre') {
				this.mulIndex -= 1;
			} else if (type == 'next') {
				this.mulIndex += 1;
			}
			sim = this.multipleLights[this.mulIndex];
			this.openLightDetail(sim, true);
		},
		getQrcode(str) {
			const canvas = document.getElementById('qrcodeLight');
			QRCode.toCanvas(canvas, str, error => {
				if (error) {
					console.log('二维码异常', error);
				}
			});
		},
		getUpdateStr(status) {
			//刷新字符串
			let str = '',
				nowTime = new Date();
			if (!status) {
				return;
			}
			switch (status) {
				case 1:
					str = '';
					break;
				case 2:
					str = '等待参数同步结果,请检查三色灯是否在线，稍后“刷新”查看同步结果';
					break;
				case 3:
					str = '参数已同步';
					break;
				case 4:
					str = this.$moment(nowTime).format('MM/DD HH:mm:ss') + '同步参数...  稍后点击“刷新”查看同步结果';
					break;
				default:
					str = '';
			}
			return str;
		},
		//打印三色灯
		printLightLabel(labelList) {
			this.$refs.PrintLightLabelRef.printData(labelList);
		},
		//批量打印
		printAll() {
			if (this.selectionData && this.selectionData.length > 0) {
				const rowIds = [];
				const printList = this.selectionData.map(item => {
					rowIds.push(item.tlId);
					return {
						sim: item.sim,
						mac: item.mac,
						pro: item.productName,
					};
				});
				this.printLightLabel(printList);
				this.countPrints(rowIds);
			} else {
				this.$message.warning('请勾选三色灯后再批量打印');
			}
		},
		countPrints(tlids) {
			const API = 'printTricolourLight';
			this.$axios[API](JSON.stringify({ tlids }))
				.then(res => {
					if (res.data.success) {
						this.queryTableData();
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
		// 批量删除三色灯
		deleteAll() {
			const simList = [],
				tlIdList = [];
			if (this.selectionData && this.selectionData.length > 0) {
				this.$confirm('勾选的三色灯将被删除', '删除三色灯', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.selectionData.forEach(item => {
							simList.push(item.sim);
							tlIdList.push(item.tlId);
						});
						this.$axios
							.batchDeleteTricolourLight(
								JSON.stringify({
									simList: simList,
									tlIdList: tlIdList,
								}),
							)
							.then(res => {
								if (res.data.success) {
									this.$succ('操作成功!');
									this.queryTableData(1);
								} else {
									this.$err(res.data.message);
								}
							})
							.catch(error => {
								console.log('batchDeleteTricolourLight |' + error);
							});
					})
					.catch(() => {
						this.$message.info('已取消');
					});
			} else {
				this.$message.warning('请勾选三色灯后再删除');
			}
		},
	},
};
</script>

<style lang="scss">
#lightAutoRegistData {
	width: 100%;
	overflow: hidden;
	position: relative;

	#lightMainToggle {
		position: absolute;
		width: 100%;
		min-height: 100%;
		top: 0;
		left: 110%;
		z-index: 66;
		transition: left 0.3s linear;
		background: #f2f2f2;
		// overflow-y:overlay;
		overflow-x: hidden;

		.p_row {
			padding: 0;
			display: flex;
			align-items: center;

			.p_label {
				width: 8px;
				height: 16px;
				display: inline-block;
				background-color: #23b781;
			}

			.p_text {
				margin-left: 0.3vw;
				font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
				font-weight: 650;
				color: #666666;
			}
		}

		.p_nor {
			padding: 0 0.5vw;
			margin: 0.2vh 0;
			display: flex;
			align-items: center;

			span {
				color: #999;
				font-size: 12px;
			}

			pre {
				color: #999;
				font-family: 'Microsoft YaHei';
				font-size: 12px;
				margin: 0;
				margin-left: 0.5vw;
				display: inline-block;
			}
		}

		.workCtable {
			width: 99%;
			border-left: 1px solid #e9e9e9;
			border-bottom: 1px solid #e9e9e9;

			tr {
				border-color: #e9e9e9;

				th {
					text-align: left;
					font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
					font-weight: 650;
					font-style: normal;
					font-size: 14px;
					color: #666666;
					padding: 15px 0 15px 5px;
					background: #f5f5f5;
					border-right: 1px solid #e9e9e9;
					border-top: 1px solid #e9e9e9;
					word-wrap: break-word;
				}

				td {
					border-right: 1px solid #e9e9e9;
					border-top: 1px solid #e9e9e9;
					font-size: 12px;
					height: 28px;

					.norSpan {
						font-size: 14px;
						margin-left: 0.2vw;
						color: #666;
					}
				}
			}
		}

		.bot_left {
			font-size: 13px;
			margin-top: 1.5vh;
			display: flex;
			align-items: center;
			color: #ec808d;
		}
	}
}
</style>
