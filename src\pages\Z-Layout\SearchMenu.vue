<template>
	<div :class="{ show: show }" class="search-menu flex-align-center">
		<i class="el-icon-search pointer fs-24 color-666" @click.stop="click" />
		<el-cascader
			ref="SearchMenuCascader"
			class="search-menu-select"
			popper-class="SearchMenu"
			v-model="search"
			key="rcid"
			:options="newMenuList"
			:props="{ value: 'url', label: 'resourceName', children: 'adminResourceVOS', expandTrigger: 'hover' }"
			:show-all-levels="false"
			@change="change"
			@visible-change="removeCascaderAriaOwns"
			@expand-change="removeCascaderAriaOwns"
			size="small"
			placeholder="搜索菜单"
			clearable
			filterable
		>
			<template slot-scope="{ data }">
				<span :class="data.icon" class="icon-third mr5"></span>
				<span>{{ data.resourceName }}</span>
				<span v-if="data.adminResourceVOS"> ({{ data.adminResourceVOS.length }}) </span>
			</template>
		</el-cascader>
	</div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
	name: 'SearchMenu',
	data() {
		return {
			search: [],
			show: false,
		};
	},
	computed: {
		...mapGetters(['userInfos', 'menuList']),
		// 菜单列表
		newMenuList() {
			const newMenuList = [...this.menuList]; // 使用扩展运算符避免直接修改原数组
			if (newMenuList[0] && newMenuList[0].resourceName === '首页') {
				newMenuList[0].url = 'welCome';
			}
			return newMenuList;
		},
	},
	watch: {
		// 监听 show 状态
		show(value) {
			if (value) {
				document.body.addEventListener('click', this.close);
			} else {
				document.body.removeEventListener('click', this.close);
			}
		},
	},
	mounted() {},
	beforeDestroy() {
		document.body.removeEventListener('click', this.close); // 确保在组件销毁时移除事件监听
	},
	methods: {
		// 点击搜索菜单
		click() {
			this.show = !this.show; // 切换 show 状态
			if (this.show && this.$refs.SearchMenuCascader) {
				setTimeout(() => {
					this.$refs.SearchMenuCascader.handleInput();
					this.$refs.SearchMenuCascader.handleFocus();
				}, 250);
			}
		},
		// 关闭搜索菜单
		close(event) {
			// 检查点击的目标是否在搜索菜单内
			if (!this.$el.contains(event.target)) {
				this.$refs.SearchMenuCascader?.handleClear();
				this.search = [];
				this.show = false; // 关闭菜单
			}
		},
		// 选择菜单
		change(val) {
			const path = val[val.length - 1] || '';
			const queryId = this.userInfos?.adminUserVO.phoneNo || '';

			if (!path) {
				return '';
				// return this.$message.warning('未找到对应菜单，请重新搜索/刷新浏览器重试！');
			}
			if (!queryId) {
				return this.$message.warning('未找到用户信息，请重新登录/刷新浏览器重试！');
			}

			this.$router.replace({ path, query: { queryId } }); // 跳转路由
			this.$nextTick(() => {
				this.show = false;
			});
		},
		// 移除 cascader aria-owns
		removeCascaderAriaOwns() {
			this.$nextTick(() => {
				const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]');
				Array.from($el).map(item => item.removeAttribute('aria-owns'));
			});
		},
	},
};
</script>

<style lang="scss">
.search-menu {
	font-size: 0 !important;

	.search-icon {
		cursor: pointer;
		font-size: 18px;
		vertical-align: middle;
	}

	.search-menu-select {
		font-size: 18px;
		transition: width 0.2s;
		width: 0;
		overflow: hidden;
		background: transparent;
		border-radius: 0;
		display: inline-block;
		vertical-align: middle;

		.el-input__inner {
			color: #1e9d6f;
			border-radius: 0;
			border: 0;
			padding-left: 0;
			padding-right: 0;
			box-shadow: none !important;
			border-bottom: 1px solid #d9d9d9;
			vertical-align: middle;
		}
	}

	&.show {
		.search-menu-select {
			width: 230px;
			margin-left: 10px;
		}
	}
}
</style>

<style lang="scss">
.SearchMenu {
	.el-cascader-menu__wrap {
		height: 50vh;
		min-height: 300px;
	}
	.el-cascader-node {
		padding: 0 30px 0 0px;
		.el-cascader-node__prefix {
			display: none;
		}
	}
}
</style>
