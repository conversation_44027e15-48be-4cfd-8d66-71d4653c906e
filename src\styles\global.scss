/* 

	全局报表页面统一风格 大部分报表遵循该风格 
  这里的放的样式与common.scss不同的是这里一般在固定场景用且嵌套了多层className等

	折叠所有区域代码的快捷键：ctrl+k, ctrl+0;
	展开所有折叠区域代码的快捷键：ctrl +k, ctrl+J;		
*/
@import './element-variables.scss';

/* 搜索输入框样式（椭圆灰色） */
.searchBox {
	width: 180px !important;
	// margin: 0 0 0 10px;
	.el-input__inner {
		border-radius: 15px;
		background-color: #f7f7f7;
		outline: none;
		border-color: #dcdfe6;
		-webkit-appearance: none;
		background-image: none;
		border: 1px solid #dcdfe6;
		font-size: 13px;

		transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

		&:hover,
		&:focus,
		&:active {
			color: $--color-primary;
			border-color: $--color-primary;
			caret-color: $--color-primary; /* 修改光标颜色 */
		}
	}

	input {
		color: #23b781;
		&::-webkit-input-placeholder {
			color: #8d8d8d;
		}
		&::-moz-placeholder {
			/* Mozilla Firefox 19+ */
			color: #8d8d8d;
		}
		&:-moz-placeholder {
			/* Mozilla Firefox 4 to 18 */
			color: #8d8d8d;
		}
		&:-ms-input-placeholder {
			/* Internet Explorer 10-11 */
			color: #8d8d8d;
		}
	}
}

/* 报表内容 */
.table-wrapper {
	/* 
     统一表格样式 使用clss="table-wrapper"包裹表格主体（u-table）并设置clss="table-main" 
  	*/
	.table-main {
		width: 100%;
		min-height: 200px;
		height: calc(100vh - 300px) !important; //修改这里height以调整表格高度
		display: flex;
		flex-direction: column;
		// el-table 样式
		.el-table {
			flex: 1; //表格自适应
			// 按钮样式
			.el-button {
				padding: 0 5px;
				&:last-child {
					padding-right: 0;
				}
			}
			.el-button--text {
				padding: 0;
			}
		}
		// elx-table 样式(边框去除)
		.elx-table {
			.elx-table--header,
			.elx-header--column {
				font-size: 12px;
				color: #606266;
				font-weight: 400;

				border-color: transparent;
				background-image: none;
				background: #fff;
			}
			// 边框线
			.elx-body--column,
			.elx-table--border-line {
				border-color: transparent;
				background-image: none;
			}
			// 拖拽线
			.elx-header--column .elx-resizable.is--line:before {
				background-color: transparent;
			}
		}

		// 表头标题
		th {
			background-color: #fff;
			vertical-align: top;
			font-size: 12px;
			color: #606266;
			font-weight: 400;
			border-bottom: 1px solid #e9e9e9 !important;
			padding: 10px 0 6px 0;
			padding: 10px 0;
			> .cell.highlight {
				color: $--color-primary;
			}
		}

		// 每列样式
		td {
			height: 45px !important;
			padding: 10px 0;
			font-size: 12px;
			border-color: transparent;
			// 单元格样式
			.cell {
				overflow: hidden;
				vertical-align: top;
			}
		}
		// 选择多行数据时高亮提示 如果是使用el-table可以用 highlight-selection-row 代替
		.selectTr td {
			// background: #c8e6c9 !important;
			background: #e9f5f1 !important;
		}
		// 分页 padding 设置
		.myPagination {
			padding-top: 5px;
		}

		// 表格多选框
		.el-table-column--selection .cell {
			padding-left: 8px;
			padding-right: 0px;
		}
		.el-table-column--selection .umy-table-beyond {
			padding-right: 0px;
		}

		// 表头样式
		.el-table__header-wrapper {
			/* 表格右对齐时 */
			.is-right:not(:last-child) {
				padding: 10px 10px 10px 0 !important;
			}
			// .is-leaf:not(:last-child) {
			//   padding: 10px 0 !important;
			//   padding: 10px 0 0 0 !important;
			// }
		}

		// expand 展开行样式
		.el-table__expanded-cell {
			padding: 0;
		}

		// 斑马纹颜色
		.el-table--striped .el-table__body tr.el-table__row--striped td {
			background: #f2f2f2;
		}

		// 表尾/表底的背景颜色
		.el-table__footer-wrapper,
		.el-table__fixed-footer-wrapper {
			td {
				background: transparent;
			}
		}

		// 鼠标移入高光颜色
		.el-table__body tr.hover-row td {
			background-color: #e9f5f1 !important;
		}
		// 鼠标点击高光颜色
		.el-table__body tr.current-row td {
			background-color: #e9f5f1 !important;
		}

		// 表尾/表底的背景颜色
		.el-table__footer-wrapper tbody td {
			background: transparent;
		}

		/* border样式消除,列宽拖动覆盖 */
		.el-table--border td,
		.el-table--border th,
		.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
			border-color: transparent;
		}
		.el-table--border,
		.el-table--group {
			border-color: transparent;
			&::after {
				background-color: transparent;
			}
		}

		// fxied固定列 样式
		.el-table__fixed-right::before,
		.el-table__fixed::before {
			background-color: transparent;
		}
		// 固定列时解决行未对齐
		.el-table__fixed-body-wrapper .el-table__body {
			padding-bottom: 16px;
		}

		// 解决el-table中同时使用expand和fixed时造成的显示错位冲突
		.el-table__fixed,
		.el-table__fixed-right {
			.el-table__expanded-cell {
				visibility: hidden;
				padding: 0;
				.el-table__body-wrapper {
					overflow: hidden;
				}
			}
		}

		// 展开行样式
		.el-table__body-wrapper {
			.el-table__expanded-cell {
				z-index: 100;
				padding: 0;
			}
		}

		// 排序按钮样式
		.el-table .caret-wrapper {
			// visibility: hidden;
			width: auto;
			max-width: 16px;
			min-width: 10px;
			height: 20px;
			vertical-align: top;
			.descending {
				top: 12px;
			}
			.ascending {
				top: 0px;
			}
			i {
				padding: 0;
			}
		}
		.el-table .sort-caret {
			left: 3px;
		}

		// 排序图标显示
		// .el-table .descending,
		// .el-table .ascending {
		// 	.caret-wrapper {
		// 		visibility: visible;
		// 	}
		// }

		// 列拖拽样式隐藏
		.el-table-column--selection {
			.pltableDragIcon {
				display: none !important;
			}
		}
		.el-table__expand-column {
			.pltableDragIcon {
				display: none !important;
			}
		}
	}

	// 表格标题灰色背景
	.th-bg-gray {
		th {
			padding: 10px 0 !important;
			background-color: #e9e9e9 !important;
		}
	}
	// 表格标题透明背景
	.th-bg-none {
		th {
			padding: 10px 0 !important;
			background-color: transparent !important;
		}
	}
}

/*  放大镜搜索框样式 */
.searchPop {
	&-header {
		position: relative;
		border-bottom: 1px solid #d7d7d7;
		padding: 1vh 0.5vw 1.5vh 0.2vw;
		&-close {
			position: absolute;
			top: 0.2vh;
			right: 0.2vw;
			font-size: 16px;
			font-weight: 700;
			cursor: pointer;
		}
	}
	.el-form .el-form-item {
		&:last-child {
			margin-bottom: 0px;
		}
		.el-form-item__content {
			height: 28px;
		}
		.el-form-item__label {
			font-size: 12px;
			padding: 0;
		}
		.el-checkbox {
			margin-right: 3px;
			.el-checkbox__label {
				font-size: 12px;
				font-weight: 400;
			}
			.el-checkbox__inner {
				box-sizing: border-box;
				width: 20px;
				height: 18px;
				border: 2px solid #d7d7d7;
			}
			.el-checkbox__inner::after {
				border-width: 2px;
				height: 8px;
				left: 6px;
			}
		}
		.el-checkbox .is-checked {
			.el-checkbox__inner {
				background-color: $--color-primary-light;
			}
		}
		.el-checkbox__input.is-checked + .el-checkbox__label {
			color: #555;
		}
	}
	.el-radio {
		font-weight: 400;
		margin-right: 3px;
		.el-radio__inner {
			width: 18px;
			height: 18px;
		}
		.el-radio__label {
			padding-left: 5px;
			font-size: 12px;
		}
	}
}

/* 明细滑窗样式 */
.moveToggle {
	width: 100%;
	height: 100%;

	position: absolute !important;
	opacity: 1;
	transition: all 0.3s ease-in-out;
	transform: translateX(0);

	z-index: 888;
	background: #f2f2f2;
	color: #606266;
	font-size: 14px;
	.detail {
		&-wrapper {
			border-radius: 15px 15px 0 0;
			width: 100%;
			height: 100%;
			border: 1px solid #e9e9e9;
			background: #f9f9f9;
			border-bottom: 1px solid transparent;
			box-sizing: border-box;
			overflow-y: auto;
		}

		&-head {
			padding: 10px;
			height: 50px;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
		&-title {
			font-size: 16px;
			font-weight: 650;
			color: #555;
		}

		&-content {
			background: #fff;
			border-top: 1px solid #f2f2f2;
			padding: 0 2vw;
			// min-height: 80vh;
			height: calc(100% - 50px);
			box-sizing: border-box;
			padding: 5px 20px;
			overflow-y: scroll;

			// 保存按钮
			.bottom-button {
				text-align: end;
				margin-top: 2vh;
				// width: 100%;
				width: inherit;
				.el-button {
					min-width: 100px;
					position: sticky;
					right: 0;
				}
			}
		}
	}

	// 绿色块标题
	.detail-content-title {
		padding: 0;
		margin: 15px 0;
		display: flex;
		align-items: center;
		font-weight: 600;
		color: #555;
		font-size: 16px;
		&::before {
			content: '';
			width: 8px;
			height: 16px;
			display: inline-block;
			background-color: #23b781;
			margin-right: 0.3vw;
		}
	}
	// 操作日志
	.detail-log {
		width: 100%;

		// max-height: 500px;
		// overflow-y: scroll;
		.detail-log-item {
			padding: 0 0.5vw;
			margin: 0.2vh 0;
			display: flex;
			align-items: flex-start;

			span {
				color: #999;
				font-size: 12px;
			}

			pre {
				color: #999;
				font-size: 12px;
				font-family: 'Microsoft YaHei';
				margin: 0;
				margin-left: 0.5vw;
				display: inline-block;
			}
		}
	}
	// 基本信息
	.base-table {
		width: 100%;
		border-left: 1px solid #e9e9e9;
		border-bottom: 1px solid #e9e9e9;

		tr {
			border-color: #e9e9e9;

			th {
				min-width: 120px;
				padding: 15px 20px;
				text-align: left;
				font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
				font-weight: 650;
				font-style: normal;
				font-size: 14px;
				color: #666666;
				background: #f5f5f5;
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				
			}

			td {
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				font-size: 14px;
				color: #666;
				height: 48px;
				padding: 5px;
				// .el-input__inner {
				//   border: none;
				// }

				> span {
					font-size: 14px;
					padding: 0 10px;
					color: #666;
					display: inline-block;
					word-break: break-all;
				}

				&-light {
					&:hover {
						text-decoration: underline;
						color: #23b781;
						cursor: pointer;
					}
				}

				&-number {
					// width: 150px;
				}
				// 输入框
				.el-input {
					width: 100%;
				}
				// 下拉框
				.el-select {
					width: 100%;
				}
				// 单选框
				.el-radio-group {
					padding: 0 10px;
				}
				.el-radio {
					margin-right: 10px;
					.el-radio__label {
						font-size: 14px;
					}
				}
			}
		}
	}
	// 其他表格
	.detail-table {
		th {
			background-color: #d7d7d7;
			padding: 15px 0;
			font-weight: 650;
			font-style: normal;
			font-size: 14px;
			color: #666;
		}
		td {
			height: 45px !important;
		}
		.el-table__virtual-wrapper {
			height: 600 !important;
		}
		.el-table__row {
			td:first-child {
				border-left: 1px solid #ebeef5;
			}
		}

		// .el-input__inner {
		//   border: 1px solid transparent;
		// }

		// .el-textarea__inner {
		//   border: 1px solid transparent;
		// }

		.input_textR {
			.el-input__inner {
				border: 1px solid transparent;
				text-align: end;
			}
		}
	}
}
.moveToggle-hide {
	// left: 110%; //隐藏在右侧
	opacity: 0;
	transform: translateX(110%);
	visibility: hidden; // 隐藏元素，避免连续按TAB键时聚焦导致的页面偏移
}
/* 带绿色块的标题 */
.label-title {
	display: flex;
	align-items: center;
	color: #606266;
	&::before {
		content: ' ';
		display: inline-block;
		width: 8px;
		height: 16px;
		display: inline-block;
		background-color: #23b781;
		margin-right: 5px;
	}
}

// 输入框文本绿色高光
.input-green {
	.el-input__inner {
		color: #23b781;
	}
}
// 输入框文字右对齐
.input-right {
	.el-input__inner {
		text-align: end;
	}
}
// 输入框去掉边框
.input-border-none {
	.el-input__inner,
	.el-textarea__inner {
		border: none;
	}
}
.input-border-red {
	.el-input__inner,
	.el-textarea__inner {
		border: 1px solid #f56c6c !important;
	}
}

/* 开启umyUI字段设置时的样式 */
.modal-backdrop {
	display: none !important; //隐藏遮罩
}
.plTableBox .plDialog .table-cus-header {
	color: #23b781;
	font-size: 14px;
	height: 45px;
	line-height: 45px;
	border-bottom: none;
}
.plTableBox .plDialog .checkListBox {
	height: calc(100% - 80px);
	ul {
		font-size: 12px;
		padding-left: 10px;
		margin: 0;
		li {
			display: flex;
			align-items: center;
			padding: 0 10px;
			height: 25px;
			i:first-of-type {
				margin-left: auto;
			}
			.el-checkbox {
				display: flex;
				align-items: center;
			}
			.el-checkbox__label {
				font-size: 12px;
			}
		}
	}
}
.plTableBox .plDialog .table-cus-footer {
	height: 30px;
	line-height: 30px;
	padding-left: 20px;
	.el-button {
		padding: 6px 15px;
		font-size: 12px;
	}
}

/* -----------------以下非通用（复制时可去除）------------------  */

/*  看板时间选择样式 */
.dataView {
	.el-input__prefix {
		display: none;
	}

	.el-input__icon {
		display: none;
	}

	.el-input__inner {
		background: none;
		border: none;
		color: #fff;
		cursor: pointer;
		padding: 0;
		text-align: end;
		width: 90%;
	}
	.header-button {
		position: absolute;
		right: 0;
		top: -3px;
		// top: 10px;
		button {
			position: relative;
			padding: 0px 5px;
			cursor: pointer;
			border-radius: 7px;
			border: 1px solid rgb(61, 106, 255);
			font-size: 14px;
			text-transform: uppercase;
			font-weight: 400;
			letter-spacing: 2px;
			background: transparent;
			color: #fff;
			overflow: hidden;
			box-shadow: 0 0 0 0 transparent;
			-webkit-transition: all 0.2s ease-in;
			-moz-transition: all 0.2s ease-in;
			transition: all 0.2s ease-in;
		}

		button:hover {
			background: rgb(61, 106, 255);
			box-shadow: 0 0 30px 5px rgba(0, 142, 236, 0.815);
			-webkit-transition: all 0.2s ease-out;
			-moz-transition: all 0.2s ease-out;
			transition: all 0.2s ease-out;
		}

		button:hover::before {
			-webkit-animation: sh02 0.5s 0s linear;
			-moz-animation: sh02 0.5s 0s linear;
			animation: sh02 0.5s 0s linear;
		}

		button::before {
			content: '';
			display: block;
			width: 0px;
			height: 86%;
			position: absolute;
			top: 7%;
			left: 0%;
			opacity: 0;
			background: #fff;
			box-shadow: 0 0 50px 30px #fff;
			-webkit-transform: skewX(-20deg);
			-moz-transform: skewX(-20deg);
			-ms-transform: skewX(-20deg);
			-o-transform: skewX(-20deg);
			transform: skewX(-20deg);
		}

		@keyframes sh02 {
			from {
				opacity: 0;
				left: 0%;
			}

			50% {
				opacity: 1;
			}

			to {
				opacity: 0;
				left: 100%;
			}
		}

		button:active {
			box-shadow: 0 0 0 0 transparent;
			-webkit-transition: box-shadow 0.2s ease-in;
			-moz-transition: box-shadow 0.2s ease-in;
			transition: box-shadow 0.2s ease-in;
		}
	}
}
.view-date-picker {
	background: rgba($color: #0761a5, $alpha: 0.8) !important;

	.el-year-table td .cell {
		color: #fff !important;
		font-size: 16px !important;
	}

	.el-date-picker__header-label {
		color: #fff !important;
		font-weight: 600 !important;
	}

	.el-picker-panel__icon-btn {
		color: #fff !important;
	}

	td.current:not(.disabled) .cell {
		color: $--color-primary-lighter !important;
	}
}

/*  明细滑窗样式（暂时保留 可能会弃用） */
.detail-container {
	// 头部：大标题、返回按钮等
	.detail-header {
		border-radius: 15px 15px 0 0;
		width: 100%;
		border: 1px solid #e9e9e9;
		background: #f9f9f9;
		border-bottom: 1px solid transparent;
		box-sizing: border-box;
		padding: 5px 20px 5px 20px;
		&-title {
			color: #555;
			font-size: 18px;
			font-weight: 500;
		}
	}
	// 主体：小标题、表格等
	.detail-main {
		background: #fff;
		border: 1px solid #e9e9e9;
		box-sizing: border-box;
		height: calc(100% - 50px);
		overflow-y: overlay;
		padding: 0 30px;

		// 每子项
		.detail-item {
			padding: 0;
			display: flex;
			align-items: center;

			.detail-item-label {
				width: 8px;
				height: 16px;
				display: inline-block;
				background-color: $--color-primary-light;
			}

			.detail-item-title {
				margin-left: 0.3vw;
				font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
				font-weight: 650;
				color: #666666;
			}
		}
		// 表格表单
		.detail-table {
			width: 100%;
			border-left: 1px solid #e9e9e9;
			border-bottom: 1px solid #e9e9e9;
			tr {
				border-color: #e9e9e9;
				th {
					text-align: left;
					font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
					font-weight: 650;
					font-style: normal;
					font-size: 14px;
					color: #666666;
					padding: 15px 0 15px 5px;
					background: #f5f5f5;
					border-right: 1px solid #e9e9e9;
					border-top: 1px solid #e9e9e9;
					word-wrap: break-word;
				}

				td {
					border-right: 1px solid #e9e9e9;
					border-top: 1px solid #e9e9e9;
					font-size: 14px;
					color: #666;
				}
			}

			/* 表单必填信息红星 */
			.label-required::after {
				content: '*';
				color: #f56c6c;
				margin-left: 5px;
				font-weight: bolder;
			}
		}
		// 操作日志
		.detail-log {
			.detail-log-item {
				margin: 0.2vh 0;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				padding: 0 0.5vw;

				span {
					color: #999;
					font-size: 14px;
				}

				pre {
					color: #999;
					font-size: 14px;
					margin: 0;
					display: inline-block;
				}
			}
		}
	}
}

/* 开发管理-甘特图样式 */
.taskManagemnet-gantt {
	border-radius: 10px;
	background: #fff;
	border: 1px solid #d7d7d7;
	box-sizing: border-box;
	height: calc(100vh - 168px);
	font-size: 14px;
	color: #606266;
	// 顶部
	.gantt-header {
		display: flex;
		align-items: center;
		padding: 10px 20px 0 20px;

		.el-button {
			display: flex;
			align-items: center;
		}

		i {
			font-size: 18px;
		}

		.circle {
			font-size: 20px;
			margin-right: 5px;
		}
	}
	// 内容
	.gantt-content {
		height: calc(100vh - 245px);
		margin: 0 10px;
		padding: 0;
		border: 1px solid #d7d7d7;
		border-radius: 10px;
		overflow-y: scroll;

		.td-task-tooltip {
			max-height: 15px !important;
			display: inline-block;
		}
		// 左边
		.left {
			width: 100%;
			.search-box {
				height: 47px;
				line-height: 47px;
			}
			// 树型控件样式
			.taskManagemnet-tree {
				.el-tree-node__content {
					height: 28px;
					align-items: center;

					.custom-tree-node {
						width: 100%;
						position: relative;
						display: flex;
						align-items: center;

						.task-content {
							margin: 0px -0.7vw;
							font-size: 12px;
							//font-family: monospace;
							height: 30px;
							width: 100%;
							max-width: 380px;
							display: flex;
							align-items: center;
							justify-content: space-between;

							.task-info {
								.taskNo {
									width: 250px;
								}

								.task-label {
									&:after {
										vertical-align: super;
										font-size: 18px;
										content: '● ';
									}
								}
							}

							.task-time {
								width: 120px;
								font-family: monospace;
							}
						}

						.folder-content {
							height: 30px;
							line-height: 30px;
							display: flex;
							align-items: center;
							// width: 200px;
							// max-width: 350px;
							.folder-title {
								display: flex;
								align-items: center;
								.folder-img {
									vertical-align: text-top;
									height: 15px;
									margin-right: 0.2vw;
								}

								.folder-name {
									margin-right: 0.5vw;
								}
							}

							.folder-time {
								font-size: 14px;
								font-family: monospace;
							}
						}
					}
				}

				.el-tree-node__children {
					.folder-content {
						width: 100%;
						max-width: 366px;
						color: #788;
					}
					.space-between {
						justify-content: space-between;
					}
				}
			}
		}
		// 右边
		.right {
			overflow-x: auto;
			width: 100%;
			box-sizing: border-box;
			padding: 5px 0;
			padding-left: 5px;
			.date-table-wrap {
				font-size: 13px;
				text-align: center;
				color: #7f7f7f;
				.date-table {
					table-layout: fixed;
					border: 1px solid #d7d7d7;
					th {
						// height: 23px;
						min-width: 30px;
						padding: 0 3px;
						border-left-width: 0;
						border-right: 1px solid #d7d7d7;
						border-bottom: 1px solid #d7d7d7;
						background-color: #f2f2f2;
						outline-width: 0;
						white-space: nowrap;
					}
					td {
						height: 28px;
						font-weight: 600;
						.td-task {
							border-radius: 10px;
							color: #7f7f7f;
							border: 1px solid #f2f2f2;
							background-color: #f2f2f2;

							span {
								cursor: pointer;
								display: inline-block;
							}
						}
					}
				}
			}
		}
	}
}
