<template>
	<div class="trafficAndPromotion">
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane v-if="isTrafficSpecialist || isSuperAdmin" label="流量汇报" name="videoDaily">
				<VideoDaily ref="videoDaily" v-if="activeTab == 'videoDaily'" />
			</el-tab-pane>
			<el-tab-pane v-if="isPromoter || isSuperAdmin" label="推广素材配置" name="promotionConfig">
				<PromotionConfig v-if="activeTab === 'promotionConfig'" />
			</el-tab-pane>
			<el-tab-pane v-if="isVideoManager || isSuperAdmin" label="视频号配置" name="videoConfig">
				<VideoConfig v-if="activeTab === 'videoConfig'" />
			</el-tab-pane>

			<el-tab-pane v-if="isTrafficManager || isSuperAdmin" label="流量积分配置" name="videoDailyConfig">
				<VideoDailyConfig v-if="activeTab === 'videoDailyConfig'" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import PromotionConfig from './promotionConfig.vue'; //推广素材配置
import VideoConfig from './videoConfig.vue'; //视频号配置

import VideoDaily from './videoDaily.vue'; //流量汇报
import VideoDailyConfig from './videoDailyConfig.vue'; //流量积分配置

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		PromotionConfig,
		VideoConfig,
		VideoDaily,
		VideoDailyConfig,
	},
	name: 'trafficAndPromotion',
	data() {
		return {
			activeTab: 'videoDaily',
			searchForm: {
				channelName: [],
				twidList: [],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 标签是推广管理员
		isPromoter() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '推广管理员') || false;
		},
		// 标签是视频号管理
		isVideoManager() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '视频号管理') || false;
		},
		// 标签是流量专员
		isTrafficSpecialist() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '流量专员') || false;
		},
		// 标签是流量管理员
		isTrafficManager() {
			const roleList = this.userInfos?.adminUserVO.adminUserLabelVOS || null;
			return roleList?.some(item => item.remark == '流量管理员') || false;
		},

		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		//切换tab页
		changeTab(tab, event) {
			if (tab.name == 'trafficAndPromotion') {
				this.$refs[tab.name].queryTableData();
			}
		},
	},
};
</script>
<style lang="scss" scoped>
.trafficAndPromotion {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
