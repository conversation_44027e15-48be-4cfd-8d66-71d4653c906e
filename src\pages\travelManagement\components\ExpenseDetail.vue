<template>
	<div class="ExpenseDetail border flex p10">
		<!-- 左边表格 -->
		<div class="section-left W50 min-w-400 border-right overflow-x-auto">
			<div class="expense-table table-wrapper pr10">
				<u-table
					id="printTable"
					ref="uTableRef"
					class="table-main"
					:key="expenseForm.key"
					:data="tableData"
					:height="1200"
					:row-height="45"
					:row-class-name="getRowColor"
					@row-click="clickRow"
					show-header-overflow="title"
					stripe
				>
					<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="['expenseDate', 'applyDate'].includes(item.colNo)"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 金额 -->
							<Tooltips
								v-else-if="item.colNo == 'amount' && scope.row[item.colNo]"
								:cont-str="scope.row[item.colNo].toFixed(2)"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 费用类别 -->
							<Tooltips
								v-else-if="item.colNo == 'expenseType'"
								:cont-str="expenseTypeMap[scope.row[item.colNo]]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<u-table-column v-if="!isApprove" label="" width="220" align="right" fixed="right">
						<template #header>
							<!-- 存在未上传凭证，不允许提交 -->
							<el-button
								:disabled="!isSubmitApprove || isApproveComplete"
								:type="['primary', 'warning', '', 'primary', 'warning'][expenseForm.approveStatus]"
								size="mini"
								class="p5 pl8 pr8"
								@click="submitApprove"
								>{{ ['提交', '待审核', '', '已审核', '重新提交'][expenseForm.approveStatus] }}
							</el-button>

							<el-button v-show="!isApproveComplete" type="primary" size="mini" class="p5 pl10 pr10" @click="resetForm"
								>新增
							</el-button>
						</template>
						<template slot-scope="scope">
							<el-button
								v-show="!isApproveComplete"
								type="text"
								class="el-icon-delete color-999"
								@click.stop="deleteRow(scope.row)"
							></el-button>
						</template>
					</u-table-column>
					<u-table-column v-else label="" width="" align="left" fixed="right">
						<template slot-scope="scope">
							<Tooltips
								:class="['color-999', 'red', '', 'green', 'orange'][scope.row.approveStatus]"
								:cont-str="
									jointString(
										' ',
										['未提交', '待审核', '', '已审核', '不通过'][scope.row.approveStatus],
										dateFormat(scope.row.approveDate, 'lineM'),
										scope.row.approveUserName,
										scope.row.approveMemo,
									)
								"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
						</template>
					</u-table-column>
				</u-table>
			</div>
			<div class="flex-column gap-5">
				<div class="label-title mt5">结算明细</div>
				<div class="flex-align-center gap-5 fs-12">
					<Tooltips
						class="min-w-200 max-w-200"
						:cont-str="`报销人：${expenseForm.reimbursementUserName || userInfos.adminUserVO.userName}`"
						:cont-width="180"
					/>
					<Tooltips class="min-w-200 max-w-200" :cont-str="`合计：¥ ${getTotalAmount}`" :cont-width="200" />
					<Tooltips class="max-w-300" :cont-str="'报销金额将由公司直接发给报销人'" :cont-width="200" />
					<Tooltips
						v-if="dealAllocation"
						class="max-w-200 color-999"
						:cont-str="`；成交待分配：¥${dealAllocation}`"
						:cont-width="150"
					/>
				</div>
				<div v-if="detailForm.expenseParty == 3" class="flex-align-center gap-5 fs-12">
					<Tooltips
						class="min-w-200 max-w-200"
						:cont-str="`申请人承担：${detailForm.applyUName || expenseForm.applyUName || ''}`"
						:cont-width="180"
					/>
					<Tooltips class="min-w-200 max-w-200" :cont-str="`合计：¥ ${applyAmount}(不含出差补助)`" :cont-width="200" />
					<Tooltips
						v-if="tripEexpense"
						class="max-w-300"
						:cont-str="`${tripTypeMap[this.detailForm.tripType]}：¥${tripEexpense}`"
						:cont-width="300"
					/>
					<Tooltips v-if="dealDeduct" class="max-w-200 color-999" :cont-str="`；成交待扣减：¥${dealDeduct}`" :cont-width="150" />
				</div>
			</div>
		</div>
		<!-- 右边明细 -->
		<div class="section-right W50 min-w-450">
			<div class="expense-form overflow-x-auto p10">
				<el-form
					ref="formRef"
					:model="editForm"
					:rules="formRules"
					:disabled="isApprove || isApproveComplete"
					size="small"
					label-width="120px"
					label-position="left"
					@submit.native.prevent
				>
					<el-form-item label="日期" prop="expenseDate">
						<el-date-picker
							class="W50 max-w-200"
							v-model="editForm.expenseDate"
							type="date"
							placeholder="费用产生日期"
							format="yyyy-MM-dd"
							value-format="timestamp"
							:picker-options="pickerOptions"
						>
						</el-date-picker>
					</el-form-item>
					<el-form-item label="费用类别" prop="expenseType">
						<el-select
							class="W50 max-w-200"
							v-model="editForm.expenseType"
							placeholder="费用类别"
							clearable
							filterable
							@change="changeExpenseType"
						>
							<el-option v-for="(label, value) in expenseTypeMap" :key="value" :label="label" :value="Number(value)"> </el-option>
						</el-select>
					</el-form-item>

					<!-- 出差补助 -->
					<div v-if="editForm.expenseType == 20">
						<el-form-item label="补助日期" prop="travelAllowanceDate">
							<!-- 日期多选 -->
							<el-date-picker
								class="W50 min-w-250"
								type="dates"
								value-format="yyyy-MM-dd"
								v-model="editForm.travelAllowanceDate"
								placeholder="选择一个或多个日期"
								:clearable="false"
							>
							</el-date-picker>
						</el-form-item>
						<el-form-item label="补助天数" prop="travelAllowanceDays">
							<span v-if="editForm.travelAllowanceDate.length">{{ editForm.travelAllowanceDate.length }}天 </span>
							<span v-if="editForm.travelAllowanceDate.length" class="red">
								{{ editForm.travelAllowanceDate.length > detailForm.tripDays ? '(超出实际出差天数)' : '' }}
							</span>
						</el-form-item>
						<el-form-item label="补助类型" prop="tccid">
							<el-select class="W50 min-w-250" v-model="editForm.tccid" placeholder="补助类型" clearable filterable>
								<el-option
									v-for="item in travelAllowanceType"
									:key="item.tccid"
									:label="`${item.travelAllowanceName}：${item.expense}元 / 天`"
									:value="item.tccid"
								>
									<span>{{ `${item.travelAllowanceName}：${item.expense}元 / 天` }}</span>
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="补助金额" prop="travelAllowanceAmount">
							<span>¥ {{ getTravelAllowanceAmount }}</span>
						</el-form-item>
					</div>

					<!-- 默认 -->
					<div v-else>
						<el-form-item label="费用说明" prop="expenseMemo">
							<el-input
								class="W100"
								v-model="editForm.expenseMemo"
								:placeholder="`请输入${getPlaceholder(editForm.expenseType)}`"
								type="textarea"
								:autosize="{ minRows: 2, maxRows: 4 }"
							></el-input>
						</el-form-item>

						<!-- 用车 -->
						<div v-if="editForm.expenseType == 1" class="flex-align-center">
							<el-form-item label="开始里程(km)" prop="startMileage">
								<el-input
									class="max-w-200"
									v-model="editForm.startMileage"
									placeholder="开始里程(km)"
									clearable
									@input="getDrivenMileage"
								></el-input>
							</el-form-item>
							<el-form-item label="结束里程(km)" prop="endMileage" class="ml10 mr10">
								<el-input
									class="max-w-200"
									v-model="editForm.endMileage"
									placeholder="结束里程(km)"
									clearable
									@input="getDrivenMileage"
								></el-input>
							</el-form-item>
							<el-form-item label="行驶里程(km)" prop="drivenMileage">
								<Tooltips :cont-str="editForm.drivenMileage" :cont-width="100" />
								<!-- <el-input class="W90 max-w-200" v-model="editForm.drivenMileage" placeholder="行驶里程" clearable></el-input> -->
							</el-form-item>
						</div>

						<el-form-item label="报销金额(元)" prop="amount">
							<Tooltips v-if="editForm.expenseType == 1" :cont-str="editForm.amount || ''" :cont-width="100" />
							<el-input v-else class="W50 max-w-200" v-model="editForm.amount" placeholder="报销金额" clearable></el-input>
						</el-form-item>

						<el-form-item label="凭证" prop="receiptFiles">
							<div class="flex-align-center flex-wrap gap-10 max-h-350 overflow-y-auto">
								<div v-for="(item, index) in receiptFilesArray" :key="index" class="file-box">
									<FilePopover class="file-item" isBox :url="item" trigger="click" />
									<div
										v-if="!isApprove && !isApproveComplete"
										class="file-delete el-icon-circle-close pointer"
										@click.stop="deleteFile(index)"
									>
									</div>
								</div>
								<!-- accept="image/*" -->
								<el-upload
									v-if="!isApprove && !isApproveComplete && receiptFilesArray.length < 9"
									class="dragger-uploader-box"
									action=""
									accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
									:http-request="uploadFile"
									:show-file-list="false"
									:limit="9"
									multiple
									drag
								>
									<i class="el-icon-upload"></i>
									<div class="el-upload__text"><em>拖拽/点击上传</em>(不超过9个文件)</div>
								</el-upload>
							</div>
						</el-form-item>
					</div>
				</el-form>
				<!-- 保存按钮 -->
				<div v-if="!isApprove" class="text-right save-button">
					<div
						v-show="
							editForm.bteid && !noCheckMap.get(editForm.expenseType) && receiptFilesArray.length == 0 && !isApproveComplete
						"
						class="red fs-12"
					>
						* 当前费用明细未上传凭证，若该报销单存在未上传的费用明细时将无法提交该报销单进入审核阶段！
					</div>
					<el-button v-if="!editForm.bteid" type="primary" size="mini" @click="saveEdit('add')">保存</el-button>
					<div v-else>
						<el-button v-if="tableData.length == 1 && !isApproveComplete" type="primary" size="mini" @click="saveEdit('update')"
							>修改</el-button
						>
						<div v-else>
							<el-button :disabled="nowIndex == 0" type="primary" size="mini" @click="saveEdit('update-back')">{{
								nowIndex == 0 ? '当前为第一页' : '上一页'
							}}</el-button>
							<el-button
								:disabled="nowIndex == tableData.length - 1"
								type="primary"
								size="mini"
								@click="saveEdit('update-next')"
								>{{ nowIndex == tableData.length - 1 ? '当前为最后一页' : '下一页' }}</el-button
							>
							<el-button v-if="!isApproveComplete" :type="isUpdate ? 'primary' : ''" size="mini" @click="saveEdit('update')"
								>保存</el-button
							>
						</div>
					</div>
				</div>
				<!-- 审核按钮 -->
				<div v-else class="flex-align-center flex-wrap flex-justify-between approve-button">
					<el-form
						:model="editForm"
						:rules="formRules"
						label-width="80px"
						class="W100"
						label-position="top"
						@submit.native.prevent
					>
						<el-form-item label="审核意见" prop="approveMemo">
							<el-input
								v-model="editForm.approveMemo"
								placeholder="请输入审核意见"
								type="textarea"
								:autosize="{ minRows: 2, maxRows: 4 }"
							></el-input>
						</el-form-item>
					</el-form>

					<el-button type="danger" size="mini" @click="saveApprove(4)">审核不通过</el-button>
					<el-button type="primary" size="mini" @click="saveApprove(3)">审核通过</el-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { debounce, checkRequired, resetValues, deepClone, dateFormat, jointString } from '@/util/tool';
import { bigAdd, bigSub, bigDiv, bigMul } from '@/util/math';
import FilePopover from '@/components/FilePopover.vue'; // 文件预览
import { tripTypeMap, expensePartyMap } from '@/assets/js/contractSource'; // 差旅类别 费用承担方

import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
export default {
	name: 'ExpenseDetail',
	components: {
		FilePopover,
	},
	props: {
		detailForm: Object,
		titleName: String,
	},
	data() {
		return {
			nowIndex: null, //当前选择行
			tableData: [],
			tableColumn: [
				{ colName: '日期', colNo: 'expenseDate', align: 'center', width: '100' },
				{ colName: '费用类别', colNo: 'expenseType', align: 'left', width: '120' },
				{ colName: '费用说明', colNo: 'expenseMemo', align: 'left', width: '' },
				{ colName: '报销金额', colNo: 'amount', align: 'right', width: '150' },
			],
			expenseForm: {
				approveStatus: 0,
			},
			expenseTypeMap: {
				1: '用车费',
				2: '停车费',
				3: '过路费',
				10: '交通费',
				20: '出差补助（公司承担）',
				30: '住宿费',
				50: '餐费',
				127: '其他',
			},
			editForm: {
				amount: '',
				btaid: '',
				bteid: '',
				btrid: '',
				createTime: '',
				drivenMileage: '',
				endMileage: '',
				expenseDate: '',
				expenseMemo: '',
				expenseType: '',
				isdelete: '',
				orderNum: '',
				receiptFiles: '',
				startMileage: '',
				tccid: '',
				travelAllowanceDate: [],
				updateTime: '',
				approveMemo: '',
			},
			receiptFilesArray: [], // 文件列表（转换）
			editFormCopy: {},
			//不需要上传凭证的类别
			noCheckMap: new Map([
				[20, '出差补助'], //出差补助
				[50, '餐费'], //餐费
			]),

			travelAllowanceType: [], //出差补助类型
			tripTypeMap, //差旅类别
			expensePartyMap, // 费用承担方
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 存在未上传凭证，不允许提交
		isSubmitApprove() {
			return (
				this.tableData.length > 0 &&
				this.tableData?.every(item => {
					if (this.noCheckMap.get(item.expenseType)) {
						return true;
					} else {
						return item.receiptFiles;
					}
				})
			);
		},
		// 进入审核页面
		isApprove() {
			return this.titleName == '审核';
		},
		// 已通过审核
		isApproveComplete() {
			return this.expenseForm.approveStatus == 3;
		},

		// 动态表单规则
		formRules() {
			const RULES = {
				expenseDate: [{ required: true, message: '请输入日期', trigger: 'change' }],
				expenseType: [{ required: true, message: '请输入费用类别', trigger: 'change' }],
			};

			if (this.isApprove) {
				// 审核
				return {
					approveMemo: [{ required: true, message: '请输入审核意见', trigger: 'change' }],
				};
			} else if (this.editForm.expenseType == 1) {
				// 类型为用车费时必须填写里程
				return {
					...RULES,
					expenseMemo: [{ required: true, message: '请输入费用说明', trigger: 'change' }],
					amount: [{ required: true, message: '请输入报销金额', trigger: 'change' }],
					startMileage: [{ required: true, message: '请输入开始里程', trigger: 'change' }],
					endMileage: [{ required: true, message: '请输入结束里程', trigger: 'change' }],
					// drivenMileage: [{ required: true, message: '请输入行驶里程', trigger: 'change' }],
				};
			} else if (this.editForm.expenseType == 20) {
				// 出差补助
				return {
					...RULES,
					travelAllowanceDate: [{ required: true, message: '请选择补助日期', trigger: 'change' }],
					tccid: [{ required: true, message: '请选择补助类型', trigger: 'change' }],
				};
			} else {
				return {
					...RULES,
					expenseMemo: [{ required: true, message: '请输入费用说明', trigger: 'change' }],
					amount: [{ required: true, message: '请输入报销金额', trigger: 'change' }],
				};
			}
		},
		// 动态日期选中限制
		pickerOptions() {
			const _this = this;
			return {
				disabledDate(time) {
					const before = _this.detailForm.tripBeginDate - 86400000; //开始日期
					const after = _this.detailForm.tripEndDate; //结束日期
					return time.getTime() >= after || time.getTime() <= before;
				},
				// onPick({ maxDate, minDate }) {
				// },
			};
		},

		// 是否修改
		isUpdate() {
			return JSON.stringify(this.editForm) !== JSON.stringify(this.editFormCopy);
		},

		// 出差补助金额
		getTravelAllowanceAmount() {
			if (!this.editForm.tccid || !this.editForm.travelAllowanceDate.length) {
				return 0;
			}

			const expense = this.travelAllowanceType?.find(item => item.tccid == this.editForm.tccid)?.expense || 0;

			return bigMul(this.editForm.travelAllowanceDate.length, expense);
		},

		// 合计金额
		getTotalAmount() {
			if (this.tableData.length == 0) return 0;

			return this.tableData.reduce((prev, curr) => {
				return bigAdd(prev, curr.amount, 2);
			}, 0);
		},
		// 差旅类型配置
		tripTypeConfig() {
			return this.detailForm.selectTravelCategoryConfigurationVO;
		},
		// 成交待分配 = 扣减金额 * 分配比率 * 实际出差天数 （申请人承担）
		dealAllocation() {
			if (this.detailForm.expenseParty !== 3) return 0;
			if (this.tripTypeConfig) {
				const { dealWaitAllocateRate, dealDeductExpense } = this.tripTypeConfig; //差旅类别配置
				const dealAmount = bigMul(Number(dealWaitAllocateRate), dealDeductExpense);
				return bigMul(dealAmount, this.detailForm.tripDays, 2);
			}
			return 0;
		},
		// 成交待扣减= 扣减金额  * 实际出差天数（申请人承担）
		dealDeduct() {
			if (this.detailForm.expenseParty !== 3) return 0;
			if (this.tripTypeConfig) {
				const { dealDeductExpense } = this.tripTypeConfig; //差旅类别配置
				return bigMul(Number(dealDeductExpense), this.detailForm.tripDays, 2);
			}
			return 0;
		},
		// 总差旅费用（差旅费用 * 实际出差天数）
		tripEexpense() {
			if (this.tripTypeConfig) {
				const { expense } = this.tripTypeConfig; //差旅类别配置
				return bigMul(Number(expense), this.detailForm.tripDays, 2);
			}
			return 0;
		},
		// 申请人承担合计（除出差补助外的报销费用 + 总差旅费用）
		applyAmount() {
			const applyAmount = this.tableData?.reduce((prev, curr) => {
				if (curr.expenseType == 20) return prev;
				return bigAdd(prev, curr.amount, 2);
			}, 0);

			return bigAdd(applyAmount, this.tripEexpense, 2);
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTravelAllowanceType();
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 查询出差补助类型
		async queryTravelAllowanceType() {
			const API = 'selectTravelCategoryConfiguration';
			try {
				const res = await this.$axios[API](JSON.stringify({ cfGrouping: 1 }));
				if (res.data.success) {
					this.travelAllowanceType = res.data.data || [];
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 修改类别时清除数据
		changeExpenseType() {
			this.editForm.amount = 0;
			this.editForm.drivenMileage = '';
			this.editForm.startMileage = '';
			this.editForm.endMileage = '';
			this.editForm.expenseMemo = '';
			this.$nextTick(() => {
				this.$refs.formRef?.clearValidate(); //移除该表单项的校验结果
			});
		},
		// 计算行驶里程
		getDrivenMileage: debounce(function () {
			const isValidNumber = function (value) {
				return !isNaN(parseFloat(Number(value)));
			};
			const { startMileage, endMileage } = this.editForm;

			// 如果输入的不是数值默认为0 并输出提示
			if (!isValidNumber(startMileage)) {
				this.editForm.startMileage = 0;
				this.$message.warning('请输入正确的数值');
			}
			if (!isValidNumber(endMileage)) {
				this.editForm.endMileage = 0;
				this.$message.warning('请输入正确的数值');
			}

			this.$nextTick(() => {
				this.editForm.drivenMileage = bigSub(Number(this.editForm.endMileage), Number(this.editForm.startMileage));
				this.editForm.amount = this.editForm.drivenMileage > 0 ? bigMul(Number(this.editForm.drivenMileage), 1.2, 2) : 0; //报销金额等于 行驶里程*1.2
			});
		}),
		// 获取说明备注
		getPlaceholder(type) {
			const typeMap = new Map([
				[1, '起点和终点'], //用车费
				[2, '停车地点和天数'], //停车费
				[3, '起点和终点'], //过路费
				[10, '出行方式以及出发地和目的地'], //交通费
				[30, '酒店名称和天数以及单价'], //住宿费
				[50, '餐费说明'], //餐费
				[127, '费用说明'], //其他
			]);
			return typeMap?.get(type) || '费用说明';
		},
		// 删除文件
		deleteFile(index) {
			this.receiptFilesArray.splice(index, 1);
			this.editForm.bteid && this.saveEdit('update'); //文件修改时自动保存
		},
		// 上传文件
		async uploadFile(item) {
			if (this.receiptFilesArray.length >= 9) {
				this.receiptFilesArray.length = 9;
				return this.$message.warning('最多上传9个文件!');
			}
			try {
				// if (!item.file.type.includes('image')) {
				// 	return this.$message.warning('上传文件只能是图片格式!');
				// }
				if (!(item.file.size / 1024 / 1024 < 20)) {
					return this.$message.warning('上传文件大小不能超过 20MB!');
				}

				const formData = new FormData();
				formData.append('file', item.file);
				formData.append('type', 2);
				// type 1-用户头像,2-团队头像,3-团队图片,4-设备图片,5-反馈图片,6-原料图片,7-模具图片,8-产品图片,9-产品工艺图纸图片
				const res = await this.$axios.uploadFile(formData);
				if (res.data.success) {
					this.receiptFilesArray.push(res.data.data.path);
					this.editForm.bteid && this.saveEdit('update'); //图片修改时自动保存
				} else {
					this.$message.warning(res.data.message);
				}
			} catch (error) {
				this.$message.warning(error.message);
			}
		},
		// 重置表单
		resetForm() {
			this.editForm = resetValues(this.editForm);
			this.editForm.expenseDate = this.detailForm.tripBeginDate || new Date().getTime();
			this.editFormCopy = deepClone(this.editForm);
			this.receiptFilesArray = [];
			this.$nextTick(() => {
				this.$refs.uTableRef?.setCurrentRow({});
				this.$refs.formRef?.clearValidate(); //移除该表单项的校验结果
			});
		},
		// 点击行
		async clickRow(row) {
			this.editForm = { ...deepClone(row), travelAllowanceDate: row.travelAllowanceDate || [] };
			this.editFormCopy = deepClone(this.editForm);
			this.nowIndex = this.tableData.findIndex(item => item.bteid == row.bteid);
			this.receiptFilesArray = this.editForm.receiptFiles ? this.editForm.receiptFiles.split(',') : [];
			this.$nextTick(() => {
				this.$refs.uTableRef?.setCurrentRow(row);

				// 自动滚动到容器最底部
				const scrollDom = document.querySelector('.scrolling-auto');
				scrollDom && (scrollDom.scrollTop = scrollDom.scrollHeight);
			});
		},
		//获取详情数据
		queryDetailData(btrid, type) {
			this.tableData = [];
			this.$axios
				.selectBusinessTripReimbursementById(JSON.stringify({ id: btrid }))
				.then(res => {
					if (res.data.success) {
						this.$nextTick(() => {
							this.expenseForm = res.data.data;
							this.expenseForm.key = new Date().getTime();
							this.$nextTick(() => {
								this.tableData = res.data.data.businessTripExpensesVOList || [];
								this.$refs.uTableRef?.doLayout();
								this.$refs.formRef?.clearValidate(); //移除该表单项的校验结果
								// 初始时
								if (type == 'init' && this.tableData.length) {
									this.clickRow(this.tableData[0]); //默认选中第一行
								}

								// 上下页切换时定位当前选中的行
								if (type?.includes('update')) {
									this.clickRow(this.tableData[this.nowIndex]);
								}

								// 添加时滚动到最后一行
								if (type == 'add') {
									this.$refs.uTableRef?.scrollBottom();
								}
							});
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectBusinessTripReimbursementById |' + error);
				});
		},
		// 保存费用明细
		saveEdit: debounce(async function (type) {
			if (this.editForm.expenseType == 20) {
				const { travelAllowanceName } = this.travelAllowanceType?.find(item => item.tccid == this.editForm.tccid) || {};
				// 费用说明
				this.editForm.expenseMemo = `${travelAllowanceName}：${this.editForm.travelAllowanceDate.length}天出差补助 ${this.editForm.travelAllowanceDate}`;
				// 报销金额
				this.editForm.amount = this.getTravelAllowanceAmount;
			}

			if (checkRequired(this.editForm, this.formRules)) return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			this.editForm.receiptFiles = this.receiptFilesArray?.join(',') || '';

			// 上下页切换
			if (type.includes('back')) {
				this.nowIndex > 0 ? --this.nowIndex : this.nowIndex = this.tableData.length - 1;
			} else if (type.includes('next')) {
				this.nowIndex < this.tableData.length - 1 ? ++this.nowIndex : this.nowIndex = 0;
			}

			// 如果是 已审核/没有修改 切换时表单不调用保存接口
			if (type.includes('update') && (!this.isUpdate || this.isApproveComplete)) {
				this.clickRow(this.tableData[this.nowIndex]);
				console.log('表单未修改，不调用保存接口');
				return;
			}
			const API = this.editForm.bteid ? 'updateBusinessTripExpenses' : 'addBusinessTripExpenses';
			try {
				const res = await this.$axios[API](
					JSON.stringify({
						...this.editForm,
						btaid: this.detailForm.btaid,
						btrid: this.detailForm.btrid,
					}),
				);
				if (res.data.success) {
					this.queryDetailData(this.detailForm.btrid, type);
					if (type == 'add') {
						this.resetForm();
					}
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),

		// 审核费用明细
		saveApprove: debounce(async function (approveStatus) {
			const MEMO = { 3: '通过', 4: '不通过' }[approveStatus];
			if (this.editForm.approveMemo == '通过' || this.editForm.approveMemo == '不通过') {
				this.editForm.approveMemo = '';
			}
			this.editForm.approveMemo = this.editForm.approveMemo ? this.editForm.approveMemo : MEMO;

			if (approveStatus == 4 && checkRequired(this.editForm, this.formRules)) return; // 如果有空字段，则停止执行后面的逻辑，并输出提示{
			const API = 'updateBusinessTripExpensesApprove';
			try {
				const res = await this.$axios[API](
					JSON.stringify({ approveStatus, bteid: this.editForm.bteid, approveMemo: this.editForm.approveMemo }),
				);
				if (res.data.success) {
					this.nowIndex !== this.tableData.length - 1 ? ++this.nowIndex : this.nowIndex = 0;
					this.queryDetailData(this.detailForm.btrid, 'update');
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),

		// 删除行
		async deleteRow({ bteid }) {
			const API = 'deleteBusinessTripExpenses';
			try {
				const res = await this.$axios[API](JSON.stringify({ id: bteid }));
				if (res.data.success) {
					this.queryDetailData(this.detailForm.btrid);

					//删除的是当前的行
					if (this.editForm.bteid == bteid) {
						this.resetForm();
					}
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 提交审核
		async submitApprove() {
			const API = 'updateBusinessTripReimbursementSubmitApprove';
			try {
				const res = await this.$axios[API](JSON.stringify({ id: this.detailForm.btrid }));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.queryDetailData(this.detailForm.btrid, 'submit');
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 表格文本高光显示(未上传凭证红色显示)
		getRowColor({ row }) {
			if (this.isApprove) {
				return !row.approveStatus ? 'red' : ''; //未审核红色显示
			} else {
				return (!row.receiptFiles && !this.noCheckMap.get(row.expenseType)) || !row.amount ? 'red' : ''; //未上传凭证/金额0 红色显示
			}
		},

		dateFormat: dateFormat, //日期format
		jointString: jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.ExpenseDetail {
	// width: auto;
	// height: calc(100% - 220px);
	// overflow: hidden;
	position: relative;
	.section-left {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		.expense-table {
			height: 100%;
		}
		.table-wrapper .table-main {
			min-height: 200px;
			// height: calc(100vh - 510px) !important;
			// height: calc(100% - 100px) !important;
			height: 100% !important;
		}
	}

	.expense-form {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
}
</style>
<style lang="scss">
.ExpenseDetail {
	height: calc(100vh - 400px) !important;
	// 文件上传组件样式
	.file-box {
		position: relative;

		.file-item {
			width: 10vw;
			height: 10vh;
			max-width: 200px;
			max-height: 100px;
			position: relative;
		}

		.file-delete {
			position: absolute;
			top: 5px;
			right: 5px;
			font-size: 16px;
			color: #999;
			opacity: 0;
			&:hover {
				color: #f14b83;
				font-weight: 600;
				transform: scale(1.2);
			}
		}

		&:hover .file-delete {
			transition: all 0.3s;
			opacity: 1;
		}
	}
	.dragger-uploader-box {
		.el-upload-dragger {
			display: flex;
			flex-direction: column;
			width: 10vw;
			height: 10vh;
			max-width: 200px;
			max-height: 100px;

			.el-icon-upload {
				font-size: 50px;
				color: #c0c4cc;
				margin: 0;
			}
			.el-upload__text {
				font-size: 12px;
			}
		}
	}

	.plTableBox .el-table__fixed-footer-wrapper tbody td {
		background: transparent;
		// border: none;
	}
}
</style>
