import Vue from 'vue';
/**
 * 显示成功通知消息。
 *
 * @param {string} message - 要显示在通知中的消息。
 * @return {void}
 */
export function succ(message = '请继续操作', duration = 2000, position = 'bottom-right') {
	Vue.prototype.$notify({
		title: '操作成功',
		message: '操作成功:' + message,
		iconClass: 'icon-third_succ',
		position: position,
		customClass: 'notice-save',
		duration: duration,
	});
}
/**
 * 显示错误通知消息
 *
 * @param {string} message - 要显示在通知中的消息。
 */
export function err(message = '请重新操作', duration = 2000, position = 'bottom-right') {
	Vue.prototype.$notify({
		title: '操作失败',
		message: '操作失败:' + message,
		iconClass: 'icon-third_err',
		position: position,
		customClass: 'notice-err',
		duration: duration,
	});
}
