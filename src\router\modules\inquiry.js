/* 询盘管理路由 */
const channelSetup = () => import('@/pages/inquiryManagement/channelSetup.vue'); //渠道设置
const customerRecord = () => import('@/pages/inquiryManagement/customerRecord.vue'); //客户备案
const extensionChecked = () => import('@/pages/inquiryManagement/extensionChecked.vue'); //展期申请
const inquiryAndDocumentary = () => import('@/pages/inquiryManagement/inquiryAndDocumentary/inquiryAndDocumentary.vue'); //询盘与跟单
const trafficAndPromotion = () => import('@/pages/inquiryManagement/trafficAndPromotion/trafficAndPromotion.vue'); //流量与推广
const dealProtection = () => import('@/pages/inquiryManagement/dealProtection.vue'); //客户保护
const cooperateInquiry = () => import('@/pages/inquiryManagement/cooperateInquiry.vue'); //托管询盘
const InquiryDetail = () => import('@/components/InquiryDetail/InquiryDetail.vue'); //询盘详情

const routers = [
	{
		//渠道设置
		path: '/channelSetup',
		name: 'channelSetup',
		component: channelSetup,
		meta: {
			parentTitle: '询盘管理',
			title: '渠道设置',
		},
	},
	{
		//客户备案
		path: '/customerRecord',
		name: 'customerRecord',
		component: customerRecord,
		meta: {
			parentTitle: '询盘管理',
			title: '客户备案',
		},
	},
	{
		//展期申请
		path: '/extensionChecked',
		name: 'extensionChecked',
		component: extensionChecked,
		meta: {
			parentTitle: '询盘管理',
			title: '展期申请',
		},
	},
	{
		//询盘与跟单
		path: '/inquiryAndDocumentary',
		name: 'inquiryAndDocumentary',
		component: inquiryAndDocumentary,
		meta: {
			parentTitle: '询盘管理',
			title: '询盘与跟单',
		},
		children: [
			{
				path: '/InquiryDetail',
				name: 'InquiryDetail',
				component: InquiryDetail,
				meta: {
					parentTitle: '询盘管理',
					title: '询盘详情',
				},
			},
		],
	},
	{
		//流量与推广
		path: '/trafficAndPromotion',
		name: 'trafficAndPromotion',
		component: trafficAndPromotion,
		meta: {
			parentTitle: '询盘管理',
			title: '流量与推广',
		},
	},
	{
		//成交客户保护
		path: '/dealProtection',
		name: 'dealProtection',
		component: dealProtection,
		meta: {
			parentTitle: '询盘管理',
			title: '成交客户保护',
		},
	},
	{
		//托管询盘
		path: '/cooperateInquiry',
		name: 'cooperateInquiry',
		component: cooperateInquiry,
		meta: {
			parentTitle: '询盘管理',
			title: '托管询盘',
		},
	},
];

export default routers;
