<template>
	<div class="DevelopTableCom">
		<div class="table-title flex-align-center pl2 pr2">
			<el-badge class="mini-badge pointer pr10" :value="tableInfo.badgeNum" :max="99" :hidden="!showBadge">
				<div class="flex-align-center" :title="tableInfo.id">
					<Tooltips class="fs-12 bolder" :cont-str="tableTitle" />
					<!-- <Tooltips v-if="tableInfo.count" class="fs-12 bolder" :cont-str="`(${tableInfo.count})`" /> -->
				</div>
			</el-badge>
			<Tooltips
				v-if="totalAmount"
				class="fs-12 bolder color-999 ml10"
				:class="showBadge ? 'ml20' : ''"
				:cont-str="`${totalAmount} 万元`"
			/>
			<Tooltips v-if="tableInfo.subTitle" class="sub-title fs-12 color-999 mr10" :cont-str="tableInfo.subTitle" />

			<el-checkbox
				v-if="tableTitle == '未清首期款'"
				v-model="monthFilter"
				:true-label="1"
				:false-label="0"
				border
				size="mini"
				class="ml10"
				@change="filterTableData('month')"
				>仅显示本月
			</el-checkbox>
			<el-checkbox
				v-if="tableTitle == '未完成的项目'"
				v-model="delayFilter"
				:true-label="1"
				:false-label="0"
				border
				size="mini"
				class="ml10"
				@change="filterTableData('marks')"
				>仅显示延误项目
			</el-checkbox>

			<el-input
				v-if="['本月积分排名', '本月扣分排名', '工作质量待评价'].includes(tableTitle)"
				class="searchBox zoom-6 mb2"
				size="mini"
				v-model="pointsFilter"
				placeholder="姓名"
				clearable
				@input="filterTableData('uname')"
			></el-input>
			<!-- <Tooltips class="fs-12 bolder mr10" :cont-str="tableTitle" /> -->

			<el-button v-if="tableInfo.button" type="text" size="mini" class="p0 ml-auto" @click="openList">
				{{ tableInfo.button }}
			</el-button>
		</div>
		<!-- 表格主体 -->
		<div class="table-wrapper border border-radius-8 p2">
			<u-table
				ref="uTableRef"
				class="table-main"
				:height="1000"
				:row-height="25"
				:row-class-name="getRowColor"
				:empty-text="tableInfo.emptyText || '暂无数据'"
				:total="tablePageForm.total"
				:page-size="tablePageForm.pageSize"
				:current-page="tablePageForm.currentPage"
				:page-sizes="tablePageForm.pageSizes"
				:pagination-show="tableData.length > 50 ? true : false"
				@handlePageSize="handlePageSize"
				show-header-overflow="title"
				use-virtual
				stripe
			>
				<u-table-column :label="' '" width="30" type="index" align="center"></u-table-column>
				<u-table-column
					v-for="item in tableInfo.tableColumn"
					:key="item.colNo"
					:label="item.colName"
					:prop="item.colNo"
					:align="item.align"
					:width="item.width"
					resizable
				>
					<!-- sortable="custom" -->
					<template slot-scope="{ row, column }">
						<!-- 各种日期（默认不显示分秒 lineM ） -->
						<Tooltips
							v-if="item.colName.includes('时间')"
							:cont-str="dateFormat(row[item.colNo], 'MDHM')"
							:cont-width="column.realWidth - 20"
						/>

						<Tooltips
							v-else-if="item.colName.includes('到期日')"
							:cont-str="dateFormat(row[item.colNo], 'MD')"
							:cont-width="column.realWidth - 20"
						/>

						<Tooltips
							v-else-if="item.colNo.includes('Month')"
							:cont-str="dateFormat(row[item.colNo], 'YM')"
							:cont-width="column.realWidth - 20"
						/>

						<Tooltips
							v-else-if="item.colName == '工时'"
							:cont-str="`${row[item.colNo] || 0} h`"
							:cont-width="column.realWidth - 20"
						/>
						<Tooltips
							v-else-if="item.colName == '延误'"
							:cont-str="`${row[item.colNo]} 天`"
							:cont-width="column.realWidth - 20"
						/>

						<Tooltips
							v-else-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
							:cont-str="dateFormat(row[item.colNo], 'MD')"
							:cont-width="column.realWidth - 20"
						/>

						<!-- 工作项 -->
						<Tooltips
							v-else-if="item.colNo == 'operation'"
							class="hover-green"
							@click.native="
								row.pmtid ? openDetail('TaskDetail', row, '查看研发任务') : row.pmid ? openDetail('ProjectDetail', row) : () => {}
							"
							:cont-str="getOperation(row)"
							:cont-width="column.realWidth - 20"
						/>
						<!-- 任务 -->
						<Tooltips
							v-else-if="item.colNo == 'taskName'"
							class="hover-green"
							@click.native="openDetail('TaskDetail', row, '执行研发任务')"
							:cont-str="row[item.colNo]"
							:cont-width="column.realWidth - 20"
						/>

						<!-- 未完成的项目进度 -->
						<div
							v-else-if="item.colNo == 'developProjectVOS'"
							:class="row.pmid ? 'pointer' : ''"
							@click="row.pmid ? openDetail('ProjectDetail', row) : () => {}"
						>
							<StepsCom :rowData="row" />
						</div>

						<!-- 默认显示 -->
						<Tooltips
							v-else-if="row[item.colNo]"
							:cont-str="row[item.colNo] !== null ? row[item.colNo] : ''"
							:cont-width="column.realWidth - 20"
						/>
					</template>
				</u-table-column>
				<!-- 其他列/操作 -->
				<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDialogRow(row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(row)"></el-button>
                </template>
              </u-table-column> -->
			</u-table>
		</div>
	</div>
</template>

<script>
import { debounce, dateFormat, jointString, sortTableData, accAdd, deepClone } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import StepsCom from '../DevelopStepsCom'; //开发项目进度
export default {
	// import引入的组件需要注入到对象中才能使用
	components: { StepsCom },
	name: 'DevelopTableCom', //组件名应同路由名(否则keep-alive不生效)
	props: {
		tableInfo: Object,
	},
	data() {
		return {
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 100, 500],
			},
			monthFilter: false,
			delayFilter: false,
			pointsFilter: '',
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 表格标题
		tableTitle() {
			return this.tableInfo.title;
		},
		// 合计金额
		totalAmount() {
			if (!this.tableData.length) return 0;
			const { title } = this.tableInfo;
			const amountMap = {
				未清首期款: 'amount',
				昨日签单: 'dealAmount',
				昨日收款: 'receiptsAmount',
				本月预计成交: 'estimatedAmount',
			};

			if (!amountMap[title]) return 0;

			return this.tableData.reduce((total, row) => {
				let amount = Number(row[amountMap[title]] || 0);
				if (title === '未清首期款') amount = this.convertToMillion(amount);
				if (title === '昨日收款') amount = this.convertToMillion(amount);
				return accAdd(total, amount, 2);
			}, 0);
		},

		// 显示badge
		showBadge() {
			if (['工作质量待评价'].includes(this.tableTitle)) {
				return false;
			}
			return this.tableInfo.badgeNum;
		},
	},
	// 监控data中的数据变化
	watch: {
		'tableInfo.data'(newVal) {
			if (this.monthFilter) {
				this.filterTableData('month');
			} else if (this.delayFilter) {
				this.filterTableData('marks');
			} else if (this.pointsFilter) {
				this.filterTableData('uname');
			} else {
				this.initTableData(newVal);
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	activated() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开明细
		openDetail(ref, ...args) {
			this.$emit('openDetail', ref, ...args);
			this.$emit('getTableOptions', this.tableData);
		},
		// 打开清单
		openList() {
			const { button } = this.tableInfo;
			const buttonActions = {
				询盘清单: 'InquriyList',
				合同管理: 'ContractTable',
				交付管理: 'DeliveryManagement',
				项目总览: 'DeliveryOverview',
				回款与开票: 'PaybackManagement',
				客户健康度管理: 'ActivityRating',
				到期管理: 'ExpirationManagement',
				出差审批: 'TravelApproval',
				质量评价: 'IndividualPoints',
			};

			if (buttonActions[button]) {
				this.$emit('openList', buttonActions[button], 'openMove');
			} else {
				this.$message.warning(`打开${button}的功能，我们正在开发中！`);
			}
		},

		// 行样式（颜色显示）
		getRowColor({ row }) {
			if (this.tableTitle == '积分/扣分记录') {
				return row.points < 0 ? 'red ' : ''; //扣分玫红色
			} else if (this.tableTitle == '我的开发任务' && row.endTime) {
				// 未来三天内的黄色
				// const nowTime = this.$moment().startOf('day').valueOf();
				const after3Days = this.$moment().add(3, 'days').endOf('day').valueOf();
				return row.endTime <= after3Days ? 'orange' : '';
			}
			const keywords = ['延误', '失', '扣分排名'];
			return keywords.some(keyword => this.tableTitle.includes(keyword)) ? 'red breathing-text' : undefined;
		},
		// 获取工作项信息
		getOperation(row) {
			const { operation, pointsDate, projectManagementTask: taskInfo, projectManagement: projectInfo } = row;
			const time = this.dateFormat(pointsDate, 'MDHM');
			const content = row.pmtid ? `${taskInfo?.taskNo} ${taskInfo?.taskName}` : row.pmid ? projectInfo.projectName : '';
			return this.jointString(' ', time, operation, content);
		},
		// 将金额转成万单位
		convertToMillion(value, d = 2) {
			if (!value || Number(value) <= 0) return '';
			// Convert the value to million units
			const valueInMillion = value / 10000;

			// Round to two decimal places
			const resultVal = (Math.round(valueInMillion * 100) / 100).toFixed(2);
			const decimalPart = resultVal.split('.')[1];
			const decimalsToAdd = 2 - decimalPart.length;
			const finalValue = decimalsToAdd > 0 ? resultVal + '0'.repeat(decimalsToAdd) : resultVal;
			return finalValue;
		},

		// 初始化表格数据/分页等
		initTableData(tableData) {
			this.tableData = deepClone(tableData);
			this.tablePageForm = {
				total: tableData.length,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 100, 500],
			};
			const { currentPage: page, pageSize: size } = this.tablePageForm;
			this.handlePageSize({ page, size });
		},
		// 过滤表格数据
		filterTableData: debounce(function (type) {
			if (type === 'month' && this.monthFilter) {
				// 过滤本月数据
				return this.initTableData(this.tableInfo.data.filter(i => new Date(i.complateMonth).getMonth() == new Date().getMonth()));
			} else if (type === 'marks' && this.delayFilter) {
				// 过滤延误的项目
				return this.initTableData(this.tableInfo.data.filter(i => i.marks == 0));
			} else if (type === 'uname' && this.pointsFilter) {
				// 过滤名称
				return this.initTableData(this.tableInfo.data.filter(i => i.uname.includes(this.pointsFilter)));
			}

			this.initTableData(this.tableInfo.data);
		}),
		// 分页(不通过接口分页)
		handlePageSize({ page, size }) {
			const tableData = this.tableData.slice((page - 1) * size, page * size);
			this.$nextTick(() => {
				this.$refs.uTableRef?.reloadData(tableData);
				this.$refs.uTableRef?.doLayout();
			});
		},
		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss">
.DevelopTableCom {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
	// .breathing-text {
	// 	animation: breathing 2s infinite; // 应用动画
	// 	@keyframes breathing {
	// 		0%,
	// 		100% {
	// 			opacity: 1; // 完全不透明
	// 			font-weight: 600;
	// 		}
	// 		50% {
	// 			opacity: 0.6; // 半透明
	// 		}
	// 	}
	// }
	.border-radius-8 {
		border-radius: 8px;
	}
	.zoom-7 {
		zoom: 0.7;
	}
	.zoom-6 {
		zoom: 0.6;
	}
	.table-title {
		color: #555;
		height: 20px;
		.mini-badge {
			.el-badge__content {
				z-index: 88;
				zoom: 0.75;
				padding: 0px 5px;
				/* right: 12px; */
				top: 6.5px;
			}
		}

		.el-checkbox.el-checkbox--mini.is-bordered {
			border: none;
		}
	}

	// 表格样式调整
	.table-wrapper {
		width: 100%;
		height: calc(100% - 20px) !important;
		&:hover {
			border-color: #1e9d6f !important;
		}

		// 表格主体
		.table-main {
			width: 100% !important;
			height: 100% !important;
			min-height: 50px !important;
			// height: calc(100% - 22px) !important;

			.el-table__body td {
				height: 25px !important;
				padding: 0px;
			}
			.el-table__body th {
				height: 25px !important;
				padding: 0px;
			}
			.is-leaf:not(:last-child) {
				padding: 0 !important;
			}
			.el-table__empty-block {
				min-height: 40px;
				width: 100% !important;
				.el-table__empty-text {
					line-height: normal;
				}
			}

			// 表格底部线去除
			.el-table::before {
				height: 0px;
				background: transparent;
			}
		}

		// 表格分页
		.myPagination {
			zoom: 0.7;
		}
	}
}
</style>
