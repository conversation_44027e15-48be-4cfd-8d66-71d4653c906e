<template>
	<div class="errorQuestion">
		<!-- 数据导出弹窗 -->
		<!-- <ExportTable ref="ExportTable" /> -->
		<!-- 明细组件 -->
		<BaseLayout>
			<template #header>
				<!-- 带建议日期 -->
				<span class="search-label">期间</span>
				<DateSelect
					defaultDate="本月"
					:clearable="false"
					@change="
						searchForm.startDate = $event.startTime;
						searchForm.endDate = $event.endTime;
						queryTableData(1);
					"
				/>
				<!-- 模糊查询 -->
				<el-input
					class="searchBox"
					size="small"
					v-model.trim="searchForm.userNameQuery"
					placeholder="姓名"
					@input="queryTableData(1)"
					clearable
				></el-input>
				<el-input
					class="searchBox"
					size="small"
					v-model.trim="searchForm.trainPlanQuery"
					placeholder="培养计划"
					@input="queryTableData(1)"
					clearable
				></el-input>
				<el-input
					class="searchBox"
					size="small"
					v-model.trim="searchForm.courseNameQuery"
					placeholder="课程"
					@input="queryTableData(1)"
					clearable
				></el-input>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button>
				</div>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar"></div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:dialog-data="tableColumn"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					@header-dragend="headerDragend"
					@reset="updateColumn(tableColumnCopy)"
					@show-field="updateColumn"
					show-header-overflow="title"
					header-drag-style
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="(item, index) in tableColumn.filter(item => item.state)"
						:key="item.colNo + index"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期（默认不显示分秒 lineM ） -->
							<Tooltips
								v-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 选项 -->
							<Tooltips
								v-else-if="item.colNo.includes('option')"
								:class="[
									scope.row[item.colNo]?.isUserChooseAnswer ? 'red' : '',
									scope.row[item.colNo]?.isCorrect == 0 ? 'green' : '',
								]"
								:cont-str="scope.row[item.colNo]?.answer || ''"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>
							<!-- 答案解析 -->
							<Tooltips
								v-else-if="item.colNo == 'answerDetailedExplanation'"
								:class="scope.row[item.colNo] ? '' : 'color-999'"
								:cont-str="scope.row[item.colNo] || '暂无解析'"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDetail('编辑',scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template>
              </u-table-column> -->
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
// import ExportTable from '@/components/ExportTable'; //导出组件
// import btnAuth from '@/mixins/btnAuth';

export default {
	name: 'errorQuestion', //组件名应同路由名(否则keep-alive不生效)
	// import引入的组件需要注入到对象中才能使用
	components: {
		// ExportTable,
		DateSelect,
	},
	// mixins: [btnAuth],
	data() {
		return {
			activeTab: 'errorQuestion', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '培养计划', colNo: 'trainPlan', align: 'left', width: '' },
				{ colName: '姓名', colNo: 'userName', align: 'left', width: '150' },
				{ colName: '课程', colNo: 'coursesName', align: 'left', width: '' },
				// { colName: '序号', colNo: 'no', align: 'right', width: '' },
				{ colName: '题目', colNo: 'title', align: 'left', width: '' },
				{ colName: '选项A', colNo: 'optionA', align: 'left', width: '' },
				{ colName: '选项B', colNo: 'optionB', align: 'left', width: '' },
				{ colName: '选项C', colNo: 'optionC', align: 'left', width: '' },
				{ colName: '选项D', colNo: 'optionD', align: 'left', width: '' },
				{ colName: '本次提交', colNo: 'userAnswer', align: 'center', width: '' },
				{ colName: '正确答案', colNo: 'correctAnswer', align: 'center', width: '' },
				{ colName: '答案解析', colNo: 'answerDetailedExplanation', align: 'left', width: '' },
				{ colName: '提交时间', colNo: 'answerDate', align: 'center', width: '150' },
			],
			tableColumnCopy: [],
			// 查询表单
			searchForm: {
				startDate: '',
				endDate: '',
				userNameQuery: '',
				trainPlanQuery: '',
				courseNameQuery: '',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.tableColumnCopy = deepClone(this.tableColumn);
		this.tableColumn = getColumn(this.tableColumn, this.$options.name);
		// this.queryTableData();
	},
	activated() {
		// this.queryTableData(1);
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectWrongAnswerLogs'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						this.tableData =
							res.data.data?.map(item => {
								const { selectAnswerVOS, ...rest } = item;
								const correctAnswer = [];
								const userAnswer = [];
								selectAnswerVOS.forEach((item, index) => {
									const option = ['A', 'B', 'C', 'D'][index];
									if (item.isUserChooseAnswer) {
										userAnswer.push(option);
									}
									if (item.isCorrect == 0) {
										correctAnswer.push(option);
									}
								});

								const [optionA, optionB, optionC, optionD] = selectAnswerVOS || [];
								return { ...rest, optionA, optionB, optionC, optionD, userAnswer, correctAnswer };
							}) || [];

						console.log(this.tableData);
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 展示字段
		updateColumn(data) {
			this.tableColumn = updateColumn(this.$options.name, data);
			this.sortChange(this.tableSort, true);
		},
		// 列宽拖动
		headerDragend(newWidth, oldWidth, column) {
			updateColumnWidth(this.$options.name, column.property, newWidth, this.tableColumn);
		},

		//数据导出
		openExport: debounce(function () {
			const PROPS = {
				DATA: JSON.stringify({ ...this.searchForm }), //接口参数
				API: 'xxxxxDownload', //导出接口
				downloadData: 'xxxxx明细', //数据报表参数（后台确认字段downloadData）
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.errorQuestion {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
