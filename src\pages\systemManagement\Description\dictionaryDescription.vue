<template>
	<div id="dictionaryDescription">
		<!-- 资源功能说明 -->
		<ResourceDescription ref="ResourceDescription" @update="queryDetailData"></ResourceDescription>
		<!-- 主容器 -->
		<div class="main-container flex W100 H100 gap-10">
			<!-- 左侧 -->
			<div class="left flex-column W20 H100 min-w-250 overflow-auto">
				<!-- 左侧-标题 -->
				<div class="header-title">
					<div class="label-title">资源说明 </div>
				</div>
				<!-- 左侧-树结构 -->
				<div class="tree-wrapper">
					<div class="tree-guide-line">
						<el-tree
							ref="treeRef"
							node-key="rcid"
							:data="treeData"
							:props="{ children: 'fieldResourceVOS', label: 'resourceName' }"
							:default-expand-all="false"
							:indent="0"
							:highlight-current="true"
							:expand-on-click-node="false"
							@node-click="clickNode"
						>
							<div class="h-40 flex-align-center gap-10" :class="node.isLeaf ? 'no-children' : ''" slot-scope="{ node, data }">
								<img :src="data.rdcid ? boardIcon : folderIcon" />
								<span class="fs-14">{{ data.resourceName }}</span>
								<el-input
									v-if="data.rcid"
									class="flex-1 input-green input-border-none p0 m0"
									v-model="data.resourceExtension"
									size="mini"
									placeholder="备注"
									clearable
									@change="changeNode(data)"
								></el-input>
							</div>
						</el-tree>
					</div>
				</div>
			</div>

			<!-- 右侧 -->
			<div class="right flex-1 flex-column H100 min-w-500">
				<!-- 右侧-空状态 -->
				<div v-if="!detailForm.rdcid" class="flex-center H100">
					<div class="empty-content">
						<img src="@/assets/img/none.webp" />
						<p class="empty-text flex-align-center gap-10">
							<span>请选择左侧的资源</span>
							<img :src="boardIcon" />
						</p>
					</div>
				</div>

				<!-- 右侧-标题 -->
				<div v-if="detailForm.rdcid" class="header-title flex-align-center gap-10">
					<div class="label-title">{{ detailForm.resourceName }} </div>
					<el-button class="el-icon-info p0 m0" type="text" @click="openDetail(detailForm)"> 功能说明</el-button>
				</div>

				<!-- 右侧-表格 -->
				<div v-if="detailForm.rdcid" class="table-wrapper H100">
					<u-table class="table-main" :data="tableData" :height="1200" :row-height="45" stripe show-header-overflow="title">
						<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
						<u-table-column label="字段" prop="fieldName" width="120" resizable>
							<template slot-scope="scope">
								<Tooltips
									:cont-str="scope.row.fieldName || ''"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</template>
						</u-table-column>
						<u-table-column label="字段说明" prop="description" width="" resizable>
							<template slot-scope="scope">
								<el-input
									class="input-green input-border-none p0 m0"
									v-model="scope.row.description"
									size="small"
									placeholder="字段说明"
									@change="changeRow(scope.row)"
								></el-input>
							</template>
						</u-table-column>
					</u-table>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { dateFormat, deepClone, debounce, resetValues } from '@/util/tool';
import ResourceDescription from './ResourceDescription.vue';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ResourceDescription,
	},
	name: 'dictionaryDescription',
	data() {
		return {
			folderIcon: require('@/assets/img/folder.svg'), // 文件夹图标
			boardIcon: require('@/assets/img/boardIcon.svg'), // 板块图标
			treeData: [], // 树结构数据
			tableData: [], // 表格数据
			detailForm: {}, // 节点详情数据
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	activated() {
		this.queryTreeData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTreeData();
	},
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开功能说明
		openDetail(form) {
			this.$refs.ResourceDescription.openDetail(form);
		},

		// 查询树结构
		queryTreeData: debounce(async function () {
			const API = 'selectFieldResources';
			try {
				const res = await this.$axios[API](JSON.stringify({}));
				if (res.data.success) {
					this.treeData = res.data.data;
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		// 点击树节点
		clickNode(node) {
			if (!node.rdcid || this.detailForm.rdcid == node.rdcid) {
				this.tableData = [];
				this.detailForm = resetValues(this.detailForm);
				return;
			}
			this.detailForm = { ...node };
			this.queryDetailData();
		},

		// 查询字段说明配置信息
		async queryDetailData() {
			const API = 'selectFieldDescriptionConfig';
			try {
				const res = await this.$axios[API](JSON.stringify({ rdcid: this.detailForm.rdcid }));
				if (res.data.success) {
					this.detailForm = { ...this.detailForm, ...res.data.data };
					this.tableData = res.data.data?.fieldDescriptionConfigVOS || [];
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 修改字段说明
		changeRow: debounce(async function (row) {
			const API = 'updateFieldDescriptionConfig';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...row }));
				if (res.data.success) {
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),

		// 修改节点
		changeNode: debounce(async function (node) {
			const API = 'addResourceExplain';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...node }));
				if (res.data.success) {
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		//日期format
		dateFormat,
	},
};
</script>

<style lang="scss" scoped>
#dictionaryDescription {
	width: 100%;
	height: calc(100vh - 110px);
	overflow: hidden;
	position: relative;
	box-sizing: border-box;
}
</style>

<style lang="scss">
#dictionaryDescription {
	.main-container {
		border: 1px solid #d7d7d7;
		background: #fff;
		border-radius: 8px;
		color: #666;
		padding: 5px 10px;
		.header-title {
			height: 40px;
			line-height: 40px;
			padding: 0 5px;
			position: sticky;
			top: 0;
			background: #fff;
			z-index: 10;
		}

		.left {
			position: relative;
		}

		.right {
			position: relative;
			.table-main {
				height: 100% !important;
			}
			.empty-content {
				text-align: center;
				.empty-text {
					font-size: 16px;
					color: #999;
				}
			}
		}
	}
}
</style>
