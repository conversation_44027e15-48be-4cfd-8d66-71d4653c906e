<template>
	<!-- 在线支持工作台 ： 这里控制【动态组件清单】 【提问清单】 【问题详情】 刷新时机和数据通信-->
	<div id="OnlineSupport">
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane v-for="workbenche in workbenches" :key="workbenche.id" :label="workbenche.label" :name="workbenche.component">
				<div class="OnlineSupport-wapper flex-align-center">
					<!-- 动态组件清单 -->
					<section class="W20 H100 flex-column gap-10 pr10 border-right mr20">
						<component
							:is="workbenche.component"
							:ref="workbenche.component"
							@getNowTid="getNowTid"
							@getTopRcid="getTopRcid"
						></component>
					</section>

					<!-- 提问清单 -->
					<section class="W30 flex-1 H100 flex-column gap-10 pr10 border-right">
						<QuestionList
							:ref="`${workbenche.id}_QuestionList`"
							:resData="resData"
							:nowDetail="nowDetail"
							:workbenche="workbenche"
							@getDetail="getDetail"
						/>
					</section>

					<!-- 提问详情和回答清单 -->
					<section class="W50 H100 flex-column gap-10 pl20">
						<QuestionDetail
							:ref="`${workbenche.id}_QuestionDetail`"
							:workbenche="workbenche"
							:resData="resData"
							@getNowDetail="nowDetail = $event"
							@refresh="refreshList"
						/>
					</section>
				</div>
			</el-tab-pane>
			<el-tab-pane label="在线支持清单" name="OnlineSupportList">
				<OnlineSupportList ref="OnlineSupportList" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>
<script>
import { jointString, dateFormat, resetValues, deepClone, debounce } from '@/util/tool';
import { mapGetters } from 'vuex';
import TeamList from './TeamList'; // 交付在线支持工作台 - 团队清单
import ResourceList from './ResourceList'; // 成功部在线支持工作台 - 资源清单
import MenuList from './MenuList.vue'; // 研发在线支持工作台 - 菜单清单
import QuestionList from './QuestionList'; // 提问清单
import QuestionDetail from './QuestionDetail.vue'; // 提问详情和回答清单

import OnlineSupportList from './OnlineSupportList.vue'; // 在线支持记录清单

export default {
	name: 'OnlineSupport',
	props: {},
	components: { TeamList, ResourceList, MenuList, QuestionList, QuestionDetail, OnlineSupportList },
	data() {
		return {
			activeTab: 'ResourceList',
			nowDetail: {}, // 详情数据
			resData: {}, // 资源描述数据
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['menuTitle', 'userInfos', 'menuList']),
		// 用户角色判断
		userRolesMap() {
			const roleList = this.userInfos?.adminUserVO?.adminRoleVOS || [];
			const labelList = this.userInfos?.adminUserVO?.adminUserLabelVOS || [];

			return {
				isSuperAdmin: roleList.some(item => item.arid == 1 || item.roleName == '超级管理员'),
				isSuccessSpecialist: labelList.some(item => item.remark == '成功部专员'),
				isDeliveryTeacher: labelList.some(item => item.remark == '实施顾问'),
				isDevManager: labelList.some(item => ['开发经理', '研发经理', '技术经理'].includes(item.remark)),
			};
		},
		// 各角色判断 - 直接使用userRolesMap的结果
		// 超级管理员
		isSuperAdmin() {
			return this.userRolesMap.isSuperAdmin;
		},
		// 成功部专员
		isSuccessSpecialist() {
			return this.userRolesMap.isSuccessSpecialist;
		},
		// 交付老师
		isDeliveryTeacher() {
			return this.userRolesMap.isDeliveryTeacher;
		},
		// 研发经理
		isDevManager() {
			return this.userRolesMap.isDevManager;
		},
		// tab列表 0-交付 1-成功部 2-研发
		workbenches() {
			const { isSuperAdmin } = this.userRolesMap;
			// 定义工作台映射
			const workbenches = [
				{
					replyDept: 0,
					id: 'Delivery',
					type: '交付',
					role: 'isDeliveryTeacher',
					label: '交付在线支持工作台',
					component: 'TeamList',
					api: 'selectConsultantTeamAndUnReplyCount',
				},
				{
					replyDept: 1,
					id: 'Success',
					type: '成功部',
					role: 'isSuccessSpecialist',
					label: '成功部在线支持工作台',
					component: 'ResourceList',
					api: 'selectCurrentTeamMenuAndUnReadCount',
				},
				{
					replyDept: 2,
					id: 'Develop',
					type: '研发',
					role: 'isDevManager',
					label: '研发在线支持工作台',
					component: 'MenuList',
					api: 'onlineSupportWorkBenchOfRDMenuInfo',
				},
			];

			// 超级管理员可以访问所有工作台
			if (isSuperAdmin) {
				return workbenches;
			}

			// 其他角色只能访问对应的工作台
			for (const bench of workbenches) {
				if (this.userRolesMap[bench.role]) {
					return [{ ...bench }];
				}
			}

			return [];
		},

		// 当前工作台
		nowWorkbenche() {
			return this.workbenches.find(item => item.component == this.activeTab);
		},
		// 当前工作台的id
		nowWorkbencheId() {
			return this.nowWorkbenche?.id;
		},
		// 当前动态组件的ref
		nowComponentRef() {
			return this.$refs[this.activeTab][0];
		},
		// 当前QuestionList的ref
		nowQuestionListRef() {
			return this.$refs[`${this.nowWorkbencheId}_QuestionList`][0];
		},
		// 当前QuestionDetail的ref
		nowQuestionDetailRef() {
			return this.$refs[`${this.nowWorkbencheId}_QuestionDetail`][0];
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.activeTab = this.workbenches[0]?.component || 'ResourceList';
		this.changeTab();
	},
	activated() {
		this.refreshList();
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		this.resData = resetValues(this.resData);
		this.nowDetail = resetValues(this.nowDetail);
	},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 刷新动态组件的清单和问题清单
		refreshList() {
			this.nowComponentRef.queryList(); // 刷新最左侧动态组件列表
			this.nowQuestionListRef.queryList(); // 刷新问题清单
		},
		// 切换tab
		changeTab() {
			if (this.activeTab == 'OnlineSupportList') {
				this.$refs.OnlineSupportList.queryTableData(1);
			} else {
				this.nowComponentRef.queryList(); // 查询最左侧动态组件清单
				this.nowQuestionDetailRef?.clearDetail(); // 清空详情
			}
		},
		// 获取资源id
		getTopRcid(topRcid) {
			this.resData.topRcid = topRcid;
			this.resData.nowTid = '';
			this.nowQuestionListRef.queryList(); // 查询问题清单
			this.nowQuestionDetailRef.clearDetail(); // 清空详情
		},
		// 获取团队id
		getNowTid(nowTid) {
			this.resData.topRcid = '';
			this.resData.nowTid = nowTid;
			this.nowQuestionListRef.queryList(); // 查询问题清单
			this.nowQuestionDetailRef.clearDetail(); // 清空详情
		},

		// 获取详情
		getDetail(type = 'query', item) {
			if (type == 'reset') {
				this.nowQuestionDetailRef.clearDetail(); // 清空详情
			} else if (type == 'query') {
				this.nowDetail = { ...this.nowDetail, ...item };
				this.nowQuestionDetailRef.queryDetail(this.nowDetail); // 查询详情
			}
		},

		jointString, // 拼接字符串
		dateFormat, // 日期格式化
	},
};
</script>

<style lang="scss" scoped>
#OnlineSupport {
	width: 100%;
	overflow: hidden;
	position: relative;
	.OnlineSupport-wapper {
		width: 100%;
		padding: 10px;
		height: calc(100vh - 175px);
		overflow: hidden;
		position: relative;

		border: 1px solid #d7d7d7;
		border-radius: 8px;
		background: #fff;
		color: #666;
	}
	@media screen and (max-width: 1280px) {
		.OnlineSupport-wapper {
			height: calc(100vh - 120px) !important;
		}
	}
}
</style>
