<template>
	<div id="customerRecord">
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="inquiry"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twidList = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="客户" name="customerRecord">
				<BaseLayout>
					<template #header>
						<span class="search-label">备案日期</span>
						<DateSelect
							:dateList="['不限定', '最近30天', '最近60天']"
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>

						<!-- 模糊查询 -->
						<SearchHistoryInput
							name="companyName"
							placeholder="客户工商注册名称"
							v-model.trim="queryStr"
							@input="queryTableData(1)"
						/>

						<!-- <el-input
							class="searchBox"
							size="small"
							clearable
							v-model="queryStr"
							placeholder="客户工商注册名称"
							@input="queryTableData(1)"
						></el-input> -->

						<el-checkbox v-model="searchForm.status" :true-label="0" :false-label="1" @change="queryTableData(1)"
							>仅显示有效备案</el-checkbox
						>

						<!-- <el-checkbox v-model="searchForm.spreadStatus" :true-label="0" :false-label="1" @change="queryTableData(1)"
							>仅显示需展期客户</el-checkbox
						> -->

						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<el-button type="text" class="icon-third-bt_addman" @click="addCustomer">添加</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['keepRecordTime', 'recentlyDocumentaryTime', 'protectDeadline'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 询盘编号 
									 	class="hover-green primary"
										@click.native="openDetail(scope.row)"
									 -->
									<Tooltips
										v-else-if="item.colNo == 'number'"
										:cont-str="scope.row[item.colNo] || '未知'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<!-- 剩余有效天数 -->
									<Tooltips
										v-else-if="item.colNo == 'surplusValidDay'"
										:class="{ red: scope.row[item.colNo] < 10 }"
										:cont-str="scope.row[item.colNo]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:class="[scope.row[item.colNo] == 0 ? 'primary' : 'red']"
										:cont-str="scope.row[item.colNo] == 0 ? '有效' : '失效'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<u-table-column label="" width="" align="right">
								<template slot-scope="scope">
									<el-button v-show="scope.row.status == 0" type="text" size="small" @click="deleteRow(scope.row, scope.$index)"
										>删除</el-button
									>
									<!-- 展期状态 spreadStatus 0无展期 1有展期 2展期审核中 3展期审核不同意 4永久保护 -->
									<!-- <el-button v-show="scope.row.spreadStatus" type="text" size="small" @click="openDialog(scope.row, '展期')">
											<span v-show="scope.row.spreadStatus == 1">展期</span>
											<span v-show="scope.row.spreadStatus == 2" class="666">审核中...</span>
										</el-button> -->
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>

		<!-- 客户备案弹窗 -->
		<el-dialog :visible.sync="dialogEdit" width="666px" top="10vh" :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="8vw" label-position="left" ref="editFormRef" :rules="editRules">
				<el-form-item label="分销/代理" prop="twid" v-show="teamWorkList.length > 1">
					<el-select
						v-model="editForm.twid"
						placeholder="请先选择分销/代理再开始输入客户信息"
						class="W100"
						clearable
						filterable
						@change="changeChannel"
					>
						<el-option v-for="item in teamWorkList" :key="item.twid" :label="item.twName" :value="item.twid"> </el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="客户工商注册名称" prop="registeredBusinessName">
					<div class="flex-align-center">
						<el-input
							class="W90"
							placeholder="客户工商注册名称"
							v-model="editForm.registeredBusinessName"
							:disabled="!showChekedBtn"
						></el-input>
						<el-button class="ml10" :disabled="!showChekedBtn" type="primary" @click.stop="checkedName">检查</el-button>
					</div>
				</el-form-item>
				<el-form-item label="客户简称" prop="abbreviation">
					<el-input placeholder="客户简称" v-model="editForm.abbreviation" :maxlength="6"></el-input>
				</el-form-item>
				<el-form-item label="所在地区" prop="regions">
					<el-cascader
						class="W100"
						clearable
						filterable
						:options="regionData"
						v-model="editForm.regions"
						@change="regionChange"
						placeholder="所在地区"
					>
					</el-cascader>
				</el-form-item>
				<el-form-item label="联系人" prop="linkman">
					<el-input placeholder="联系人名" v-model="editForm.linkman"></el-input>
				</el-form-item>
				<el-form-item label="电话" prop="linkphone">
					<el-input placeholder="联系人的电话号码" v-model="editForm.linkphone" @change="checkContactInfo"></el-input>
				</el-form-item>
				<el-form-item label="业务顾问" prop="salesman">
					<el-select
						class="W100"
						v-model="editForm.salesman"
						placeholder="业务顾问"
						@change="getHosterList"
						default-first-option
						clearable
						filterable
					>
						<el-option disabled v-show="!editForm.twid" value=" ">
							<span class="orange fs-14">请选择对应的分销/代理后再操作！</span>
						</el-option>
						<el-option
							v-show="editForm.twid"
							v-for="item in salesmanList"
							:key="item.auid"
							:label="item.userName"
							:value="item.auid"
						>
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="托管方" prop="hosterTwid" v-show="hosterList.length >= 1">
					<el-select class="W100" v-model="editForm.hosterTwid" placeholder="托管方" clearable>
						<el-option v-for="item in hosterList" :key="item.twid" :label="item.teamworkName" :value="item.twid"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="客户需求" prop="clientNeed">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						placeholder="客户需求"
						v-model="editForm.clientNeed"
					></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button v-show="!showChekedBtn" type="primary" @click="saveEdit">保 存</el-button>
			</el-row>
		</el-dialog>
		<!-- 展期申请弹窗 -->
		<el-dialog :visible.sync="dialogSpread" width="600px" :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="8vw" label-position="left" ref="editFormRef" :rules="editRules">
				<el-form-item label="客户工商注册名称" prop="registeredBusinessName">
					<el-col :span="24">
						<span> {{ editForm.registeredBusinessName }}</span>
					</el-col>
				</el-form-item>
				<el-form-item label="客户简称" prop="abbreviation">
					<span> {{ editForm.abbreviation }}</span>
				</el-form-item>
				<el-form-item label="所在地区" prop="region">
					<span> {{ editForm.region }}</span>
				</el-form-item>
				<el-form-item label="当前保护期" prop="protectDeadline">
					<span> {{ dateFormat(editForm.protectDeadline, 'lineM') }}</span>
				</el-form-item>
				<el-form-item label="展期至" prop="spreadTime">
					<el-date-picker
						v-model="editForm.spreadTime"
						type="date"
						class="W100"
						placeholder="展期结束时间"
						value-format="timestamp"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						placeholder="展期备注"
						v-model="editForm.remark"
					></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveSpread">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
import { regionData, CodeToText, TextToCode } from 'element-china-area-data';

import ChannelSelect from '@/components/ChannelSelect.vue';

export default {
	components: {
		DateSelect,
		ChannelSelect,
	},
	name: 'customerRecord',
	data() {
		return {
			uid: '',
			regionData, //中国地区数据
			activeTab: 'customerRecord',
			queryStr: '',
			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'left', width: '' },
				{ colName: '客户编号', colNo: 'clientNo', align: 'left', width: '' },
				{ colName: '客户工商注册名称', colNo: 'registeredBusinessName', align: 'left', width: '170' },
				{ colName: '备案方', colNo: 'teamwork', align: 'left', width: '' },
				{ colName: '备案人', colNo: 'userName', align: 'left', width: '' },
				{ colName: '托管方', colNo: 'hosterName', align: 'left', width: '' },
				{ colName: '备案日期', colNo: 'keepRecordTime', align: 'center', width: '' },
				{ colName: '保护期限', colNo: 'protectDeadline', align: 'center', width: '' },
				{ colName: '剩余有效天数', colNo: 'surplusValidDay', align: 'right', width: '120' },
				{ colName: '状态', colNo: 'status', align: 'center', width: '' },
				{ colName: '最近跟单日期', colNo: 'recentlyDocumentaryTime', align: 'center', width: '120' },
				{ colName: '沉默天数', colNo: 'silenceDay', align: 'right', width: '' },
				{ colName: '业务顾问', colNo: 'salesmanName', align: 'left', width: '' },
			],
			searchForm: {
				status: '',
				spreadStatus: '',
				twidList: [],
				channelName: '',
			},
			hosterList: [], //托管方列表
			salesmanList: [], //业务顾问列表
			dialogEdit: false,
			dialogSpread: false,
			editForm: {
				abbreviation: '',
				clientNeed: '',
				linkman: '',
				linkphone: '',
				region: '',
				regions: [],
				registeredBusinessName: '',
				salesman: '',
				hosterTwid: '',
				twid: '',
			},
			showChekedBtn: true,
			dialogTitle: '客户详情',
			editRules: {
				twid: [{ required: true, message: '分销/代理', trigger: 'change' }],
				abbreviation: [{ required: true, message: '客户简称', trigger: 'blur' }],
				clientNeed: [{ required: true, message: '客户需求', trigger: 'blur' }],
				linkman: [{ required: true, message: '请输入对接人信息', trigger: 'blur' }],
				linkphone: [{ required: true, message: '请输入对接人联系方式', trigger: 'blur' }],
				region: [{ required: true, message: '客户地址', trigger: 'blur' }],
				regions: [{ required: true, message: '客户地址', trigger: 'blur' }],
				salesman: [{ required: true, message: '请输入业务顾问', trigger: 'change' }],
				registeredBusinessName: [{ required: true, message: '客户工商注册名称', trigger: 'blur' }],
				protectDeadline: [{ required: true, message: '请输入备案保护日期', trigger: 'change' }],
				spreadTime: [{ required: true, message: '请输入展期日期', trigger: 'change' }],
			},

			// 询盘
			rowData: {},
			titleName: '',
			openMove: '',
			showMap: {
				InquiryDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'teamWorkList']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		'editForm.salesman'(val) {
			if (!val) {
				this.hosterList.length = 0;
				this.editForm.hosterTwid = '';
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {
		const len = this.regionData.length;
		if (this.regionData[len - 1].label != '其他') {
			this.regionData.push({
				// children: [],
				label: '其他',
				value: '0',
			});
		}
	},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.uid = this.$route?.query?.queryId || '';
		this.queryTableData();
		this.getHosterList(this.userInfos?.adminUserVO.auid, 'init');
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 询盘详情
		openDetail(row) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom('修改', row);
			});
		},
		openDialog(row, title) {
			this.editForm = _.deepClone(row);
			this.dialogTitle = title;
			this.dialogSpread = true;
		},
		checkedName() {
			if (!this.editForm.twid) {
				this.$message.warning('分销/代理后再操作！');
				return;
			}
			const str = JSON.stringify({
				registeredBusinessName: this.editForm.registeredBusinessName,
				twid: this.editForm.twid,
			});
			this.$axios
				.checkBusinessName(str)
				.then(res => {
					if (res.data.success) {
						if (res.data.data.keepRecordStatus == 0) {
							// 未备案
							this.$message.success('检查成功，该客户可备案！');
							this.showChekedBtn = false;
						} else {
							// 1
							this.$confirm(res.data.data.keepRecordCodition, '重要提示', {
								confirmButtonText: '确定',
								cancelButtonText: '取消',
							})
								.then(() => {
									this.editForm.registeredBusinessName = '';
								})
								.catch(() => {
									this.editForm.registeredBusinessName = '';
								});
						}
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('checkBusinessName |' + error);
				});
		},
		// 检查联系方式是否存在
		checkContactInfo: _.debounce(function (linkphone) {
			this.$axios
				.checkPhone(JSON.stringify({ contactInfo: linkphone }))
				.then(res => {
					if (res.data.success) {
						// 未存在该联系方式
					} else {
						if (linkphone) {
							this.$confirm('注意,该联系方式已存在, 是否继续操作?', '提示', {
								confirmButtonText: '确定',
								cancelButtonText: '取消',
								type: 'warning',
								dangerouslyUseHTMLString: true,
								message: `
									<p>注意,该联系方式"${linkphone}"已存在, 是否继续操作?</p>
									<strong class="red fs-16">归属：${res.data.message}</strong>
								`,
							})
								.then(() => {
									this.$message.success('您已悉知，请继续操作!');
								})
								.catch(() => {
									// this.editForm.linkphone = '';
									this.$message.info('您已取消，请重新输入新联系方式！');
								});
						}
					}
				})
				.catch(error => {
					console.log('checkPhone |' + error);
				});
		}),
		// 全国地区代码转文本
		regionChange(region) {
			let loc = '';

			if (!region) {
				this.editForm.region = '';
				return;
			}
			// 其他
			if (region && region.length == 1) {
				this.editForm.region = '其他';
				return;
			}

			Array.from(region)?.map((item, index) => {
				if (index < region.length - 1) {
					loc += CodeToText[item] + '-';
				} else {
					loc += CodeToText[item];
				}
			});

			this.editForm.region = loc;
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const str = JSON.stringify({
				...this.dateSelectObj,
				...this.searchForm,
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				query: this.queryStr,
			});
			this.$axios
				.selectKeepRecordClient(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectKeepRecordClient |' + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 添加客户
		addCustomer() {
			this.editForm = {
				abbreviation: '',
				clientNeed: '',
				linkman: '',
				linkphone: '',
				region: '',
				regions: [],
				registeredBusinessName: '',
				salesman: '',
				twid: '',
			};
			this.hosterList = [];
			if (this.teamWorkList.length == 1) {
				this.editForm.twid = this.teamWorkList[0].twid;
				this.changeChannel();
			}
			this.dialogEdit = true;
			this.showChekedBtn = true;
			this.dialogTitle = '添加客户';
		},
		// 删除行
		deleteRow(row) {
			if (!row) {
				return;
			}
			const msg =
				'当前选择的客户【' +
				(row.abbreviation ? row.abbreviation : '') +
				'】被删除后将失效，失效后可被重新备案，是否确定继续执行该操作？';

			this.$confirm(msg, '重要提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteKeepRecordClient(
							JSON.stringify({
								ckrid: row.ckrid,
							}),
						)
						.then(res => {
							if (res.data.success) {
								this.$succ('客户已失效!');
								this.queryTableData();
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteKeepRecordClient |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		closeDialog() {
			this.dialogEdit = false;
			this.dialogSpread = false;
			this.showChekedBtn = true;
			this.editForm = _.resetValues(this.editForm);
			this.$refs.editFormRef.resetFields();
		},
		// 保存客户
		saveEdit() {
			const { twid, abbreviation, clientNeed, linkman, linkphone, region, registeredBusinessName, salesman, hosterTwid } =
				this.editForm;

			if (!twid) {
				this.$message.warning('分销/代理后再操作！');
				return;
			}
			if (this.showChekedBtn) {
				this.$message.warning('请检查客户工商注册名称是否已经备案！');
				return;
			}
			if (!registeredBusinessName) {
				this.$message.warning('客户工商注册名称！');
				return;
			}
			if (!abbreviation) {
				this.$message.warning('客户简称！');
				return;
			}
			if (!region) {
				this.$message.warning('客户所在地区');
				return;
			}
			if (!linkman || !linkphone) {
				this.$message.warning('请将客户联系人/联系方式信息补充完整！');
				return;
			}
			if (!salesman) {
				this.$message.warning('请将业务顾问补充完整！');
				return;
			}

			if (!clientNeed) {
				this.$message.warning('请将客户需求补充完整！');
				return;
			}
			// 添加
			this.$axios
				.addKeepRecordClient(
					JSON.stringify({
						abbreviation,
						clientNeed,
						linkman,
						linkphone,
						region,
						registeredBusinessName,
						salesman,
						twid,
						hosterTwid,
					}),
				)
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功！');
						this.closeDialog();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addKeepRecordClient |' + error);
				});
		},
		// 保存展期
		saveSpread() {
			if (!this.editForm.spreadTime) {
				this.$message.warning('请选择需延长到的展期日期！');
				return;
			}

			const API = 'applyLengthenSpread';
			this.$axios[API](
				JSON.stringify({
					ckrid: this.editForm.ckrid,
					remark: this.editForm.remark,
					spreadTime: this.editForm.spreadTime,
				}),
			)
				.then(res => {
					if (res.data.success) {
						this.$succ('保存成功!');
						this.closeDialog();
						this.queryTableData(1);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},

		changeChannel() {
			if (!this.editForm.twid) {
				return;
			}
			const str = JSON.stringify({
				twid: this.editForm.twid,
				counselor: '业务',
			});
			this.$axios
				.selectSalesmanByTwid(str)
				.then(res => {
					if (res.data.success) {
						this.editForm.salesman = '';
						// this.editForm.hosterTwid = '';
						this.$set(this.editForm, 'hosterTwid', '');
						this.salesmanList = [];
						this.hosterList = [];
						this.showChekedBtn = true;
						if (res.data.data.length > 0) {
							this.salesmanList = res.data.data;
						}
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwid |' + error);
				});
		},
		// 获取托管方列表
		getHosterList(value, type) {
			if (!value) {
				return;
			}
			const str = JSON.stringify({ auid: value });
			this.$axios
				.selectHoster(str)
				.then(res => {
					if (res.data.success) {
						this.hosterList = res.data.data.twidAndNameList || [];
						if (type == 'init') {
							// 如果不存在托管方 则报表去掉该列
							this.hosterList.length == 0 && this.tableColumn.splice(6, 1);
						}
					}
				})
				.catch(error => {
					console.log('selectHoster |' + error);
				});
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序规则
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs.uTableRef.reloadData(sortedData);
			} else {
				this.$refs.uTableRef.reloadData(this.tableData);
			}
		},
	},
};
</script>
<style lang="scss" scoped>
#customerRecord {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
