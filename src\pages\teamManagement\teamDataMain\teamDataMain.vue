<template>
	<div id="teamDataMain">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 团队详情 -->
		<TeamDetail ref="TeamDetail" :noLimit="true" @refresh="queryTableData" />
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			type="team"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twidList = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab">
			<el-tab-pane label="团队主数据" name="teamDataMain">
				<BaseLayout>
					<template #header>
						<span class="search-label">启用日期</span>
						<DateSelect
							@change="
								searchForm.startDate = $event.startTime;
								searchForm.endDate = $event.endTime;
								queryTableData(1);
							"
						/>

						<span class="search-label">剩余天数小于</span>
						<el-input
							v-model="searchForm.surplusDay"
							type="number"
							:min="0"
							@input="
								searchForm.surplusDay = /(^[0-9]\d*$)/.test(searchForm.surplusDay) ? searchForm.surplusDay : ' ';
								queryTableData(1);
							"
							@mousewheel.native.prevent
							class="vw8"
							placeholder="输入天数"
							size="small"
							clearable
						></el-input>
						<SearchHistoryInput
							name="teamName"
							placeholder="团队简称"
							v-model.trim="searchForm.query"
							@input="queryTableData(1)"
						/>

						<el-checkbox-group v-model="searchForm.version" @change="queryTableData(1)">
							<el-checkbox :label="2">OEE</el-checkbox>
							<el-checkbox :label="1">标准版</el-checkbox>
							<el-checkbox :label="4">微信版</el-checkbox>
						</el-checkbox-group>

						<el-checkbox-group v-model="searchForm.status" @change="queryTableData(1)">
							<el-checkbox :label="1">正式运行</el-checkbox>
							<el-checkbox :label="2">试用</el-checkbox>
						</el-checkbox-group>
						<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新 </el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<el-button type="text" class="icon-third-bt_newdoc" @click="openDetail(null)">添加</el-button>
							<ExportBtn @trigger="openExport" />
							<el-button type="text" class="icon-third_searchC" v-popover:thSearch>查找</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" align="center" type="index"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['validFrom', 'validTo'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<Tooltips
										v-else-if="['updateTime', 'createTime'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'ALL')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 编号 -->
									<Tooltips
										v-else-if="item.colNo == 'teamCode'"
										class="hover-green green"
										@click.native="openDetail(scope.row)"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<!-- 使用版本 -->
									<Tooltips
										v-else-if="item.colNo == 'version'"
										:cont-str="versionMap[scope.row.version]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:cont-str="statusMap[scope.row.status]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 剩余天数 -->
									<Tooltips
										v-else-if="item.colNo == 'surplusDay'"
										:class="[scope.row.surplusDay < 10 ? 'red' : '']"
										:cont-str="scope.row.surplusDay"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>

							<u-table-column label="" width="60" align="right" fixed="right">
								<template slot-scope="scope">
									<el-button type="text" class="el-icon-edit-outline" @click="openDetail(scope.row)"></el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>

		<!-- 放大镜查询 -->
		<el-popover v-model="searchPopver" ref="thSearch" placement="bottom-end" width="400" :visible-arrow="false">
			<!-- 放大镜搜索表单 -->
			<div class="searchPop p0 fs-12">
				<div class="searchPop-header">
					<span>查找条件</span>
					<i class="searchPop-header-close el-icon-close" @click.stop="searchPopver = false"></i>
				</div>
				<el-form :model="searchForm" label-width="80px" label-position="left" class="W100 pt10 pr20">
					<el-form-item label="编号" prop="teamCode">
						<el-input placeholder="请输入编号" v-model="searchForm.teamCode" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="团队简称" prop="teamName">
						<el-input placeholder="请输入团队简称" v-model="searchForm.teamName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="区域" prop="region">
						<el-input placeholder="请输入区域" v-model="searchForm.region" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="设备标签" prop="machineLabel">
						<el-input placeholder="请输入设备标签" v-model="searchForm.machineLabel" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="产品标签" prop="productLabel">
						<el-input placeholder="请输入产品标签" v-model="searchForm.productLabel" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="实施顾问" prop="consultantName">
						<el-input placeholder="请输入实施顾问" v-model="searchForm.consultantName" size="mini"></el-input>
					</el-form-item>
					<el-form-item label="业务顾问" prop="salesName">
						<el-input placeholder="业务顾问" v-model="searchForm.salesName" size="mini"></el-input>
					</el-form-item>
				</el-form>
				<div class="mt20 border-top text-right">
					<el-button type="text" class="color-666" @click.stop="queryTableData(1)">确定</el-button>
				</div>
			</div>
		</el-popover>
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
import ExportTable from '@/components/ExportTable';
import ChannelSelect from '@/components/ChannelSelect.vue';
import TeamDetail from './TeamDetail.vue';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	components: {
		DateSelect,
		ExportTable,
		ChannelSelect,
		TeamDetail,
		ExportBtn,
	},
	name: 'teamDataMain',
	data() {
		return {
			activeTab: 'teamDataMain',

			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '团队编号', colNo: 'teamCode', align: 'left', width: '90' },
				{ colName: '团队简称', colNo: 'teamName', align: 'left', width: '' },
				{ colName: '团队全称', colNo: 'teamFullname', align: 'left', width: '' },
				{ colName: '使用版本', colNo: 'version', align: 'left', width: '' },
				{ colName: '启用日期', colNo: 'validFrom', align: 'center', width: '90' },
				{ colName: '到期日期', colNo: 'validTo', align: 'center', width: '90' },
				{ colName: '剩余天数', colNo: 'surplusDay', align: 'right', width: '90' },
				{ colName: '年费', colNo: 'annualFee', align: 'right', width: '60' },
				{ colName: '团队管理员', colNo: 'userName', align: 'left', width: '100' },
				{ colName: '渠道', colNo: 'channelName', align: 'left', width: '' },
				{ colName: '分销/代理', colNo: 'teamworkName', align: 'left', width: '90' },
				{ colName: '业务顾问', colNo: 'salesName', align: 'left', width: '' },
				// { colName: '代理人', colNo: 'agentName', align: 'left', width: '' },
				{ colName: '实施顾问', colNo: 'consultantName', align: 'left', width: '' },
				{ colName: '设备标签', colNo: 'machineLabel', align: 'left', width: '' },
				{ colName: '产品标签', colNo: 'productLabel', align: 'left', width: '' },
				{ colName: '区域', colNo: 'region', align: 'left', width: '' },
				{ colName: '登记时间', colNo: 'createTime', align: 'center', width: '' },
				{ colName: '登记人', colNo: 'createName', align: 'left', width: '' },
				{ colName: '状态', colNo: 'status', align: 'left', width: '' },
				{ colName: '运营商剩余天数', colNo: 'remainderDays', align: 'right', width: '' },
			],
			searchForm: {
				query: '',
				surplusDay: '',

				teamCode: '',
				teamName: '',
				machineLabel: '',
				productLabel: '',
				region: '',
				version: [1, 2],
				consultantName: '',
				salesName: '',
				channelName: [],
				twidList: [],
				status: [],
				statusList: [0, 3, 6],
			},

			statusMap: {
				1: '正式运行',
				0: '禁用',
				2: '试用',
			},
			searchPopver: false,

			versionMap: {
				1: '标准版',
				2: 'OEE',
				4: '微信版',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		openDetail(row) {
			this.$refs.TeamDetail.showDetailCom(row);
		},

		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectTeam';
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTeam |' + error);
				});
		}),
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify(this.searchForm), //接口参数
				API: 'teamDownload', //导出接口
				downloadData: '团队', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),

		//日期format
		dateFormat,
	},
};
</script>

<style lang="scss">
#teamDataMain {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
