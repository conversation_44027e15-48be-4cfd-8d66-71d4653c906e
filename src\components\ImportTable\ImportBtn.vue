<template>
	<el-dropdown v-if="show">
		<el-button :type="buttonType" :class="buttonClass">
			<span>{{ buttonText }}</span>
			<i :class="iconClass"></i>
		</el-button>
		<el-dropdown-menu slot="dropdown">
			<el-dropdown-item v-for="(item, index) in dropdownItems" :key="index" @click.native="selectItem(item.type)">
				<span :class="item.icon + ' fs-12'"> {{ item.text }}</span>
			</el-dropdown-item>
		</el-dropdown-menu>
	</el-dropdown>
</template>

<script>
export default {
	name: 'ImportBtn',
	props: {
		// 是否显示导出按钮
		show: {
			type: Boolean,
			default: true,
		},
		// 按钮类型
		buttonType: {
			type: String,
			default: 'text',
		},
		// 按钮自定义类名
		buttonClass: {
			type: String,
			default: 'icon-third_pl',
		},
		// 按钮文本
		buttonText: {
			type: String,
			default: '导入',
		},
		// 图标类名
		iconClass: {
			type: String,
			default: 'el-icon-arrow-down m0',
		},
		// 下拉菜单项
		dropdownItems: {
			type: Array,
			default: () => [
				{ text: '导入文件', icon: 'el-icon-upload', type: 'import' },
				{ text: '下载模板', icon: 'el-icon-download', type: 'download' },
			],
		},
	},
	methods: {
		// 选择菜单项
		selectItem(type = 'import') {
			this.$emit('trigger', type);
		},
	},
};
</script>
