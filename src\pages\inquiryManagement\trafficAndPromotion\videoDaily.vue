<template>
	<div class="videoDaily">
		<!-- 流量报工 -->
		<DetailCom ref="DetailComRef" @close="queryTableData(1)" />

		<BaseLayout>
			<template #header>
				<span class="search-label">月份</span>
				<el-date-picker
					size="small"
					v-model="selectTime"
					:default-value="selectTime"
					type="month"
					value-format="timestamp"
					format="yyyy 年 MM 月"
					placeholder="请选择月份"
					:clearable="false"
					@change="changeDateSelect"
				>
				</el-date-picker>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-minus" @click="$refs.PointsDialog.open()"> 不规范扣分 </el-button>
					<el-button type="text" class="el-icon-plus" @click="openDetail('添加', null)"> 流量报工 </el-button>
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
				</div>
			</template>
			<template #main>
				<div class="table-toolbar"> </div>
				<u-table ref="uTableRef" class="table-main" :height="1200" :row-height="45" stripe :span-method="cellMergre">
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						:fixed="item.fixed && needFixed"
						resizable
					>
						<template slot-scope="scope">
							<div
								v-if="
									item.colNo != 'userName' &&
									item.colNo != 'accountName' &&
									item.colNo != 'indicatorName' &&
									item.colNo != 'total' &&
									item.colNo
								"
							>
								<Tooltips
									:cont-str="getColumnValue(item.colNo, scope.row.adminVideoTotalVO)"
									:cont-width="(scope.column.width || scope.column.realWidth) - 18"
								>
								</Tooltips>
							</div>
							<!-- 合计 -->
							<Tooltips
								v-else-if="item.colNo == 'accountName' && scope.row[item.colNo]"
								class="accountName-title"
								:cont-str="formatAccountNames(scope.row[item.colNo])"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
		<PointsDialog ref="PointsDialog" :userList="userList" @update="queryTableData(1)" />
	</div>
</template>

<script>
import { debounce, jointString, sortTableData } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DetailCom from './videoDailyReporting.vue';
import PointsDialog from '@/pages/dashboardManagement/ManagerDashboard/PointsDialog.vue'; //个人积分（质量评价）
export default {
	name: 'videoDaily',
	components: {
		DetailCom,
		PointsDialog,
	},
	props: {},
	data() {
		return {
			selectTime: new Date(),
			today: new Date().getDate(),

			titleName: '',
			//日期相关
			startTime: '',
			endTime: '',
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			needFixed: true,
			userList: [], // 用户列表
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）

		// 列数据
		tableColumn() {
			// 使用moment根据当前月份共多少天,然后按天数显示列
			const daysInMonth = this.$moment(this.selectTime).daysInMonth();
			const tableColumn = [
				{ colName: '人员', colNo: 'userName', align: 'left', width: 123, fixed: 'left' },
				{ colName: '账号', colNo: 'accountName', align: 'left', width: 225, fixed: 'left' },
				{ colName: '指标', colNo: 'indicatorName', align: 'left', width: 123, fixed: 'left' },
				{ colName: '合计', colNo: 'total', align: 'right', width: 75, fixed: 'left' },
			];

			for (let i = 1; i <= daysInMonth; i++) {
				tableColumn.push({ colName: `${'' + i}`, colNo: `${'' + i}`, width: i > 9 ? 75 : 70, align: 'right' });
			}
			return tableColumn;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.changeDateSelect(new Date().getTime());
		this.queryUserByTwids();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			this.$axios
				.selectSalesmanByTwids(JSON.stringify({ twids: [], counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		// 获取列值
		getColumnValue(colNo, adminVideoTotalVO) {
			// console.log(':',colNo,':', adminVideoTotalVO)
			const entry = adminVideoTotalVO.find(item => parseInt(item.date, 10) === parseInt(colNo, 10));
			if (entry) {
				// console.log(entry)
				return entry.value;
			} else {
				return null;
			}
		},
		// 格式化账号名称
		formatAccountNames(accountNames) {
			if (!Array.isArray(accountNames)) {
				return accountNames; // Return as is if it's not an array
			}
			if (accountNames.length == 1) {
				return accountNames.map((accountName, index) => `${accountName}`).join('\n');
			}
			return accountNames.map((accountName, index) => `${index + 1}. ${accountName}`).join('\n');
		},
		// 单元格合并
		cellMergre({ row, column, rowIndex, columnIndex }) {
			// 针对前两列（columnIndex 0、1）进行操作
			if (columnIndex >= 0 && columnIndex <= 1) {
				let rowspan = 1;
				const colspan = 1;

				// 行合并逻辑
				// 检查前一行是否有相同的值
				const previousRow = rowIndex > 0 ? this.tableData[rowIndex - 1] : null;
				// console.log(previousRow)
				if (previousRow && previousRow[column.property] === row[column.property]) {
					return [0, 0]; // 如果与前一行相同，隐藏当前单元格
				}

				// 查找后续相同的行数
				for (let i = rowIndex + 1; i < this.tableData.length; i++) {
					if (this.tableData[i][column.property] === row[column.property]) {
						rowspan++;
					} else {
						break;
					}
				}

				// // 列合并逻辑
				// // 检查前一列是否有相同的值
				// if (columnIndex > 0) {
				// 	const previousCol = this.columns[columnIndex - 1];
				// 	if (
				// 		previousCol &&
				// 		row[previousCol.property] === row[column.property]
				// 	) {
				// 		return [0, 0]; // 如果与前一列相同，隐藏当前单元格
				// 	}
				// }

				// // 查找后续相同的列数
				// for (let i = columnIndex + 1; i < this.columns.length; i++) {
				// 	const currentCol = this.columns[i];
				// 	if (
				// 		currentCol &&
				// 		row[currentCol.property] === row[column.property]
				// 	) {
				// 		colspan++;
				// 	} else {
				// 		break;
				// 	}
				// }

				return [rowspan, colspan];
			}
			// 对其他列不进行合并处理
			return [1, 1];
		},

		jointString, //拼接字符串
		// 打开明细
		openDetail(type, row) {
			this.$refs.DetailComRef.showDetailCom(type, row);
		},
		// 修改日期
		changeDateSelect(date) {
			const year = new Date(date).getFullYear();
			const month = new Date(date).getMonth();

			this.startTime = new Date(year, month, '1').getTime();
			this.endTime = new Date(year, month + 1, '1').getTime() - 1;
			// console.log(date , year, month);
			this.needFixed = false;
			this.queryTableData();
			setTimeout(() => {
				this.needFixed = true;
			}, 1500);
		},
		//自定义排序(新增/覆盖比较逻辑)
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop !== 'userName') {
						if (!a?.score) return -1;
						if (!b?.score) return 1;
						return a.score - b.score;
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs?.uTableRef?.reloadData(sortedData);
				console.log(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		// 获取bug数数据
		queryTableData: debounce(function (type) {
			const str = JSON.stringify({
				startDate: this.startTime,
				endDate: this.endTime,
			});
			this.$axios
				.selectReportTableReport(str)
				.then(res => {
					if (res.data.success) {
						// 将接口数据打平后返回列表数据
						const originalData = res.data.data;
						// 转换成更简洁的列表形式，每个指标（如“询盘量”、“投流费用”等）都作为一个独立的对象
						const videoIndicators = [
							'inquiryTotal',
							'todayMoneyAmount',
							'videoAccountPlayCount',
							'douyinPlayCount',
							'playTotal',
							'editingCount',
							'publishingCount',
							'points',
						];

						const nonVideoIndicators = ['inquiryTotal', 'todayMoneyAmount', 'points'];

						const indicatorNames = {
							inquiryTotal: '询盘量',
							todayMoneyAmount: '投流费用',
							videoAccountPlayCount: '视频账号播放量',
							douyinPlayCount: '抖音播放量',
							playTotal: '总播放量',
							editingCount: '剪辑量',
							publishingCount: '发布量',
							points: '积分',
						};

						const transformedData = [];
						// 遍历每个原始数据条目
						originalData.forEach(entry => {
							const { userName, accountName, total, adminVideoTotalVO, category } = entry;

							let indicatorsToExtract;
							//根据 category 是否包含 "视频类" 来决定提取哪些指标。如果不包含 "视频类"，我们只提取 inquiryTotal, advertisingCostTotal, 和 points 这三个指标。
							if (userName === '合计') {
								videoIndicators.unshift('videoInquiryTotal');
								indicatorNames.videoInquiryTotal = '视频询盘量';
								indicatorNames.inquiryTotal = '总询盘量';
								indicatorsToExtract = videoIndicators;
							} else if (category?.some(cat => cat.categoryName === '视频类')) {
								indicatorsToExtract = videoIndicators;
							} else {
								indicatorsToExtract = nonVideoIndicators;
							}
							// 对于每个需要提取的指标，创建一个新的对象，并从 total 和 adminVideoTotalVO 中提取相应的值
							indicatorsToExtract.forEach(indicator => {
								const dataEntry = {
									userName,
									accountName,
									indicatorName: indicatorNames[indicator],
									total: total?.[indicator] || null,
									adminVideoTotalVO: adminVideoTotalVO.map(day => ({
										date: day.date ? new Date(day.date).getDate() : null,
										value: day?.[indicator] || null,
									})),
								};

								transformedData.push(dataEntry);
							});
						});

						// console.log(JSON.stringify(transformedData, null, 2));

						this.tableData = transformedData;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						// this.tablePageForm.total = res.data.totalItems;

						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('consultingEvaluationReport |' + error);
				});
		}),
	},
};
</script>
<style lang="scss">
.videoDaily {
	.accountName-title {
		white-space: pre-wrap; /* Allows whitespace to wrap as defined */
		word-wrap: break-word; /* Breaks long words if necessary */
		overflow-wrap: break-word; /* Ensures long words are broken */
	}
	.table-wrapper .table-main td {
		height: 45px !important;
		padding: 10px 0;
		font-size: 12px;
		border-top: 1px solid #e9e9e9 !important;
		border-right: 1px solid #e9e9e9 !important;
	}
	// .plTableBox .el-table__fixed-footer-wrapper tbody td {
	// 	background-color: transparent !important;
	// }
	// .table-wrapper .table-main td .cell {
	// 	overflow: visible;
	// }
	// .table-wrapper .table-main td {
	// 	height: 45.28px !important; //高度被badge撑开，需要调整
	// }
	.selectedCol {
		background: #0d8dc9 !important;
	}
}
</style>
