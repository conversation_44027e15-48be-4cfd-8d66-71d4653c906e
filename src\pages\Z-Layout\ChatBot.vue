<template>
	<div class="chat-bot">
		<div class="chat-bot-header">
			<el-button type="text" class="el-icon-position" @click="openBlank"></el-button>
			<el-button type="text" class="el-icon-close" @click="close"></el-button>
		</div>
		<iframe
			v-if="isHttp && !isError"
			class="W100 H100 min-h-300"
			:src="chatBotUrl"
			frameborder="0"
			allow="microphone"
			@load="onLoad"
			@error="onError"
		>
		</iframe>
		<div v-else class="W100 H100 min-h-300 flex-center flex-column gap-30">
			<p class="text-center"> 抱歉，当前环境不支持运行【树字运营平台+DeepSeekR1】 🤷‍♂️🤷‍♀️ </p>
			<el-button class="w-100" type="primary" @click="close">退 出</el-button>
		</div>
	</div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
	name: 'ChatBot',
	components: {},
	computed: {
		...mapGetters(['showChatBot']),
		// 判断当前环境是否http
		isHttp() {
			return window.location.protocol === 'http:';
		},
	},
	data() {
		return {
			chatBotUrl: 'http://*************:3030/chat/Vca7LZPwIQBy53zd',
			isError: false,
		};
	},
	methods: {
		// 关闭聊天机器人
		close() {
			this.$store.commit('setShowChatBot', false);
		},
		// 打开聊天机器人
		openBlank() {
			window.open(this.chatBotUrl, '_blank');
		},
		// 加载成功
		onLoad(e) {
			this.isError = false;
			console.log(e);
		},
		// 错误处理
		onError(e) {
			this.isError = true;
			console.log(e);
		},
	},
};
</script>

<style lang="scss" scoped>
.chat-bot {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;

	color: #666;
	font-size: 14px;

	.chat-bot-header {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		gap: 10px;
		padding: 5px 20px;
		border-bottom: 1px solid #e6e6e6;

		.el-button--text {
			color: #475467;
			padding: 0;
			margin: 0;
			&:hover {
				font-weight: 600;
			}
		}
	}
}
</style>
