/* 成交管理API */

const urlList = [
	// 合同/交付管理
	'/background/web/DeliverManagementController/selectDeliverManagement', // 查询交付/合同管理列表
	'/background/web/DeliverManagementController/selectInquiryDocumentaryDetail', // 根据询盘编号模糊查询信息
	'/background/web/DeliverManagementController/selectDeliverManagementDetail', // 查询合同/交付管理详情
	'/background/web/DeliverManagementController/exportContractManager', // 合同管理导出
	'/background/web/DeliverManagementController/exportDeliverManager', // 交付管理导出
	'/background/web/DeliverManagementController/addDeliverManagement', // 添加合同
	'/background/web/DeliverManagementController/addOrUpdateStage', // 添加/修改项目阶段
	'/background/web/DeliverManagementController/deleteStage', // 删除项目阶段
	'/background/web/DeliverManagementController/releaseProcess', // 发布交付过程
	'/background/web/DeliverManagementController/updateReleaseProcess', // 修改交付过程
	'/background/web/DeliverManagementController/deliverDaily', // 交付日报
	'/background/web/DeliverManagementController/extensionQuery', // 计划完工月延期查询
	'/background/web/DeliverManagementController/extensionRequest', // 计划完工月延期申请
	'/background/web/DeliverManagementController/releaseProcessFileSave', // 交付过程文件保存
	'/background/web/DeliverManagementController/releaseProcessFileDelete', // 交付过程文件删除
	'/background/web/DeliverManagementController/uploadStageCompleteInfo', // 上传阶段完工信息
	'/background/web/DeliverManagementController/deleteStageCompleteInfo', // 删除阶段完工信息
	'/background/web/DeliverManagementController/selectStageComplete', // 查询阶段完工信息
	'/background/web/DeliverManagementController/deliveryRiver', // 交付河流图
	'/background/web/DeliverManagementController/selectDeliverRecordByUidAndTime', // 根据时间以及用户查询交付记录
	'/background/web/DeliverManagementController/addDeliverManagementProject', // 添加/修改交付管理项目阶段详情
	'/background/web/DeliverManagementController/selectDeliverManagementInfo', //  查询交付管理项目阶段详情
	'/background/web/DeliverManagementController/deleteDeliverManagementProject', //  查询交付管理项目阶段详情
	'/background/web/DeliverManagementController/selectDeliverManagementByStages', // 判断是否有未规划计划的阶段
	'/background/web/DeliverManagementController/deliverManagementApprove', // 合同审核
	'/background/web/DeliverManagementController/deliverManagementSubmitApprove', // 提交或撤销合同审核
	'/background/web/DeliverManagementController/updateDeliverManagementCloseStatus', // 更新合同关闭状态
	'/background/web/DeliverManagementController/updateDeliverStageManagementCloseStatus', // 更新合同阶段关闭状态
	'/background/web/DeliverManagementController/updateDeliverManagementRecord', // 合同录入状态
	// 延期申请
	'/background/web/DeliverManagementController/selectExtensionList', // 延期申请列表
	'/background/web/DeliverManagementController/approval', // 延期申请审批
	'/background/web/DeliverManagementController/exportExtensionRequest', // 延期申请导出
	// 订单管理
	'/background/web/OrderManagementController/orderManagementList', // 查询订单管理列表
	'/background/web/OrderManagementController/addOrderManagement', // 添加订单
	'/background/web/OrderManagementController/selectOrderManagementDetail', // 查询订单详情信息
	'/background/web/OrderManagementController/uploadStageCompleteInfo', // 修改订单产品信息（注意 ）
	'/background/web/OrderManagementController/deleteStageCompleteInfo', // 删除订单产品信息
	'/background/web/OrderManagementController/exportOrderManagement', // 订单管理导出
	'/background/web/OrderManagementController/collection', // 收款
	'/background/web/OrderManagementController/invoicing', // 开票
	'/background/web/OrderManagementController/collectionMonthRivers', // 收款月度汇总
	'/background/web/OrderManagementController/collectionMonthRiversByUidAndTime', // 收款月度汇总详情
	// 交付人员时间安排
	'/background/web/DeliverManagementController/webDeliveryScheduleTime', // 交付人员时间安排
	// 交付计划
	'/background/web/DeliverManagementController/deliverySchedule', // 交付时间计划
	// 产品清单
	'/background/web/ProductController/productList', // 产品列表
	'/background/web/ProductController/addOrUpdateProduct', // 添加/修改产品
	'/background/web/ProductController/exportProduct', // 产品导出
	'/background/web/ProductController/importProduct', // 产品导入
	// 计划模板
	'/background/web/PlanTemplateController/addPlanTemplate', // 添加计划模板
	'/background/web/PlanTemplateController/deletePlanTemplate', // 删除计划模板
	'/background/web/PlanTemplateController/selectPlanTemplate', // 查询计划模板
	'/background/web/PlanTemplateController/updatePlanTemplate', // 修改计划模板
	// 合同业绩分配
	'/background/web/DeliverStageCommissionController/addDeliverStageCommissionDetail', // 添加合同业绩分配明细对象
	'/background/web/DeliverStageCommissionController/deleteDeliverStageCommissionDetail', // 删除合同业绩分配明细对象
	'/background/web/DeliverStageCommissionController/fetchDeliverStageCommissionDetailForContract', // 查询并计算合同业绩分配
	'/background/web/DeliverStageCommissionController/selectDeliverStageCommissionDetailList', // 查询业绩分配明细
	'/background/web/DeliverStageCommissionController/updateDeliverStageCommissionDetail', // 更新合同业绩分配明细对象
	'/background/web/DeliverStageCommissionController/selectDeliverCommissionSummaryList', // 查询合同交付佣金人员级别汇总, 如果佣金没有被计算过，则返回空数组
	'/background/web/DeliverStageCommissionController/selectDeliverStageCommissionStatisticList', // 业绩结算统计
	'/background/web/DeliverStageCommissionController/selectDeliverStageCommissionDetailListForOrderMgr', // 查询业绩分配明细-收款视图
	'/background/web/DeliverStageCommissionController/refreshMonthlyAwards', //刷新月度达标奖励-收款视图
	'/background/web/DeliverStageCommissionController/addDeliverStageCommissionDetailForSettlement', //添加手工结算项
	'/background/web/DeliverStageCommissionController/selectDeliverStagePartnerSettlementPrintTable', //合伙人结算打印单，前台提供一个月的结算时间区间
	'/background/web/DeliverStageCommissionController/bpulLeadBpuCommissionMonthly', //师带徒结算
];

// 重名函数映射
const apiNameMap = {
	'/background/web/DeliverManagementController/uploadStageCompleteInfo': 'uploadStageCompleteInfoDeliver',
	'/background/web/DeliverManagementController/deleteStageCompleteInfo': 'deleteStageCompleteInfoDeliver',
};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['exportContractManager', 'exportDeliverManager'].includes(urlName)) {
		timeout = 60000;
	}

	return { urlName, url, timeout };
});

export default apiList;
