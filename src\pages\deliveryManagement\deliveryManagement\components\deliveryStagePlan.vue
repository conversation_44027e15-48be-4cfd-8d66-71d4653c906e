<template>
	<div class="deliveryStagePlan pl20 input-border-none">
		<p class="detail-content-title ml50 h-25 fs-12">阶段详细计划 </p>
		<u-table
			class="table-main expand-table ml50 W95"
			:max-height="500"
			:row-height="45"
			:data="expandTable"
			:row-style="{ height: '0', padding: '0 0' }"
			:cell-style="{ padding: '0 0', borderBottom: '1px solid #e9e9e9' }"
			:header-cell-style="{
				fontSize: '12px',
				background: '#fafafa !important',
				// border: '1px solid #e9e9e9',
				padding: '5px 0 !important',
			}"
		>
			<u-table-column label="序号" type="index" align="center" width="50"></u-table-column>
			<u-table-column
				v-for="item in tableColumn"
				:key="'colCurr' + item.colNo"
				:label="item.colName"
				:prop="item.colNo"
				:align="item.align"
				:width="item.width"
				resizable
			>
				<template slot-scope="scope">
					<div v-if="item.colNo == 'plannedTime'">
						<el-date-picker
							:disabled="scope.row.status == 4"
							class="el-date-picker"
							v-model="scope.row[item.colNo]"
							size="mini"
							type="date"
							format="yyyy-MM-dd"
							placeholder="请选择时间"
							value-format="timestamp"
							:clearable="false"
							@change="updateExpandStage(scope.$index)"
						></el-date-picker>
					</div>
					<div v-else-if="item.colNo == 'status'" class="flex-align-center W100">
						<el-button
							:type="['', 'info', 'warning', 'danger', 'primary'][scope.row.status]"
							class="p6"
							size="mini"
							@click="openDialog('状态', scope.row)"
						>
							{{ ['', '待开始', '进行中', '已延误', '已完工'][scope.row.status] }}
						</el-button>
						<!-- 附件 -->
						<div v-if="scope.row.file" class="flex ml10">
							<FilePopover class="inline-block max-w-150" trigger="click" :url="scope.row.file" :content="scope.row.fileName" />
							<i class="el-icon-circle-close pointer" size="mini" @click.stop="removeFile(scope.row, 'update')"></i>
						</div>
					</div>
					<div v-else-if="item.colNo == 'durationDays'" class="flex-align-center W100">
						<el-input
							:disabled="scope.row.status == 4"
							v-model="scope.row[item.colNo]"
							size="mini"
							placeholder="请输入内容"
							clearable
							@change="updateExpandStage(scope.$index)"
						></el-input>

						<div class="travel-info W100 ml10 flex-align-center">
							<span class="flex-align-center hover-green" @click="openDialog('出差', scope.row)">
								<i :class="scope.row.btaid ? '' : 'color-999'" class="el-icon-suitcase-1 mr5"></i>
								<Tooltips v-if="scope.row.tripUsers" class="max-w-120 fs-12" :cont-str="scope.row.tripUsers" />
							</span>
							<span v-if="scope.row.btaid" class="ml5 min-w-120 pointer fs-12">
								<span v-if="scope.row.approveStatus > 0">
									{{ ['未提交', '已提交(待审核)', '', '已审核通过', '被退回修改'][scope.row.approveStatus] }}
								</span>
								<el-button
									v-if="scope.row.approveStatus == 0 || scope.row.approveStatus == 4"
									type="text"
									size="mini"
									@click="submitApprove(scope.row)"
								>
									{{ ['提交申请', '撤回', '', '撤回', '重新提交'][scope.row.approveStatus] }}
								</el-button>
							</span>
						</div>
					</div>

					<el-select
						v-else-if="item.colNo == 'stage'"
						:disabled="scope.row.aupcid && scope.row.status == 4"
						v-model="scope.row.aupcid"
						size="mini"
						class="min-w-100"
						placeholder="选择计划任务"
						popper-class="select-column-w200-3"
						clearable
						filterable
						@change="changeScoreConfig(scope.row, scope.$index)"
					>
						<el-option v-for="item in scoreConfigList" :key="item.aupcid" :label="item.cfgName" :value="item.aupcid">
							<span> {{ `${item.cfgCate} - ${item.cfgName}` }} </span>
							<span :class="item.cfgHasFacilitator ? 'el-icon-user ml10' : ''"> </span>
							<span :class="item.cfgAttachments ? 'el-icon-paperclip ml10' : ''"> </span>
						</el-option>
					</el-select>

					<div v-else>
						<el-input
							:disabled="scope.row.status == 4"
							:maxlength="6"
							v-model="scope.row[item.colNo]"
							size="mini"
							placeholder="请输入内容"
							clearable
							@change="updateExpandStage(scope.$index)"
						></el-input>
					</div>
				</template>
			</u-table-column>
			<u-table-column width="200" align="right">
				<template #header>
					<div class="flex-align-center">
						<!-- <el-button class="ml-auto" type="text" @click="deleteExpandRow(expandTable)">清除</el-button> -->
						<!-- <el-button type="text" @click="openDialog('参考', null)">参考</el-button> -->
					</div>
					<el-button type="text" @click="addExpandRow(expandTable.length)">添加一行</el-button>
				</template>
				<template slot-scope="scope">
					<span v-if="scope.row.mark" class="fs-12 orange"> * 行业案例</span>
					<el-button
						:disabled="scope.row.status == 4"
						type="text"
						class="el-icon-delete"
						@click="deleteExpandRow([scope.row])"
					></el-button>
					<el-button type="text" class="el-icon-plus" @click="addExpandRow(scope.$index + 1)"></el-button>
				</template>
			</u-table-column>
		</u-table>

		<!-- 完工弹窗 -->
		<el-dialog
			width="500px"
			:visible.sync="dialogEdit"
			:close-on-click-modal="false"
			:append-to-body="true"
			@close="closeDialog"
			v-loading.lock="isUpLoading"
			element-loading-text="正在上传文件中，请勿中途退出..."
		>
			<span slot="title">计划完工确认</span>
			<el-form :model="editForm" :rules="formRules" label-width="100px" label-position="left" @submit.native.prevent>
				<el-form-item label="计划完工日期" prop="plannedTime">
					<el-date-picker
						:class="{ 'input-border-red': editForm.plannedTime > maxTime }"
						class="el-date-picker"
						v-model="editForm.plannedTime"
						type="date"
						format="yyyy-MM-dd"
						placeholder="请选择时间"
						value-format="timestamp"
						:clearable="false"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="附件" prop="plannedTime">
					<div v-if="editForm.file" class="flex">
						<FilePopover class="inline-block max-w-300" trigger="click" :url="editForm.file" :content="editForm.fileName" />
						<i class="el-icon-circle-close pointer" size="mini" @click.stop="removeFile(editForm)"></i>
					</div>
					<el-upload
						v-else
						action=""
						accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
						:http-request="f => uploadFile(f)"
						:show-file-list="false"
					>
						<el-button type="primary" class="p8" size="mini"> 上传附件 </el-button>
					</el-upload>
				</el-form-item>
				<!-- <el-form-item label="" prop="mark">
					<el-checkbox v-model="editForm.mark" :disabled="!editForm.file" :true-label="1" :false-label="0">
						<span>行业案例</span>
						<span v-show="editForm.mark && !editForm.file">(请先上传附件)</span>
					</el-checkbox>
				</el-form-item> -->
				<!-- <el-form-item label="完工状态" prop="status">
					<el-checkbox v-model="editForm.status" :true-label="4" :false-label="2">
						<span>确认完工</span>
					</el-checkbox>
				</el-form-item> -->
			</el-form>

			<span slot="footer">
				<el-button v-show="editForm.status === 4" type="info" @click="saveEdit(1)">撤销完工</el-button>
				<el-button type="primary" @click="saveEdit(4)">确认完工</el-button>
			</span>
		</el-dialog>

		<!-- 出差申请弹窗 -->
		<el-dialog
			width="888px"
			:visible.sync="dialogTravel"
			:close-on-click-modal="false"
			:append-to-body="true"
			@close="closeDialog"
		>
			<span slot="title">
				<span>出差申请</span>
				<span v-show="travelForm.btaid">{{
					'当前' + ['未提交', '已提交(待审核)', '', '已审核通过', '被退回修改'][travelForm.approveStatus]
				}}</span>
			</span>

			<el-form :model="travelForm" :rules="travelFormRules" label-width="100px" label-position="left" @submit.native.prevent>
				<div class="flex-justify-between">
					<el-form-item label="客户" prop="tripClientName">
						<Tooltips :cont-str="travelForm.tripClientName" />
					</el-form-item>
					<el-form-item class="W30 ml20" label="承担方" prop="expenseParty">
						<!-- <Tooltips class="pl10" :cont-str="expensePartyMap[travelForm.expenseParty]" :cont-width="100" /> -->
						<el-select v-model="travelForm.expenseParty" placeholder="承担方" clearable filterable>
							<!-- <el-option v-for="[key, value] in Object.entries(expensePartyMap)" :key="key" :label="value" :value="Number(key)">
							</el-option> -->
							<el-option v-for="item in expensePartyOptions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
						</el-select>
					</el-form-item>
				</div>
				<el-form-item label="目的地" prop="tripDestination">
					<el-autocomplete
						ref="cautocomplete"
						class="W100"
						v-model="travelForm.tripDestination"
						placeholder="请输入/点击选择目的地"
						clearable
						:debounce="500"
						:fetch-suggestions="querySearch"
						@select="handleSelect"
						@clear="$refs.cautocomplete.activated = true"
					>
						<template slot-scope="{ item }">
							<span>{{ item.clientAddress }}</span>
						</template>
					</el-autocomplete>
				</el-form-item>
				<el-form-item label="事由" prop="tripReason">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						v-model="travelForm.tripReason"
						placeholder="请输入事由"
					></el-input>
				</el-form-item>
				<div class="flex-justify-between">
					<el-form-item class="W40" label="差旅类别 " prop="tripType">
						<el-select v-if="isParter" v-model="travelForm.tripType" placeholder="差旅类别" clearable filterable>
							<el-option
								v-for="item in tripTypeOptions"
								:key="item.tripType"
								:label="item.travelCategoryName"
								:value="item.tripType"
							>
								<span>{{ item.travelCategoryName }}：</span>
								<span>{{ item.expense }}元 / 天</span>
								<span v-if="item.isDealDeduct"> | 成交扣减{{ item.dealDeductExpense }}元</span>
							</el-option>
						</el-select>
						<span v-else>{{ tripTypeMap[travelForm.tripType] }}</span>
					</el-form-item>
					<el-form-item class="W25 ml20" label="开始日期" prop="tripBeginDate">
						<span>{{ dateFormat(travelForm.tripBeginDate, 'line') }}</span>
					</el-form-item>
					<el-form-item class="W35" label="出差天数" prop="tripDays">
						<el-input v-model="travelForm.tripDays" placeholder="请输入出差天数"></el-input>
					</el-form-item>
				</div>
				<el-form-item label="出差人" prop="tripUidsArray">
					<el-select v-model="travelForm.tripUidsArray" placeholder="请选择出差人" clearable filterable multiple class="W100">
						<el-option disabled v-if="tripUserList.length == 0">
							<span class="orange fs-14">当前分销/代理未与系统用户绑定，请联系管理员绑定用户后再操作！</span>
						</el-option>
						<el-option v-else v-for="item in tripUserList" :key="item.auid" :label="item.userName" :value="item.auid">
						</el-option>
					</el-select>
				</el-form-item>
				<div class="flex-justify-between">
					<el-form-item class="W45" label="申请人" prop="applyUName">
						<span>{{ travelForm.applyUName || userInfos.adminUserVO.userName }}</span>
					</el-form-item>
					<el-form-item class="W45" label="申请时间" prop="applyDate">
						<span>{{ dateFormat(travelForm.applyDate || new Date(), 'lineM') }}</span>
					</el-form-item>
				</div>
				<div v-if="travelForm.approveStatus == 1 || travelForm.approveStatus == 3" class="red text-right">
					* 当前为【{{ { 1: '已提交（待审核）', 3: '审核通过' }[travelForm.approveStatus] }}】
					状态，保存后将需要再重新进行提交申请审核的操作，请悉知！
				</div>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveTravel">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { deepClone, jointString, dateFormat, checkRequired, debounce } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';
import { projectTypeOptions, projectTypeMap, tripTypeMap, expensePartyMap } from '@/assets/js/contractSource';
export default {
	name: 'deliveryStagePlan',
	components: { FilePopover },
	props: {
		userList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	data() {
		return {
			detailForm: { dmid: '', projectType: '', deliverStageManagementVOS: [] }, //项目详情
			stageForm: { dmsid: '' }, //项目阶段详情
			editForm: {}, //任务详情

			expandTable: [], //阶段展开详情表格
			tableColumn: [
				{ colName: '计划任务', colNo: 'stage', align: 'left', width: '180' },
				{ colName: '计划完成日期', colNo: 'plannedTime', align: 'left', width: '120' },
				{ colName: '所需天数', colNo: 'durationDays', align: 'left', width: '400' },
				{ colName: '任务状态', colNo: 'status', align: 'left', width: '200' },
				{ colName: '备注', colNo: 'note', align: 'left', width: '' },
			],

			templateData: [], //计划模板
			projectTypeMap,

			dialogEdit: false,
			isUpLoading: false,
			dialogTravel: false,
			expensePartyMap, // 费用承担方
			// 出差申请弹窗表单
			tripTypeOptions: [], //差旅类别
			// 出差申请弹窗表单
			tripTypeMap, // 差旅类别
			travelForm: {
				applyMemo: '',
				applySource: '',
				btaid: '',
				dmid: '',
				dmsid: '',
				dmsiid: '',
				drid: '',
				expenseParty: '',
				idid: '',
				tripBeginDate: '',
				tripClientName: '',
				tripDays: '',
				tripDestination: '',
				tripType: '',
				tripReason: '',
				tripUids: '',
				tripUidsArray: [], //用于转换为el-select的value
				twid: '',
			},
			travelListMap: new Map(), //出差申请列表（Map）
			formRules: {
				documentaryTime: [{ required: true, message: '请选择跟单日期', trigger: 'change' }],
				content: [{ required: true, message: '请输入跟单内容', trigger: 'blur' }],
				nextStep: [{ required: true, message: '请选择计划日期', trigger: 'change' }],
				nextPlan: [{ required: true, message: '请输入计划内容', trigger: 'blur' }],
				planStatus: [{ required: true, message: '请输入计划状态', trigger: 'change' }],
			},
			travelFormRules: {
				expenseParty: [{ required: true, message: '请输入承担方', trigger: 'change' }],
				tripDestination: [{ required: true, message: '请输入目的地', trigger: 'change' }],
				tripReason: [{ required: true, message: '请输入事由', trigger: 'blur' }],
				tripType: [{ required: true, message: '请输入差旅类别', trigger: 'blur' }],
				tripDays: [{ required: true, message: '请输入出差天数', trigger: 'blur' }],
				tripUidsArray: [{ required: true, message: '请输入出差人', trigger: 'blur' }],
				tripUids: [{ required: true, message: '请输入出差人', trigger: 'blur' }],
			},
			destinationList: [], // 目的地列表

			scoreConfigList: [], // 积分配置
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userList']), //当前登录用户信息（含团队/菜单/权限等）
		// 最大日期
		maxTime() {
			// 当前阶段完工月份的最后一天
			return this.$moment(this.stageForm.complateMonth).endOf('month').startOf('day').valueOf();
		},
		// 当前用户是否合伙人
		isParter() {
			return this.userInfos.adminUserVO.userName.startsWith('8');
		},
		// 承担方列表 （如果是合伙人只返回承担方为申请人 3）
		expensePartyOptions() {
			const options = Object.entries(this.expensePartyMap).map(([key, value]) => ({ id: Number(key), name: value }));
			return this.isParter ? options.filter(item => item.id == 3) : options;
		},
		// 出差人列表
		tripUserList() {
			// 如果是合伙人，不可以勾选自己
			return this.isParter ? this.userList?.filter(item => item.auid != this.userInfos.adminUserVO.auid) : this.userList;
		},
	},
	// 监控data中的数据变化
	watch: {},
	created() {
		this.queryScoreConfig(); // 查询积分配置项
	},
	// 方法集合
	methods: {
		// 查询积分配置项（非系统）
		async queryScoreConfig() {
			const API = 'selectAdminUserPointsConfigurationList';
			try {
				const res = await this.$axios[API](JSON.stringify({ cfgGroup: '交付积分项' }));
				if (res.data.success) {
					this.scoreConfigList = res.data.data.filter(item => !item.cfgCate.includes('系统')) || [];
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 选择分配置项
		changeScoreConfig(row, index) {
			if (row.aupcid) {
				const scoreInfo = this.scoreConfigList.find(i => i.aupcid === row.aupcid);
				row.recordType = scoreInfo.cfgName;
				this.expandTable[index] = { ...row, ...scoreInfo };
				// console.log({ row }, { scoreInfo });
			} else {
				row.cfgAttachments = '';
				row.cfgHasFacilitator = '';
			}
			row.recordAttachment = '';
			row.facilitatorUidArray = [];
			row.facilitatorUids = '';
			this.updateExpandStage(index);
		},
		// 查询渠道/代理下用户（不限制范围）

		// 查询差旅类别
		queryTripTypeOptions: debounce(async function () {
			const API = 'selectTravelCategoryConfiguration';
			try {
				const res = await this.$axios[API](JSON.stringify({ cfGrouping: 0 }));
				if (res.data.success) {
					this.tripTypeOptions = res.data.data || [];
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		// 查询目的地
		async queryDestinationList() {
			if (!this.detailForm.dmid) return this.destinationList = [];
			const API = 'selectClientAddressesByContractId';
			try {
				const res = await this.$axios[API](JSON.stringify({ id: this.detailForm.dmid }));
				if (res.data.success) {
					// this.$succ(res.data.message);
					this.destinationList = res.data.data || [];
					if (this.destinationList.length && !this.travelForm.tripDestination) {
						this.travelForm.tripDestination = this.destinationList[this.destinationList.length - 1].clientAddress;
					}
				} else {
					// this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// querySearch(查询接口:xxx)
		querySearch(queryStr, cb) {
			let result = this.destinationList;
			if (queryStr) {
				result = this.destinationList?.filter(item => item.clientAddress.indexOf(queryStr) > -1) || [];
			}
			cb(result);
		},
		// handleSelect(选择时:xxx)
		handleSelect(item) {
			this.travelForm.tripDestination = item.clientAddress;
		},
		// 查询出差申请
		queryTravelData: debounce(async function (expandTable) {
			const API = 'selectDeliverBusinessTripApplicationList';
			try {
				const res = await this.$axios[API](JSON.stringify({ id: this.detailForm.dmid }));
				if (res.data.success) {
					this.travelListMap = new Map(res.data.data?.map(item => [item.dmsiid, item])) || new Map();
					if (res.data.data?.length) {
						//展开表格合并（合并出差申请信息）
						this.expandTable = expandTable?.map(aItem => {
							let newItem = { ...aItem };
							const bItem = this.travelListMap?.get(newItem.dmsiid);
							if (bItem) {
								newItem = { ...aItem, ...bItem };
								newItem.tripUsers = Object.values(bItem?.tripUsers)?.join(',') || '';
							}
							return newItem;
						});
					} else {
						this.expandTable = expandTable;
					}
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}, 200),
		// 保存修改 - 修改出差申请
		saveTravel: debounce(async function () {
			this.travelForm.tripUids = this.travelForm.tripUidsArray?.join(',') || '';

			if (checkRequired(this.travelForm, this.travelFormRules)) return; //必填项校验
			const API = this.travelForm.btaid ? 'updateBusinessTripApplication' : 'addBusinessTripApplication';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.travelForm }));
				if (res.data.success) {
					this.closeDialog();
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		// 提交差旅申请到审核状态
		async submitApprove(item) {
			const API = 'updateBusinessTripApplicationSubmitApprove';
			try {
				const res = await this.$axios[API](JSON.stringify({ id: item.btaid }));
				if (res.data.success) {
					this.queryTravelData(this.expandTable);
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 删除完工报告
		removeFile(item, type) {
			this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					item.file = '';
					item.fileName = '';
					item.mark = 0;
					const index = this.expandTable.findIndex(i => i.dmsiid == item.dmsiid);
					type == 'update' && this.updateExpandStage(index);
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 将数组转为字符串
		getArrayStr(arr) {
			if (arr && arr.length > 0) {
				return arr.map(item => item.planTask + '(' + item.needDay + '天)').join(',');
			} else {
				return '';
			}
		},

		// 打开弹窗
		openDialog(type, row) {
			if (type == '参考') {
				// this.queryTemplate(this.detailForm); //查询计划模板
			} else if (type == '状态') {
				// if (!row.stage) {
				// 	return this.$message.warning('请先填写计划内容!');
				// }
				// this.editForm = deepClone(row);
				this.editForm = row; //浅拷贝 意味着修改这个表单 表格的数据也会变化
				if (row.status !== 4) {
					this.editForm.plannedTime = this.$moment().startOf('day').valueOf(); //如果不是已完工的默认计划完成日期为今天
				}
				this.dialogEdit = true;
			} else if (type == '出差') {
				// 如果合同类型为合伙，则提交出差申请的必须由合伙人操作
				if (this.detailForm.contractType == 1 && !this.userInfos.adminUserVO.userName.startsWith('8')) {
					console.log(this.detailForm.contractType, this.userInfos.adminUserVO.userName);
					return this.$message.warning('当前合同类型为合伙，必须由合伙人提交出差申请，请联系相应的合伙人！');
				}

				this.travelForm = {
					...this.travelForm,
					...row,
					tripType: this.isParter ? '' : 2, //从询盘创建的出差申请默认为商务 交付为交付
					expenseParty: this.isParter ? 3 : '', // 合伙人出差默认承担方为申请人
					// expenseParty: Number(this.detailForm.travelExpensesParty) || '',

					tripDays: row.durationDays || '', //出差天数为所需天数
					tripClientName: this.detailForm.registeredBusinessName || row.tripClientName || '', //客户为拜访的公司
					tripReason: row.tripReason || row.stage || '', //事由为计划内容
					tripBeginDate: row.plannedTime - 24 * 60 * 60 * 1000 * (row.durationDays - 1) || row.tripBeginDate || '', //开始日期为预计日期减去天数
					// 自动补充出差人（咨询业务实施等）
					tripUidsArray:
						row?.tripUids?.split(',') || this.isParter ? [] : Array.from(new Set([this.detailForm.implement].filter(Boolean))),

					// 交付/合同的各种id
					dmid: this.detailForm.dmid,
					dmsid: this.stageForm.dmsid,

					idid: this.detailForm.idid,
					ckrid: this.detailForm.ckrid,
					twid: this.detailForm.twid,
				};
				this.queryDestinationList(); // 查询目的地并自动补充
				this.isParter && this.queryTripTypeOptions(); // 查询差旅类别

				this.dialogTravel = true;
			}
		},
		closeDialog() {
			this.$emit('update', this.stageForm); //更新数据（因为row跟editForm浅拷贝了）
			this.dialogEdit = false;
			this.dialogTravel = false;
		},
		saveEdit(status) {
			// 如果为行业案例,完工时必须上传文件
			if (this.editForm.mark && !this.editForm.file) {
				this.$message.warning('当前为行业案例，请上传附件！');
				return;
			}
			this.editForm.status = status;
			// 计划完成日期不能超过当前项目阶段计划完工月份(阶段已完工的除外)

			if (!this.stageForm.reportName && this.editForm.plannedTime > this.maxTime) {
				const maxDate = this.$moment(this.maxTime).format('YYYY-MM-DD'); //日期
				this.$message.warning(`计划完成日期不能超过当前项目阶段计划完工月份（${maxDate}）！`);
				return;
			}
			const index = this.expandTable.findIndex(i => i.dmsiid == this.editForm.dmsiid);
			this.updateExpandStage(index);
		},
		// 获取初始数据
		getInitData({ expandTable, detailForm, stageForm }) {
			// this.expandTable = expandTable; //展开的表格
			this.detailForm = { ...this.detailForm, ...detailForm }; //项目详情
			this.stageForm = { ...this.stageForm, ...stageForm }; //项目阶段详情
			this.queryTravelData(expandTable); //展开表格合并（合并出差申请信息）
		},

		// 添加展开阶段计划
		async addExpandRow(index) {
			// 默认日期 不得小于或者大于预计完工月
			this.expandTable.splice(index + 1, 0, {
				dmid: this.detailForm.dmid,
				dmsid: this.stageForm.dmsid,
				dmsiid: '',
				durationDays: 1,
				file: '',
				fileName: '',
				note: '',
				plannedTime: this.maxTime,
				stage: '',
				sort: 0,
				status: 1,
				mark: 0,
			});
			this.updateExpandStage(index);
		},
		// 修改展开阶段计划详情
		async updateExpandStage(index) {
			const nowTime = this.$moment().startOf('day').valueOf();
			const sortIndex = this.detailForm.deliverStageManagementVOS.findIndex(item => {
				return item.dmsid == this.stageForm.dmsid; //阶段顺序
			});

			let notInTime = false; // 是否在时间范围
			this.expandTable.forEach((item, index) => {
				const startTime = item.plannedTime - (item.durationDays - 1) * 86400000;
				// 1 待开始  2 进行中   3 已延误 4已完成
				if (item.status == 4) {
					//已完工
				} else if (item.plannedTime < nowTime) {
					item.status = 3; //已延误
				} else if (item.durationDays && nowTime >= startTime && nowTime <= item.plannedTime) {
					item.status = 2; //进行中
				} else if (item.plannedTime > nowTime) {
					item.status = 1; //待开始
				}
				//  || item.plannedTime < minTime
				notInTime = notInTime || item.plannedTime > this.maxTime;
				item.sort = sortIndex * 100 + (index + 1); //计划排序修改
				//手动补充计划名称(因前期未传cfgName参数导致，为防差错后期可删除)
				if (item.aupcid) {
					item.cfgName = this.scoreConfigList.find(i => i.aupcid === item.aupcid)?.cfgName;
				}
			});
			// 计划完成日期不能超过当前项目阶段计划完工月份(阶段已完工的除外)
			if (!this.stageForm.reportName && notInTime) {
				this.$emit('update', this.stageForm); //更新数据
				const maxDate = this.$moment(this.maxTime).format('YYYY-MM-DD'); //日期
				this.$message.warning(`计划完成日期不能超过当前项目阶段计划完工月份（${maxDate}）！`);
				return;
			}

			// 只传新增和修改了的数据
			const deliverStageManagementInfoDTOS = [this.expandTable[index]] || [];

			const API = 'addDeliverManagementProject';
			try {
				const res = await this.$axios[API](JSON.stringify({ deliverStageManagementInfoDTOS }));
				if (res.data.success) {
					this.dialogEdit = false;
					this.$emit('update', this.stageForm); //更新数据
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 删除展开阶段计划行
		async deleteExpandRow(rows) {
			const dmsiid = rows?.map(item => {
				return item.dmsiid;
			});
			const API = 'deleteDeliverManagementProject';
			try {
				const confirmed = await this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				});
				if (confirmed) {
					const res = await this.$axios[API](JSON.stringify({ dmsiid }));
					if (res.data.success) {
						this.$emit('update', this.stageForm);
						this.$succ(res.data.message);
					} else {
						this.$err(res.data.message);
					}
				} else {
					this.$message.info('已取消');
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 计划任务完工报告上传
		uploadFile(file) {
			const isLt50M = file.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				this.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}
			const formData = new FormData();
			formData.append('file', file.file);
			this.isUpLoading = true;
			this.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						this.isUpLoading = false;
						this.editForm.file = res.data.data.path;
						this.editForm.fileName = res.data.data.fileName;
						// this.updateExpandStage();
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.$message.warning(error.message);
				});
		},
		jointString: jointString, //拼接字符串
		dateFormat: dateFormat, //日期format
	},
};
</script>

<style lang="scss" scoped>
.deliveryStagePlan {
	width: 100%;
	// height: 100%;
	overflow: hidden;
	position: relative;
	.expand-table {
		min-height: 100px !important;
		height: auto !important;
	}
}
</style>
