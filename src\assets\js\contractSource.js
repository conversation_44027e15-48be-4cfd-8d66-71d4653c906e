/* 合同用到的信息 */
export const projectTypeOptions = [
	{ id: 1, label: 'OEE版本' },
	{ id: 2, label: '标品MES' },
	{ id: 3, label: '标品MES+定制' },
	{ id: 4, label: '完全定制' },
	{ id: 5, label: '硬件' },
	{ id: 10, label: 'OEE续费' },
	{ id: 11, label: 'MES续费' },
];

export const projectTypeMap = {
	1: 'OEE版本',
	2: '标品MES',
	3: '标品MES+定制',
	4: '完全定制',
	5: '硬件',
	10: 'OEE续费',
	11: 'MES续费',
};

// commissionType 提成类别 合伙人：（1 税金、2 货款、3 定制；）4 咨询、 5 业务 6 实施 7 BPUL 8 BPUT 9 BPUM 127 其他
export const commissionTypeMap = {
	1: '税金',
	2: '货款',
	3: '定制',
	4: '咨询',
	5: '业务',
	6: '实施',
	7: 'BPUL',
	8: 'BPUT',
	9: 'BPUM',
	127: '其他',
};

// 结算类型 0: 无需结算 1: 支援总部 2: 总部支援
export const settlementTypeMap = {
	0: '无需结算',
	1: '支援总部',
	2: '总部支援',
};

//  差旅类别(1:商务 2:交付 3:支援总部商务 4:支援总部交付 5:总部支援商务 6:总部支援交付 7:总部支援接待 8:总部支援装灯
export const tripTypeMap = {
	1: '商务',
	2: '交付',
	3: '支援总部商务',
	4: '支援总部交付',
	5: '总部支援商务',
	6: '总部支援交付',
	7: '总部支援接待',
	8: '总部支援装灯',
	127: '其他',
};

// 费用承担方 0 公司承担，1 客户承担, 2 无 3 申请人承担
export const expensePartyMap = {
	0: '公司承担',
	1: '客户承担',
	3: '申请人承担',
	// 2: '无',
};
