/* 系统管理API */

const urlList = [
	// 登录相关
	'/background/web/loginAdminUserController/sendAdminLoginSmsCode', //手机请求登录验证码
	'/background/web/loginAdminUserController/smsCodeLogin', //手机验证码登录
	'/background/web/loginAdminUserController/pwLogin', //账号密码登录
	// 用户相关
	'/background/web/adminUserController/updatePassWordByOld', //密码修改
	'/background/web/adminUserController/selectAdminUser', //用户管理-查询
	'/background/web/adminUserController/insertAdminUser', //用户管理-添加用户
	'/background/web/adminUserController/updateAdminUser', //用户管理-修改用户
	'/background/web/adminUserController/forbiddenAdminUser', //禁用用户
	'/background/web/adminUserController/deleteAdminUser', //删除用户
	'/background/web/adminUserController/updateUserTeamWork', //修改用户合作方
	'/background/web/adminUserController/selectUserByArid', //根据角色id查询出用户列表
	'/background/web/adminUserController/selectPhoneNoByAuid', //用户详情
	'/background/web/adminUserController/selectSalesmanByTwid', //查询渠道下的业务顾问
	'/background/web/adminUserController/selectHoster', //查询托管方
	'/background/web/adminUserController/selectSalesmanByTwids', //查询多个渠道的业务顾问（限制了业务范围内）
	'/background/web/adminUserController/selectSalesmanByTwidsSimple', //查询多个渠道的业务顾问
	'/background/web/adminUserController/selectTeamworkName', //查询分销/代理
	'/background/web/adminUserController/selectChannelName', //查询用户渠道信息
	'/background/web/adminUserController/selectUserTag', //查询系统用户标签
	'/background/web/adminUserController/updateUserTag', //修改系统用户标签
	'/background/web/adminUserController/deleteUserTag', //删除系统用户标签
	'/background/web/adminUserController/importAdminUser', //后台用户导入
	// 角色相关
	'/background/web/adminUserRoleController/selectAdminRole', //查询用户可分配角色
	'/background/web/adminUserRoleController/distributeAdminRole', //给用户分配角色
	'/background/web/adminRole/selectAdminRole', //查询所有角色
	'/background/web/adminRole/insertAdminRole', //添加角色
	'/background/web/adminRole/deleteAdminRole', //删除角色
	'/background/web/adminRole/updateAdminRole', //修改角色
	// 部门相关
	'/background/web/AdminDepartmentController/deleteAdminDepartment', //删除后台部门对象
	'/background/web/AdminDepartmentController/saveAdminDepartment', //添加或更新后台部门
	'/background/web/AdminDepartmentController/selectAdminDepartmentList', //查询部门

	/* QadnA */
	'/background/web/questionController/addQuestion', //提出问题--新增
	'/background/web/questionController/deleteQuestion', //删除问题
	'/background/web/questionController/selectQuestionList', //查询问题列表
	'/background/web/questionController/updateQuestion', //修改问题
	'/background/web/replyController/addReply', //问题回复 --新增回复
	'/background/web/replyController/deleteReply', //删除回复
	'/background/web/replyController/getReplyListByQid', //根据问题查询回复列表
	'/background/web/replyController/updateReply', //修改回复

	// 在线支持工作台
	'/background/web/menuRelatedQuestionsController/addMenuRelateReply', //问题回复 --新增回复
	'/background/web/menuRelatedQuestionsController/selectConsultantTeamAndUnReplyCount', //查询当前交付老师下的有关菜单提问的相关团队及问题未回复数量
	'/background/web/menuRelatedQuestionsController/selectCurrentTeamMenuAndUnReadCount', //查询当前团队下菜单及菜单下的未读情况(在线支持平台-系统管理员)
	'/background/web/menuRelatedQuestionsController/onlineSupportWorkBenchOfRDMenuInfo', //研发在线支持工作台相关菜单信息
	'/background/web/menuRelatedQuestionsController/selectRDQuestionListByTopId', //根据菜单TopId查询研支持工作台菜单相关提问
	'/background/web/menuRelatedQuestionsController/selectDetailByQuestionId', //根据问题id查询问题详情
	'/background/web/menuRelatedQuestionsController/selectQuestionListByMenuAndColumn', //根据菜单及列序号查询提问清单
	'/background/web/menuRelatedQuestionsController/deleteMenuReplyByRid', //删除回复
	'/background/web/menuRelatedQuestionsController/submitBug', //提交BUG
	'/background/web/menuRelatedQuestionsController/acceptBug', //接收BUG
	'/background/web/menuRelatedQuestionsController/returnBug', //退回BUG
	'/background/web/menuRelatedQuestionsController/onlineSupportList', //在线支持清单

	// 其他/通用
	'/background/web/adminResource/selectAdminResource', //查询所有菜单资源
	'/background/web/adminDownloadRecordController/selectAdminDownloadRecord', //导出下载清单查询
	'/background/web/DeliverManagementController/loadpic', //文件上传
];

// 重名函数映射
const apiNameMap = {
	'/background/web/adminRole/selectAdminRole': 'selectAdminRoleAll',
};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['loadpic'].includes(urlName)) {
		timeout = 60000;
	}

	return { urlName, url, timeout };
});

export default apiList;
