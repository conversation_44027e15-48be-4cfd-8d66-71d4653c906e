<template>
	<div id="comprehensiveView" class="dataView">
		<div class="header">
			<div class="header-background">
				<!-- <span class="header-button" ><el-button size="mini" @click="backToHome">首页</el-button></span> -->
				<span class="header-background-title">树字MES运营综合看板</span>
				<!-- <span class="header-button" ><el-button size="mini">刷新</el-button></span> -->
				<div class="header-button">
					<button @click="screenFull">全屏</button>
					<button @click="backToHome">首页</button>
				</div>
			</div>
		</div>

		<div class="main">
			<div class="main-left">
				<div class="main-left-contract">
					<div class="main-title">
						<span>合同档案管理分析</span>
					</div>
					<div class="main-left-contract-data" style="height: 27vh">
						<div>
							<div>已上传</div>
							<div style="display: flex; align-items: center; letter-spacing: 0.2vw">
								<CountTo
									:startVal="0"
									:endVal="uploaded"
									:duration="3000"
									style="color: rgb(53, 214, 218); font-size: 3.5vh; margin-right: 0.5vw"
								/>
								单
							</div>
						</div>
						<div>
							<div>未上传</div>
							<div style="display: flex; align-items: center; letter-spacing: 0.2vw">
								<CountTo
									:startVal="0"
									:endVal="notUploaded"
									:duration="3000"
									style="color: rgb(53, 214, 218); font-size: 3.5vh; margin-right: 0.5vw"
								/>
								单
							</div>
						</div>
					</div>
				</div>
				<div class="main-left-load">
					<div class="main-left-loadData">
						<div class="main-title">
							<span>交付负荷分析</span>
						</div>
						<div style="height: 27vh">
							<!-- :toolTipOption="{ isAuto: true, duration: 3, }" -->
							<EChart :seriesData="emptyData" :extraOption="loadOption" :activeOption="{ isActive: true, duration: 19 }">
							</EChart>
						</div>
					</div>
				</div>
				<div class="main-left-complete">
					<div class="main-left-completeData">
						<div class="main-title">
							<span>交付达成分析</span>
						</div>
						<div style="height: 27vh">
							<EChart
								:seriesData="emptyData"
								:extraOption="completeOption"
								:activeOption="{ isActive: true, duration: 28 }"
								:toolTipOption="{ isAuto: true, duration: 3 }"
							>
							</EChart>
						</div>
					</div>
				</div>
			</div>
			<div class="main-center">
				<div class="main-center-receipt">
					<div class="main-title">
						<span>收款分析</span>
						<span class="main-title-right">
							<span> 实收：{{ recepited }}万</span>
							<span> 待收：{{ notReceipted }}万</span>
							<span>
								<el-date-picker
									size="mini"
									style="width: 7vw"
									v-model="receiptYear"
									type="year"
									value-format="timestamp"
									popper-append-to-body="false"
									popper-class="view-date-picker"
									format="yyyy 年度 ▼"
									placeholder="请选择年份 ▼"
									@change="getEchartsData('receipt')"
								>
								</el-date-picker>
							</span>
						</span>
					</div>
					<div style="height: 23vh">
						<EChart
							:seriesData="emptyData"
							:extraOption="receiptOption"
							:activeOption="{ isActive: true, duration: 37 }"
						></EChart>
					</div>
					<div style="display: flex">
						<div style="width: 50%; height: 33vh">
							<!-- 收入分析  -->
							<EChart
								ref="income"
								:seriesData="emptyData"
								:extraOption="incomeOption"
								:toolTipOption="{ isAuto: true, duration: 3 }"
								:activeOption="{ isActive: true, duration: 46 }"
							>
							</EChart>
						</div>
						<div style="width: 50%; height: 33vh">
							<!-- 产品分析 -->
							<EChart
								:seriesData="emptyData"
								:extraOption="productOption"
								:activeOption="{ isActive: true, duration: 55 }"
							></EChart>
						</div>
					</div>
				</div>
				<div class="main-center-sign">
					<div class="main-title">
						<span>签单分析</span>
						<span class="main-title-right">
							<span>
								<el-date-picker
									size="mini"
									style="width: 7vw"
									v-model="signYear"
									type="year"
									value-format="timestamp"
									popper-append-to-body="false"
									popper-class="view-date-picker"
									format="yyyy 年度 ▼"
									placeholder="请选择年份 ▼"
									@change="getEchartsData('sign')"
								>
								</el-date-picker>
							</span>
						</span>
					</div>
					<div style="height: 27vh">
						<EChart :seriesData="emptyData" :extraOption="signOption" :activeOption="{ isActive: true, duration: 64 }"> </EChart>
					</div>
				</div>
			</div>
			<div class="main-right">
				<div class="main-right-light">
					<div class="main-title">
						<span>工业互联灯出货量分析（过去12个月）</span>
					</div>
					<div style="height: 27vh">
						<EChart :seriesData="emptyData" :extraOption="lightOption" :activeOption="{ isActive: true, duration: 73 }"></EChart>
					</div>
				</div>
				<div class="main-right-cloud">
					<div class="main-title">
						<span>云服务收入分析（过去12个月）</span>
					</div>
					<div style="height: 27vh">
						<EChart :seriesData="emptyData" :extraOption="cloudOption" :activeOption="{ isActive: true, duration: 82 }"></EChart>
					</div>
				</div>
				<div class="main-right-contribute">
					<div class="main-title">
						<span>贡献度分析</span>
						<span class="main-title-right">
							<span>
								<el-date-picker
									size="mini"
									style="width: 7vw"
									v-model="contributeYear"
									type="year"
									value-format="timestamp"
									popper-append-to-body="false"
									popper-class="view-date-picker"
									format="yyyy 年度 ▼"
									placeholder="请选择年份 ▼"
									@change="getEchartsData('contribute')"
								>
								</el-date-picker>
							</span>
						</span>
					</div>
					<div style="height: 27vh; display: flex">
						<!-- 客户前20名 -->
						<dv-scroll-board :config="customerConfig" style="width: 50%" />
						<!-- 渠道前10名 -->
						<dv-scroll-board :config="channelConfig" style="width: 50%" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import CountTo from 'vue-count-to';
import { EChart } from '@/components/Chart/index.js';
import * as echarts from 'echarts';
import ring from '@/assets/img/view-ring.svg';
export default {
	name: 'comprehensiveView',
	components: { CountTo, EChart },
	props: {
		fullScreenFlag: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			userInfo: {},
			refreshInterval: null, //数据刷新间隔
			receiptYear: new Date(), // 收款年度
			signYear: new Date(), // 签单年度
			contributeYear: new Date(), // 贡献度年度
			uploaded: 0, //已上传
			notUploaded: 0, //未上传
			recepited: 0, //实收
			notReceipted: 0, //未收
			// 客户前20名
			customerConfig: {
				header: ['<span style="color:#fff;font-weight:600;font-size:16px;">客户前20名</span>', ''],
				data: [
					// ['京瓷集团', '190万',],
					// ['大洋电机', '158万',],
				],
				index: true,
				indexHeader: '',
				columnWidth: [50, 150, 100],
				// columnWidth: [200, 100],
				align: ['center', 'left', 'right'],
				// align: ['left', 'right'],
				waitTime: '5000',
				carousel: 'page',
				headerBGC: '#040d25',
				oddRowBGC: 'rgba(22, 40, 75 ,.3)',
				evenRowBGC: 'rgba(65, 138, 165 ,.3)',
			},
			// 渠道前10名
			channelConfig: {
				header: ['<span style="color:#fff;font-weight:600;font-size:16px;">渠道前10名</span>', ''],
				data: [
					// ['聚心橙', '1900万',],
					// ['一诺科技', '1508万',],
				],
				index: true,
				indexHeader: '',
				columnWidth: [50, 150, 100],
				// columnWidth: [200, 100],
				align: ['center', 'left', 'right'],
				// align: ['left', 'right'],
				waitTime: '5000',
				carousel: 'page',
				headerBGC: '#040d25',
				oddRowBGC: 'rgba(22, 40, 75 ,.3)',
				evenRowBGC: 'rgba(65, 138, 165 ,.3)',
			},
			//空数据
			emptyData: [{}],
			// 工业互联灯出货量分析（过去12个月）
			lightOption: {
				title: {
					// text: '一周内人员出入总数变化图',
					// textStyle: {
					// fontSize: _.fitChartSize(30),
					//   fontWeight: 'normal',
					//   color: '#fff',
					// },
					// x: 'center'
				},
				// backgroundColor: "#05224d",
				tooltip: {
					trigger: 'axis',
					// axisPointer: {
					//   type: 'shadow'
					// },
					backgroundColor: 'rgb(7, 97, 165,.8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					valueFormatter: value => value + '支',
				},
				grid: {
					top: '15%',
					left: '1%',
					right: '3%',
					bottom: '1%',
					containLabel: true,
				},
				legend: {
					itemGap: 50,
					data: [''],
					textStyle: {
						fontSize: _.fitChartSize(28),
						color: '#f9f9f9',
						borderColor: '#fff',
					},
				},
				xAxis: [
					{
						type: 'category',
						boundaryGap: false,
						axisLine: {
							//坐标轴轴线相关设置。数学上的x轴
							show: true,
							lineStyle: {
								color: '#f9f9f9',
							},
						},
						axisLabel: {
							//坐标轴刻度标签的相关设置
							interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，
							textStyle: {
								fontSize: _.fitChartSize(30),
								color: '#d1e6eb',
								margin: 15,
							},
						},
						axisTick: {
							show: false,
						},
						data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
					},
					{
						// 第二坐标轴
						position: 'bottom',
						offset: _.fitChartSize(30),
						type: 'category',
						boundaryGap: false,
						axisLine: {
							//坐标轴轴线相关设置。数学上的x轴
							show: false,
							lineStyle: {
								color: '#f9f9f9',
							},
						},
						axisLabel: {
							//坐标轴刻度标签的相关设置
							interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，
							textStyle: {
								fontSize: _.fitChartSize(24),
								color: '#d1e6eb',
								margin: 15,
							},
						},
						axisTick: {
							show: false,
						},
						// data: [2000, 2200, 3100, 1600, 2700, 2300, 1500, 1999, 1245, 2221, 1000, 3000]
						data: [],
					},
				],
				yAxis: [
					{
						name: '支',
						nameTextStyle: {
							color: '#aaa',
							nameLocation: 'start',
						},
						type: 'value',
						min: 0,
						// max: 140,
						minInterval: 1,
						splitNumber: 7,
						splitLine: {
							show: true,
							lineStyle: {
								color: '#0a3256',
							},
						},
						axisLine: {
							show: false,
						},
						axisLabel: {
							margin: 20,
							textStyle: {
								fontSize: _.fitChartSize(28),
								color: '#d1e6eb',
							},
						},
						axisTick: {
							show: false,
						},
					},
				],
				series: [
					{
						name: '出货量分析',
						type: 'line',
						smooth: false, //是否平滑曲线显示
						symbol: 'none', // 默认是空心圆（中间是白色的），改成实心圆 circle
						showAllSymbol: false,
						// symbolSize: 6,
						lineStyle: {
							color: '#66ffff', // 线条颜色
							borderColor: '#f0f',
						},
						label: {
							show: false,
							position: 'top',
							textStyle: {
								fontSize: _.fitChartSize(30),
								color: '#fff',
							},
						},
						itemStyle: {
							color: '#66ffff',
						},
						tooltip: {
							show: true,
						},
						areaStyle: {
							//区域填充样式
							normal: {
								//线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
								color: new echarts.graphic.LinearGradient(
									0,
									0,
									0,
									1,
									[
										{
											offset: 0,
											color: 'rgba(0,154,120,1)',
										},
										{
											offset: 1,
											color: 'rgba(0,0,0, 0)',
										},
									],
									false,
								),
								shadowColor: 'rgba(53,142,215, 0.9)', //阴影颜色
								shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
							},
						},
						// data: [2000, 2200, 3100, 1600, 2700, 2300, 1500, 1999, 1245, 2221, 1000, 3000]
					},
				],
			},
			// 交付负荷分析
			loadOption: {
				backgroundColor: 'transparent',
				tooltip: {
					// position:'center',
					trigger: 'item',
					backgroundColor: 'transparent',
					borderWidth: 0,
					padding: 0,
					formatter: ' ',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
				},
				legend: {
					type: 'scroll',
					pageIconColor: 'white', //激活的分页按钮颜色
					pageIconInactiveColor: '#2f4554', //没激活的分页按钮颜色
					orient: 'horizontal',
					top: '5%',
					left: 'center',
					icon: 'circle',
					data: [],
					// formatter: function (name) {
					//   //通过name获取到数组对象中的单个对象
					//   // let singleData = data.filter(function (item) {
					//   //   return item.name == name
					//   // })
					//   // return name + ' | ' + singleData[0].value;
					//   console.log({name});
					//   return name;
					// },
					textStyle: {
						color: '#fff',
						fontSize: _.fitChartSize(28),
					},
				},
				title: {
					show: false,
				},
				calculable: true,
				series: [
					{
						// name: '交付金额',
						name: '',
						type: 'pie',
						//起始角度，支持范围[0, 360]
						startAngle: 0,
						//饼图的半径，数组的第一项是内半径，第二项是外半径
						radius: ['25%', '125%'],
						//支持设置成百分比，设置成百分比时第一项是相对于容器宽度，第二项是相对于容器高度
						center: ['50%', '20%'],
						//是否展示成南丁格尔图，通过半径区分数据大小。可选择两种模式：
						// 'radius' 面积展现数据的百分比，半径展现数据的大小。
						//  'area' 所有扇区面积相同，仅通过半径展现数据大小
						roseType: 'area',
						//是否启用防止标签重叠策略，默认开启，圆环图这个例子中需要强制所有标签放在中心位置，可以将该值设为 false。
						// avoidLabelOverlap: false,
						label: {
							show: true,
							// formatter: ["{c|{c}个} ", "{d|({d}%)}"].join("\n"),
							formatter: ['{c|{c}} ', '{d|{b}} {d|({d}%)}'].join('\n'),
							rich: {
								c: {
									color: 'inherit',
									// color: "rgb(102, 255, 255)",
									fontSize: _.fitChartSize(35),
									fontWeight: 'bold',
									lineHeight: _.fitChartSize(45),
								},
								b: {
									color: 'inherit',
									fontSize: _.fitChartSize(30),
									// fontWeight: "bold",
									lineHeight: _.fitChartSize(45),
								},
								d: {
									color: 'rgb(98,137,169)',
									// color: 'auto',
									fontSize: _.fitChartSize(26),
									// height: 40,
								},
							},
						},
						labelLine: {
							show: true,
							length2: 1,
						},
						emphasis: {
							// show: true,
							// labelLine: {
							//   lineStyle: {
							//     color: "rgb(98,137,169)",
							//   },
							// }
							scaleSize: 8,
							label: {
								show: true,
								focus: 'self',
								// color: "inherit",
							},
							// labelLine: {
							//   lineStyle: {
							//     color: "auto",
							//   },

							// }
						},
						itemStyle: {
							shadowBlur: 100,
							shadowOffsetX: 0,
							shadowColor: 'rgba(0, 0, 0, 0.5)',
						},
						// data: [
						//   {
						//   value: 600.58,
						//   name: '刘振洲',
						//   itemStyle: {
						//     normal: {
						//       color: '#f845f1'
						//     }
						//   }
						// },
						// {
						//   value: 888.58,
						//   name: '戴谋甜',
						//   itemStyle: {
						//     normal: {
						//       color: '#ad46f3'
						//     }
						//   }
						// },
						// {
						//   value: 1200.58,
						//   name: '胡飞',
						//   itemStyle: {
						//     normal: {
						//       color: '#5045f6'
						//     }
						//   }
						// },

						// // 以下部分留白(各一半)
						// {
						//   value: 0,
						//   name: "",
						//   itemStyle: {
						//     normal: {
						//       color: 'transparent'
						//     }
						//   },
						//   label: {
						//     show: false
						//   },
						//   labelLine: {
						//     show: false
						//   }
						// },
						// {
						//   value: 0,
						//   name: "",
						//   itemStyle: {
						//     normal: {
						//       color: 'transparent'
						//     }
						//   },
						//   label: {
						//     show: false
						//   },
						//   labelLine: {
						//     show: false
						//   }
						// },
						// {
						//   value: 0,
						//   name: "",
						//   itemStyle: {
						//     normal: {
						//       color: 'transparent'
						//     }
						//   },
						//   label: {
						//     show: false
						//   },
						//   labelLine: {
						//     show: false
						//   }
						// },

						// ]
						data: [],
					},
				],
			},
			// 交付达成分析
			completeOption: {
				// backgroundColor: "#2c343c",
				title: {
					show: false,
				},

				tooltip: {
					trigger: 'item',
					backgroundColor: 'transparent',
					borderWidth: 0,
					padding: 0,
					formatter: ' ',
					// valueFormatter: (value) => value + '个',
					// backgroundColor: 'rgb(7, 97, 165, .8)',
					// textStyle: {
					//   color: 'transparent',
					//   fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',"
					// },
				},
				legend: {
					// itemGap: 65,
					left: 'center',
					orient: 'horizontal',
					bottom: '2%',

					data: ['达成', '计划中', '延误'],
					textStyle: {
						fontSize: _.fitChartSize(28),
						color: '#f9f9f9',
						borderColor: '#fff',
					},
				},
				visualMap: {
					show: false,
					min: 500,
					max: 600,
					inRange: {
						//colorLightness: [0, 1]
					},
				},
				series: [
					{
						name: '交付达成',
						type: 'pie',
						//饼图的半径，数组的第一项是内半径，第二项是外半径
						radius: [0, '60%'],
						center: ['50%', '45%'],
						color: ['#50f260', '#f6d54a', '#ff4343'],
						data: [
							// {
							//   value: 110,
							//   name: "计划中",
							// },
							// {
							//   value: 50,
							//   name: "延误",
							// },
							// {
							//   value: 260,
							//   name: "达成",
							// },
						],
						// .sort(function (a, b) {
						//   return a.value - b.value;
						// })
						// roseType: "radius",

						label: {
							show: true,
							formatter: ['{c|{c}个} ', '{b|{b}}  {b|({d}%)} '].join('\n'),
							rich: {
								c: {
									// color: 'inherit',
									color: 'rgb(102, 255, 255)',
									fontSize: _.fitChartSize(40),
									fontWeight: 'bold',
									// lineHeight: 5,
								},
								d: {
									color: 'rgb(102, 255, 255)',
									fontSize: _.fitChartSize(40),
									// fontWeight: "bold",
									lineHeight: 5,
								},
								b: {
									color: 'rgb(98,137,169)',
									// color: 'auto',
									fontSize: _.fitChartSize(26),
									// height: 40,
								},
							},
						},
						labelLine: {
							normal: {
								lineStyle: {
									color: 'rgb(98,137,169)',
								},
								smooth: 0.2,
								length: 10,
								length2: 20,
							},
						},
						emphasis: {
							label: {
								show: true,
								focus: 'self',
								// color: "inherit",
							},
							// labelLine: {
							//   lineStyle: {
							//     color: "rgb(98,137,169)",
							//   },

							// }
						},
						itemStyle: {
							normal: {
								shadowColor: 'rgba(0, 0, 0, 0.9)',
								shadowBlur: 50,
							},
						},
					},
				],
			},
			// 收款分析
			receiptOption: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow',
					},
					backgroundColor: 'rgb(7, 97, 165,.8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					formatter: function (params) {
						// console.log(params)
						// params.sort((a, b) => { return b.value - a.value });
						let res = params[0].name + '<br/>';
						let total = 0;
						for (let i = 0, length = params.length; i < length; i++) {
							total = _.accAdd(total, params[i].data);
							if (params[i].seriesName == '待收') params[i].seriesName = '本月待收';
							if (params[i].seriesName == '合计') {
								params[i].seriesName = '本月合计';
								params[i].data = total;
							}

							params[i].data &&
								(res +=
									params[i].marker +
									params[i].seriesName +
									'<span  style="float:right;margin-left: 16px;font-weight:600;font-size:16px;"> ' +
									params[i].data +
									'万' +
									'</span><br/>');
						}
						return res;
						// return params[0].name  +'<br>生产量 '+ params[0].data;
					},
					// valueFormatter:(value) =>   value + '万',
				},
				legend: {
					show: true,
					type: 'scroll',
					pageIconColor: 'white', //激活的分页按钮颜色
					pageIconInactiveColor: '#2f4554', //没激活的分页按钮颜色
					orient: 'horizontal',
					top: '3%',
					// data: [
					//   '支伟杰部',
					//   '胡高明部',
					//   '杨要军部',
					//   '罗雪梅部',
					//   '胡久春部',
					//   '待收',
					// ],

					textStyle: {
						fontSize: _.fitChartSize(28),
						color: '#d1e6eb',
						// margin: 15
					},
				},
				grid: {
					left: '1%',
					right: '1%',
					bottom: '3%',
					containLabel: true,
				},
				xAxis: [
					{
						type: 'category',
						axisLabel: {
							//坐标轴刻度标签的相关设置
							interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，
							textStyle: {
								fontSize: _.fitChartSize(30),
								color: '#d1e6eb',
								margin: 15,
							},
						},
						axisTick: {
							show: false,
						},
						data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
					},
				],
				yAxis: [
					{
						name: '万元',
						nameTextStyle: {
							color: '#aaa',
							nameLocation: 'start',
						},
						type: 'value',
						min: 0,
						splitLine: {
							show: true,
							lineStyle: {
								color: '#6e7079',
							},
						},
						axisTick: {
							show: true,
						},
						axisLine: {
							show: true,
						},
						axisLabel: {
							// margin: 20,
							textStyle: {
								fontSize: _.fitChartSize(28),
								color: '#d1e6eb',
							},
						},
					},
				],
				series: [
					// {
					//   name: '支伟杰部',
					//   type: 'bar',
					//   stack: 'account',
					//   emphasis: {
					//     focus: 'series'
					//   },
					//   label: {
					//     show: false
					//   },
					//   color: 'rgba(20, 120, 255, .9)',
					//   data: [120, 132, 101, 134, 90, 230, 210]
					// },
					// {
					//   name: '胡高明部',
					//   type: 'bar',
					//   stack: 'account',
					//   emphasis: {
					//     focus: 'series'
					//   },
					//   label: {
					//     show: false
					//   },
					//   color: 'rgba(40, 140, 255, .9)',
					//   data: [220, 182, 191, 234, 290, 330, 310]
					// },
					// {
					//   name: '杨要军部',
					//   type: 'bar',
					//   stack: 'account',
					//   emphasis: {
					//     focus: 'series'
					//   },
					//   label: {
					//     show: false
					//   },
					//   color: 'rgba(60, 160, 255, .9)',
					//   data: [150, 232, 201, 154, 190, 330, 410]
					// },
					// {
					//   name: '罗雪梅部',
					//   type: 'bar',
					//   stack: 'account',
					//   emphasis: {
					//     focus: 'series'
					//   },
					//   label: {
					//     show: false
					//   },
					//   color: 'rgba(80, 180, 255, .9)',
					//   data: [150, 232, 201, 154, 190, 330, 410]
					// },
					// {
					//   name: '胡久春部',
					//   type: 'bar',
					//   stack: 'account',
					//   emphasis: {
					//     focus: 'series'
					//   },
					//   label: {
					//     show: false
					//   },
					//   color: 'rgba(100, 200, 255, .9)',
					//   data: [100, 1018, 20, 299, 311, 135, 77],
					// },
					// {
					//   name: '待收',
					//   type: 'bar',
					//   color: '#ec808d',
					//   label: {
					//     show: true,
					//     position: 'top',
					//     color: '#fff'
					//   },
					//   data: [150, 232, 201, 154, 190, 330, 410],
					//   emphasis: {
					//     focus: 'series'
					//   }
					// },
					// {
					//   name: '合计',
					//   type: 'bar',
					//   stack: 'account',
					//   label: {
					//     show: true,
					//     formatter: function (params) {
					//       let total = [1500, 2320, 2010, 1540, 1900, 3300, 4100];
					//       return total[params.dataIndex];
					//     },
					//     // fontSize: 16,
					//     // fontWeight: 'bold',
					//     position: 'top',
					//     color: '#fff'
					//   },
					//   color: 'rgba(100, 200, 255, 0)',
					//   data: [0, 0, 0, 0, 0, 0, 0],
					// },
				],
			},
			// 收入分析
			incomeOption: {
				color: [
					'rgba(255,146,62,1)',
					'rgba(122,253,184,1)',
					'rgba(93,75,253,1)',
					'rgba(255,226,133,1)',
					'rgba(11,185,253,1)',
					'rgba(11,185,253,1)',
				],
				// backgroundColor: '#020f18',
				title: {
					show: false,
					// text: '所占比重{a|' + value + '}\n ',
					text: '所占比重',
					subtext: '100%',
					x: 'center',
					y: 'center',

					textStyle: {
						color: '#fff',
						fontSize: _.fitChartSize(35),
						// lineHeight: '100%',
						// fontSize: _.fitChartSize(80),
						fontWeight: 600,
						// fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',"
					},
					subtextStyle: {
						color: '#fff',
						fontSize: _.fitChartSize(24),
						// lineHeight: '100%',
						// fontSize: _.fitChartSize(80),
						// fontWeight: 600,
						// fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',"
					},
				},

				graphic: {
					elements: [
						{
							type: 'image',
							z: 3,
							style: {
								image: ring,
								width: _.fitChartSize(460),
								height: _.fitChartSize(460),
								//width: '100%',
								// height: '100%',
							},
							left: 'center',
							top: 'center',
							// transition: ['shape', 'style', 'x', 'y'],
							originX: _.fitChartSize(460 / 2),
							originY: _.fitChartSize(460 / 2),
							keyframeAnimation: {
								// 自转动画
								duration: 720000,
								loop: true,
								keyframes: [
									{
										percent: 1,
										easing: 'linear',
										rotation: '360',
									},
								],
							},
						},
					],
				},
				tooltip: {
					trigger: 'item',
					show: true,
					confine: true,
					// formatter: "{a} <br/>{b} : {c}元",
					formatter: '所占比重<br/>{d} %',
					// position: function (pos, params, dom, rect, size) {
					//   let obj = {};
					//   obj[['left', 'right'][+(pos[0] > size.viewSize[0] / 2)]] = '42.5%';
					//   obj[['top', 'bottom'][+(pos[0] > size.viewSize[1] / 2)]] = '42.5%';
					//   // obj[['left', 'right'][+(pos[0] > size.viewSize[0] / 2)]] = '35%';
					//   // obj[['top', 'bottom'][+(pos[0] > size.viewSize[1] / 2)]] = '35%';
					//   return obj;
					// },
					// position: ['41.5%', '43.5%'],
					position: !this.fullScreenFlag ? { left: '42%', top: '43%' } : { left: '43%', top: '44%' },
					color: [
						'rgba(255,146,62,1)',
						'rgba(122,253,184,1)',
						'rgba(93,75,253,1)',
						'rgba(255,226,133,1)',
						'rgba(11,185,253,1)',
						'rgba(11,185,253,1)',
					],
					backgroundColor: 'transparent',
					// backgroundColor: 'rgb(7, 97, 165, .5)',
					// borderColor: 'inherit',
					borderWidth: 0,
					padding: 0,

					textStyle: {
						fontSize: _.fitChartSize(30),
						// fontWeight: "bold",
						color: [
							'rgba(255,146,62,1)',
							'rgba(122,253,184,1)',
							'rgba(93,75,253,1)',
							'rgba(255,226,133,1)',
							'rgba(11,185,253,1)',
							'rgba(11,185,253,1)',
						],
						// fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',"
					},
					extraCssText: 'text-align: center',
				},
				legend: [
					{
						orient: 'horizontal',
						formatter: '{name}收入',

						// left: '88%',
						bottom: '3%',

						textStyle: {
							fontSize: _.fitChartSize(28),
							color: '#fff',
						},
						// itemGap: 10 //间距
					},
				],
				series: [
					{
						///数据层
						name: '',
						type: 'pie',
						hoverAnimation: true,
						hoverOffset: 25,
						// startAngle: 0, //起始角度
						// clockwise: false, //是否顺时针
						radius: ['30%', '50%'],
						center: ['50%', '50%'],
						avoidLabelOverlap: false,

						label: {
							show: true,
							color: 'inherit',
							// position: 'center',
							fontSize: _.fitChartSize(40),
							// formatter: function(params) {
							//     // return params.name + '(' + params.value + '万)';
							//     return params.name + '(' + params.value + '万)';
							// },
							// b|{b}} "{c|{c}万} ", {d|({d}%)}
							formatter: ['{c|{c}万}', '{b|{b}} '].join('\n'),
							rich: {
								b: {
									color: 'rgb(98,137,169)',
									fontSize: _.fitChartSize(30),
									// fontWeight: "bold",
									// lineHeight: 5,
									height: 20,
								},
								c: {
									// color: "rgb(102, 255, 255)",
									fontSize: _.fitChartSize(35),
									fontWeight: 'bold',
									// lineHeight:_.fitChartSize(24),
								},
								d: {
									color: 'rgb(98,137,169)',
									// color: 'auto',
									fontSize: _.fitChartSize(26),
									// height: 40,
								},
							},
						},
						labelLine: {
							show: true,
							smooth: 0.2,
							length: _.fitChartSize(20),
							length2: _.fitChartSize(40),
							// lineStyle: {
							//   color: "transparent",
							//   // color: "inherit",
							// }
						},
						emphasis: {
							itemStyle: {
								borderWidth: 0, // 间距的宽度
							},
							label: {
								show: true,
								focus: 'self',
								color: 'inherit',
								// position: 'center',
								// color: "rgb(157,251,255)",
								// fontWeight: 800,
								// fontSize: _.fitChartSize(24),
								// formatter: ["{c|{c}万} ", "{d|({d}%)}"].join("\n"),
							},
							// labelLine: {
							//   lineStyle: {
							//     color: 'inherit',
							//   }

							// }
						},
						data: [
							//   {
							//   value: 430,
							//   name: '云服务'
							// },
							// {
							//   value: 890,
							//   name: '直购'
							// },
							// {
							//   value: 1043,
							//   name: '合同'
							// },
						],
						itemStyle: {
							borderWidth: 5, // 间距的宽度
							borderColor: 'rgba(40,48,65,1)', //背景色
						},
						zlevel: 30,
					},
					{
						//最内层
						type: 'pie',
						radius: ['0%', '40%'],
						center: ['50%', '50%'],
						hoverAnimation: false,
						clockWise: false,

						itemStyle: {
							normal: {
								color: {
									type: 'radial',
									x: 0.5,
									y: 0.493,
									r: 0.5,
									colorStops: [
										{
											offset: 0,
											color: 'rgb(20, 36, 72)', // 0% 处的颜色
										},
										{
											offset: 0.9,
											color: 'rgba(20, 36, 72, .2)', // 90% 处的颜色
										},
										{
											offset: 0.95,
											color: 'rgba(103, 223, 242, .5)', // 95% 处的颜色
										},
										{
											offset: 1,
											color: 'rgb(103, 223, 242)', // 100% 外侧的颜色
										},
									],
									global: false, // 缺省为 false
								},
								shadowBlur: 10,
								shadowColor: 'transparent',
							},
						},
						label: {
							show: false,
						},
						data: [100],
					},
					{
						//外发光
						type: 'pie',
						radius: ['40%', '60%'],
						center: ['50%', '50%'],
						hoverAnimation: true,
						clockWise: false,
						tooltip: {
							show: false,
						},
						label: {
							show: false,
						},
						itemStyle: {
							normal: {
								borderWidth: 12,
								color: 'rgba(28, 36, 67,.5)',
							},
						},
						data: [100],
					},
				],
			},
			// 产品分析
			productOption: {
				tooltip: {
					trigger: 'item',
					// formatter: "{a} <br/>{b} : {c}元",

					// axisPointer: {
					//   type: 'shadow'
					// },
					backgroundColor: 'rgb(7, 97, 165, .8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					valueFormatter: value => value + '万',
				},
				grid: {
					top: '15%',
					left: '1%',
					right: '3%',
					bottom: '0%',
					containLabel: true,
				},
				yAxis: [
					{
						type: 'category',
						// data: ['lightMES', '工业互联灯', '安灯系统', 'ESOP系统', '售服系统', '维保系统'],
						data: [],
						axisLine: {
							show: false,
							lineStyle: {
								color: 'white',
							},
						},
						axisTick: {
							show: false,
							length: 9,
							alignWithLabel: true,
							lineStyle: {
								color: '#7DFFFD',
							},
						},
						axisLabel: {
							fontSize: _.fitChartSize(35),
							color: '#d1e6eb',
						},
					},
					{
						name: '万元',
						nameTextStyle: {
							color: '#aaa',
							nameLocation: 'center',
						},
						type: 'category',
						// data: [220, 182, 191, 234, 290, 330],
						data: [],
						axisLine: {
							show: false,
							lineStyle: {
								color: 'white',
							},
						},
						axisTick: {
							show: false,
							length: 9,
							alignWithLabel: true,
							lineStyle: {
								color: '#7DFFFD',
							},
						},
						axisLabel: {
							fontSize: _.fitChartSize(30),
							color: '#d1e6eb',
						},
					},
				],
				xAxis: {
					type: 'value',
					alignWithLabel: true,
					axisLine: {
						show: false,
						lineStyle: {
							color: 'white',
						},
					},
					splitLine: {
						show: false,
					},
					axisTick: {
						show: false,
						alignWithLabel: true,
					},
					axisLabel: {
						show: false,
						fontSize: 16,
					},
					// boundaryGap: ['10%', '10%']
				},

				series: [
					{
						type: 'bar',
						// barWidth: _.fitChartSize(30),
						// barMaxWidth: 20,
						color: '#67def1',
						// label: {
						//     show: true,
						//     position: 'right',
						//     color: '#fff'
						// },
						itemStyle: {
							color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
								{
									offset: 0,
									color: 'rgb(20, 36, 72)',
								},
								// {
								//     offset: 0.5,
								//     color: '#188df0'
								// },
								{
									offset: 1,
									color: 'rgb(103, 223, 242)',
								},
							]),
						},
						// data: [220, 182, 191, 234, 290, 330],
						data: [],
					},
				],
			},
			// 签单分析
			signOption: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow',
					},
					backgroundColor: 'rgb(7, 97, 165,.8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					// valueFormatter: (value) => value + '万',
					formatter: function (params) {
						params.sort((a, b) => {
							return b.value - a.value;
						});
						let res = params[0].name + '<br/>';
						// let total = 0;
						for (let i = 0, length = params.length; i < length; i++) {
							// if (params[i].seriesName == '合计') {
							//   params[i].seriesName = '本月合计';
							//   params[i].data = total;
							// };
							// total = _.accAdd(total, params[i].data);
							params[i].data &&
								(res +=
									params[i].marker +
									params[i].seriesName +
									'<span  style="float:right;margin-left: 16px;font-weight:600;font-size:16px;"> ' +
									params[i].data +
									'万' +
									'</span><br/>');
						}
						return res;
						// return params[0].name  +'<br>生产量 '+ params[0].data;
					},
				},
				legend: {
					type: 'scroll',
					pageIconColor: 'white', //激活的分页按钮颜色
					pageIconInactiveColor: '#2f4554', //没激活的分页按钮颜色
					show: true,
					orient: 'horizontal',
					top: '3%',
					textStyle: {
						fontSize: _.fitChartSize(28),
						color: '#d1e6eb',
						// margin: 15
					},
				},
				grid: {
					left: '1%',
					right: '1%',
					bottom: '3%',
					// top: '25%',
					containLabel: true,
				},
				xAxis: [
					{
						type: 'category',
						axisLabel: {
							//坐标轴刻度标签的相关设置
							interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，
							textStyle: {
								fontSize: _.fitChartSize(30),
								color: '#d1e6eb',
								margin: 15,
							},
						},
						axisTick: {
							show: false,
						},
						data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
					},
				],
				yAxis: [
					{
						name: '万元',
						nameTextStyle: {
							color: '#aaa',
							nameLocation: 'start',
						},
						type: 'value',
						min: 0,
						splitLine: {
							show: true,
							lineStyle: {
								color: '#6e7079',
							},
						},
						axisTick: {
							show: true,
						},
						axisLine: {
							show: true,
						},
						axisLabel: {
							// margin: 20,
							textStyle: {
								fontSize: _.fitChartSize(28),
								color: '#d1e6eb',
							},
						},
					},
				],
				series: [
					// {
					//   name: '支伟杰部',
					//   type: 'bar',
					//   stack: 'account',
					//   emphasis: {
					//     focus: 'series'
					//   },
					//   label: {
					//     show: false
					//   },
					//   color: 'rgba(20, 120, 255, .9)',
					//   barWidth: '35%',
					//   data: [120, 132, 101, 134, 90, 230, 210]
					// },
					// {
					//   name: '胡高明部',
					//   type: 'bar',
					//   stack: 'account',
					//   emphasis: {
					//     focus: 'series'
					//   },
					//   label: {
					//     show: false
					//   },
					//   color: 'rgba(40, 140, 255, .9)',
					//   data: [220, 182, 191, 234, 290, 330, 310]
					// },
					// {
					//   name: '杨要军部',
					//   type: 'bar',
					//   stack: 'account',
					//   emphasis: {
					//     focus: 'series'
					//   },
					//   label: {
					//     show: false
					//   },
					//   color: 'rgba(60, 160, 255, .9)',
					//   data: [150, 232, 201, 154, 190, 330, 410]
					// },
					// {
					//   name: '罗雪梅部',
					//   type: 'bar',
					//   stack: 'account',
					//   emphasis: {
					//     focus: 'series'
					//   },
					//   label: {
					//     show: false
					//   },
					//   color: 'rgba(80, 180, 255, .9)',
					//   data: [150, 232, 201, 154, 190, 330, 410]
					// },
					// {
					//   name: '胡久春部',
					//   type: 'bar',
					//   stack: 'account',
					//   emphasis: {
					//     focus: 'series'
					//   },
					//   label: {
					//     show: false
					//   },
					//   color: 'rgba(100, 200, 255, .9)',
					//   data: [150, 232, 201, 154, 190, 330, 410]
					// },
				],
			},
			// 云服务收入分析（过去12个月）
			cloudOption: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						// 坐标轴指示器，坐标轴触发有效
						type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
					},
					backgroundColor: 'rgb(7, 97, 165,.8)',
					textStyle: {
						color: '#fff',
						fontFamily: "'sans-serif', 'serif' , 'monospace', 'Arial', 'Courier New', 'Microsoft YaHei',",
					},
					valueFormatter: value => value + '万',
				},
				grid: {
					top: '15%',
					left: '1%',
					right: '1%',
					bottom: '3%',
					containLabel: true,
				},
				xAxis: {
					data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
					axisTick: {
						show: false,
					},
					//坐标值标注
					axisLabel: {
						interval: 0, //横轴数据如果非常多，会自动隐藏一部分数据，

						textStyle: {
							fontSize: _.fitChartSize(30),
							color: '#fff',
						},
					},
				},
				yAxis: {
					name: '万元',
					nameTextStyle: {
						color: '#aaa',
						nameLocation: 'start',
					},
					min: 0,
					minInterval: 1,
					//坐标轴
					axisLine: {
						show: true,
					},
					//坐标值标注
					axisLabel: {
						show: true,
						textStyle: {
							fontSize: _.fitChartSize(28),
							color: '#fff',
						},
					},
					//分格线
					splitLine: {
						show: false,
					},
				},
				series: [
					{
						// 正面
						name: '',
						tooltip: {
							tooltip: {
								show: true,
								valueFormatter: value => value + '万',
							},
						},
						type: 'bar',
						barWidth: 17,
						color: '#1d1841',
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,],
						barGap: 0,
					},
					{
						// 侧面
						type: 'bar',
						barWidth: 6,
						color: '#2a418d',
						tooltip: {
							show: false,
						},
						barGap: 0,
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,].map(item => item * 1.05)
					},
					{
						// 顶部
						name: 'b',
						tooltip: {
							show: false,
						},
						label: {
							show: true,
							position: 'top',
							textStyle: {
								color: '#fff',
							},
						},
						type: 'pictorialBar',

						itemStyle: {
							borderWidth: 1,
							borderColor: '#ffff99',
							color: '#ffff99',
						},
						symbol: 'path://M 0,0 l 120,0 l -30,60 l -120,0 z',
						symbolSize: ['20', '9'],
						symbolOffset: ['0', '-8'],
						// symbolRotate: -5,
						symbolPosition: 'end',
						// data: [220, 182, 191, 234, 290, 330, 310, 182, 191, 234, 220, 330,],
						data: [],
						z: 3,
					},
				],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['menuList']),
	},
	// 监控data中的数据变化
	watch: {
		fullScreenFlag(newVal) {
			const comprehensiveView = document.getElementById('comprehensiveView');
			if (newVal) {
				comprehensiveView.style.position = 'absolute';
			} else {
				comprehensiveView.style.position = 'relative';
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.resetChartsDb();
		window.addEventListener('resize', this.resetChartsDb);
		this.$once('hook:beforeDestroy', () => {
			window.removeEventListener('resize', this.resetChartsDb);
		});

		this.refreshInterval = setInterval(() => {
			//数据刷新定时器
			console.log('每30分钟更新数据');
			this.getEchartsData();
		}, 60000 * 30);
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		window.removeEventListener('resize', this.resetChartsDb);
	},
	// 生命周期 - 销毁完成
	destroyed() {
		clearInterval(this.refreshInterval);
		this.refreshInterval = null;
		// setTimeout(() => {
		// 	document.querySelector(".el-main").style.padding = "20px";
		// },)
	},
	// 方法集合
	methods: {
		// 获取图表数据
		getEchartsData() {
			this.uploaded = 0;
			this.notUploaded = 0;

			const str = JSON.stringify({
				clientActiveEndDate: _.getYearFirstDayAndLastDay(this.contributeYear)[1],
				clientActiveStartDate: _.getYearFirstDayAndLastDay(this.contributeYear)[0],
				collectionEndDate: _.getYearFirstDayAndLastDay(this.receiptYear)[1],
				collectionStartDate: _.getYearFirstDayAndLastDay(this.receiptYear)[0],
				signingEndDate: _.getYearFirstDayAndLastDay(this.signYear)[1],
				signingStartDate: _.getYearFirstDayAndLastDay(this.signYear)[0],
			});
			this.$axios
				.comprehensiveKanban(str)
				.then(res => {
					if (res.data.success) {
						const {
							contractAnalysis,
							deliveryLoadAnalysis,
							deliveryOfAAnalysis,
							collectionAnalysis,
							lightShipmentsAnalysis,
							channelContributionAnalysis,
							clientContributionAnalysis,
							cloudServiceCollectionAnalysis,
							signAnalysis,
							barChartVOS,
							kanBanCollectionPieChartVOS,
						} = res.data.data;
						// 合同分析
						this.uploaded = contractAnalysis.count;
						this.notUploaded = contractAnalysis.monthCount;
						// 交付负荷分析
						const len = deliveryLoadAnalysis.length;
						const loadLegend = [];
						const loadOption = new Array(len * 2).fill({
							value: 0,
							name: '',
							itemStyle: {
								normal: {
									color: 'transparent',
								},
							},
							label: {
								show: false,
							},
							labelLine: {
								show: false,
							},
						});
						deliveryLoadAnalysis &&
							deliveryLoadAnalysis.length &&
							deliveryLoadAnalysis.forEach((item, index) => {
								loadOption[len - index - 1] = {
									name: item.month,
									value: item.monthData,
								};
								loadLegend[len - index - 1] = item.month;
							});
						this.loadOption.series[0].data = loadOption;
						this.loadOption.legend.data = loadLegend.reverse();
						// 交付达成分析
						const completeOption = new Array(3);
						deliveryOfAAnalysis &&
							deliveryOfAAnalysis.length &&
							deliveryOfAAnalysis.forEach(item => {
								let name, index;
								if (item.name == '已完成') {
									name = '达成';
									index = 0;
								} else if (item.name == '待完成') {
									name = '计划中';
									index = 1;
								} else {
									name = '延误';
									index = 2;
								}
								completeOption[index] = { name, value: item.amount };
							});
						this.completeOption.series[0].data = completeOption;
						// 收款分析 collectionAnalysis
						const receiptOption = [];
						const collected = [];
						const uncollected = [];
						const legendData = [];
						collectionAnalysis &&
							collectionAnalysis.length &&
							collectionAnalysis.forEach(cItem => {
								const index = Number(cItem.month) - 1;
								// 实收和待收
								collected[index] = _.accDiv(cItem.collected, 10000, 2);
								uncollected[index] = _.accDiv(cItem.uncollected, 10000, 2);
								cItem.salesmanYearDataVOS.forEach((person, pIndex) => {
									const fIndex = receiptOption.findIndex(fItem => fItem.name == person.name);
									if (fIndex > -1) {
										receiptOption[fIndex].data[index] = this.returnDoubleFloat(_.accDiv(person.receipt, 10000, 2));
									} else {
										if (!legendData.includes(person.name)) {
											legendData.push(person.name);
										}
										const data = new Array(12).fill(0);
										data[index] = this.returnDoubleFloat(_.accDiv(person.receipt, 10000, 2));

										const R = 10 * (receiptOption.length + 1);
										const G = 20 * (receiptOption.length + 1);
										const color = `rgba(${R}, ${G}, 255, .95)`;
										receiptOption.push({
											name: person.name,
											type: 'bar',
											stack: 'account',
											emphasis: {
												focus: 'series',
											},
											label: {
												show: false,
											},
											color: color,
											data: data,
										});
									}
								});
							});
						// 塞入待收和合计项
						receiptOption.push(
							{
								name: '待收',
								type: 'bar',
								color: '#ec808d',
								label: {
									show: true,
									position: 'top',
									color: '#fff',
								},
								data: uncollected,
								emphasis: {
									focus: 'series',
								},
							},
							{
								name: '合计',
								type: 'bar',
								stack: 'account',
								label: {
									show: true,
									formatter: function (params) {
										const total = collected;
										return total[params.dataIndex];
									},
									position: 'top',
									color: '#fff',
								},
								// color: 'rgba(100, 200, 255, 0)',
								color: 'rgba(102, 82, 255,1)',
								data: new Array(collected.length).fill(0),
							},
						);
						legendData.push('待收');
						this.receiptOption.legend.data = legendData;
						this.receiptOption.series = receiptOption;
						// 实收和待收合计
						const totalArr = function (arr) {
							let sum = 0;
							sum = arr.reduce((accumulator, currentValue) => _.accAdd(accumulator, currentValue), 0);
							return sum;
						};
						this.recepited = totalArr(collected);
						this.notReceipted = totalArr(uncollected);
						// 收入分析（有外环的饼图）
						const incomeData = [];
						kanBanCollectionPieChartVOS &&
							kanBanCollectionPieChartVOS.length &&
							kanBanCollectionPieChartVOS.forEach((item, index) => {
								incomeData.push({ value: _.accDiv(item.amount, 10000, 2), name: item.name });
								// incomeData.push({ value: item.amount, name: item.name });
							});
						this.incomeOption.series[0].data = incomeData;
						// 产品分析
						const productData = [];
						const productName = [];
						barChartVOS &&
							barChartVOS.length &&
							barChartVOS.forEach((item, index) => {
								productName[index] = item.month;
								productData[index] = _.accDiv(item.monthData, 10000, 2);
							});
						this.productOption.yAxis[0].data = productName;
						this.productOption.yAxis[1].data = productData;
						this.productOption.series[0].data = productData;
						// 签单分析
						const signOption = [];
						signAnalysis &&
							signAnalysis.length &&
							signAnalysis.forEach(sItem => {
								const month = Number(sItem.month) - 1;
								sItem.salesmanYearDataVOS.forEach((person, pIndex) => {
									const fIndex = signOption.findIndex(item => item.name == person.name);
									if (fIndex > -1) {
										signOption[fIndex].data[month] = this.returnDoubleFloat(person.receipt);
									} else {
										const data = new Array(12).fill(0);
										data[month] = this.returnDoubleFloat(person.receipt);

										const R = 10 * (signOption.length + 1);
										const G = 20 * (signOption.length + 1);
										const color = `rgba(${R}, ${G}, 255, .95)`;
										signOption.push({
											name: person.name,
											type: 'bar',
											stack: 'account',
											emphasis: {
												focus: 'series',
											},
											label: {
												show: false,
											},
											color: color,
											barWidth: '35%',
											data: data,
										});
									}
								});
							});

						this.signOption.series = signOption;
						// console.log({ signOption });
						/* 过去12个月日期计算 */
						const monthArr = [];
						const monthMap = {};
						const date = new Date();
						date.setMonth(date.getMonth() + 1, 1); // 设置到下个月
						for (let i = 0; i < 12; i++) {
							date.setMonth(date.getMonth() - 1); //每次循环一次 月份值减1
							const m = date.getMonth() + 1;
							monthArr.unshift(m + '月');
							monthMap[m] = 12 - i - 1;
						}
						// 出货量分析（过去12个月
						const lightOption = new Array(12).fill(0);
						lightShipmentsAnalysis &&
							lightShipmentsAnalysis.length &&
							lightShipmentsAnalysis.forEach(item => {
								const index = monthMap[Number(item.month)];
								lightOption[index] = item.monthData;
							});
						this.lightOption.xAxis[0].data = monthArr;
						this.lightOption.xAxis[1].data = lightOption;
						this.lightOption.series[0].data = lightOption;
						// 云服务分析（过去12个月
						const cloudOption = new Array(12).fill({ value: 0, itemStyle: { opacity: 0 } });
						let maxNum = 0;
						cloudServiceCollectionAnalysis &&
							cloudServiceCollectionAnalysis.length &&
							cloudServiceCollectionAnalysis.forEach(item => {
								const index = monthMap[Number(item.month)];
								cloudOption[index] = item.monthData;
								if (item.monthData > maxNum) maxNum = item.monthData;
							});
						this.cloudOption.xAxis.data = monthArr;
						this.cloudOption.series[0].data = cloudOption;
						this.cloudOption.series[1].data = cloudOption.map(item => (item = _.accAdd(Number(item), _.accMul(maxNum, 0.045))));
						this.cloudOption.series[2].data = cloudOption;

						// 贡献度分析  - 客户
						const customerConfig = _.deepClone(this.customerConfig);
						customerConfig.data = [];
						clientContributionAnalysis.forEach((item, index) => {
							customerConfig.data[index] = [item.name, this.returnDoubleFloat(item.amount) + '万'];
						});
						this.customerConfig = customerConfig;
						// 贡献度分析  - 渠道
						const channelConfig = _.deepClone(this.channelConfig);
						channelConfig.data = [];
						channelContributionAnalysis.forEach((item, index) => {
							channelConfig.data[index] = [item.name, this.returnDoubleFloat(item.amount) + '万'];
						});
						this.channelConfig = channelConfig;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log('comprehensiveKanban |' + error);
				});
		},

		resetChartsDb: _.debounce('resetCharts'),
		resetCharts() {
			//动态更新图表文字大小
			console.log('触发更新');
			this.$nextTick(() => {
				//字体大小重置(没人看懒得搞了)

				//触发图形更新
				this.getEchartsData();
			});
		},

		// 返回双精度浮点数
		returnDoubleFloat(value) {
			if (!value) return 0;
			var value = Math.round(parseFloat(value) * 100) / 100;
			const point = value.toString().split('.');
			if (point.length == 1) {
				value = value.toString() + '.00';
				return value;
			}
			if (point.length > 1) {
				if (point[1].length < 2) {
					value = value.toString() + '0';
				}
				return value;
			}
			// console.log(value);
			return value;
		},
		//全屏
		screenFull() {
			const flag = !this.fullScreenFlag;
			this.$emit('fullScreen', flag);
			this.$nextTick(() => {
				this.incomeOption.tooltip.position = flag ? { left: '42%', top: '43%' } : { left: '43%', top: '44%' };
			});
			// this.getEchartsData();
		},
		// 返回主页
		backToHome() {
			this.$router.replace({
				path: '/welCome',
				query: {
					queryId: this.userInfos?.adminUserVO.phoneNo,
				},
			});
			this.$emit('fullScreen', false);
		},
	},
};
</script>

<style lang="scss" scoped>
#comprehensiveView {
	width: 100%;
	// height: 100%;
	background: #04051a;
	color: #ffffff;
	padding: 0.5vw;
	box-sizing: border-box;
	position: relative;
	top: 0;
	left: 0;

	.header {
		text-align: center;
		position: relative;

		&-background {
			height: 5.3vh;
			line-height: 5.3vh;
			width: 100%;
			background: url('@/assets/img/view-title.svg') no-repeat center center;
			background-size: 100% 120%;
			margin: auto;
			position: relative;

			&-title {
				//font-weight: bolder;
				font-size: 3vh;
				letter-spacing: 3px;
				cursor: pointer;
			}
		}
	}

	.main {
		margin-top: 0.5vw;
		display: flex;
		justify-content: space-between;

		& > div {
			display: flex;
			flex-direction: column;
			//justify-content: space-between;
		}

		.main-title {
			display: flex;
			justify-content: space-between;
			height: 3.5vh;
			line-height: 3.5vh;
			padding: 0 0.5vw;
			padding-right: 0;
			font-family: PingFangSC-Regular, 'PingFang SC', sans-serif;
			font-weight: 400;
			font-style: normal;
			color: rgb(102, 255, 255);
			background: linear-gradient(90deg, rgb(2, 119, 198) 0%, rgb(2, 119, 198) 0%, rgb(16, 62, 112) 100%, rgb(16, 62, 112) 100%);
			border: none;
			border-radius: 0px;
			box-shadow: none;

			&-right {
				color: #fff;
				font-size: 14px;
			}
		}

		.main-left {
			margin-top: -2.5vh;
			width: 26.5%;

			& > div {
				min-height: 27vh;
				margin-bottom: 1vh;
				background-color: #040d25;
			}

			&-contract {
				&-data {
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;
					margin-left: 20%;

					& > div {
						font-family: 'Arial Negreta', 'Arial Normal', Arial, sans-serif;
						font-size: 2vh;
						font-weight: 600;
					}
				}
			}
		}

		.main-center {
			width: 45%;

			& > div {
				min-height: 27vh;
				margin-bottom: 1vh;
				background-color: #040d25;
			}
		}

		.main-right {
			margin-top: -2.5vh;
			width: 26.5%;

			& > div {
				min-height: 27vh;
				margin-bottom: 1vh;
				background-color: #040d25;
			}
		}
	}
}
</style>
