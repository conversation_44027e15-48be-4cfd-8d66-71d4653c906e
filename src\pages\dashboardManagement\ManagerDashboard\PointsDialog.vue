<template>
	<div id="DialogPoints">
		<!-- 添加扣分 -->
		<el-dialog width="600px" :visible.sync="isOpen" :close-on-click-modal="false" :append-to-body="true" @close="close">
			<span slot="title">不规范扣分</span>
			<el-form
				ref="editFormRef"
				:model="editForm"
				:rules="formRules"
				label-width="100px"
				label-position="left"
				@submit.native.prevent
			>
				<el-form-item label="用户" prop="auid">
					<el-select v-model="editForm.auid" placeholder="用户" class="W100" popper-class="select-column-3" clearable filterable>
						<el-option v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid">
							<span :title="item.userName">
								{{ item.userName }}
							</span>
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="不规范事项" prop="aupcid">
					<el-select
						v-model="editForm.aupcid"
						placeholder="不规范事项"
						class="W100"
						popper-class="select-column-3"
						clearable
						filterable
					>
						<el-option
							v-for="item in scoreConfigList"
							:key="item.aupcid"
							:label="`${item.cfgName}(${item.cfgPoints}分)`"
							:value="item.aupcid"
						>
							<span :title="`${item.cfgName}(${item.cfgPoints}分)`">
								{{ `${item.cfgName}(${item.cfgPoints}分)` }}
							</span>
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="备注" prop="auditMemo">
					<el-input
						v-model="editForm.auditMemo"
						placeholder="请输入备注"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="close">取 消</el-button>
				<el-button type="primary" @click="save">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { checkRequired, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'DialogPoints',
	components: {},
	props: {
		// 用户列表
		userList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			isOpen: false,
			editForm: {
				auid: '',
				aupcid: '',
				auditMemo: '',
			},
			formRules: {
				auid: [{ required: true, message: '请选择人员', trigger: 'blur' }],
				aupcid: [{ required: true, message: '请选择不规范事项', trigger: 'blur' }],
			},
			scoreConfigList: [],
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['userInfos']) },
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 查询积分配置项（跟单方式）
		async queryScoreConfig() {
			const API = 'selectAdminUserPointsConfigurationList';
			try {
				const res = await this.$axios[API](JSON.stringify({ cfgGroup: '业务扣分项' }));
				if (res.data.success) {
					this.scoreConfigList = res.data.data.filter(item => item.cfgCate === '手工扣分') || [];
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				
				console.error(`${API} |` + error);
			}
		},
		open() {
			this.isOpen = true;
			this.queryScoreConfig();
		},
		close() {
			this.isOpen = false;
			this.editForm = resetValues(this.editForm);
			this.$nextTick(() => {
				this.$refs.editFormRef?.resetFields();
				this.$refs.editFormRef?.clearValidate();
			});
		},
		// 保存评分
		async save() {
			if (checkRequired(this.editForm, this.formRules)) return;
			const API = 'createPointManually';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.editForm, clientName: '树字工厂' }));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.$emit('update');
					this.isOpen = false;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				
				console.error(`${API} |` + error);
			}
		},
	},
};
</script>

<style lang="scss" scoped>
#DialogPoints {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
}
</style>
