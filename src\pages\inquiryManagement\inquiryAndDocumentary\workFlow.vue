<template>
	<div id="workFlow">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 询盘清单 -->
		<InquriyList
			ref="InquriyList"
			:titleName="titleName"
			:requestType="searchForm.requestType"
			:auid="auid"
			:twidList="twidList"
			:channelName="channelName"
			api="selectMonthData"
			parentComName="业务河流"
			@refresh="queryTableData()"
		/>
		<BaseLayout>
			<template #header>
				<span class="search-label">年度</span>
				<SwitchDatePicker
					class="w-100"
					v-model="searchForm.year"
					type="year"
					format="yyyy 年"
					placeholder="请选择年份"
					:clearable="false"
					@change="queryTableData"
				/>

				<el-radio-group v-model="searchForm.requestType" @change="queryTableData(1)">
					<el-radio :label="1">按询盘金额</el-radio>
					<el-radio :label="2">按询盘数量</el-radio>
					<el-radio :label="3">按跟单次数</el-radio>
				</el-radio-group>
				<el-select
					size="small"
					class="vw8"
					v-model="searchForm.channelList"
					placeholder="来源"
					clearable
					filterable
					multiple
					collapse-tags
					@change="queryTableData(1)"
				>
					<el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-select
					class="w-150"
					size="small"
					multiple
					collapse-tags
					v-model="searchForm.quality"
					placeholder="询盘质量"
					clearable
					@change="queryTableData(1)"
				>
					<el-option v-for="key in Object.keys(qualityMap)" :key="key" :label="qualityMap[key]" :value="Number(key)"> </el-option>
				</el-select>
				<el-select class="w-100" size="small" v-model="searchForm.rePurchase" placeholder="客户类型" @change="queryTableData(1)">
					<!-- 0:新客户,1:复购，2：全部 -->
					<el-option label="新客户" :value="0"> </el-option>
					<el-option label="复购" :value="1"> </el-option>
					<el-option label="全部" :value="2"> </el-option>
				</el-select>

				<SearchHistoryInput
					name="salesmanName"
					placeholder="业务顾问"
					v-model.trim="searchForm.salesman"
					@input="queryTableData(1)"
				/>

				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-toolbar">
					<el-checkbox-group class="mr-auto" v-model="searchForm.stage" @change="queryTableData(1)">
						<el-checkbox :disabled="getCheckboxDisabled(item)" v-for="item in stageList" :label="item.value" :key="item.value">
							<span class="fs-12">{{ item.label }}</span>
						</el-checkbox>
					</el-checkbox-group>
					<ExportBtn v-if="isSuperAdmin || isFinance" @trigger="openExport" />
				</div>

				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					pagination-show
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column label="序号" width="50" align="center" type="index"> </u-table-column>

					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<Tooltips
								v-if="item.colNo != 'salesman'"
								@click.native="openDetail(scope.row, item.colNo)"
								class="hover-green"
								:cont-str="scope.row[item.colNo]?.toFixed(searchForm.requestType == 1 ? 2 : 0)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { debounce, deepClone, sortTableData, dateFormat } from '@/util/tool';
import { bigAdd } from '@/util/math';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import ExportTable from '@/components/ExportTable'; //导出组件
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
import InquriyList from '@/components/InquriyList.vue';
import { sourceList, stageList, qualityMap } from '@/assets/js/inquirySource.js';
import SwitchDatePicker from '@/components/DateSelect/SwitchDatePicker.vue';
export default {
	props: {
		twidList: Array,
		channelName: Array,
	},
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		InquriyList,
		ExportBtn,
		SwitchDatePicker,
	},
	name: 'workFlow',
	data() {
		return {
			activeTab: 'workFlow',
			salesman: '',
			canEditBtn: false,
			titleName: '',
			auid: '',

			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableDataCopy: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '业务顾问', colNo: 'salesman', align: 'left', width: '150' },
				{ colName: '一月', colNo: 'janCount', align: 'right' },
				{ colName: '二月', colNo: 'febCount', align: 'right' },
				{ colName: '三月', colNo: 'marCount', align: 'right' },
				{ colName: '四月', colNo: 'aprCount', align: 'right' },
				{ colName: '五月', colNo: 'mayCount', align: 'right' },
				{ colName: '六月', colNo: 'junCount', align: 'right' },
				{ colName: '七月', colNo: 'julCount', align: 'right' },
				{ colName: '八月', colNo: 'augCount', align: 'right' },
				{ colName: '九月', colNo: 'sepCount', align: 'right' },
				{ colName: '十月', colNo: 'octCount', align: 'right' },
				{ colName: '十一月', colNo: 'novCount', align: 'right' },
				{ colName: '十二月', colNo: 'decCount', align: 'right' },
				{ colName: '合计', colNo: 'totalCount', align: 'right' },
			],

			// 表格底部合计行
			totalOption: [
				{ label: '一月', unit: '  ' },
				{ label: '二月', unit: '  ' },
				{ label: '三月', unit: '  ' },
				{ label: '四月', unit: '  ' },
				{ label: '五月', unit: '  ' },
				{ label: '六月', unit: '  ' },
				{ label: '七月', unit: '  ' },
				{ label: '八月', unit: '  ' },
				{ label: '九月', unit: '  ' },
				{ label: '十月', unit: '  ' },
				{ label: '十一月', unit: '  ' },
				{ label: '十二', unit: '  ' },
				{ label: '合计', unit: '  ' },
			],
			// 月份列表
			selectMonth: {
				janCount: 1,
				febCount: 2,
				marCount: 3,
				aprCount: 4,
				mayCount: 5,
				junCount: 6,
				julCount: 7,
				augCount: 8,
				sepCount: 9,
				octCount: 10,
				novCount: 11,
				decCount: 12,
			},
			searchForm: {
				year: new Date().getTime(),
				startTime: '',
				endTime: '',
				quality: [],
				stage: [],
				channelList: [],
				twidList: [],
				rePurchase: 2,
				salesman: '',
				requestType: 1,
			},

			sourceList, // 来源
			qualityMap, // 询盘质量
			stageList, // 询盘阶段
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userRoles', 'userLabels']), //当前登录用户信息（含团队/菜单/权限等）
		// 是否超级管理员
		isSuperAdmin() {
			return this.userRoles?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
		// 标签是财务专员
		isFinance() {
			return this.userLabels?.some(item => item.remark == '财务专员') || false;
		},
	},
	// 监控data中的数据变化
	watch: {
		channelName(newVal) {
			this.queryTableData(1);
		},
		twidList(newVal) {
			this.queryTableData(1);
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		const localSearchForm = JSON.parse(window?.localStorage.getItem(this.activeTab + '_searchForm')) || {};
		this.searchForm = { ...this.searchForm, ...localSearchForm };
		this.queryTableData();
		this.isStartUser();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 如果点击了签单其他不可选
		getCheckboxDisabled(item) {
			if (this.searchForm?.stage?.includes(5) && item.value !== 5) {
				return true;
			} else {
				return false;
			}
		},
		// 判断是否为业务范围星号用户
		isStartUser() {
			const str = JSON.stringify({});
			this.$axios
				.selectSuperUser(str)
				.then(res => {
					if (res.data.success) {
						this.canEditBtn = Number(res.data.message);
						if (this.userInfos?.adminUserVO.adminRoleVOS) {
							this.userInfos?.adminUserVO.adminRoleVOS.map(item => {
								//  超级管理员可查看
								item.arid == 1 && (this.canEditBtn = true);
							});
						}
					}
				})
				.catch(error => {
					console.log('selectSuperUser |' + error);
				});
		},

		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => Number(item[column.property]));
				if (!values.every(value => isNaN(value))) {
					means[columnIndex] = values.reduce((prev, curr) => {
						const value = Number(curr);
						if (!isNaN(value)) {
							return bigAdd(prev, curr, this.searchForm.requestType == 1 ? 2 : 0);
						} else {
							return prev;
						}
					}, 0);
					if (means[columnIndex] > 0) {
						// const summaryClass = this.requestType == 1 ? 'hover-green pr8' : 'hover-green pr10';
						const summaryClass = 'hover-green ';
						means[columnIndex] = (
							<span
								class={summaryClass}
								on-click={e => {
									e.stopPropagation();
									this.openDetail('', columnIndex);
								}}
							>
								{this.searchForm.requestType == 1 ? means[columnIndex] + '万' : means[columnIndex]}
							</span>
						);
					} else {
						means[columnIndex] = '';
					}
				} else {
					means[columnIndex] = '';
				}
			}
			return [means];
		},
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData(1);
		},
		//自定义排序(非虚拟表格)
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableDataCopy;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},

		// 打开询盘清单
		openDetail(row, months) {
			// if (!this.canEditBtn && row.auid !== this.userInfos?.adminUserVO.auid) {
			// 	this.$message.warning('没有查看的权限！');
			// 	return;
			// }

			// 非(超级管理员/业务范围内/当前人员)不可查看
			if (
				!(
					this.isSuperAdmin ||
					this.userInfos?.adminUserVO.auid == row?.auid ||
					this.userInfos?.adminUserVO?.salesmanUid?.includes(row?.auid) ||
					this.userInfos?.adminUserVO?.salesmanUid?.split(',').find(i => i == '888')
				)
			) {
				return this.$message.warning(`抱歉，您没有查看${row?.salesman ? row.salesman : '合计'}询盘的权限！`);
			}

			if (!row) {
				months = months - 1;
				this.titleName = `${new Date(this.searchForm.startTime).getFullYear()}年${months}月`;
				this.auid = '';

				if (months <= 12) {
					const year = new Date(this.searchForm.startTime).getFullYear();
					const firstDay = new Date(year, months - 1, 1).getTime();
					const lastDay = new Date(year, months, 1).getTime() - 1;
					this.searchForm.startTime = firstDay; //根即传入的月份获取当前年某月的第一天
					this.searchForm.endTime = lastDay;
				} else {
					this.titleName = `${new Date(this.searchForm.startTime).getFullYear()}年`;
					this.queryTableData();
				}
			} else {
				this.titleName = row.salesman;
				this.auid = row.auid;

				if (this.selectMonth[months]) {
					const year = new Date(this.searchForm.startTime).getFullYear();
					const month = this.selectMonth[months];
					const firstDay = new Date(year, month - 1, 1).getTime();
					const lastDay = new Date(year, month, 1).getTime() - 1;
					this.searchForm.startTime = firstDay;
					this.searchForm.endTime = lastDay;
				} else {
					this.queryTableData();
				}
			}

			setTimeout(() => {
				const DATA = { ...this.searchForm };
				this.$refs.InquriyList.openList(DATA);
			}, 300);
		},
		// 获取业务河流数据
		queryTableData: debounce(function (type) {
			if (this.searchForm?.stage?.length > 1 && this.searchForm?.stage?.includes(5)) {
				// 去除5之外的选项
				this.searchForm.stage = [5];
			}
			window?.localStorage.setItem(this.activeTab + '_searchForm', JSON.stringify(this.searchForm));
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			this.searchForm.startTime = this.$moment(this.searchForm.year).startOf('year').valueOf();
			this.searchForm.endTime = this.$moment(this.searchForm.year).endOf('year').valueOf();
			this.$axios
				.selectOperationFlow(JSON.stringify({ ...this.searchForm, twidList: this.twidList, channelName: this.channelName }))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data.sort((a, b) => b.totalCount - a.totalCount);
						this.tableDataCopy = deepClone(this.tableData);
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectOperationFlow |' + error);
				});
		}),

		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({ ...this.searchForm, twidList: this.twidList, channelName: this.channelName }), //接口参数
				API: 'exportOperationFlow', //导出接口
				downloadData: '业务河流导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),

		//日期format
		dateFormat: dateFormat,
	},
};
</script>

<style lang="scss" scoped>
#workFlow {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
