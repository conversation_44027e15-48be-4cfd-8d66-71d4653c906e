<template>
	<div class="course-detail" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">开始学习</span>
				<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content flex-column gap-10">
				<!-- 顶部课程信息 -->
				<header class="flex-align-center gap-10 pt10 pb5">
					<span>课程编号： {{ detailForm.coursesNo }}；</span>
					<span>课程名称： {{ detailForm.coursesName }}；</span>
					<span>课程简介： {{ detailForm.coursesIntroduction }}；</span>
					<span v-show="getProgress" class="orange">学习进度： {{ getProgress }}；</span>
					<span v-show="getTestProgress" class="blue">考试进度： {{ getTestProgress }}；</span>
					<!-- <span v-show="getCorrectRate" class="green">准确率： {{ getCorrectRate }}</span> -->
				</header>
				<!-- 中间内容 -->
				<main class="border flex H90">
					<!-- 左侧目录 -->
					<section class="left-question">
						<div ref="leftQuestionWrapper" class="left-question-wrapper flex-column gap-10">
							<div
								class="left-question-item border pointer p10 color-666"
								:class="currentIndex == index ? ' bg-selected-green sticky-top-10 sticky-bottom' : ''"
								v-for="(item, index) in detailForm.courseDetailsVOS"
								:key="'left-question-item-' + index"
								@click="changeSection(index)"
							>
								<!-- 题目 -->
								<div class="flex-align-center gap-10">
									<!-- 课程详情类型 0-图片 1-媒体 2-文本 3-考题 -->
									<div v-if="item.type == 0" class="image">🖼️</div>
									<div v-if="item.type == 1" class="image">🎞️</div>
									<div v-if="item.type == 2" class="image">📔</div>
									<div v-if="item.type == 3" class="image">📝</div>
									<Tooltips class="fs-12" :cont-str="`${index + 1}.${item.title} `" :cont-width="300"> </Tooltips>

									<div v-if="item.isCompletedCourse" class="ml-auto">
										<i v-if="item.isCorrectCourseDetail" class="el-icon-success green"></i>
										<i v-else class="el-icon-error red"></i>
									</div>
								</div>
							</div>
						</div>
					</section>
					<!-- 右侧预览 -->
					<section class="right-preview">
						<!-- 背景图 -->
						<img class="bg-logo" src="@/assets/img/lightmes-logo.webp" />
						<div class="W100 H100" v-if="detailForm.courseDetailsVOS && detailForm.courseDetailsVOS.length > 0">
							<!-- 图片 -->
							<div class="right-preview-item" v-if="detailForm.courseDetailsVOS[currentIndex].type == 0">
								<div class="flex-align-center gap-10">
									<!-- 标题 -->
									<div class="label-title W100">
										<span>{{ currentIndex + 1 }}. </span>
										<span>{{ detailForm.courseDetailsVOS[currentIndex].title }}</span>
									</div>

									<!-- 倒计时 -->
									<el-statistic
										class="ml-auto w-100"
										v-if="!detailForm.courseDetailsVOS[currentIndex].isCompletedCourse"
										:key="elementKey"
										ref="statistic"
										@finish="finishDeadline"
										format="HH:mm:ss"
										:value="getDeadline()"
										time-indices
									>
									</el-statistic>
								</div>

								<el-image
									class="W100 H100 border"
									v-if="detailForm.courseDetailsVOS[currentIndex].imageUrl"
									:src="detailForm.courseDetailsVOS[currentIndex].imageUrl"
								>
								</el-image>
							</div>

							<!-- 视频 -->
							<div class="right-preview-item" v-if="detailForm.courseDetailsVOS[currentIndex].type == 1">
								<div class="flex-align-center gap-10">
									<!-- 标题 -->
									<div class="label-title W100">
										<span>{{ currentIndex + 1 }}. </span>
										<span>{{ detailForm.courseDetailsVOS[currentIndex].title }}</span>
									</div>
									<!-- 倒计时 -->
									<el-statistic
										class="ml-auto w-100"
										v-if="!detailForm.courseDetailsVOS[currentIndex].isCompletedCourse"
										:key="elementKey"
										ref="statistic"
										@finish="finishDeadline"
										format="HH:mm:ss"
										:value="getDeadline()"
										time-indices
									>
									</el-statistic>
								</div>
								<iframe
									height="650"
									class="W100 border min-h-500"
									v-if="detailForm.courseDetailsVOS[currentIndex].mediaUrl"
									:src="detailForm.courseDetailsVOS[currentIndex].mediaUrl + '?rel=0&amp;autoplay=1'"
									frameborder="0"
									allowfullscreen
								></iframe>
								<!-- <video
									class="W100 H100 border"
									v-if="detailForm.courseDetailsVOS[currentIndex].mediaUrl"
									:src="detailForm.courseDetailsVOS[currentIndex].mediaUrl"
									:autoplay="detailForm.courseDetailsVOS[currentIndex].isAutoPlay == 0"
									controls
								></video> -->
							</div>

							<!-- 富文本 -->
							<div class="right-preview-item" v-if="detailForm.courseDetailsVOS[currentIndex].type == 2">
								<div class="flex-align-center gap-10">
									<!-- 标题 -->
									<div class="label-title W100">
										<span>{{ currentIndex + 1 }}. </span>
										<span>{{ detailForm.courseDetailsVOS[currentIndex].title }}</span>
									</div>
									<el-statistic
										class="ml-auto w-100"
										v-if="!detailForm.courseDetailsVOS[currentIndex].isCompletedCourse"
										:key="elementKey"
										ref="statistic"
										@finish="finishDeadline"
										format="HH:mm:ss"
										:value="getDeadline()"
										time-indices
									>
									</el-statistic>
								</div>

								<wang-editor
									class="W100"
									v-model="detailForm.courseDetailsVOS[currentIndex].textContent"
									:editorHeight="editorHeight"
									disabled
									:hasToolBar="false"
								></wang-editor>
							</div>
							<!-- 考题(未完成) -->
							<div
								class="right-preview-item"
								v-if="
									detailForm.courseDetailsVOS[currentIndex].type == 3 &&
									!detailForm.courseDetailsVOS[currentIndex].isCompletedCourse
								"
							>
								<!-- 标题 -->
								<div class="label-title W100 ellipsis">
									<Tooltips
										toolClass="tdTwoNormal"
										:cont-str="`${currentIndex + 1}. ${detailForm.courseDetailsVOS[currentIndex].title} `"
										:cont-width="300"
									>
									</Tooltips>
								</div>
								<Tooltips
									toolClass="tdTwoNormal"
									class="fs-12"
									:cont-str="detailForm.courseDetailsVOS[currentIndex].examQuestionsDetail"
									:cont-width="300"
								>
								</Tooltips>
								<!-- 单选 -->
								<div v-if="detailForm.courseDetailsVOS[currentIndex].examQuestionsMode == 0">
									<el-radio-group v-model="radio">
										<el-radio
											class="W90 mb10"
											:label="item.isUserChooseAnswer"
											v-for="(item, index) in detailForm.courseDetailsVOS[currentIndex].selectAnswerVOS"
											:key="'radio-' + index"
											@change="singleChange(item, detailForm.courseDetailsVOS[currentIndex].selectAnswerVOS)"
										>
											<span>{{ ['A. ', 'B. ', 'C. ', 'D. '][index] }} {{ item.answer }}</span>
										</el-radio>
									</el-radio-group>
								</div>
								<!-- 多选 -->
								<div v-if="detailForm.courseDetailsVOS[currentIndex].examQuestionsMode == 1">
									<el-checkbox
										class="W90 mb10"
										:checked="item.isUserChooseAnswer"
										v-for="(item, index) in detailForm.courseDetailsVOS[currentIndex].selectAnswerVOS"
										:key="'checkbox-' + index"
										@change="multiChange(item)"
									>
										<span>{{ ['A. ', 'B. ', 'C. ', 'D. '][index] }} {{ item.answer }}</span>
									</el-checkbox>
								</div>
								<!-- 提交按钮 -->
								<div class="flex-align-center gap-10">
									<el-button class="vw10" type="primary" @click="saveTestQuestion" :loading="submitStatus">提交</el-button>
									<!-- <el-checkbox v-model="autoNext" :true-label="1" :false-label="0"> 自动进入下一题 </el-checkbox> -->
								</div>
							</div>

							<!-- 考题(已完成) -->
							<div
								class="right-preview-item"
								v-if="
									detailForm.courseDetailsVOS[currentIndex].type == 3 &&
									detailForm.courseDetailsVOS[currentIndex].isCompletedCourse
								"
							>
								<!-- 标题 -->
								<div class="label-title W100 ellipsis">
									<Tooltips
										toolClass="tdTwoNormal"
										:cont-str="`${currentIndex + 1}. ${detailForm.courseDetailsVOS[currentIndex].title} `"
										:cont-width="300"
									>
									</Tooltips>
								</div>
								<Tooltips
									toolClass="tdTwoNormal"
									class="fs-12"
									:cont-str="detailForm.courseDetailsVOS[currentIndex].examQuestionsDetail"
									:cont-width="300"
								>
								</Tooltips>
								<!-- 单选 -->
								<div v-if="detailForm.courseDetailsVOS[currentIndex].examQuestionsMode == 0">
									<el-radio-group v-model="radio">
										<el-radio
											:disabled="!!detailForm.courseDetailsVOS[currentIndex].isCorrectCourseDetail"
											class="W90 mb10"
											:label="item.isUserChooseAnswer"
											v-for="(item, index) in detailForm.courseDetailsVOS[currentIndex].selectAnswerVOS"
											:key="'radio-' + index"
											@change="singleChange(item, detailForm.courseDetailsVOS[currentIndex].selectAnswerVOS)"
										>
											<span>{{ ['A. ', 'B. ', 'C. ', 'D. '][index] }} {{ item.answer }}</span>
										</el-radio>
									</el-radio-group>
								</div>
								<!-- 多选 -->
								<div v-if="detailForm.courseDetailsVOS[currentIndex].examQuestionsMode == 1">
									<el-checkbox
										:disabled="!!detailForm.courseDetailsVOS[currentIndex].isCorrectCourseDetail"
										class="W90 mb10"
										:checked="item.isUserChooseAnswer"
										v-for="(item, index) in detailForm.courseDetailsVOS[currentIndex].selectAnswerVOS"
										:key="'checkbox-' + index"
										@change="multiChange(item)"
									>
										<span>{{ ['A. ', 'B. ', 'C. ', 'D. '][index] }} {{ item.answer }}</span>
									</el-checkbox>
								</div>
								<!-- 提交按钮 -->
								<div class="flex-align-center gap-10" v-if="!detailForm.courseDetailsVOS[currentIndex].isCorrectCourseDetail">
									<el-button
										class="vw10"
										:type="detailForm.courseDetailsVOS[currentIndex].isCorrectCourseDetail == 0 ? 'warning' : 'primary'"
										@click="saveTestQuestion"
										:loading="submitStatus"
									>
										{{ detailForm.courseDetailsVOS[currentIndex].isCorrectCourseDetail == 0 ? '重新作答' : '提交' }}
									</el-button>

									<!-- <el-checkbox v-model="autoNext" :true-label="1" :false-label="0"> 自动进入下一题 </el-checkbox> -->
								</div>

								<!-- 提示 -->
								<div v-if="detailForm.courseDetailsVOS[currentIndex].isCorrectCourseDetail == 1" class="flex-column gap-10">
									<div class="green"> 🎉 恭喜你，回答正确! ✨ </div>
									<div class="color-999">
										<span v-if="detailForm.courseDetailsVOS[currentIndex].answerDetailedExplanation">
											📋 题目解析：{{ detailForm.courseDetailsVOS[currentIndex].answerDetailedExplanation }}
										</span>
										<span v-else>🤷‍♂️ 🤷‍♀️ 本题暂无解析，如对答案有疑问，请联系管理员</span>
									</div>
								</div>
								<div v-else class="flex-column gap-10">
									<div class="red">‼️ 很遗憾，回答错误!</div>
									<div class="color-999">💪 请再仔细阅读题目后重新作答，加油一鼓作气！</div>
								</div>

								<!-- 上下题切换 -->
								<div class="flex-align-center gap-30">
									<el-button
										type="text"
										class="p0 m0 el-icon-caret-top"
										:disabled="currentIndex == 0"
										@click="currentIndex > 0 ? --currentIndex : ''"
										>上一题</el-button
									>
									<el-button
										type="text"
										class="p0 m0 el-icon-caret-bottom"
										:disabled="currentIndex == detailForm.courseDetailsVOS.length - 1"
										@click="currentIndex < detailForm.courseDetailsVOS.length - 1 ? ++currentIndex : ''"
									>
										下一题
									</el-button>
								</div>
							</div>
						</div>
					</section>
				</main>
				<!-- 底部操作按钮 -->
				<!-- <footer class="h-40 text-right">
						<el-button type="primary" @click="saveDetail">保存</el-button>
					</footer> -->
			</div>
		</div>
	</div>
</template>
<script>
import { resetValues, deepClone, dateFormat, jointString } from '@/util/tool';
import { mapGetters } from 'vuex';
import wangEditor from '@/components/WangEditor/wangEditor';

export default {
	name: 'CourseDetail',
	directives: {},
	components: { wangEditor },
	props: {
		channelName: {
			type: Array,
			default: () => {
				return [];
			},
		},
		twidList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			detailForm: {
				cid: '',
				tpid: '',
				courseNo: '',
				courseName: '',
				coursesIntroduction: '',
				courseDetailsVOS: [],
			},
			currentIndex: 0,

			elementKey: 0,
			radio: true,

			submitStatus: false, // 正在提交状态
			autoNext: 0, // 自动进入下一题
		};
	},
	created() {},
	computed: {
		editorHeight() {
			// return this.showType ? 480 : 550;
			return 500;
		},
		// 获取学习进度（非考题）
		getProgress() {
			const nonTestCount = this.detailForm.courseDetailsVOS?.filter(item => item.type != 3).length;
			const completedCount = this.detailForm.courseDetailsVOS?.filter(item => item.isCompletedCourse && item.type != 3).length;
			if (!nonTestCount) return 0;
			return `${completedCount}/${nonTestCount}`;
		},
		// 获取考题完成进度(考题)
		getTestProgress() {
			const correctCount = this.detailForm.courseDetailsVOS?.filter(item => item.type == 3 && item.isCompletedCourse).length;
			const totalCount = this.detailForm.courseDetailsVOS?.filter(item => item.type == 3).length;
			if (!totalCount) return 0;
			return `${correctCount}/${totalCount}`;
		},
		// 获取准确率(考题)
		getCorrectRate() {
			const correctCount = this.detailForm.courseDetailsVOS?.filter(
				item => item.type == 3 && item.isCompletedCourse && item.isCorrectCourseDetail,
			).length;
			const totalCount = this.detailForm.courseDetailsVOS?.filter(item => item.type == 3 && item.isCompletedCourse).length;
			if (!totalCount) return 0;
			return `${correctCount}/${totalCount}`;
		},
		// 获取当前章节
		currentSection() {
			return this.detailForm.courseDetailsVOS[this.currentIndex];
		},
		// 是否进入下一题
		isNext() {
			return (
				this.autoNext == 1 &&
				this.currentSection.type == 3 &&
				this.currentSection.isCorrectCourseDetail == 1 &&
				this.currentIndex + 1 <= this.detailForm.courseDetailsVOS.length
			);
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.detailForm = resetValues(this.detailForm); //重置对象
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 切换章节
		changeSection(index) {
			this.elementKey += 1;
			this.currentIndex = index;
			this.radio = true;
			const currentItem = this.currentSection;
			if (!currentItem.isCompletedCourse && Number(currentItem.minReadTime) == 0) {
				// this.saveTestQuestion(); //会跟倒计时结束回调方法重复
			}
		},
		// 获取倒计时
		getDeadline() {
			let deadline = this.currentSection.minReadTime * 1000 + Date.now();
			if (this.currentSection.isCompletedCourse) {
				deadline = Date.now();
			}
			return deadline;
		},
		// 多选
		multiChange(item) {
			if (item.isUserChooseAnswer) {
				item.isUserChooseAnswer = false;
			} else {
				item.isUserChooseAnswer = true;
			}
			console.log(item);
		},
		// 单选
		singleChange(item, list) {
			this.radio = true;
			for (let index = 0; index < list.length; index++) {
				if (list[index].answer == item.answer) {
					list[index].isUserChooseAnswer = true;
				} else {
					list[index].isUserChooseAnswer = false;
				}
			}
			console.log(item, list);
		},

		// 查询明细
		queryDetail({ cid, tpid }, type = 'refresh') {
			if (!cid || !tpid) return;
			this.$axios
				.selectUserTrainPlanCourseDetailCase(JSON.stringify({ cid, tpid }))
				.then(res => {
					if (res.data.success) {
						this.detailForm = { ...this.detailForm, ...res.data.data };
						this.submitStatus = false;
						console.log(this.detailForm);
						if (type === 'init') {
							this.currentIndex = 0;
						} else if (type === 'refresh' && this.isNext) {
							this.currentIndex += 1;
						}
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectCourseDetails |' + error);
				});
		},
		// 倒计时结束
		finishDeadline() {
			if (!this.currentSection.isCompletedCourse) {
				this.saveTestQuestion(); //倒计时结束，保存考题
			}
		},
		// 添加/保存信息
		saveTestQuestion() {
			const cdid = this.currentSection.cdid;
			if (!cdid) return this.$message.error('当前章节不存在，无法提交，请刷新页面后重试!');

			// 设置选项答案
			const aids = this.currentSection?.selectAnswerVOS?.filter(item => item.isUserChooseAnswer).map(item => item.aid) || [];
			if (this.currentSection.type == 3 && aids.length == 0) {
				return this.$message.error('当前选项为空，无法提交，请选择答案后再提交!');
			}

			// 提交状态
			this.submitStatus = true;
			const API = 'recordCurrentUserCompleteCourseDetailLogs';
			this.$axios[API](JSON.stringify({ aids, cdid, cid: this.detailForm.cid, tpid: this.detailForm.tpid }))
				.then(res => {
					if (res.data.success) {
						this.queryDetail(this.detailForm);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},
		//显示弹窗
		showDetailCom(rowData) {
			if (rowData) {
				this.detailForm = { ...this.detailForm, ...rowData };
				this.queryDetail(this.detailForm, 'init');
			}
			this.showCom = true;
		},

		dateFormat: dateFormat, //日期format
		jointString: jointString,
	},
};
</script>
<style lang="scss" scoped>
.course-detail {
	main {
		.left-question {
			min-width: 23%;
			max-width: 23%;
			height: 100%;
			background-color: #efefef;

			.left-question-wrapper {
				height: 100%;
				padding: 10px;
				overflow: scroll;
				.left-question-item {
					border-radius: 5px;
					background-color: #fff;
				}
				.sticky-bottom {
					bottom: -11px;
				}
			}
		}

		.right-preview {
			width: 100%;
			height: 100%;
			overflow: scroll;
			border-left: 1px solid #dbdee4;
			position: relative;
			.right-preview-item {
				display: flex;
				flex-direction: column;
				gap: 10px;
				padding: 15px 15px 15px 20px;
			}

			.bg-logo {
				position: absolute;
				background-size: cover;
				opacity: 0.05;
				top: 50%;
				left: 50%;
				width: 800px;
				transform: translate(-50%, -50%);
				pointer-events: none; // 禁用鼠标事件
			}
		}
	}
}
</style>
