<!-- 一个带建议的日期选择器 使用说明请阅读 README.md  -->
<template>
	<div class="DateSelect">
		<el-select
			ref="DateSelect"
			class="W100"
			popper-class="DateSelect-popper"
			:size="size"
			v-model="dateString"
			:placeholder="placeholder"
			@change="changeDateSelect"
			@visible-change="visibleChange"
			:clearable="clearable"
		>
			<el-option v-for="item in dateList" :key="item" :label="item" :value="item"> </el-option>
			<div class="pt5 pl20 pr20 w-350 color-666 fs-14">
				<!-- 日期范围选择器 -->
				<span :class="{ 'primary , bolder': isRange }">自定义</span>
				<el-date-picker
					ref="DatePicker"
					class="W80 ml10"
					size="mini"
					v-model="dateRanges"
					type="daterange"
					value-format="timestamp"
					start-placeholder="开始日期"
					range-separator="至"
					end-placeholder="结束日期"
					@focus="
						isRange = true;
						dateList.includes(dateString) && (dateString = '');
					"
					@blur="isRange = false"
					@change="changeDatePicker"
				>
				</el-date-picker>
			</div>
		</el-select>
	</div>
</template>
<script>
export default {
	name: 'DateSelect',
	props: {
		// 建议选项列表
		dateList: {
			type: Array,
			default: function () {
				return ['不限定', '今天', '昨天', '本周', '本月', '上月'];
			},
		},

		// 默认建议选项
		defaultDate: {
			type: String,
			default: function () {
				return '不限定';
			},
		},

		// 日期对象（一般不用，默认日期请使用defaultDate，当外部需要返回时用 如：本地缓存回传日期）
		dateSelectObj: {
			type: Object,
			default: function () {
				return {
					endTime: '',
					startTime: '',
				};
			},
		},

		// 占位符
		placeholder: {
			type: String,
			default: function () {
				return '请选择日期';
			},
		},

		// 尺寸
		size: {
			type: String,
			default: function () {
				return 'small';
			},
		},

		// 开始和结束时间的key 传入时先start再end
		dateKeys: {
			type: Array,
			default: function () {
				return ['startTime', 'endTime'];
			},
		},

		// 可清除
		clearable: {
			type: Boolean,
			default: function () {
				return true;
			},
		},
	},
	data() {
		return {
			dateString: '不限定', //选择的日期（建议选项）
			dateRanges: [], //选择的日期(日期范围选择器)
			isRange: false, //激活自定义/日期范围选择器
		};
	},
	created() {},
	mounted() {
		this.dateString = this.defaultDate;

		// 当传入了非默认的dateKeys时
		if (this.dateKeys[0] != 'startTime' || this.dateKeys[1] != 'endTime') {
			this.dateList.map(item => {
				this.DATE_MAP[item][this.dateKeys[0]] = this.DATE_MAP[item].startTime;
				this.DATE_MAP[item][this.dateKeys[1]] = this.DATE_MAP[item].endTime;
				// 删除原来的startTime和endTime
				delete this.DATE_MAP[item].startTime;
				delete this.DATE_MAP[item].endTime;
			});
		}
		if (this.dateList.includes(this.dateString)) {
			this.changeDateSelect(this.dateString);
		} else {
			this.isRange = true;
		}
	},
	destroyed() {},
	watch: {
		// 监听日期对象 用于返回显示日期的字符串
		dateSelectObj(newVal) {
			/**
			 * 返回给定对象中第一个匹配的键，其 endTime 和 startTime 属性与给定的 value 对象的值匹配
			 *
			 * @param {Object} object - 要搜索的对象。
			 * @param {Object} value - 要与其 endTime 和 startTime 属性进行匹配的对象。
			 * @return {string} 对象中第一个匹配条目的键，如果找不到匹配项，则返回 undefined。
			 */
			function getDateString(object, value, dateKeys) {
				return Object.entries(object).find(
					([key, val]) => val[dateKeys[1]] === value[dateKeys[1]] && val[dateKeys[0]] === value[dateKeys[0]],
				)?.[0];
			}

			if (getDateString(this.DATE_MAP, newVal, this.dateKeys)) {
				this.dateString = getDateString(this.DATE_MAP, newVal, this.dateKeys); // 建议选项
			} else {
				// 日期范围选择器
				const startTime = this.$moment(newVal[this.dateKeys[0]]).format('YYYY-MM-DD');
				const endTime = this.$moment(newVal[this.dateKeys[1]]).format('YYYY-MM-DD');
				this.dateString = `${startTime} 至 ${endTime}`; //返回显示日期的字符串
				this.dateRanges = [newVal[this.dateKeys[0]], newVal[this.dateKeys[1]]];
			}
		},
	},
	computed: {
		DATE_MAP() {
			console.log('computed：DATE_MAP');
			// 日期映射（匹配）对象（使用moment.js按需添加）
			return {
				不限定: {
					endTime: '',
					startTime: '',
				},
				// 常见日期建议
				昨天: {
					endTime: this.$moment().startOf('day').valueOf() - 1, //今天 0点0分0秒 -1
					startTime: this.$moment().subtract(1, 'day').startOf('day').valueOf(), //昨天 0点0分0秒 -1
				},
				今天: {
					endTime: this.$moment().add(1, 'day').startOf('day').valueOf() - 1, //明天 0点0分0秒 -1
					startTime: this.$moment().startOf('day').valueOf(), //今天 0点0分0秒
				},
				上周: {
					endTime: this.$moment().subtract(1, 'week').endOf('isoWeek').valueOf(), //上周最后一天 23点59分59秒
					startTime: this.$moment().subtract(1, 'week').startOf('isoWeek').valueOf(), //上周第一天 0点0分0秒
				},
				本周: {
					endTime: this.$moment().endOf('isoWeek').valueOf(), //本周最后一天 23点59分59秒
					startTime: this.$moment().startOf('isoWeek').valueOf(), //本周第一天 0点0分0秒
				},
				上月: {
					endTime: this.$moment().subtract(1, 'month').endOf('month').valueOf(), //上月最后一天 23点59分59秒
					startTime: this.$moment().subtract(1, 'month').startOf('month').valueOf(), //上月第一天 0点0分0秒
				},
				本月: {
					endTime: this.$moment().endOf('month').valueOf(), //本月最后一天 23点59分59秒
					startTime: this.$moment().startOf('month').valueOf(), //本月第一天 0点0分0秒
				},
				本年: {
					endTime: this.$moment().endOf('year').valueOf(),
					startTime: this.$moment().startOf('year').valueOf(),
				},
				今年: {
					endTime: this.$moment().endOf('year').valueOf(),
					startTime: this.$moment().startOf('year').valueOf(),
				},
				// 最近n天 直接修改 subtract(n, 'days') 添加即可
				最近7天: {
					endTime: this.$moment().endOf('day').valueOf(),
					startTime: this.$moment().subtract(7, 'days').startOf('day').valueOf(),
				},
				最近15天: {
					endTime: this.$moment().endOf('day').valueOf(),
					startTime: this.$moment().subtract(15, 'days').startOf('day').valueOf(),
				},
				最近30天: {
					endTime: this.$moment().endOf('day').valueOf(),
					startTime: this.$moment().subtract(30, 'days').startOf('day').valueOf(),
				},
				最近60天: {
					endTime: this.$moment().endOf('day').valueOf(),
					startTime: this.$moment().subtract(60, 'days').startOf('day').valueOf(),
				},
				// 未来n天 直接修改 add(n, 'days') 添加即可
				下月: {
					endTime: this.$moment().add(1, 'month').endOf('month').valueOf(), //下月最后一天 23点59分59秒
					startTime: this.$moment().add(1, 'month').startOf('month').valueOf(), //下月第一天 0点0分0秒
				},
				未来3天: {
					endTime: this.$moment().add(3, 'days').endOf('day').valueOf(),
					startTime: this.$moment().endOf('day').valueOf(),
				},
			};
		},
	},
	methods: {
		// 选择器选项菜单展开/收起时
		visibleChange(visible) {
			// visible 为 true 时表示展开选择器选项菜单
			if (!visible) {
				// 如果激活了日期范围选择器此时激活菜单（阻止关闭）
				const isFocused = this.isRange && this.$refs.DatePicker.$el.classList.contains('is-active');
				isFocused && this.$refs.DateSelect.toggleMenu();
			} else {
				if (!this.dateList.includes(this.dateString)) {
					// 当前dateString内容是日期范围选择器
					this.isRange = true;
					const isFocused = this.isRange && this.$refs.DatePicker.$el.classList.contains('is-active');
					isFocused && this.$refs.DatePicker.updatePopper();
				} else {
					this.isRange = false;
					this.dateString == '不限定' && (this.dateRanges = []);
				}
			}
		},

		// 触发日期选择（建议选项）
		changeDateSelect(date) {
			this.dateString = date || '不限定';
			if (this.dateString !== '不限定') {
				// 显示日期范围
				const startTime = this.DATE_MAP[this.dateString][this.dateKeys[0]];
				const endTime = this.DATE_MAP[this.dateString][this.dateKeys[1]];
				this.dateRanges = [startTime, endTime];
			}
			this.$emit('change', this.DATE_MAP[this.dateString]); //触发父组件的change事件
		},

		//触发日期选择（日期范围选择器）
		changeDatePicker(date) {
			const DATE = {
				[this.dateKeys[1]]: date[1] + 24 * 3600000 - 1,
				[this.dateKeys[0]]: date[0],
			};
			const startTime = this.$moment(date[0]).format('YYYY-MM-DD');
			const endTime = this.$moment(date[1]).format('YYYY-MM-DD');
			this.dateString = `${startTime} 至 ${endTime}`; //返回显示日期的字符串
			this.$emit('change', DATE); //触发父组件的change事件
			this.$refs.DateSelect.handleClose(); //关闭选项选择器
		},
	},
};
</script>
<style lang="scss">
.DateSelect {
	width: 180px;
}
.DateSelect-popper {
	.el-select-dropdown__wrap {
		max-height: 60vh;
	}
}
</style>
