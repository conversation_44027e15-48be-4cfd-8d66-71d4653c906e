<template>
	<div id="OnlineTestingDetail" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">
					评测项目:{{ detailForm.project }}，姓名:{{ detailForm.candidateName }}，联系方式:{{ detailForm.phoneNo }}
				</span>
				<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
			</div>
			<!-- 
			-------------------------------------------------------------	
			---------------------	历史遗留（写死的逻辑） -------------------
			-------------------------------------------------------------	
			-->
			<div v-if="paperType == 'legacy'" class="detail-content">
				<!-- <div class="fs-12 red">* 1. 成长能力低于27分，不通过 2. 逻辑题低于30分，不通过</div> -->
				<div class="" v-for="(aItem, index) in paperInfo" :key="index">
					<p class="detail-content-title">
						<span> {{ titleMap[aItem[0]] }}</span>
						<span> （得分：{{ detailForm[scoreMap[aItem[0]]] }}）</span>
						<!-- 提示 -->
						<el-tooltip placement="top">
							<div slot="content" class="p5">
								<pre v-for="(tItem, tIndex) in tipsMap[aItem[0]]" :key="tIndex"> {{ tIndex + 1 }}.{{ tItem }} </pre>
							</div>
							<i class="pointer el-icon-warning-outline"></i>
						</el-tooltip>
					</p>
					<div class="detail-content-options" v-for="(bItem, bIndex) in aItem[1]" :key="bItem.questionId">
						<!-- 问题 -->
						<p>{{ bIndex + 1 + '.' }}{{ bItem.question }}</p>
						<!-- 选项 -->
						<el-checkbox
							disabled
							v-for="cItem in bItem.options"
							:key="cItem.score"
							v-model="cItem.selected"
							:true-label="'1'"
							:class="checkWarning(titleMap[aItem[0]], cItem)"
						>
							{{ cItem.title }}({{ cItem.score }})
						</el-checkbox>
					</div>
				</div>
			</div>

			<!-- 
			-------------------------------------------------------------	
			-----------------	新的逻辑（动态修改） ------------------------
			-------------------------------------------------------------	
			-->
			<div v-if="paperType == 'new'" class="detail-content">
				<div class="" v-for="(aItem, index) in paperInfo" :key="index">
					<p class="detail-content-title">
						<span> {{ aItem.projectItem }}</span>
						<span> （得分：{{ aItem.actualScore }} ）</span>

						<!-- 提示 -->
						<el-tooltip placement="top">
							<div slot="content" class="p5">
								<pre> 判定说明如下： </pre>
								<pre> 1.当前大题总得分低于{{ aItem.passScore || 0 }}分时判定为不通过 </pre>
								<pre> 2.每道题目选项得分低于{{ aItem.warnScore || 0 }}分时标记红色警示</pre>
								<pre> 3.任一大题不通过时会影响最终评测结果（即不通过）</pre>
							</div>
							<span class="pointer" :class="aItem.actualScore < Number(aItem.passScore) ? 'red' : 'green'">
								{{ aItem.actualScore < aItem.passScore ? '不通过' : '通过' }}
								<i class="el-icon-warning-outline"></i>
							</span>
						</el-tooltip>
					</p>
					<div class="detail-content-options" v-for="(bItem, bIndex) in aItem.candidateQuestionVOS" :key="bItem.cqid">
						<!-- 问题 -->
						<p>{{ bIndex + 1 + '.' }}{{ bItem.questionText }}</p>
						<!-- 选项 -->
						<el-checkbox
							disabled
							v-for="cItem in bItem.questionOptionsVOS"
							:key="cItem.qoid"
							v-model="cItem.qoid"
							:true-label="bItem.selectedOqid"
							:class="{
								warning: bItem.selectedOqid == cItem.qoid && (cItem.score < Number(aItem.warnScore) || cItem.score == 0),
							}"
						>
							{{ cItem.optionText }}({{ cItem.score }})
						</el-checkbox>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import * as _ from '@/util/tool';
import { mapGetters } from 'vuex';
export default {
	name: 'OnlineTestingDetail',
	props: { warehouseOptions: Array },
	data() {
		return {
			paperType: 'new', // 页面类型 legacy为历史遗留问题 new为现在最新用的逻辑
			paperInfo: {}, //试卷
			detailForm: {}, //明细
			// 题目类别
			titleMap: {
				singleGrowUpQuestionList: '个人成长能力',
				singleLogicQuestionList: '个人逻辑能力',
			},
			// 提示
			tipsMap: {
				singleGrowUpQuestionList: [
					'当个人成长能力【得分低于27分】，判定为【不通过】',
					'当个人成长能力【选项低于4分】，将标记为【红色】',
				],
				singleLogicQuestionList: [
					'当个人逻辑能力【得分低于30分】，判定为【不通过】',
					'当个人逻辑能力【选项等于0分】，将标记为【红色】',
				],
			},
			// 得分
			scoreMap: {
				singleGrowUpQuestionList: 'growthThinkingAbility',
				singleLogicQuestionList: 'logicalThinkingAbility',
			},

			showCom: false, //控制弹窗显隐
			titleName: '',
		};
	},
	created() {},
	computed: {},
	watch: {},
	mounted() {},
	methods: {
		checkWarning(title, item) {
			// 成长能力题得分低于4分标红，对于逻辑能力题得分为0标红
			const { score, selected } = item;
			if (selected == 1 && (title == '个人成长能力' && score < 4 || title == '个人逻辑能力' && score == 0)) {
				return 'warning';
			}
		},
		//显示弹窗
		showDetailCom(rowData, type) {
			this.detailForm = rowData;
			this.paperType = type;
			if (type == 'new') {
				// 根据sort排序
				rowData.candidateItemVOS?.sort((a, b) => a?.sort - b?.sort);
				this.paperInfo = rowData.candidateItemVOS;
			} else {
				this.paperInfo = JSON.parse(rowData.singleGrowUpQuestionLists);
				// 将对象转成数组
				this.paperInfo = Object.entries(this.paperInfo);
			}

			this.showCom = true;
		},
		//日期format
		dateFormat: _.dateFormat,
		jointString: _.jointString,
	},
};
</script>
<style lang="scss">
#OnlineTestingDetail {
	.detail-content-options {
		// 禁选情况下 将选中高亮
		.is-checked {
			color: #1e9d6f !important;
			.el-checkbox__label {
				color: #1e9d6f !important;
			}
			.el-checkbox__input {
				.el-checkbox__inner {
					background-color: #1e9d6f !important;
					border-color: #1e9d6f !important;
					&::after {
						border-color: #fff !important;
						color: #1e9d6f !important;
					}
				}
			}
		}
		.warning {
			color: #f56c6c !important;
			.el-checkbox__label {
				color: #f56c6c !important;
			}
			.el-checkbox__input {
				.el-checkbox__inner {
					background-color: #f56c6c !important;
					border-color: #f56c6c !important;
					&::after {
						border-color: #fff !important;
						color: #f56c6c !important;
					}
				}
			}
		}
	}
}
</style>
