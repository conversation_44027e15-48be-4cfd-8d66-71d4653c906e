/* 差旅管理API */

const urlList = [
	//  差旅申请
	'/background/web/BusinessTripController/addBusinessTripApplication', //添加 差旅申请
	'/background/web/BusinessTripController/updateBusinessTripApplication', //修改 差旅申请
	'/background/web/BusinessTripController/deleteBusinessTripApplication', //删除 差旅申请
	'/background/web/BusinessTripController/selectInquiryBusinessTripApplicationList', //查询 某个询盘下所有差旅申请信息
	'/background/web/BusinessTripController/selectDeliverBusinessTripApplicationList', //查询 某个交付下所有的差旅申请信息
	'/background/web/BusinessTripController/selectClientAddressesByClientKeepRecordId', //查询 尝试查询备案客户地址集合
	'/background/web/BusinessTripController/selectClientAddressesByContractId', //查询 尝试查询备案客户地址集合(合同id)

	// 差旅类别
	'/background/web/travelCategoryConfigurationController/selectTravelCategoryConfiguration', //查询 差旅类别
	'/background/web/travelCategoryConfigurationController/updateTravelCategoryConfiguration', //修改 差旅类别

	// 出差审批
	'/background/web/BusinessTripController/selectBusinessTripApplicationList', //查询 差旅申请列表
	'/background/web/BusinessTripController/updateBusinessTripApplicationSubmitApprove', //提交差旅申请到审核状态
	'/background/web/BusinessTripController/updateBusinessTripApplicationBatchApprove', //批量更新差旅申请审核状态
	'/background/web/BusinessTripController/exportTripApplicationAuditList', //导出差旅申请列表

	// 费用报销申请
	'/background/web/BusinessTripController/selectBusinessTripApplicationWithReimbursementList', //查询 费用报销申请列表
	'/background/web/BusinessTripController/addBusinessTripReimbursement', //添加 差旅报销单
	'/background/web/BusinessTripController/updateBusinessTripReimbursement', //修改 差旅报销单
	'/background/web/BusinessTripController/deleteBusinessTripReimbursement', //删除 差旅报销单
	'/background/web/BusinessTripController/exportTripApplicationWithReimbursement', //导出差旅申请-费用报销列表
	'/background/web/BusinessTripController/updateTripsDaysByBtrid', //根据报销单id修改出差天数

	'/background/web/BusinessTripController/selectBusinessTripReimbursementById', //根据报销单id查询该报销单的明细，包括费用明细
	'/background/web/BusinessTripController/addBusinessTripExpenses', //添加 差旅报销费用明细
	'/background/web/BusinessTripController/updateBusinessTripExpenses', //修改 差旅报销费用明细
	'/background/web/BusinessTripController/deleteBusinessTripExpenses', //删除 差旅报销费用明细
	'/background/web/BusinessTripController/updateBusinessTripReimbursementSubmitApprove', //提交差旅报销单到审核状态
	'/background/web/BusinessTripController/updateBusinessTripReimbursementBatchApprove', //批量 更新差旅申请审核状态
	'/background/web/BusinessTripController/updateBusinessTripApplicationBatchSettlement', //差旅申请单批量结算

	// 费用报销审核
	'/background/web/BusinessTripController/selectBusinessTripReimbursementList', //查询 费用报销审批列表
	'/background/web/BusinessTripController/updateBusinessTripExpensesApprove', //更新 差旅费用明细审核状态
	'/background/web/BusinessTripController/exportTripReimbursement', //导出 差旅报销单审核列表
	'/background/web/BusinessTripController/updateBusinessTripReimbursementBatchSettlement', //报销单批量结算
	'/background/web/BusinessTripController/bpuDealWaitDeductManualSettlement', //合伙人成交待扣减手动结算

	// 上传报销凭证
	'/background/web/BusinessTripController/uploadFile', //上传报销凭证
	'/background/web/BusinessTripController/uploadMultipartFiles', //批量上传报销凭证
];

// 重名函数映射
const apiNameMap = {};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['uploadFile'].includes(urlName)) {
		timeout = 60000;
	}
	return { urlName, url, timeout };
});

export default apiList;
