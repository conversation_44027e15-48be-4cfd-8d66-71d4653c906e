
/* 
	import queryUsersByArid from '@/mixins/queryUsersByArid';
	mixins: [queryUsersByArid],
*/
export default {
	methods: {
		queryUsersByArid(arid, dataKey) {
			const str = JSON.stringify({
				arid: [arid],
			});
			this.$axios
				.selectUserByArid(str)
				.then(res => {
					if (res.data.success) {
						this[dataKey] = res.data.data[0]?.uidAndUserNameVOS;
					}
				})
				.catch(error => {

					console.log('selectUserByArid' + ' |' + error);
				});
		},
	},
};
