<template>
	<el-aside class="SideMenu">
		<el-menu
			router
			unique-opened
			mode="vertical"
			class="el-menu-vertical"
			:collapse="isCollapse"
			:collapse-transition="false"
			:default-active="$route.path"
			background-color="#304156"
			text-color="#BFCBD9"
		>
			<div v-for="mItem in menuData" :key="'menu' + mItem.id">
				<el-submenu :index="mItem.id + ''" :key="'submenu' + mItem.id" v-if="mItem.application == 1 && mItem.sonMenu">
					<div slot="title" @click="clickMenu(mItem, 'parent')">
						<i :class="mItem.icon + ' iconfont'" class="menu-icon"></i>
						<span class="ml10 fs-18">{{ mItem.menuName }}</span>
					</div>
					<el-menu-item-group>
						<span v-show="isCollapse" slot="title">{{ mItem.menuName }}</span>
						<!-- el-menu的default-active和el-menu-item的index一致才可与router-link同步联动 -->
						<el-menu-item
							v-for="child in mItem.sonMenu"
							:index="'/' + child.path"
							:route="{ path: child.path, query: { queryId: userInfos.adminUserVO.phoneNo } }"
							:key="'menuItem' + child.id"
							@click.native.stop="clickMenu(child, 'child')"
						>
							<span class="menu-item-text" slot="title">
								{{ child.menuName }}
							</span>
						</el-menu-item>
					</el-menu-item-group>
				</el-submenu>
				<el-menu-item
					v-if="!mItem.sonMenu"
					index="/welCome"
					:route="{ path: mItem.path, query: { queryId: userInfos.adminUserVO.phoneNo } }"
					@click.native.stop="clickMenu(mItem, 'parent')"
				>
					<i :class="mItem.icon" class="menu-icon"></i>
					<span slot="title" class="ml10 fs-18">{{ mItem.menuName }}</span>
				</el-menu-item>
			</div>
		</el-menu>
	</el-aside>
</template>
<script>
import { mapGetters } from 'vuex';
// import MENU from '@/assets/js/MENU'; // 静态菜单数据

export default {
	name: 'SideMenu', //该组件主要用于侧边栏菜单展示、获取权限控制信息等
	components: {},
	data() {
		return {
			// MENU,
			menuData: [], //菜单数据
			menuTitle: { id: '' },
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队等）
		...mapGetters(['menuList']),
		...mapGetters(['isCollapse']),
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		// this.getBtnMap(this.menuList);
		// this.getBtnAuths(this.menuList);
		this.getMenuData(this.menuList);
		this.$store.commit('setIsCollapse', false);
	},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 获取按钮权限
		getBtnAuths(menuArr) {
			//菜单的导出和结案权限,为了避免大范围改动
			const btnAuth = {};
			menuArr?.forEach(item => {
				//首页特殊处理
				if (item.resourceCode == 'SY') {
					btnAuth[item.arcid] = {
						edit: item.edit,
					};
				} else {
					item?.adminResourceVOS?.forEach(rItem => {
						btnAuth[rItem.arcid] = {
							edit: rItem?.edit, //编辑
							downloadData: rItem?.downloadData, //下载
							close: rItem?.close, //结案
						};
					});
				}
			});
			// console.log('btnAuth :>> ', btnAuth);
			// this.$store.commit('setbtnAuth', btnAuth);
			window.localStorage.setItem('btnAuth', JSON.stringify(btnAuth));
		},
		getBtnMap(arr) {
			const self = this;
			const buttonMap = new Map();
			let count = 0,
				setCount = 0;
			function SetVueBtn(arr, buttonMap) {
				count += arr.length;
				if (arr && arr.length > 0) {
					arr.forEach(aItem => {
						if (aItem.adminResourceVOS && aItem.adminResourceVOS.length > 0) {
							setCount++;
							SetVueBtn(aItem.adminResourceVOS, buttonMap);
						} else {
							buttonMap.set(aItem.arcid, aItem.edit);
							setCount++;
							if (setCount == count) {
								self.$store.commit('setButtonMap', buttonMap);
								// console.log('butMap complete');
								// self.isRouterAlive = true;
							}
						}
					});
				}
			}
			SetVueBtn(arr, buttonMap);
		},

		// 转换成菜单所需的数据结构
		getMenuData(menuArr) {
			this.menuData = menuArr?.map((mItem, mIndex) => {
				const sonMenu = mItem.adminResourceVOS?.map((sonmItem, sonmIndex) => ({
					id: sonmItem.arcid,
					parentRcid: sonmItem.arcid,
					menuName: sonmItem.resourceName,
					resourceCode: sonmItem.resourceCode,
					path: sonmItem.url || 'Notfound' + mIndex + sonmIndex,
					parentMenuName: mItem.resourceName,
				}));
				return {
					application: mItem.application,
					id: mItem.arcid,
					resourceCode: mItem.resourceCode,
					menuName: mItem.resourceName,
					icon: mItem.icon || 'el-icon-menu',
					sonMenu,
					path: mItem.url || (sonMenu && sonMenu[0] && sonMenu[0].path) || 'welCome',
				};
			});

			// this.menuData = MENU;
		},

		// 点击菜单（路由已经跳转了，当前只为了保存菜单id用于权限控制）
		clickMenu(item, type) {
			// item 菜单数据 type 菜单类型
			if (item.menuName == '首页' || type == 'child') {
				// 本地存储
				window.localStorage.setItem('OPS_LAST_PATH_' + this.userInfos?.adminUserVO.phoneNo, item.path); // 保存当前菜单路径
				this.menuTitle.id = item.id;
				this.$store.commit('setMenuTitle', this.menuTitle);
			}
		},
	},
};
</script>

<style lang="scss">
@import '@/styles/element-variables.scss';

//侧边菜单栏自适应
.SideMenu {
	background-color: #333333;
	overflow: hidden;
	width: auto !important;
	// 侧边栏折叠动画速度
	transition: all 0.25s;
	-webkit-transition: all 0.25s;
	-moz-transition: all 0.25s;
	-webkit-transition: all 0.25s;
	-o-transition: all 0.25s;

	.el-menu {
		box-sizing: border-box;
		border: 0;
		overflow-y: hidden;
		overflow-x: hidden;
		height: 100%;
		font-size: 14px;
		.menu-icon {
			display: inline-block;
			width: 18px;
			font-size: 20px;
			color: #d7d7d7;
		}
		.menu-item-text {
			margin-left: 10px;
			font-size: 17px;
			text-overflow: ellipsis;
			overflow: hidden;
			width: 150px;
			display: inline-block;
		}
		::-webkit-scrollbar {
			width: 6px;
			height: 6px;
		}

		&::-webkit-scrollbar-thumb {
			/*滚动条里面小方块*/
			border-radius: 10px;
			-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
			background: #535353;
		}

		&::-webkit-scrollbar-track {
			/*滚动条里面轨道*/
			-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
			border-radius: 10px;
			background: rgba(48, 65, 86, 0.9);
		}

		// .el-menu-item-group {
		// 	margin-top: -15px; //因为下面那个不生效所以设置了这里
		// }
		.el-menu-item-group__title {
			padding: 0 !important;
		}

		.is-active.el-menu-item {
			color: $--color-primary-lighter;
			background-color: #263445 !important;
		}

		.is-opened {
			> div.el-submenu__title {
				span {
					color: #fff;
				}
			}

			.is-active.el-menu-item {
				color: $--color-primary-lighter;
				background-color: #263445 !important;
			}
		}
	}
	// 展开菜单
	.el-menu-vertical:not(.el-menu--collapse) {
		width: 225px;
		height: 100%;
		min-height: 400px;
		box-sizing: border-box;
		border: 0;
		overflow-y: scroll;
	}

	/*折叠菜单时 隐藏文字*/
	.el-menu--collapse .el-submenu__title span {
		display: none !important;
	}

	/*折叠菜单隐藏 箭头> */
	.el-menu--collapse .el-submenu__title .el-submenu__icon-arrow {
		display: none !important;
	}
}
</style>
