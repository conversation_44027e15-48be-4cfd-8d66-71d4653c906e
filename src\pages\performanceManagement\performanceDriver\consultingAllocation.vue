<template>
	<div class="consultingAllocation">
		<BaseLayout>
			<template #header>
				<!-- 模糊查询 -->
				<el-input
					class="searchBox"
					size="small"
					v-model.trim="searchForm.auname"
					placeholder="姓名"
					@input="queryTableData(1)"
					clearable
				></el-input>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<el-button type="text" class="icon-third-bt_newdoc" @click="openDialog('添加', null)">添加</el-button>
				</div>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar"></div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<Tooltips
								v-if="['startTime', 'endTime', 'modifyTime'].includes(item.colNo)"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>

							<!-- 状态/类型 -->
							<Tooltips
								v-else-if="item.colNo == 'sampleType'"
								:cont-str="['按比例', '按批量'][scope.row[item.colNo]]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<el-input
								v-else-if="item.colNo == 'poolSize'"
								v-model="scope.row[item.colNo]"
								placeholder="请输入数量"
								size="mini"
								clearable
								@change="saveEdit(scope.row)"
							></el-input>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<u-table-column label="" width="" align="right">
						<template slot-scope="scope">
							<el-button type="text" class="el-icon-edit-outline" @click="openDialog('编辑', scope.row)"></el-button>
							<el-button type="text" class="el-icon-copy-document" @click="openDialog('复制', scope.row)"></el-button>
							<el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>

		<el-dialog width="500px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">{{ dialogTitle }}</span>
			<el-form :model="editForm" :rules="formRules" label-width="100px" label-position="left" @submit.native.prevent>
				<el-form-item label="姓名" prop="auid">
					<!-- <el-input v-model="editForm.auid" placeholder="请输入内容" clearable></el-input> -->
					<el-select v-model="editForm.auid" placeholder="请选择用户" class="W100" clearable filterable>
						<el-option v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="咨询评价池" prop="poolSize">
					<el-input v-model="editForm.poolSize" placeholder="请输数量" clearable></el-input>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEdit(editForm)">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'consultingAllocation', //组件名应同路由名(否则keep-alive不生效)
	props: {
		userList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			activeTab: 'consultingAllocation', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '姓名', colNo: 'auname', align: 'left', width: '100' },
				{ colName: '咨询评价池', colNo: 'poolSize', align: 'left', width: '150' },
			],

			// 查询表单
			searchForm: {
				auname: '',
				// 其他...
			},
			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},

			dialogTitle: '',
			dialogEdit: false,
			editForm: {
				auid: '',
				ieaid: '',
				poolSize: '',
			},
			formRules: {
				name: [{ required: true, message: '请输入内容', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		openDialog(action, row) {
			row && (this.editForm = _.deepClone(row));
			if (action == '复制') {
				this.editForm.auid = '';
				this.editForm.ieaid = '';
			}
			this.dialogTitle = action + '咨询评价分配规则';
			this.dialogEdit = true;
		},
		closeDialog() {
			this.editForm = _.resetValues(this.editForm);
			this.dialogEdit = false;
		},
		async saveEdit(formData) {
			const API = formData.ieaid ? 'updateInquiryEvaluateAllocationRule' : 'addInquiryEvaluateAllocationRule';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...formData }));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.queryTableData(1);
					this.dialogEdit = false;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				
				console.error(`${API} |` + error);
			}
		},
		// 删除
		deleteRow({ ieaid }) {
			this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'deleteInquiryEvaluateAllocationRule';
					try {
						const res = await this.$axios[API](JSON.stringify({ ieaid }));
						if (res.data.success) {
							this.$succ(res.data.message);
							this.queryTableData();
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectInquiryEvaluateAllocationRuleList'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				// ...this.dateSelectObj,
				...this.searchForm,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
	},
};
</script>

<style lang="scss">
.consultingAllocation {
	width: 100%;
	overflow: hidden;
	position: relative;
	.table-wrapper .table-main td {
		height: 49px !important;
	}
}
</style>
