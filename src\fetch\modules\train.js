/* 培训管理API */

const urlList = [
	// 课程制作
	'/background/web/coursesController/addCourse', // 课程制作-添加课程
	'/background/web/coursesController/selectCourse', // 课程制作-查询课程列表
	'/background/web/courseDetails/selectCourseDetails', // 课程制作-查询课程详情
	'/background/web/coursesController/updateCourse', // 课程制作-修改课程
	'/background/web/coursesController/deleteCourse', // 课程制作-删除课程
	'/background/web/coursesController/importCourse', // 课程制作-导入课程

	// 培养计划
	'/background/web/coursesController/selectCourseNameAndCid', // 添加计划-查询课程列表
	'/background/web/coursesController/selectTrainManageAdminUser', // 添加计划-查询用户列表
	'/background/web/adminUserController/selectSZGCTeamworkAllUser',
	'/background/web/trainPlanController/addTrainPlan', // 培养计划-添加计划
	'/background/web/trainPlanController/selectTrainPlan', // 培养计划-查询计划列表
	'/background/web/trainPlanController/selectTrainPlanDetail', // 培养计划-查询计划详情
	'/background/web/trainPlanController/updateTrainPlanDetail', // 培养计划-修改计划
	'/background/web/trainPlanController/deleteTrainPlan', // 培养计划-删除计划

	// 在线培训
	'/background/web/trainingCourseClientController/selectBelongsCurrentUserTrainingPlan', // 在线培训-培训计划列表
	'/background/web/trainingCourseClientController/selectCurrentTpidCourse', // 在线培训-课程列表
	'/background/web/trainingCourseClientController/selectUserTrainPlanCourseDetailCase', // 在线培训-课程详情
	// '/background/web/trainingCourseClientController/recordCurrentUserCompleteCourseDetailLogs', // 在线培训-完成某个章节
	'/background/web/trainingCourseClientController/recordCurrentUserCompleteCourseDetailLogs', // 在线培训-提交选择题答案

	// 培训进度
	'/background/web/trainProgressController/selectTrainProgress', // 培训进度
	'/background/web/wrongAnswerLogsController/selectWrongAnswerLogs', // 查询错题集
];

// 重名函数映射
const apiNameMap = {};
const apiList = urlList.map(url => {
	const urlName = apiNameMap[url] || url.split('/').pop();
	let timeout = ''; // 请求时间超过多少秒时断开 默认 10秒
	if (['selectAllONGoingProject'].includes(urlName)) {
		timeout = 60000;
	}
	return { urlName, url, timeout };
});

export default apiList;
