<template>
	<!-- 交付项目步骤图 -->
	<div class="DeliveryStepsCom project-steps-wrap">
		<span :class="rowData.marks == 0 ? 'red' : 'color-999'">
			{{
				jointString(
					' ',
					rowData.number,
					rowData.projectName,
					projectTypeMap[rowData.projectType],
					rowData.salesmanName,
					rowData.implementName,
				)
			}}
		</span>
		<el-steps class="project-steps" :space="space" :active="getActive(stepsData)">
			<el-step
				v-for="(sItem, index) in stepsData"
				:key="index"
				:status="['', 'wait', 'process', 'error', 'success'][sItem.status]"
				:class="(getStepClass(sItem), sItem.dmsiid ? 'steps-plan' : 'steps-stage')"
			>
				<!--  -->
				<template slot="title">
					<Tooltips class="max-w-60" :cont-str="sItem.stage || sItem.dsmStage || sItem.cfgName || '未填写'" :cont-width="50" />
				</template>

				<template slot="description">
					<!-- 项目阶段 -->
					<div v-if="sItem.dmsid">
						<Tooltips
							v-if="sItem.reportDate"
							:cont-str="dateFormat(sItem.reportDate, 'line')"
							class="max-w-120"
							:cont-width="120"
						/>
						<Tooltips
							v-else-if="sItem.complateMonth"
							:cont-str="dateFormat(sItem.complateMonth, 'line')"
							class="max-w-120"
							:cont-width="120"
						/>
					</div>
					<!-- 阶段计划 -->
					<div v-else-if="sItem.dmsiid">
						<Tooltips class="max-w-100" :cont-str="dateFormat(sItem.plannedTime, 'MD')" :cont-width="100" />
					</div>
				</template>
			</el-step>
		</el-steps>
	</div>
</template>
<script>
import { debounce, jointString, dateFormat } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { projectTypeMap } from '@/assets/js/contractSource';

export default {
	name: 'DeliveryStepsCom',
	components: {},
	props: {
		rowData: { type: Object, default: () => {} },
		space: { type: Number, default: () => 100 },
	},
	data() {
		return {
			projectTypeMap,
			hasDelay: false,
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		stepsData() {
			return this.getStepsData(this.rowData);
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 获取步骤条数据
		getStepsData(row) {
			const stepsData = []; // 用于收集阶段数据

			// 检查 deliveryScheduleStateVOS 是否存在并遍历
			row.deliveryScheduleStateVOS?.forEach((bItem, bIndex) => {
				bItem.number = bIndex + 1; // 设置阶段编号

				// 对 deliveryScheduleInfoVOS 进行排序
				bItem.deliveryScheduleInfoVOS?.sort((a, b) => {
					return a?.sort - b?.sort;
				});

				// 合并当前 bItem 的 deliveryScheduleInfoVOS 到 stepsData
				stepsData.push(...bItem.deliveryScheduleInfoVOS || []); // 使用扩展运算符合并数组

				// 设置阶段状态
				if (bItem.reportDate && bItem.reportName && bItem.reportUrl) {
					bItem.status = 4; // 完工状态
				} else {
					// 设置阶段状态来源于后端
					bItem.status = bItem.stageStatus === 2 ? 4 : bItem.stageStatus;
				}

				// 存在阶段延误
				if (bItem.status == 3) {
					this.hasDelay = true;
				}
				// 将完整的 bItem 添加到结果中
				stepsData.push(bItem); // 添加项目阶段
			});
			// row.stepsData = stepsData; // 将最终的 stepsData 存入 row 步骤数据
			return stepsData; // 返回收集的步骤数据
		},
		// 获取最后一个完成的状态4 的索引 如果没有则查询最后一个延误3的索引
		getActive(data) {
			const lastIndex3 = data?.findLastIndex(item => item?.status === 3);
			if (lastIndex3 !== -1) {
				return lastIndex3 + 1;
			}
			const lastIndex4 = data?.findLastIndex(item => item?.status === 4);
			return lastIndex4 + 1;
		},
		// el-step样式修改
		getStepClass: debounce(function () {
			// 步骤图标修改 性能不太行 后续在优化
			const errors = document.getElementsByClassName('el-step__icon-inner is-status el-icon-close');
			Array.from(errors).forEach(item => {
				item.classList.add('icon-third-icon-yanwu');
				item.classList.remove('el-icon-close');
			});
		}),

		jointString,
		dateFormat,
	},
};
</script>

<style lang="scss">
// el-steps样式调整
.DeliveryStepsCom.project-steps-wrap {
	// width: max-content;
	.max-w-60 {
		max-width: 60px;
	}
	overflow-x: scroll;
	.project-steps {
		font-size: 12px !important;
		height: 28px;
		.el-step {
			zoom: 0.9;
			min-width: 100px;
			.el-step__main {
				position: absolute;
				top: -9px;
				right: -30px;
				min-width: 100px;
			}
		}
		.el-step__title {
			font-size: 12px;
			line-height: 24px;
		}
		.is-process {
			color: #ff9800;
			border-color: #ff9800;
		}
	}
}
</style>
