<template>
	<div id="defectManagement">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 明细组件 -->
		<DetailCom ref="DetailComRef" @close="queryTableData(1)" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="现网缺陷" name="defectManagement">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">提出日期</span>
						<DateSelect
							:dateKeys="['startDate', 'endDate']"
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>
						<!-- 模糊查询 -->
						<el-input
							class="searchBox"
							size="small"
							v-model="searchForm.query"
							placeholder="提出人"
							@input="queryTableData(1)"
							clearable
						></el-input>
						<el-checkbox v-model="searchForm.status" @change="queryTableData(1)">仅显示未结案的记录 </el-checkbox>
						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button type="text" class="icon-third-bt_newdoc" @click="openDetail('添加', null)">添加</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['startTime', 'endTime', 'submitTime'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 状态/类型 -->
									<Tooltips
										v-else-if="item.colNo == 'number'"
										@click.native="openDetail('编辑', scope.row)"
										class="hover-green green"
										:cont-str="scope.row[item.colNo]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 状态/类型 -->
									<Tooltips
										v-else-if="item.colNo == 'bugLevel'"
										:class="['', 'blue', 'orange', 'red'][scope.row[item.colNo]]"
										:cont-str="['微小', '一般', '严重', '致命'][scope.row[item.colNo]]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<Tooltips
										v-else-if="item.colNo == 'status'"
										:class="['', 'green'][scope.row[item.colNo]]"
										:cont-str="['未结案', '已结案'][scope.row[item.colNo]] || '未结案'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<span
										v-else-if="item.colNo == 'detail' && scope.row.bugLevel > 1"
										class="green hover-green"
										@click="openDetail('编辑', scope.row)"
									>
										详情
									</span>
									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<u-table-column label="" width="" align="center">
								<template slot-scope="scope">
									<div class="flex-align-center">
										<el-button size="mini" class="p0 m0" type="text" @click="openDialog(scope.row)">
											{{ scope.row.status ? '撤销结案' : '结案' }}
										</el-button>
										<span class="ml10 p0 fs-12" v-if="scope.row.status == 0 || scope.row.status == 1">
											<span>{{ scope.row.closeUname }}于</span>
											<span>{{ dateFormat(scope.row.closeDate, 'lineM') }} </span>
											<span> {{ ['撤销结案', '结案'][scope.row.status] }} </span>
											<span v-if="scope.row.score"> , 评分：{{ scope.row.score }}</span>
											<span v-if="scope.row.remark"> , {{ scope.row.remark }}</span>
										</span>
									</div>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="月统计" name="defectRiver">
				<defectRiver v-if="activeTab == 'defectRiver'"></defectRiver>
			</el-tab-pane>
		</el-tabs>

		<el-dialog width="500px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">现网缺陷记录结案</span>
			<el-form :model="editForm" :rules="formRules" label-width="100px" label-position="left" @submit.native.prevent>
				<el-form-item label="评分" prop="score">
					<el-input v-model="editForm.score" placeholder="请输入评分" clearable></el-input>
				</el-form-item>
				<el-form-item label="备注" prop="remark">
					<el-input
						v-model="editForm.remark"
						placeholder="请输入备注"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>
				<el-form-item label="结案" prop="status">
					<el-radio-group v-model="editForm.status">
						<el-radio :label="0">撤销结案</el-radio>
						<el-radio :label="1">结案</el-radio>
					</el-radio-group>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEdit">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import ExportTable from '@/components/ExportTable'; //导出组件
import DetailCom from './defectDetail.vue'; //明细组件
import defectRiver from './defectRiver.vue';
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		DateSelect,
		DetailCom,
		defectRiver,
	},
	name: 'defectManagement', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'defectManagement', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '编号', colNo: 'number', align: 'left', width: '120' },
				{ colName: '提出时间', colNo: 'submitTime', align: 'center', width: '150' },
				{ colName: '提出人', colNo: 'submitUname', align: 'left', width: '120' },
				{ colName: '客户来源', colNo: 'customerSource', align: 'left', width: '120' },
				{ colName: '缺陷等级', colNo: 'bugLevel', align: 'center', width: '80' },
				{ colName: '缺陷主题', colNo: 'bugTopic', align: 'left', width: '200' },
				{ colName: '8D报告', colNo: 'detail', align: 'center', width: '80' },
				{ colName: '状态', colNo: 'status', align: 'left', width: '80' },
			],

			// 查询表单
			searchForm: {
				status: false,
				query: '',
				// 其他...
			},
			// 日期相关
			dateSelectObj: {
				endDate: '',
				startDate: '',
			},

			dialogEdit: false,
			editForm: {
				brid: '',
				remark: '',
				score: '',
				status: '',
			},
			formRules: {
				status: [{ required: true, message: '请选择结案状态', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		openDialog(row) {
			this.editForm = { ...this.editForm, ...row };
			this.dialogEdit = true;
		},
		closeDialog() {
			this.dialogEdit = false;
			this.editForm = _.resetValues(this.editForm);
		},
		async saveEdit() {
			if (_.checkRequired(this.editForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}

			const API = 'closeBugRecord';
			try {
				const res = await this.$axios[API](
					JSON.stringify({
						brid: this.editForm.brid,
						remark: this.editForm.remark,
						score: Number(this.editForm.score),
						status: this.editForm.status,
					}),
				);
				if (res.data.success) {
					this.dialogEdit = false;
					this.queryTableData(1);
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 打开明细
		openDetail(type, row) {
			this.$refs.DetailComRef.showDetailCom(type, row);
		},
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			const status = this.searchForm.status ? 0 : '';
			type && (this.tablePageForm.currentPage = 1);
			const API = 'bugRecordList'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.dateSelectObj,
				...this.searchForm,
				status,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
	},
};
</script>

<style lang="scss" scoped>
#defectManagement {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
