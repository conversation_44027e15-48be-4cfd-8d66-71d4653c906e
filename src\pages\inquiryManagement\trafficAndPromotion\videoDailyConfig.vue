<template>
	<BaseLayout :showHeader="false">
		<template #main>
			<div class="videoDailyConfig-wrap overflow-auto">
				<div class="videoDailyConfig-info flex-1">
					<div class="flex-align-center sticky-top">
						<span class="label-title mr10"> 流量中心成员 </span>
						<!-- <el-button type="primary" class="ml-auto" @click="">保 存</el-button> -->
					</div>
					<div v-for="(item, index) in userList" :key="index" class="flex-align-center mt10">
						<span class="w-120"> {{ item.userName || '' }}({{ item.categorys ? item.categorys.length : 0 }}) </span>
						<el-select
							class="width-100"
							v-model="item.categorys"
							placeholder="管理类型"
							popper-class="select-column-3"
							multiple
							clearable
							filterable
							@change="saveEdit(item)"
							@remove-tag="saveEdit(item)"
						>
							<el-option v-for="sitem in typeList" :key="sitem" :label="sitem" :value="sitem"> </el-option>
						</el-select>
					</div>
				</div>
			</div>
		</template>
	</BaseLayout>
</template>

<script>
import { debounce, checkRequired, deepClone, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'videoDailyConfig', //组件名应同路由名(否则keep-alive不生效)
	props: {
		twidList: Array,
		channelName: Array,
	},
	data() {
		return {
			activeTab: 'videoDailyConfig', //激活tab页
			userList: [],
			typeList: ['百度类', '视频类', '电商类'],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		twidList() {
			this.queryTableData();
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		// this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 查询用户
		queryTableData: debounce(function () {
			this.$axios
				.selectAdminUserCategory(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						if (res.data.data) {
							res.data.data.forEach(item => {
								const categorys = [];
								if (item.category) {
									item.category.forEach(sitem => {
										categorys.push(sitem.categoryName);
									});
								} else {
									item.category = [];
								}
								item.categorys = categorys;
							});
						}
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		// 保存
		saveEdit: debounce(async function (row) {
			// console.log(row)
			let API = 'addAdminUserCategory';
			const params = {};
			if (row.category.length > row.categorys.length) {
				// 删除逻辑
				const currentCategoryNames = new Map();
				// 计算 row.categorys 中每个 categoryName 的出现次数
				row.categorys.forEach(categoryName => {
					if (currentCategoryNames.has(categoryName)) {
						currentCategoryNames.set(categoryName, currentCategoryNames.get(categoryName) + 1);
					} else {
						currentCategoryNames.set(categoryName, 1);
					}
				});

				// 过滤出需要删除的类别
				const deletedItems = [];
				row.category.forEach(item => {
					if (currentCategoryNames.has(item.categoryName)) {
						const count = currentCategoryNames.get(item.categoryName);
						if (count > 0) {
							currentCategoryNames.set(item.categoryName, count - 1);
						} else {
							deletedItems.push(item.acid);
						}
					} else {
						deletedItems.push(item.acid);
					}
				});

				// const currentCategoryNames = new Set(row.categorys);
				// const deletedItems = row.category.filter(item => !currentCategoryNames.has(item.categoryName));
				if (deletedItems.length > 0) {
					params.acid = deletedItems[0];
					console.log('需要删除的分类：', params); // 输出: { acid: [518549982932893697] }
				}
				API = 'delateAdminUserCategory';
			} else {
				const currentCategoryNames = new Set(row.category.map(item => item.categoryName));
				const addedCategories = row.categorys.filter(cat => !currentCategoryNames.has(cat));
				if (addedCategories.length > 0) {
					params.auid = row.auid; // 假设需要用户ID
					params.categoryName = addedCategories[0];
					// console.log('需要添加的分类：', params);
				}
			}

			try {
				const res = await this.$axios[API](JSON.stringify(params));
				if (res.data.success) {
					this.queryTableData(1);
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
					this.queryTableData(1);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		// 切换tab
		changeTab() {},
	},
};
</script>

<style lang="scss">
.videoDailyConfig-wrap {
	display: flex;

	width: 100%;
	height: calc(100vh - 200px);
	overflow: hidden;
	position: relative;
	font-size: 14px;
	color: #666;
	// background-color: #fff;
	// border: solid 1px #d7d7d7;
	// border-radius: 8px;
	// padding: 20px !important;
	// box-sizing: border-box;

	.sticky-top {
		position: sticky;
		top: 0px;
		z-index: 10;
		background-color: #fff;
	}

	.width-100 {
		width: 100%;
	}
}
</style>
