<template>
	<!-- 资源功能说明 -->
	<div id="ResourceDescription">
		<div :class="moveToggle ? 'moveToggle' : 'moveToggle moveToggle-hide'">
			<div class="detail-wrapper">
				<!-- 头部标题 -->
				<div class="detail-head">
					<div class="detail-title">
						<div class="flex-align-center gap-10">
							<span class="green">{{ detailForm.resourceName + '功能说明' }} </span>
							<span v-show="detailForm.userName">修改人：{{ detailForm.userName }}</span>
							<span v-show="detailForm.modifyTime">修改时间：{{ dateFormat(detailForm.modifyTime, 'lineM') }}</span>
						</div>
					</div>
					<div class="flex-align-center gap-10">
						<el-button type="text" class="icon-third_save m0 p0" @click="saveEdit"> 保存</el-button>
						<el-button type="text" class="el-icon-arrow-left m0 p0" @click="moveToggle = false">返回</el-button>
					</div>
				</div>
				<!-- 主体内容 -->
				<div class="detail-content p20">
					<el-form :model="detailForm" ref="detailForm" label-width="80px" size="mini" label-position="left">
						<el-form-item label="简介">
							<el-input
								type="textarea"
								size="small"
								v-model="detailForm.introduction"
								:autosize="{ minRows: 2, maxRows: 4 }"
								placeholder="简介"
							></el-input>
						</el-form-item>

						<el-form-item label="概述">
							<el-input
								type="textarea"
								size="small"
								v-model="detailForm.overview"
								:autosize="{ minRows: 2, maxRows: 4 }"
								placeholder="概述"
							></el-input>
						</el-form-item>
						<el-form-item label="详细说明"> </el-form-item>
					</el-form>
					<!-- 富文本编辑器 -->
					<wang-editor v-if="moveToggle" class="W100" v-model="detailForm.description" :editorHeight="460"></wang-editor>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { debounce, deepClone, resetValues, dateFormat } from '@/util/tool';
import wangEditor from '@/components/WangEditor/wangEditor';
export default {
	name: 'ResourceDescription',

	components: {
		wangEditor,
	},
	data() {
		return {
			moveToggle: false, //滑动控制
			// 任务表单
			detailForm: {
				introduction: '',
				overview: '',
				rdcid: '',
				description: '',
				userName: '',
				modifyTime: '',
				resourceName: '',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {},
	// 监控data中的数据变化
	watch: {
		moveToggle(val) {
			if (!val) {
				this.detailForm = resetValues(this.detailForm);
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 打开任务详情
		openDetail(form) {
			this.detailForm = deepClone(form);
			this.moveToggle = true;
		},
		// 保存编辑
		saveEdit: debounce(function () {
			this.$axios
				.updateResourceDescriptionConfig(JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						this.$succ('修改成功!');
						this.$emit('update');
						this.moveToggle = false;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('updateResourceDescriptionConfig |' + error);
				});
		}),
		//日期format
		dateFormat,
	},
};
</script>
