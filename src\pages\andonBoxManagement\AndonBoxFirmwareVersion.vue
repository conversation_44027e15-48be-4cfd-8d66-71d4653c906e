<template>
	<div id="AndonBoxFirmwareVersion">
		<BaseLayout>
			<template #header>
				<el-input
					class="searchBox w-300"
					size="small"
					clearable
					v-model="queryStr"
					placeholder="固件版本名称/芯片名称/功能描述"
				></el-input>

				<el-select size="small" placeholder="安灯盒子类型" clearable @change="getTableDataDB" v-model="searchForm.andonLightType">
					<el-option v-for="item in andonTypeList" :key="'andonType' + item.id" :label="item.andonType" :value="item.andonType">
					</el-option>
				</el-select>

				<el-select
					class="ml10 mr10"
					size="small"
					placeholder="芯片"
					filterable
					clearable
					v-model="searchForm.chipName"
					@change="getTableDataDB"
				>
					<el-option v-for="item in chipList" :key="'chip' + item.id" :label="item.chipName" :value="item.chipName"> </el-option>
				</el-select>

				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="refreshData">刷新</el-button>
			</template>
			<template #main>
				<div class="table-toolbar">{{ nowProName }}</div>
				<u-table ref="uTableRef" class="table-main" :data="tableData" :height="1200" :row-height="45" stripe>
					<u-table-column width="60" label="序号" type="index" align="center"></u-table-column>
					<u-table-column prop="firmwareName" label="固件版本" width="400">
						<template slot-scope="scope">
							<Tooltips :cont-str="scope.row.firmwareName" :cont-width="(scope.column.width || scope.column.realWidth) - 20" />
						</template>
					</u-table-column>
					<u-table-column prop="chipName" label="芯片名称" width="120" align="center">
						<template slot-scope="scope">
							<Tooltips :cont-str="scope.row.chipName" :cont-width="(scope.column.width || scope.column.realWidth) - 20" />
						</template>
					</u-table-column>
					<u-table-column prop="andonLightType" label="安灯盒子类型" align="center" width="120">
						<template slot-scope="scope">
							<Tooltips :cont-str="scope.row.andonLightType" :cont-width="(scope.column.width || scope.column.realWidth) - 20" />
						</template>
					</u-table-column>
					<u-table-column label="功能描述" prop="description" width="200">
						<template slot-scope="scope">
							<Tooltips :cont-str="scope.row.description" :cont-width="(scope.column.width || scope.column.realWidth) - 20" />
						</template>
					</u-table-column>
					<u-table-column label="发布日期" prop="releaseTime" align="center" width="120">
						<template slot-scope="scope">
							<div>
								{{ dateFormat(scope.row.releaseTime, 'line') }}
							</div>
						</template>
					</u-table-column>
					<u-table-column label="固件下载地址" prop="downloadUrl">
						<template slot-scope="scope">
							<Tooltips :cont-str="scope.row.downloadUrl" :cont-width="(scope.column.width || scope.column.realWidth) - 20" />
						</template>
					</u-table-column>
					<u-table-column label="" width="150">
						<template slot="header" slot-scope="scope">
							<el-button type="text" class="icon-third-bt_newdoc" @click="dialogUpdate = true">添加</el-button>
						</template>
						<template slot-scope="scope">
							<el-button type="text" @click="openDialogUpdate(scope.row)"><i class="el-icon-edit-outline"></i>修改</el-button>
							<el-button type="text" @click="delFirmware(scope.row, scope.$index)"><i class="el-icon-close"></i></el-button>
						</template>
					</u-table-column>
				</u-table>
			</template>
		</BaseLayout>

		<el-dialog :visible.sync="dialogUpdate" width="30%" close-on-click-modal @close="closeDialog">
			<el-row slot="title"> {{ eidtWorkForm.aftid ? '修改' + nowProName : '添加 ' + nowProName }} 固件版本</el-row>
			<el-form :model="eidtWorkForm" label-width="6vw" label-position="center" :rules="editRules">
				<el-form-item label="固件版本" prop="firmwareName">
					<el-input placeholder="请输入固件版本" v-model="eidtWorkForm.firmwareName"></el-input>
				</el-form-item>
				<el-form-item label="芯片名称" prop="chipName">
					<el-select class="W100" v-model="eidtWorkForm.chipName" placeholder="请选择芯片" filterable clearable>
						<el-option v-for="item in chipList" :key="'chip' + item.id" :label="item.chipName" :value="item.chipName">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="安灯盒子类型" prop="andonLightType">
					<el-select class="W100" v-model="eidtWorkForm.andonLightType" placeholder="请选择安灯盒子类型" clearable>
						<el-option v-for="item in andonTypeList" :key="'andonType' + item.id" :label="item.andonType" :value="item.andonType">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="功能描述" prop="description">
					<el-input placeholder="请输入功能描述" v-model="eidtWorkForm.description"></el-input>
				</el-form-item>
				<el-form-item label="发布日期" prop="newNum">
					<el-date-picker
						value-format="timestamp"
						v-model="eidtWorkForm.releaseTime"
						type="date"
						class="W100"
						placeholder="请选择发布日期"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="固件地址" prop="downloadUrl">
					<el-upload
						action=""
						accept="*"
						:http-request="uploadFile"
						:on-remove="handleRemove"
						:on-error="uploadFail"
						:file-list="excelFileList"
						:show-file-list="false"
					>
						<el-button type="text" style="white-space: pre-wrap">{{
							eidtWorkForm.downloadUrl ? eidtWorkForm.downloadUrl : '上传'
						}}</el-button>
					</el-upload>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="updateFirmware">保存</el-button>
			</el-row>
		</el-dialog>
		<el-dialog :visible.sync="dialogAddChip" width="25%" close-on-click-modal @close="cancelAddChip">
			<el-row slot="title">添加芯片</el-row>

			<el-form ref="formChip" :model="chipForm" label-width="7vw" label-position="left">
				<el-form-item label="名称">
					<el-input placeholder="请输入名称" v-model="chipForm.chipName"></el-input>
				</el-form-item>
				<el-form-item label="固件版本">
					<el-select class="W100" v-model="chipForm.classCategory" placeholder="请选择产品类型">
						<el-option v-for="item in NetTypeList" :key="'netType' + item" :label="item" :value="item"> </el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="描述">
					<el-input placeholder="请输入名称" v-model="chipForm.description"></el-input>
				</el-form-item>
			</el-form>

			<el-row slot="footer">
				<el-button type="primary" v-show="chipForm.ctid" @click="delChip">删除芯片</el-button>
				<el-button type="primary" @click="saveEditChip">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import folderImg from '@/assets/img/folder.svg';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'AndonBoxFirmwareVersion',
	props: {
		chipList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			queryStr: '',
			searchForm: { andonLightType: '', chipName: '' },

			firmwareTreeList: [
				{
					id: '1',
					label: '4G',
					children: [],
				},
				{
					id: '2',
					label: 'WIFI',
					children: [],
				},
			],
			tableData: [],
			proFlag: false,
			nowProName: '',
			dialogAddShift: false,
			shiftTableList: [],
			classGroups: [],
			classOrderList: [],
			andonTypeList: [
				{
					id: 1,
					andonType: '4G',
				},
				{
					id: 2,
					andonType: 'WIFI',
				},
			],

			colorList: [
				'#C01900',
				'#FF2600',
				'#FFC000',
				'#FFFB01',
				'#92D050',
				'#00B050',
				'#00B0F0',
				'#0070C0',
				'#002060',
				'#7030A0',
				'#F2F2F2',
			],
			shiftModeList: [],
			colorLeftCurr: [],
			colorRightCurr: [],

			chipNameCurr: '',
			btnFlag: false, //防止多次请求

			//批量
			dialogSetCalender: false,
			dateSetRange: '',
			selectedShiftMode: [],
			shiftModeCurr: [],
			pickerOptions: {
				disabledDate(time) {
					return time.getTime() < Date.now();
				},
			},
			//Ctrl设置
			batchCalenList: [],
			dialogUpdateCalender: false,
			//添加
			dialogUpdate: false,
			selectedProline: '',
			excelFileList: [],
			editRules: {
				firmwareName: [{ required: true, message: '请输入固件版本', trigger: 'blur' }],
				andonLightType: [
					{
						required: true,
						message: '请选择安灯盒子类型',
						trigger: ['blur', 'change'],
					},
				],
				chipName: [
					{
						required: true,
						message: '请选择芯片',
						trigger: ['blur', 'change'],
					},
				],
				downloadUrl: [
					{
						required: true,
						message: '请选择固件地址',
						trigger: ['blur', 'change'],
					},
				],
			},
			eidtWorkForm: {
				aftid: '',
				firmwareName: '',
				description: '',
				file: '',
				andonLightType: '',
				chipName: '',
				downloadUrl: '',
				releaseTime: '',
			},
			//芯片
			dialogAddChip: false,
			chipForm: {
				ctid: '',
				chipName: '',
				classCategory: '',
				classType: '',
				description: '',
			},
			//芯片下产品列表
			productList: [],
			NetTypeList: ['4G', 'WIFI'],
			classTypeList: [],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		queryStr() {
			this.getTableDataDB();
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {
		this.getTableDataDB();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		console.log(this.chipList);
	},
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		refreshData() {
			this.getTableDataDB();
			this.$message.success('刷新成功，数据已更新！');
		},
		renderContent(h, { node, data, store }) {
			return (
				<div
					class="custom-tree-node"
					style="width:100%;position:relative"
					on-mouseenter={() => {
						if (data.editFlag === false) data.editFlag = true;
						if (data.addFlag === false) data.addFlag = true;
					}}
					on-mouseleave={() => {
						if (data.editFlag === true) data.editFlag = false;
						if (data.addFlag === true) data.addFlag = false;
					}}
				>
					<img src={folderImg} style="display:inline-block;vertical-align:middle;margin:0 1vw 0 .5vw" />
					<span>{node.label}</span>
					<i
						class="el-icon-edit"
						v-show={data.editFlag}
						style="cursor:pointer;color:#1E9D6F;margin-left:.5vw"
						on-click={e => {
							e.stopPropagation();
							this.updateChip(node);
						}}
					></i>
					<i
						class="el-icon-plus"
						v-show={data.addFlag}
						style="cursor:pointer;color:#1E9D6F;margin-left:.5vw"
						on-click={e => {
							e.stopPropagation();
							this.createChip(node);
						}}
					></i>
				</div>
			);
		},

		openChip(node) {
			if (!node) {
				return;
			}
			const id = node.id.split('-')[1],
				name = node.nowName,
				type = node.type;
			if (type == 'chip') {
				this.proFlag = true;
				this.nowProName = name;

				this.eidtWorkForm.classType = node.classType;
				//查询固件列表
				this.getTableDataDB();
				this.getProList(node.classType);
			} else {
				this.proFlag = false;

				this.eidtWorkForm.classType = '';
				this.nowProName = '';
			}
		},
		// 设置防抖
		getTableDataDB: _.debounce('getTableData'),
		getTableData() {
			const str = JSON.stringify({
				andonLightType: this.searchForm.andonLightType,
				chipName: this.searchForm.chipName,
				pageNum: '',
				pageSize: '',
				query: this.queryStr,
			});
			this.$axios
				.selectAndonFirmwareTypeVO(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAndonFirmwareTypeVO |' + error);
				});
		},
		openDialogUpdate(row) {
			if (!row) {
				this.$message.warning('该数据异常，请刷新后重试！');
				return;
			} else {
				this.eidtWorkForm = row;
				this.dialogUpdate = true;
			}
		},
		delFirmware(row, index) {
			//  aftid = row.aftid,

			if (!row) {
				return;
			}
			const msg = '固件【' + row.firmwareName + '】将被删除';
			const str = JSON.stringify({
				aftids: [row.aftid],
			});

			this.$confirm(msg, '删除固件', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteAndonFirmwareType(str)
						.then(res => {
							if (res.data.success) {
								this.$succ('操作成功!');
								this.tableData.splice(index, 1);
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteAndonFirmwareType |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		modeListPageIndexChange(val) {
			this.modeList.pageIndex = val;
			this.getTableDataDB();
		},
		modeListPageSizeChange(val) {
			this.modeList.pageSize = val;
			this.getTableDataDB();
		},
		closeDialog() {
			this.dialogUpdate = false;
			setTimeout(() => {
				this.eidtWorkForm = {
					aftid: '',
					firmwareName: '',
					description: '',
					file: '',
					andonLightType: '',
					chipName: '',
					downloadUrl: '',
					releaseTime: '',
				};
			}, 500);
			this.getTableDataDB();
		},
		updateFirmware() {
			let str = null;
			if (this.eidtWorkForm.aftid) {
				// 修改
				str = JSON.stringify({
					aftid: this.eidtWorkForm.aftid,
					andonLightType: this.eidtWorkForm.andonLightType,
					chipName: this.eidtWorkForm.chipName,
					description: this.eidtWorkForm.description,
					downloadUrl: this.eidtWorkForm.downloadUrl,
					firmwareName: this.eidtWorkForm.firmwareName,
					releaseTime: this.eidtWorkForm.releaseTime,
				});
				this.$axios
					.updateAndonFirmwareType(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.closeDialog();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('updateAndonFirmwareType |' + error);
					});
			} else {
				// 添加
				str = JSON.stringify({
					andonLightType: this.eidtWorkForm.andonLightType,
					chipName: this.eidtWorkForm.chipName,
					description: this.eidtWorkForm.description,
					downloadUrl: this.eidtWorkForm.downloadUrl,
					firmwareName: this.eidtWorkForm.firmwareName,
					releaseTime: this.eidtWorkForm.releaseTime,
				});
				this.$axios
					.addAndonFirmwareType(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.closeDialog();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('addAndonFirmwareType |' + error);
					});
			}
		},
		uploadFile(item) {
			const self = this;
			if (!self.eidtWorkForm.chipName || !self.eidtWorkForm.andonLightType) {
				self.$message.warning('请将芯片信息或安灯盒子类型补充完整再上传！');
				return;
			}
			// const isIMAGE = item.file.type === 'image/jpeg' || 'image/jpg' || 'image/png';
			const isLt50M = item.file.size / 1024 / 1024 < 50;

			// if (!isIMAGE) {
			//   self.$message.warning('上传文件只能是图片格式!');
			//   return;
			// }
			if (!isLt50M) {
				self.$message.warning('上传固件的大小不能超过 50MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', item.file);
			formData.append('chipName', self.eidtWorkForm.chipName);
			formData.append('andonLightType', self.eidtWorkForm.andonLightType);

			self.$axios
				.uploadFirmwareBox(formData)
				.then(res => {
					if (res.data.success) {
						self.eidtWorkForm.downloadUrl = res.data.data;
						// self.eidtWorkForm.file = res.data.data;
					} else {
						self.$message.warning(res.data.message + ':请检查固件信息是否完整！');
					}
				})
				.catch(error => {
					self.$message.warning(error.message);
				});
		},
		handleRemove() {
			//文件移除
			this.excelFileList = [];
		},
		uploadFail(err, file, fileList) {
			this.excelFileList = [];
		},
		//添加芯片
		createChip(node) {
			const id = node.data.id.split('-')[1],
				type = node.data.id.split('-')[0];
			this.chipForm.classCategory = type;
			this.chipForm.classType = Number(id);
			this.dialogAddChip = true;
		},
		updateChip(node) {
			const id = node.data.id.split('-')[1];
			// console.log('aaa',node.data)
			this.chipForm.ctid = id;

			this.chipForm.chipName = node.data.label;
			this.chipForm.classCategory = node.data.classCategory;
			this.chipForm.classType = node.data.classType;
			this.chipForm.description = node.data.description;
			this.dialogAddChip = true;
		},
		saveEditChip() {
			let str;
			if (this.chipForm.ctid) {
				//修改芯片
				str = JSON.stringify({
					ctid: this.chipForm.ctid,
					chipName: this.chipForm.chipName,
					classCategory: this.chipForm.classCategory,
					classType: this.chipForm.classType,
					description: this.chipForm.description,
				});
				this.$axios
					.updateChipType(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.cancelAddChip();
							this.getOrgTreeList();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('updateChipType |' + error);
					});
			} else {
				//添加芯片
				str = JSON.stringify({
					chipName: this.chipForm.chipName,
					classCategory: this.chipForm.classCategory,
					classType: this.chipForm.classType,
					description: this.chipForm.description,
				});
				this.$axios
					.addChipType(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.cancelAddChip();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('addChipType |' + error);
					});
			}
		},
		cancelAddChip() {
			this.dialogAddChip = false;
			this.chipForm.ctid = '';
			this.chipForm.chipName = '';
			this.chipForm.classCategory = '';
			this.chipForm.classType = '';
			this.chipForm.description = '';
		},
		delChip() {
			let ctid = this.chipForm.ctid,
				msg;
			msg = '芯片【' + this.chipForm.chipName + '】将被删除';
			const str = JSON.stringify({
				ctid: ctid,
			});

			this.$confirm(msg, '删除芯片', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteChipType(str)
						.then(res => {
							if (res.data.success) {
								this.$succ('操作成功!');
								this.cancelAddChip();
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteChipType |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		//日期format
		dateFormat: _.dateFormat,
		getProList(classType) {
			const str = JSON.stringify({
				classType: classType,
			});
			this.productList = [];
			this.$axios
				.getTricolourProductByClassType(str)
				.then(res => {
					if (res.data.success) {
						this.productList = res.data.data;
					} else {
						// this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('getTricolourProductByClassType |' + error);
				});
		},
	},
};
</script>
