<template>
	<div id="andonBoxList">
		<!-- 导入弹窗 -->
		<ImportTable ref="ImportTable" @refresh="queryTableData" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="安灯盒子清单" name="andonBoxList">
				<BaseLayout>
					<template #header>
						<span class="search-label">到期日期</span>
						<DateSelect
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>

						<el-input
							class="searchBox"
							size="small"
							clearable
							v-model="queryStr"
							placeholder="SIM编号/芯片/固件版本"
							@input="queryTableData(1)"
						></el-input>
						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新 </el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<!-- 导入按钮 -->
							<ImportBtn @trigger="openImport" />
							<el-button type="text" class="el-icon-check" @click="openDialogUpgrade">批量升级</el-button>
							<el-button type="text" class="el-icon-delete" @click="delWorkOrderAll">批量删除</el-button>
							<el-button type="text" class="icon-third-bt_newdoc" @click="dialogAddBox = true">添加</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@selection-change="selectionData = $event"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column type="selection" width="30" label="" align="center"></u-table-column>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<Tooltips
										v-if="item.colNo == 'createTime' || item.colNo == 'lastOnlineTime'"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<Tooltips
										v-else-if="item.colNo == 'operatorExpiration' || item.colNo == 'memberExpiration'"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<div v-else-if="item.colNo == 'signalIntensity' && scope.row[item.colNo]">
										{{ String(scope.row[item.colNo]) }}
									</div>
									<div v-else-if="item.colNo == 'andonColor' && scope.row[item.colNo]">
										<el-button round class="bg-black p5">
											<span v-for="(item, index) in scope.row.andonColor.split('')" :key="index">
												<span
													class="bolder fs-16"
													:style="{
														color: item == 1 ? andonColorMap[index] : '#999',
														margin: ' 0 2px',
													}"
													>{{ item }}</span
												>
											</span>
										</el-button>
									</div>

									<Tooltips
										v-else-if="item.colNo == 'sim' && scope.row[item.colNo]"
										:toolClass="'tdTwoNormal'"
										class="hover-green green"
										@click.native="openLightDetail(scope.row, null, true)"
										:cont-str="scope.row[item.colNo]"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<div class="flex-center" v-else-if="item.colNo == 'line'">
										<i class="icon-third_graysd fs-24" :style="{ color: onlineColorMap[scope.row.line] }"></i>
										<span> {{ scope.row[item.colNo] ? '在线' : '离线' }}</span>
									</div>

									<Tooltips
										v-else
										:toolClass="'tdTwoNormal'"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>

			<el-tab-pane label="固件版本" name="AndonBoxFirmwareVersion">
				<andon-box-firmware-version
					v-if="activeTab == 'AndonBoxFirmwareVersion'"
					:chipList="chipList"
				></andon-box-firmware-version>
			</el-tab-pane>
		</el-tabs>
		<div id="andonBoxListToggle" class="detail-container">
			<div style="padding: 0">
				<div
					style="
						border-radius: 15px 15px 0 0;
						width: 100%;
						border: 1px solid #e9e9e9;
						background: #f9f9f9;
						border-bottom: 1px solid transparent;
						box-sizing: border-box;
					"
				>
					<el-row class="orgManageHead" style="padding: 10px; box-sizing: border-box">
						<el-col :span="8" style="font-size: 18px; line-height: 40px; font-weight: 500">
							<span style="margin-left: 0.5vw">{{ boxEdit ? '修改安灯盒子参数' : '查看安灯盒子参数' }}</span>
						</el-col>
						<el-col :span="8" :offset="8" style="text-align: right">
							<el-button type="text" class="el-icon-arrow-left" @click="cleartLight">返回</el-button>
						</el-col>
					</el-row>
					<div
						style="
							background: #fff;
							border-top: 1px solid #d7d7d7;
							padding: 0 10px;
							box-sizing: border-box;
							min-height: 78vh;
							overflow-y: auto;
						"
					>
						<div style="padding-left: 1vw">
							<p class="p_row" style="align-items: flex-end">
								<span style="display: flex-inline; align-items: center">
									<span class="p_label"></span>
									<span class="p_text">基本信息</span>
								</span>
								<canvas id="qrcodeBox" style="margin-bottom: -13px"></canvas>
							</p>
							<table class="workCtable" cellpadding="5" cellspacing="0">
								<tr>
									<th>安灯盒子编号</th>
									<th>盒子类型(4G/WIFI)</th>
									<th>归属团队</th>
									<th>芯片</th>
									<th>固件版本</th>
									<th>安灯颜色</th>
								</tr>
								<tr>
									<td>
										<span>{{ boxInfos.sim }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="boxEdit"
											placeholder="请输入盒子类型"
											v-model="boxInfos.andonLightType"
										></el-input>
										<span v-show="!boxEdit">{{ boxInfos.andonLightType }}</span>
									</td>
									<td>
										<el-input type="number" v-show="boxEdit" placeholder="请输入归属团队" v-model="boxInfos.teamName"></el-input>
										<span v-show="!boxEdit">{{ boxInfos.teamName }}</span>
									</td>
									<td>
										<el-input type="number" v-show="boxEdit" placeholder="请输入芯片" v-model="boxInfos.chipName"></el-input>
										<span v-show="!boxEdit">{{ boxInfos.chipName }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="boxEdit"
											placeholder="请输入固件版本"
											v-model="boxInfos.firmwareName"
										></el-input>
										<span v-show="!boxEdit">{{ boxInfos.firmwareName }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="boxEdit"
											placeholder="请输入安灯颜色"
											v-model="boxInfos.andonColor"
										></el-input>
										<span v-show="!boxEdit">{{ boxInfos.andonColor }}</span>
									</td>
								</tr>
								<tr>
									<th>最后上线时间</th>
									<th>团队到期时间</th>
									<th>信号强度</th>
									<th>是否在线</th>
									<th>运营商及到期时间</th>
									<th>创建人及创建时间</th>
								</tr>
								<tr>
									<td>
										<el-input
											type="number"
											v-show="boxEdit"
											placeholder="请输入最后上线时间"
											v-model="boxInfos.lastOnlineTime"
										></el-input>
										<span v-show="!boxEdit">{{ dateFormat(boxInfos.lastOnlineTime, 'lineM') }}</span>
									</td>
									<td>
										<el-input
											type="number"
											v-show="boxEdit"
											placeholder="请输入到期时间"
											v-model="boxInfos.memberExpiration"
										></el-input>
										<span v-show="!boxEdit">{{ dateFormat(boxInfos.memberExpiration, 'line') }}</span>
									</td>
									<td>
										<span>{{ boxInfos.signalIntensity }}</span>
									</td>
									<td>
										<span :style="{ color: onlineColorMap[boxInfos.line] }">{{ boxInfos.line ? '在线' : '离线' }}</span>
									</td>
									<td>
										<span v-if="boxInfos.operator || boxInfos.operatorExpiration"
											>{{ boxInfos.operator }} | {{ dateFormat(boxInfos.operatorExpiration, 'line') }}
										</span>
									</td>
									<td>
										<span v-if="boxInfos.createName || boxInfos.createTime"
											>{{ boxInfos.createName }} | {{ dateFormat(boxInfos.createTime, 'lineM') }}</span
										>
									</td>
								</tr>
							</table>
							<div :style="{ visibility: boxInfos.updated && boxInfos.updated != 1 ? 'visible' : 'hidden' }" class="bot_left">
								<span
									>*<span>{{ getUpdateStr(boxInfos.updated) }}</span></span
								>
								<el-button
									type="text"
									class="el-icon-refresh-right"
									@click="openLightDetail(boxInfos.sim, true, !boxEdit)"
									style="margin-left: 2vw"
									>刷新</el-button
								>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<el-dialog :visible.sync="dialogUpgradeBox" width="30%" :close-on-click-modal="false" @close="closeUpgradeBox">
			<el-row slot="title">批量升级固件版本</el-row>
			<el-form :model="eidtWorkForm" label-width="7vw" label-position="left" :rules="editRules">
				<el-form-item label="芯片" prop="chipName">
					<el-input placeholder="请输入芯片" v-model="eidtWorkForm.chipName" disabled></el-input>
				</el-form-item>
				<el-form-item label="安灯盒子类型" prop="andonLightType">
					<el-input placeholder="请选择安灯盒子类型" v-model="eidtWorkForm.andonLightType" disabled></el-input>
				</el-form-item>
				<el-form-item label="目标固件版本" prop="updateFirmware">
					<el-select class="W100" v-model="eidtWorkForm.updateFirmware" placeholder="请选择适用固件版本">
						<el-option
							v-for="item in updateFirmwareList"
							:key="'product' + item.aftid"
							:label="item.firmwareName + ' ' + dateFormat(item.releaseTime, 'line')"
							:value="item.aftid"
						>
						</el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="upgradeAndonBox">批量升级</el-button>
			</el-row>
		</el-dialog>
		<el-dialog :visible.sync="dialogAddBox" width="30%" close-on-click-modal @close="cancelAddBox">
			<el-row slot="title">添加安灯盒子</el-row>
			<el-form :model="editBoxForm" label-width="7vw" label-position="left" ref="addBoxRef" :rules="editRules">
				<el-form-item label="SIM编号" prop="sim">
					<el-input placeholder="请输入安灯盒子编号" v-model="editBoxForm.sim"></el-input>
				</el-form-item>
				<el-form-item label="芯片" prop="chipName">
					<el-select class="W100" v-model="editBoxForm.chipName" placeholder="请选择芯片" filterable clearable>
						<el-option v-for="item in chipList" :key="'chip' + item.id" :label="item.chipName" :value="item.chipName">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="安灯盒子类型" prop="andonLightType">
					<el-select class="W100" v-model="editBoxForm.andonLightType" placeholder="请选择安灯盒子类型">
						<el-option
							v-for="item in andonTypeList"
							:key="'andonLightType' + item.id"
							:label="item.andonLightType"
							:value="item.andonLightType"
						>
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="运营商" prop="operator">
					<el-input placeholder="请输入运营商" v-model="editBoxForm.operator"></el-input>
				</el-form-item>
				<el-form-item label="运营商到期时间" prop="operatorExpiration">
					<el-date-picker
						v-model="editBoxForm.operatorExpiration"
						type="date"
						class="W100"
						placeholder="请选择运营商到期时间"
					></el-date-picker>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveAddBox">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
import ImportTable from '@/components/ImportTable'; //导入组件
import ImportBtn from '@/components/ImportTable/ImportBtn'; //导入按钮
import AndonBoxFirmwareVersion from '@/pages/andonBoxManagement/AndonBoxFirmwareVersion';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import QRCode from 'qrcode';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		AndonBoxFirmwareVersion,
		ImportTable,
		ImportBtn,
	},
	name: 'andonBoxList',
	data() {
		return {
			queryStr: '',
			activeTab: 'andonBoxList',
			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			// 表格相关
			tableData: [],
			tableDataCopy: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '盒子SIM编号', align: 'left', colNo: 'sim', width: 200 },
				{ colName: '盒子类型', align: 'center', colNo: 'andonLightType', width: 100 },
				{ colName: '归属团队', align: 'left', colNo: 'teamName' },
				{ colName: '芯片', align: 'left', colNo: 'chipName' },
				{ colName: '固件版本', align: 'left', colNo: 'firmwareName' },
				{ colName: '信号强度', align: 'right', colNo: 'signalIntensity' },
				{ colName: '最后上线时间', align: 'center', colNo: 'lastOnlineTime' },
				{ colName: '安灯颜色', align: 'center', colNo: 'andonColor' },
				{ colName: '是否在线', align: 'center', colNo: 'line', width: 100 },
				{ colName: '创建时间', align: 'center', colNo: 'createTime' },
			],
			searchForm: {
				chipName: '',
				firmwareName: '',
				bindingTeamName: '', //绑定团队
				operator: '',
				sim: '',
				line: ['0', '1'],
			},
			selectionData: [], //批量选择的数据

			onlineStatusMap: {
				false: '离线',
				true: '在线',
			},
			andonColorMap: {
				0: '#DC143C', //红
				1: '#F7A944', //黄/橙
				2: '#28D094', //绿
				3: '#2196f3', //蓝色
				4: '#fafafa', //白色
				5: '', //空
			},
			onlineColorMap: {
				1: '#28D094', //绿
				// "0": "#F7A944", //橙
				// 0: "#DC143C", //红
			},
			lightTypeMap: {
				0: '未测试',
				1: '测试未通过',
				2: '待出库',
				3: '已经发货',
				4: '退货',
				5: '报废',
			},
			dialogUpgradeBox: false,
			//业务顾问修改
			eidtWorkForm: {
				chipName: '',
				andonLightType: '',
				updateFirmware: '',
			},

			updateFirmwareList: [], //可升级固件列表
			//安灯盒子编辑
			editBoxForm: {
				sim: '',
				productId: '',
				chipName: '',
				operator: '',
				andonLightType: '',
				operatorExpiration: '',
			},
			dialogAddBox: false,
			editRules: {
				sim: [{ required: true, message: '请输入安灯盒子编号', trigger: 'blur' }],
				updateFirmware: [{ required: true, message: '请输入目标固件版本', trigger: 'blur' }],
				andonLightType: [
					{
						required: true,
						message: '请选择安灯类型',
						trigger: ['blur', 'change'],
					},
				],
				chipName: [
					{
						required: true,
						message: '请选择芯片',
						trigger: ['blur', 'change'],
					},
				],
			},
			productList: [],
			andonTypeList: [
				// {
				//   id: 0,
				//   andonLightType: "未知",
				// },
				{
					id: 1,
					andonLightType: '4G',
				},
				{
					id: 2,
					andonLightType: 'WIFI',
				},
			],
			chipList: [
				{
					id: 1,
					chipName: 'Air720-TD-LTE',
				},
				{
					id: 2,
					chipName: 'Air724UG-NAT',
				},
				{
					id: 3,
					chipName: 'Air724UG-NFC',
				},
				{
					id: 4,
					chipName: 'WIFI',
				},
				{
					id: 5,
					chipName: 'STM32',
				},
				{
					id: 6,
					chipName: 'STM32-5G',
				},
			],

			boxInfos: {
				qrCode: '',
				sim: '',
				andonLightType: 0,
				teamName: 0,
				chipName: 0,
				firmwareName: 0,
				lastOnlineTime: '',
				memberExpiration: '',
				andonColor: '',
				line: '',
				messageVersion: '',
				updated: 1,
			},
			multipleLights: [],
			mulIndex: '',
			boxEdit: false,
		};
	},
	// 监听属性 类似于data概念
	computed: {},
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryProData();
		this.queryTableData();
	},
	activated() {
		this.queryProData();
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
		//切换tab页
		changeTab(tab, event) {
			if (tab.name == 'andonBoxList') {
				this.queryTableData();
			}
		},

		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const str = JSON.stringify({
				...this.dateSelectObj,
				line: this.searchForm.line,
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				query: this.queryStr,
			});
			this.$axios
				.andonBoxList(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tableDataCopy = _.deepClone(this.tableData);
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('andonBoxList |' + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		toggleAside(value, type) {
			//侧边栏滑出
			const workDiv = document.getElementById('andonBoxListToggle');
			const outDiv = document.getElementById('andonBoxList');
			if (workDiv) {
				let newValue = 0;
				if (value) {
					newValue = 0;
					outDiv.style.height = 'auto';
					outDiv.style.overflow = 'initial';
				} else {
					newValue = '110%';
					outDiv.style.height = '100%';
					outDiv.style.overflow = 'hidden';
				}
				workDiv.style.left = newValue;
			}
		},

		closeUpgradeBox() {
			this.dialogUpgradeBox = false;
			this.updateFirmwareList = [];
			this.eidtWorkForm.chipName = '';
			this.eidtWorkForm.updateFirmware = '';
			this.queryTableData();
		},
		getFirmwareList() {
			const str = JSON.stringify({
				andonLightType: this.eidtWorkForm.andonLightType,
				chipName: this.eidtWorkForm.chipName,
				pageNum: '',
				pageSize: '',
				query: '',
			});
			this.$axios
				.selectAndonFirmwareTypeVO(str)
				.then(res => {
					if (res.data.success) {
						this.updateFirmwareList = res.data.data;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAndonFirmwareTypeVO |' + error);
				});
		},
		//日期format
		dateFormat: _.dateFormat,
		openDialogUpgrade() {
			let isSameChip = false;
			if (this.selectionData && this.selectionData.length > 0) {
				const chipName = this.selectionData[0].chipName;
				const andonLightType = this.selectionData[0].andonLightType;
				isSameChip = this.selectionData.every(item => {
					return item.chipName == chipName;
				});
				if (isSameChip) {
					this.eidtWorkForm.chipName = chipName;
					this.eidtWorkForm.andonLightType = andonLightType;
					this.getFirmwareList();
					this.dialogUpgradeBox = true;
				} else {
					this.$message.warning('必须是同芯片的安灯盒子可以进行批量升级');
				}
			} else {
				this.$message.warning('请勾选安灯盒子后再批量升级');
			}
		},
		upgradeAndonBox() {
			const alIds = [];
			this.selectionData.forEach(item => {
				alIds.push(item.alId);
			});

			const str = JSON.stringify({
				aftid: this.eidtWorkForm.updateFirmware,
				alIds,
			});
			this.$axios
				.andonFirmwareUpgrade(str)
				.then(res => {
					if (res.data.success) {
						this.closeUpgradeBox();
						this.$succ('操作成功!');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('andonFirmwareUpgrade |' + error);
				});
		},
		delWorkOrderAll() {
			let alids = [],
				str;
			if (this.selectionData && this.selectionData.length > 0) {
				this.$confirm('勾选的安灯盒子将被删除', '删除安灯盒子', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.selectionData.forEach(item => {
							alids.push(item.alId);
						});
						str = JSON.stringify({
							alids: alids,
						});
						this.$axios
							.deleteAndonLights(str)
							.then(res => {
								if (res.data.success) {
									this.$succ('操作成功!');
									this.tablePageForm.currentPage = 1;
									this.queryTableData();
								} else {
									this.$err(res.data.message);
								}
							})
							.catch(error => {
								console.log('deleteAndonLights |' + error);
							});
					})
					.catch(() => {
						this.$message.info('已取消');
					});
			} else {
				this.$message.warning('请勾选安灯盒子后再删除');
			}
		},

		// 数据导入
		openImport: _.debounce(function (type = 'import') {
			const PROPS = {
				API: 'inportAndonBoxList', //导入接口
				templateName: 'andonLight_template', //模板文件名称（下载模板用）
				dataName: '安灯盒子数据', //数据名（提示：成功导入xxx数据xxx条!）
				type, // 导入或查看导入记录
			};
			this.$refs.ImportTable.openImport(PROPS);
		}),
		cancelAddBox() {
			this.dialogAddBox = false;
			this.editBoxForm.sim = '';
			this.editBoxForm.operator = '';
			this.editBoxForm.chipName = '';
			this.editBoxForm.andonLightType = '';
			this.$refs.addBoxRef.resetFields();
		},
		// 添加安灯盒子
		saveAddBox() {
			const sim = this.editBoxForm.sim,
				chipName = this.editBoxForm.chipName,
				andonLightType = this.editBoxForm.andonLightType;
			if (!sim) {
				this.$message.warning('请输入安灯盒子编号');
				return;
			}
			// if (!proId) {
			//   this.$message.warning("请选择适用产品");
			//   return;
			// }
			if (!chipName) {
				this.$message.warning('请输入芯片');
				return;
			}
			if (!andonLightType) {
				this.$message.warning('请输入安灯盒子类型');
				return;
			}
			const str = JSON.stringify({
				andonLightType: andonLightType,
				chipName: chipName,
				productId: 0,
				sim: sim,
				operator: this.editBoxForm.operator,
				operatorExpiration: this.editBoxForm.operatorExpiration,
			});
			this.$axios
				.addAndonBox(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
						this.cancelAddBox();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('addAndonBox |' + error);
				});
		},
		queryProData() {
			this.productList = [];
			this.$axios
				.getTricolourProductList(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.productList = res.data.data;
					}
				})
				.catch(error => {
					console.log('getTricolourProductList |' + error);
				});
		},
		cleartLight() {
			this.toggleAside(0);
			this.boxInfos = '';
			this.mulIndex = 0;
			this.multipleLights = [];
			this.$refs.uTableRef.clearSelection();
		},
		openLightDetail(row, flag, edit) {
			if (!row.sim) {
				this.$message.warning('数据异常');
				return;
			}
			this.boxInfos = row;
			this.boxInfos.qrCode = row.simQRCode.replace(/\\/, '');

			if (!flag) {
				this.toggleAside(1, 'andonBox');
				setTimeout(() => {
					this.getQrcode(this.boxInfos.qrCode);
				}, 300);
			} else {
				this.getQrcode(this.boxInfos.qrCode);
			}
			if (!edit) {
				this.boxEdit = true;
			} else {
				this.boxEdit = false;
			}
		},
		getQrcode(str) {
			const canvas = document.getElementById('qrcodeBox');
			QRCode.toCanvas(canvas, str, error => {
				if (error) {
					console.log('二维码异常', error);
				}
			});
		},
		getUpdateStr(status) {
			//刷新字符串
			let str = '',
				nowTime = new Date();
			if (!status) {
				return;
			}
			switch (status) {
				case 1:
					str = '';
					break;
				case 2:
					str = '等待参数同步结果,请检查安灯盒子是否在线，稍后“刷新”查看同步结果';
					break;
				case 3:
					str = '参数已同步';
					break;
				case 4:
					str = this.$moment(nowTime).format('MM/DD HH:mm:ss') + '同步参数...  稍后点击“刷新”查看同步结果';
					break;
				default:
					str = '';
			}
			return str;
		},
	},
};
</script>

<style lang="scss">
#andonBoxList {
	width: 100%;
	overflow: hidden;
	position: relative;
	#andonBoxListToggle {
		position: absolute;
		width: 100%;
		min-height: 100%;
		top: 0;
		left: 110%;
		z-index: 66;
		transition: left 0.3s linear;
		background: #f2f2f2;
		// overflow-y:overlay;
		overflow-x: hidden;

		.p_row {
			padding: 0;
			display: flex;
			align-items: center;

			.p_label {
				width: 8px;
				height: 16px;
				display: inline-block;
				background-color: #23b781;
			}

			.p_text {
				margin-left: 0.3vw;
				font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
				font-weight: 650;
				color: #666666;
			}
		}

		.p_nor {
			padding: 0 0.5vw;
			margin: 0.2vh 0;
			display: flex;
			align-items: center;

			span {
				color: #999;
				font-size: 12px;
			}

			pre {
				color: #999;
				font-family: 'Microsoft YaHei';
				font-size: 12px;
				margin: 0;
				margin-left: 0.5vw;
				display: inline-block;
			}
		}

		.workCtable {
			width: 99%;
			border-left: 1px solid #e9e9e9;
			border-bottom: 1px solid #e9e9e9;

			tr {
				border-color: #e9e9e9;

				th {
					text-align: left;
					font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
					font-weight: 650;
					font-style: normal;
					font-size: 14px;
					color: #666666;
					padding: 15px 0 15px 5px;
					background: #f5f5f5;
					border-right: 1px solid #e9e9e9;
					border-top: 1px solid #e9e9e9;
					word-wrap: break-word;
				}

				td {
					border-right: 1px solid #e9e9e9;
					border-top: 1px solid #e9e9e9;
					font-size: 12px;

					.norSpan {
						font-size: 14px;
						margin-left: 0.2vw;
						color: #666;
					}
				}
			}
		}

		.bot_left {
			font-size: 13px;
			margin-top: 1.5vh;
			display: flex;
			align-items: center;
			color: #ec808d;
		}
		.stepCont {
			.el-steps {
				.el-step__head {
					> .el-step__icon {
						width: 30px !important;
						height: 30px !important;
					}

					> .el-step__line {
						top: 14px;
					}
				}

				.el-step__head.is-success {
					color: #28d094;
					border-color: #28d094;

					> .el-step__line {
						.el-step__line-inner {
							width: 100% !important;
							border-width: 1px !important;
							background-color: #28d094;
						}
					}
				}

				.el-step__head.is-process {
					border-color: #28d094;
					color: #fff;

					> .el-step__line {
						.el-step__line-inner {
							width: 50% !important;
							border-width: 1px !important;
							background-color: #28d094;
						}
					}

					.el-step__icon {
						background-color: #28d094;
					}
				}

				.el-step__title {
					color: #666;
					font-weight: 400;
					font-size: 12px;
				}

				.el-step__description {
					color: #ccc;
				}
			}

			.norBlock {
				width: 90%;
				box-sizing: border-box;
				border: 1px solid #e9e9e9;
				margin: 2vh 0;
				margin-left: 4vw;

				p {
					margin: 5px 0;
					padding: 0 5px;
				}

				.norTitle {
					color: #666;
					font-weight: 500;
				}

				.norTip {
					color: #999;
					font-size: 13px;
				}
			}

			.norDiv {
				p {
					margin: 10px 0;
					padding: 0 5px;
				}

				.norTitle {
					color: #666;
					font-weight: 500;
					font-size: 20px;
				}

				.norTip {
					color: #999;
					font-size: 15px;
				}
			}

			.percentCont {
				.el-progress {
					margin-top: 5vh;
					width: 80%;
					margin: auto;

					.el-progress-bar__outer {
						background-color: #e6fff7;
						border-radius: 0;
						border: 1px solid #28d094;

						.el-progress-bar__inner {
							border-radius: 0;
						}
					}

					.el-progress__text {
						position: absolute;
						font-size: 14px;
						color: #999;
						top: -50px;
						left: 47%;
					}
				}
			}

			.importTable {
				margin-top: 8vh;

				.isNeedTh:after {
					content: '*';
					color: #f56c6c;
					margin-left: 4px;
				}

				.el-table__header-wrapper {
					background-color: #f2f2f2;
					color: #333;
				}

				.el-table__body-wrapper {
					.el-table__row {
						td:last-child {
							border-right: 2px solid #e9e9e9;
						}
					}

					td.is-left {
						border-left: 1px solid #e9e9e9;
					}
				}
			}
		}
	}
}
</style>
