<!-- 这是一个递归组件：评论回复树 -->
<template>
	<div class="ReplyTree flex-column gap-10 fs-12">
		<div v-for="(item, index) in replies" :key="item.replyAuid + item.replyTime + index" class="tree-item flex-column">
			<!-- 回复 -->
			<div class="flex gap-10">
				<!-- 回复信息（用户名、时间） -->
				<div class="flex-column ellipsis w-125">
					<div class="ellipsis" v-if="parent.replyUserName">
						<span class="flex-column">
							<span class="bolder ellipsis" :title="item.replyUserName">{{ item.replyUserName }}：</span>
							<span class="color-999 ellipsis" :title="parent.replyUserName">回复 @{{ parent.replyUserName }}</span>
						</span>
					</div>
					<div class="ellipsis" v-else>
						<span class="bolder ellipsis" :title="item.replyUserName"> {{ item.replyUserName }}</span>
					</div>
					<span class="color-999 ellipsis"> {{ dateFormat(item.replyTime, 'lineM') }}</span>
				</div>

				<!-- 回复内容 -->
				<div class="flex-column flex-1 flex-justify-between">
					<div class="flex-start">
						<pre class="m0 p0">{{ item.replyContent }}</pre>
						<div class="ml-auto" v-show="getEditBtn(item)">
							<el-button type="text" size="mini" class="el-icon-delete color-999 m0 p0" @click="deleteReply(item)"></el-button>
						</div>
					</div>

					<!-- 回复按钮 -->
					<div class="flex-align-center gap-10 h-18">
						<el-button type="text" size="mini" class="p0 m0" @click="openReply(item)">
							回复
							<i v-show="item.rid === editForm.parentRid" class="el-icon-arrow-down"></i>
						</el-button>
						<el-button v-if="item.getReplyListByQidVOS.length" type="text" size="mini" class="p0 m0" @click="openReplyList(item)">
							<span> {{ getOpenState(item) ? '收起' : '展开' }}({{ item.getReplyListByQidVOS.length }})</span>
							<i :class="getOpenState(item) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i>
						</el-button>
					</div>

					<!-- 回复输入框 -->
					<div v-if="item.rid === editForm.parentRid" class="flex-column W100">
						<el-input
							size="mini"
							class="min-w-300 W50"
							v-model="editForm.replyContent"
							:placeholder="`回复 @${item.replyUserName || parent.replyUserName}`"
							type="textarea"
							:autosize="{ minRows: 4, maxRows: 6 }"
						></el-input>

						<!-- 取消/回复 -->
						<div class="flex-align-center gap-10">
							<el-button class="mt10 W10 min-w-100" size="mini" type="info" @click="editForm.parentRid = ''"> 取消 </el-button>
							<el-button class="mt10 W10 min-w-100" size="mini" type="primary" @click="saveReply"> 回复 </el-button>
						</div>
					</div>
				</div>
			</div>

			<!-- 子级回复（对回答进行回复） -->
			<ReplyTree
				class="child-reply"
				v-show="item.getReplyListByQidVOS.length && getOpenState(item)"
				:parent="item"
				:replies="item.getReplyListByQidVOS"
				:detailForm="detailForm"
				:isChild="true"
				@refresh="$emit('refresh')"
			/>

			<!-- 父级回答的分割线-->
			<div :class="!isChild ? 'border-bottom pt10' : ''"></div>
		</div>
	</div>
</template>

<script>
import { deepClone, dateFormat, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'ReplyTree',
	props: {
		// 回复列表
		replies: {
			type: Array,
			required: true,
		},
		// 详情表单
		detailForm: {
			type: Object,
			required: true,
		},
		// 是否是子级回复
		isChild: {
			type: Boolean,
			default: false,
		},
		// 父级回复
		parent: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			editForm: {
				parentRid: '',
				qid: '',
				replyContent: '',
				content: '',
				rid: '',
				isOpen: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),
		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
	},
	methods: {
		// 获取展开状态
		getOpenState(item) {
			return this.editForm[`isOpen_${item.rid}`] || false;
		},
		// 设置展开状态
		setOpenState(rid, state) {
			this.$set(this.editForm, `isOpen_${rid}`, state);
		},
		// 获取修改/删除按钮
		getEditBtn(item) {
			return this.isSuperAdmin || item.replyAuid == this.userInfos.adminUserVO.auid;
		},
		// 删除回复
		deleteReply(item) {
			this.$confirm('此操作将永久删除该回复, 是否继续?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'deleteReply';
					try {
						const res = await this.$axios[API](JSON.stringify({ rid: item.rid }));
						if (res.data.success) {
							this.$succ(res.data.message);
							this.$emit('refresh');
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 打开回复
		openReply(item) {
			if (this.editForm.parentRid == item.rid) {
				this.editForm.parentRid = ''; // 取消回复
			} else {
				this.editForm.parentRid = item.rid; // 回复
			}
		},
		// 打开回复列表
		openReplyList(item) {
			const isOpen = !this.getOpenState(item);
			this.setOpenState(item.rid, isOpen);
			if (!isOpen) {
				this.editForm.parentRid = ''; // 取消回复
			}
		},
		// 保存回答
		async saveReply() {
			const { parentRid, replyContent } = this.editForm;
			const API = 'addReply';
			try {
				const res = await this.$axios[API](JSON.stringify({ parentRid, qid: '', replyContent }));
				if (res.data.success) {
					this.editForm.replyContent = '';
					this.editForm.parentRid = '';
					this.setOpenState(parentRid, true);
					this.$succ(res.data.message);
					this.$emit('refresh');
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		dateFormat, // 日期格式化
	},
};
</script>
<style lang="scss" scoped>
.ReplyTree {
	.h-18 {
		height: 18px !important;
	}
	.w-125 {
		width: 125px !important;
	}
	.max-w-125 {
		max-width: 125px !important;
	}
}

.child-reply {
	margin: 10px 10px 0 10px;
	padding: 5px;
	border-radius: 8px;
	background-color: #f9f9f9;
	border: 1px solid #f0f0f0;
	.tree-item {
		border-left: 1.5px dashed #d7d7d7;
		padding-left: 10px;
	}
}
</style>
