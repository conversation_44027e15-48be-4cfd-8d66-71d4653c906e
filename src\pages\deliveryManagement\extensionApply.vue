<template>
	<div id="extensionApply">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="deal"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twid = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="延期申请" name="extensionApply">
				<BaseLayout>
					<template #header>
						<span class="search-label">成交月份</span>
						<el-date-picker
							class="w-150"
							size="small"
							v-model="dateSelectObj.startTime"
							type="month"
							value-format="timestamp"
							placeholder="不限"
							@change="queryTableData(1)"
							clearable
						>
						</el-date-picker>

						<SearchHistoryInput
							name="companyName"
							placeholder="客户工商注册名称"
							v-model.trim="searchForm.queryParam"
							@input="queryTableData(1)"
						/>

						<el-checkbox-group v-model="searchForm.approvalStatus" @change="queryTableData(1)">
							<el-checkbox label="0">待审核</el-checkbox>
							<el-checkbox label="1">已审核</el-checkbox>
						</el-checkbox-group>

						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<ExportBtn @trigger="openExport" />
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['dealMonth', 'initialComplateMonth', 'afterExtension', 'extension'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'YM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 各种人员 -->
									<Tooltips
										v-else-if="['salesman', 'implement', 'consulting', 'talk'].includes(item.colNo)"
										:cont-str="getUserName(scope.row[item.colNo])"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<u-table-column label="" width="" align="right">
								<template slot-scope="scope">
									<div class="flex-align-center">
										<el-button type="text">
											<span v-show="scope.row.status == 0 && isSuperAdmin">
												<span @click="openDialog(scope.row, '审核')"> 审核</span>
											</span>
											<span v-show="scope.row.status == 1" class="color-999 fs-12">
												<Tooltips
													:cont-str="getApprovalStr(scope.row, 1)"
													:cont-obj="[
														{ title: '审核人：', content: scope.row.approvalUname },
														{ title: '审核结果：', content: '审核通过' },
														{ title: '审核意见：', content: scope.row.approvalOpinion },
														{ title: '审核时间：', content: dateFormat(scope.row.approvalDate, 'lineM') },
													]"
													:cont-width="(scope.column.width || scope.column.realWidth) - 20"
												/>
											</span>
											<span v-show="scope.row.status == 2" class="red fs-12">
												<Tooltips
													:cont-str="getApprovalStr(scope.row, 2)"
													:cont-obj="[
														{ title: '审核人：', content: scope.row.approvalUname },
														{ title: '审核结果：', content: '驳回' },
														{ title: '审核意见：', content: scope.row.approvalOpinion },
														{ title: '审核时间：', content: dateFormat(scope.row.approvalDate, 'lineM') },
													]"
													:cont-width="(scope.column.width || scope.column.realWidth) - 20"
												/>
											</span>
										</el-button>
									</div>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>

		<!-- 展期申请弹窗 -->
		<el-dialog :visible.sync="dialogExtension" width="30%" :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="5vw" label-position="left" ref="editFormRef" :rules="editRules">
				<el-form-item label="延期原因" prop="">
					<Tooltips :cont-str="editForm.remark" />
				</el-form-item>
				<el-form-item label="审核意见" prop="approvalOpinion">
					<el-input
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						placeholder="请输入审核意见，不同意时请填写说明"
						v-model="editForm.approvalOpinion"
					></el-input>
				</el-form-item>
				<el-form-item label="">
					<div style="display: flex; align-items: center; height: 40px">
						<el-radio-group v-model="editForm.status" class="flex-align-center">
							<el-radio :label="1">同意</el-radio>
							<el-radio :label="2">不同意</el-radio>
						</el-radio-group>
					</div>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveEdit">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable'; //导出组件
import ChannelSelect from '@/components/ChannelSelect.vue';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		ChannelSelect,
		ExportBtn,
	},
	name: 'extensionApply',
	data() {
		return {
			uid: '',
			activeTab: 'extensionApply',
			queryParam: '',
			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableDataCopy: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'left', width: '95' },
				{ colName: '公司工商注册名称', colNo: 'registeredBusinessName', align: 'left', width: '170' },
				{ colName: '业务顾问', colNo: 'salesman', align: 'left', width: '95' },
				{ colName: '实施顾问', colNo: 'implement', align: 'left', width: '95' },
				{ colName: '咨询', colNo: 'consulting', align: 'left', width: '' },
				{ colName: '谈单', colNo: 'talk', align: 'left', width: '' },
				{ colName: '成交月份', colNo: 'dealMonth', align: 'center', width: '95' },
				{ colName: '合同金额(万元)', colNo: 'dealAmount', align: 'right', width: '95' },
				{ colName: '已收款金额', colNo: 'receivedAmount', align: 'right', width: '110' },
				{ colName: '完工期数', colNo: 'complate', align: 'right', width: '' },
				{ colName: '未完工期数', colNo: 'unComplate', align: 'right', width: '70' },
				{ colName: '当前阶段', colNo: 'currentStage', align: 'center', width: '65' },
				{ colName: '当前计划完工月份', colNo: 'afterExtension', align: 'center', width: '90' },
				{ colName: '延期次数', colNo: 'requestQty', align: 'center', width: '65' },
				{ colName: '延期后计划完工月份', colNo: 'extension', align: 'left', width: '100' },
				{ colName: '延期原因', colNo: 'remark', align: 'left', width: '' },
			],
			searchForm: {
				approvalStatus: ['0'],
				twid: [],
				channelName: '',
				queryParam: '',
			},
			isSuperAdmin: false,
			dialogEdit: false,
			dialogExtension: false,
			editForm: {
				erid: '',
				approvalOpinion: '',
				status: '',
				remark: '',
			},
			dialogTitle: '客户详情',
			editRules: {
				abbreviation: [{ required: true, message: '客户简称', trigger: 'blur' }],
				clientNeed: [{ required: true, message: '客户需求', trigger: 'blur' }],
				linkman: [{ required: true, message: '请输入对接人信息', trigger: 'blur' }],
				linkphone: [{ required: true, message: '请输入对接人联系方式', trigger: 'blur' }],
				region: [{ required: true, message: '客户地址', trigger: 'blur' }],
				regions: [{ required: true, message: '客户地址', trigger: 'blur' }],
				salesman: [{ required: true, message: '请输入业务顾问', trigger: 'blur' }],
				registeredBusinessName: [{ required: true, message: '客户工商注册名称', trigger: 'blur' }],
				protectDeadline: [{ required: true, message: '请输入备案保护日期', trigger: 'blur' }],
				spreadTime: [{ required: true, message: '请输入展期日期', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.uid = this.$route?.query?.queryId || '';

		if (this.userInfos?.adminUserVO.adminRoleVOS) {
			this.isSuperAdmin = false;
			this.userInfos?.adminUserVO.adminRoleVOS.map(item => {
				//  超级管理员/渠道经理 可审核
				if (item.arid == 15 || item.arid == 1) this.isSuperAdmin = true;
			});
		}
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 切换tab页
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			this.dateSelectObj.endTime = this.dateSelectObj.startTime ? _.getNowMonthEndDay(this.dateSelectObj.startTime) : null;
			type && (this.tablePageForm.currentPage = 1);
			const str = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.dateSelectObj,
				...this.searchForm,
			});
			this.$axios
				.selectExtensionList(str)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item['checkRecord'] = `${item.checkStatus == 0 ? '通过' : '不通过'}，${item.checkUname}，${
								item.checkOpinion
							}，${this.dateFormat(item.checkDate, 'lineM')}`;
						});
						this.tableData = res.data.data;
						this.tableDataCopy = _.deepClone(this.tableData);
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectExtensionList |' + error);
				});
		}),

		openDialog(row, type) {
			this.dialogTitle = type;
			this.editForm.erid = row.erid;
			this.editForm.approvalOpinion = row.approvalOpinion;
			this.editForm.remark = row.remark;
			this.editForm.status = row.status == null ? '' : row.status;
			if (type == '审核') {
				this.dialogExtension = true;
			} else {
				this.dialogEdit = true;
			}
		},
		closeDialog() {
			this.dialogEdit = false;
			this.dialogExtension = false;

			this.$refs.editFormRef.resetFields();
		},
		saveEdit() {
			const { approvalOpinion, erid, status } = this.editForm;

			if (!status && status != 1) {
				this.$message.warning('请选择同意/不同意');
				return;
			}
			if (status == 2 && !approvalOpinion) {
				this.$message.warning('请填写申请不通过的原因！');
				return;
			}
			const str = JSON.stringify({
				approvalOpinion,
				erid,
				status,
			});
			this.$axios
				.approval(str)
				.then(res => {
					if (res.data.success) {
						this.$succ(res.data.message);
						this.closeDialog();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('approval  |' + error);
				});
		},
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		getUserName(item) {
			if (item) {
				return item.userName || '';
			}
			return '';
		},
		// 获取审批结果字符串
		getApprovalStr(item, type) {
			if (type == 1) {
				return '审核通过  ' + item.approvalUname + ' ' + this.dateFormat(item.approvalDate, 'lineM');
			} else {
				return '驳回  ' + item.approvalUname + ' ' + this.dateFormat(item.approvalDate, 'lineM');
			}
		},
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableDataCopy, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop == 'salesman' || prop == 'implement' || prop == 'consulting' || prop == 'talk') {
						if (!a?.userName) return -1;
						if (!b?.userName) return 1;
						return a?.userName.localeCompare(b?.userName, 'zh-CN');
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.tableData = [...sortedData];
				this.$refs.uTableRef?.reloadData(this.tableData);
			} else {
				this.tableData = [...this.tableDataCopy];
				this.$refs.uTableRef?.reloadData(this.tableData);
			}
		},
		//数据导出
		openExport: _.debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					pageNum: this.tablePageForm.currentPage,
					pageSize: this.tablePageForm.pageSize,
					...this.dateSelectObj,
					...this.searchForm,
					...this.searchForm,
				}), //接口参数
				API: 'exportExtensionRequest', //导出接口
				downloadData: '延期申请导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>
<style lang="scss" scoped>
#extensionApply {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
