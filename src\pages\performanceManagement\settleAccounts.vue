<template>
	<div class="settleAccounts">
		<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
			<div class="detail-wrapper">
				<div class="detail-head">
					<span class="detail-title">业绩结算明细</span>
					<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
				</div>
				<div class="detail-content table-wrapper">
					<p class="detail-content-title">基本信息 </p>
					<p>{{ titleName }}</p>
					<SettlementList ref="SettlementList" :searchForm="searchForm_detail" @openAllocationPlan="openAllocationPlan" />
				</div>
			</div>
		</div>
		<!-- 合同业绩分配 -->
		<AllocationPlan ref="AllocationPlan" @close="queryTableData" />

		<el-tabs v-model="activeTab">
			<el-tab-pane label="业绩结算" name="settleAccounts">
				<BaseLayout>
					<template #header>
						<span class="search-label">年度</span>
						<el-date-picker
							size="small"
							class="w-150"
							v-model="searchForm.searchYear"
							type="year"
							value-format="timestamp"
							format="yyyy 年"
							placeholder="请选择年份"
							@change="queryTableData(1)"
							:default-value="new Date()"
							:clearable="false"
						>
						</el-date-picker>
						<!-- 模糊查询 -->

						<SearchHistoryInput
							name="salesmanName"
							placeholder="分配对象"
							v-model.trim="searchForm.userName"
							@input="queryTableData(1)"
						/>

						<el-radio-group v-model="searchForm.type" @change="queryTableData(1)">
							<el-radio :label="0">已回款</el-radio>
							<el-radio :label="1">待回款</el-radio>
						</el-radio-group>

						<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<div class="table-toolbar"> </div>
						<!-- :pagination-show="true" -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:data="tableData"
							@sort-change="sortChange"
							use-virtual
							stripe
							show-summary
							:summary-method="summaryMethod"
						>
							<u-table-column label="序号" width="50" align="center" type="index"> </u-table-column>

							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								resizable
								sortable="custom"
							>
								<template slot-scope="scope">
									<div v-if="item.colNo != 'uaname'">
										<Tooltips
											@click.native="openDetail(scope.row, item.colNo)"
											class="hover-green"
											:cont-str="scope.row[item.colNo] ? scope.row[item.colNo]?.toFixed(2) + '' : ''"
											:cont-width="(scope.column.width || scope.column.realWidth) - 18"
										>
										</Tooltips>
									</div>
									<!-- <div v-else-if="item.colNo == 'uaname'">
										<span>{{ getRowStr(scope.row[item.colNo]) }}</span>
									</div> -->

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import AllocationPlan from '@/pages/deliveryManagement/contractManagement/allocationPlan.vue';
import SettlementList from './SettlementList';

// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
export default {
	props: {},
	// import引入的组件需要注入到对象中才能使用
	components: {
		AllocationPlan,
		SettlementList,
	},
	name: 'settleAccounts',
	data() {
		return {
			activeTab: 'settleAccounts',
			showCom: false,
			titleName: '',
			searchForm: {
				searchYear: new Date().getTime(),
				type: 0,
				userName: '',
			},
			searchForm_detail: {},
			//日期相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableColumn: [
				{ colName: '分配对象', colNo: 'uaname', align: 'left' },
				{ colName: '一月', colNo: '0', align: 'right' },
				{ colName: '二月', colNo: '1', align: 'right' },
				{ colName: '三月', colNo: '2', align: 'right' },
				{ colName: '四月', colNo: '3', align: 'right' },
				{ colName: '五月', colNo: '4', align: 'right' },
				{ colName: '六月', colNo: '5', align: 'right' },
				{ colName: '七月', colNo: '6', align: 'right' },
				{ colName: '八月', colNo: '7', align: 'right' },
				{ colName: '九月', colNo: '8', align: 'right' },
				{ colName: '十月', colNo: '9', align: 'right' },
				{ colName: '十一月', colNo: '10', align: 'right' },
				{ colName: '十二月', colNo: '11', align: 'right' },
				// { colName: '合计', colNo: '12', align: 'right' },
			],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 是超级管理
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || false;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					return Number(item[column.property]) || 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? _.accAdd(prev, curr, 2) : prev;
				}, 0); //合计计算
				// means[columnIndex] = sum > 0 ? <span>{sum}</span> : '';
				means[columnIndex] = 
					<span
						class="hover-green"
						on-click={e => {
							e.stopPropagation();
							this.openDetail(null, columnIndex - 2);
						}}
					>
						{sum > 0 ? sum : ''}
					</span>
				;
			}
			return [means];
		},

		//自定义排序(非虚拟表格 )
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? _.sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 时间选择

		// 打开该清单
		openDetail(row, month) {
			if (this.searchForm.type == 1) {
				return this.$message.warning('抱歉，当前无法查看未回款业绩结算清单！');
			}
			// 非(超级管理员/业务范围内/当前人员)不可查看
			if (
				!(
					this.isSuperAdmin ||
					this.userInfos?.adminUserVO.auid == row?.uaid ||
					this.userInfos?.adminUserVO?.salesmanUid?.includes(row?.uaid) ||
					this.userInfos?.adminUserVO?.salesmanUid?.split(',').find(i => i == '888')
				)
			) {
				return this.$message.warning(`抱歉，您没有查看${row?.uaname ? row.uaname : '合计'}业绩结算清单的权限！`);
			}

			const year = this.$moment(this.searchForm.searchYear).year();
			const number_month = Number(month) + 1;

			this.searchForm_detail = {
				dateEnd: this.$moment(`${year}-${number_month}`, 'YYYY-M').endOf('month').valueOf(),
				dateStart: this.$moment(`${year}-${number_month}`, 'YYYY-M').startOf('month').valueOf(),
				commissionObjectId: row?.uaid || '',
				// commissionObjectName: row?.uaname || '',
			};
			this.$refs.SettlementList.queryTableData(1);

			this.titleName = `分配月份：${year}年${number_month}月  分配对象：${row?.uaname || '全部'}`;
			this.showCom = true;
		},
		// 打开明细
		openAllocationPlan(type, row) {
			this.$refs.AllocationPlan.showDetailCom(type, row);
		},
		// 获取询盘趋势数据
		queryTableData: _.debounce(function (type) {
			const searchYear = this.$moment(this.searchForm.searchYear).format('YYYY-MM-DD');
			this.$axios
				.selectDeliverStageCommissionStatisticList(JSON.stringify({ ...this.searchForm, searchYear }))
				.then(res => {
					if (res.data.success) {
						// 将channelList放到数据里
						res.data.data.forEach(item => {
							item.statisticList.map((cItem, cIndex) => {
								item[cIndex] = cItem;
							});
						});
						this.tableData = res.data.data;
						this.tableDataCopy = _.deepClone(this.tableData);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectDeliverStageCommissionStatisticList |' + error);
				});
		}),
	},
};
</script>

<style lang="scss" scoped>
.settleAccounts {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
