<template>
	<div id="firmwareVersion">
		<div class="left">
			<div style="width: 90%; margin: 2vh auto">
				<div style="position: relative; height: 30px; line-height: 30px">
					<div class="inline-div" style="left: 0.5vw">
						<p class="label-title">固件类型</p>
					</div>
				</div>
				<div class="treeContainer" style="margin-top: 1.5vh; max-height: 75vh; overflow-y: auto">
					<el-tree
						:data="firmwareTreeList"
						ref="calendarRef"
						node-key="id"
						default-expand-all
						:renderContent="renderContent"
						@node-click="openChip"
					></el-tree>
				</div>
			</div>
		</div>
		<div class="right" style="position: relative">
			<div style="position: absolute; top: 35%; left: 40%" v-if="!proFlag">
				<img src="@/assets/img/none.webp" />
				<p style="color: #7f7f7f; padding: 0 10px; box-sizing: border-box">请选择固件</p>
			</div>
			<div v-if="proFlag">
				<p class="label-title">{{ nowProName }}</p>
			</div>
			<div class="table-wrapper" v-if="proFlag">
				<u-table
					class="table-main W100"
					:data="firmWareList"
					:height="1200"
					:row-height="45"
					v-loading.lock="calenTableLoading"
					stripe
				>
					<!-- <u-table-column label="行号" type="index" width="50" align="center"></u-table-column> -->
					<u-table-column prop="firmwareName" label="版本">
						<template slot-scope="scope">
							<Tooltips
								:toolClass="'tdTwoNormal'"
								:cont-str="scope.row.firmwareName"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<u-table-column label="说明" prop="description">
						<template slot-scope="scope">
							<Tooltips
								:toolClass="'tdTwoNormal'"
								:cont-str="scope.row.description"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<u-table-column label="发布日期" prop="createTime" width="100">
						<template slot-scope="scope">
							<Tooltips
								:cont-str="dateFormat(scope.row.createTime, 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<u-table-column label="固件位置" prop="downloadUrl">
						<template slot-scope="scope">
							<Tooltips
								:toolClass="'tdTwoNormal'"
								:cont-str="scope.row.downloadUrl"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<u-table-column label="" width="100" align="right">
						<template #header>
							<el-button type="text" class="icon-third-bt_newdoc" @click="dialogCopyShift = true">添加</el-button>
						</template>
						<template slot-scope="scope">
							<el-button type="text" class="el-icon-edit-outline" @click="updateFirmware(scope.row)"></el-button>
							<el-button type="text" class="el-icon-close" @click="delFirmware(scope.row, scope.$index)"></el-button>
						</template>
					</u-table-column>
				</u-table>
			</div>
		</div>
		<div style="clear: both"></div>
		<el-dialog :visible.sync="dialogCopyShift" width="666px" :close-on-click-modal="false" @close="cancelDiaCopyShift">
			<el-row slot="title">{{ eidtWorkForm.ftid ? '修改 ' + nowProName : '添加 ' + nowProName }} 固件版本</el-row>
			<div>
				<el-form label-width="100px" label-position="left">
					<el-form-item label="版本" prop="version">
						<el-input placeholder="请输入版本" v-model="eidtWorkForm.version"></el-input>
					</el-form-item>
					<el-form-item label="说明" prop="description">
						<el-input
							type="textarea"
							:autosize="{ minRows: 2, maxRows: 4 }"
							placeholder="请输入说明"
							v-model="eidtWorkForm.description"
						></el-input>
					</el-form-item>
					<el-form-item label="发布日期" prop="newNum">
						<el-date-picker
							v-model="eidtWorkForm.createTime"
							type="date"
							class="W100"
							placeholder="请选择发布日期"
						></el-date-picker>
					</el-form-item>
					<el-form-item label="固件位置" prop="file">
						<el-upload
							action=""
							accept="*"
							:http-request="uploadFile"
							:on-remove="handleRemove"
							:on-error="uploadFail"
							:file-list="excelFileList"
							:show-file-list="false"
						>
							<el-button type="text">
								<Tooltips class="max-w-500" :cont-str="eidtWorkForm.file ? eidtWorkForm.file : '上传'" cont-width="500" />
							</el-button>
						</el-upload>
					</el-form-item>
					<el-form-item label="适用产品" prop="fitProduct">
						<!-- <el-input placeholder="请输入适用产品" v-model="eidtWorkForm.fitProduct" ></el-input> -->
						<el-select class="W100" v-model="eidtWorkForm.fitProduct" placeholder="请选择适用产品">
							<el-option v-for="item in productList" :key="'product' + item.id" :label="item.productName" :value="item.id">
							</el-option>
						</el-select>
					</el-form-item>
				</el-form>
			</div>
			<el-row slot="footer">
				<el-button type="primary" @click="saveDiaCopyShift">保存</el-button>
			</el-row>
		</el-dialog>
		<el-dialog :visible.sync="dialogAddChip" width="25%" :close-on-click-modal="false" @close="cancelAddChip">
			<el-row slot="title">添加芯片</el-row>
			<div>
				<el-form ref="formChip" :model="chipForm" label-width="7vw" label-position="left">
					<el-form-item label="名称">
						<el-input placeholder="请输入名称" v-model="chipForm.chipName"></el-input>
					</el-form-item>
					<el-form-item label="版本">
						<!-- <el-input placeholder="请选择版本" v-model="chipForm.classCategory">

                                </el-input> -->
						<el-select class="W100" v-model="chipForm.classCategory" placeholder="请选择产品类型">
							<el-option v-for="item in NetTypeList" :key="'netType' + item" :label="item" :value="item"> </el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="产品类型">
						<!-- <el-input placeholder="请输入名称" v-model="chipForm.classType"></el-input> -->
						<el-select class="W100" v-model="chipForm.classType" placeholder="请选择产品类型">
							<el-option v-for="item in classTypeList" :key="'classType' + item.id" :label="item.classTypeName" :value="item.id">
							</el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="描述">
						<el-input placeholder="请输入名称" v-model="chipForm.description"></el-input>
					</el-form-item>
				</el-form>
			</div>
			<el-row slot="footer">
				<el-col :span="6" style="text-align: center">
					<el-button type="text" v-show="chipForm.ctid" @click="delChip">删除芯片</el-button>
				</el-col>
				<el-col :span="chipForm.ctid ? 14 : 20" :offset="4">
					<el-button size="mini" type="primary" @click="saveEditChip">保存</el-button>
				</el-col>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import folderImg from '@/assets/img/folder.svg';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'firmwareVersion',
	data() {
		return {
			teamDataInfo: {
				teamName: '',
				tid: '',
				uid: '',
			},
			firmwareTreeList: [
				{
					id: '1',
					label: '4G',
					children: [],
				},
				{
					id: '2',
					label: 'WIFI',
					children: [],
				},
			],
			firmWareList: [],
			proFlag: false,
			nowProName: '',
			dialogAddShift: false,
			shiftTableList: [],
			classGroups: [],
			classOrderList: [],
			colorList: [
				'#C01900',
				'#FF2600',
				'#FFC000',
				'#FFFB01',
				'#92D050',
				'#00B050',
				'#00B0F0',
				'#0070C0',
				'#002060',
				'#7030A0',
				'#F2F2F2',
			],
			shiftModeList: [],
			colorLeftCurr: [],
			colorRightCurr: [],
			ctidCurr: '',
			chipNameCurr: '',
			btnFlag: false, //防止多次请求
			modeList: {
				pageIndex: 1,
				pageSize: 8,
				pageCount: 0,
				pageSizesList: [4, 8, 16],
			},
			//批量
			dialogSetCalender: false,
			dateSetRange: '',
			selectedShiftMode: [],
			shiftModeCurr: [],
			pickerOptions: {
				disabledDate(time) {
					return time.getTime() < Date.now();
				},
			},
			calenTableLoading: false,
			//Ctrl设置
			batchCalenList: [],
			dialogUpdateCalender: false,
			//参考
			dialogCopyShift: false,
			selectedProline: '',
			excelFileList: [],
			eidtWorkForm: {
				ftid: '',
				version: '',
				description: '',
				createTime: '',
				classType: '',
				file: '',
				fitProduct: '',
			},
			//芯片
			dialogAddChip: false,
			chipForm: {
				ctid: '',
				chipName: '',
				classCategory: '',
				classType: '',
				description: '',
			},
			//芯片下产品列表
			productList: [],
			NetTypeList: ['4G', 'WIFI'],
			classTypeList: [],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {
		this.getOrgTreeList();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		renderContent(h, { node, data, store }) {
			return (
				<div
					class="custom-tree-node"
					style="width:100%;position:relative"
					on-mouseenter={() => {
						if (data.editFlag === false) data.editFlag = true;
						if (data.addFlag === false) data.addFlag = true;
					}}
					on-mouseleave={() => {
						if (data.editFlag === true) data.editFlag = false;
						if (data.addFlag === true) data.addFlag = false;
					}}
				>
					<img src={folderImg} style="display:inline-block;vertical-align:middle;margin:0 1vw 0 .5vw" />
					<span>{node.label}</span>
					<i
						class="el-icon-edit"
						v-show={data.editFlag}
						style="cursor:pointer;color:#1E9D6F;margin-left:.5vw"
						on-click={e => {
							e.stopPropagation();
							this.updateChip(node);
						}}
					></i>
					<i
						class="el-icon-plus"
						v-show={data.addFlag}
						style="cursor:pointer;color:#1E9D6F;margin-left:.5vw"
						on-click={e => {
							e.stopPropagation();
							this.createChip(node);
						}}
					></i>
				</div>
			);
		},
		getOrgTreeList() {
			this.classTypeList = [];
			function loopTreeList(currLeaf, currItem, typeName) {
				if (currItem && currItem.length > 0) {
					currItem.forEach((row, index) => {
						if (row.chipMenuList && row.chipMenuList.length > 0) {
							currLeaf.push({
								id: typeName + '-' + row.id,
								label: row.typeName,
								type: 'lightType',
								children: [],
								addFlag: false,
							});
							row.chipMenuList.forEach(cItem => {
								currLeaf[index].children.push({
									id: row.typeName + '-' + cItem.ctid,
									label: cItem.chipName,
									type: 'chip',
									nowName: typeName + ' ' + row.typeName + ' ' + cItem.chipName + ' 固件版本',
									description: cItem.description,
									classType: cItem.classType,
									classCategory: cItem.classCategory,
									editFlag: false,
								});
							});
						} else {
							currLeaf.push({
								id: typeName + '-' + row.id,
								label: row.typeName,
								type: 'lightType',
								addFlag: false,
							});
						}
					});
				}
			}
			const str = JSON.stringify({});
			this.firmwareTreeList = [
				{
					id: '1',
					label: '4G',
					children: [],
				},
				{
					id: '2',
					label: 'WIFI',
					children: [],
				},
			];
			this.$axios
				.getClassTypeMenu(str)
				.then(res => {
					if (res.data.success) {
						if (res.data.data) {
							if (res.data.data.fourthGeneration) {
								//4G
								res.data.data.fourthGeneration.forEach(item => {
									this.classTypeList.push({
										id: item.id,
										classTypeName: item.typeName,
									});
								});
								loopTreeList(this.firmwareTreeList[0].children, res.data.data.fourthGeneration, '4G');
							}
							if (res.data.data.wifi) {
								loopTreeList(this.firmwareTreeList[1].children, res.data.data.wifi, 'WIFI');
							}
						}
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('getClassTypeMenu |' + error);
				});
		},
		openChip(node) {
			if (!node) {
				return;
			}
			const id = node.id.split('-')[1],
				name = node.nowName,
				type = node.type;
			if (type == 'chip') {
				this.proFlag = true;
				this.nowProName = name;
				this.ctidCurr = id;
				this.eidtWorkForm.classType = node.classType;
				//查询固件列表
				this.getFirmWareList();
				this.getProList(node.classType);
			} else {
				this.proFlag = false;
				this.ctidCurr = '';
				this.eidtWorkForm.classType = '';
				this.nowProName = '';
			}
		},
		getFirmWareList() {
			const ctid = this.ctidCurr;
			const str = JSON.stringify({
				ctid: ctid,
			});
			this.calenTableLoading = true;
			this.firmWareList = [];
			this.$axios
				.getChipTypeAndFirmwareType(str)
				.then(res => {
					if (res.data.success) {
						this.firmWareList = res.data.data.firmwareTypeList;
						this.calenTableLoading = false;
					} else {
						this.calenTableLoading = false;
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.calenTableLoading = false;

					console.log('getChipTypeAndFirmwareType |' + error);
				});
		},
		updateFirmware(row) {
			if (!row) {
				return;
			} else {
				this.eidtWorkForm.ftid = row.ftid;
				this.eidtWorkForm.version = row.firmwareName;
				this.eidtWorkForm.createTime = row.releaseTime;
				this.eidtWorkForm.description = row.description;
				this.eidtWorkForm.classType = row.classType;
				this.eidtWorkForm.file = row.downloadUrl;
				this.eidtWorkForm.fitProduct = row.productId;
				this.dialogCopyShift = true;
			}
		},
		delFirmware(row, index) {
			let ftid = row.ftid,
				msg;
			if (!row) {
				return;
			}
			msg = '固件【' + row.firmwareName + '】将被删除';
			const str = JSON.stringify({
				ftid: ftid,
			});

			this.$confirm(msg, '删除固件', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteFirmwareType(str)
						.then(res => {
							if (res.data.success) {
								this.$succ('操作成功!');
								this.firmWareList.splice(index, 1);
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteFirmwareType |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		modeListPageIndexChange(val) {
			this.modeList.pageIndex = val;
			this.getFirmWareList();
		},
		modeListPageSizeChange(val) {
			this.modeList.pageSize = val;
			this.getFirmWareList();
		},
		/* 取消固件添加 */
		cancelDiaCopyShift() {
			this.dialogCopyShift = false;
			this.eidtWorkForm.ftid = '';
			this.eidtWorkForm.version = '';
			this.eidtWorkForm.description = '';
			this.eidtWorkForm.createTime = new Date(new Date().toLocaleDateString()).getTime();
			this.eidtWorkForm.classType = '';
			this.eidtWorkForm.file = '';
			this.eidtWorkForm.fitProduct = '';
		},
		saveDiaCopyShift() {
			let str = null,
				currDid = this.ctidCurr;
			if (this.eidtWorkForm.ftid) {
				str = JSON.stringify({
					classType: this.eidtWorkForm.classType,
					ctid: currDid,
					description: this.eidtWorkForm.description,
					downloadUrl: this.eidtWorkForm.file,
					firmwareName: this.eidtWorkForm.version,
					releaseTime: this.eidtWorkForm.createTime ? new Date(this.eidtWorkForm.createTime).getTime() : '',
					ftid: this.eidtWorkForm.ftid,
					productId: this.eidtWorkForm.fitProduct,
				});
				this.$axios
					.updateFirmwareType(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.cancelDiaCopyShift();
							this.getFirmWareList();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('updateFirmwareType |' + error);
					});
			} else {
				str = JSON.stringify({
					classType: this.eidtWorkForm.classType,
					ctid: currDid,
					description: this.eidtWorkForm.description,
					downloadUrl: this.eidtWorkForm.file,
					firmwareName: this.eidtWorkForm.version,
					productId: this.eidtWorkForm.fitProduct,
					releaseTime: this.eidtWorkForm.createTime ? new Date(this.eidtWorkForm.createTime).getTime() : '',
				});
				this.$axios
					.addFirmwareType(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.cancelDiaCopyShift();
							this.getFirmWareList();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('addFirmwareType |' + error);
					});
			}
		},
		uploadFile(item) {
			const self = this;
			// const isIMAGE = item.file.type === 'image/jpeg' || 'image/jpg' || 'image/png';
			const isLt50M = item.file.size / 1024 / 1024 < 50;

			// if (!isIMAGE) {
			//   self.$message.warning('上传文件只能是图片格式!');
			//   return;
			// }
			if (!isLt50M) {
				self.$message.warning('上传固件的大小不能超过 50MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', item.file);
			formData.append('ctid', self.ctidCurr);
			console.log('aaa', formData);
			self.$axios
				.uploadFirmware(formData)
				.then(res => {
					if (res.data.success) {
						self.eidtWorkForm.file = res.data.data;
					} else {
						self.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					self.$message.warning(error.message);
				});
		},
		handleRemove() {
			//文件移除
			this.excelFileList = [];
		},
		uploadFail(err, file, fileList) {
			this.excelFileList = [];
		},
		//添加芯片
		createChip(node) {
			const id = node.data.id.split('-')[1],
				type = node.data.id.split('-')[0];
			this.chipForm.classCategory = type;
			this.chipForm.classType = Number(id);
			this.dialogAddChip = true;
		},
		updateChip(node) {
			const id = node.data.id.split('-')[1];
			// console.log('aaa',node.data)
			this.chipForm.ctid = id;
			this.ctidCurr = id;
			this.chipForm.chipName = node.data.label;
			this.chipForm.classCategory = node.data.classCategory;
			this.chipForm.classType = node.data.classType;
			this.chipForm.description = node.data.description;
			this.dialogAddChip = true;
		},
		saveEditChip() {
			let str;
			if (this.chipForm.ctid) {
				//修改芯片
				str = JSON.stringify({
					ctid: this.chipForm.ctid,
					chipName: this.chipForm.chipName,
					classCategory: this.chipForm.classCategory,
					classType: this.chipForm.classType,
					description: this.chipForm.description,
				});
				this.$axios
					.updateChipType(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.cancelAddChip();
							this.getOrgTreeList();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('updateChipType |' + error);
					});
			} else {
				//添加芯片
				str = JSON.stringify({
					chipName: this.chipForm.chipName,
					classCategory: this.chipForm.classCategory,
					classType: this.chipForm.classType,
					description: this.chipForm.description,
				});
				this.$axios
					.addChipType(str)
					.then(res => {
						if (res.data.success) {
							this.$succ('操作成功!');
							this.cancelAddChip();
							this.getOrgTreeList();
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('addChipType |' + error);
					});
			}
		},
		cancelAddChip() {
			this.dialogAddChip = false;
			this.chipForm.ctid = '';
			this.chipForm.chipName = '';
			this.chipForm.classCategory = '';
			this.chipForm.classType = '';
			this.chipForm.description = '';
		},
		delChip() {
			let ctid = this.chipForm.ctid,
				msg;
			msg = '芯片【' + this.chipForm.chipName + '】将被删除';
			const str = JSON.stringify({
				ctid: ctid,
			});

			this.$confirm(msg, '删除芯片', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteChipType(str)
						.then(res => {
							if (res.data.success) {
								this.$succ('操作成功!');
								this.cancelAddChip();
								this.getOrgTreeList();
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteChipType |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		//日期format
		dateFormat: _.dateFormat,
		getProList(classType) {
			const str = JSON.stringify({
				classType: classType,
			});
			this.productList = [];
			this.$axios
				.getTricolourProductByClassType(str)
				.then(res => {
					if (res.data.success) {
						this.productList = res.data.data;
					} else {
						// this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('getTricolourProductByClassType |' + error);
				});
		},
	},
};
</script>

<style lang="scss" scoped>
#firmwareVersion {
	width: 100%;
	height: 82.5vh;
	// margin:.2vh auto;
	overflow-y: auto;
	box-sizing: border-box;
	border: 1px solid #d7d7d7;
	background: #fff;
	border-radius: 15px;

	.left {
		float: left;
		width: 20%;
		box-sizing: border-box;
		height: 100%;
	}

	.right {
		float: left;
		width: 80%;
		box-sizing: border-box;
		height: 100%;
		border-left: 1px solid #d7d7d7;
		padding: 10px;
	}
}
</style>

<style lang="scss">
#firmwareVersion {
	.right {
		.treeContainer {
			width: 100%;
			margin: 2vh auto;

			// border:1px solid #ccc;
			.el-tree {
				padding: 1vh 0;
			}

			.tree_p {
				font-size: 12px;
				padding: 0;
				margin: 0;
			}

			.treeP_gray {
				color: #999999;
			}

			.el-tree-node__content {
				height: auto !important;
			}
		}
	}

	.left {
		.treeContainer {
			.el-tree-node__content {
				height: 45px !important;
			}
		}
	}
}
</style>
