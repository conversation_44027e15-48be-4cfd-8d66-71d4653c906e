<!-- 搜索历史输入框 -->
<template>
	<div class="search-history-input">
		<!-- 显示在输入框上的字符串默认为占位符 -->
		<transition v-if="showLabel" appear name="label-transition" mode="out-in">
			<span v-show="inputValue" class="search-history-input-label ellipsis">{{ placeholder }}</span>
		</transition>
		<!-- 补全输入框 -->
		<el-autocomplete
			class="search-history-input-autocomplete"
			:popper-class="`search-history-input-popper ${popperKey}`"
			ref="autocomplete"
			v-model.trim="inputValue"
			:size="size"
			:class="[inputClass, width ? `w-${width}` : '', className]"
			:placeholder="placeholder"
			@focus="loadHistory"
			@blur="saveHistory(inputValue)"
			@keyup.enter.native="saveHistory(inputValue)"
			:fetch-suggestions="querySearch"
			clearable
			@select="selectHistory"
			@clear="handleClear"
		>
			<template slot-scope="{ item }">
				<div class="suggestion-item flex-align-center flex-justify-between">
					<!-- 显示的历史记录 -->
					<Tooltips class="color-666 min-w-100 max-w-200 mr10" :cont-str="item" :cont-width="200" />
					<!-- 删除 -->
					<el-button size="mini" type="text" @click.stop="removeHistory(item)" class="el-icon-circle-close color-999">
					</el-button>
				</div>
			</template>
		</el-autocomplete>
	</div>
</template>

<script>
import Cookies from 'js-cookie';

export default {
	props: {
		// v-model
		value: {
			type: String,
			default: '',
		},
		// 历史记录名称（保存到cookie的key）
		name: {
			type: String,
			required: true, // 确保传入 name
		},
		// 冒泡popper的标题
		popperTitle: {
			type: String,
			default: '历史输入',
		},
		// 大小
		size: {
			type: String,
			default: 'small', // 默认大小为 small
		},
		// 输入框样式
		inputClass: {
			type: String,
			default: 'searchBox', // 默认 class 为 searchBox
		},
		// 占位符
		placeholder: {
			type: String,
			default: '请输入搜索内容', // 默认占位符文本
		},
		// 宽度
		width: {
			type: String,
			default: '',
		},
		// 样式
		className: {
			type: String,
			default: '',
		},
		// 是否过滤
		filterable: {
			type: Boolean,
			default: false,
		},
		// 保留数量
		keepCount: {
			type: Number,
			default: 5,
		},
		// 过期时间
		expireDays: {
			type: Number,
			default: 3,
		},
		// 是否显示标签
		showLabel: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			inputValue: '', // 初始化为传入的值
			history: [], // 历史记录
		};
	},
	computed: {
		// popper唯一key
		popperKey() {
			// 获取父组件名称，如果不存在则使用回退值
			const parentName = this.$parent?.$parent?.$options?.name || this.$parent?.$options?.name || 'unknown-parent';

			// 确保name属性存在且有效
			const safeName = this.name || `history-${Date.now()}`;

			// 添加随机字符串以确保唯一性，即使父组件名称和name属性相同
			const uniqueId = Math.random().toString(36).substring(2, 8);

			// 返回格式化的唯一key
			return `${parentName}-${safeName}-${uniqueId}`;
		},
	},
	watch: {
		value(newVal) {
			this.inputValue = newVal;
			this.$emit('input', newVal);
		},
	},
	methods: {
		// 加载历史记录
		loadHistory() {
			const savedHistory = JSON.parse(Cookies.get(`${this.name}SearchHistory`) || '[]');
			// console.log('Loaded history:', savedHistory);
			this.history = Array.isArray(savedHistory) ? savedHistory : []; // 确保是数组
		},
		// 设置冒泡popper的标题
		setTitle() {
			this.$nextTick(() => {
				const popperEl = document.querySelector(`.${this.popperKey}`);
				if (popperEl) {
					popperEl.setAttribute(
						'popperTitle',
						`${this.popperTitle}（保留最近${this.keepCount}条记录，${this.expireDays}天内有效）`,
					);
				}
			});
		},
		// 查询历史记录
		querySearch(queryString, cb) {
			this.$emit('input', queryString);
			this.setTitle();
			// 根据输入的字符串过滤历史记录
			const results = this.filterable ? this.history.filter(item => item.includes(queryString)) : this.history;
			cb(results); // 调用回调函数返回结果
		},
		// 保存历史记录
		saveHistory(value) {
			if (!value) return; // 如果没有值，直接返回

			const savedHistory = new Set(JSON.parse(Cookies.get(`${this.name}SearchHistory`) || '[]'));

			// 如果记录已存在，则移除
			if (savedHistory.has(value)) {
				savedHistory.delete(value);
			}

			// 保留最近的 n 条记录
			const historyArray = [value, ...Array.from(savedHistory)].slice(0, this.keepCount); // 保留最近的 n 条记录
			Cookies.set(`${this.name}SearchHistory`, JSON.stringify(historyArray), { expires: this.expireDays }); // 设置 Cookie 过期时间为 n 天
		},
		// 选择历史记录
		selectHistory(item) {
			this.inputValue = item;
			this.$emit('input', item);
			this.saveHistory(item); // 保存选择的历史记录
		},
		// 清空输入框
		handleClear() {
			this.inputValue = ''; // 清空输入值
			this.$emit('input', ''); // Emit 空值给父组件
			this.$nextTick(() => {
				this.$refs.autocomplete.activated = true;
				this.$refs.autocomplete.focus();
			});
		},
		// 移除历史记录
		removeHistory(item) {
			// 从历史记录中移除指定项
			this.history = this.history.filter(historyItem => historyItem !== item);
			Cookies.set(`${this.name}SearchHistory`, JSON.stringify(this.history), { expires: 7 }); // 更新 Cookie
			this.$nextTick(() => {
				this.$refs.autocomplete.focus();
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.search-history-input {
	position: relative;
	&-label {
		position: absolute;
		top: -3px;
		transform: translateY(-50%);
		left: 16px;
		z-index: 1;
		font-size: 10px;
		color: #999;
	}
	// 显示动画过渡 label-transition
	.label-transition-enter-active,
	.label-transition-leave-active {
		transition: all 0.5s ease-in-out;
		// transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
	}
	.label-transition-enter {
		transform: translateY(10px);
		opacity: 0;
	}
	.label-transition-leave-to {
		transform: translateY(10px);
		opacity: 0;
	}
}
</style>
<style lang="scss">
.search-history-input-popper {
	&::before {
		// content: '历史输入';
		content: attr(popperTitle);
		font-size: 12px;
		color: #999;
		margin: 0 10px;
	}
	.el-autocomplete-suggestion__wrap {
		border-top: 1px solid #f2f2f2;
		max-height: 280px;
		padding: 5px 0;
	}
	li {
		padding: 0px 10px !important;
		line-height: 24px !important;
		font-size: 12px !important;
	}
}
</style>
