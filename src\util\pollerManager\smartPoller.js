/**
 *  斯玛特轮询器
 *  在页面可见时执行轮询任务，页面隐藏或者用户待机时（默认不启用）暂停轮询，支持多种高级配置
 */
import { formatMilliseconds } from '../tool';

class SmartPoller {
	/**
	 * 创建轮询器实例
	 * @param {String} id - 轮询器唯一标识
	 * @param {Object} options - 配置选项
	 * @param {Function} options.callback - 要执行的回调函数，可以返回Promise
	 * @param {Number} options.interval - 轮询间隔时间(毫秒)，默认60000ms(1分钟)
	 * @param {Boolean} options.checkOnWindowFocus - 窗口获得焦点时是否立即执行，默认true
	 * @param {Boolean} options.runImmediately - 创建后是否立即执行一次，默认true
	 * @param {Boolean} options.autoStart - 是否自动开始轮询，默认true
	 * @param {Number} options.maxRetries - 失败时最大重试次数，默认3
	 * @param {Number} options.retryDelay - 重试延迟时间(毫秒)，默认5000ms(5秒)
	 * @param {Boolean} options.skipIfBusy - 如果上一次任务还在执行，是否跳过新任务，默认true
	 * @param {Function} options.onError - 错误处理回调函数
	 * @param {Function} options.onSuccess - 成功处理回调函数
	 * @param {Boolean} options.debug - 是否启用调试日志，默认false
	 * @param {Number} options.idleTimeout - 用户无操作超时时间(毫秒)，超时后暂停轮询，默认null(不启用)
	 */
	constructor(id = '', options = {}) {
		// 轮询器ID
		this.id = id;
		// 基本配置
		this.callback = options.callback || (() => {});
		this.interval = options.interval || 60000;
		// this.checkOnWindowFocus = options.checkOnWindowFocus !== false;
		this.runImmediately = options.runImmediately !== false;
		this.autoStart = options.autoStart !== false;

		// 高级配置
		this.maxRetries = options.maxRetries || 3;
		this.retryDelay = options.retryDelay || 5000;
		this.skipIfBusy = options.skipIfBusy !== false;
		this.onError = options.onError || (error => console.error('SmartPoller error:', error));
		this.onSuccess = options.onSuccess || (() => {});
		this.debug = options.debug || false;
		this.idleTimeout = options.idleTimeout || null;

		// 内部状态
		this.timerId = null;
		this.retryTimerId = null;
		this.lastRunTime = 0;
		this.retryCount = 0;
		this.isRunning = false;
		this.isPaused = false;
		this.lastUserActivity = Date.now();
		this.idleTimerId = null;

		// 绑定方法到实例
		this.start = this.start.bind(this);
		this.stop = this.stop.bind(this);
		this.resume = this.resume.bind(this);
		this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
		// this.handleWindowFocus = this.handleWindowFocus.bind(this);
		this.executeTask = this.executeTask.bind(this);
		this.resetRetryCount = this.resetRetryCount.bind(this);
		this.log = this.log.bind(this);
		this.handleUserActivity = this.handleUserActivity.bind(this);
		this.checkUserIdle = this.checkUserIdle.bind(this);

		// 添加事件监听
		document.addEventListener('visibilitychange', this.handleVisibilityChange);
		// if (this.checkOnWindowFocus) {
		//   window.addEventListener('focus', this.handleWindowFocus);
		// }

		// 用户活动监听
		if (this.idleTimeout) {
			['mousedown', 'keypress', 'mousemove', 'touchstart', 'scroll'].forEach(eventType => {
				document.addEventListener(eventType, this.handleUserActivity, { passive: true });
			});
			this.idleTimerId = setInterval(this.checkUserIdle, Math.min(this.idleTimeout / 2, 30000));
		}

		this.log(`⚙️ 初始化轮询配置:`, options);

		// 自动开始轮询
		if (this.autoStart) {
			this.start();
		}
	}

	/**
	 * 记录调试日志
	 */
	log(...args) {
		if (this.debug) {
			console.log(`[${this.id} ${new Date().toLocaleString()}]`, ...args);
		}
	}

	/**
	 * 执行任务
	 */
	async executeTask() {
		// 如果页面不可见或已暂停，不执行任务
		if (document.visibilityState !== 'visible' || this.isPaused) {
			this.log('⚠️ 页面不可见或已暂停，不执行任务');
			return;
		}

		// 如果设置了skipIfBusy且任务正在执行中，则跳过
		if (this.skipIfBusy && this.isRunning) {
			this.log('⚠️ 上一次任务还在执行中，跳过新任务');
			return;
		}

		this.isRunning = true;
		this.lastRunTime = Date.now();
		this.log('🚀 开始执行任务', { callback: this.callback.name || 'anonymous' });

		try {
			const result = this.callback();
			// 处理Promise结果
			if (result instanceof Promise) {
				await result;
			}

			// 任务成功，重置重试计数
			this.resetRetryCount();
			this.onSuccess();
			this.log('✅ 任务执行成功');
		} catch (error) {
			this.log('⛔ 任务执行失败:', error);
			this.retryCount++;

			// 调用错误处理回调
			this.onError(error);

			// 如果未达到最大重试次数，则进行重试
			if (this.retryCount <= this.maxRetries) {
				this.log(`⚠️ ${this.retryDelay}ms 后重试任务 (重试次数 ${this.retryCount} ，最大重试次数 ${this.maxRetries})`);
				this.retryTimerId = setTimeout(() => {
					this.executeTask();
				}, this.retryDelay);
			} else {
				this.log('⚠️ 达到最大重试次数，放弃重试');
				this.resetRetryCount();
			}
		} finally {
			this.isRunning = false;
		}
	}

	/**
	 * 重置重试计数
	 */
	resetRetryCount() {
		this.retryCount = 0;
		if (this.retryTimerId) {
			clearTimeout(this.retryTimerId);
			this.retryTimerId = null;
		}
	}

	/**
	 * 开始轮询
	 */
	start() {
		// 如果已经有定时器在运行，先停止它
		this.stop();

		// 只在页面可见且未暂停时启动定时器
		if (document.visibilityState === 'visible' && !this.isPaused) {
			const formattedInterval = formatMilliseconds(this.interval, 'auto');
			const formattedIdleTimeout = this.idleTimeout ? formatMilliseconds(this.idleTimeout, 'auto') : '未设置';
			this.log(`🎉 启动轮询器，轮询间隔: ${formattedInterval}，待机时间: ${formattedIdleTimeout}`);

			this.timerId = setInterval(this.executeTask, this.interval);
			this.runImmediately && this.executeTask(); // 如果设置了立即运行，则立即执行一次
		} else {
			this.log('⚠️ 轮询器未启动 - 页面不可见或已暂停');
		}

		return this; // 支持链式调用
	}

	/**
	 * 停止轮询
	 */
	stop(message = '⛔ 轮询停止') {
		if (this.timerId) {
			clearInterval(this.timerId);
			this.timerId = null;
			this.log(message);
		}

		this.resetRetryCount();
		return this; // 支持链式调用
	}

	/**
	 * 暂停轮询
	 */
	pause(message = '🚫 暂停轮询') {
		this.isPaused = true;
		this.stop(message);
		return this; // 支持链式调用
	}

	/**
	 * 恢复轮询
	 */
	resume(message = '♻️ 恢复轮询') {
		this.isPaused = false;
		this.log(message);
		this.start();
		return this; // 支持链式调用
	}

	/**
	 * 更新配置
	 * @param {Object} options - 新的配置选项
	 */
	updateOptions(options = {}) {
		const needRestart = this.timerId !== null && options.interval !== undefined && options.interval !== this.interval;

		// 更新配置
		Object.keys(options).forEach(key => {
			if (this.hasOwnProperty(key)) {
				this[key] = options[key];
			}
		});

		this.log('♻️ 更新配置:', options);

		// 如果需要，重新启动轮询以应用新的间隔时间
		if (needRestart) {
			this.stop();
			this.start();
		}

		return this; // 支持链式调用
	}

	/**
	 * 处理页面可见性变化(页面变为可见/隐藏)
	 */
	handleVisibilityChange() {
		if (document.visibilityState === 'visible' && !this.isPaused) {
			// 页面变为可见时，重新启动轮询
			this.start('👁️‍🗨️ 访问页面，恢复轮询');
		} else if (document.visibilityState !== 'visible') {
			// 页面隐藏时停止轮询
			this.stop('🙈 离开页面，停止轮询');
		}
	}

	/**
	 * 处理窗口获得焦点事件
	 */
	// handleWindowFocus() {
	//   if (this.checkOnWindowFocus && document.visibilityState === 'visible' && !this.isPaused) {
	//     this.log('👁️‍🗨️ 窗口获得焦点，立即执行任务');
	//     this.executeTask();
	//   }
	// }

	/**
	 * 处理用户活动
	 */
	handleUserActivity() {
		this.lastUserActivity = Date.now();

		// 如果因为用户不活跃而暂停了，现在恢复
		if (this.isPaused && this._pausedDueToIdle) {
			this._pausedDueToIdle = false;
			this.resume('♻️ 因为用户不活跃而暂停了，现在恢复轮询');
		}
	}

	/**
	 * 检查用户是否待机
	 */
	checkUserIdle() {
		if (!this.idleTimeout) return;

		const now = Date.now();
		const idleTime = now - this.lastUserActivity;

		if (idleTime >= this.idleTimeout && !this.isPaused) {
			this._pausedDueToIdle = true;
			this.pause();
			// const formattedIdleTime = formatMilliseconds(idleTime, 'auto');
			const formattedIdleTimeout = formatMilliseconds(this.idleTimeout, 'auto');
			this.log(
				`⏰ 因待机超过【${formattedIdleTimeout}】，暂停轮询，最近活跃时间：${new Date(this.lastUserActivity).toLocaleTimeString()}`,
			);
		}
	}

	/**
	 * 销毁实例，清除事件监听和定时器
	 */
	destroy() {
		this.stop();

		// 清除重试定时器
		if (this.retryTimerId) {
			clearTimeout(this.retryTimerId);
			this.retryTimerId = null;
		}

		// 清除待机检查定时器
		if (this.idleTimerId) {
			clearInterval(this.idleTimerId);
			this.idleTimerId = null;
		}

		// 移除事件监听
		document.removeEventListener('visibilitychange', this.handleVisibilityChange);
		// if (this.checkOnWindowFocus) {
		//   window.removeEventListener('focus', this.handleWindowFocus);
		// }

		// 移除用户活动监听
		if (this.idleTimeout) {
			['mousedown', 'keypress', 'mousemove', 'touchstart', 'scroll'].forEach(eventType => {
				document.removeEventListener(eventType, this.handleUserActivity);
			});
		}

		this.log(`🔥 销毁轮询器实例，清除事件监听和定时器`);
		return null; // 帮助垃圾回收
	}
}

export default SmartPoller;
