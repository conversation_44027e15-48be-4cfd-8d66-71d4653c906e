<template>
	<div class="TravelApproval" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<!-- 询盘详情 -->
		<InquiryDetail v-if="showMap.InquiryDetail" ref="InquiryDetail" :inquiryOptions="tableData" @close="queryTableData(1)" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="出差审批" name="TravelApproval">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">申请日期</span>
						<DateSelect
							@change="
								searchForm.applyDateBegin = $event.startTime;
								searchForm.applyDateEnd = $event.endTime;
								queryTableData(1);
							"
						/>
						<!-- 模糊查询 -->
						<el-input
							class="searchBox"
							size="small"
							v-model.trim="searchForm.userName"
							placeholder="姓名"
							@input="queryTableData(1)"
							clearable
						></el-input>

						<el-checkbox v-model="searchForm.onlyNotApproveItems" @change="queryTableData(1)">仅显示未审批记录</el-checkbox>
						<el-checkbox v-model="searchForm.onlyShowSettlementRequire" @change="queryTableData(1)">
							仅显示需要结算的数据
						</el-checkbox>
						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>

							<el-button :disabled="!isBatch" type="text" class="el-icon-s-check" @click="openDialog(selectedData)"
								>批量审批</el-button
							>
							<el-button :disabled="!isBatch" type="text" class="el-icon-s-claim" @click="openSettlement(selectedData)"
								>批量结算</el-button
							>
							<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar"></div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							:row-class-name="getRowColor"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							@selection-change="selectedData = $event"
							selectTrClass="selectTr"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column width="50" type="selection" align="center" :selectable="row => row.approveStatus"></u-table-column>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['tripBeginDate', 'applyDate'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 申请单号 -->
									<Tooltips
										v-else-if="item.colNo == 'applyNo'"
										class="hover-green green"
										:class="['color-999', 'red', '', 'green', 'orange'][scope.row.approveStatus]"
										:cont-str="scope.row[item.colNo]"
										:cont-width="scope.column.width || scope.column.realWidth"
										@click.native="$refs.ApplyDialog.openDialog(scope.row)"
									/>
									<!-- 询盘编号 -->
									<Tooltips
										v-else-if="item.colNo == 'idNumber'"
										class="hover-green green"
										:class="getRowColor(scope)"
										@click.native="openInquiryDetail('修改', scope.row)"
										:cont-str="scope.row[item.colNo] || '未知'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<!-- 出差人 -->
									<Tooltips
										v-else-if="item.colNo == 'tripUsers' && scope.row[item.colNo]"
										:cont-str="Object.values(scope.row[item.colNo]).join(',')"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 出差天数 -->
									<Tooltips
										v-else-if="item.colNo == 'tripDays' && scope.row[item.colNo]"
										:cont-str="scope.row[item.colNo] + '天'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!--  差旅类别-->
									<Tooltips
										v-else-if="item.colNo == 'tripType'"
										:cont-str="tripTypeMap[scope.row[item.colNo]]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 审核状态 0: 未提交，1已提交(待审核)，3 已审核，4，被退回修改 -->
									<Tooltips
										v-else-if="item.colNo == 'approveStatus'"
										class="pointer"
										:class="['color-999', 'red', '', 'green', 'orange'][scope.row[item.colNo]]"
										:cont-str="
											jointString(
												' ',
												['未提交', '已提交(待审核)', '', '已审核', '被退回修改'][scope.row[item.colNo]],
												dateFormat(scope.row.approveDate, 'lineM'),
												scope.row.approveUserName,
												scope.row.approveMemo,
											)
										"
										:cont-width="scope.column.width || scope.column.realWidth"
										@click.native="openDialog([scope.row])"
									/>
									<!-- 结算类型 -->
									<Tooltips
										v-else-if="item.colNo == 'settlementType'"
										:cont-str="settlementTypeMap[scope.row[item.colNo]]"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 结算状态 -->
									<Tooltips
										v-else-if="item.colNo == 'settlementStatus'"
										class="pointer"
										:class="['color-999', 'green'][scope.row[item.colNo]]"
										:cont-str="
											jointString(
												' / ',
												{ 0: '未结算', 1: '已结算' }[scope.row[item.colNo]],
												dateFormat(scope.row.settlementDate, 'lineM'),
											)
										"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										@click.native="
											scope.row.settlementStatus == 0 && scope.row.settlementType !== 0 ? openSettlement([scope.row]) : null
										"
									/>
									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDialogRow(scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template>
              </u-table-column> -->
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="差旅类别" name="TravelType">
				<TravelType ref="TravelType" />
			</el-tab-pane>
		</el-tabs>

		<!-- 审核备注-->
		<el-dialog
			width="600px"
			:visible.sync="dialogApprove"
			:close-on-click-modal="false"
			:append-to-body="true"
			@close="closeDialog"
		>
			<span slot="title">审核信息</span>
			<el-form :model="approveForm" :rules="formRules" label-width="100px" label-position="left" @submit.native.prevent>
				<el-form-item label="审核备注" prop="approveMemo">
					<el-input
						v-model="approveForm.approveMemo"
						placeholder="请输入内容"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>
				<el-form-item label="审核状态" prop="approveStatus">
					<el-radio-group v-model="approveForm.approveStatus" @change="changeApproveStatus">
						<el-radio :label="3">通过</el-radio>
						<el-radio :label="4">驳回</el-radio>
					</el-radio-group>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveApprove">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 差旅单结算 -->
		<el-dialog width="400px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">差旅单结算</span>
			<el-form :model="editForm" :rules="editFormRules" label-width="100px" label-position="left" @submit.native.prevent>
				<el-form-item label="结算日期" prop="settlementDate">
					<el-date-picker
						v-model="editForm.settlementDate"
						type="date"
						placeholder="结算日期"
						format="yyyy-MM-dd"
						value-format="timestamp"
					>
					</el-date-picker>
				</el-form-item>
				<!-- <el-form-item label="备注" prop="allocateMemo">
					<el-input
						v-model="editForm.allocateMemo"
						placeholder="请输入备注"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item> -->
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="openSettlement(editForm.rows, 'save')">确 定</el-button>
			</span>
		</el-dialog>

		<ApplyDialog ref="ApplyDialog" />
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import ApplyDialog from '../components/ApplyDialog.vue';
import TravelType from './TravelType.vue';
import { tripTypeMap, settlementTypeMap } from '@/assets/js/contractSource'; // 结算类型 差旅类别
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		InquiryDetail,
		ApplyDialog,
		TravelType,
	},
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	name: 'TravelApproval', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'TravelApproval', //激活tab页
			tripTypeMap, //差旅类别
			settlementTypeMap, // 结算类型
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '申请单号', colNo: 'applyNo', align: 'left', width: '110' },
				{ colName: '申请日期', colNo: 'applyDate', align: 'left', width: '100' },
				{ colName: '申请人', colNo: 'applyUName', align: 'left', width: '110' },
				{ colName: '出差人', colNo: 'tripUsers', align: 'left', width: '' },
				{ colName: '出差日期', colNo: 'tripBeginDate', align: 'center', width: '100' },
				{ colName: '出差天数', colNo: 'tripDays', align: 'right', width: '80' },
				{ colName: '客户', colNo: 'tripClientName', align: 'left', width: '' },
				{ colName: '目的地', colNo: 'tripDestination', align: 'left', width: '' },
				{ colName: '事由', colNo: 'tripReason', align: 'left', width: '' },
				{ colName: '询盘号', colNo: 'idNumber', align: 'left', width: '110' },
				{ colName: '差旅类别', colNo: 'tripType', align: 'left', width: '80' },
				{ colName: '审批人', colNo: 'approveUserName', align: 'left', width: '80' },
				{ colName: '审批状态', colNo: 'approveStatus', align: 'left', width: '150' },
				{ colName: '结算类型', colNo: 'settlementType', align: 'left', width: '80' },
				{ colName: '结算状态', colNo: 'settlementStatus', align: 'left', width: '80' },
			],

			// 查询表单
			searchForm: {
				applyDateBegin: '',
				applyDateEnd: '',
				onlyNotApproveItems: false,
				onlyShowSettlementRequire: false,
				tripClientName: '',
				userName: '',
				// 其他...
			},

			dialogApprove: false,
			approveForm: {
				approveMemo: '',
				approveStatus: '',
				btaid: [],
			},
			formRules: {
				approveMemo: [{ required: true, message: '请输入审核意见', trigger: 'blur' }],
				approveStatus: [{ required: true, message: '请输入审批状态', trigger: 'blur' }],
			},

			// 弹窗相关
			dialogEdit: false,
			editForm: {
				btridList: [],
				settlementDate: '',
			},
			editFormRules: {
				settlementDate: [{ required: true, message: '请输入结算日期', trigger: 'blur' }],
			},

			openMove: false, //打开组件
			showMap: {
				InquiryDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 是否批量操作
		isBatch() {
			return this.selectedData.length > 0;
		},
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开结算
		async openSettlement(rows, type = 'edit') {
			// 未审核通过的不允许结算
			// if (rows.some(item => item.approveStatus !== 3)) {
			// 	return this.$message.warning('不允许对未审核通过的差旅单进行结算');
			// }
			// if (rows.some(item => item.settlementStatus)) {
			// 	return this.$message.warning('请勿重复结算');
			// }
			if (type == 'edit') {
				this.editForm.rows = rows;
				this.dialogEdit = true;
				return;
			} else if (type == 'save' && _.checkRequired(this.editForm, this.editFormRules)) {
				return; // 验证
			}

			const API = 'updateBusinessTripApplicationBatchSettlement';
			try {
				const res = await this.$axios[API](
					JSON.stringify({
						btaidList: rows.map(item => item.btaid),
						settlementDate: this.editForm.settlementDate,
					}),
				);
				if (res.data.success) {
					this.queryTableData(1);
					this.closeDialog();
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 自动填充审核意见
		changeApproveStatus() {
			const MEMO = { 3: '同意', 4: '拒绝' }[this.approveForm.approveStatus];
			if (this.approveForm.approveMemo == '同意' || this.approveForm.approveMemo == '拒绝') {
				this.approveForm.approveMemo = '';
			}
			this.approveForm.approveMemo = this.approveForm.approveMemo ? this.approveForm.approveMemo : MEMO;
		},
		// 打开询盘详情
		openInquiryDetail(type, row, api) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom(type, row, api);
			});
		},
		// 新增
		openDialog(rows) {
			if (!rows[0].approveStatus) {
				return this.$message.warning('无法对提交未的申请进行审批！');
			}
			this.approveForm.approveMemo = '';
			this.approveForm.approveStatus = '';
			this.approveForm.btaid = rows.map(i => i.btaid);
			this.dialogApprove = true;
		},
		closeDialog() {
			this.dialogEdit = false;
			this.dialogApprove = false;
			this.approveForm = _.resetValues(this.approveForm);
			this.editForm = _.resetValues(this.editForm);
		},
		// 保存审核
		async saveApprove() {
			if (_.checkRequired(this.approveForm, this.formRules)) return; // 如果有空字段，则停止执行后面的逻辑，并输出提示

			const API = 'updateBusinessTripApplicationBatchApprove';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.approveForm }));
				if (res.data.success) {
					this.closeDialog();
					this.$succ(res.data.message);
					this.queryTableData(1);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 表格文本高光显示
		getRowColor({ row }) {
			if (row.approveStatus == 0) {
				return 'color-999'; //未提交
			}
			// 标红处理：未联系上，阶段为空
			return row.approveStatus == 1 ? 'red' : ''; //未联系上 标红显示
		},
		// 切换tab
		changeTab() {
			if (this.activeTab == 'TravelApproval') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectBusinessTripApplicationList'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.searchForm,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//自定义排序(新增/覆盖比较逻辑)
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData =
					order && prop
						? _.sortTableData(this.tableData, prop, order, (a, b) => {
								// 新增/覆盖比较逻辑
								if (prop == 'tripUsers') {
									if (!a) return -1;
									if (!b) return 1;
									if (Object.values(a).length == Object.values(b).length) {
										const aName = Object.values(a)[0];
										const bName = Object.values(b)[0];
										return aName.localeCompare(bName, 'zh-CN');
									} else {
										return Object.values(a).length - Object.values(b).length;
									}
								} else {
									return null; //其他情况必须要返回null
								}
							})
						: this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},

		dateFormat: _.dateFormat, //日期format
		jointString: _.jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.TravelApproval {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
