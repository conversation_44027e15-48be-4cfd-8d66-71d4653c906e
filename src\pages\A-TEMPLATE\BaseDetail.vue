<template>
	<!-- 基础详情（抽屉组件） -->
	<div class="BaseDetail">
		<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
			<div class="detail-wrapper">
				<div class="detail-head">
					<span class="detail-title">{{ titleName }}XX详情</span>
					<div class="flex-align-center">
						<!-- <el-button type="text" clacss="icon-third_save " @clcick="saveDetail"> 保存</el-button> -->
						<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
					</div>
				</div>
				<!-- 明细详情弹窗 -->
				<div class="detail-content">
					<p class="detail-content-title">基本信息 </p>
					<BaseTableForm :detailForm="detailForm" :formList="baseList" @update="detailForm = $event">
						<template #th-xxx></template>
						<!-- 手机号码 -->
						<template #td-phoneNo="{ item }">
							<el-input v-model.trim="detailForm[item.prop]" :placeholder="item.name" clearable />
						</template>
						<!-- 业务顾问 -->
						<template #td-suid="{ item }">
							<el-select v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
								<el-option v-for="i in salesmanList" :key="i.auid" :label="i.userName" :value="i.auid"> </el-option>
							</el-select>
						</template>
						<!-- 状态 -->
						<template #td-status="{ item }">
							<el-select v-model="detailForm[item.prop]" :placeholder="item.name" clearable filterable>
								<el-option label="试用" :value="2"></el-option>
								<el-option label="正式运行" :value="1"></el-option>
							</el-select>
						</template>
					</BaseTableForm>
					<div class="bottom-button">
						<el-button v-if="titleName == '修改'" class="mr20" @click="delDetail">删 除</el-button>
						<el-button @click="saveDetail" type="primary">保 存</el-button>
					</div>

					<p class="detail-content-title">xx清单</p>
					<u-table
						class="detail-table"
						ref="detailTableRef"
						:row-height="45"
						:max-height="600"
						use-virtual
						border
						show-header-overflow="title"
					>
						<u-table-column label="序号" type="index" align="center" width="60"> </u-table-column>
						<u-table-column
							v-for="(item, index) in tableColumn"
							:key="item.colNo + item.colName"
							:label="item.colName"
							:prop="item.colNo"
							:align="item.align"
							:width="item.width"
						>
							<template slot-scope="scope">
								<!-- 物料编码 -->
								<div v-if="item.colNo == 'productNo'">
									<el-autocomplete
										:ref="'cautocomplete' + index"
										v-model="scope.row[item.colNo]"
										placeholder="输入物料编码查找"
										size="mini"
										class="W100 input-green"
										:debounce="500"
										:fetch-suggestions="(queryString, cb) => queryByInput(queryString, cb, scope.row, '物料')"
										@select="handleSelect($event, scope.row, '物料')"
									>
										<template slot-scope="{ item }">
											<Tooltips
												class="max-w-500"
												:cont-str="jointString(' | ', item.materialNo, item.materialName, item.materialSpec, item.unit)"
												:cont-width="480"
											/>
										</template>
									</el-autocomplete>
								</div>

								<!-- 日期 -->
								<div v-else-if="['billTime'].includes(item.colNo)">
									<el-date-picker
										class="w-150"
										v-model="scope.row[item.colNo]"
										type="date"
										placeholder="选择日期"
										size="mini"
										format="yyyy-MM-dd"
										value-format="timestamp"
									>
									</el-date-picker>
								</div>
								<!-- 不允许输入 -->
								<div v-else-if="['productName', 'productSpec', 'unit', 'alreadySendingQty'].includes(item.colNo)">
									<Tooltips :cont-str="scope.row[item.colNo]" :cont-width="(scope.column.width || scope.column.realWidth) - 20" />
								</div>

								<!-- 正常输入 -->
								<div v-else>
									<el-input class="input-green" v-model="scope.row[item.colNo]" size="mini"></el-input>
								</div>
							</template>
						</u-table-column>

						<u-table-column label="" width="40" align="right">
							<template slot-scope="scope">
								<el-button type="text" class="el-icon-delete color-999 fs14" @click="delRow(scope.row, scope.$index)"></el-button>
							</template>
						</u-table-column>
					</u-table>
					<div class="text-left">
						<el-button type="text" class="el-icon-plus" @click="addRow">添加</el-button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import BaseTableForm from '@/components/BaseTableForm';

import { resetValues, checkRequired, deepClone, debounce, dateFormat, jointString } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters } from 'vuex';
export default {
	name: 'BaseDetail',
	components: {
		BaseTableForm,
	},
	props: { warehouseOptions: Array },
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '',

			// 明细
			detailFormCopy: [],
			detailForm: {
				deliveryMaterialTime: '',
				inCargoLocationNo: '',
				inOfWarehouse: '',
				inWid: '',
				lineNumber: '',
				materialBatch: '',
				materialName: '',
				materialNo: '',
				materialSpec: '',
				outCargoLocationNo: '',
				outOfWarehouse: '',
				outWid: '',
				transferInQty: '',
				transferOrderNumber: '',
				transferOutQty: '',
				transferQty: '',
				unit: '',
			},

			// 表单信息
			formList: [
				[
					{ name: '分销/代理', prop: 'twid', class: 'label-required W10', type: 'select' },
					{ name: '团队简称', prop: 'teamName', class: 'label-required W10' },
					{ name: '团队全称', prop: 'teamFullname', class: 'label-required W10', colspan: 2 },
					{ name: '团队管理员', prop: 'userName', class: 'label-required  W10' },
					{ name: '手机号码', prop: 'phoneNo', class: 'label-required  W10' },
					{ name: '业务顾问', prop: 'suid', class: 'label-required  W10', type: 'select' },
					{ name: '实施顾问', prop: 'cuid', class: 'label-required  W10', type: 'select' },
					{ name: 'WEB图标', prop: 'teamImage', class: ' W10' },
				],
				[
					{ name: '使用版本', prop: 'version', class: 'label-required  W10', type: 'select' },
					{ name: '状态', prop: 'status', class: 'label-required  W10', type: 'select' },
					{ name: '启用日期', prop: 'validFrom', class: 'label-required  W10', type: 'date' },
					{ name: '到期日期', prop: 'validTo', class: 'label-required  W10', type: 'date' },
					{ name: '工作中心', prop: 'simLimit', class: 'label-required  W10' },
					{ name: '年费（元）', prop: 'annualFee', class: 'label-required  W10' },
					{ name: '设备标签', prop: 'machineLabel', class: 'W10' },
					{ name: '产品标签', prop: 'productLabel', class: 'W10' },
					{ name: '区域', prop: 'region', class: 'W10' },
				],
			],
			// 表单验证规则
			formRules: {
				transferOrderNumber: [{ required: true, message: '请输入调拨单号！', trigger: 'blur' }],
				materialNo: [{ required: true, message: '请输入物料编码！', trigger: 'blur' }],
				materialName: [{ required: true, message: '请输入物料名称！', trigger: 'blur' }],
				transferQty: [{ required: true, message: '请输入调拨数量！', trigger: 'blur' }],
			},

			// 表格
			tableColumn: [
				{ colName: '销售单号', colNo: 'saleNo', align: 'left', width: '120' },
				{ colName: '项次', colNo: 'lineNumber', align: 'left', width: '80' },
				{ colName: '物料编码', colNo: 'productNo', align: 'left', width: '' },
				{ colName: '物料名称', colNo: 'productName', align: 'left', width: '150' },
				{ colName: '销售单数量', colNo: 'demandQty', align: 'right', width: '' },
				{ colName: '已交数量', colNo: 'alreadySendingQty', align: 'right', width: '' },
				{ colName: '计划出货', colNo: 'sendingQty', align: 'right', width: '' },
				{ colName: '单位', colNo: 'unit', align: 'left', width: '80' },
				{ colName: '交货日期', colNo: 'billTime', align: 'left', width: '' },
				{ colName: '客户机种名', colNo: 'sapName', align: 'left', width: '' },
				{ colName: '客户机种代码', colNo: 'sapNo', align: 'left', width: '' },
				{ colName: '仕向地代码', colNo: 'inventoryPlaceNo', align: 'left', width: '' },
			],
		};
	},
	created() {},
	computed: {},
	watch: {
		showCom(val) {
			if (!val) {
				this.detailForm = resetValues(this.detailForm); //重置对象
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 输入框查询
		async queryByInput(queryStr, cb, row, type) {
			if (!queryStr) {
				this.handleSelect({}, row, type); //清空
			}
			let API, DATA;
			// 请求接口和参数映射
			if (type == '物料') {
				API = 'selectMaterialVO';
				DATA = JSON.stringify({ label: 0, materialNo: queryStr, pageNum: 1, pageSize: 30 });
			} else if (type == '客户') {
				API = 'selectCustomer';
				DATA = JSON.stringify({ query: queryStr, pageNum: 1, pageSize: 30 });
			}

			try {
				const res = await this.$axios[API](DATA);
				if (res.data.success) {
					cb(res.data.data);
				} else {
					this.$message.warning(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 选中选项
		handleSelect(item, row, type) {
			if (type == '物料') {
				row.mtid = item?.mtid || '';
				row.productNo = item?.materialNo || '';
				row.productName = item?.materialName || '';
				row.productSpec = item?.materialSpec || '';
				row.unit = item?.unit || '';
				row.unwarehouseAdministratorIdit = item?.warehouseAdministratorId || '';
			} else if (type == '客户') {
				this.detailForm.customNo = item?.customerNo || '';
				this.detailForm.customerName = item?.customerName || '';
			}
		},

		// 添加/保存信息
		saveDetail: debounce(async function (isClose) {
			if (checkRequired(this.detailForm, this.formRules)) return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			const API = this.titleName == '添加' ? 'addTransferOrder' : 'updateTransferOrder';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.detailForm }));
				if (res.data.success) {
					isClose && (this.showCom = false);
					this.$succ('保存成功！');
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),

		// 删除信息
		delDetail: debounce(async function () {
			this.$confirm('该数据将被删除，删除后数据不可恢复', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(async () => {
					const API = 'deleteTransferOrder';
					try {
						const res = await this.$axios[API](JSON.stringify({ toid: [this.detailForm.toid] }));
						if (res.data.success) {
							this.$succ(res.data.message);
							this.showCom = false;
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		}),

		//获取详情数据
		async queryDetail({ pid }) {
			const API = 'selectProductDetail';
			try {
				const res = await this.$axios[API](JSON.stringify({ pid }));
				if (res.data.success) {
					this.detailForm = res.data.data;
					this.detailFormCopy = deepClone(this.detailForm);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 获取日志
		async queryLog({ pid }) {
			const API = 'selectLog';
			try {
				const res = await this.$axios[API](JSON.stringify({ pid }));
				if (res.data.success) {
					this.logList = res.data.data;
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		//显示弹窗
		showDetailCom(rowData) {
			if (rowData.pid) {
				this.detailForm = { ...this.detailForm, ...deepClone(rowData) };
				this.detailFormCopy = deepClone(this.detailForm);
				// this.queryDetail(rowData); //如果有查询接口
				// this.queryLog(rowData); //如果有日志接口
			} else {
				this.detailFormCopy = deepClone(this.detailForm);
			}
			this.titleName = rowData.pid ? '修改' : '添加';
			this.showCom = true;
		},
		//点击返回
		closeDetailCom() {
			const showConfirm = JSON.stringify(this.detailForm) != JSON.stringify(this.detailFormCopy) && this.showCom;
			if (showConfirm) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.showCom = false;
					})
					.catch(() => {
						return this.$message.info('取消返回！');
					});
			} else {
				this.showCom = false;
			}
		},

		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>
