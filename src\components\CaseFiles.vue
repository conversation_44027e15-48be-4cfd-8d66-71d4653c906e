<template>
	<div id="CaseFiles">
		<el-dialog width="800px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">{{ rowData.type }}附件（{{ rowData.project }}）</span>

			<div class="max-h-500 overflow-y-auto overflow-x-hidden" v-if="rowData.fileVOList.length">
				<p v-for="(file, index) in rowData.fileVOList" :key="index" class="file-card W100 flex-align-center gap-5">
					<i class="fs-24" :class="getFileIcon(file.file)"></i>
					<FilePopover class="inline-block flex-1" trigger="click" :url="file.file" :content="file.fileName" />
					<el-button class="ml-auto el-icon-copy-document" type="text" @click.stop="copyFileUrl(file)">复制</el-button>
					<el-button class="el-icon-download" type="text" @click.stop="downloadFile(file.file)">下载</el-button>
				</p>
			</div>
			<span slot="footer">
				<el-button @click="closeDialog">关闭</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import { copyToClipboard } from '@/util/common';
import { mapGetters } from 'vuex';
import FilePopover from '@/components/FilePopover.vue';
export default {
	name: 'CaseFiles',
	components: { FilePopover },
	data() {
		return {
			rowData: {
				fileVOList: [],
			},
			dialogEdit: false,
		};
	},
	computed: { ...mapGetters(['userInfos']) },
	methods: {
		openDialog(row) {
			this.rowData = row;
			this.dialogEdit = true;
		},
		closeDialog() {
			this.dialogEdit = false;
			this.rowData = {
				fileVOList: [],
			};
		},
		// 判断文档类型
		getFileIcon(file) {
			const fileTypeMapping = {
				'.jpeg': 'JPG',
				'.gif': 'GIF',
				'.jpg': 'JPG',
				'.png': 'PNG',
				'.bmp': 'tupianziliao',
				'.pic': 'tupianziliao',
				'.svg': 'tupianziliao',
				'.docx': 'WORD',
				'.doc': 'WORD',
				'.xls': 'ECEL',
				'.xlsx': 'ECEL',
				'.ppt': 'PPT',
				'.pptx': 'PPT',
				'.pdf': 'PDF',
			};
			const fileExtension = file?.substring(file?.lastIndexOf('.')).toLowerCase();
			return `office-icon-${fileTypeMapping[fileExtension] || 'zonghewendang'}`;
		},
		// 下载文件
		downloadFile(url) {
			window.open(url, '_blank');
		},
		// 复制文件链接
		copyFileUrl(file) {
			copyToClipboard(`${file.fileName}：${file.file}`, '复制成功，文件链接已经复制到粘贴板了！');
			// const fileUrl = file.file; // 获取文件链接
			// fetch(fileUrl)
			// 	.then(response => {
			// 		console.log(response);
			// 		if (!response.ok) throw new Error('网络错误');
			// 		return response.blob(); // 将文件转换为 Blob
			// 	})
			// 	.then(blob => {
			// 		const blobUrl = URL.createObjectURL(blob); // 创建 Blob URL
			// 		const item = new ClipboardItem({ [blob.type]: blob }); // 创建 ClipboardItem
			// 		navigator.clipboard
			// 			.write([item]) // 将文件写入粘贴板
			// 			.then(() => {
			// 				this.$message.success('复制成功，文件已保存到粘贴板！');
			// 			})
			// 			.catch(err => {
			// 				console.error('复制失败:', err);
			// 			});
			// 		// 释放 Blob URL
			// 		URL.revokeObjectURL(blobUrl);
			// 	})
			// 	.catch(err => {
			// 		console.error('获取文件失败:', err);
			// 	});
		},
	},
};
</script>

<style lang="scss" scoped>
#CaseFiles {
	width: 100%;
	height: 100%;
	overflow: hidden;
	position: relative;
}

.file-card {
	margin: 8px 0;
	padding: 5px 10px;
	border: 1px solid #e0e0e0;
	border-radius: 5px;

	&:hover {
		border-color: green;
	}
}
</style>
