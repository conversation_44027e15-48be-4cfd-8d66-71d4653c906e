1
<template>
	<div class="project-detail">
		<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
			<div class="detail-wrapper">
				<div class="detail-head">
					<span class="detail-title">{{ titleName }}项目</span>
					<div>
						<el-button v-show="detailForm.pmid" type="text" class="el-icon-s-data" @click="openWorkPlan(detailForm)">
							任务规划
						</el-button>
						<el-button v-show="detailForm.pmid" type="text" class="el-icon-refresh-right" @click="queryDetailData(detailForm)">
							刷新
						</el-button>
						<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
					</div>
				</div>
				<!-- 明细详情弹窗 -->
				<div class="detail-content">
					<p class="detail-content-title">基本信息 </p>
					<BaseTableForm class="input-border-none" :detailForm="detailForm" :formList="formList" @update="detailForm = $event">
						<!-- <template #th-xxx></template> -->

						<template #td-businessPeople="{ item, formData }">
							<el-select v-model="formData[item.prop]" placeholder="请选择交付项目经理" clearable filterable>
								<el-option v-for="op in implementList" :key="op.auid" :label="op.userName" :value="op.auid"> </el-option>
							</el-select>
						</template>
						<template #td-projectManager="{ item, formData }">
							<el-select v-model="formData[item.prop]" placeholder="请选择项目经理" clearable filterable>
								<el-option v-for="op in userList" :key="op.auid" :label="op.userName" :value="op.auid"> </el-option>
							</el-select>
						</template>
						<template #td-testManager="{ item, formData }">
							<el-select v-model="formData[item.prop]" placeholder="请选择测试经理" clearable filterable>
								<el-option v-for="op in testerList" :key="op.auid" :label="op.userName" :value="op.auid"> </el-option>
							</el-select>
						</template>
						<template #td-technicalManager="{ item, formData }">
							<el-select v-model="formData[item.prop]" placeholder="请选择技术经理" clearable filterable>
								<el-option v-for="op in developerList" :key="op.auid" :label="op.userName" :value="op.auid"> </el-option>
							</el-select>
						</template>
						<template #td-status="{ item, formData }">
							<el-radio-group class="m13" v-model="formData[item.prop]" @change="changeStatus">
								<el-radio :label="0">规划中</el-radio>
								<el-radio :label="1">需求与方案评审通过</el-radio>
								<el-radio :label="2">功能分解完成 </el-radio>
								<el-radio :label="3">开发计划完成</el-radio>
								<el-radio :label="4">开发全部完成</el-radio>
								<el-radio :label="5">测试全部完成</el-radio>
								<el-radio :label="6">功能验收通过</el-radio>
								<el-radio :label="7">发版准备就绪</el-radio>
								<el-radio :label="8">发版完成</el-radio>
								<el-radio disabled :label="9">延误</el-radio>
							</el-radio-group>
						</template>
					</BaseTableForm>
					<div class="bottom-button">
						<el-button v-if="detailForm.pmid" @click.stop="delDetail(detailForm)">删 除</el-button>
						<el-button @click="saveDetail" type="primary">保 存</el-button>
					</div>

					<p class="detail-content-title">项目计划 </p>
					<div class="flex-align-center fs-14 color-666 bolder">
						<div class="w-50 text-center">序号</div>
						<div class="w-200">关键里程碑</div>
						<div class="w-150 text-center">要求完成日期</div>
						<div></div>
					</div>
					<div class="flex-align-center fs-14 color-666" v-for="(item, index) in planTableData" :key="item.time">
						<div class="w-50 text-center"> {{ index + 1 }}</div>
						<div class="w-200">
							<div>{{ item.target }}</div>
							<div class="fs-12 color-999">{{ item.description }}</div>
						</div>
						<el-date-picker
							class="w-150"
							size="small"
							v-model="detailForm[item.time]"
							type="date"
							placeholder="请选择日期"
							format="yyyy-MM-dd"
							value-format="timestamp"
						>
						</el-date-picker>
						<div v-if="detailForm.status == 9">
							<el-button disabled type="danger" class="ml10 p5">延误</el-button>
						</div>
						<div v-else-if="detailForm.status && detailForm[item.time]">
							<el-button
								type="warning"
								class="ml10 p5"
								v-if="nowTime <= detailForm[item.time] && detailForm.status + 1 == item.status"
								@click="detailForm.status = item.status"
								>进行中
							</el-button>
							<el-button disabled type="primary" class="ml10 p5" v-else-if="item.status <= detailForm.status">完工</el-button>
							<el-button disabled type="danger" class="ml10 p5" v-else-if="nowTime > detailForm[item.time]">延误 </el-button>
							<el-button v-else type="info" class="ml10 p5">待开始 </el-button>
						</div>
					</div>

					<div v-show="detailForm.pmid">
						<p class="detail-content-title">项目资料 </p>
						<div class="flex mb10">
							<span>原型图：</span>
							<el-input
								class="w-400"
								v-model="detailForm.axureAddr"
								placeholder="请补充项目原型图链接"
								type="textarea"
								:autosize="{ minRows: 2, maxRows: 4 }"
							></el-input>
						</div>
						<el-upload
							class="w-500"
							ref="upload"
							multiple
							action=""
							accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
							:on-preview="handlePreview"
							:on-remove="handleRemove"
							:file-list="meetFileList"
							:http-request="
								file => {
									uploadFile(file, 2);
								}
							"
						>
							<el-button type="text"> + 添加项目资料</el-button>
						</el-upload>

						<p class="detail-content-title">项目会议 </p>
						<el-upload
							class="w-500"
							ref="upload"
							multiple
							action=""
							accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
							:on-preview="handlePreview"
							:on-remove="handleRemove"
							:file-list="proFileList"
							:http-request="
								file => {
									uploadFile(file, 1);
								}
							"
						>
							<el-button type="text"> + 添加会议资料</el-button>
						</el-upload>

						<p class="detail-content-title">操作日志 </p>
						<div class="detail-log">
							<div class="detail-log-item" v-for="(item, index) in detailForm.projectManagementLogVOList" :key="'oper' + index">
								<span class="mr8">{{ dateFormat(item.operationTime, 'lineM') }} </span>
								<span> {{ item.operationUname }}</span>
								<pre>{{ item.operationContext }}</pre>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<WorkPlan ref="WorkPlan" />
	</div>
</template>
<script>
import { resetValues, deepClone, debounce, checkRequired, dateFormat, jointString } from '@/util/tool';
import { mapGetters } from 'vuex';
import BaseTableForm from '@/components/BaseTableForm';
import WorkPlan from '@/pages/developmentManagement/projectManagement/workPlan/workPlan.vue'; //任务规划
export default {
	name: 'projectDetail',
	props: { warehouseOptions: Array },
	components: { BaseTableForm, WorkPlan },

	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '',
			formList: [
				[
					{ name: '项目名称', prop: 'projectName', class: 'label-required ' },
					{ name: '要求开始日期', prop: 'startTime', class: 'label-required ', type: 'date' },
					{ name: '要求完成日期', prop: 'endTime', class: 'label-required ', type: 'date' },
					{ name: '客户名称', prop: 'customer', class: 'label-required ' },
					{ name: '交付项目经理', prop: 'businessPeople', class: 'label-required ', type: 'select' },
					{ name: '开发项目经理', prop: 'projectManager', class: 'label-required ', type: 'select' },
					{ name: '测试经理', prop: 'testManager', class: 'label-required ', type: 'select' },
					{ name: '技术经理', prop: 'technicalManager', class: 'label-required ', type: 'select' },
				],
				[{ name: '项目状态 ', prop: 'status', class: 'label-required  ', type: 'radio', colspan: 8 }],
			],
			detailFormCopy: [],
			detailForm: {
				//明细详情
				axureAddr: '',
				businessPeople: '',
				checkTime: '',
				conference: '',
				customer: '',
				demandTime: '',
				developmentTime: '',
				endTime: '',
				functionTime: '',
				pageNum: '',
				pageSize: '',
				planTime: '',
				pmid: '',
				prepareTime: '',
				projectManagementConferenceFileVOS: [],
				projectManagementDataFileVOS: [],
				projectManagementLogVOList: [],
				projectManager: '',
				projectName: '',
				projectNo: '',
				publishTime: '',
				queryParam: '',
				startTime: '',
				status: '',
				statusList: [],
				taskComplete: '',
				taskDelay: '',
				taskHours: '',
				taskInExecution: '',
				taskNum: '',
				technicalManager: '',
				testManager: '',
				testTime: '',
			},
			formRules: {
				projectName: [{ required: true, message: '请输入项目名称！', trigger: 'blur' }],
				startTime: [{ required: true, message: '请输入要求开始日期！', trigger: 'blur' }],
				endTime: [{ required: true, message: '请输入要求完成日期！', trigger: 'blur' }],
				customer: [{ required: true, message: '请输入客户简称！', trigger: 'blur' }],
				businessPeople: [{ required: true, message: '请输入交付项目经理！', trigger: 'blur' }],
				projectManager: [{ required: true, message: '请输入开发项目经理！', trigger: 'blur' }],
				testManager: [{ required: true, message: '请输入测试经理！', trigger: 'blur' }],
				technicalManager: [{ required: true, message: '请输入技术经理！', trigger: 'blur' }],
				status: [{ required: true, message: '请输入项目状态！', trigger: 'blur' }],
			},

			// 计划信息
			planTableData: [
				{ target: '需求与方案评审通过', description: '交付项目经理负责，技术经理确认', status: 1, time: 'demandTime' },
				{ target: '功能分解完成', description: '技术经理负责，交付项目经理确认', status: 2, time: 'functionTime' },
				{ target: '开发计划确定', description: '技术经理负责，交付项目经理确认', status: 3, time: 'planTime' },
				{ target: '开发全部完成', description: '开发项目经理负责，技术经理确认', status: 4, time: 'developmentTime' },
				{ target: '测试全部完成', description: '测试经理负责，开发项目经理确认', status: 5, time: 'testTime' },
				{ target: '功能验收通过', description: '交付项目经理负责，测试经理确认', status: 6, time: 'checkTime' },
				{ target: '发版准备就绪', description: '开发项目经理负责，技术经理确认', status: 7, time: 'prepareTime' },
				{ target: '发版完成', description: '技术经理负责，技术经理确认', status: 8, time: 'publishTime' },
			],

			proFileList: [], //项目文件
			meetFileList: [], //会议文件
		};
	},
	created() {},
	computed: {
		...mapGetters(['userList']),
		//开发人员列表(标签：开发)
		developerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('开发')) || [];
		},
		//测试人员列表(标签：测试)
		testerList() {
			return this.userList?.filter(user => user?.userLabel?.includes('测试')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
		//当前时间
		nowTime() {
			return this.$moment().startOf('day').valueOf();
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.detailForm = resetValues(this.detailForm); //重置对象
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		//任务规划弹窗
		openWorkPlan(row) {
			this.$nextTick(() => {
				this.$refs.WorkPlan.showDetailCom(row);
			});
		},
		// 下载文件
		handlePreview(file) {
			window.open(file.url, '_blank');
		},
		// 移除文件
		handleRemove(file) {
			const str = JSON.stringify({ pmfid: file.pmfid });
			this.$axios
				.deleteFile(str)
				.then(res => {
					if (res.data.success) {
						if (file.type === 1) {
							this.proFileList = this.proFileList.filter(item => item.pmfid !== file.pmfid);
						} else if (file.type === 2) {
							this.meetFileList = this.meetFileList.filter(item => item.pmfid !== file.pmfid);
						}
						this.$succ('删除成功!');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('deleteFile |' + error);
				});
		},
		// 上传文件
		uploadFile(file, type) {
			const formData = new FormData();
			formData.append('file', file.file);
			formData.append('type', type);
			formData.append('pmid', this.detailForm.pmid);
			this.$axios
				.batchUploadFile(formData)
				.then(res => {
					if (res.data.success) {
						this.queryDetailData(this.detailForm);
						this.$succ('上传成功!');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('batchUploadFile |' + error);
				});
		},
		// 修改状态热
		changeStatus() {
			if (this.detailForm.status == 8 && (this.detailForm.taskDelay || this.detailForm.taskInExecution)) {
				this.$confirm('该项目存在未完成的任务项，请在关闭项目前确保所有任务完成！', '提示', {
					confirmButtonText: '确定',
					type: 'warning',
				})
					.then(() => {
						this.detailForm.status = 7;
					})
					.catch(() => {
						this.detailForm.status = 7;
					});
			}
		},

		// 添加/保存信息
		saveDetail(isClose = true) {
			if (checkRequired(this.detailForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}

			const API = 'addProject'; //添加不传 pmid 修改传pmid
			this.$axios[API](JSON.stringify({ ...this.detailForm }))
				.then(res => {
					if (res.data.success) {
						isClose && (this.showCom = false);
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},
		// 删除信息
		delDetail({ pmid }) {
			this.$confirm('该数据将被删除，删除后数据不可恢复', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteProject(JSON.stringify({ pmid }))
						.then(res => {
							if (res.data.success) {
								this.$succ('删除成功！');
								this.showCom = false;
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteProject |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		//获取详情数据
		queryDetailData({ pmid }) {
			const str = JSON.stringify({ pmid });
			this.$axios
				.selectProjectDetail(str)
				.then(res => {
					if (res.data.success) {
						const uid_keys = ['businessPeople', 'projectManager', 'testManager', 'technicalManager'];
						uid_keys.forEach(key => {
							res.data.data[key] = res.data.data[key]?.auid || '';
						});

						// 文件数据转换
						const processDataFiles = filesArray => {
							return filesArray.map(item => {
								const fileName = `${this.dateFormat(item.uploadTime, 'lineM')}  ${item.uploadUname}  ${item.fileName}`;
								return {
									name: fileName,
									url: item.uploadAddr,
									pmfid: item.pmfid,
									type: item.type,
								};
							});
						};

						if (res.data.data.projectManagementDataFileVOS) {
							this.proFileList = processDataFiles(res.data.data.projectManagementDataFileVOS);
						}

						if (res.data.data.projectManagementConferenceFileVOS) {
							this.meetFileList = processDataFiles(res.data.data.projectManagementConferenceFileVOS);
						}

						this.detailForm = res.data.data;
						this.detailFormCopy = deepClone(this.detailForm);
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectProjectDetail |' + error);
				});
		},
		// 获取日志
		queryLogData({ pmid }) {
			const str = JSON.stringify({ pmid });
			this.$axios
				.selectLog(str)
				.then(res => {
					if (res.data.success) {
						this.logList = res.data.data;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectLog |' + error);
				});
		},
		// 获取表单数据
		getDetailForm(rowData, isOpen = true) {
			this.detailForm = { ...this.detailForm, ...rowData };
			isOpen && this.showDetailCom(this.detailForm);
		},
		//显示弹窗
		showDetailCom(rowData) {
			console.log('rowData', rowData);

			if (rowData && rowData.pmid) {
				this.queryDetailData(rowData);
			} else {
				this.detailForm.status = this.detailForm.status || 0; // 未开始
				this.detailFormCopy = deepClone(this.detailForm);
			}
			this.titleName = rowData?.pmid ? '修改' : '添加';
			this.showCom = true;
		},

		//点击返回
		closeDetailCom() {
			let isSameData = true;
			isSameData = JSON.stringify(this.detailForm) == JSON.stringify(this.detailFormCopy);
			console.log('isSameData', isSameData);
			if (!isSameData) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.showCom = false;
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.showCom = false;
			}
		},
		//日期format
		dateFormat: dateFormat,
		jointString: jointString,
	},
};
</script>
