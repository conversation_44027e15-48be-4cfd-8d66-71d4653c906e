<template>
	<!-- 基础弹窗（弹窗组件） -->
	<div id="BaseDialog">
		<el-dialog width="600px" :visible.sync="isShow" :close-on-click-modal="false" :append-to-body="true" @close="close">
			<!-- 标题 -->
			<span slot="title">{{ editForm.id ? '编辑' : '新增' }}基础弹窗</span>
			<!-- 表单 -->
			<el-form
				ref="editFormRef"
				:model="editForm"
				:rules="formRules"
				label-width="100px"
				label-position="left"
				@submit.native.prevent
			>
				<el-form-item label="用户" prop="auid">
					<el-select v-model="editForm.auid" placeholder="用户" class="W100" popper-class="select-column-3" clearable filterable>
						<el-option v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid">
							<span :title="item.userName">
								{{ item.userName }}
							</span>
						</el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="信息" prop="info">
					<el-input v-model="editForm.info" placeholder="请输入信息" clearable></el-input>
				</el-form-item>

				<el-form-item label="备注" prop="remark">
					<el-input
						v-model="editForm.remark"
						placeholder="请输入备注"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>
			</el-form>
			<!-- 底部按钮 -->
			<span slot="footer">
				<el-button @click="close">取 消</el-button>
				<el-button type="primary" @click="save">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { checkRequired, resetValues } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'BaseDialog',
	components: {},
	props: {
		// 用户列表
		userList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			isShow: false,
			editForm: {
				auid: '',
				info: '',
				remark: '',
			},
			formRules: {
				auid: [{ required: true, message: '请选择人员', trigger: 'blur' }],
				info: [{ required: true, message: '请输入信息', trigger: 'blur' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: { ...mapGetters(['userInfos']) },
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 打开
		open(data = {}) {
			this.editForm = { ...this.editForm, ...data };
			this.isShow = true;
		},
		// 关闭
		close() {
			this.isShow = false;
			this.editForm = resetValues(this.editForm);
			this.$nextTick(() => {
				this.$refs.editFormRef?.resetFields();
				this.$refs.editFormRef?.clearValidate();
			});
		},
		// 保存
		async save() {
			if (checkRequired(this.editForm, this.formRules)) return;
			const API = 'savexxxx';
			try {
				const res = await this.$axios[API](JSON.stringify(this.editForm));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.$emit('update');
					this.isShow = false;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
	},
};
</script>
