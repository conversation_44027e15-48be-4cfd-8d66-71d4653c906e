<template>
	<div class="HealthDegreeOfBP">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- BP健康度详情 -->
		<HealthDegreeOfBPList ref="HealthDegreeOfBPList" @close="queryTableData(1)" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="BP健康度监测表" name="HealthDegreeOfBP">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<span class="search-label">期间</span>
						<DateSelect
							defaultDate="本月"
							:dateList="['今天', '昨天', '本周', '本月', '上月', '本年']"
							:clearable="false"
							@change="
								searchForm.startTime = $event.startTime;
								searchForm.endTime = $event.endTime;
								queryTableData(1);
							"
						/>
						<!-- <span class="search-label">部门</span> -->
						<!-- <el-select v-model="searchForm.adid" size="small" placeholder="部门" clearable @change="queryTableData(1)">
							<el-option v-for="item in departmentList" :key="item.adid" :label="item.departmentName" :value="item.adid" />
						</el-select> -->

						<SearchHistoryInput
							name="departmentName"
							placeholder="部门"
							v-model.trim="searchForm.departmentName"
							@input="queryTableData(1)"
						/>
						<SearchHistoryInput name="bpName" placeholder="BP" v-model.trim="searchForm.bpname" @input="queryTableData(1)" />

						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<ExportBtn @trigger="openExport" />
							<!-- <el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button> -->
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<!-- <div class="table-toolbar"></div> -->
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							:pagination-show="false"
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center" fixed></u-table-column>
							<u-table-column
								v-for="(item, index) in tableColumn"
								:key="item.colNo + index"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								:fixed="item.fixed"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期（默认不显示分秒 lineM ） -->
									<Tooltips
										v-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 健康度 -->
									<Tooltips
										v-else-if="item.colNo == 'healthDegree'"
										:class="getHealthDegreeClass(scope.row[item.colNo])"
										:cont-str="scope.row[item.colNo]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 点评达成率 *100 再显示百分比-->
									<Tooltips
										v-else-if="item.colNo == 'requiredAchievementRate' && scope.row[item.colNo]"
										:cont-str="bigMul(scope.row[item.colNo], 100, 0) + '%'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- xx率 默认显示百分比-->
									<Tooltips
										v-else-if="item.colName.includes('率') && scope.row[item.colNo]"
										:cont-str="scope.row[item.colNo] + '%'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 电话平均时长 -->
									<Tooltips
										v-else-if="item.colNo == 'averageCallDuration' && scope.row[item.colNo]"
										:cont-str="timeFormat(scope.row[item.colNo], 's', '分秒')"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 总积分/系统扣分 -->
									<Tooltips
										v-else-if="['totalScore', 'scoreDeductionSystem'].includes(item.colNo) && scope.row[item.colNo]"
										:cont-str="scope.row[item.colNo].toFixed(2)"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 可查看清单的列 -->
									<Tooltips
										v-else-if="VOS_Map[item.colNo] && scope.row[item.colNo]"
										class="hover-green"
										:cont-str="scope.row[item.colNo] || '-'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
										@click.native="openList(scope.row, VOS_Map[item.colNo])"
									/>

									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] || '-'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDetail('编辑',scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template>
              </u-table-column> -->
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="BPUL健康度监测表" name="HealthDegreeOfBPUL">
				<HealthDegreeOfBPUL ref="HealthDegreeOfBPUL" />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData, timeFormat } from '@/util/tool'; //按需引入常用工具函数
import { bigMul } from '@/util/math';

import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
// import DetailCom from './components/baseDetail.vue'; //明细组件
// import btnAuth from '@/mixins/btnAuth';
import ExportTable from '@/components/ExportTable'; //导出组件
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
import HealthDegreeOfBPList from './HealthDegreeOfBPList.vue';
import HealthDegreeOfBPUL from './HealthDegreeOfBPUL.vue';
export default {
	name: 'HealthDegreeOfBP', //组件名应同路由名(否则keep-alive不生效)
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		// DetailCom,
		ExportTable,
		HealthDegreeOfBPList,
		ExportBtn,
		HealthDegreeOfBPUL,
	},
	// mixins: [btnAuth],
	data() {
		return {
			activeTab: 'HealthDegreeOfBP', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			// 可查看清单的列 (新询盘、咨询数、已点评数、未点评数、合格数、不合格数)
			VOS_Map: {
				newInquiryCount: 'newInquiryDetailVOS',
				consultCount: 'consultDetailVOS',
				commentCount: 'commentDetailVOS',
				unCommentCount: 'unCommentDetailVOS',
				qualifiedCount: 'qualifiedDetailVOS',
				unQualifiedCount: 'unQualifiedDetailVOS',
			},
			// 选择器数据 - 车间/部门/类型等...
			scoreConfigList: [], // 积分配置列表
			departmentList: [], // 部门列表
			// 查询表单
			searchForm: {
				adid: '',
				auid: '',
				departmentName: '',
				bpname: '',
				// 其他...
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 动态获取业务积分列
		DynamicColums() {
			// console.log(this.scoreConfigList);
			if (!this.scoreConfigList?.length) return [];
			return this.scoreConfigList.map(item => {
				return {
					colName: item.cfgName,
					colNo: item.aupcid,
					align: 'right',
					width: '95',
				};
			});
		},
		tableColumn() {
			return [
				{ colName: '部门', colNo: 'departmentName', align: 'left', width: '120', fixed: 'left' },
				{ colName: 'BPUL', colNo: 'bpulname', align: 'left', width: '120', fixed: 'left' },
				{ colName: 'BP', colNo: 'bpname', align: 'left', width: '120', fixed: 'left' },
				{ colName: '新询盘数', colNo: 'newInquiryCount', align: 'right', width: '' },
				{ colName: '咨询数', colNo: 'consultCount', align: 'right', width: '' },
				{ colName: '咨询率', colNo: 'consultRate', align: 'right', width: '' },
				{ colName: '电话平均时长', colNo: 'averageCallDuration', align: 'right', width: '100' },
				{ colName: '需点评率', colNo: 'requiredReviewRate', align: 'right', width: '100' },
				{ colName: '需点评数', colNo: 'requiredReviewCount', align: 'right', width: '100' },
				{ colName: '已点评数', colNo: 'commentCount', align: 'right', width: '' },
				{ colName: '未点评数', colNo: 'unCommentCount', align: 'right', width: '' },
				{ colName: '点评率', colNo: 'commentRate', align: 'right', width: '' },
				{ colName: '点评达成率', colNo: 'requiredAchievementRate', align: 'right', width: '' },
				{ colName: '合格数', colNo: 'qualifiedCount', align: 'right', width: '' },
				{ colName: '不合格数', colNo: 'unQualifiedCount', align: 'right', width: '' },
				{ colName: '合格率', colNo: 'qualifiedRate', align: 'right', width: '' },

				// 这里插入动态列
				...this.DynamicColums,
				// 这里插入动态列

				{ colName: '培训积分', colNo: 'scoreLearning', align: 'right', width: '' },
				{ colName: '系统自动释放公海数', colNo: 'inquiryToOpenSeaCount', align: 'right', width: '' },
				{ colName: '系统扣分', colNo: 'scoreDeductionSystem', align: 'right', width: '' },
				{ colName: '手工扣分', colNo: 'scoreDeductionManual', align: 'right', width: '' },
				// { colName: '手工加分', colNo: 'scoreAddManual', align: 'right', width: '' },
				{ colName: '总积分', colNo: 'totalScore', align: 'right', width: '100', fixed: 'right' },
				{ colName: '健康度', colNo: 'healthDegree', align: 'left', width: '120', fixed: 'right' },
				// { colName: '不健康原因', colNo: 'unHealthReason', align: 'left', width: '120' },
			];
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryDepartmentList();
		this.queryScoreConfig();
		this.queryTableData();
	},
	activated() {
		// this.queryTableData(1);
		this.changeTab();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开清单
		async openList(row, VOS_Key) {
			this.$refs.HealthDegreeOfBPList.showDetailCom({
				...row,
				endTime: this.searchForm.endTime,
				startTime: this.searchForm.startTime,
				VOS_Key,
			});
		},
		// 获取健康度颜色
		getHealthDegreeClass(healthDegree) {
			// 根据字段返回的字符串判断 如存在优秀则返回绿色，合格则返回蓝色，预警返回红色 其他不变
			if (healthDegree.includes('优秀')) return 'green';
			if (healthDegree.includes('合格')) return 'blue';
			if (healthDegree.includes('预警')) return 'red';
			return ''; // 默认返回红色
		},
		// 查询部门列表
		async queryDepartmentList() {
			const API = 'selectAdminDepartmentList';
			const res = await this.$axios[API](JSON.stringify({}));
			if (res.data.success) {
				this.departmentList = res.data.data.filter(item => item.organization == '树字工厂') || [];
			} else {
				this.$err(res.data.message);
			}
		},
		// 查询积分配置项（动态列）
		async queryScoreConfig() {
			const API = 'selectAdminUserPointsConfigurationList';
			try {
				const res = await this.$axios[API](JSON.stringify({ cfgGroup: '业务积分项' }));
				if (res.data.success) {
					this.scoreConfigList = res.data.data || [];
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 切换tab
		changeTab() {
			if (this.activeTab == 'HealthDegreeOfBP') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 查询表格数据
		queryTableData: debounce(async function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectHealthDegreeOfBP';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.searchForm }));
				if (res.data.success) {
					this.tableData = res.data.data.map(item => {
						const newItem = { ...item, ...item.pointsConfigurationDynamicColumnMap };
						return newItem;
					});
					this.tablePageForm.total = res.data.totalItems;
					type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					this.sortChange(this.tableSort, true);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		//自定义排序(新增/覆盖比较逻辑)
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData =
					order && prop
						? sortTableData(this.tableData, prop, order, (a, b) => {
								// 新增/覆盖比较逻辑
								if (prop == 'healthDegree') {
									// 获取优先级
									const getPriority = str => {
										if (str.includes('优秀')) return 3;
										if (str.includes('合格')) return 2;
										if (str.includes('预警')) return 1;
										return 0; // 默认优先级
									};
									const aPriority = getPriority(a);
									const bPriority = getPriority(b);
									return aPriority - bPriority; // 根据优先级排序
								} else {
									return null; //其他情况必须要返回null
								}
							})
						: this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({ ...this.searchForm }), //接口参数
				API: 'exportHealthDegreeOfBP', //导出接口
				downloadData: 'BP健康度统计表导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
		dateFormat, //日期format
		jointString, //拼接字符串
		timeFormat, //时间格式化
		bigMul, // 大数乘法
	},
};
</script>

<style lang="scss" scoped>
.HealthDegreeOfBP {
	width: 100%;
	overflow: hidden;
	position: relative;
	.table-main {
		height: calc(100vh - 260px) !important;
	}
}
</style>
