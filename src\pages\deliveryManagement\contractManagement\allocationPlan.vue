<template>
	<div class="allocationPlan" :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}</span>
				<el-button type="text" class="el-icon-arrow-left" @click="showCom = false">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content">
				<div class="flex">
					<!-- 左边  -->
					<div class="W50 H100 flex-column">
						<div :class="shouldBP1220 ? 'H70 flex-column' : 'H100 flex-column'">
							<!-- 合同文件  -->
							<!-- <p class="detail-content-title">合同文件 </p> -->
							<FilePreview
								:class="shouldBP1220 ? 'min-h-500' : 'min-h-666'"
								ref="FilePreview"
								:url="detailForm?.contractUrl"
								:content="detailForm?.contractName"
							/>
						</div>
						<div v-if="shouldBP1220" class="H30 flex-column">
							<!-- <p class="detail-content-title">BP合同结算分配试算表 </p> -->
							<BPCommission class="flex-1 H100 min-h-300 mt15" :detailForm="detailForm" />
						</div>
					</div>

					<!-- 业绩分配 -->
					<div class="W50 flex-column border-left pl10 ml10 pb60">
						<!-- 审核和查看合同 -->
						<div v-if="titleName == '合同审核' || titleName == '查看合同'">
							<p class="detail-content-title">
								<span>合同摘要 </span>
								<span v-if="detailForm?.approveStatus !== 1 && titleName == '合同审核'">
									<el-button
										type="danger"
										size="small"
										class="ml20"
										@click="
											approveForm.approveStatus = 2;
											dialogApprove = true;
										"
									>
										审核不通过
									</el-button>
									<el-button
										type="primary"
										size="small"
										class="ml20"
										@click="
											approveForm.approveStatus = 1;
											saveApprove();
										"
										>审核通过</el-button
									>
								</span>
								<span
									v-if="detailForm?.approveStatus !== 0"
									class="ml20 fs-12"
									:class="{ 0: 'color-999', 1: 'green', 2: 'red' }[detailForm.approveStatus]"
								>
									<span> {{ { 0: '未审核', 1: '已通过审核', 2: '已被驳回' }[detailForm.approveStatus] }} </span>
									<span class="ml20"> 审核时间：{{ dateFormat(detailForm.approveDate, 'lineM') }} </span>
									<span v-if="detailForm.approveMemo" class="ml20"> 审核备注： {{ detailForm.approveMemo }} </span>
								</span>
							</p>

							<div>
								<span>业务员：</span><span class="red">{{ detailForm?.salesmanName }}</span>
							</div>
							<div>
								<span>询盘号：</span><span class="">{{ detailForm?.number || '未知' }}</span> ；<span>客户：</span
								><span class="">{{ detailForm?.registeredBusinessName }}</span> ；<span>合同类型：</span
								><span class="red">{{ { 0: '直销', 1: '合伙' }[detailForm.contractType] }}</span> ；<span>项目类型：</span
								><span class="red">{{ projectTypeMap[detailForm?.projectType] }}</span>
							</div>
							<div>
								<span>辅导天数：</span><span class="red">{{ detailForm?.tutoringDays }}</span> ； <span>差旅费承担方：</span
								><span class="red">{{ expensePartyMap[detailForm?.travelExpensesParty] }}</span>
							</div>
							<div>
								<span>人天单价</span><span class="green">{{ detailForm?.customizedServiceDevelopDaysFile }}</span> ；
							</div>
						</div>

						<p class="detail-content-title">
							<span class="flex-align-center">
								<span>业绩分配 </span>
								<span v-if="detailForm.number" class="ml20 green">询盘号：{{ detailForm?.number }} </span>
								<span v-if="detailForm.dealAmount" class="ml20 green">合同金额：￥{{ detailForm?.dealAmount }} 万</span>
								<!-- <el-button type="primary" size="small" class="ml20" @click="queryDetailData(detailForm)">重新计算</el-button> -->
							</span>
						</p>
						<!-- overflow-auto -->
						<div class="flex-1 table-wrapper">
							<el-collapse v-model="activeName">
								<el-collapse-item v-for="(item, index) in detailForm?.deliverStageManagementVOS" :key="item.dmsid" :name="index">
									<template slot="title">
										<div class="flex-align-center max-w-500">
											<Tooltips :cont-str="`第${index + 1}阶段：${item.stage} - ${item.standard}`" :cont-width="480" />
											<!-- 已回款 -->
											<i v-show="item.collectionAmount" class="el-icon-circle-check ml10 green"> </i>
										</div>
									</template>
									<div class="pl10 pr15">
										<div>
											<span>合同金额：{{ item.amount || 0 }}</span>
											<span v-show="detailForm?.contractType == 0" class="ml10">居间费：{{ item.mediAmount || 0 }}</span>
											<span v-show="detailForm?.contractType == 1" class="ml10"
												>标品数量：{{ item.standardProductCount || 0 }}</span
											>
											<span v-show="detailForm?.contractType == 1" class="ml10"
												>定制金额：{{ item.customizedServiceAmount || 0 }}</span
											>
										</div>
										<u-table
											class="table-main detail-table W100 input-border-none"
											size="mini"
											ref="refTable"
											row-key="dscdid"
											max-height="500px"
											:row-height="45"
											:data="item?.allotList || []"
											:row-style="{ height: '0' }"
											:cell-style="{ padding: '0 0', borderBottom: '1px solid #e9e9e9' }"
											:header-cell-style="{ border: 'transparent', padding: '5px 0 !important' }"
											:show-summary="detailForm?.contractType === 0"
											:summary-method="summaryMethod"
										>
											<u-table-column
												v-for="col in tableColumn"
												:key="col.colNo"
												:label="col.colName"
												:prop="col.colNo"
												:align="col.align"
												:width="col.width"
											>
												<template slot-scope="scope">
													<!-- 补偿金额 -->
													<el-input
														v-if="col.colNo == 'allocateCompensationAmount'"
														:disabled="titleName !== '业绩分配' || !!item.collectionAmount"
														class="input-red text-right"
														v-model="scope.row[col.colNo]"
														size="mini"
														@change="updateRow(scope.row)"
													>
													</el-input>

													<!-- 备注 -->
													<el-input
														v-else-if="col.colNo == 'allocateMemo'"
														:disabled="titleName !== '业绩分配' || !!item.collectionAmount"
														v-model="scope.row[col.colNo]"
														class="input-red"
														size="mini"
														clearable
														@change="updateRow(scope.row)"
													>
													</el-input>

													<Tooltips
														v-else-if="col.colNo == 'commissionType'"
														:cont-str="commissionTypeMap[scope.row[col.colNo]]"
														:cont-width="(scope.column.width || scope.column.realWidth) - 20"
													/>

													<!-- <Tooltips
													v-else-if="col.colNo == 'allocateSummary'"
													:cont-str="scope.row[col.colNo] ? scope.row[col.colNo] + '%' : ''"
													:cont-width="(scope.column.width || scope.column.realWidth) - 20"
												/> -->

													<Tooltips
														v-else
														:cont-str="scope.row[col.colNo] ? scope.row[col.colNo] + '' : ''"
														:cont-width="(scope.column.width || scope.column.realWidth) - 20"
													/>
												</template>
											</u-table-column>
											<u-table-column label="" width="55" align="center">
												<template slot-scope="scope">
													<el-button
														v-show="scope.row.commissionType == 127 && !item.collectionAmount"
														type="text"
														class="el-icon-delete"
														@click="deleteRow(scope.row)"
													></el-button>
												</template>
											</u-table-column>
										</u-table>

										<div v-if="titleName == '业绩分配' && !item.collectionAmount" class="flex-align-center">
											<el-button type="text" size="small" class="iconClass" @click="openDialog(item, '分配')"
												>+添加一行
											</el-button>
											<!-- <el-button type="primary" size="mini" class="ml-auto" @click="queryDetailData(detailForm)">保存</el-button> -->
										</div>
									</div>
								</el-collapse-item>
							</el-collapse>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 新增业绩分配项 -->
		<el-dialog width="600px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">{{ editForm.stage }} 新增业绩分配项</span>
			<el-form :model="editForm" :rules="formRules" label-width="100px" label-position="left" @submit.native.prevent>
				<el-form-item label="分配对象" prop="commissionObjectId">
					<el-select v-model="editForm.commissionObjectId" placeholder="分配对象" class="W100" clearable filterable>
						<el-option v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="补偿" prop="allocateCompensationAmount">
					<el-input v-model="editForm.allocateCompensationAmount" placeholder="请输入金额" clearable></el-input>
				</el-form-item>
				<el-form-item label="备注" prop="allocateMemo">
					<el-input v-model="editForm.allocateMemo" placeholder="请输入内容" clearable></el-input>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEdit">确 定</el-button>
			</span>
		</el-dialog>

		<!-- 审核备注-->
		<el-dialog
			width="600px"
			:visible.sync="dialogApprove"
			:close-on-click-modal="false"
			:append-to-body="true"
			@close="closeDialog"
		>
			<span slot="title">审核信息</span>
			<el-form :model="approveForm" label-width="100px" label-position="left" @submit.native.prevent>
				<el-form-item label="审核备注" prop="approveMemo">
					<el-input
						v-model="approveForm.approveMemo"
						placeholder="请输入内容"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
					></el-input>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveApprove">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { debounce, dateFormat, jointString, deepClone, resetValues, checkRequired } from '@/util/tool';
import { bigAdd } from '@/util/math';
import { mapGetters } from 'vuex';
import FilePreview from '@/components/FilePreview.vue';
import BPCommission from './BPCommission.vue';
import { projectTypeOptions, projectTypeMap, commissionTypeMap, expensePartyMap } from '@/assets/js/contractSource';

export default {
	name: 'allocationPlan',
	components: {
		FilePreview,
		BPCommission,
	},
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '',
			activeName: [0, 1, 2, 4, 5, 6, 7, 8, 9, 10], //默认打开的折叠面板
			detailFormCopy: [],
			detailForm: {
				//明细详情
				dmid: '',
				dmsid: '',
				deliverStageManagementVOS: [], //项目阶段
			},

			tableColumn: [
				{ colName: '类别', colNo: 'commissionType', align: 'center', width: '60' },
				{ colName: '分配对象', colNo: 'commissionObjectName', align: 'left', width: '100' },
				{ colName: '', colNo: 'allocateSummary', align: 'right', width: '160' },
				{ colName: '系统分配', colNo: 'allocateSystemAmount', align: 'right', width: '90' },
				{ colName: '补偿', colNo: 'allocateCompensationAmount', align: 'right', width: '100' },
				{ colName: '金额', colNo: 'allocateAmount', align: 'right', width: '90' },
				{ colName: '备注', colNo: 'allocateMemo', align: 'left', width: '' },
			],
			userList: [],
			dialogEdit: false,
			editForm: {
				allocateAmount: '',
				allocateCompensationAmount: '',
				allocateMemo: '',
				allocateSource: '',
				allocateSummary: '',
				allocateSystemAmount: '',
				commissionObjectId: '',
				commissionObjectName: '',
				commissionObjectType: '',
				commissionType: '',
				dmid: '',
				dmsid: '',
				dscdid: '',
				stageMediAmount: '',
			},
			dialogApprove: false,
			approveForm: {
				approveMemo: '',
				approveStatus: '',
				dmid: '',
			},
			formRules: {
				commissionObjectId: [{ required: true, message: '请输入分配对象', trigger: 'blur' }],
				allocateCompensationAmount: [{ required: true, message: '请输入补偿', trigger: 'blur' }],
			},
			projectTypeMap, // 项目类型
			commissionTypeMap, // 提成类别
			expensePartyMap,
		};
	},
	created() {},
	computed: {
		// 合伙合同是否应该走 BP1220 分成保底制 结算方案
		shouldBP1220() {
			return (
				this.detailForm.commissionContext && this.detailForm.contractType == 1 && this.detailForm.businessPartnerShareRatioMode
			);
		},
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.$emit('close', this.detailForm.dmid);
				this.detailForm = resetValues(this.detailForm); //重置对象
				this.editForm = resetValues(this.editForm); //重置对象
				this.approveForm = resetValues(this.approveForm); //重置对象
			}
		},
	},
	mounted() {},
	methods: {
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					if (column.property == 'allocateAmount') return Number(item[column.property]) || 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? bigAdd(prev, curr, 2) : prev;
				}, 0); //合计计算
				means[columnIndex] = sum > 0 ? <span>{sum}</span> : '';
			}
			return [means];
		},
		// 新增
		openDialog(item) {
			this.editForm = deepClone(item);
			this.dialogEdit = true;
		},
		closeDialog() {
			this.dialogApprove = false;
			this.dialogEdit = false;
			this.editForm = resetValues(this.editForm);
			this.approveForm = resetValues(this.approveForm);
		},
		// 保存审核
		async saveApprove() {
			const API = 'deliverManagementApprove';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.approveForm, dmid: this.detailForm.dmid }));
				if (res.data.success) {
					this.closeDialog();
					this.$succ(res.data.message);
					this.showCom = false;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},

		// 新增业绩分配项
		async saveEdit() {
			if (checkRequired(this.editForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}

			this.editForm.commissionObjectName = this.userList.find(item => item.auid == this.editForm.commissionObjectId).userName;
			this.editForm.commissionType = 127; //提成类别 127 其他
			this.editForm.commissionObjectType = 10; //分配对象类别 0 系统托管-代扣， 1 系统托管-公司，10 用户
			this.editForm.stageMediAmount = this.editForm.mediAmount || 0;
			this.editForm.allocateSystemAmount = 0;
			this.editForm.allocateAmount = this.editForm.allocateCompensationAmount || 0;
			const API = 'addDeliverStageCommissionDetail';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.editForm }));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.closeDialog();
					this.queryDetailData(this.detailForm);
					this.dialogEdit = false;
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 修改
		async updateRow(row) {
			if (this.titleName == '查看合同') return this.$message.warning('当前为查看合同业绩分配，不允许修改!');
			const API = 'updateDeliverStageCommissionDetail';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...row }));
				if (res.data.success) {
					this.queryDetailData(this.detailForm);
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 删除
		async deleteRow(row) {
			const API = 'deleteDeliverStageCommissionDetail';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...row }));
				if (res.data.success) {
					this.$succ(res.data.message);
					this.queryDetailData(this.detailForm);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		//获取详情数据
		queryDetailData({ dmid }) {
			const str = JSON.stringify({ id: dmid });
			this.$axios
				.fetchDeliverStageCommissionDetailForContract(str)
				.then(res => {
					if (res.data.success) {
						this.detailForm.commissionContext = res.data.data?.commissionContext || null;
						this.detailForm?.deliverStageManagementVOS?.forEach(aItem => {
							aItem.allotList = res.data.data?.deliverStageCommissionDetailVOList?.filter(bItem => {
								bItem.allocateSystemAmount = bigAdd(bItem.allocateSystemAmount, 0, 2);
								bItem.allocateAmount = bigAdd(bItem.allocateAmount, 0, 2);
								return aItem?.dmsid == bItem.dmsid;
							});
						});
						this.detailForm = Object.assign({}, this.detailForm); //浅拷贝对象达到动态变化的效果
						this.detailFormCopy = deepClone(this.detailForm);

						this.showCom = true;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('fetchDeliverStageCommissionDetailForContract |' + error);
				});
		},
		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			this.$axios
				.selectSalesmanByTwids(JSON.stringify({ twids: [], counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		//显示弹窗
		async showDetailCom(type, rowData) {
			this.titleName = type;
			if (this.titleName == '业绩分配') {
				this.detailForm = deepClone(rowData);
				this.queryUserByTwids();
				this.queryDetailData(this.detailForm); //查询所有分配的业绩
			} else if (this.titleName == '合同审核' || this.titleName == '查看合同') {
				const API = 'selectDeliverManagementDetail';
				try {
					const res = await this.$axios[API](JSON.stringify({ dmid: rowData.dmid })); //查询合同详情
					this.detailForm = { ...deepClone(rowData), ...res.data.data };
					this.$set(this.detailForm, 'salesmanName', res.data.data.salesman?.userName || '');
					this.queryDetailData(this.detailForm); //查询所有分配的业绩
				} catch (error) {
					console.error(`${API} |` + error);
				}
			}
		},

		//日期format
		dateFormat,
		jointString,
	},
};
</script>
<style lang="scss">
.allocationPlan {
	.detail-table {
		height: auto !important;
		min-height: fit-content;
		td {
			height: 30px !important;
		}
	}
	.el-table__body-wrapper {
		.cell {
			padding: 0 !important;
		}
	}

	.table-wrapper .table-main td .cell {
		padding: 0 !important;
	}

	.el-collapse-item__header {
		height: 40px !important;
		line-height: 40px !important;
	}
	.el-collapse-item__content {
		padding-bottom: 0 !important;
	}

	.input-red {
		.el-input__inner {
			color: #f56c6c !important;
		}
	}
	.text-right {
		.el-input__inner {
			text-align: right;
		}
	}

	.min-h-666 {
		min-height: 666px;
	}

	// .sticky-top {
	// 	position: sticky;
	// 	top: 0;
	// 	z-index: 888;
	// }
}
</style>
