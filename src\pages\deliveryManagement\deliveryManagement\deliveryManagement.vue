<template>
	<div id="deliveryManagement" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<!-- 交付详情 -->
		<DeliveryDetailCom ref="deliveryDetailCom" @close="queryTabPaneData" :userList="userList" />
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 动态询盘详情：聚心城使用聚心城询盘详情，其他使用默认询盘详情 -->
		<component
			v-if="showMap.InquiryDetail"
			:is="isJuxinCity ? 'InquiryDetail_JXC' : 'InquiryDetail'"
			ref="InquiryDetail"
			:inquiryOptions="tableData"
			@close="queryTableData"
		/>

		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="deal"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twid = $event.twidList;
				queryTabPaneData();
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="交付管理" name="deliveryManagement">
				<BaseLayout>
					<template #header>
						<span class="search-label">成交月份</span>
						<el-date-picker
							class="vw6"
							size="small"
							v-model="searchForm.startTime"
							type="month"
							value-format="timestamp"
							placeholder="不限"
							@change="queryTableData(1)"
							clearable
						>
						</el-date-picker>

						<label class="search-label">项目类型</label>
						<el-select
							size="small"
							class="vw6"
							v-model="searchForm.projectType"
							placeholder="项目类型"
							clearable
							filterable
							@change="queryTableData(1)"
						>
							<el-option v-for="item in projectTypeOptions" :key="item.id" :label="item.label" :value="item.id"> </el-option>
						</el-select>

						<!-- <el-select
							v-model="searchForm.salesman"
							class="vw6"
							size="small"
							placeholder="业务顾问"
							clearable
							filterable
							@change="queryTableData(1)"
						>
							<el-option v-for="item in salesmanList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
						</el-select> -->

						<el-select
							v-model="searchForm.implement"
							class="vw6"
							size="small"
							placeholder="实施顾问"
							clearable
							filterable
							@change="queryTableData(1)"
						>
							<el-option v-for="item in implementList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
						</el-select>

						<SearchHistoryInput
							className="min-w-100 vw8"
							name="salesmanName"
							placeholder="业务顾问"
							v-model.trim="searchForm.saleName"
							@input="queryTableData(1)"
						/>

						<SearchHistoryInput
							name="companyName"
							placeholder="客户工商注册名称"
							v-model="searchForm.queryParam"
							@input="queryTableData(1)"
						/>

						<el-checkbox-group v-model="searchForm.contractFlag" @change="queryTableData(1)">
							<el-checkbox label="0">未完成项目</el-checkbox>
							<el-checkbox label="1">已完成项目</el-checkbox>
						</el-checkbox-group>

						<el-checkbox v-model="searchForm.receiptStatus" :true-label="2" :false-label="0" @change="queryTableData(1)"
							>仅显示未付清的合同
						</el-checkbox>

						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
						</div>
					</template>
					<template #main>
						<div class="table-toolbar">
							<ExportBtn @trigger="openExport" />
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
							show-summary
							:summary-method="summaryMethod"
						>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="item.colNo == 'dealMonth'"
										:cont-str="dateFormat(scope.row[item.colNo], 'YM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 询盘编号 -->
									<Tooltips
										v-else-if="item.colNo == 'number'"
										class="hover-green green"
										@click.native="openInquiryDetail(scope.row)"
										:cont-str="scope.row[item.colNo] || '未知'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>
									<!-- 项目名称 -->
									<Tooltips
										v-else-if="item.colNo == 'projectName'"
										class="hover-green green"
										@click.native="openDetail(scope.row, '交付详情')"
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>
									<!-- 合同金额 -->
									<Tooltips
										v-else-if="item.colNo == 'dealAmount'"
										:cont-str="scope.row[item.colNo]?.toFixed(3)"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<!-- 状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:class="scope.row[item.colNo] == 1 ? 'green' : 'red'"
										:cont-str="scope.row[item.colNo] == 1 ? '已完成' : '未完成'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 项目类型 -->
									<Tooltips
										v-else-if="item.colNo == 'projectType'"
										:cont-str="projectTypeMap[scope.row[item.colNo]]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<u-table-column label="" width="60" align="right">
								<template slot-scope="scope">
									<el-button type="text" size="small" @click="openDetail(scope.row, '交付详情')">交付</el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="交付河流图" name="deliveryRive">
				<delivery-river
					v-if="activeTab == 'deliveryRive'"
					ref="deliveryRive"
					:twidList="searchForm.twid"
					:channelName="searchForm.channelName"
				></delivery-river>
			</el-tab-pane>
			<el-tab-pane label="项目总览" name="deliveryOverview">
				<deliveryOverview
					v-if="activeTab == 'deliveryOverview'"
					ref="deliveryOverview"
					:twidList="searchForm.twid"
					:channelName="searchForm.channelName"
					@openDetail="openDetail"
				></deliveryOverview>
			</el-tab-pane>
			<el-tab-pane label="人员计划" name="deliverySchedule">
				<deliverySchedule
					v-if="activeTab == 'deliverySchedule'"
					ref="deliverySchedule"
					:twidList="searchForm.twid"
					:channelName="searchForm.channelName"
					@openDetail="openDetail"
				></deliverySchedule>
			</el-tab-pane>
			<!-- <el-tab-pane label="交付时间计划" name="deliveryPlan">
				<deliveryPlan
					v-if="activeTab == 'deliveryPlan'"
					ref="deliveryPlan"
					:twidList="searchForm.twid"
					:channelName="searchForm.channelName"
					@openDetail="openDetail"
				></deliveryPlan>
			</el-tab-pane> -->
			<el-tab-pane label="交付日报" name="deliveryDaily">
				<deliveryDaily
					v-if="activeTab == 'deliveryDaily'"
					ref="deliveryDaily"
					:twidList="searchForm.twid"
					:channelName="searchForm.channelName"
					@openDetail="openDetail"
				></deliveryDaily>
			</el-tab-pane>
			<el-tab-pane label="产品清单" name="productionList">
				<production-list v-if="activeTab == 'productionList'"></production-list>
			</el-tab-pane>
			<el-tab-pane label="计划模板" name="planTemplate">
				<planTemplate v-if="activeTab == 'planTemplate'"></planTemplate>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import { debounce, dateFormat, sortTableData, bigAdd, getNowMonthEndDay } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable';
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryDetail_JXC from '@/components/InquiryDetail_JXC/InquiryDetail.vue'; //询盘详情弹窗

import DeliveryDetailCom from './components/deliveryDetailCom.vue';
import productionList from './productionList.vue';
import deliveryRiver from './deliveryRiver.vue';
import ChannelSelect from '@/components/ChannelSelect.vue';
// import deliveryPlan from './deliveryPlan.vue'; //交付时间计划
import deliveryDaily from './deliveryDaily.vue'; //交付日报
import deliveryOverview from './deliveryOverview.vue'; //交付总览
import deliverySchedule from './deliverySchedule.vue'; //交付人员时间安排
import planTemplate from './planTemplate.vue'; //交付计划模板
import { projectTypeOptions, projectTypeMap } from '@/assets/js/contractSource';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		InquiryDetail,
		InquiryDetail_JXC,
		DeliveryDetailCom,
		productionList,
		ChannelSelect,
		deliveryRiver,
		// deliveryPlan,
		deliveryDaily,
		deliveryOverview,
		deliverySchedule,
		planTemplate,
		ExportBtn,
	},
	name: 'deliveryManagement',
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	data() {
		return {
			openMove: false, //打开组件
			activeTab: 'deliveryManagement',
			rowData: {},
			userList: [], //渠道/代理人员列表

			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 200, 500, 1000],
			},
			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'left', width: '' },
				{ colName: '项目名称', colNo: 'projectName', align: 'left', width: '' },
				{ colName: '项目类别', colNo: 'projectType', align: 'left', width: '' },
				{ colName: '客户工商注册名称', colNo: 'registeredBusinessName', align: 'left', width: '170' },
				{ colName: '业务顾问', colNo: 'salesmanName', align: 'left', width: '' },
				{ colName: '实施顾问', colNo: 'implementName', align: 'left', width: '' },
				{ colName: '合同编号', colNo: 'contractNo', align: 'left', width: '' },
				{ colName: '成交月份', colNo: 'dealMonth', align: 'center', width: '' },
				{ colName: '合同金额(万元)', colNo: 'dealAmount', align: 'right', width: '' },
				{ colName: '已收金额', colNo: 'collectionAmount', align: 'right', width: '' },
				{ colName: '完工期数', colNo: 'complate', align: 'right', width: '' },
				{ colName: '未完工期数', colNo: 'unComplate', align: 'right', width: '120' },
				{ colName: '延迟期数', colNo: 'delay', align: 'right', width: '' },
				{ colName: '项目状态', colNo: 'status', align: 'center', width: '' },
			],
			searchForm: {
				receiptStatus: 2,
				contractFlag: ['0', '1'],
				twid: [],
				channelName: [],
				twidList: [],
				queryParam: '',
			},
			projectTypeOptions,
			projectTypeMap,
			showMap: {
				InquiryDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['isJuxinCity']), //是否聚心城人员
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
		//实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	actived() {
		this.queryTabPaneData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					if (['dealAmount', 'collectionAmount'].includes(column.property)) return Number(item[column.property]) || 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? bigAdd(prev, curr, 3) : prev;
				}, 0); //合计计算
				means[columnIndex] = sum > 0 ? <span>{sum}</span> : '';
			}
			return [means];
		},
		// 查询不同tab页面的数据
		queryTabPaneData() {
			if (this.activeTab !== 'deliveryManagement') {
				this.$refs[this.activeTab]?.queryTableData('init');
			} else {
				this.queryTableData('init');
			}
		},
		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			this.$axios
				.selectSalesmanByTwids(JSON.stringify({ twids: this.searchForm.twidList, counselor: '' }))
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		// 打开交付详情
		openDetail(rowData) {
			this.$refs.deliveryDetailCom.showDetailCom(rowData);
		},
		// 打开询盘详情
		openInquiryDetail(row) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom('修改', row);
			});
		},
		changeTab(tab) {
			if (tab.name == 'deliveryManagement') {
				this.$refs.ChannelSelect.getLocalStorage();
			}
		},

		queryTableData: debounce(function (type) {
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			this.searchForm.endTime = this.searchForm.startTime ? getNowMonthEndDay(this.searchForm.startTime) : null;
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectDeliverManagement'; //接口
			const DATA = JSON.stringify({
				...this.searchForm,
				flag: 2, //1 合同管理列表 2 ：交付管理列表
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							item['salesmanName'] = item?.salesman?.userName || '';
							item['implementName'] = item?.implement?.userName || '';
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						this.sortChange(this.tableSort, true);
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
						type == 'init' && this.queryUserByTwids(); //初始时查询渠道人员
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		//日期format
		dateFormat: dateFormat,
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					...this.searchForm,
					flag: 2, //1 合同管理列表 2 ：交付管理列表
				}), //接口参数
				API: 'exportContractManager', //导出接口
				downloadData: '合同管理导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
#deliveryManagement {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
