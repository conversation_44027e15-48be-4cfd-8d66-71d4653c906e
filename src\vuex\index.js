import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);

// 动态引入 Vuex 模块
const requireModule = require.context('./modules', false, /\.js$/);
const modules = requireModule.keys().reduce((modules, fileName) => {
	const moduleName = fileName.replace('./', '').replace('.js', '');
	modules[moduleName] = requireModule(fileName).default;
	return modules;
}, {});

export default new Vuex.Store({
	state: {},
	actions: {},
	mutations: {
		// 将state的值重置
		resetState(state) {
			state.inquiryList = [];
		},
	},
	modules, // 使用动态引入的模块
});
