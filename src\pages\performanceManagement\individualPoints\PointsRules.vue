<template>
	<div class="PointsRules">
		<div class="main-card flex">
			<div class="left W20 min-w-200">
				<div class="table-title label-title m10">规则类别</div>
				<div class="treeContainer">
					<el-tree
						ref="treeRef"
						:data="treeData"
						node-key="aupcid"
						default-expand-all
						:highlight-current="true"
						:props="{ children: 'children', label: 'label' }"
						:renderContent="renderContent"
						@node-click="clickNode"
					></el-tree>
				</div>
			</div>
			<div class="right W80 min-w-300">
				<div v-if="!tableTitle" class="flex-center H90">
					<div class="empty-content">
						<img src="@/assets/img/none.webp" />
						<p class="empty-text">请选择左侧的规则类别</p>
					</div>
				</div>

				<div v-if="tableTitle" class="W100 pt10 pb10">
					<div class="table-title label-title ml10">{{ tableTitle }}</div>
					<div class="table-wrapper">
						<u-table
							:data="tableData"
							class="table-main table-main2"
							:height="888"
							:row-height="35"
							@sort-change="sortChange"
							@selection-change="selectedData = $event"
							selectTrClass="selectTr"
							show-header-overflow="title"
							stripe
						>
							<u-table-column
								v-for="item in tableColumn"
								:key="item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 是否需协助人 -->
									<div v-if="['cfgHasFacilitator', 'cfgAudit', 'cfgAttachments'].includes(item.colNo)">
										<span>{{ scope.row[item.colNo] ? 'Y' : '' }}</span>
									</div>

									<!-- 积分方式 是否按天积分 0 整体积分，1 按天积分；交付老师的积分里部分需要按天积分 -->
									<div v-else-if="item.colName === '积分方式' && item.colNo === 'cfgPointsByDay'">
										<span>{{ { 0: '整体积分', 1: '按天积分' }[scope.row[item.colNo]] }}</span>
									</div>
									<!-- 扣分方式 0单次 1每天 -->
									<div v-else-if="item.colName === '扣分方式' && item.colNo === 'cfgPointsByDay'">
										<span>{{ { 0: '单次', 1: '每天' }[scope.row[item.colNo]] }}</span>
									</div>
									<!-- 免检系数 -->
									<div v-else-if="item.colNo === 'cfgQualityFactor'">
										<span>{{ scope.row[item.colNo] + '%' }}</span>
									</div>

									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<u-table-column label="" width="" align="" />
							<u-table-column label="" width="120" align="right">
								<template #header>
									<el-button type="text" class="el-icon-plus" @click="openDialog(null)"> 添加</el-button>
								</template>
								<template slot-scope="scope">
									<el-button type="text" class="el-icon-edit-outline" @click="openDialog(scope.row)"></el-button>
									<el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row.aupcid)"></el-button>
								</template>
							</u-table-column>
						</u-table>
					</div>
				</div>
			</div>
		</div>

		<!-- 右边表格弹窗 -->
		<el-dialog :visible.sync="dialogEdit" width="800px" :close-on-click-modal="false" append-to-body @close="closeDialog('row')">
			<el-row slot="title">{{ dialogTitle }}{{ tableTitle }}</el-row>
			<el-form
				ref="formRef"
				class="multiColumn-form"
				label-width="100px"
				label-position="left"
				:model="editForm"
				:rules="formRules"
			>
				<el-form-item v-for="item in tableColumn" :key="item.colNo" :label="item.colName" :prop="item.colNo">
					<div v-if="item.type == 'input'">
						<el-input :placeholder="`请输入${item.colName}`" v-model="editForm[item.colNo]" clearable></el-input>
					</div>

					<div v-else-if="item.type == 'radio'">
						<el-radio-group v-if="tableTitle.includes('积分') && item.colNo == 'cfgPointsByDay'" v-model="editForm[item.colNo]">
							<el-radio :label="0">整体积分</el-radio>
							<el-radio :label="1">按天积分</el-radio>
						</el-radio-group>
						<el-radio-group
							v-else-if="tableTitle.includes('扣分') && item.colNo == 'cfgPointsByDay'"
							v-model="editForm[item.colNo]"
						>
							<el-radio :label="0">单次</el-radio>
							<el-radio :label="1">每天</el-radio>
						</el-radio-group>
						<el-radio-group v-else v-model="editForm[item.colNo]">
							<el-radio :label="0">否</el-radio>
							<el-radio :label="1">是</el-radio>
						</el-radio-group>
					</div>

					<div v-else-if="item.type == 'check'">
						<el-checkbox v-model="editForm[item.colNo]" :true-label="1" :false-label="0"></el-checkbox>
					</div>

					<div v-else-if="item.type == 'select'">
						<el-select
							class="W100"
							v-model="editForm.cfgExcludeInspectionObjectId"
							placeholder="请选择免检人员"
							clearable
							filterable
							@change="changeUser"
						>
							<el-option v-for="user in userList" :key="user.auid" :label="user.userName" :value="user.auid"> </el-option>
						</el-select>
					</div>

					<div v-else>
						<Tooltips :cont-str="editForm[item.colNo]" />
					</div>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button @click="closeDialog('row')">取消</el-button>
				<el-button type="primary" @click="saveEdit(editForm, 'dialog')">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import folderImg from '@/assets/img/folder.svg';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { checkRequired, debounce, dateFormat, resetValues, sortTableData, deepClone } from '@/util/tool'; //按需引入常用工具函数

export default {
	// import引入的组件需要注入到对象中才能使用
	components: {},
	name: 'PointsRules',
	data() {
		return {
			activeTab: 'PointsRules',
			treeData: [
				{ cfgGroup: '流量积分项', cfgCate: '', cfgCode: '', cfgName: '', aupcid: '1', children: [] },
				{ cfgGroup: '业务积分项', cfgCate: '', cfgCode: '', cfgName: '', aupcid: '2', children: [] },
				{ cfgGroup: '业务扣分项', cfgCate: '', cfgCode: '', cfgName: '', aupcid: '3', children: [] },
				{ cfgGroup: '交付积分项', cfgCate: '', cfgCode: '', cfgName: '', aupcid: '4', children: [] },
				{ cfgGroup: '交付扣分项', cfgCate: '', cfgCode: '', cfgName: '', aupcid: '5', children: [] },
				{ cfgGroup: '开发积分项', cfgCate: '', cfgCode: '', cfgName: '', aupcid: '6', children: [] },
				{ cfgGroup: '开发扣分项', cfgCate: '', cfgCode: '', cfgName: '', aupcid: '7', children: [] },
				{ cfgGroup: '质量系数', cfgCate: '', cfgCode: '', cfgName: '', aupcid: '8', children: [] },
				{ cfgGroup: '免检人员', cfgCate: '', cfgCode: '', cfgName: '', aupcid: '9', children: [] },
			], //树结构数据
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [], //表格数据
			tableDataCopy: [],
			treeNode: {}, //当前树节点
			// 弹窗相关
			dialogTitle: '',
			dialogEdit: false,
			editForm: {
				aupcid: '',
				cfgAudit: '',
				cfgCate: '',
				cfgCode: '',
				cfgExcludeInspectionObjectId: '',
				cfgExcludeInspectionObjectName: '',
				cfgFacilitatorPoints: '',
				cfgGroup: '',
				cfgHasFacilitator: '',
				cfgName: '',
				cfgOrderNum: '',
				cfgPoints: '',
				cfgPointsByDay: '',
				cfgQualityFactor: '',
			},
			userList: [],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 表格标题
		tableTitle() {
			return this.treeNode?.cfgGroup || ''; //表格标题
		},
		// 表单规则
		formRules() {
			const baseRules = {
				cfgCode: [{ required: true, message: '请输入代码', trigger: 'change' }],
				cfgName: [{ required: true, message: '请输入名称', trigger: 'change' }],
			};

			const rulesMapping = {
				质量系数: {
					...baseRules,
					cfgQualityFactor: [{ required: true, message: '请输入质量系数', trigger: 'change' }],
				},
				免检人员: {
					cfgExcludeInspectionObjectName: [{ required: true, message: '请输入免检人员', trigger: 'change' }],
				},
				扣分: {
					...baseRules,
					cfgPoints: [{ required: true, message: '请输入扣分', trigger: 'change' }],
					cfgCate: [{ required: true, message: '请输入类别', trigger: 'change' }],
				},
				积分: {
					...baseRules,
					cfgPoints: [{ required: true, message: '请输入积分', trigger: 'change' }],
					cfgCate: [{ required: true, message: '请输入类别', trigger: 'change' }],
					// 开发积分项不需要审查
					cfgAudit:
						(this.treeNode.cfgGroup !== '开发积分项'&&this.treeNode.cfgGroup !== '流量积分项')
							? [{ required: true, message: '请选择是否需要审查', trigger: 'blur' }]
							: undefined,
					// 需要协助人时协助人积分必填
					cfgFacilitatorPoints:
						this.editForm.cfgHasFacilitator == 1 ? [{ required: true, message: '请输入协助人积分', trigger: 'blur' }] : undefined,
				},
			};

			// 遍历规则映射，查找符合条件的规则
			for (const [key, rules] of Object.entries(rulesMapping)) {
				if (this.tableTitle === key || this.tableTitle.includes(key)) {
					// 过滤掉未定义的规则
					return Object.fromEntries(Object.entries(rules).filter(([_, value]) => value !== undefined));
				}
			}

			return baseRules;
		},

		// 列表表头以及弹窗编辑表单
		tableColumn() {
			const columnMapping = {
				流量积分: [
					{ colName: '代码', colNo: 'cfgCode', align: 'left', width: '', type: 'input' },
					{ colName: '名称', colNo: 'cfgName', align: 'left', width: '', type: 'input' },
					{ colName: '积分', colNo: 'cfgPoints', align: 'right', width: '', type: 'input' },
					{ colName: '类别', colNo: 'cfgCate', align: 'left', width: '', type: 'input' },
				],
				业务积分: [
					{ colName: '代码', colNo: 'cfgCode', align: 'left', width: '', type: 'input' },
					{ colName: '名称', colNo: 'cfgName', align: 'left', width: '', type: 'input' },
					{ colName: '积分', colNo: 'cfgPoints', align: 'right', width: '', type: 'input' },
					{ colName: '类别', colNo: 'cfgCate', align: 'left', width: '', type: 'input' },
					{ colName: '需协助人', colNo: 'cfgHasFacilitator', align: 'center', width: '', type: 'radio' },
					{ colName: '协助人积分', colNo: 'cfgFacilitatorPoints', align: 'right', width: '', type: 'input' },
					{ colName: '需审查', colNo: 'cfgAudit', align: 'center', width: '', type: 'radio' },
					{ colName: '需上传附件', colNo: 'cfgAttachments', align: 'center', width: '', type: 'radio' },
				],
				交付积分: [
					{ colName: '代码', colNo: 'cfgCode', align: 'left', width: '', type: 'input' },
					{ colName: '名称', colNo: 'cfgName', align: 'left', width: '', type: 'input' },
					{ colName: '积分', colNo: 'cfgPoints', align: 'right', width: '', type: 'input' },
					{ colName: '类别', colNo: 'cfgCate', align: 'left', width: '', type: 'input' },
					{ colName: '需协助人', colNo: 'cfgHasFacilitator', align: 'center', width: '', type: 'radio' },
					{ colName: '协助人积分', colNo: 'cfgFacilitatorPoints', align: 'right', width: '', type: 'input' },
					{ colName: '需审查', colNo: 'cfgAudit', align: 'center', width: '', type: 'radio' },
					{ colName: '需上传附件', colNo: 'cfgAttachments', align: 'center', width: '', type: 'radio' },
					{ colName: '积分方式', colNo: 'cfgPointsByDay', align: 'center', width: '', type: 'radio' },
				],
				开发积分: [
					{ colName: '代码', colNo: 'cfgCode', align: 'left', width: '', type: 'input' },
					{ colName: '名称', colNo: 'cfgName', align: 'left', width: '', type: 'input' },
					{ colName: '积分', colNo: 'cfgPoints', align: 'right', width: '', type: 'input' },
					{ colName: '类别', colNo: 'cfgCate', align: 'left', width: '', type: 'input' },
					{ colName: '积分方式', colNo: 'cfgPointsByDay', align: 'center', width: '', type: 'radio' },
				],
				扣分: [
					{ colName: '代码', colNo: 'cfgCode', align: 'left', width: '100', type: 'input' },
					{ colName: '名称', colNo: 'cfgName', align: 'left', width: '250', type: 'input' },
					{ colName: '扣分', colNo: 'cfgPoints', align: 'right', width: '100', type: 'input' },
					{ colName: '类别', colNo: 'cfgCate', align: 'left', width: '', type: 'input' },
					{ colName: '扣分方式', colNo: 'cfgPointsByDay', align: 'center', width: '', type: 'radio' },
				],
				质量系数: [
					{ colName: '代码', colNo: 'cfgCode', align: 'left', width: '100', type: 'input' },
					{ colName: '名称', colNo: 'cfgName', align: 'left', width: '250', type: 'input' },
					{ colName: '质量系数', colNo: 'cfgQualityFactor', align: 'right', width: '100', type: 'input' },
				],
				免检人员: [
					{ colName: '免检人员', colNo: 'cfgExcludeInspectionObjectName', align: 'left', width: '200', type: 'select' },
					{ colName: '备注', colNo: 'cfgName', align: 'left', width: '', type: 'input' },
				],
			};

			// 找到符合条件的列配置
			for (const [key, columns] of Object.entries(columnMapping)) {
				if (this.tableTitle === key || this.tableTitle.includes(key)) {
					return columns;
				}
			}
			return [];
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	beforeMount() {}, // 生命周期 - 挂载之前
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryUserByTwids();
	},
	activated() {},
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 查询渠道/代理下用户
		queryUserByTwids: debounce(function () {
			const str = JSON.stringify({
				twids: [],
				counselor: '',
			});
			this.$axios
				.selectSalesmanByTwids(str)
				.then(res => {
					if (res.data.success) {
						
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {

					console.log('selectSalesmanByTwids |' + error);
				});
		}),
		// JSX渲染树结构
		renderContent(h, { node, data, store }) {
			const { cfgGroup } = data;
			return (
				<div class="custom-tree-node ">
					<div class="flex-align-center h-40">
						<img class="folder-img" src={folderImg} />
						<span class="ml10 mr10">
							<div class="node-title">{cfgGroup}</div>
						</span>
					</div>
				</div>
			);
		},

		// 点击树节点
		clickNode: debounce(async function (node) {
			if (!node) return;
			this.treeNode = deepClone(node);
			const API = 'selectAdminUserPointsConfigurationList';
			try {
				const res = await this.$axios[API](JSON.stringify({ cfgGroup: node.cfgGroup }));
				if (res.data.success) {
					this.tableData = res.data.data;
					this.tableDataCopy = deepClone(this.tableData);
					this.sortChange(this.tableSort);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				
				console.error(`${API} |` + error);
			}
		}, 300),

		// 修改表格数据
		openDialog(row) {
			this.dialogTitle = row?.aupcid ? '修改' : '添加';
			if (row) this.editForm = deepClone(row);
			this.dialogEdit = true;
			this.$nextTick(() => {
				this.$refs.formRef.clearValidate(); //移除该表单项的校验结果
			});
		},
		// 关闭对话框
		closeDialog() {
			this.dialogEdit = false;
			this.editForm = resetValues(this.editForm); //数据重置
			this.$nextTick(() => {
				this.$refs.formRef.clearValidate(); //移除该表单项的校验结果
			});
		},
		changeUser() {
			this.editForm.cfgExcludeInspectionObjectName = this.editForm.cfgExcludeInspectionObjectId
				? this.userList.find(i => i.auid == this.editForm.cfgExcludeInspectionObjectId)?.userName
				: '';
		},
		// 保存表格数据
		saveEdit(form, type) {
			const { cfgGroup } = this.treeNode;
			if (checkRequired(form, this.formRules)) return; //必填项校验
			const API = form.aupcid ? 'updateAdminUserPointsConfiguration' : 'addAdminUserPointsConfiguration';

			this.$axios[API](JSON.stringify({ ...form, cfgGroup }))
				.then(res => {
					if (res.data.success) {
						type == 'dialog' && this.$succ('保存成功');
						this.clickNode(this.treeNode);
						this.closeDialog();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {

					console.log(`${API} |` + error);
				});
		},

		//删除数据（批量）
		deleteRow(aupcid) {
			this.$confirm('选中数据将被删除，删除后数据不可恢复', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteAdminUserPointsConfiguration(JSON.stringify({ id: aupcid }))
						.then(res => {
							if (res.data.success) {
								this.clickNode(this.treeNode);
								this.$succ('数据已删除！');
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							
							console.log('deleteAdminUserPointsConfiguration |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		//自定义排序（非虚拟列表）
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = sortTableData(this.tableDataCopy, prop, order);
				this.tableData = [...sortedData];
			} else {
				this.tableData = [...this.tableDataCopy];
			}
		},

		dateFormat,
		deepClone,
		resetValues,
	},
};
</script>

<style lang="scss" scoped>
.PointsRules {
	width: 100%;
	position: relative;
	overflow: hidden;
}
</style>

<style lang="scss">
.PointsRules {
	.main-card {
		width: 100%;
		height: calc(100vh - 170px);
		overflow-y: auto;
		box-sizing: border-box;
		border: 1px solid #d7d7d7;
		background: #fff;
		border-radius: 10px;
		.left {
			box-sizing: border-box;
			height: 100%;
			padding: 10px;
			.logo-img {
				width: 20px;
				height: 20px;
			}
			.logo-title {
				font-size: 16px;
				color: #555;
				// font-weight: 600;
			}
			.folder-img {
				width: 18px;
			}
			.treeContainer {
				overflow: auto;
				height: 93%;
				.el-tree-node__content {
					// height: 45px !important;
					display: flex;
					align-items: flex-start;
					height: max-content !important;
					.el-tree-node__expand-icon {
						margin-top: 8px;
					}
					.node-title {
						color: #555;
						// font-weight: 400;
						font-size: 14px;
					}
					.node-subTitle {
						margin: -10px 0 5px 28px;
						color: #999;
						font-size: 12px;
					}
				}
			}
		}

		.right {
			box-sizing: border-box;
			height: 100%;
			border-left: 1px solid #d7d7d7;
			padding: 10px;
			.empty-content {
				text-align: center;
				.empty-text {
					font-size: 16px;
					color: #999;
				}
			}

			.table-title {
				font-weight: 500;
				font-size: 16px;
				color: #555;
			}
			.table-main {
				height: calc(100vh - 235px) !important;
			}
			.table-wrapper .table-main2 .el-table__body td {
				// height: 49px !important;
				height: 35px !important;
				padding: 0px;
				.el-input__inner {
					padding-left: 0px;
				}

				.el-range-input {
					width: 65px !important;
					background: transparent !important;
				}
			}
			.table-wrapper .table-main2 .is-right {
				.tdNormal {
					padding-right: 10px;
				}
			}
			.el-table--striped .el-table__body tr.el-table__row--striped td {
				background: #f2f2f2;
				.el-input__inner {
					background: #f2f2f2 !important;
				}
			}
		}

		// 鼠标移入移出时显隐按钮
		.el-tree {
			.el-tree-node__content {
				.buttons {
					display: none !important;
				}
				&:hover .buttons {
					display: flex !important;
				}
			}
		}
	}
}
</style>
