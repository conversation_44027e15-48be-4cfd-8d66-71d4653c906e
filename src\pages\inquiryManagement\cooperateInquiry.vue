<template>
	<div id="cooperateInquiry">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 询盘详情 -->
		<InquiryDetail v-if="showMap.InquiryDetail" ref="InquiryDetail" :inquiryOptions="tableData" @close="queryTableData" />
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="托管询盘" name="cooperateInquiry">
				<BaseLayout>
					<template #header>
						<div v-show="hosterList.length > 1">
							<span class="search-label mr10">托管方</span>
							<el-select
								size="small"
								multiple
								collapse-tags
								v-model="hosterTwidList"
								placeholder="全部托管方"
								@change="queryTableData(1)"
								clearable
							>
								<el-option v-for="item in hosterList" :key="item.twid" :label="item.twName" :value="item.twid"> </el-option>
							</el-select>
						</div>

						<span class="search-label">询盘日期</span>

						<DateSelect
							class="vw8"
							:dateSelectObj="dateSelectObj"
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>

						<el-select
							class="w-150"
							size="small"
							multiple
							collapse-tags
							v-model="searchForm.quality"
							placeholder="询盘质量"
							@change="queryTableData(1)"
							clearable
						>
							<el-option v-for="key in Object.keys(qualityMap)" :key="key" :label="qualityMap[key]" :value="Number(key)">
							</el-option>
						</el-select>

						<el-input
							class="searchBox"
							size="small"
							clearable
							v-model="queryStr"
							placeholder="手机号/公司名称/地区"
							@input="queryTableData(1)"
						></el-input>

						<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<el-checkbox-group class="mr-auto" v-model="searchForm.stage" size="mini" @change="queryTableData(1)">
								<el-checkbox v-for="item in stageList" :label="item.value" :key="item.value">
									<span class="fs-12">{{ item.label }}</span>
								</el-checkbox>
							</el-checkbox-group>
							<el-button type="text" class="icon-third_searchC" v-popover:thSearch>查找</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" type="index" align="center"> </u-table-column>
							<!-- 展开行 -->
							<u-table-column label="" width="20" type="expand">
								<template slot-scope="scope">
									<div
										class="p_expand"
										v-for="(item, index) in scope.row.documentaryRecordsList"
										:key="index + item.documentaryTime"
									>
										<span class="s_content">{{ dateFormat(item.documentaryTime, 'lineM') }} </span>
										<span class="s_name"> {{ item.salesmanName ? item.salesmanName : '跟单员' }}</span>
										<span class="s_content">：{{ item.content }} </span>
										<span v-show="item.nextPlan || item.nextStep"> | 计划安排：</span>
										<span
											:style="{
												display: item.nextPlan ? 'content' : 'none',
												'text-decoration': item.planStatus == 1 ? 'line-through' : '',
											}"
										>
											{{ dateFormat(item.nextStep, 'MD') }} {{ item.nextPlan }}
										</span>
									</div>

									<div class="p_expand" v-show="!scope.row.documentaryRecordsList">
										<span class="s_content">当前暂无跟单记录</span>
									</div>
								</template>
							</u-table-column>

							<!-- :width="columnWidthCurr[item.colNo]" -->
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="item.colNo == 'lastDate'"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<Tooltips
										v-else-if="item.colNo == 'idDateTime' || item.colNo == 'signingDate'"
										:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<Tooltips
										v-else-if="item.colNo == 'expectedMonth'"
										class="orange"
										:cont-str="dateFormat(scope.row[item.colNo], 'YM')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 来源 -->
									<Tooltips
										v-else-if="item.colNo == 'channel'"
										:cont-str="
											jointString(
												'/',
												sourceMap[scope.row[item.colNo]],
												scope.row.promotionalVidUserName,
												scope.row.promotionalVid,
											)
										"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 询盘质量 -->
									<Tooltips
										v-else-if="item.colNo == 'quality'"
										:cont-str="qualityMap[scope.row[item.colNo]]"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 商机质量 -->
									<Tooltips
										v-else-if="item.colNo == 'businessOpportunityQuality'"
										:cont-str="businessQualityMap[scope.row[item.colNo]]"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 阶段 -->
									<Tooltips
										v-else-if="item.colNo == 'stage'"
										:cont-str="stageMap[scope.row[item.colNo]]"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<Tooltips
										v-else-if="item.colNo == 'estimatedAmount'"
										:cont-str="scope.row[item.colNo]?.toFixed(2)"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 合同金额 -->
									<Tooltips
										v-else-if="item.colNo == 'contractAmount' && scope.row.stage == 5"
										:cont-str="scope.row[item.colNo]?.toFixed(2)"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- rePurchase 1 复购 #de873d  status 0 已备案  status 1 未备案 #ff1744-->
									<Tooltips
										v-else-if="item.colNo == 'companyName' && scope.row.rePurchase"
										class="blue"
										:cont-str="(scope.row[item.colNo] || '未知') + '（复购）'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<Tooltips
										v-else-if="item.colNo == 'companyName' && !scope.row.rePurchase && scope.row.status"
										class="red"
										:cont-str="(scope.row[item.colNo] || '未知') + '（未备案）'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 询盘编号 -->
									<Tooltips
										v-else-if="item.colNo == 'number'"
										class="hover-green primary"
										@click.native="openDetail('修改', scope.row)"
										:cont-str="scope.row[item.colNo] || '未知'"
										:cont-width="(scope.column.width || scope.column.realWidth) - 18"
									/>

									<!-- 录音 -->
									<div class="audioPlayer" v-else-if="item.colNo == 'callRecording' && scope.row[item.colNo]">
										<!-- 询盘录音播放器 -->
										<InquiryAudioPlayer :audioUrl="scope.row[item.colNo]" :idid="scope.row.idid" />
									</div>
									<!-- 是否代理 -->
									<span v-else-if="item.colNo == 'isProxy'">
										{{ scope.row[item.colNo] ? '是' : '否' }}
									</span>
									<!-- 是否复购 -->
									<span v-else-if="item.colNo == 'rePurchase'">
										{{ scope.row[item.colNo] ? '是' : '否' }}
									</span>
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>

							<u-table-column label="" width="50" align="center">
								<template slot-scope="scope">
									<el-button type="text" class="el-icon-edit-outline" @click="openDetail('修改', scope.row)"></el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>
		<!-- 放大镜查询 -->
		<el-popover v-model="searchPopver" ref="thSearch" placement="bottom-end" width="400" :visible-arrow="false">
			<!-- 放大镜搜索表单 -->
			<div class="searchPop p0 fs-12">
				<div class="searchPop-header">
					<span>查找条件</span>
					<i class="searchPop-header-close el-icon-close" @click.stop="searchPopver = false"></i>
				</div>
				<el-form :model="searchForm" label-width="80px" label-position="left" class="W100 pt10 pr20">
					<el-form-item label="联系人" prop="customerName">
						<el-input
							@keyup.enter.native="queryTableData(1)"
							placeholder="请输入联系人"
							v-model="searchForm.customerName"
							size="mini"
						></el-input>
					</el-form-item>

					<el-form-item label="产品" prop="keyword">
						<el-input
							@keyup.enter.native="queryTableData(1)"
							placeholder="请输入产品"
							v-model="searchForm.keyword"
							size="mini"
						></el-input>
					</el-form-item>
					<el-form-item label="业务顾问" prop="salesman">
						<el-select class="W100" size="mini" v-model="searchForm.salesman" placeholder="请选择业务顾问" filterable clearable>
							<el-option v-for="item in salesmanList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="来源" prop="channel">
						<el-select
							class="W100"
							size="mini"
							multiple
							collapse-tags
							v-model="searchForm.channel"
							placeholder="来源"
							clearable
							filterable
						>
							<el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
						</el-select>
					</el-form-item>
				</el-form>
				<div class="mt20 border-top text-right">
					<el-button type="text" @click.stop="queryTableData(1)" class="color-666">确定</el-button>
				</div>
			</div>
		</el-popover>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import DateSelect from '@/components/DateSelect/DateSelect';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable';
import { sourceList, sourceMap, stageList, stageMap, qualityMap, businessQualityMap } from '@/assets/js/inquirySource.js';
import InquiryDetail from '@/components/InquiryDetail/InquiryDetail.vue'; //询盘详情弹窗
import InquiryAudioPlayer from '@/components/InquiryAudioPlayer'; //询盘录音播放器
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		ExportTable,
		InquiryDetail,
		InquiryAudioPlayer,
	},
	name: 'cooperateInquiry',
	data() {
		return {
			isCooper: true,
			queryStr: '',
			openMove: false,
			region: '',
			activeTab: 'cooperateInquiry',
			// 团队信息
			teamDataInfo: {
				teamName: '',
				tid: '',
				uid: '',
				admin: '',
			},
			titleName: '',
			hosterList: [],
			hosterTwidList: [],

			checkAll: false,
			checkedStages: [],
			isIndeterminate: true,
			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			userList: [], //渠道/代理人员列表
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tableDataCopy: [],
			tableHeight: 650,
			tablePageForm: {
				total: 0,
				pageSize: 50,
				currentPage: 1,
				pageSizes: [50, 100, 200, 400],
			},
			tableColumn: [
				{ colName: '询盘编号', colNo: 'number', align: 'center' },
				{ colName: '日期', colNo: 'idDateTime', align: 'center' },
				{ colName: '登记人', colNo: 'createName', align: 'left', width: '80' },
				{ colName: '来源', colNo: 'channel', align: 'left', width: '80' },
				{ colName: '推广素材来源', colNo: 'promotionalMaterialSource', align: 'left', state: true, width: '80' },
				{ colName: '区域', colNo: 'region', align: 'left' },
				{ colName: '客户称呼', colNo: 'customerName', align: 'left' },
				{ colName: '公司简称', colNo: 'companyName', align: 'left' },
				{ colName: '所属行业', colNo: 'industry', align: 'left' },
				{ colName: '分销/代理', colNo: 'twidName', align: 'left', width: '90' },
				{ colName: '咨询情况', colNo: 'consultingCase', align: 'left' },
				{ colName: '产品', colNo: 'keyword', align: 'left' },
				{ colName: '业务顾问', colNo: 'salesmanName', align: 'left' },
				// { colName: '业务反馈', colNo: 'feedback', align: 'left' },
				{ colName: '询盘质量', colNo: 'quality', align: 'left', width: '65' },
				{ colName: '商机质量', colNo: 'businessOpportunityQuality', align: 'left', state: true, width: '65' },
				{ colName: '跟进频次', colNo: 'followUpFrequency', align: 'left', state: true, width: '' },
				{ colName: '跟进次数', colNo: 'count', align: 'right', width: '65' },
				{ colName: '最后跟进日期', colNo: 'lastDate', align: 'center', width: '78' },
				{ colName: '静置天数', colNo: 'restDays', align: 'center', width: '65' },
				{ colName: '预计成交月份', colNo: 'expectedMonth', align: 'center', width: '75' },
				{ colName: '预计成交金额（万元）', colNo: 'estimatedAmount', align: 'right', width: '100' },
				{ colName: '阶段', colNo: 'stage', align: 'left', width: '70' },
				{ colName: '录音', colNo: 'callRecording', align: 'left', width: '55' },
				{ colName: '成交时间', colNo: 'signingDate', align: 'center', state: true },
				{ colName: '合同金额（万元）', colNo: 'contractAmount', align: 'right', state: true, width: '100' },
				{ colName: '是否复购', colNo: 'rePurchase', align: 'center', state: true },
				{ colName: '是否代理', colNo: 'isProxy', align: 'center', state: true },
			],
			searchForm: {
				customerName: '',
				contactInfo: '',
				keyword: '',
				region: '',
				channel: [],
				quality: [],
				stage: [],
			},

			qualityMap, // 询盘质量
			businessQualityMap, // 商机质量
			// 阶段列表
			stageList,
			stageMap,
			// 来源列表
			sourceList,
			sourceMap,

			rowData: {},

			searchPopver: false,
			thSortVisibal: false,
			dialogEditWorkOrder: false,

			produreKey: 1,
			isEdit: false, //控制基本信息的编辑
			editRules: {
				staffName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
				phoneNo: [{ required: true, message: '请输入电话号码', trigger: 'blur' }],
			},
			moveTrue: false, //控制导出弹窗
			exportName: '', //控制导出什么数据
			showMap: {
				InquiryDetail: false,
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
	},
	// 监控data中的数据变化
	watch: {
		queryStr(newVal) {
			if (window.localStorage) {
				window.localStorage.setItem(this.activeTab + '_queryStr', JSON.stringify(newVal));
			} else {
				_.setCookie(this.activeTab + '_queryStr', newVal, 24 * 30);
			}
			this.queryTableData(newVal);
		},
		dateSelectObj: {
			handler(oldVal, newVal) {
				if (window.localStorage) {
					window.localStorage.setItem(this.activeTab + '_selectTime', JSON.stringify(newVal.selectTime));
				} else {
					_.setCookie(this.activeTab + '_selectTime', newVal.selectTime, 24 * 30);
				}
			},
			deep: true,
		},
		searchForm: {
			handler(oldVal, newVal) {
				if (window.localStorage) {
					window.localStorage.setItem(this.activeTab + '_stage', JSON.stringify(newVal.stage));
					window.localStorage.setItem(this.activeTab + '_quality', JSON.stringify(newVal.quality));
				} else {
					_.setCookie(this.activeTab + '_stage', newVal.stage, 24 * 30);
					_.setCookie(this.activeTab + '_quality', newVal.quality, 24 * 30);
				}
			},
			deep: true,
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.dateSelectObj = JSON.parse(window?.localStorage.getItem(this.activeTab + '_dateSelectObj')) || this.dateSelectObj;
		this.queryStr = JSON.parse(window?.localStorage.getItem(this.activeTab + '_queryStr')) || '';
		this.searchForm.quality = JSON.parse(window?.localStorage.getItem(this.activeTab + '_quality')) || [];
		this.searchForm.stage = JSON.parse(window?.localStorage.getItem(this.activeTab + '_stage')) || [];
		this.searchForm.channel = JSON.parse(window?.localStorage.getItem(this.activeTab + '_channel')) || [];
		this.getHosterList();
		this.queryUserByTwids();
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 查询渠道/代理下用户
		queryUserByTwids: _.debounce(function () {
			const str = JSON.stringify({
				twids: this.searchForm.twidList,
				counselor: '',
			});
			this.$axios
				.selectSalesmanByTwids(str)
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data || [];
					}
				})
				.catch(error => {
					console.log('selectSalesmanByTwids |' + error);
				});
		}),

		//切换tab页
		changeTab(tab, event) {},
		// 时间字符串转换成时间戳
		getDateTime(date, time) {
			if (date !== null) {
				const DATE = Number(new Date(date).getTime());
				if (time !== null) {
					const hour = String(time).split(':')[0] - 8;
					const min = String(time).split(':')[1];
					// let sec = String(time).split(":")[2]; + Number(sec)
					const TIME = (Number(hour * 60 * 60) + Number(min * 60)) * 1000;
					return DATE + TIME;
				}
			}
		},

		// 获取数据
		queryTableData: _.debounce(function (type) {
			window?.localStorage.setItem(this.activeTab + '_queryStr', JSON.stringify(this.queryStr));
			window?.localStorage.setItem(this.activeTab + '_dateSelectObj', JSON.stringify(this.dateSelectObj));
			window?.localStorage.setItem(this.activeTab + '_stage', JSON.stringify(this.searchForm.stage));
			window?.localStorage.setItem(this.activeTab + '_quality', JSON.stringify(this.searchForm.quality));
			window?.localStorage.setItem(this.activeTab + '_channel', JSON.stringify(this.searchForm.channel));
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectTeamworkInquiryDocumentaryInfo'; //接口
			const DATA = JSON.stringify({
				queryParam: this.queryStr,
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				hosterTwidList: this.hosterTwidList,
				...this.dateSelectObj,
				...this.searchForm,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							if (item.idTime) {
								item.idTime = item.idTime.replace('-', ':');
								item.idDateTime = this.getDateTime(item.idDate, item.idTime);
							}
							if (item.province == '其他') {
								item.region = '其他';
							} else {
								item.region = _.jointString('/', item.province, item.city, item.area);
							}
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 获取当前用户的托管方
		getHosterList() {
			this.hosterList = [];
			this.hosterTwidList = [];
			const str = JSON.stringify({});
			this.$axios
				.selectCurrentUserHoster(str)
				.then(res => {
					if (res.data.success) {
						this.hosterList = res.data.data;
						if (this.hosterList.length > 0) {
							// this.hosterTwidList = [this.hosterList[0]];
							this.hosterList.forEach(element => {
								this.hosterTwidList.push(element.twid);
							});
						}
					}
				})
				.catch(error => {
					console.log('selectCurrentUserHoster |' + error);
				});
		},
		dateFormat: _.dateFormat, //日期format
		jointString: _.jointString, //拼接字符串
		//详情弹窗
		openDetail(type, row) {
			this.showMap.InquiryDetail = true;
			this.$nextTick(() => {
				this.$refs.InquiryDetail.showDetailCom(type, row);
			});
		},

		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order);
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},

		//数据导出
		openExport: _.debounce(function () {
			const PROPS = {
				DATA: JSON.stringify({
					queryParam: this.queryStr,
					pageNum: this.tablePageForm.currentPage,
					pageSize: this.tablePageForm.pageSize,
					hosterTwidList: this.hosterTwidList,
					...this.dateSelectObj,
					...this.searchForm,
				}), //接口参数
				API: 'exportCooperInquiry', //导出接口
				downloadData: '托管询盘', //数据报表参数（后台确认字段downloadData）
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
#cooperateInquiry {
	width: 100%;
	overflow: hidden;
	position: relative;

	.p_expand {
		margin: 5px 20px;
		border: 1px solid #f5f5f5;
		padding: 10px;
		width: max-content;
		background: aliceblue;
		font-weight: 400;
	}
}
</style>
