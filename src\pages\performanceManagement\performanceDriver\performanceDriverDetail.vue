1
<template>
	<div :class="showCom ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<div class="detail-head">
				<span class="detail-title">{{ titleName }}询盘业绩与分配规则</span>
				<el-button type="text" class="el-icon-arrow-left" @click="closeDetailCom">返回</el-button>
			</div>
			<!-- 明细详情弹窗 -->
			<div class="detail-content">
				<p class="detail-content-title">基本信息 </p>
				<table class="base-table input-border-none" cellpadding="5" cellspacing="0">
					<tbody v-for="(tItem, index) in formList" :key="index">
						<!-- 标题 -->
						<tr>
							<th v-for="item in tItem" :key="item.prop" :colspan="item.colspan" :class="item.class">
								<div v-if="item.prop == 'intervalReviewRateJson'" class="flex-align-center gap-10">
									<span>{{ item.name }}</span>
									<el-button type="text" @click="addReviewRate" class="el-icon-plus m0 p0">添加点评率规则</el-button>
								</div>
								<span v-else>{{ item.name }}</span>
							</th>
						</tr>
						<!-- 输入框 -->
						<tr>
							<td v-for="item in tItem" :key="item.prop" :colspan="item.colspan">
								<!-- 只读文本 -->
								<Tooltips v-if="item.type == 'text'" :cont-str="detailForm[item.prop]" />
								<!-- 业务类型 -->
								<el-radio-group v-else-if="item.type == 'radio'" v-model="detailForm[item.prop]">
									<el-radio :label="0">直销</el-radio>
									<el-radio :label="1">合伙</el-radio>
								</el-radio-group>
								<!-- 选择器 -->
								<div v-else-if="item.type == 'select'">
									<el-select
										v-model="detailForm[item.prop]"
										:placeholder="item.name"
										popper-class="select-column-3"
										clearable
										filterable
									>
										<!-- 用户 -->
										<template v-if="item.prop == 'auid'">
											<el-option v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
										</template>
										<!-- 师带徒（用户） -->
										<template v-else-if="item.prop == 'consultantMasterUid'">
											<el-option v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
										</template>
										<!-- BPUL（用户） -->
										<template v-else-if="item.prop == 'businessPartnerUnitUid'">
											<el-option v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
										</template>
										<!-- 咨询类别 -->
										<div v-else-if="item.prop == 'consultantType'">
											<el-option label="无" :value="0"> </el-option>
											<el-option label="自己" :value="1"> </el-option>
											<el-option label="直销" :value="2"> </el-option>
											<el-option label="合伙人" :value="3"> </el-option>
											<el-option label="不限" :value="4"> </el-option>
										</div>
										<!-- 咨询级别 -->
										<div v-else-if="item.prop == 'consultingLevel'">
											<el-option label="初级咨询" :value="1"> </el-option>
											<el-option label="中级咨询" :value="2"> </el-option>
											<el-option label="高级咨询" :value="3"> </el-option>
										</div>
									</el-select>
								</div>

								<!-- 业务区域 -->
								<!-- <el-input
									v-else-if="item.prop == 'businessArea'"
									v-model="detailForm[item.prop]"
									placeholder="业务区域"
									clearable
								></el-input> -->

								<div v-else-if="item.prop == 'trainingPeriod'" class="input-border-none">
									<el-date-picker
										v-model="detailForm[item.prop]"
										type="daterange"
										range-separator="至"
										start-placeholder="开始日期"
										end-placeholder="结束日期"
										align="right"
										value-format="timestamp"
										unlink-panels
										:picker-options="periodOptions"
									>
									</el-date-picker>
								</div>

								<!-- 点评率规则 -->
								<div v-else-if="item.prop == 'intervalReviewRateJson'" class="flex-column gap-5 max-h-300 overflow-y-auto">
									<div v-for="(row, index) in detailForm[item.prop]" :key="index" class="flex-align-center gap-10">
										<!-- 月份范围 -->
										<el-date-picker
											class="W100 min-w-200"
											size="small"
											v-model="row.reviewRateTime"
											type="monthrange"
											align="right"
											unlink-panels
											range-separator="至"
											start-placeholder="开始月份"
											end-placeholder="结束月份"
											:picker-options="reviewRateOptions"
											@change="changeReviewRate(row, $event)"
										>
										</el-date-picker>
										<!-- 点评率 -->
										<div class="flex-align-center gap-5">
											<el-input
												class="W20 min-w-200"
												:class="{ 'input-border-red': !row.requiredReviewRate }"
												v-model="row.requiredReviewRate"
												size="small"
												placeholder="点评率"
											>
											</el-input>
											<span>%</span>
										</div>

										<el-button type="text" size="small" @click="delReviewRate(index)">删除</el-button>
									</div>
								</div>
								<!-- 默认输入框 -->
								<el-input
									v-else
									:disabled="item.disabled"
									v-model="detailForm[item.prop]"
									:placeholder="item.name"
									clearable
								></el-input>
							</td>
						</tr>
					</tbody>
				</table>
				<div class="bottom-button">
					<el-button v-if="titleName == '编辑'" class="mr20" @click="delDetail">删 除</el-button>
					<el-button @click="saveDetail" type="primary">保 存</el-button>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { resetValues, checkRequired, deepClone, dateFormat, jointString } from '@/util/tool';
import { mapGetters } from 'vuex';
export default {
	name: 'performanceDriverDetail',
	props: { userList: Array },
	data() {
		return {
			showCom: false, //控制弹窗显隐
			titleName: '',
			detailFormCopy: [],
			detailForm: {
				//明细详情
				auid: '',
				auname: '',
				businessArea: '',
				// businessAreaMap: {},
				businessType: 0,
				consultantCertificationApplyTime: '',
				consultantCertificationMemo: '',
				consultantCertificationStatus: '',
				consultantMasterName: '',
				consultantMasterUid: '',
				consultantReviewTime: '',
				consultantType: '',
				consultingCommissionDirectly: '',
				consultingCommissionOwnself: '',
				consultingCommissionPartner: '',
				consultingLevel: '',
				createTime: '',
				iapid: '',
				inquiryPoolSize: '',
				isdelete: '',
				monthlyAchievementRewards: '',
				monthlyGoals: '',
				partnerTaxRate: '',
				renewalFeeMes: '',
				renewalFeeOee: '',
				salesCommission: '',
				serviceCostRate: '',
				supplierPriceMes: '',
				supplierPriceOee: '',
				updateTime: '',

				// 后续添加的字段
				consultingPoolSize: '',
				inquiryEvaluateCount: '',

				businessProbationStart: '', // 实战培训期1
				businessProbationEnd: '', // 实战培训期1

				businessProbation2ndStart: '', // 实战培训期2
				businessProbation2ndEnd: '', // 实战培训期2

				businessPartnerUnitMaster1stCommission: '', // 师带徒实战1期提成%
				businessPartnerUnitMaster2ndCommission: '', // 师带徒实战2期提成%

				businessPartnerUnitTechnicianUid: '', // BPUT
				businessPartnerUnitTechnicianCommission: '', // BPUT%
			},
			formRules: {
				auid: [{ required: true, message: '请选择人员！', trigger: 'blur' }],
				businessArea: [{ required: true, message: '请输入业务区域！', trigger: 'blur' }],
				businessType: [{ required: true, message: '请选择业务类型！', trigger: 'blur' }],
				consultantType: [{ required: true, message: '请选择咨询类别！', trigger: 'blur' }],
			},

			// 实战培训期
			periodOptions: {
				shortcuts: [
					{
						text: '未来一周',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 7);
							picker.$emit('pick', [start, end]);
						},
					},
					{
						text: '未来一个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
							picker.$emit('pick', [start, end]);
						},
					},
					{
						text: '未来三个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
							picker.$emit('pick', [start, end]);
						},
					},
				],
			},
			// 点评率规则日期选择器
			reviewRateOptions: {
				shortcuts: [
					{
						text: '本月',
						onClick(picker) {
							picker.$emit('pick', [new Date(), new Date()]);
						},
					},
					{
						text: '今年至今',
						onClick(picker) {
							const end = new Date();
							const start = new Date(new Date().getFullYear(), 0);
							picker.$emit('pick', [start, end]);
						},
					},
					{
						text: '最近六个月',
						onClick(picker) {
							const end = new Date();
							const start = new Date();
							start.setMonth(start.getMonth() - 6);
							picker.$emit('pick', [start, end]);
						},
					},
				],
				// 如果已经存在范围内，则不能选择
				disabledDate: time => {
					const currentMonth = this.$moment(time).startOf('month').valueOf();
					// 检查当前选择的月份是否会与已有的任何区间产生交叉
					return this.detailForm.intervalReviewRateJson.some(item => {
						// 跳过没有完整时间范围的项
						if (!item.startTime || !item.endTime) return false;
						// 如果当前日期在任何已存在的区间内，则禁用
						return currentMonth >= item.startTime && currentMonth <= item.endTime;
					});
				},
			},
		};
	},
	created() {},
	computed: {
		// 表单
		formList() {
			const normalList = [
				{ name: '姓名', prop: 'auid', class: 'label-required W10', type: 'select' },
				{ name: '业务区域', prop: 'businessArea', class: 'label-required W30', colspan: 3 },
				{ name: '业务类型', prop: 'businessType', class: 'label-required  W10', type: 'radio' },
				{ name: '询盘池', prop: 'inquiryPoolSize', class: 'W10' },
				{ name: '咨询类别', prop: 'consultantType', class: 'label-required W10', type: 'select' },
				{ name: '咨询池', prop: 'consultingPoolSize', class: 'W10' },
				{ name: '咨询级别', prop: 'consultingLevel', class: 'W10', type: 'select' },
				{ name: '评价数', prop: 'inquiryEvaluateCount', class: 'W10' },
			];

			let directList = []; // 直销表单 businessType == 0
			let partnerList = []; // 合伙表单 businessType == 1
			if (this.detailForm.businessType == 0) {
				// 直销表单
				directList = [
					{ name: '师带徒', prop: 'consultantMasterUid', class: 'W10', type: 'select' },
					{ name: 'BPUL', prop: 'businessPartnerUnitUid', class: 'W10', type: 'select' },
					{ name: 'BPUL%', prop: 'businessPartnerUnitCommission', class: 'W10' },
					{ name: '实施提成%', prop: 'implementCommission', class: 'W10' },
					{
						name: '自有咨询提成%',
						prop: 'consultingCommissionOwnself',
						class: 'W10',
						disabled: this.detailForm.consultantType == 3 ? true : false,
					},
					{
						name: '直销咨询提成%',
						prop: 'consultingCommissionDirectly',
						class: 'W10',
						disabled: this.detailForm.consultantType == 3 ? true : false,
					},
					{ name: '合伙咨询提成%', prop: 'consultingCommissionPartner', class: 'W10' },
					{ name: '月度目标(元)', prop: 'monthlyGoals', class: 'W10' },
					{ name: '月达标奖励(元)', prop: 'monthlyAchievementRewards', class: 'W10' },
					{ name: '业务提成%', prop: 'salesCommission', class: 'W10' },
				];
			} else if (this.detailForm.businessType === 1) {
				partnerList = [
					[
						{ name: '实战培训期', prop: 'trainingPeriod', class: 'W10', colspan: 2 },
						{ name: '师带徒', prop: 'consultantMasterUid', class: 'W10', type: 'select' },
						{ name: 'BPUL', prop: 'businessPartnerUnitUid', class: 'W10', type: 'select' },
						{ name: 'BPUL%', prop: 'businessPartnerUnitCommission', class: 'W10' },
						{ name: '实施提成%', prop: 'implementCommission', class: 'W10' },
						{
							name: '自有咨询提成%',
							prop: 'consultingCommissionOwnself',
							class: 'W10',
							disabled: this.detailForm.consultantType == 3 ? true : false,
						},
						{
							name: '直销咨询提成%',
							prop: 'consultingCommissionDirectly',
							class: 'W10',
							disabled: this.detailForm.consultantType == 3 ? true : false,
						},
						{ name: '合伙咨询提成%', prop: 'consultingCommissionPartner', class: 'W10' },
						{ name: '合伙税率%', prop: 'partnerTaxRate', class: 'W10' },
					],
					[
						{ name: '服务成本%', prop: 'serviceCostRate', class: 'W10' },
						{ name: 'MES供货价(元)', prop: 'supplierPriceMes', class: 'W10' },
						{ name: 'OEE供货价(元)', prop: 'supplierPriceOee', class: 'W10' },
						{ name: 'MES续费(元)', prop: 'renewalFeeMes', class: 'W10' },
						{ name: 'OEE续费(元)', prop: 'renewalFeeOee', class: 'W10' },
						{ name: '', prop: 'empty', class: 'W10', colspan: 5 },
					],
				];
			}

			// 附加表单
			const appendList = [
				{ name: '点评率规则', prop: 'intervalReviewRateJson', class: 'W10', colspan: 5 },
				{ name: '', prop: '', class: 'W10', colspan: 5, type: 'empty' },
			];
			return [normalList, directList, ...partnerList, appendList];
		},
		//业务顾问列表(标签：业务)
		// salesmanList() {
		// 	return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		// },
	},
	watch: {
		showCom(val) {
			if (!val) {
				this.detailForm = resetValues(this.detailForm); //重置对象
				this.$emit('close');
			}
		},
	},
	mounted() {},
	methods: {
		// 点评率规则
		changeReviewRate(row, event) {
			const [start, end] = event;
			const startTime = this.$moment(start).startOf('month').valueOf() || '';
			const endTime = this.$moment(end).endOf('month').valueOf() || '';

			// 检查是否与现有时间范围重叠
			const hasOverlap = this.detailForm.intervalReviewRateJson.some(item => {
				// 跳过当前项和无效项
				if (item === row || !item.startTime || !item.endTime) return false;

				// 检查是否有重叠：
				// 1. 新范围的开始时间在已有范围内
				// 2. 新范围的结束时间在已有范围内
				// 3. 新范围完全包含已有范围
				return (
					(startTime >= item.startTime && startTime <= item.endTime) ||
					(endTime >= item.startTime && endTime <= item.endTime) ||
					(startTime <= item.startTime && endTime >= item.endTime)
				);
			});

			if (hasOverlap) {
				this.$message.warning('选择的月份范围与已有范围重叠，请重新选择');
				// 重置为原始值或清空
				row.reviewRateTime = row.startTime && row.endTime ? [row.startTime, row.endTime] : [];
			} else {
				// 更新时间范围
				row.startTime = startTime;
				row.endTime = endTime;
				row.reviewRateTime = [startTime, endTime];
			}
		},
		// 添加点评率
		addReviewRate() {
			this.detailForm.intervalReviewRateJson.push({
				reviewRateTime: [],
				requiredReviewRate: '',
			});
		},
		// 删除点评率
		delReviewRate(index) {
			this.detailForm.intervalReviewRateJson.splice(index, 1);
		},
		// 添加/保存信息
		saveDetail(isClose = true) {
			if (checkRequired(this.detailForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}
			if (!this.detailForm.consultantMasterUid) {
				this.detailForm.consultantMasterName = '';
			}
			if (this.detailForm.trainingPeriod && this.detailForm.trainingPeriod?.length > 0) {
				this.detailForm.businessProbationStart = this.detailForm?.trainingPeriod[0];
				this.detailForm.businessProbationEnd = this.detailForm?.trainingPeriod[1];
			} else {
				this.detailForm.businessProbationStart = '';
				this.detailForm.businessProbationEnd = '';
			}

			// 点评率规则（JSON字符串） 不保存reviewRateTime
			const intervalReviewRateJson =
				this.detailForm?.intervalReviewRateJson?.length > 0
					? JSON.stringify(
							this.detailForm.intervalReviewRateJson.map(item => ({
								requiredReviewRate: item.requiredReviewRate,
								startTime: item.startTime || item.reviewRateTime[0] || '',
								endTime: item.endTime || item.reviewRateTime[1] || '',
							})),
						)
					: '';

			// 将业务区域的，都转换成，半角字符,
			this.detailForm.businessArea = this.detailForm.businessArea?.replace(/，/g, ',');
			const API = !this.detailForm.iapid
				? 'addInquiryAllocationPerformanceIndicator'
				: 'updateInquiryAllocationPerformanceIndicator';
			this.$axios[API](JSON.stringify({ ...this.detailForm, intervalReviewRateJson }))
				.then(res => {
					if (res.data.success) {
						isClose && (this.showCom = false);
						this.$succ('保存成功！');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} | ` + error);
				});
		},
		// 删除信息
		delDetail() {
			this.$confirm('该数据将被删除，删除后数据不可恢复', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
			})
				.then(() => {
					this.$axios
						.deleteInquiryAllocationPerformanceIndicator(JSON.stringify({ iapid: this.detailForm.iapid }))
						.then(res => {
							if (res.data.success) {
								this.showCom = false;
								this.$succ(res.data.message);
							} else {
								this.$err(res.data.message);
							}
						})
						.catch(error => {
							console.log('deleteInquiryAllocationPerformanceIndicator |' + error);
						});
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},

		//显示弹窗
		showDetailCom(action, rowData, businessType = 0) {
			this.titleName = action;
			if (rowData) {
				this.detailForm = deepClone(rowData);
				this.detailForm.auid = action == '复制' ? '' : this.detailForm.auid;
				this.detailForm.iapid = action == '复制' ? '' : this.detailForm.iapid;
				this.detailFormCopy = deepClone(this.detailForm);
			} else {
				this.detailForm.businessType = businessType ? businessType : 0;
				this.detailFormCopy = deepClone(this.detailForm);
			}

			this.showCom = true;
		},

		//点击返回
		closeDetailCom() {
			let isSameData = true;
			isSameData = JSON.stringify(this.detailForm) == JSON.stringify(this.detailFormCopy);
			console.log('isSameData', isSameData);
			if (!isSameData) {
				this.$confirm(`当前页面有内容修改但未保存，是否继续退出（不保存）操作？`, '重要提醒', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				})
					.then(() => {
						this.showCom = false;
					})
					.catch(() => {
						this.$message.info('取消返回！');
						return;
					});
			} else {
				this.showCom = false;
			}
		},
		dateFormat, //日期format
		jointString, //字符串拼接
	},
};
</script>
