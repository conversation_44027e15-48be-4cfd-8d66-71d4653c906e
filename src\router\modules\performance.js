/* 业务顾问管理路由 */

const performanceDriver = () => import('@/pages/performanceManagement/performanceDriver/performanceDriver.vue'); //业绩驱动器
const settleAccounts = () => import('@/pages/performanceManagement/settleAccounts.vue'); //业绩结算
const settlementDetails = () => import('@/pages/performanceManagement/settlementDetails/settlementDetails.vue'); //业绩结算明细
const individualPoints = () => import('@/pages/performanceManagement/individualPoints/IndividualPoints.vue'); //个人积分
const HealthDegreeOfBP = () => import('@/pages/performanceManagement/HealthDegreeOfBP/HealthDegreeOfBP.vue'); //BP健康度

const routers = [
	{
		//业绩驱动器
		path: '/performanceDriver',
		name: 'performanceDriver',
		component: performanceDriver,
		meta: {
			parentTitle: '业绩管理',
			title: '业绩驱动器',
		},
	},
	{
		//业绩结算
		path: '/settleAccounts',
		name: 'settleAccounts',
		component: settleAccounts,
		meta: {
			parentTitle: '业绩管理',
			title: '业绩结算',
		},
	},
	{
		//业绩结算明细
		path: '/settlementDetails',
		name: 'settlementDetails',
		component: settlementDetails,
		meta: {
			parentTitle: '业绩管理',
			title: '业绩结算明细',
		},
	},
	{
		//个人积分
		path: '/individualPoints',
		name: 'individualPoints',
		component: individualPoints,
		meta: {
			parentTitle: '业绩管理',
			title: '个人积分',
		},
	},
	{
		//BP健康度监测表
		path: '/HealthDegreeOfBP',
		name: 'HealthDegreeOfBP',
		component: HealthDegreeOfBP,
		meta: {
			parentTitle: '业绩管理',
			title: 'BP健康度监测表',
		},
	},
];

export default routers;
