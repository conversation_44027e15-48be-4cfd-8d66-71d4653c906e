/* 
	 项目中常用或者使用频率较高的工具函数（必须掌握他们的使用方式和场景）
*/

import { Message } from 'element-ui';
import moment from 'moment';
import 'moment/locale/zh-cn';

/**
 * moment.js 转换日期format (按需添加，已有规则请勿删改)
 * @param Date,String
 * @returns
 */
export function dateFormat(timestamp, type) {
	if (!timestamp) {
		return '';
	}
	const date = Number(timestamp);
	const dataMap = {
		MD: 'MM/DD',
		MDHM: 'MM/DD HH:mm',
		MDS: 'MM/DD HH:mm:ss',
		HMS: 'HH:mm:ss',
		line: 'YYYY-MM-DD',
		lineM: 'YYYY-MM-DD HH:mm',
		HM: 'HH:mm',
		yyyy: 'YYYY',
		YM: 'YYYY-MM',
		ALL: 'YYYY-MM-DD HH:mm:ss',
		// 显示星期几 中文
		YMDddddHm: 'YYYY-MM-DD dddd HH:mm',
	};
	const formattedDate = moment(date)
		.locale('zh-cn')
		.format(dataMap[type] || 'YYYY/MM/DD');

	return formattedDate;
}

/**
 * @param {Number} time 时间戳/时间差
 * @param {String} unit 单位：'ms' 表示毫秒，'s' 表示秒
 * @param {String} format 格式：'hh:mm:ss', 'mm:ss', '时分秒'
 * @returns format as "hh:mm:ss", "mm:ss", or "时分秒"
 */
export function timeFormat(time, unit = 's', format = 'hh:mm:ss') {
	let seconds = unit === 's' ? Math.floor(time) : Math.floor(time / 1000);
	const hours = Math.floor(seconds / 3600);
	seconds %= 3600;
	const minutes = Math.floor(seconds / 60);
	seconds %= 60;

	if (format === 'hh:mm:ss') {
		return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
	} else if (format === 'mm:ss') {
		return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
	} else if (format === '时分秒') {
		return `${hours}小时${minutes}分${seconds}秒`;
	} else if (format === '时分') {
		return `${hours}小时${minutes}分`;
	} else if (format === '分秒') {
		return `${minutes}分${seconds}秒`;
	} else {
		// 默认情况下，根据时间长度选择格式
		if (hours > 0) {
			return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
		} else if (minutes > 0) {
			return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
		} else {
			return `${seconds}`; // 直接返回秒数
		}
	}
}
/**
 * 防抖函数，确保只有在一段时间的不活动后（即最后一次）才执行。
 *
 * @param {function|string} fn - 需要防抖的函数。如果提供的是字符串，则假设该字符串是调用对象的方法。
 * @param {number} [wait=600] - 执行防抖函数之前等待的毫秒数。
 * @return {function} -  防抖函数。
 */
export function debounce(fn, wait = 600) {
	let timeout = null;
	return function () {
		const self = this,
			args = arguments;
		clearTimeout(timeout);
		timeout = setTimeout(() => {
			if (typeof fn === 'string') {
				self[fn](...args);
			} else if (typeof fn === 'function') {
				fn.apply(self, args);
			}
		}, wait);
	};
}

/**
 * 节流函数，确保函数在一段时间内只执行一次。
 *
 * @param {function} fn - 需要节流的函数。
 * @param {number} wait - 执行函数的时间间隔，单位毫秒。
 * @return {function} - 节流后的函数。
 */
export function throttle(fn, wait = 600) {
	let inThrottle;

	return function () {
		const context = this;
		const args = arguments;

		if (!inThrottle) {
			fn.apply(context, args);
			inThrottle = true;

			setTimeout(() => {
				inThrottle = false;
			}, wait);
		}
	};
}

/**
 * 深度克隆一个对象或数组。
 *
 * @param {object|array} obj - 要进行深度克隆的对象或数组。
 * @return {object|array} - 深度克隆的对象或数组。
 */
export function deepClone(obj) {
	//判断拷贝的要进行深拷贝的是数组还是对象，是数组的话进行数组拷贝，对象的话进行对象拷贝
	const objClone = Array.isArray(obj) ? [] : {};
	//进行深拷贝的不能为空，并且是对象或者是数组
	if (obj && typeof obj === 'object') {
		for (const key in obj) {
			if (obj.hasOwnProperty(key)) {
				if (obj[key] && typeof obj[key] === 'object') {
					objClone[key] = deepClone(obj[key]);
				} else {
					objClone[key] = obj[key];
				}
			}
		}
	}
	return objClone;
}

/**
 * 重置对象的值。
 *
 * @param {Object} obj - 需要重置值的对象。
 * @return {Object}  - 重置后的对象。
 */
export function resetValues(obj) {
	if (obj instanceof Date) {
		return '';
	}

	if (Array.isArray(obj)) {
		return [];
	}
	if (typeof obj === 'function') {
		return function () {};
	}
	if (typeof obj === 'object' && obj !== null) {
		return Object.fromEntries(Object.entries(obj).map(([key, value]) => [key, resetValues(value)]));
	}

	return '';
}

/**
 * 检查表单中是否有任何必填字段为空，并输出相应的错误消息。
 *
 * @param {Object} editForm - 表单.
 * @param {Object} formRules - 表单规则.
 * @returns {boolean} - 返回是否有空字段。
 */
export function checkRequired(editForm, formRules) {
	try {
		for (const [field, rules] of Object.entries(formRules)) {
			const isEmpty = rules.some(rule => rule.required && (editForm[field] === null || editForm[field] === ''));
			if (isEmpty) {
				Message.warning(rules.find(rule => rule.required).message);
				return true;
			}
		}
	} catch (error) {
		console.log('🚨', { error });
	}

	return false; // 返回false表示没有空字段
}

/**
 *根据指定的属性和顺序对给定的表格数据进行排序。 *
 *@param {Array} tableData - 需要排序的数据。
 *@param {string} prop - 根据该属性对数据进行排序。
 *@param {string} order - 数据排序的顺序（'ascending' 或 'descending'）。
 *@param {function} newLogic - 新增/覆盖比较逻辑(必须要返回null)。
 *@return {Array} 排序后的表格数据。
 */
export function sortTableData(tableData, prop, order, newLogic) {
	// 如果没有排序条件，直接返回原数据
	if (!order || !prop) {
		return tableData;
	}

	// 比较两个值
	const compareValues = (a, b) => {
		if (newLogic && typeof newLogic === 'function') {
			const customResult = newLogic(a, b);
			if (customResult != null) {
				return customResult; // 调用自定义逻辑
			}
		}

		// 统一处理空值
		const aIsEmpty = a === null || a === '' || a === undefined;
		const bIsEmpty = b === null || b === '' || b === undefined;

		if (aIsEmpty && bIsEmpty) return 0;
		if (aIsEmpty) return -1;
		if (bIsEmpty) return 1;

		//数字字符串比较
		if (typeof a === 'string' && typeof b === 'string') {
			const aIsNumber = /^-?\d+(\.\d+)?$/.test(a);
			const bIsNumber = /^-?\d+(\.\d+)?$/.test(b);

			if (aIsNumber && bIsNumber) {
				return Number(a) - Number(b);
			}
			// 普通字符串比较
			return a.localeCompare(b, 'zh-CN');
		}

		// 不同类型比较
		if (typeof a === 'number' && typeof b === 'number') return a - b;
		if (a instanceof Date && b instanceof Date) return a.getTime() - b.getTime();
		if (Array.isArray(a) && Array.isArray(b)) return a.length - b.length;

		// 默认字符串比较
		return String(a).localeCompare(String(b), 'zh-CN');
	};

	// 只在需要时进行深拷贝
	const dataToSort = order === 'ascending' ? [...tableData] : [...tableData];

	return dataToSort.sort((a, b) => {
		const aValue = a[prop];
		const bValue = b[prop];

		if (order === 'ascending') {
			return compareValues(aValue, bValue); //升序
		} else if (order === 'descending') {
			return compareValues(bValue, aValue); // 降序
		}
		return 0;
	});
}

/**
 * 使用可选的符号将多个字符串连接在一起
 *
 * @param {string} symbol - 插入在每个字符串之间的符号。默认为 '/'
 * @param {...string} strings - 要连接的字符串
 * @return {string} 连接后的字符串
 */
export function jointString(symbol = ' / ', ...strings) {
	const filteredStrings = strings.filter(str => str && String(str) && String(str)?.trim() !== '');
	if (filteredStrings.length === 0) {
		return '';
	}

	const resultStr = filteredStrings.join(symbol);
	return resultStr;
}

/**
 * 根据指定键去重返回一个新的数组，该数组仅包含根据指定键唯一的对象。
 *
 * @param {Array} array - 要过滤的数组。
 * @param {string} key - 用于比较对象的键。
 * @return {Array} -一个包含唯一对象的新数组。
 *
 * */
export function uniqueArrayByKey(array, key) {
	return array.reduce((result, item) => {
		const foundItem = result.find(element => element[key] === item[key]);
		if (!foundItem) {
			result.push(item);
		}
		return result;
	}, []);
}

/**
 * 复制文本到粘贴板
 *
 * @param {string} text - 要复制的文本。
 * @param {string} message - 复制成功后的提示消息。
 * @return {void} - 无返回值。
 *
 * */
export function copyToClipboard(text, message = '复制成功，文本已经复制到粘贴板了！') {
	const unsecuredCopyToClipboard = text => {
		const textArea = document.createElement('textarea');
		textArea.value = text;
		document.body.appendChild(textArea);
		textArea.focus();
		textArea.select();
		try {
			document.execCommand('copy');
		} catch (err) {
			console.error('复制失败', err);
		}
		document.body.removeChild(textArea);
	};

	// HTTP下不允许使用navigator.clipboard
	if (window.isSecureContext && navigator.clipboard) {
		try {
			navigator.clipboard.writeText(text);
			console.log('复制成功：', text);
			message && Message.success(message);
		} catch (err) {
			console.error('复制失败: ', err);
		}
	} else {
		unsecuredCopyToClipboard(text);
		console.log('复制成功：', text);
		message && Message.success(message);
	}
}

/**
 * 下载服务器上的文件
 * @param {string} origin - 服务器地址
 * @param {string} fileName - 文件名称，不需要包含扩展名
 * @param {string} fileExt - 文件扩展名，默认为xlsx
 * @param {string} target - 打开方式，默认为_self
 * @returns {void}
 */
export function downloadFile(origin = window.location.origin, fileName, fileExt = 'xlsx', target = '_self') {
	if (!fileName) return console.log('文件名称不能为空！');
	window.open(origin + '/template/' + fileName + '.' + fileExt, target);
}
