<template>
	<div class="ResourcesTree">
		<div class="WebTree W35 min-w-350" v-show="treeType == 0">
			<div class="tree-title">标品MES - Web 🖥️</div>
			<el-tree
				ref="webTree"
				node-key="rcid"
				:indent="0"
				:data="WebTreeList"
				:render-content="renderContent"
				:default-expanded-keys="expandedKeys"
				show-checkbox
				@check="checkNode"
				:props="{
					label: 'resourceName',
					children: 'resources',
					disabled: () => {
						return !isSuperAdmin;
					},
				}"
			></el-tree>
		</div>

		<div class="AppTree W35 min-w-350" v-show="treeType == 0">
			<div class="tree-title">标品MES - App 📱 </div>
			<el-tree
				ref="appTree"
				node-key="rcid"
				:indent="0"
				:data="AppTreeList"
				:render-content="renderContent"
				:default-expanded-keys="expandedKeys"
				show-checkbox
				@check="checkNode"
				:props="{
					label: 'resourceName',
					children: 'resources',
					disabled: () => {
						return !isSuperAdmin;
					},
				}"
			></el-tree>
		</div>
		<div class="WmsTree W35 min-w-350" v-show="treeType == 1">
			<div class="tree-title">标品WMS - Web 🖥️</div>
			<el-tree
				ref="wmsTree"
				node-key="rcid"
				:indent="0"
				:data="WmsTreeList"
				:render-content="renderContent"
				:default-expanded-keys="expandedKeys"
				show-checkbox
				@check="checkNode"
				:props="{
					label: 'resourceName',
					children: 'resources',
					disabled: () => {
						return !isSuperAdmin;
					},
				}"
			></el-tree>
		</div>
	</div>
</template>
<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import { jointString } from '@/util/tool';

export default {
	name: 'ResourcesTree',
	components: {},
	props: {
		// 资源类型
		treeType: {
			type: Number,
			default: 0,
		},
		// 编辑权限(团队主数据不限制，我的客户等其他页面做对应的限制)
		noLimit: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			tid: '', //团队id
			WebTreeList: [], //标品MES 资源
			AppTreeList: [], //app资源
			WmsTreeList: [], //wms资源

			expandedKeys: [], //展开的节点
			checkedList: [], //选中的节点
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']),

		// 是超级管理 可编辑所有内容
		isSuperAdmin() {
			const roleList = this.userInfos?.adminUserVO.adminRoleVOS || null;
			return roleList?.some(item => item.arid == 1 || item.roleName == '超级管理员') || this.noLimit || false;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {},
	// 生命周期 - 挂载之前
	beforeMount() {},
	// 生命周期 - 销毁之前
	beforeDestroy() {
		this.clearTree();
	},
	// 生命周期 - 销毁完成
	destroyed() {},
	// 方法集合
	methods: {
		// 树节点渲染
		renderContent(h, { node, data, store }) {
			const introduction = data.introduction ? `简介：${data.introduction}` : '';
			const extension = data.resourceExtension ? `备注：${data.resourceExtension}` : '';
			const text = jointString(' / ', introduction, extension);
			const textArr = [
				data.introduction ? { title: '简介', content: data.introduction } : null,
				data.resourceExtension ? { title: '备注', content: data.resourceExtension } : null,
			].filter(Boolean);
			return (
				<span class={`W90 flex-align-center color-999  ${node.isLeaf ? 'fs-12 no-children' : 'fs-14'}`}>
					<span class={`color-666 mr10`}>{data.resourceName}</span>
					<Tooltips class="inline-block flex-1" cont-str={text} cont-obj={textArr} cont-width={250} />
				</span>
			);
		},
		// 获取选中的资源id beHave == 1 的资源
		getSelectedRcids(treeData, children = 'resources') {
			const extractRcids = nodes => {
				return nodes.reduce((acc, node) => {
					if (node.beHave == 1 && !node[children]?.length) {
						acc.push(node.rcid);
					}
					if (node[children]) {
						acc.push(...extractRcids(node[children]));
					}
					return acc;
				}, []);
			};

			const allRcids = extractRcids(treeData); // 所有选中的资源 ID

			return { allRcids };
		},

		//查询团队资源列表
		queryResource({ tid }) {
			if (!tid) return;
			this.tid = tid;
			this.expandedKeys = [];
			this.checkedList = [];

			this.$axios
				.getAllResources(JSON.stringify({ tid }))
				.then(res => {
					if (res.data.success) {
						if (res.data.data) {
							this.WebTreeList = res.data.data?.web ? res.data.data.web.resources : [];
							this.AppTreeList = res.data.data?.app ? res.data.data.app.resources : [];
							this.WmsTreeList = res.data.data?.wms ? res.data.data.wms.resources : [];

							const treeData = [...this.WebTreeList, ...this.AppTreeList, ...this.WmsTreeList];
							const { allRcids } = this.getSelectedRcids(treeData);
							// this.expandedKeys = allRcids || [];
							this.checkedList = allRcids || [];

							this.$nextTick(() => {
								this.$refs.webTree.setCheckedKeys(this.checkedList);
								this.$refs.appTree.setCheckedKeys(this.checkedList);
								this.$refs?.wmsTree?.setCheckedKeys(this.checkedList);
							});
						}
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('getAllResources |' + error);
				});
		},

		// 树节点选中事件
		checkNode(data, node) {
			const rcid = data.rcid;
			const API = node.checkedKeys.includes(rcid) ? 'addTeamResources' : 'deleteTeamResources';
			this.$axios[API](JSON.stringify({ tid: this.tid, rcid }))
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},

		// 清空树
		clearTree() {
			this.WebTreeList = [];
			this.AppTreeList = [];
			this.WmsTreeList = [];

			this.expandedKeys = [];
			this.checkedList = [];
		},
	},
};
</script>

<style lang="scss" scoped>
.ResourcesTree {
	display: flex;
	position: relative;
	gap: 20px;
	color: #666;

	.WebTree,
	.AppTree,
	.WmsTree {
		position: relative;
		border: 1px solid #e6e6e6;
		border-radius: 4px;
		padding: 10px;

		// max-height: 800px;
		// overflow-y: auto;

		.tree-title {
			font-size: 14px;
			font-weight: 600;
			padding-bottom: 5px;

			position: sticky;
			top: -5px;
			background-color: #fff;
			z-index: 6;
		}
	}
}
</style>
