<template>
	<!-- 跟单信息组件 -->
	<div id="FollowInfo" v-loading.lock="isUploading">
		<table class="base-table input-border-none" cellpadding="5" cellspacing="0">
			<tr>
				<th class="W60 p0">
					<div class="flex-align-center flex-wrap min-h-50">
						<div class="flex-align-center m5">
							<span class="label-required pl10 min-w-50">日期</span>
							<el-date-picker
								:disabled="isPublicInquiry"
								class="min-w-100 vw7"
								size="mini"
								v-model="followForm.documentaryTime"
								value-format="timestamp"
								type="datetime"
								format="yyyy-MM-dd HH:mm"
								placeholder="选择日期"
								:clearable="false"
								:default-value="new Date()"
							></el-date-picker>
						</div>

						<div class="flex-align-center m5">
							<span class="label-required pl10 min-w-50">方式</span>
							<el-select
								:disabled="isPublicInquiry"
								v-model="followForm.aupcid"
								size="mini"
								class="min-w-100 vw7"
								placeholder="跟单方式"
								popper-class="select-column-w200-3"
								clearable
								filterable
								@change="changeScoreConfig"
							>
								<el-option v-for="item in scoreConfigList" :key="item.aupcid" :label="item.cfgName" :value="item.aupcid">
									<span> {{ item.cfgName }} </span>
									<span :class="item.cfgHasFacilitator ? 'el-icon-user ml10' : ''"> </span>
									<span :class="item.cfgAttachments ? 'el-icon-paperclip ml10' : ''"> </span>
								</el-option>
							</el-select>

							<el-select
								v-if="followForm.cfgHasFacilitator"
								size="mini"
								class="min-w-200 vw7 ml10"
								popper-class="select-column-3"
								v-model="followForm.facilitatorUidArray"
								placeholder="协助人"
								multiple
								collapse-tags
								clearable
								filterable
								@change="followForm.facilitatorUids = $event?.join(',') || ''"
							>
								<el-option v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid"> </el-option>
							</el-select>
							<!-- 附件已上传 -->
							<div v-else-if="followForm.recordAttachment">
								<!-- 如果是音频，则显示播放按钮 -->
								<div v-if="followForm.recordType == '电话跟单'">
									<!-- 录音播放器 -->
									<AudioPlayer :audioUrl="followForm.recordAttachment" />
								</div>
								<FilePopover v-else class="inline-block max-w-200 ml10" :url="followForm.recordAttachment" :isIcon="true" />
							</div>

							<el-upload
								v-if="followForm.cfgAttachments"
								ref="upload"
								action=""
								:accept="fileTypes"
								:http-request="uploadFile"
								:show-file-list="false"
							>
								<el-button size="mini" type="warning" class="ml10">
									{{ !followForm.recordAttachment ? '上传' : '替换'
									}}{{ followForm.recordType == '电话跟单' ? '录音 📞' : '附件 📄' }}
								</el-button>
							</el-upload>
						</div>

						<div v-if="planList.length" class="flex-align-center m5">
							<span class="pl10 min-w-50">计划</span>
							<el-select
								:disabled="isPublicInquiry"
								v-model="planForm.drid"
								placeholder="请选择进行中的计划"
								class="min-w-100 vw7"
								size="mini"
								clearable
								filterable
								@change="changePlan"
							>
								<el-option v-for="item in planList" :key="item.drid" :label="item.nextPlan" :value="item.drid">
									<Tooltips
										class="max-w-500"
										:cont-str="`${item.nextPlan} / ${dateFormat(item.nextStep, 'line')}`"
										:cont-width="480"
									/>
								</el-option>
							</el-select>
							<i
								:disabled="isPublicInquiry"
								v-show="planForm.drid"
								class="el-icon-edit-outline hover-green ml10"
								@click="openDialog('计划', planForm)"
							></i>
						</div>
					</div>
				</th>
				<th class="W40">
					<div class="flex-align-center">
						<span class="label-required min-w-100">下一步计划</span>
						<el-date-picker
							:disabled="isPublicInquiry"
							class="min-w-100 vw10"
							size="mini"
							v-model="followForm.nextStep"
							value-format="timestamp"
							type="datetime"
							format="yyyy-MM-dd HH:mm"
							placeholder="选择日期后再输入内容"
							:picker-options="pickerOptions"
							:default-value="$moment(new Date()).endOf('day').valueOf()"
							default-time="23:59:59"
						></el-date-picker>
					</div>
				</th>
			</tr>
			<tr>
				<td>
					<el-input
						ref="focusInput"
						:disabled="!followForm.documentaryTime || isPublicInquiry"
						class="mr20"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						v-model="followForm.content"
						placeholder="请输入跟单情况..."
					></el-input>
				</td>
				<td>
					<el-input
						:disabled="!followForm.nextStep || isPublicInquiry"
						class="mr20"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						v-model="followForm.nextPlan"
						placeholder="请输入以何种方式联系谁达成什么目标,如：微信联系张总了解其对报价单的看法"
					></el-input>
				</td>
			</tr>
		</table>
		<div class="bottom-button" v-show="!isPublicInquiry" :class="{ mt0: !isPublicInquiry && showTips }">
			<div class="fs-12 red" v-show="showTips">* 当前阶段发布跟单时，下一步计划不能为空！</div>
			<div class="fs-12 red" v-show="isFollowPlan">* 当前选择了跟单计划，发布跟单后会自动将该跟单计划关闭！</div>
			<el-button class="ml-auto" @click="savePublish" :disabled="!isPublish" :type="isPublish ? 'primary' : ''"
				>发布跟单
			</el-button>
		</div>

		<!-- 跟单记录 -->
		<FollowRecord
			ref="FollowRecord"
			:idid="idid"
			:detailForm="detailForm"
			:newDocumentaryRecordsList="newDocumentaryRecordsList"
			@refresh="$emit('refresh')"
		/>
	</div>
</template>
<script>
import { debounce, dateFormat, jointString, resetValues, checkRequired, deepClone } from '@/util/tool'; //按需引入常用工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import eventBus from '../eventBus';
import FollowRecord from './FollowRecord.vue';
import FilePopover from '@/components/FilePopover.vue';
import AudioPlayer from '@/components/AudioPlayer'; // 录音播放器

export default {
	name: 'FollowInfo',
	components: {
		FollowRecord,
		FilePopover,
		AudioPlayer,
	},
	props: {
		// 询盘主键
		idid: {
			type: [String, Number],
			default: '',
		},
		// 是否公海询盘
		isPublicInquiry: { type: Boolean, default: false },
		// 跟单记录
		documentaryRecordsList: {
			type: Array,
			default: () => [],
		},
		// 用户列表
		userList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			detailForm: {}, //事件总线表单

			scoreConfigList: [], //积分配置

			// 跟单/计划表单(非弹窗的)
			followForm: {
				content: '',
				idid: '',
				drid: '',
				ckrid: '',
				nextPlan: '',
				nextStep: '',
				documentaryTime: '',

				aupcid: '', //积分配置项id
				recordType: '', // 跟单类型 冗余字段，来自积分配置项 cfg_name
				recordAttachment: '', //  依据积分配置项cfgAttachments来判断是否需要上传附件
				facilitatorUids: '', //是否需要协助人cfgHasFacilitator，多个协助人因为逗号隔开 eg: 1001,1002
				facilitatorUidArray: [], //显示和转换用

				cfgAttachments: '', // 需附件
				cfgHasFacilitator: '', // 需协助人
			},

			// 跟单/计划弹窗表单
			planForm: {
				content: '',
				drid: '',
				nextPlan: '',
				nextStep: '',
				planStatus: 0,
				documentaryTime: '',
			},
			isUploading: false, //上传loading
			travelListMap: new Map(), //出差申请列表（Map）

			pickerOptions: {
				disabledDate: time => {
					// 不允许选择过去日期 只能选择今天以及未来93天内的日期
					const now = this.$moment().startOf('day').valueOf() - 1;
					const pickTime = this.$moment(time).startOf('day').valueOf();
					const thirtyDaysAfter = this.$moment().add(93, 'days').valueOf();
					return pickTime <= now || pickTime >= thirtyDaysAfter;
				},
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 是否可发布
		isPublish() {
			if (this.detailForm.stage == 5 || this.detailForm.stage == 10) {
				return this.followForm.documentaryTime && this.followForm.content;
			} else {
				return this.followForm.documentaryTime && this.followForm.content && this.followForm.nextPlan && this.followForm.nextStep;
			}
		},
		// 显示发布提示
		showTips() {
			// 如果阶段不是【'已签单' '丢单'】 则提示:当前阶段下一步计划不能为空！。
			if (this.detailForm.stage == 5 || this.detailForm.stage == 10) {
				return false;
			} else {
				return !this.followForm.nextPlan;
			}
		},
		// 跟单记录合并（合并出差申请信息）
		newDocumentaryRecordsList() {
			return this.documentaryRecordsList.map(aItem => {
				let newItem = { ...aItem };
				const bItem = this.travelListMap?.get(newItem.drid);
				if (bItem) {
					newItem = { ...aItem, ...bItem };
					newItem.tripUsers = Object.values(bItem?.tripUsers)?.join(',') || '';
				}
				return newItem;
			});
		},
		// 正在进行中跟单的计划
		planList() {
			return this.newDocumentaryRecordsList?.filter(item => item.planStatus == 0 && item.nextPlan && item.nextStep) || [];
		},

		// 是否对计划进行跟单(跟单且同时关闭这个计划)
		isFollowPlan() {
			return this.planList?.length && this.planForm.drid;
		},
		// 附件类型
		fileTypes() {
			// 如果是电话跟单，则只允许上传音频
			if (this.followForm.recordType == '电话跟单') {
				return 'audio/*';
			}
			return '.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*';
		},

		formRules() {
			if (this.followForm.cfgAttachments) {
				return {
					documentaryTime: [{ required: true, message: '请选择跟单日期!', trigger: 'change' }],
					aupcid: [{ required: true, message: '请选择跟进方式!', trigger: 'change' }],
					recordAttachment: [{ required: true, message: '请选择上传附件!', trigger: 'change' }],
				};
			}
			return {
				documentaryTime: [{ required: true, message: '请选择跟单日期!', trigger: 'change' }],
				aupcid: [{ required: true, message: '请选择跟进方式!', trigger: 'change' }],
			};
		},
	},
	// 监控data中的数据变化
	watch: {
		planList: {
			handler() {
				if (this.planList.length && this.followForm.drid == '') {
					this.planForm = deepClone(this.planList[0]) || this.planForm;
					// this.followForm.content = this.followForm.content ? this.followForm.content : this.planList[0]?.nextPlan || '';
				}
			},
			deep: true,
		},
	},
	created() {
		// 订阅事件（注意这里要加多一层parent 使其与发布事件一致）
		eventBus.$on(`updateDetailForm_${this.$parent.$parent.$options.name}`, detailForm => {
			// 处理事件，更新本地的 detailForm 对象
			this.detailForm = detailForm;
			this.followForm.documentaryTime = new Date().getTime();
			// console.log(`updateDetailForm_${this.$parent.$parent.$options.name}`, this.detailForm, detailForm);
			console.log('eventBus - on ', `updateDetailForm_${this.$parent.$parent.$options.name}`);
		});

		this.queryScoreConfig(); // 查询积分配置项
	},

	beforeDestroy() {
		eventBus.$off(`updateDetailForm_${this.$parent.$parent.$options.name}`);
		console.log('eventBus - off ', `updateDetailForm_${this.$parent.$parent.$options.name}`);
	},
	destroyed() {
		this.detailForm = null;
		this.followForm = null;
	},
	// 方法集合
	methods: {
		// 上传附件
		uploadFile(item) {
			const isLt50M = item.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				this.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}
			this.isUploading = true;
			const formData = new FormData();
			formData.append('file', item.file);

			this.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						// this.detailForm.recordAttachment = res.data.data.fileName;
						this.followForm.recordAttachment = res.data.data.path;
						this.isUploading = false;
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.isUploading = false;
					this.$message.warning(error.message);
				});
		},
		// 选择分配置项
		changeScoreConfig(aupcid) {
			// 是否为提交报价，如果是则需要弹窗提示输入/确认成绩金额和预计成交月份
			const isSubmitOffer = this.scoreConfigList.find(i => i.aupcid === aupcid)?.cfgName == '提交报价';
			if (isSubmitOffer) {
				this.$emit('checkAmountAndMonth');
			}

			if (aupcid) {
				const scoreInfo = this.scoreConfigList.find(i => i.aupcid === aupcid);
				this.followForm = { ...this.followForm, ...scoreInfo };
				this.followForm.recordType = scoreInfo.cfgName;
			} else {
				this.followForm.cfgAttachments = '';
				this.followForm.cfgHasFacilitator = '';
				this.followForm.recordType = '';
			}
			this.followForm.recordAttachment = '';
			this.followForm.facilitatorUidArray = [];
			this.followForm.facilitatorUids = '';
		},
		// 切换当前跟单计划
		changePlan(drid) {
			if (drid) {
				const findPlan = this.planList.find(item => item.drid == drid);
				this.followForm.content = this.followForm.content ? this.followForm.content : findPlan.nextPlan;
				this.planForm = deepClone(findPlan);
			} else {
				this.planForm = resetValues(this.planForm);
			}
		},
		// 聚焦输入框
		focus() {
			this.$refs?.focusInput?.focus();
		},

		// 保存跟单 - 添加跟单记录
		savePublish: debounce(async function () {
			if (checkRequired(this.followForm, this.formRules)) return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			const API = 'addDocumentaryRecords';
			try {
				const res = await this.$axios[API](
					JSON.stringify({ ...this.followForm, idid: this.detailForm.idid || this.idid, ckrid: this.detailForm.ckrid }),
				);
				if (res.data.success) {
					this.clearFollowForm();
					if (this.isFollowPlan) {
						this.$refs.FollowRecord.savePlan('关闭计划', this.planForm); //对计划进行跟单，同时关闭该计划
						return;
					}
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		openDialog(title, item) {
			this.$refs.FollowRecord.openDialog(title, item);
		},
		// 跟单情况表单清空
		clearFollowForm() {
			this.followForm = resetValues(this.followForm);
			this.followForm.documentaryTime = new Date().getTime();
		},

		// 查询积分配置项（跟单方式）
		async queryScoreConfig() {
			const API = 'selectAdminUserPointsConfigurationList';
			try {
				const res = await this.$axios[API](JSON.stringify({ cfgGroup: '业务积分项' }));
				if (res.data.success) {
					this.scoreConfigList = res.data.data.filter(item => item.cfgCate === '跟单方式') || [];
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		},
		// 查询出差申请
		queryTravelData: debounce(async function () {
			if (!(this.detailForm.idid || this.idid)) return;
			const API = 'selectInquiryBusinessTripApplicationList';
			try {
				const res = await this.$axios[API](JSON.stringify({ id: this.detailForm.idid || this.idid }));
				if (res.data.success) {
					this.travelListMap = new Map(res.data.data?.map(item => [item.drid, item])) || new Map();
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}, 200),
		dateFormat,
		jointString,
	},
};
</script>
