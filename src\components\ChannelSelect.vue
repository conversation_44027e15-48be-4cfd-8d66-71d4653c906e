<template>
	<!-- 渠道、分销/代理选择器 -->
	<div id="ChannelSelect">
		<!-- 渠道 -->
		<div v-if="showChannel">
			<span class="select-label">渠道</span>
			<el-select
				class="vw10 min-w-150 channel-select"
				size="mini"
				v-model="channelName"
				placeholder="全部渠道"
				filterable
				multiple
				collapse-tags
				clearable
				@change="changeChannelName"
			>
				<el-option v-for="item in channelOptions" :key="item" :label="item" :value="item"> </el-option>
			</el-select>
		</div>
		<!-- 分销/代理 -->
		<div v-if="showTeamWork">
			<span class="select-label">分销/代理</span>
			<el-select
				popper-class="select-column-3"
				class="vw10 min-w-150"
				size="mini"
				v-model="twidList"
				placeholder="分销/代理"
				filterable
				multiple
				collapse-tags
				clearable
				@change="changeTwidList"
			>
				<el-option v-for="item in twidOptions" :key="item.teamworkId" :label="item.teamworkName" :value="item.teamworkId">
				</el-option>
			</el-select>
		</div>
	</div>
</template>
<script>
import { getLocalStorage, setLocalStorage } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';

export default {
	name: 'ChannelSelect',
	components: {},
	props: {
		//类型：询盘inquiry | 合同contract | 团队team
		type: {
			type: String,
			default: 'inquiry',
		},
	},
	// 数据
	data() {
		return {
			uid: '',
			channelOptions: [],
			channelName: [],
			twidOptions: [],
			twidList: [],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 显示渠道筛选框
		showChannel() {
			return this.channelOptions.length > 1;
		},
		// 显示代理筛选框
		showTeamWork() {
			return this.showChannel || this.twidOptions.length > 1;
		},
	},
	// 监控data中的数据变化
	watch: {},

	mounted() {
		this.uid = this.$route?.query?.queryId || '';
		this.getLocalStorage();
		this.queryChannelOptions();
		this.queryTwidOptions();
	},
	activated() {
		this.getLocalStorage();
	}, // 如果页面有keep-alive缓存功能，这个函数会触发
	// 方法集合
	methods: {
		// 获取本地数据
		getLocalStorage() {
			this.channelName = getLocalStorage(`${this.type}_channelName` + this.uid) || [];
			this.twidList = getLocalStorage(`${this.type}_twidList` + this.uid) || [];
			// 通知父组件更新渠道和代理信息
			this.$emit('change', {
				channelName: this.channelName,
				twidList: this.twidList,
			});
		},
		// 查询用户渠道信息
		queryChannelOptions() {
			const API = 'selectChannelName';
			this.$axios[API](JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						this.channelOptions = res.data.data.channelName;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.error(`${API} |` + error);
				});
		},
		// 选择渠道-查询分销/代理
		changeChannelName() {
			if (this.channelName.length == 0) {
				this.twidList = [];
				this.queryTwidOptions();
				// 通知父组件更新渠道和代理信息
				this.$emit('change', {
					channelName: this.channelName,
					twidList: this.twidList,
				});
				return;
			}
			const API = 'selectTeamworkName';
			this.$axios[API](JSON.stringify({ channelName: this.channelName }))
				.then(res => {
					if (res.data.success) {
						this.twidOptions = res.data.data || [];
						// 如果twidList不属于twidOptions时则去掉
						if (!this.twidList?.every(item => this.twidOptions?.some(twid => twid.teamworkId == item))) {
							this.twidList = this.twidList?.filter(item => this.twidOptions?.some(twid => twid.teamworkId == item));
						}
						// 如果选择了channelName把twidOptions的所有id放入twidList
						if (this.channelName?.length > 0) {
							this.twidList = this.twidOptions?.map(item => item.teamworkId);
						}

						setLocalStorage(`${this.type}_channelName` + this.uid, this.channelName);
						setLocalStorage(`${this.type}_twidList` + this.uid, this.twidList);
						// 通知父组件更新渠道和代理信息
						this.$emit('change', {
							channelName: this.channelName,
							twidList: this.twidList,
						});
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
		changeTwidList() {
			setLocalStorage(`${this.type}_channelName` + this.uid, this.channelName);
			setLocalStorage(`${this.type}_twidList` + this.uid, this.twidList);
			// 通知父组件更新渠道和代理信息
			this.$emit('change', {
				channelName: this.channelName,
				twidList: this.twidList,
			});
		},
		// 查询当前用户所有分销/代理
		queryTwidOptions() {
			this.$axios
				.selectUserTeamwork(JSON.stringify({}))
				.then(res => {
					if (res.data.success) {
						if (res.data.data.teamworkMap) {
							this.twidOptions =
								Object?.entries(res.data.data.teamworkMap)?.map(([twid, twName]) => ({
									teamworkId: twid,
									teamworkName: twName,
								})) || [];
						}
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectUserTeamwork |' + error);
				});
		},
	},
};
</script>

<style lang="scss" scoped>
#ChannelSelect {
	/*  全局select - 渠道  */
	position: absolute;
	right: 0;
	z-index: 2;
	line-height: 40px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 1vw;
	.select-label {
		font-size: 14px;
		font-weight: 400;
		line-height: 30px;
		margin-right: 0.5vw;
		color: #666;
	}

	@media screen and (max-width: 1280px) {
		.channel-select {
			display: none;
		}
		.select-label {
			display: none;
		}
	}
}
</style>
