# BaseTableForm 组件

基于表格（table）结构封装的表单组件，用于以表格形式展示和编辑表单数据。该组件提供了灵活的布局和丰富的表单项类型，适用于复杂表单的展示和编辑场景。

## 功能特点

- 基于 HTML 表格结构，提供清晰的表单布局
- 支持多种表单项类型：文本、日期、数字、单选框、多行文本等
- 支持表单项的自定义渲染
- 支持表头提示信息
- 支持表格单元格合并
- 响应式设计，适配不同屏幕尺寸
- 支持禁用整个表单或单个表单项
- 自动处理数字输入格式化

## 属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| formList | Array | - | 是 | 表单配置数组，定义表单的结构和每个表单项的属性 |
| detailForm | Object | - | 是 | 表单数据对象，包含表单项的值 |
| disabled | Boolean | false | 否 | 是否禁用整个表单 |

### formList 配置项

`formList` 是一个二维数组，每个子数组代表表格的一行，子数组中的每个对象代表一个表单项（单元格）：

```js
[
  [ // 第一行
    { name: '姓名', prop: 'name', class: 'label-required' },
    { name: '年龄', prop: 'age', type: 'number' }
  ],
  [ // 第二行
    { name: '出生日期', prop: 'birthday', type: 'date' },
    { name: '备注', prop: 'remark', type: 'textarea', colspan: 3 }
  ]
]
```

每个表单项对象支持以下属性：

| 属性名 | 类型 | 说明 |
|--------|------|------|
| name | String | 表头名称（必填） |
| prop | String | 表单项对应的数据属性名（必填） |
| class | String | 表头样式类名，如 'label-required' 表示必填项 |
| type | String | 表单项类型，支持 'text'、'date'、'date-text'、'textarea'、'radio'、'number'、'empty' |
| colspan | Number | 表单项跨列数 |
| tooltips | Array | 表头提示信息数组 |
| disabled | Boolean | 是否禁用该表单项 |
| numberOption | Object | 数字输入框配置，如 `{ d: 2 }` 表示保留 2 位小数 |

## 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update | 表单数据更新时触发 | (formData: Object) |

## 插槽

组件提供了两种类型的具名插槽，用于自定义表头和表单项的渲染：

### 表头插槽

```vue
<template v-slot:th-[prop]="{ item }">
  <!-- 自定义表头内容 -->
</template>
```

### 表单项插槽

```vue
<template v-slot:td-[prop]="{ item, formData, updateFormData }">
  <!-- 自定义表单项内容 -->
</template>
```

## 使用示例

### 基本用法

```vue
<template>
  <BaseTableForm
    :formList="formList"
    :detailForm="detailForm"
    @update="handleUpdate"
  />
</template>

<script>
import BaseTableForm from '@/components/BaseTableForm';

export default {
  components: {
    BaseTableForm
  },
  data() {
    return {
      detailForm: {
        name: '张三',
        age: 28,
        birthday: 1609459200000, // 时间戳
        remark: '这是一段备注信息'
      },
      formList: [
        [
          { name: '姓名', prop: 'name', class: 'label-required' },
          { name: '年龄', prop: 'age', type: 'number', numberOption: { d: 0 } }
        ],
        [
          { name: '出生日期', prop: 'birthday', type: 'date' },
          { name: '备注', prop: 'remark', type: 'textarea' }
        ]
      ]
    };
  },
  methods: {
    handleUpdate(formData) {
      console.log('表单数据已更新:', formData);
      // 可以在这里进行数据处理或保存
    }
  }
}
</script>
```

### 使用自定义插槽

```vue
<template>
  <BaseTableForm
    :formList="formList"
    :detailForm="detailForm"
    @update="handleUpdate"
  >
    <!-- 自定义表头 -->
    <template v-slot:th-status="{ item }">
      <div class="flex-align-center">
        <span>{{ item.name }}</span>
        <el-tag size="mini" type="success" class="ml5">自定义</el-tag>
      </div>
    </template>
    
    <!-- 自定义表单项 -->
    <template v-slot:td-status="{ item, formData, updateFormData }">
      <el-select v-model="formData[item.prop]" @change="updateFormData">
        <el-option label="正常" :value="1" />
        <el-option label="禁用" :value="0" />
        <el-option label="审核中" :value="2" />
      </el-select>
    </template>
  </BaseTableForm>
</template>
```

### 带提示的表单项

```vue
<template>
  <BaseTableForm :formList="formList" :detailForm="detailForm" />
</template>

<script>
export default {
  data() {
    return {
      detailForm: { score: 85 },
      formList: [
        [
          { 
            name: '评分', 
            prop: 'score', 
            type: 'number', 
            tooltips: ['评分范围：0-100', '60分以下为不及格'] 
          }
        ]
      ]
    };
  }
}
</script>
```

## 注意事项

1. 表单项的 `prop` 属性必须与 `detailForm` 对象中的属性名一致
2. 使用 `label-required` 类可以为表单项添加必填标记（红色星号）
3. 数字输入框可以通过 `numberOption` 配置小数位数
4. 使用自定义插槽时，需要手动调用 `updateFormData` 方法来触发表单数据更新
5. 日期类型的表单项使用时间戳格式存储数据

## 相关组件

- Tooltips - 提示组件，用于显示文本提示
- Element UI 组件 - 表单项使用了 Element UI 的表单组件 