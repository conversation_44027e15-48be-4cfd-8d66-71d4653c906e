<template>
	<!-- 数据导出弹窗 -->
	<div :class="openMove ? 'moveToggle' : 'moveToggle moveToggle-hide'">
		<div class="detail-wrapper">
			<!-- 头部标题 -->
			<div class="detail-head">
				<span class="detail-title">数据导出（ 温馨提示：您导出的文件生成后会暂存在服务器上，三天后自动删除。 ）</span>
				<div>
					<el-button type="text" class="el-icon-refresh-right" @click="queryExportTable('refresh')">刷新</el-button>
					<el-button type="text" class="el-icon-arrow-left" @click="openMove = false">返回</el-button>
				</div>
			</div>
			<!-- 主体内容 -->
			<div class="detail-content">
				<!-- 内容标题 -->
				<p class="detail-content-title">导出日志</p>
				<!-- 统一表格 -->
				<div class="table-wrapper">
					<u-table
						ref="uTableRef"
						class="table-main"
						:data="tableData"
						:height="650"
						:row-height="45"
						:total="tablePageForm.total"
						:page-size="tablePageForm.pageSize"
						:current-page="tablePageForm.currentPage"
						:page-sizes="tablePageForm.pageSizes"
						@handlePageSize="handlePageSize"
						pagination-show
						stripe
					>
						<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
						<u-table-column
							v-for="item in tableColumn"
							:key="'colCurr' + item.colNo"
							:label="item.colName"
							:prop="item.colNo"
							:align="item.align"
							:width="item.width"
							sortable
							resizable
						>
							<template slot-scope="scope">
								<!-- 各种日期 -->
								<Tooltips
									v-if="['createTime', 'endTime'].includes(item.colNo)"
									:cont-str="dateFormat(scope.row[item.colNo], 'ALL')"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>

								<!-- 导出状态在标准版项目、本地化项目返回方式可能会有差异，根据后台返回结果调整 -->
								<Tooltips
									v-else-if="item.colNo == 'downloadRecordStatusEnum'"
									:cont-str="scope.row.downloadRecordStatusEnum?.statusName"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>

								<Tooltips
									v-else
									:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</template>
						</u-table-column>
						<u-table-column>
							<template slot-scope="scope">
								<el-button
									type="text"
									@click="downloadTable(scope.row.downloadPath)"
									:disabled="scope.row.downloadRecordStatusEnum.statusName !== '导出完成'"
									>下载
								</el-button>
							</template>
						</u-table-column>
					</u-table>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { dateFormat } from '@/util/tool';
export default {
	name: 'ExportTable',
	components: {},
	props: {},
	data() {
		return {
			// 文件状态枚举
			statusMap: {
				COMPLETE_DOWNLOAD: '导出完成',
				FILE_EXPIRED: '文件过期',
			},
			downloadData: '', //数据报表参数（后台确认字段downloadData）
			openMove: false, //控制弹窗显隐
			tableData: [], //导出日志表格数据
			// 分页
			tablePageForm: {
				total: 0,
				pageSize: 15,
				currentPage: 1,
				pageSizes: [15, 50, 100],
			},
			// 表格列
			tableColumn: [
				{ colName: '创建时间', colNo: 'createTime', align: 'left', width: '300' },
				{ colName: '用户', colNo: 'userName', align: 'left', width: '' },
				{ colName: '文件名', colNo: 'downloadName', align: 'left', width: '300' },
				{ colName: '数据', colNo: 'downloadData', align: 'left', width: '' },
				{ colName: '状态', colNo: 'downloadRecordStatusEnum', align: 'left', width: '' },
			],
		};
	},
	mounted() {},
	methods: {
		// 下载表格
		downloadTable(path) {
			// 如果是本地化部署，可能会没有拼http，需要给path拼上'http://' 根据后台返回结果调整
			window.open(path);
		},
		//获取导出记录表格数据
		queryExportTable(type) {
			const pageNum = this.tablePageForm.currentPage,
				pageSize = this.tablePageForm.pageSize;
			const data = JSON.stringify({
				downloadData: this.downloadData,
				pageSize,
				pageNum,
			});
			this.$axios.selectAdminDownloadRecord(data).then(res => {
				if (res.data.data) {
					this.tableData = res.data.data;
					this.tablePageForm.total = res.data.totalItems;
					// if (this.tableData[0]?.downloadRecordStatusEnum?.statusName == '导出中..') {
					// 	// 后台正在导出，一秒后自动查询导出记录
					// 	setTimeout(() => {
					// 		this.queryExportTable();
					// 	}, 1000);
					// }
					type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
				} else {
					this.$err(res.data.message);
				}
			});
		},
		//调用导出接口，成功后调用导出记录并显示弹窗
		openExport({ DATA, API, downloadData, type = 'export' }) {
			this.downloadData = downloadData; //数据报表参数，比如 计件明细、工时统计等（后台确认字段downloadData）

			if (type == 'record') {
				this.queryExportTable();
				this.openMove = true;
				return;
			}
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.$succ('数据已导出!');
						setTimeout(() => {
							this.queryExportTable(); //获取数据导出记录
							this.openMove = true;
						}, 500);
					} else {
						this.$err('导出失败:' + res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		},
		// 分页控制
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryExportTable();
		},
		//日期format
		dateFormat,
	},
};
</script>
<style lang="scss" scoped>
.table-main {
	height: calc(100vh - 244px) !important;
}
</style>
