<template>
	<!-- 业务反馈组件 -->
	<div id="FeedbackInfo" v-loading.lock="detailForm.isUpLoadContract" element-loading-text="正在上传合同中，请勿中途退出...">
		<table class="base-table" cellpadding="5" cellspacing="0">
			<!-- 首行 业务跟进反馈 -->
			<!-- <tr>
				<th colspan="5" class="label-required">业务跟进反馈</th>
			</tr>
			<tr class="input-border-none">
				<td colspan="5">
					<el-input
						:disabled="isPublicInquiry"
						type="textarea"
						:autosize="{ minRows: 2, maxRows: 4 }"
						v-model="detailForm.feedback"
						placeholder="业务跟进反馈"
					></el-input>
				</td>
			</tr> -->
			<!-- 次行咨询信息 -->
			<tr>
				<th class="label-required W10">商机质量</th>
				<th class="W10">预计成交月份</th>
				<th class="W10">预计成交金额（万元）</th>
				<th class="W10">客户价值（万元）</th>
				<th class="W10">实施顾问</th>

				<th class="W65">
					<div class="flex-align-center">
						<span class="label-required mr10">阶段</span>
						<span class="primary" v-show="detailForm.signingDate">
							签单日期： {{ dateFormat(detailForm.signingDate, 'lineM') }}
						</span>
						<el-button v-if="detailForm.dmid" type="text" size="mini" class="ml-auto p0" @click="openContract">
							{{ detailForm.contractNo || '合同详情' }}
						</el-button>
					</div>
				</th>
			</tr>
			<tr class="input-border-none">
				<td>
					<el-select :disabled="isPublicInquiry" v-model="detailForm.businessOpportunityQuality" placeholder="商机质量">
						<el-option
							v-for="key in Object.keys(businessQualityMap)"
							:key="key"
							:label="businessQualityMap[key]"
							:value="Number(key)"
						>
							<div class="flex-align-center gap-10">
								<span>{{ businessQualityMap[key] }}</span>
								<Tooltips class="fs-12 color-999" :cont-str="businessQualityRemark[businessQualityMap[key]]" :cont-width="888" />
							</div>
						</el-option>
					</el-select>
				</td>
				<td>
					<el-date-picker
						:disabled="isPublicInquiry"
						class="w-120"
						v-model="detailForm.expectedMonth"
						value-format="timestamp"
						type="month"
						placeholder="成交月份"
						clearable
						format="yyyy-MM"
					></el-date-picker>
				</td>
				<td>
					<el-input
						:disabled="isPublicInquiry"
						class="w-200"
						v-model="detailForm.estimatedAmount"
						placeholder="预计成交金额"
						clearable
					></el-input
				></td>
				<td>
					<el-input
						:disabled="isPublicInquiry"
						v-model="detailForm.customerValueAmount"
						placeholder="客户价值"
						clearable
					></el-input
				></td>
				<td>
					<el-select
						:disabled="isPublicInquiry"
						v-model="detailForm.implementUid"
						placeholder="请选择实施顾问"
						popper-class="select-column-3"
						clearable
						filterable
					>
						<el-option disabled v-if="!detailForm.twid" value="">
							<span class="orange">请选择对应的分销/代理后再操作！</span>
						</el-option>
						<el-option disabled v-else-if="detailForm.twid && implementList.length == 0" value="">
							<span class="orange">当前分销/代理未与系统用户绑定(实施顾问)，请联系管理员绑定用户后再操作！</span>
						</el-option>
						<el-option
							v-else
							v-for="item in implementList"
							:key="item.auid"
							:label="item.userName"
							:value="item.auid"
							:title="item.userName"
						>
						</el-option>
					</el-select>
				</td>

				<td>
					<div class="flex-align-center flex-justify-between">
						<el-radio-group :disabled="detailForm.stage == 5 || isPublicInquiry" v-model="detailForm.stage" @change="changeStage">
							<el-radio :label="8">业务接手</el-radio>
							<el-radio :label="1">线上会议</el-radio>
							<el-radio :label="9">试点试用</el-radio>
							<el-radio :label="2">已上门拜访</el-radio>
							<el-radio :label="3">已报价</el-radio>
							<el-radio :label="4">合同流程</el-radio>
							<el-radio :label="5">已签单</el-radio>
							<el-radio :label="10">丢单</el-radio>
							<el-input
								class="border mt5"
								v-show="isLosing"
								v-model="detailForm.losingOrderReasons"
								placeholder="请输入丢单原因"
								type="textarea"
								:autosize="{ minRows: 2, maxRows: 4 }"
							></el-input>
						</el-radio-group>
						<div class="flex-align-center gap-10">
							<FilePopover
								v-if="detailForm.contractName"
								class="inline-block max-w-150"
								:url="detailForm.contractUrl"
								:content="detailForm.contractName"
							/>

							<el-upload
								:disabled="isPublicInquiry"
								v-if="detailForm.stage == 5"
								ref="upload"
								action=""
								accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
								:http-request="uploadFile"
								:show-file-list="false"
							>
								<el-button size="mini" type="text"> {{ !detailForm.contractName ? '上传合同' : '替换' }} </el-button>
							</el-upload>
						</div>
					</div>
				</td>
			</tr>
		</table>

		<div class="bottom-button flex-align-center">
			<div class="text-left">
				<div v-show="detailForm.oldConsultantConsultName">原咨询人：{{ detailForm.oldConsultantConsultName }}</div>
				<div v-show="detailForm.oldConsultant">原业务员：{{ detailForm.oldConsultant }}</div>
				<div v-show="detailForm.businessTakeoverTime">
					业务接手时间：{{ dateFormat(detailForm.businessTakeoverTime, 'line') }}
				</div>
			</div>
			<div v-show="!isPublicInquiry" class="ml-auto">
				<span v-show="!detailForm.consultationTime" class="red mr10 fs-12"> * 请先完成咨询内容并保存后再进行业务反馈</span>
				<el-button :disabled="!detailForm.consultationTime" @click="saveEdit" :type="isUpdate ? 'primary' : ''"
					>保存反馈
				</el-button>
			</div>
		</div>

		<el-dialog width="500px" :visible.sync="dialogEdit" :close-on-click-modal="false" :append-to-body="true" @close="closeDialog">
			<span slot="title">请确认预计成交金额和月份</span>
			<el-form :model="detailForm" :rules="formRules" label-width="150px" label-position="top" @submit.native.prevent>
				<el-form-item label="预计成交金额（万元）" prop="estimatedAmount">
					<el-input class="W100" v-model="detailForm.estimatedAmount" placeholder="预计成交金额" clearable></el-input>
				</el-form-item>
				<el-form-item label="预计成交月份" prop="expectedMonth">
					<el-date-picker
						class="W100"
						v-model="detailForm.expectedMonth"
						value-format="timestamp"
						type="month"
						placeholder="成交月份"
						clearable
						format="yyyy-MM"
					></el-date-picker>
				</el-form-item>
			</el-form>

			<span slot="footer">
				<el-button @click="closeDialog">取 消</el-button>
				<el-button type="primary" @click="saveEdit">确 定</el-button>
			</span>
		</el-dialog>
	</div>
</template>
<script>
import { debounce, deepClone, dateFormat, checkRequired } from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import eventBus from './eventBus';
import { stageList, businessQualityMap, businessQualityRemark } from '@/assets/js/inquirySource.js';
import FilePopover from '@/components/FilePopover.vue';

export default {
	name: 'FeedbackInfo',
	components: { FilePopover },

	props: {
		// // 询盘主键
		idid: {
			type: [String, Number],
			default: '',
		},
		// 是否公海询盘
		isPublicInquiry: { type: Boolean, default: false },
		//实施顾问列表(标签：实施)
		implementList: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			dialogEdit: false,

			detailForm: {}, //事件总线表单
			feedbackFormCopy: {}, //克隆数据
			businessQualityMap, // 商机质量
			businessQualityRemark, // 商机质量备注
			stageList, // 询盘阶段
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
		// 咨询表单
		feedbackForm() {
			return {
				businessOpportunityQuality: this.detailForm.businessOpportunityQuality,
				estimatedAmount: this.detailForm.estimatedAmount,
				expectedMonth: this.detailForm.expectedMonth,
				feedback: this.detailForm.feedback,
				implement: this.detailForm.implementUid,
				// quality: this.detailForm.quality,
				stage: this.detailForm.stage,
				// callRecording: this.detailForm.callRecording,
				contractUrl: this.detailForm.contractUrl,
				contractName: this.detailForm.contractName,
				idid: this.detailForm.idid || this.idid,
				ckrid: this.detailForm.ckrid,
				talktrade: this.detailForm.talktradeUid,
				// consult: this.detailForm.consultUid,
				losingOrderReasons: this.detailForm.losingOrderReasons, //丢单原因
				customerValueAmount: this.detailForm.customerValueAmount, //客户价值
			};
		},
		// 是否修改
		isUpdate() {
			const FLAG = JSON.stringify(this.feedbackForm) !== JSON.stringify(this.feedbackFormCopy);
			// console.log('feedbackForm_isUpdate', FLAG);
			return FLAG;
		},
		// 阶段 - 丢单
		isLosing() {
			return this.detailForm.stage == 10;
		},
		// 表单验证规则
		formRules() {
			const baseRules = {
				// feedback: [{ required: true, message: '请输入业务跟进反馈', trigger: 'blur' }],
				businessOpportunityQuality: [{ required: true, message: '请选择商机质量', trigger: 'blur' }],
			};

			if (this.dialogEdit) {
				return {
					estimatedAmount: [{ required: true, message: '请输入预计成交金额', trigger: 'blur' }],
					expectedMonth: [{ required: true, message: '请选择预计成交月份', trigger: 'blur' }],
				};
			}
			return baseRules;
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {
		// 订阅事件（注意这里要加多一层parent 使其与发布事件一致）
		eventBus.$on(`updateDetailForm_${this.$parent.$parent.$options.name}`, detailForm => {
			// 处理事件，更新本地的 detailForm 对象
			this.detailForm = detailForm;
			this.feedbackFormCopy = deepClone(this.feedbackForm); //克隆数据
		});
	},
	beforeDestroy() {
		eventBus.$off(`updateDetailForm_${this.$parent.$parent.$options.name}`);
	},
	destroyed() {
		this.detailForm = null;
		this.feedbackFormCopy = null;
	},
	// 方法集合
	methods: {
		closeDialog() {
			this.dialogEdit = false;
		},
		// 检查成交金额和月份（由父组件调用该方法）
		openCheck() {
			this.dialogEdit = true;
		},
		// 打开合同
		openContract() {
			this.$emit('openContract');
		},
		// 选择阶段时如果是已签单，信息必须完整且不允许再修改阶段
		changeStage(value) {
			if (value == 5) {
				if (!this.detailForm.salesmanUid) {
					this.$message.warning('请输入业务顾问！');
					this.detailForm.stage = '';
					return;
				}
				// if (!this.detailForm.feedback) {
				// 	this.$message.warning('请输入业务跟进反馈！');
				// 	this.detailForm.stage = '';
				// 	return;
				// }
				if (!this.detailForm.businessOpportunityQuality && this.detailForm.businessOpportunityQuality != 0) {
					this.$message.warning('请对该询盘商机进行评星！');
					this.detailForm.stage = '';
					return;
				}
				if (!this.detailForm.expectedMonth) {
					this.$message.warning('请填写成交月份！');
					this.detailForm.stage = '';
					return;
				}
				if (!this.detailForm.estimatedAmount) {
					this.$message.warning('请填写成交金额！');
					this.detailForm.stage = '';
					return;
				}

				const NOW_MONTH = this.$moment(new Date()).startOf('month').format('YYYY-MM');
				const EXPECTED_MONTH = this.$moment(this.detailForm.expectedMonth).startOf('month').format('YYYY-MM');
				// console.log(NOW_MONTH !== EXPECTED_MONTH, { NOW_MONTH }, { EXPECTED_MONTH });
				if (NOW_MONTH !== EXPECTED_MONTH) {
					this.$confirm(
						`成交月份【${EXPECTED_MONTH}】与当前月份【${NOW_MONTH}】不一致，当前签单金额会计入【${EXPECTED_MONTH}】月份，是否继续保存?`,
						'提示',
						{
							confirmButtonText: '我已知晓',
							cancelButtonText: '返回修改',
							type: 'warning',
						},
					)
						.then(() => {
							this.detailForm.stage = 5;
							this.confirmSign();
						})
						.catch(() => {
							this.detailForm.stage = '';
							this.$message.info('已取消');
						});

					return;
				}
			} else if (value == 10) {
				// 当阶段为丢单时，预计成交月分=2099年12，预计成交金额= 0，出现丢单原因输入框
				// this.detailForm.losingOrderReasons = this.detailForm.losingOrderReasons || '';
				this.detailForm.expectedMonth = this.$moment('2099-12-31').valueOf();
				this.detailForm.estimatedAmount = 0;
			}
		},
		// 签单确认（并上传合同）
		confirmSign() {
			this.$confirm('将阶段改为【已签单】后将不可修改, 是否继续该操作?', '确认信息', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					// 如果合同未上传，则上传合同
					if (!this.detailForm.contractUrl) {
						this.$confirm(
							'上传合同成功会在合同管理新增新的合同记录并同步该合同，标准版或者金额大于2万的合同，必须上传客户和公司签章的合同，否则不予审批通过!',
							'上传文件',
							{
								confirmButtonText: '上传',
								cancelButtonText: '取消',
								type: 'success',
							},
						)
							.then(() => {
								this.$refs['upload']?.$children[0]?.$refs?.input?.click(); //触发点击上传
							})
							.catch(() => {
								this.detailForm.stage = '';
								this.$message({
									type: 'info',
									message: '已取消上传合同，请重新选择阶段！',
								});
							});
					}
				})
				.catch(() => {
					this.detailForm.stage = '';
					this.$message({
						type: 'info',
						message: '已取消修改阶段，请重新选择阶段！',
					});
				});
		},
		// 电子合同上传
		uploadFile(item) {
			const isLt50M = item.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				this.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}
			this.detailForm.isUpLoadContract = true;
			const formData = new FormData();
			formData.append('file', item.file);

			this.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						this.detailForm.contractName = res.data.data.fileName;
						this.detailForm.contractUrl = res.data.data.path;
						this.detailForm.isUpLoadContract = false;
						this.saveEdit();
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					this.detailForm.isUpLoadContract = false;
					this.$message.warning(error.message);
				});
		},

		// 删除
		delFile() {
			this.$confirm('注意,该询盘合同删除后需重新上传, 是否继续操作?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					this.detailForm.contractName = '';
					this.detailForm.contractUrl = '';
					this.saveEdit();
				})
				.catch(() => {
					this.$message.info('已取消操作！');
				});
		},
		// 保存基本信息(修改询盘业务反馈信息)
		saveEdit: debounce(async function () {
			if (checkRequired(this.feedbackForm, this.formRules)) {
				return; // 如果有空字段，则停止执行后面的逻辑，并输出提示
			}

			if (this.detailForm.stage == 5 && !this.detailForm.contractUrl) {
				return this.$message.warning('当前阶段为已签单，请上传合同后再保存！');
			}
			if (this.detailForm.stage == 5 && (!this.detailForm.expectedMonth || !this.detailForm.estimatedAmount)) {
				return this.$message.warning('当前阶段为已签单，请填写成交月份和成交金额后再保存！');
			}

			const API = 'feedbackUpdate';
			try {
				const res = await this.$axios[API](JSON.stringify({ ...this.feedbackForm, consult: this.detailForm.consultUid }));
				if (res.data.success) {
					this.dialogEdit = false;
					this.feedbackFormCopy = deepClone(this.feedbackForm); //克隆数据
					this.$emit('refresh'); //刷新详情
					this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.log(`${API} |` + error);
			}
		}),
		dateFormat,
	},
};
</script>
<style lang="scss">
#FeedbackInfo {
	.el-radio {
		margin-right: 10px;
	}
}
</style>
