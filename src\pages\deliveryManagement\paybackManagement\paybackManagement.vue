<template>
	<div id="paybackManagement" :class="isMoveCom ? (openMove ? 'moveToggle' : 'moveToggle moveToggle-hide') : ''">
		<!-- 导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<!-- 渠道/分销/代理下拉筛选 -->
		<ChannelSelect
			ref="ChannelSelect"
			type="deal"
			@change="
				searchForm.channelName = $event.channelName;
				searchForm.twid = $event.twidList;
				queryTableData(1);
			"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="回款管理" name="paybackManagement">
				<BaseLayout>
					<template #header>
						<span class="search-label">订单日期</span>
						<DateSelect
							:dateSelectObj="dateSelectObj"
							@change="
								dateSelectObj = $event;
								queryTableData(1);
							"
						/>

						<span class="search-label">实收日期</span>
						<DateSelect
							defaultDate="本月"
							:dateKeys="['receiptsStartDate', 'receiptsEndDate']"
							:dateSelectObj="dateSelectObj2"
							@change="
								dateSelectObj2 = $event;
								queryTableData(1);
							"
						/>
						<SearchHistoryInput
							className="min-w-100 vw8"
							name="salesmanName"
							placeholder="业务顾问"
							v-model.trim="searchForm.saleName"
							@input="queryTableData(1)"
						/>
						<SearchHistoryInput
							name="companyName"
							placeholder="客户工商注册名称"
							v-model="searchForm.queryParam"
							@input="queryTableData(1)"
						/>
						<el-checkbox-group v-model="searchForm.contractFlag" @change="queryTableData(1)">
							<el-checkbox :label="0">未收款</el-checkbox>
							<el-checkbox :label="1">已收款</el-checkbox>
						</el-checkbox-group>

						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button v-if="isMoveCom" type="text" class="el-icon-arrow-left" @click.stop="openMove = false">返回</el-button>
						</div>
					</template>
					<template #main>
						<div class="table-toolbar">
							<span class="mr-auto">
								<el-select
									v-model="selectDecimal"
									placeholder="请选择计量单位"
									size="mini"
									clearable
									filterable
									@change="queryTableData"
								>
									<el-option label="计量单位:万元（保留两位）" :value="2"> </el-option>
									<el-option label="计量单位:元" :value="0"> </el-option>
								</el-select>
							</span>
							<ExportBtn @trigger="openExport" />
						</div>

						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
							show-summary
							:summary-method="summaryMethod"
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->

									<Tooltips
										v-if="item.colNo == 'createTime' || item.colNo == 'complateMonth' || item.colNo == 'receiptsDate'"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 各种人员 -->
									<Tooltips
										v-else-if="
											item.colNo == 'salesman' || item.colNo == 'implement' || item.colNo == 'consulting' || item.colNo == 'talk'
										"
										:cont-str="scope.row[item.colNo]?.userName"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 项目类型 -->
									<Tooltips
										v-else-if="item.colNo == 'projectType'"
										:cont-str="projectTypeMap[scope.row[item.colNo]]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 订单类型 -->
									<Tooltips
										v-else-if="item.colNo == 'type'"
										:cont-str="scope.row[item.colNo] == 1 ? '合同' : scope.row[item.colNo] == 2 ? '直购' : '续费'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:class="scope.row[item.colNo] == 1 ? '' : 'green'"
										:cont-str="scope.row[item.colNo] == 1 ? '未收款' : '已收款'"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>
									<!-- 开票状态  1 无需开票 2 需要开票 -->
									<Tooltips
										v-else-if="item.colNo == 'invoicing'"
										:cont-str="
											scope.row[item.colNo] == 1
												? '无需开票'
												: scope.row['ticketNo']
													? '票号：' + scope.row['ticketNo']
													: '未申请开票'
										"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 客户上传凭证 -->
									<FilePopover
										v-else-if="item.colNo == 'bankReceiptName'"
										:url="scope.row.bankReceiptUrl"
										:content="scope.row.bankReceiptName"
									/>

									<!-- 来源 -->
									<Tooltips
										v-else-if="item.colNo == 'channel'"
										:cont-str="
											jointString(
												'/',
												sourceMap[scope.row[item.colNo]],
												scope.row.promotionalVidUserName,
												scope.row.promotionalVid,
											)
										"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 金额转换 -->
									<Tooltips
										v-else-if="item.colNo == 'receivableAmount' || item.colNo == 'receiptsAmount'"
										:cont-str="convertToMillion(scope.row[item.colNo])"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 合同状态 -->
									<Tooltips
										v-else-if="item.colNo == 'approveStatus'"
										:class="{ 0: 'color-999', 1: 'green', 2: 'red' }[scope.row.approveStatus]"
										:cont-str="{ 0: '未审核', 1: '已审核', 2: '驳回' }[scope.row.approveStatus]"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<u-table-column label="" width="90" align="right" fixed="right">
								<template slot-scope="scope">
									<el-button type="text" size="mini" @click="openDialog(scope.row, '收款')">收款</el-button>
									<el-button type="text" size="mini" @click="openDialog(scope.row, '开票')">开票</el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
			<el-tab-pane label="收款月度汇总" name="paybackRiver">
				<payback-river
					v-if="activeTab == 'paybackRiver'"
					:twidList="searchForm.twid"
					:channelName="searchForm.channelName"
				></payback-river>
			</el-tab-pane>
		</el-tabs>

		<!-- 收款弹窗 -->
		<el-dialog :visible.sync="dialogCollection" width="520px" :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="130px" label-position="left" ref="editFormRef">
				<el-form-item label="公司工商注册名称" prop="registeredBusinessName">
					<span> {{ editForm.registeredBusinessName }}</span>
				</el-form-item>
				<el-form-item label="应收金额（元）" prop="receivableAmount">
					<span> {{ editForm.receivableAmount }}</span>
				</el-form-item>
				<el-form-item label="实收金额（元）" prop="receiptsAmount">
					<el-input class="W100" placeholder="请输入实收金额（元）" v-model="editForm.receiptsAmount"></el-input>
				</el-form-item>
				<el-form-item label="收款日期" prop="receiptsDate">
					<el-date-picker
						v-model="editForm.receiptsDate"
						type="date"
						class="W100"
						value-format="timestamp"
						placeholder="请选收款日期"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="银行凭证号" prop="bankVoucherNo">
					<el-input class="W100" placeholder="请输入银行凭证号" v-model="editForm.bankVoucherNo"></el-input>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveCollection">保存</el-button>
			</el-row>
		</el-dialog>
		<!-- 开票弹窗 -->
		<el-dialog :visible.sync="dialogInvoicing" width="600px" :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="100px" label-position="left" ref="editFormRef">
				<el-form-item label="应收金额" prop="receivableAmount">
					<el-col :span="24">
						<span> {{ editForm.receivableAmount }}</span>
					</el-col>
				</el-form-item>
				<el-form-item label="实收金额" prop="receiptsAmount">
					<span> {{ editForm.receiptsAmount }}</span>
				</el-form-item>
				<el-form-item label="开票日期" prop="invoicingDate">
					<el-date-picker
						v-model="editForm.invoicingDate"
						type="date"
						class="W100"
						value-format="timestamp"
						placeholder="请选开票日期"
					></el-date-picker>
				</el-form-item>
				<el-form-item label="票号" prop="ticketNo">
					<el-input class="W100" placeholder="请输入票号" v-model="editForm.ticketNo"></el-input>
				</el-form-item>
				<el-form-item label="快递单号" prop="courierNumber">
					<el-input class="W100" placeholder="请输入快递单号" v-model="editForm.courierNumber"></el-input>
				</el-form-item>
				<el-form-item label="快递单" prop="courierUrl">
					<el-upload
						ref="upload"
						action=""
						accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,image/*"
						:http-request="uploadFile"
						:show-file-list="false"
					>
						<el-button v-if="!editForm.courierUrl" size="small" type="warning">上传</el-button>
						<FilePopover v-else class="max-w-300 inline-block" :url="editForm.courierUrl" :content="editForm.courierUrl" />
					</el-upload>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveInvoicing">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import paybackRiver from './paybackRiver.vue';
import { sourceList, sourceMap } from '@/assets/js/inquirySource.js';
import DateSelect from '@/components/DateSelect/DateSelect';
import ExportTable from '@/components/ExportTable';
import ChannelSelect from '@/components/ChannelSelect.vue';
import FilePopover from '@/components/FilePopover.vue';
import { projectTypeOptions, projectTypeMap } from '@/assets/js/contractSource';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		paybackRiver,
		DateSelect,
		ChannelSelect,
		ExportTable,
		FilePopover,
		ExportBtn,
	},
	props: {
		isMoveCom: Boolean, // 将该组件设为可移动组件
	},
	name: 'paybackManagement',
	data() {
		return {
			openMove: false, //打开组件
			projectTypeMap,
			sourceList,
			selectDecimal: 2,
			sourceMap,
			uid: '',
			activeTab: 'paybackManagement',
			visible: false,
			//日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			dateSelectObj2: {
				receiptsEndDate: '',
				receiptsStartDate: '',
			},
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '公司工商注册名称', colNo: 'registeredBusinessName', align: 'left', width: 200 },
				{ colName: '来源', colNo: 'channel', align: 'left', width: 80 },
				{ colName: '业务顾问', colNo: 'salesman', align: 'left', width: 100 },
				{ colName: '实施顾问', colNo: 'implement', align: 'left', width: 100 },
				{ colName: '订单号', colNo: 'orderNo', align: 'left', width: 100 },
				{ colName: '项目类型', colNo: 'projectType', align: 'left', width: 80 },
				{ colName: '订单类型', colNo: 'type', align: 'center', width: 80 },
				{ colName: '应收金额', colNo: 'receivableAmount', align: 'right', width: '100' },
				{ colName: '实收金额', colNo: 'receiptsAmount', align: 'right', width: '100' },
				{ colName: '订单日期', colNo: 'createTime', align: 'center', width: '100' },
				{ colName: '实收日期', colNo: 'receiptsDate', align: 'center', width: '100' },
				{ colName: '状态', colNo: 'status', align: 'center', width: 80 },
				{ colName: '开票状态', colNo: 'invoicing', align: 'left', width: 80 },
				{ colName: '客户上传凭证', colNo: 'bankReceiptName', align: 'left' },
				{ colName: '银行凭证号', colNo: 'bankVoucherNo', align: 'left' },
				{ colName: '合同状态', colNo: 'approveStatus', align: 'left', width: 80 },
			],

			searchForm: {
				contractFlag: [0, 1],
				twid: [],
				channelName: '',
				queryParam: '',
			},
			channelList: [],

			dialogInvoicing: false,
			dialogCollection: false,
			editForm: {},
			fileList: [],

			dialogTitle: '开票',
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {
		openMove(newVal) {
			if (!newVal) {
				this.$emit('closeMove');
				this.$refs?.uTableRef?.reloadData([]); // 加载页面数据
			}
		},
	},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.uid = this.$route?.query?.queryId || '';
		this.searchForm.queryParam = _.getLocalStorage(this.activeTab + '_queryParam') || '';
		this.searchForm.contractFlag = _.getLocalStorage(this.activeTab + '_contractFlag') || [0, 1];
		this.dateSelectObj = _.getLocalStorage(this.activeTab + '_dateSelectObj') || this.dateSelectObj;
		this.dateSelectObj2 = _.getLocalStorage(this.activeTab + '_dateSelectObj2') || this.dateSelectObj2;
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成

	// 方法集合
	methods: {
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = []; // 合计
			columns.forEach((column, columnIndex) => {
				if (columnIndex === 2) {
					means.push('合计');
				} else if (columnIndex === 8 || columnIndex === 9) {
					const values = data?.map(item => Number(item[column.property])).filter(value => !isNaN(value));
					if (values.length > 0) {
						means[columnIndex] = values.reduce((prev, curr) => _.accAdd(prev, curr), 0);
						means[columnIndex] =
							isNaN(means[columnIndex]) || Number(means[columnIndex]) === 0 ? 
								''
							 : 
								<span>{this.convertToMillion(means[columnIndex])}</span>
							;
					} else {
						means[columnIndex] = '';
					}
				}
			});
			return [means];
		},

		// 快递单
		uploadFile(item) {
			const self = this;
			const isLt50M = item.file.size / 1024 / 1024 < 50;
			if (!isLt50M) {
				self.$message.warning('上传文件的大小不能超过 50MB!');
				return;
			}

			const formData = new FormData();
			formData.append('file', item.file);

			self.$axios
				.loadpic(formData)
				.then(res => {
					if (res.data.success) {
						self.editForm.courierUrl = res.data.data.path;
					} else {
						self.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					self.$message.warning(error.message);
				});
		},
		downloadFile(url) {
			window.open(url, '_blank');
		},

		openDialog(row, title) {
			console.log(row);
			// 合同未审核不能收款，续费除外
			if (row.approveStatus !== 1 && row.type !== 3) {
				return this.$message.warning(`合同未审核，不能${title}！`);
			}
			if (title == '收款') {
				this.editForm = {
					registeredBusinessName: row.registeredBusinessName,
					receivableAmount: row.receivableAmount,
					receiptsAmount: row.receiptsAmount || row.receivableAmount,
					receiptsDate: row.receiptsDate || new Date(),
					bankReceiptName: row.bankReceiptName,
					bankReceiptUrl: row.bankReceiptUrl,
					omid: row.omid,
					bankVoucherNo: row.bankVoucherNo,
				};
				// if (!this.editForm.bankReceiptName) {
				//   this.$message.warning("该用户未上传银行凭证，请确认已支付后再操作！")
				// }

				this.dialogTitle = title + '（单位：元）';
				this.dialogCollection = true;
			} else {
				this.editForm = {
					receivableAmount: row.receivableAmount,
					receiptsAmount: row.receiptsAmount,
					invoicingDate: row.invoicingDate || new Date(),
					ticketNo: row.ticketNo,
					courierNumber: row.courierNumber,
					courierUrl: row.courierUrl,
					omid: row.omid,
				};
				this.dialogTitle = title;
				this.dialogInvoicing = true;
			}
		},

		changeTab(tab) {
			if (tab.name == 'paybackManagement') {
				this.$refs.ChannelSelect.getLocalStorage();
			}
		},
		convertToMillion(num) {
			if (!num) return;
			if (this.selectDecimal !== 2) {
				return Number(num).toFixed(0);
			}
			return _.convertToMillion(num);
		},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			_.setLocalStorage(this.activeTab + '_queryParam', this.searchForm.queryParam);
			_.setLocalStorage(this.activeTab + '_contractFlag', this.searchForm.contractFlag);
			_.setLocalStorage(this.activeTab + '_dateSelectObj', this.dateSelectObj);
			_.setLocalStorage(this.activeTab + '_dateSelectObj2', this.dateSelectObj2);

			type && (this.tablePageForm.currentPage = 1);
			const API = 'orderManagementList'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				...this.searchForm,
				...this.dateSelectObj,
				...this.dateSelectObj2,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data.map(item => {
							if (item.deliverManagementDetailVO) {
								item.projectType = item.deliverManagementDetailVO.projectType;
							}
							return item;
						});
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						type == 'openMove' && (this.openMove = true); //当前为移动组件 - 打开
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		addCustomer() {
			this.editForm = {
				abbreviation: '',
				clientNeed: '',
				linkman: '',
				linkphone: '',
				region: '',
				regions: [],
				registeredBusinessName: '',
				salesman: '',
			};
			this.dialogEdit = true;
			this.dialogTitle = '添加客户';
		},
		closeDialog() {
			this.dialogEdit = false;
			this.dialogCollection = false;

			this.dialogInvoicing = false;

			this.editForm = {};
			this.$refs.editFormRef.resetFields();
		},
		saveCollection() {
			const { receivableAmount, bankVoucherNo, receiptsAmount, receiptsDate, omid } = this.editForm;

			if (receiptsAmount != 0 && !receiptsAmount) {
				this.$message.warning('请输入收款金额！');
				return;
			}
			if (receiptsAmount > receivableAmount) {
				this.$message.warning('实收金额不能大于应收金额！');
				return;
			}
			if (!receiptsDate) {
				this.$message.warning('请选择收款日期！');
				return;
			}
			if (!bankVoucherNo) {
				this.$message.warning('请输入银行凭证号');
				return;
			}
			const str = JSON.stringify({
				// bankReceiptName,
				// bankReceiptUrl,
				bankVoucherNo,
				omid,
				receiptsAmount,
				receiptsDate,
			});

			this.$axios
				.collection(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');

						this.closeDialog();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('collection |' + error);
				});
		},
		saveInvoicing() {
			const { receivableAmount, receiptsAmount, invoicingDate, ticketNo, courierNumber, courierUrl, omid } = this.editForm;
			if (!invoicingDate) {
				this.$message.warning('请选择开票日期！');
				return;
			}
			if (!ticketNo) {
				this.$message.warning('请输入票号！');
				return;
			}
			const str = JSON.stringify({
				courierNumber,
				courierUrl,
				invoicingDate,
				omid,
				ticketNo,
			});

			this.$axios
				.invoicing(str)
				.then(res => {
					if (res.data.success) {
						this.$succ('操作成功!');

						this.closeDialog();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('invoicing |' + error);
				});
		},

		dateFormat: _.dateFormat, //日期format
		jointString: _.jointString, //拼接字符串
		//自定义排序（新增逻辑）
		sortChange({ prop, order }, doLayout = false) {
			let sortedData = [];
			if (order && prop) {
				sortedData = _.sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop == 'salesman' || prop == 'implement' || prop == 'consulting' || prop == 'talk') {
						if (!a?.userName) return -1;
						if (!b?.userName) return 1;
						return a?.userName.localeCompare(b?.userName, 'zh-CN');
					} else {
						return null; //其他情况必须要返回null
					}
				});
			} else {
				sortedData = this.tableData;
			}

			this.$nextTick(() => {
				this.tableSort = { prop, order };
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		//数据导出
		openExport: _.debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({
					pageNum: this.tablePageForm.currentPage,
					pageSize: this.tablePageForm.pageSize,
					...this.searchForm,
					...this.dateSelectObj,
					...this.dateSelectObj2,
				}), //接口参数
				API: 'exportOrderManagement', //导出接口
				downloadData: '订单管理导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
	},
};
</script>

<style lang="scss" scoped>
#paybackManagement {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
