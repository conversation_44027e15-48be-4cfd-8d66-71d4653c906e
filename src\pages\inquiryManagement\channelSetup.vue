<template>
	<div id="channelSetup">
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="渠道" name="channelSetup">
				<BaseLayout>
					<template #header>
						<el-input
							class="searchBox"
							size="small"
							clearable
							v-model="searchForm.queryParam"
							placeholder="分销/代理名称"
							@input="queryTableData(1)"
						></el-input>
						<el-checkbox-group v-model="searchForm.statusList" @change="queryTableData(1)">
							<el-checkbox :label="0">仅显示在用分销/代理</el-checkbox>
						</el-checkbox-group>
						<el-button type="text" class="ml-auto el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					</template>
					<template #main>
						<div class="table-toolbar">
							<el-button type="text" class="icon-third-bt_addman" @click="openDialog(null)">添加</el-button>
						</div>
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="item in tableColumn"
								:key="'colCurr' + item.colNo"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期 -->
									<Tooltips
										v-if="['startTime', 'endTime'].includes(item.colNo)"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>

									<!-- 对接人 -->
									<div v-else-if="item.colNo == 'nameAndPhoneList'">
										<div v-if="scope.row.nameAndPhoneList">
											<Tooltips
												v-for="(user, index) in scope.row.nameAndPhoneList"
												:key="index"
												:cont-str="jointString(' / ', user.userName, user.phoneNo)"
												:cont-obj="[
													{ title: '对接人', content: user.userName },
													{ title: '手机号码', content: user.phoneNo },
												]"
												:cont-width="scope.column.width || scope.column.realWidth"
											>
											</Tooltips>
										</div>

										<span v-else class="hover-green primary" @click="openDialog(scope.row)"> 暂未设置对接人</span>
									</div>

									<!-- 状态 -->
									<span v-else-if="item.colNo == 'status'" :class="{ primary: scope.row.status == 0 }">
										{{ scope.row.status == 0 ? '在用' : '停止' }}
									</span>

									<!-- 公海规则 -->
									<span v-else-if="item.colNo == 'highRules'" :class="{ primary: scope.row.highRules == 1 }">
										{{ scope.row.highRules == 1 ? '已启用' : '未启用' }}
									</span>

									<!-- 分销/代理模式 0内部 1代理 2托管-->
									<span v-else-if="item.colNo == 'channelPattern'">{{ patternMap[scope.row[item.colNo]] }}</span>

									<!-- 展期窗口 -->
									<Tooltips
										v-else-if="item.colNo == 'firstSpread' && scope.row.firstSpread"
										:cont-str="scope.row.firstSpread + ' 到 ' + ' - ' + scope.row.lastSpread"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<u-table-column label="" width="60" align="center">
								<template slot-scope="scope">
									<el-button type="text" class="el-icon-edit-outline fs-14" @click="openDialog(scope.row)"></el-button>
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>

		<!-- 添加/修改弹窗 -->
		<el-dialog top="10vh" :visible.sync="dialogEdit" width="999px" :close-on-click-modal="false" @close="closeDialog">
			<el-row slot="title">{{ dialogTitle }}</el-row>
			<el-form :model="editForm" label-width="150px" label-position="left" ref="editFormRef" size="small" :rules="editRules">
				<el-form-item label="渠道" prop="channelName">
					<el-autocomplete
						ref="cautocomplete"
						class="W45"
						v-model="editForm.channelName"
						placeholder="请输入/点击选择渠道"
						clearable
						:debounce="500"
						:fetch-suggestions="querySearch"
						@select="editForm.channelName = $event.channelName"
						@clear="$refs.cautocomplete.activated = true"
					>
						<template slot-scope="{ item }">
							<span>{{ item.channelName }}</span>
						</template>
					</el-autocomplete>

					<!-- <el-input placeholder="请输入渠道名" v-model="editForm.channelName"></el-input> -->
					<!-- <el-select v-model="editForm.channelName" placeholder="请输入渠道名" clearable filterable allow-create class="W100">
						<el-option v-for="item in channelNameList" :key="item.twid" :label="item.channelName" :value="item.channelName">
						</el-option>
					</el-select> -->
				</el-form-item>
				<el-form-item label="分销/代理" prop="teamworkName">
					<el-input class="W45" placeholder="请输入分销/代理名" v-model="editForm.teamworkName"></el-input>
				</el-form-item>
				<el-form-item label="合作期间" prop="startTime">
					<el-col :span="11">
						<el-date-picker
							v-model="editForm.startTime"
							type="date"
							class="W100"
							placeholder="请选择合作开始时间"
						></el-date-picker
					></el-col>
					<el-col class="justify-center" :span="2"> - </el-col>
					<el-col :span="11">
						<el-date-picker v-model="editForm.endTime" type="date" class="W100" placeholder="请选择合作结束时间"></el-date-picker
					></el-col>
				</el-form-item>
				<el-form-item label="对接人" prop="auidList" v-show="dialogTitle == '编辑分销/代理信息'">
					<el-select v-model="editForm.auidList" placeholder="分销/代理对接人" clearable filterable multiple class="W100">
						<el-option disabled v-if="userList.length == 0" :value="null">
							<span class="orange fs-14">当前分销/代理未与系统用户绑定，请联系管理员绑定用户后再操作！</span>
						</el-option>
						<el-option v-else v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid">
							<span>{{ item.userName }}({{ item.phoneNo }})</span>
						</el-option>
					</el-select>
				</el-form-item>
				<el-row>
					<el-col :span="13">
						<el-form-item label="可备案有效客户数" prop="ableRecordQty">
							<el-input class="W90" placeholder="请输入可备案客户数" v-model="editForm.ableRecordQty"></el-input> </el-form-item
					></el-col>
					<el-col :span="11">
						<el-form-item label="备案有效天数" prop="protectDay" label-width="6vw">
							<el-input placeholder="请输入备案有效天数" v-model="editForm.protectDay"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="13">
						<el-form-item label="展期窗口期" prop="firstSpread">
							<el-input style="width: 44%" placeholder="提前天数" v-model="editForm.firstSpread"></el-input>
							<el-input style="width: 44%" placeholder="延迟天数" v-model="editForm.lastSpread"></el-input> </el-form-item
					></el-col>
					<el-col :span="11">
						<el-form-item label="展期天数" prop="spreadDay" label-width="6vw">
							<el-input placeholder="请输入备案展期天数" v-model="editForm.spreadDay"></el-input>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row>
					<el-col :span="13">
						<el-form-item label="分销/代理管理层" prop="managementList">
							<el-select
								:disabled="dialogTitle !== '编辑分销/代理信息'"
								v-model="editForm.managementList"
								placeholder="分销/代理管理层"
								clearable
								filterable
								multiple
								collapse-tags
								class="W90"
							>
								<el-option disabled v-if="userList.length == 0" :value="null">
									<span class="orange fs-14">当前分销/代理未与系统用户绑定，请联系管理员绑定用户后再操作！</span>
								</el-option>
								<el-option v-else v-for="item in userList" :key="item.auid" :label="item.userName" :value="item.auid">
									<span>{{ item.userName }}({{ item.phoneNo }})</span>
								</el-option>
							</el-select>
						</el-form-item></el-col
					>
					<el-col :span="11">
						<el-form-item label="分销/代理模式" prop="channelPattern" label-width="6vw">
							<el-select v-model="editForm.channelPattern" placeholder="分销/代理模式" clearable filterable class="W100">
								<el-option
									v-for="item in patternList"
									:key="item.channelPattern"
									:label="item.label"
									:value="item.channelPattern"
								>
									<span style="float: left">{{ item.label }}</span>
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>

				<el-form-item label="营销分管" prop="scoreManagementUserIdList" v-show="dialogTitle == '编辑分销/代理信息'">
					<el-select
						v-model="editForm.scoreManagementUserIdList"
						placeholder="分销/代理对接人"
						clearable
						filterable
						multiple
						class="W100"
					>
						<el-option disabled v-if="marketingList.length == 0" :value="null">
							<span class="orange fs-14">当前分销/代理未与系统用户绑定（营销分管人员），请联系管理员绑定用户后再操作！</span>
						</el-option>
						<el-option v-else v-for="item in marketingList" :key="item.auid" :label="item.userName" :value="item.auid">
							<span>{{ item.userName }}({{ item.phoneNo }})</span>
						</el-option>
					</el-select>
				</el-form-item>

				<el-row v-show="dialogTitle == '编辑分销/代理信息'" :gutter="10">
					<el-col :span="8">
						<el-form-item label="公海规则" prop="highRules">
							<template slot="label">
								<el-tooltip placement="top">
									<div slot="content" class="max-w-500 p5">
										<p>🌊 公海规则说明如下：</p>
										<pre>1. 公海用户：释放到公海时将转移到该用户（业务顾问）</pre>
										<pre>2. 静置有效天数：超过X天未有跟单自动放入公海（自动）</pre>
										<pre>3. 计划有效天数：当静置超过X天，且Y天内没有跟单计划自动放入公海（自动）</pre>
										<pre>4. 跟单最小天数Z：自询盘分配日起，Z天后可以手动放入公海。</pre>

										<p>✖️ 死海规则说明如下：</p>
										<pre>1. 超级管理员，可在公海清单将公海询盘状态切换为死海/公海（手动）</pre>
										<pre>2. 最大进入公海次数：设置最大进入公海次数，超过次数后将进入死海（自动）</pre>

										<p>💡 当开启公海规则时，公海用户、静置有效天数、计划有效天数、跟单最小天数、最大进入公海次数均不允许为空！</p>
									</div>
									<div>
										<span>公海规则</span>
										<i class="el-icon-question pointer"></i>
									</div>
								</el-tooltip>
							</template>
							<el-checkbox v-model="editForm.highRules" :true-label="1" :false-label="0">启用公海规则</el-checkbox>
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="公海用户" prop="highSeasUid" label-width="6vw" :class="getInputRed('highSeasUid')">
							<el-select v-model="editForm.highSeasUid" placeholder="请选择公海用户" clearable filterable class="W100">
								<el-option disabled v-if="userList.length == 0" :value="null">
									<span class="orange fs-14">当前分销/代理未与系统用户绑定，请联系管理员绑定用户后再操作！</span>
								</el-option>
								<el-option v-else v-for="item in salesmanList" :key="item.auid" :label="item.userName" :value="item.auid">
									<span>{{ item.userName }}({{ item.phoneNo }})</span>
								</el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="最大进入公海次数" prop="highSeasCount" label-width="7vw">
							<el-input
								placeholder="最大进入公海次数"
								v-model.number="editForm.highSeasCount"
								:class="getInputRed('highSeasCount')"
							></el-input>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row v-show="dialogTitle == '编辑分销/代理信息'" :gutter="10">
					<el-col :span="8">
						<el-form-item label="静置有效天数(X)" prop="restingValidDays" :class="getInputRed('restingValidDays')">
							<el-input placeholder="静置天数(X)" v-model.number="editForm.restingValidDays"></el-input
						></el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item
							label="计划有效天数(Y)"
							prop="plannedValidDays"
							label-width="120px"
							:class="getInputRed('plannedValidDays')"
						>
							<el-input placeholder="计划天数(Y)" v-model.number="editForm.plannedValidDays"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="8">
						<el-form-item label="跟单最小天数(Z)" prop="orderMinDays" label-width="120px" :class="getInputRed('orderMinDays')">
							<el-input placeholder="跟单最小天数(Z)" v-model.number="editForm.orderMinDays"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<el-form-item label="状态" prop="status">
					<el-radio-group v-model="editForm.status" class="flex-align-center h-40">
						<el-radio :label="0">在用</el-radio>
						<el-radio :label="1">停止</el-radio>
					</el-radio-group>
				</el-form-item>
			</el-form>
			<el-row slot="footer">
				<el-button type="primary" @click="saveEdit">保存</el-button>
			</el-row>
		</el-dialog>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
export default {
	name: 'channelSetup',
	data() {
		return {
			activeTab: 'channelSetup',
			// 分销/代理模式
			patternMap: {
				0: '内部',
				1: '代理',
				2: '托管',
			},
			patternList: [
				{ channelPattern: 0, label: '内部' },
				{ channelPattern: 1, label: '代理' },
				{ channelPattern: 2, label: '托管' },
			],
			userList: [],
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '渠道', colNo: 'channelName', align: 'left', width: '' },
				{ colName: '分销/代理', colNo: 'teamworkName', align: 'left', width: '' },
				{ colName: '合作日期', colNo: 'startTime', align: 'center', width: '' },
				{ colName: '合作到期日', colNo: 'endTime', align: 'center', width: '' },
				{ colName: '对接人', colNo: 'nameAndPhoneList', align: 'left', width: '' },
				{ colName: '可备案', colNo: 'ableRecordQty', align: 'right', width: '80' },
				{ colName: '已备案', colNo: 'alreadyRecordQty', align: 'right', width: '80' },
				{ colName: '保护天数', colNo: 'protectDay', align: 'right', width: '100' },
				{ colName: '展期窗口', colNo: 'firstSpread', align: 'left', width: '120' },
				{ colName: '展期天数', colNo: 'spreadDay', align: 'right', width: '100' },
				{ colName: '状态', colNo: 'status', align: 'center', width: '80' },
				{ colName: '公海规则', colNo: 'highRules', align: 'center', width: '80' },
				{ colName: '分销/代理管理层', colNo: 'managementName', align: 'left', width: '' },
				{ colName: '分销/代理模式', colNo: 'channelPattern', align: 'center', width: '' },
				{ colName: '营销分管', colNo: 'scoreManagementUserName', align: 'left', width: '' },
			],
			tableSort: { prop: '', order: '' }, //表格排序状态
			searchForm: {
				statusList: [0],
			},
			dialogEdit: false,
			editForm: {
				ableRecordQty: '',
				auidList: [],
				endTime: '',
				firstSpread: '',
				lastSpread: '',
				protectDay: '',
				spreadDay: '',
				startTime: '',
				status: '',
				channelName: '',
				teamworkName: '',
				twid: '',
				// "phoneNo": '',
				userName: '',
				channelPattern: '',
				managementList: [],
				nameAndPhoneList: [],

				highSeasUid: '', //公海用户
				highRules: '', //公海规则 0未启用，1启用
				highSeasCount: '', //最大进入公海次数
				restingValidDays: '', //静置有效天数
				plannedValidDays: '', //计划有效天数
				orderMinDays: '', //跟单的最小天数
			},
			dialogTitle: '渠道详情',
			editRules: {
				ableRecordQty: [{ required: true, message: '请输入可备案有效客户数', trigger: 'blur' }],
				auidList: [{ required: true, message: '请选择对接人', trigger: 'change' }],
				endTime: [{ required: true, message: '请输入合作结束日期', trigger: 'blur' }],
				firstSpread: [{ required: true, message: '请输入提前展期窗口天数', trigger: 'blur' }],
				lastSpread: [{ required: true, message: '请输入延后展期窗口天数', trigger: 'blur' }],
				protectDay: [{ required: true, message: '请输入备案保护天数', trigger: 'blur' }],
				spreadDay: [{ required: true, message: '请输入备案展期天数', trigger: 'blur' }],
				startTime: [{ required: true, message: '请输入合作开始日期', trigger: 'blur' }],
				status: [{ required: true, message: '请选择渠道状态', trigger: 'blur' }],
				channelName: [{ required: true, message: '请输入渠道名称', trigger: 'change' }],
				teamworkName: [{ required: true, message: '请输入分销/代理名称', trigger: 'blur' }],
				channelPattern: [{ required: true, message: '请确认分销/代理模式', trigger: 'change' }],
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）

		channelNameList() {
			//渠道列表
			return _.uniqueArrayByKey(
				this.tableData.filter(item => item.channelName),
				'channelName',
			);
		},
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务')) || [];
		},
		//营销分管人员列表(标签：营销分管)
		marketingList() {
			return this.userList?.filter(user => user?.userLabel?.includes('营销')) || [];
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 开启公海规则时，输入框提示
		getInputRed(key) {
			if (this.editForm.highRules && !this.editForm[key] && !this.editForm[key] !== 0) {
				return 'input-border-red';
			}
			return '';
		},
		// querySearch(查询接口:xxx)
		querySearch(queryStr, cb) {
			const res =
				this.channelNameList.filter(item => {
					return item.channelName.toLowerCase().indexOf(queryStr.toLowerCase()) > -1;
				}) || [];

			cb(res);
		},

		jointString: _.jointString, //拼接字符串
		// 切换tab
		changeTab() {},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			const str = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,
				queryParam: this.searchForm.queryParam,
				statusList: this.searchForm.statusList.includes(0) ? this.searchForm.statusList : [],
			});
			this.$axios
				.selectAllTeamWork(str)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectAllTeamWork |' + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},

		// 编辑信息
		openDialog(row) {
			if (row) {
				this.editForm = { ...this.editForm, ..._.deepClone(row) };
				this.editForm.auidList = row.nameAndPhoneList?.map(item => item.auid) || [];
			}

			this.dialogEdit = true;
			this.dialogTitle = row ? '编辑分销/代理信息' : '添加渠道';
			this.queryUserList();
		},
		// 关闭弹窗
		closeDialog() {
			this.dialogEdit = false;
			this.editForm = _.resetValues(this.editForm);
			// this.$refs.editFormRef.resetFields();
		},
		// 保存
		saveEdit() {
			const {
				channelPattern,
				channelName,
				teamworkName,
				ableRecordQty,
				spreadDay,
				startTime,
				status,
				twid,
				auidList,
				firstSpread,
				lastSpread,
				protectDay,
			} = this.editForm;
			if (!channelName) {
				this.$message.warning('请输入渠道名称！');
				return;
			}
			if (!teamworkName) {
				this.$message.warning('请输入分销/代理名称！');
				return;
			}
			const endTime = this.editForm.endTime ? this.editForm.endTime : 4102415999000;
			if (startTime && endTime && endTime <= startTime) {
				this.$message.warning('合作结束时间必须大于开始时间！');
				return;
			}
			if (!startTime) {
				this.$message.warning('请将合作开始时间补充完整！');
				return;
			}
			if (!ableRecordQty) {
				this.$message.warning('请将可备案有效客户数补充完整！');
				return;
			}
			if (!protectDay) {
				this.$message.warning('请将备案有效天数补充完整！');
				return;
			}
			if (!firstSpread && firstSpread != 0) {
				this.$message.warning('请将展期窗口期补充完整！');
				return;
			}

			if (lastSpread == '' || spreadDay == '') {
				this.$message.warning('请将展期天数补充完整！');
				return;
			}
			if (!channelPattern && channelPattern != 0) {
				this.$message.warning('请将分销/代理模式补充完整！');
				return;
			}
			if (!status && status != 0) {
				this.$message.warning('请将渠道状态补充完整！');
				return;
			}

			// 添加或修改对接人
			if (this.dialogTitle == '编辑分销/代理信息') {
				if (this.editForm.auidList.length == 0) {
					this.$message.warning('请完善对接人信息后再保存！');
					return;
				}

				this.$axios
					.addOrUpdateJointPerson(JSON.stringify({ auidList, twid }))
					.then(res => {
						if (res.data.success) {
							//
						} else {
							this.$err(res.data.message);
						}
					})
					.catch(error => {
						console.log('addOrUpdateJointPerson |' + error);
					});
			}

			// 如果开启了公海规则
			if (this.editForm.highRules) {
				if (
					!this.editForm.highSeasUid ||
					!this.editForm.highSeasCount ||
					!this.editForm.restingValidDays ||
					!this.editForm.plannedValidDays ||
					!this.editForm.orderMinDays
				) {
					this.$message.warning('请完善公海规则后再保存');
					return;
				}
			}
			const API = this.dialogTitle == '编辑分销/代理信息' ? 'updateTeamWork' : 'addTeamWork';
			const DATA = JSON.stringify({ ...this.editForm });
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.closeDialog();
						this.queryTableData();
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |'` + error);
				});
		},
		// 获取用户列表
		queryUserList() {
			if (!this.editForm.twid) return;
			const str = JSON.stringify({ twid: this.editForm.twid, counselor: '' });
			this.$axios
				.selectTeamworkUser(str)
				.then(res => {
					if (res.data.success) {
						this.userList = res.data.data;
					} else {
						this.$err(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectTeamworkUser |' + error);
				});
		},

		//日期format
		dateFormat: _.dateFormat,
		//排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? _.sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
	},
};
</script>
