<template>
	<div class="DemandList">
		<!-- 数据导出弹窗 -->
		<ExportTable ref="ExportTable" />
		<BaseLayout>
			<template #header>
				<!-- 带建议日期 -->
				<span class="search-label">创建日期</span>
				<DateSelect
					:dateList="['不限定', '本月', '上月']"
					@change="
						searchForm.startTime = $event.startTime;
						searchForm.endTime = $event.endTime;
						queryTableData(1);
					"
				/>
				<!-- 模糊查询 -->
				<SearchHistoryInput
					name="demandDocumentName"
					placeholder="需求文件名"
					v-model.trim="searchForm.demandDocumentName"
					@input="queryTableData(1)"
				/>
				<SearchHistoryInput
					width="120"
					name="submitUserName"
					placeholder="提交人姓名"
					v-model.trim="searchForm.submitUserName"
					@input="queryTableData(1)"
				/>

				<el-checkbox-group v-model="searchForm.projectCategories" @change="queryTableData(1)">
					<el-checkbox v-for="key in Object.keys(projectCategoryMap)" :key="key" :label="Number(key)">
						{{ projectCategoryMap[key] }}
					</el-checkbox>
				</el-checkbox-group>

				<div class="ml-auto">
					<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
					<ExportBtn @trigger="openExport" />
					<el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button>
				</div>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar gap-10">
					<el-checkbox-group v-model="searchForm.status" @change="queryTableData(1)">
						<el-checkbox v-for="key in Object.keys(demandStatusMap)" :key="key" :label="Number(key)">
							{{ demandStatusMap[key] }}
						</el-checkbox>
					</el-checkbox-group>

					<el-checkbox
						class="mr-auto"
						v-model="searchForm.isProjectCompleted"
						:true-label="1"
						:false-label="0"
						@change="queryTableData(1)"
					>
						已实现
					</el-checkbox>
				</div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:dialog-data="tableColumn"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					@header-dragend="headerDragend"
					@reset="updateColumn(tableColumnCopy)"
					@show-field="updateColumn"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
					show-summary
					:summary-method="summaryMethod"
				>
					<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="(item, index) in tableColumn.filter(item => item.state)"
						:key="item.colNo + index"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期（默认不显示分秒 lineM ） -->
							<Tooltips
								v-if="item.colNo.includes('Time')"
								:cont-str="dateFormat(scope.row[item.colNo], 'lineM')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<Tooltips
								v-else-if="item.colNo.includes('Date')"
								:cont-str="dateFormat(scope.row[item.colNo], 'line')"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 需求说明书 -->
							<Tooltips
								v-else-if="item.colNo == 'demandDocumentName'"
								class="hover-green"
								:cont-str="scope.row[item.colNo]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								@click.native="$emit('openDocument', scope.row)"
							/>
							<!-- 项目类型 -->
							<Tooltips
								v-else-if="item.colNo == 'projectCategory'"
								:cont-str="projectCategoryMap[scope.row[item.colNo]]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 人天 -->
							<Tooltips
								v-else-if="item.colNo == 'manDayTotal' && scope.row[item.colNo]"
								:cont-str="scope.row[item.colNo].toFixed(1)"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
							<!-- 项目名称 -->
							<Tooltips
								v-else-if="item.colNo == 'projectName'"
								class="hover-green"
								:cont-str="scope.row[item.colNo]"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								@click.native="$emit('openProject', scope.row)"
							/>
							<!-- 状态/类型 -->
							<Tooltips
								v-else-if="item.colNo == 'projectStatus'"
								:cont-str="projectStatusMap[scope.row[item.colNo]]"
								:cont-width="scope.column.width || scope.column.realWidth"
							/>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<!-- <u-table-column label="" width="" align="right">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDetail('编辑',scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template>
              </u-table-column> -->
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData } from '@/util/tool'; //按需引入常用工具函数
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';
import { bigAdd } from '@/util/math';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import ExportTable from '@/components/ExportTable'; //导出组件
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
// import DetailCom from './components/baseDetail.vue'; //明细组件
// import btnAuth from '@/mixins/btnAuth';
import { demandStatusMap, projectCategoryMap } from '@/assets/js/projectSource';
import ExportBtn from '@/components/ExportTable/ExportBtn'; //导出按钮
export default {
	name: 'DemandList', //组件名应同路由名(否则keep-alive不生效)
	// import引入的组件需要注入到对象中才能使用
	components: {
		ExportTable,
		DateSelect,
		ExportBtn,
		// DetailCom,
	},
	// mixins: [btnAuth],
	data() {
		return {
			activeTab: 'DemandList', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '提交日期', colNo: 'submissionTime', align: 'center', width: '150' },
				{ colName: '需求说明书', colNo: 'demandDocumentName', align: 'left', width: '' },
				{ colName: '提出人', colNo: 'submissionName', align: 'left', width: '120' },
				{ colName: '项目类型', colNo: 'projectCategory', align: 'left', width: '100' },
				{ colName: '人天', colNo: 'manDayTotal', align: 'right', width: '80' },
				{ colName: '客户要求日期', colNo: 'customerRequiredCompletionDate', align: 'center', width: '120' },
				{ colName: '需求提交', colNo: 'demandSubmit', align: 'center', width: '80' },
				{ colName: '技术评审', colNo: 'technicalReview', align: 'center', width: '80' },
				{ colName: '签署', colNo: 'sign', align: 'center', width: '80' },
				{ colName: '开发经理确认', colNo: 'developmentManagerConfirm', align: 'center', width: '120' },
				{ colName: '开发承诺发版日期', colNo: 'developmentPromiseCompletionDate', align: 'center', width: '150' },
				{ colName: '开发项目名称', colNo: 'projectName', align: 'left', width: '120' },
				{ colName: '开发实时状态', colNo: 'projectStatus', align: 'left', width: '120' },
			],
			tableColumnCopy: [],
			colMap: {
				demandSubmit: 1,
				technicalReview: 2,
				sign: 3,
				developmentManagerConfirm: 4,
				isProjectCompleted: 0,
			},
			// 选择器数据 - 车间/部门/类型等...
			didOptions: [],
			// 查询表单
			searchForm: {
				projectName: '',
				status: [],
				submitUserName: '',
				projectCategories: [],
			},

			demandStatusMap, // 需求状态
			projectCategoryMap, // 需求项目类别

			// 状态映射
			projectStatusMap: {
				0: '规划中',
				1: '需求与方案评审通过',
				2: '功能分解完成',
				3: '开发计划完成',
				4: '开发全部完成',
				5: '测试全部完成',
				6: '功能验收通过',
				7: '发版准备就绪',
				8: '发版完成',
				9: '延误',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.tableColumnCopy = deepClone(this.tableColumn);
		this.tableColumn = getColumn(this.tableColumn, this.$options.name);
		// this.queryTableData();
	},
	activated() {
		// this.queryTableData(1);
		// this.changeTab();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 表尾合计
		summaryMethod({ columns, data }) {
			const means = ['合计']; // 合计
			for (let columnIndex = 2; columnIndex < columns.length; columnIndex++) {
				const column = columns[columnIndex];
				const values = data.map(item => {
					if (['manDayTotal'].includes(column.property)) return Number(item[column.property]) || 0;
				});
				const sum = values?.reduce((prev, curr) => {
					return !isNaN(Number(curr)) ? bigAdd(prev, curr, 1) : prev;
				}, 0); //合计计算
				means[columnIndex] = sum > 0 ? <span>{sum}</span> : '';
			}
			return [means];
		},
		// 切换tab
		changeTab() {
			if (this.activeTab == 'DemandList') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			// const API = 'selectDemandDetailList'; //接口
			const API = 'selectDemandSuperVisionList'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							const { status } = item;
							item.demandSubmit = this.colMap['demandSubmit'] <= status - 1 ? ' ✅ ' : '';
							item.technicalReview = this.colMap['technicalReview'] <= status - 1 ? ' ✅ ' : '';
							item.sign = this.colMap['sign'] <= status - 1 ? ' ✅ ' : '';
							item.developmentManagerConfirm = this.colMap['developmentManagerConfirm'] <= status - 1 ? ' ✅ ' : '';
						});
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),

		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 展示字段
		updateColumn(data) {
			this.tableColumn = updateColumn(this.$options.name, data);
			this.sortChange(this.tableSort, true);
		},
		// 列宽拖动
		headerDragend(newWidth, oldWidth, column) {
			updateColumnWidth(this.$options.name, column.property, newWidth, this.tableColumn);
		},

		//数据导出
		openExport: debounce(function (type = 'export') {
			const PROPS = {
				DATA: JSON.stringify({ ...this.searchForm }), //接口参数
				API: 'exportDemandDetailList', //导出接口
				downloadData: '需求清单导出', //数据报表参数（后台确认字段downloadData）
				type, // 导出或查看导出记录
			};
			this.$refs.ExportTable.openExport(PROPS); //调用导出接口，成功后调用导出记录并显示弹窗
		}),
		dateFormat, //日期format
		jointString, //拼接字符串
	},
};
</script>

<style lang="scss" scoped>
.DemandList {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
