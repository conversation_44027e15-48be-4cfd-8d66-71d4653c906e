<template>
	<div id="projectOverview">
		<!-- 明细组件 -->
		<DetailCom ref="DetailComRef" @close="queryTableData(1)" />
		<BaseLayout>
			<template #header>
				<!-- 带建议日期 -->
				<span class="search-label">项目要求完成日期</span>
				<DateSelect
					@change="
						dateSelectObj = $event;
						queryTableData(1);
					"
				/>

				<span class="search-label">完成状态</span>
				<el-select
					v-model="searchForm.status"
					placeholder="请选择"
					size="small"
					clearable
					filterable
					@change="
						searchForm.status == '' && (searchForm.statusList = []);
						searchForm.status == 1 && (searchForm.statusList = [8]);
						searchForm.status == 2 && (searchForm.statusList = [0, 1, 2, 3, 4, 5, 6, 7, 9]);
						searchForm.status == 3 && (searchForm.statusList = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]);
						queryTableData(1);
					"
				>
					<el-option label="已完成" :value="1"> </el-option>
					<el-option label="未完成" :value="2"> </el-option>
					<el-option label="全部" :value="3"> </el-option>
				</el-select>

				<el-input
					class="searchBox w-300"
					size="small"
					clearable
					v-model="searchForm.queryParam"
					placeholder="项目名称/客户名称"
					@input="queryTableData(1)"
				></el-input>

				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
				<div class="table-toolbar"></div>
				<!-- 表格主体 -->
				<u-table
					ref="uTableRef"
					class="table-main"
					:height="1200"
					:row-height="45"
					:total="tablePageForm.total"
					:page-size="tablePageForm.pageSize"
					:current-page="tablePageForm.currentPage"
					:page-sizes="tablePageForm.pageSizes"
					@handlePageSize="handlePageSize"
					@sort-change="sortChange"
					show-header-overflow="title"
					pagination-show
					use-virtual
					stripe
				>
					<u-table-column label="序号" width="50" type="index" align="center"></u-table-column>
					<u-table-column
						v-for="item in tableColumn"
						:key="'colCurr' + item.colNo"
						:label="item.colName"
						:prop="item.colNo"
						:align="item.align"
						:width="item.width"
						sortable="custom"
						resizable
					>
						<template slot-scope="scope">
							<!-- 各种日期 -->
							<div v-if="['startTime', 'endTime', 'modifyTime'].includes(item.colNo)">
								<Tooltips
									:cont-str="dateFormat(scope.row.startTime, 'line')"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
								<Tooltips
									:cont-str="dateFormat(scope.row.endTime, 'line')"
									:cont-width="(scope.column.width || scope.column.realWidth) - 20"
								/>
							</div>

							<Tooltips
								v-else-if="['businessPeople', 'projectManager', 'testManager', 'technicalManager'].includes(item.colNo)"
								:cont-str="scope.row[item.colNo]?.userName"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<Tooltips
								v-else-if="item.colNo == 'projectNo'"
								class="hover-green green"
								:class="{ red: scope.row.status == 9 }"
								@click.native="openDetail(scope.row)"
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 18"
							>
							</Tooltips>

							<!-- 状态/类型 -->
							<el-steps v-else-if="item.colNo == 'status'" :active="scope.row.status" align-center class="project-steps">
								<el-step
									v-for="pItem in planTableData"
									:key="pItem.status"
									:title="pItem.target"
									:status="getStatus(scope.row, pItem)"
									:description="dateFormat(scope.row[pItem.time], 'line')"
									:class="getStepClass(pItem.status)"
								></el-step>
							</el-steps>

							<!-- 默认显示 -->
							<Tooltips
								v-else
								:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
								:cont-width="(scope.column.width || scope.column.realWidth) - 20"
							/>
						</template>
					</u-table-column>
					<!-- 其他列/操作 -->
					<!-- <u-table-column label="" width="" align="center">
                <template slot-scope="scope">
                  <el-button type="text" class="el-icon-edit-outline" @click="openDialogRow(scope.row)"></el-button>
                   <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button>
                </template>
              </u-table-column> -->
				</u-table>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool'; //工具函数
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import DateSelect from '@/components/DateSelect/DateSelect'; //带建议日期选择组件
import DetailCom from './projectManagement/projectDetail'; //明细组件
export default {
	// import引入的组件需要注入到对象中才能使用
	components: {
		DateSelect,
		DetailCom,
	},
	name: 'projectOverview', //组件名应同路由名(否则keep-alive不生效)
	data() {
		return {
			activeTab: 'projectOverview', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '项目编号', colNo: 'projectNo', align: 'left', width: '' },
				{ colName: '项目名称', colNo: 'projectName', align: 'left', width: '' },
				{ colName: '起止日期', colNo: 'startTime', align: 'center', width: '90' },
				{ colName: '交付项目经理', colNo: 'businessPeople', align: 'left', width: '' },
				{ colName: '开发项目经理', colNo: 'projectManager', align: 'left', width: '' },
				{ colName: '测试经理', colNo: 'testManager', align: 'left', width: '' },
				{ colName: '技术经理', colNo: 'technicalManager', align: 'left', width: '' },
				{ colName: '完成情况', colNo: 'status', align: 'center', width: '888' },
			],

			// 查询表单
			searchForm: {
				status: 2,
				queryParam: '',
				statusList: [0, 1, 2, 3, 4, 5, 6, 7, 9],
			},
			// 日期相关
			dateSelectObj: {
				endTime: '',
				startTime: '',
			},
			planTableData: [
				{ target: '需求与方案评审通过', description: '交付项目经理负责，技术经理确认', status: 1, time: 'demandTime' },
				{ target: '功能分解完成', description: '技术经理负责，交付项目经理确认', status: 2, time: 'functionTime' },
				{ target: '开发计划确定', description: '技术经理负责，交付项目经理确认', status: 3, time: 'planTime' },
				{ target: '开发全部完成', description: '开发项目经理负责，技术经理确认', status: 4, time: 'developmentTime' },
				{ target: '测试全部完成', description: '测试经理负责，开发项目经理确认', status: 5, time: 'testTime' },
				{ target: '功能验收通过', description: '交付项目经理负责，测试经理确认', status: 6, time: 'checkTime' },
				{ target: '发版准备就绪', description: '开发项目经理负责，技术经理确认', status: 7, time: 'prepareTime' },
				{ target: '发版完成', description: '技术经理负责，技术经理确认', status: 8, time: 'publishTime' },
			],
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.dateSelectObj = JSON.parse(window?.localStorage.getItem(this.$options.name + '_dateSelectObj')) || this.dateSelectObj;
		this.searchForm = JSON.parse(window?.localStorage.getItem(this.$options.name + '_searchForm')) || this.searchForm;
		this.queryTableData();
	},
	activated() {
		this.queryTableData();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开明细
		openDetail(row) {
			this.$refs.DetailComRef.showDetailCom(row);
		},
		// 更改步骤样式
		getStepClass: _.debounce(function () {
			const errors = document.getElementsByClassName('el-step__icon-inner is-status el-icon-close');
			// console.log({ errors });
			Array.from(errors).forEach(item => {
				item.classList.add('icon-third-icon-yanwu');
				item.classList.remove('el-icon-close');
				// console.log(item.classList);
			});
		}),
		// 进行状态
		getStatus(row, plan) {
			if (row.status == 9) {
				return 'error';
			}

			const tiemMap = {
				1: 'demandTime',
				2: 'functionTime',
				3: 'planTime',
				4: 'developmentTime',
				5: 'testTime',
				6: 'checkTime',
				7: 'prepareTime',
				8: 'publishTime',
			};

			if (row.status >= plan.status) {
				return 'success';
			} else if (row.status + 1 == plan.status) {
				const planeTime = this.$moment(row[tiemMap[row.status + 1]])
					.startOf('day')
					.valueOf(); //当前阶段计划时间
				const nowTime = this.$moment().startOf('day').valueOf(); //当前时间
				if (planeTime && nowTime > planeTime) {
					return 'error';
				}
				return 'process';
			} else {
				return 'wait';
			}
		},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			window?.localStorage.setItem(this.$options.name + '_dateSelectObj', JSON.stringify(this.dateSelectObj));
			window?.localStorage.setItem(this.$options.name + '_searchForm', JSON.stringify(this.searchForm));
			type && (this.tablePageForm.currentPage = 1);
			const API = 'selectProject'; //接口
			const DATA = JSON.stringify({
				pageNum: this.tablePageForm.currentPage,
				pageSize: this.tablePageForm.pageSize,

				...this.dateSelectObj,
				...this.searchForm,
			});
			this.$axios[API](DATA)
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.$nextTick(() => {
							if (this.tableSort) {
								this.sortChange(this.tableSort);
							} else {
								this.$refs.uTableRef?.reloadData(this.tableData);
								this.$refs.uTableRef?.doLayout();
							}
						});
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		//日期format
		dateFormat: _.dateFormat,
		//自定义排序
		sortChange({ prop, order }) {
			this.tableSort = { prop, order };
			if (order) {
				const sortedData = _.sortTableData(this.tableData, prop, order, (a, b) => {
					// 新增/覆盖比较逻辑
					if (prop == 'businessPeople' || prop == 'projectManager' || prop == 'technicalManager' || prop == 'testManager') {
						if (!a?.userName) return -1;
						if (!b?.userName) return 1;
						return a?.userName.localeCompare(b?.userName, 'zh-CN');
					} else {
						return null; //其他情况必须要返回null
					}
				});
				this.$refs?.uTableRef?.reloadData(sortedData);
			} else {
				this.$refs?.uTableRef?.reloadData(this.tableData);
			}
		},
	},
};
</script>

<style lang="scss" scoped>
#projectOverview {
	width: 100%;
	overflow: hidden;
	position: relative;
	.project-steps {
		.el-step__title {
			font-size: 12px !important;
			line-height: 28px;
		}
		font-size: 12px !important;
	}
}
</style>
<style lang="scss">
#projectOverview {
	.project-steps {
		font-size: 12px !important;
		.el-step__title {
			font-size: 12px;
			line-height: 24px;
		}
		.is-process {
			color: #ff9800;
			border-color: #ff9800;
		}
	}
}
</style>
