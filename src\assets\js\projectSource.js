/* 
	需求类别
	系统BUG，系统运行出现故障不能使用，系统运行结果不正确
	系统优化，在现有功能或逻辑上的小改动小调整
	新需求，以前没有涉及的业务场景或没有涉及的业务逻辑。
	业务优化，在执行过程中业务需要怎么的配合。
	操作规范，用户不会操作或操作不按规范以为是系统的问题，需要加强培训。
	其他问题，无法归类到上述5类的归为其他问题
*/
export const demandTypeMap = {
	1: '系统BUG',
	2: '系统优化',
	3: '新需求',
	// 4: '业务优化',
	// 5: '操作规范',
	// 127: '其他问题',
};

/* 需求状态 */
export const demandStatusMap = {
	1: '未提交',
	2: '待评审',
	3: '待签署',
	4: '待开发经理接受任务',
	5: '开发经理已接受任务',
	// 6: '已实现',
};

/* 需求项目类别 */
export const projectCategoryMap = {
	1: '本地项目',
	2: '云端项目',
};
