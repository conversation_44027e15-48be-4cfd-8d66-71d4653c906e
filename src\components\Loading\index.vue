<!-- 自定义loading组件 -->
<template>
	<div class="card">
		<div class="loading-text">
			<i class="el-icon-loading"></i>
			<span class="loading-text-content">正在加载中...</span>
		</div>
		<div v-if="text || wordList.length > 0" class="loader">
			<span>{{ text }}</span>
			<div class="words">
				<span v-for="(word, index) in wordList" :key="index" class="word">{{ word }}</span>
				<!-- 重复第一个单词以完成循环 -->
				<span class="word">{{ wordList[0] }}</span>
			</div>
		</div>
	</div>
</template>
<script>
export default {
	name: 'CustomLoading',
	props: {
		// 文本内容
		text: {
			type: String,
			default: '',
		},
		// 滚动词组
		wordList: {
			type: Array,
			default: () => [],
			required: true,
		},
	},
};
</script>
<style lang="scss" scoped>
@import '@/styles/element-variables.scss';
.card {
	/* 修改为白色背景 */
	// --bg-color: #fff;
	background-color: var(--bg-color);
	padding: 1rem 2rem;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 10px;
	color: #666; /* 深色文字 */
	font-family: 'Poppins', sans-serif;
	font-weight: 500;
}

.loading-text {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10px;
	font-size: 28px;
	.el-icon-loading {
		color: $--color-primary;
		font-size: 26px;
	}

	.loading-text-content {
		background: linear-gradient(90deg, #409eff, #67c23a, #e6a23c, #f56c6c, #909399, #409eff);
		background-size: 400% 100%;
		animation: rainbow-text-animation 6s linear infinite;
		-webkit-background-clip: text;
		background-clip: text;
		-webkit-text-fill-color: transparent;
		text-fill-color: transparent;
	}
}

@keyframes rainbow-text-animation {
	0% {
		background-position: 0 0;
	}
	100% {
		background-position: 400% 0;
	}
}

.loader {
	font-size: 32px;
	font-weight: 600;
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
	height: 40px;
	padding: 10px 10px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	gap: 10px;

	.words {
		font-weight: 600;
		overflow: hidden;
		position: relative;
		.word {
			display: block;
			height: 100%;
			color: $--color-primary;
			animation: loading-words 4s infinite;
		}
	}
}

@keyframes loading-words {
	10% {
		-webkit-transform: translateY(-102%);
		transform: translateY(-102%);
	}

	25% {
		-webkit-transform: translateY(-100%);
		transform: translateY(-100%);
	}

	35% {
		-webkit-transform: translateY(-202%);
		transform: translateY(-202%);
	}

	50% {
		-webkit-transform: translateY(-200%);
		transform: translateY(-200%);
	}

	60% {
		-webkit-transform: translateY(-302%);
		transform: translateY(-302%);
	}

	75% {
		-webkit-transform: translateY(-300%);
		transform: translateY(-300%);
	}

	85% {
		-webkit-transform: translateY(-402%);
		transform: translateY(-402%);
	}

	100% {
		-webkit-transform: translateY(-400%);
		transform: translateY(-400%);
	}
}
</style>
