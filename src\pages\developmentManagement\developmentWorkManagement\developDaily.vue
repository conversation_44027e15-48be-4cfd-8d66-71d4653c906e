<template>
	<div class="developDaily">
		<BaseLayout>
			<template #header>
				<span class="search-label">周次</span>
				<el-button type="text" class="el-icon-arrow-left search-arrow" @click="changeWeek('reduce')"></el-button>
				<el-date-picker
					class="w-200"
					size="small"
					v-model="selectTime"
					type="week"
					format="yyyy 年 第 W 周"
					placeholder="请选择周次"
					:picker-options="{ firstDayOfWeek: 1 }"
					:default-value="selectTime"
					@change="changeSelectTime"
				>
				</el-date-picker>
				<el-button type="text" class="el-icon-arrow-right search-arrow" @click="changeWeek('add')"> </el-button>

				<span class="search-label" v-for="(item, index) in statusList" :key="'color' + index">
					<span :style="{ color: colorMap[item.id], 'font-size': '19px' }">●</span>
					<span :style="{ 'font-size': '14px' }">{{ item.status }}</span>
				</span>
				<el-button type="text" class="el-icon-refresh-right ml-auto" @click="queryTableData('refresh')">刷新</el-button>
			</template>
			<template #main>
				<div class="table-wrapper">
					<table class="table-main-daily" cellpadding="5" cellspacing="0">
						<!-- 表格标题 -->
						<tr class="sticky-top">
							<th class="W5 text-center">序号</th>
							<th class="W10 text-center">研发人员</th>
							<th class="W10 text-center" v-for="index in 7" :key="index">
								{{ `周${['一', '二', '三', '四', '五', '六', '日'][index - 1]} (${getWeekDate(index)})` }}
							</th>
						</tr>
						<!-- 表格内容 -->
						<tr v-for="(item, dIndex) in devDailyTableData" :key="dIndex">
							<td class="W5 text-center">{{ dIndex + 1 }}</td>
							<td>{{ item.userName }}</td>
							<td>
								<div class="max-h-200 overflow-y-auto">
									<Tooltips
										v-for="(task, index) in item.monday"
										:key="index"
										:style="{ color: colorMap[task.status] }"
										:cont-str="'【' + task.projectName + '】' + task.taskName + task.time + 'h'"
									/>
								</div>
							</td>
							<td>
								<div class="max-h-200 overflow-y-auto">
									<Tooltips
										v-for="(task, index) in item.tuesday"
										:key="index"
										:style="{ color: colorMap[task.status] }"
										:cont-str="'【' + task.projectName + '】' + task.taskName + task.time + 'h'"
									/>
								</div>
							</td>
							<td>
								<div class="max-h-200 overflow-y-auto">
									<Tooltips
										v-for="(task, index) in item.wednesday"
										:key="index"
										:style="{ color: colorMap[task.status] }"
										:cont-str="'【' + task.projectName + '】' + task.taskName + task.time + 'h'"
									/>
								</div>
							</td>
							<td>
								<div class="max-h-200 overflow-y-auto">
									<Tooltips
										v-for="(task, index) in item.thursday"
										:key="index"
										:style="{ color: colorMap[task.status] }"
										:cont-str="'【' + task.projectName + '】' + task.taskName + task.time + 'h'"
									/>
								</div>
							</td>
							<td>
								<div class="max-h-200 overflow-y-auto">
									<Tooltips
										v-for="(task, index) in item.friday"
										:key="index"
										:style="{ color: colorMap[task.status] }"
										:cont-str="'【' + task.projectName + '】' + task.taskName + task.time + 'h'"
									/>
								</div>
							</td>
							<td>
								<div class="max-h-200 overflow-y-auto">
									<Tooltips
										v-for="(task, index) in item.saturday"
										:key="index"
										:style="{ color: colorMap[task.status] }"
										:cont-str="'【' + task.projectName + '】' + task.taskName + task.time + 'h'"
									/>
								</div>
							</td>
							<td>
								<div class="max-h-200 overflow-y-auto">
									<Tooltips
										v-for="(task, index) in item.sunday"
										:key="index"
										:style="{ color: colorMap[task.status] }"
										:cont-str="'【' + task.projectName + '】' + task.taskName + task.time + 'h'"
									/>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</template>
		</BaseLayout>
	</div>
</template>

<script>
import * as _ from '@/util/tool';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'

export default {
	// import引入的组件需要注入到对象中才能使用
	props: {
		userNameList: Array,
	},
	components: {},
	name: 'developDaily',
	data() {
		return {
			activeTab: 'developDaily',
			//日期相关
			selectTime: '',
			dateStart: '',
			dateEnd: '',
			flag: 0,
			// 弹窗相关
			titleName: '',
			openMove: false,
			// 数据相关
			devDailyTableData: [],
			rowData: {},
			statusList: [
				{ id: 3, status: '已转测' },
				{ id: 5, status: '测试不通过' },
				{ id: 4, status: '转测通过' },
			],
			colorMap: {
				// 0: "#d50000", //红 开发延误
				// 1: "#9e9e9e", //灰 计划中
				// 2: "#651fff", //紫 开发中
				3: '#448aff', //蓝 已转测 测试计划中
				4: '#00bfa5', //绿 完成
				5: '#ff80ab', //粉 转测不通过
				// 6: "#d50000", //红 测试延误
			},
			weekMap: {
				1: '一',
				2: '二',
				3: '三',
				4: '四',
				5: '五',
				6: '六',
				0: '日',
			},
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos']), //当前登录用户信息（含团队/菜单/权限等）
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.changeSelectTime();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 获取每周日期
		getWeekDate(index) {
			return this.$moment(this.dateStart)
				.startOf('isoWeek')
				.add(index - 1, 'day')
				.format('MM/DD');
		},
		// 时间上下周切换
		changeWeek(str) {
			this.selectTime = new Date(this.selectTime).getTime();
			if (str == 'add') {
				this.selectTime += 7 * 24 * 60 * 60 * 1000;
				this.flag--;
			} else if (str == 'reduce') {
				// 上一周
				this.selectTime -= 7 * 24 * 60 * 60 * 1000;
				this.flag++;
			}
			this.dateStart = new Date(this.selectTime).getTime() - 8 * 60 * 60 * 1000;
			this.dateEnd = this.dateStart + 7 * 24 * 60 * 60 * 1000 - 1;
			this.queryTableData();
		},
		// 时间选择器
		changeSelectTime() {
			if (!this.selectTime) {
				this.selectTime = _.getNowWeekStart();
				this.dateStart = new Date(this.selectTime).getTime() - 8 * 60 * 60 * 1000;
				this.dateEnd = this.dateStart + 7 * 24 * 60 * 60 * 1000 - 1;
			} else {
				this.dateStart = new Date(this.selectTime).getTime() - 24 * 60 * 60 * 1000;
				this.dateEnd = this.dateStart + 7 * 24 * 60 * 60 * 1000 - 1;
			}

			this.queryTableData();
		},
		// 查询表格数据
		queryTableData: _.debounce(function (type) {
			setTimeout(() => {
				this.rowData = {};
			}, 888);
			this.devDailyTableData = [];

			this.$axios
				.selectDepartmentDaily(JSON.stringify({ endTime: this.dateEnd, startTime: this.dateStart }))
				.then(res => {
					if (res.data.success) {
						res.data.data.forEach(item => {
							const obj = {
								monday: [],
								tuesday: [],
								wednesday: [],
								thursday: [],
								friday: [],
								saturday: [],
								sunday: [],
							};

							item.projectTaskTurnDailyVOS.forEach(val => {
								const week = this.weekMap[new Date(val.submitTime).getDay()];
								if (week == '一') {
									obj.monday.push(val);
								} else if (week == '二') {
									obj.tuesday.push(val);
								} else if (week == '三') {
									obj.wednesday.push(val);
								} else if (week == '四') {
									obj.thursday.push(val);
								} else if (week == '五') {
									obj.friday.push(val);
								} else if (week == '六') {
									obj.saturday.push(val);
								} else if (week == '日') {
									obj.sunday.push(val);
								}
							});

							item = Object.assign(item, obj);
						});
						this.devDailyTableData = res.data.data;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log('selectDepartmentDaily |' + error);
				});
		}),

		//日期format
		dateFormat: _.dateFormat,
	},
};
</script>

<style lang="scss" scoped>
.developDaily {
	width: 100%;
	overflow: hidden;
	position: relative;
	.table-wrapper {
		height: calc(100vh - 250px);
	}
	.table-main-daily {
		width: 100%;
		color: #606266;
		border-left: 1px solid #e9e9e9;
		border-bottom: 1px solid #e9e9e9;

		.sticky-top {
			position: sticky;
			top: -10px;
		}
		hover-green {
			&:hover {
				color: #28d094;
				text-decoration: underline;
				cursor: pointer;
			}
		}

		tr {
			th {
				text-align: left;
				font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
				font-weight: 650;
				font-size: 14px;
				color: #666666;
				padding: 15px 0 15px 5px;
				background: #f5f5f5;
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				word-wrap: break-word;
			}

			td {
				border-right: 1px solid #e9e9e9;
				border-top: 1px solid #e9e9e9;
				font-size: 12px;
				vertical-align: top;
				max-width: 10vw;
			}
		}
	}
}
</style>
