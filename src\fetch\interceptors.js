/**
 * Axios 拦截器模块
 */
import axios from 'axios';
import { httpErrorStatusHandle } from './error-handler';
import { addPendingRequest, removePendingRequest } from './cancel';

/**
 * 根据数据类型设置请求头
 * @param {Object} config - 请求配置
 * @param {string} token - 用户token
 * @returns {Object} 更新后的配置
 */
const setupRequestHeaders = (config, token) => {
	const dataType = Object.prototype.toString.call(config.data);

	// 处理FormData类型
	if (config.data instanceof FormData) {
		config.headers = {
			token,
			// 'Content-Type': 'multipart/form-data', // 不要手动设置 Content-Type，浏览器会自动加 boundary
			'Cache-Control': 'no-cache',
			'Access-Control-Allow-Origin': '*',
		};
	}
	// 处理Blob响应类型
	else if (dataType === '[object String]' && config.responseType === 'blob') {
		config.data = JSON.parse(config.data);
		config.headers = {
			token,
			'Content-Type': 'application/msexcel;charset=UTF-8',
			'Cache-Control': 'no-cache',
			'Access-Control-Allow-Origin': '*',
		};
	}
	// 处理文本响应类型
	else if (axios.defaults.responseType === 'text' || config.data === '') {
		config.data = JSON.parse(config.data);
	}
	// 处理普通JSON请求
	else {
		config.data = JSON.parse(config.data);
		config.headers = {
			token,
			'Content-Type': 'application/json',
			'Cache-Control': 'no-cache',
			'Access-Control-Allow-Origin': '*',
		};
	}

	return config;
};

/**
 * 设置请求拦截器
 */
export const setupRequestInterceptor = () => {
	axios.interceptors.request.use(
		config => {
			// 添加请求到待处理队列，如果存在相同请求则会取消之前的请求
			addPendingRequest(config);

			// 获取用户token
			const OPS_USER_INFO = JSON.parse(window.sessionStorage.getItem('OPS_USER_INFO')) || null;
			const OPS_TOKEN = OPS_USER_INFO?.token || '';

			// 不需要token的API列表
			const noTokenApis = [
				'/background/web/loginAdminUserController/sendAdminLoginSmsCode',
				'/background/web/loginAdminUserController/smsCodeLogin',
				'/background/web/loginAdminUserController/pwLogin',
			];

			// 无token处理逻辑
			if (!OPS_TOKEN && !noTokenApis.includes(config.url)) {
				window.location.href = '/#/login'; //除了登录或特殊接口，无token自动跳转到登录页
			}

			// 设置请求头
			config = setupRequestHeaders(config, OPS_TOKEN);

			return config;
		},
		error => error,
	);
};

/**
 * 设置响应拦截器
 */
export const setupResponseInterceptor = () => {
	axios.interceptors.response.use(
		response => {
			// 从待处理队列中移除请求
			removePendingRequest(response.config);
			return response;
		},
		err => {
			// 如果是请求取消错误，不显示错误消息
			if (axios.isCancel(err)) {
				return Promise.reject(err);
			}

			// 从待处理队列中移除请求
			err.config && removePendingRequest(err.config);

			// 处理其他错误
			return httpErrorStatusHandle(err);
		},
	);
};

export default {
	setupRequestInterceptor,
	setupResponseInterceptor,
};
