const TableColumns_KEY = 'OPS-tableColumns';

/**
 * 显示表格列和表格列宽度
 *
 * @param {Object} tableColumn - 要更新的表格列数据。
 * @param {string} key - 用于标识表格列的键。
 * @return {Object} 更新后的表格列数据。
 */
export function getColumn(tableColumn, key) {
	// 获取localStorage中的表格列数据
	const tableColumns = JSON.parse(localStorage.getItem(TableColumns_KEY)) || {};
	// 如果localStorage中没有存储的表格列数据，则返回当前组件的表格列数据
	if (tableColumns[key] === undefined) {
		tableColumns[key] = tableColumn.map(item => {
			return {
				...item,
				name: item.colName,
				state: true,
			};
		});
		return tableColumns[key];
	}

	// 如果localStorage中存在当前组件的表格列数据，则获取当前组件的表格列数据
	let nowTableColumn = tableColumns[key];
	// 过滤掉既不存在于存储的表格列数据，也不存在于当前组件的表格列数据的列
	nowTableColumn = nowTableColumn?.filter(column => {
		return tableColumn?.some(c => c.colNo === column.colNo && c.colName === column.colName);
	});

	// 将当前组件的表格列数据中存在但存储的数据中不存在的列按照顺序插入到 nowTableColumn 中
	for (let i = 0; i < tableColumn.length; i++) {
		const column = tableColumn[i];
		if (!nowTableColumn?.some(c => c.colNo === column.colNo && c.colName === column.colName)) {
			nowTableColumn?.splice(i, 0, { ...column, name: column.colName, state: true });
		}
	}
	tableColumns[key] = nowTableColumn.map(item => {
		return {
			...item,
			name: item.colName,
			state: item.state === undefined ? true : item.state,
		};
	});
	localStorage.setItem(TableColumns_KEY, JSON.stringify(tableColumns));

	// 返回存储的表格列数据
	return tableColumns[key];
}

/**
 * 更新表格中特定列的宽度。
 *
 * @param {string} componentName - 组件的名称。
 * @param {number} colNo - 列的编号(字段名)。
 * @param {number} width -  列的新宽度。
 */
export function updateColumnWidth(componentName, colNo, width, tableColumn) {
	const tableColumns = JSON.parse(localStorage.getItem(TableColumns_KEY)) || {};
	if (tableColumns[componentName] === undefined) {
		tableColumns[componentName] = tableColumn;
	}

	tableColumns[componentName].forEach(item => {
		if (item.colNo === colNo) {
			item.width = width;
		}
	});
	localStorage.setItem(TableColumns_KEY, JSON.stringify(tableColumns));
}

/**
 * 更新表格列
 *
 * @param {string} componentName - 组件的名称。
 * @param {Array} tableColumn - 表格列数据。
 */
export function updateColumn(componentName, tableColumn) {
	const tableColumns = JSON.parse(localStorage.getItem(TableColumns_KEY)) || {};
	if (tableColumns[componentName] === undefined) {
		tableColumns[componentName] = tableColumn;
		localStorage.setItem(TableColumns_KEY, JSON.stringify(tableColumns));
		return tableColumn;
	}

	tableColumns[componentName] = tableColumn.map(item => {
		return {
			...item,
			name: item.colName,
			state: item.state === undefined ? true : item.state,
		};
	});
	localStorage.setItem(TableColumns_KEY, JSON.stringify(tableColumns));
	return tableColumns[componentName];
}
