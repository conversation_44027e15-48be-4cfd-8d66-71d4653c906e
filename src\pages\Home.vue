<template>
	<div class="W100 H100 flex-column">
		<SplitPane
			class="SplitPane flex-1"
			:class="{ hideRight: !showChatBot }"
			:min-percent="20"
			:default-percent="showChatBot ? 75 : 100"
			split="vertical"
		>
			<div id="homeContainer" slot="paneL">
				<!-- 顶部LOGO，用户信息 -->
				<TopHeader @fullScreen="clickF11Btn" />
				<el-container class="main-container">
					<!-- 左边菜单栏 -->
					<SideMenu />
					<!-- 中间路由导航栏和路由页面显示 -->
					<el-container class="flex-column H100">
						<!-- 路由导航栏 -->
						<RouterTags />
						<!-- 组件页面 -->
						<el-main>
							<transition appear name="router-transform" mode="out-in">
								<!-- 
									默认所有路由都缓存，如看板这类加了定时器或有需求等，可在路由meta配置noCache
									注意：include和 exclude判断的都是组件名，即要在组件设置name而不是路由的name，如果name没有对应上则缓存不会生效，
									如果你不想不需要缓存这个页面，也不想设置noCache，可以修改组件name达到不缓存的效果。
						 		-->
								<keep-alive :include="cacheTags" :max="5">
									<router-view @fullScreen="clickF11Btn" :full-screen-flag="fullScreenFlag"></router-view>
								</keep-alive>
							</transition>
						</el-main>
					</el-container>
				</el-container>
			</div>
			<!-- 聊天机器人 -->
			<ChatBot v-if="showChatBot" slot="paneR" />
		</SplitPane>
	</div>
</template>

<script>
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
import TopHeader from './Z-Layout/TopHeader.vue';
import RouterTags from './Z-Layout/RouterTags.vue';
import SideMenu from './Z-Layout/SideMenu.vue';
import SplitPane from 'vue-splitpane'; //拖拽分割面板组件
import ChatBot from './Z-Layout/ChatBot.vue';
export default {
	components: {
		TopHeader,
		RouterTags,
		SideMenu,
		SplitPane,
		ChatBot,
	},

	data() {
		return {
			fullScreenFlag: false, // 全屏控制
		};
	},
	created() {
		// 持久化存储 - vuex + 本地sessionStorage方案
		this.persistStorage();
	},
	beforeMount() {
		// @todo：该系统对于这些信息的存储非常混乱，待后续优化和统一
		const LOGIN_INFO = JSON.parse(window.sessionStorage.getItem('OPS_USER_INFO')); // 获取存储在本地的用户信息 - 登录接口返回的所有内容(非常重要)
		if (LOGIN_INFO) {
			this.$store.commit('setUserInfos', LOGIN_INFO);
		}

		// 当 queryId 和 登录用户的phoneNo不符合时 修改为 登录用户的phoneNo
		if (LOGIN_INFO && LOGIN_INFO.adminUserVO.phoneNo != this.$route.query.queryId) {
			this.$route.query.queryId = LOGIN_INFO.adminUserVO.phoneNo;
		}
	},
	mounted() {
		// 如果没有queryId 或者 userInfos为空则跳转到登录页
		if (!this.userInfos || !this.$route.query.queryId) {
			this.$router.replace({
				path: '/Login',
				query: {
					redirect: this.$router.currentRoute.fullPath,
				}, //登录成功后跳入浏览的当前页面
			});
		}

		// 设置是否是聚心城用户
		const isJuxinCity = this.userInfo.departmentName?.includes('聚心');
		this.$store.commit('setIsJuxinCity', isJuxinCity);

		// 初始化全局数据
		this.initGlobalData();
	},
	beforeDestroy() {},
	computed: {
		...mapGetters(['userInfos', 'userInfo']), //当前登录用户信息（含团队/菜单/权限等）
		...mapGetters(['menuList']),
		...mapGetters(['routerTags']),
		...mapGetters(['cacheTags']),
		...mapGetters(['showChatBot']),
	},
	methods: {
		/* 持久化存储 - vuex + 本地sessionStorage方案 */
		persistStorage() {
			// 在页面卸载前保存 Vuex 状态
			window.addEventListener('beforeunload', () => {
				const state = JSON.stringify(this.$store.state);
				window.sessionStorage.setItem('OPS_VUEX_STORE', state);
			});

			// 尝试恢复 Vuex 状态
			const storedState = window.sessionStorage.getItem('OPS_VUEX_STORE');
			if (storedState) {
				try {
					this.$store.replaceState(Object.assign({}, this.$store.state, JSON.parse(storedState)));
				} catch (err) {
					console.error('尝试恢复 Vuex 状态时出错啦:', err);
				}
			}

			// 清除存储的状态
			window.sessionStorage.removeItem('OPS_VUEX_STORE');
		},
		// 浏览器全屏
		clickF11Btn(flag) {
			const el = flag ? document.documentElement : document;
			const rfs = flag
				? el.requestFullScreen || el.webkitRequestFullScreen || el.mozRequestFullScreen || el.msRequestFullScreen
				: el.exitFullScreen || el.mozCancelFullScreen || el.webkitExitFullscreen || el.msExitFullscreen;

			if (rfs) {
				rfs.call(el);
				this.fullScreenFlag = flag;
			} else if (typeof window.ActiveXObject !== 'undefined') {
				const wscript = new ActiveXObject('WScript.Shell');
				if (wscript) {
					wscript.SendKeys(flag ? '{F11}' : '{exit}');
					this.fullScreenFlag = flag;
				}
			}
		},

		// 需要全局初始化的方法
		...mapActions(['initGlobalData']),
	},
};
</script>

<style lang="scss">
@import '@/styles/element-variables.scss';
.SplitPane.hideRight {
	> .splitter-pane-resizer,
	> .splitter-paneR {
		display: none;
	}
}
#homeContainer {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;

	// 中心 main样式
	.main-container {
		box-sizing: border-box;
		// min-height: 100vh;
		height: calc(100% - 60px);
		// padding-top: 60px;

		//主容器自适应
		.el-main {
			height: 100%;
			padding: 0 20px;
			background-color: rgba(242, 242, 242, 1);
			overflow-y: auto;
			box-sizing: border-box;
		}
	}
}
</style>
