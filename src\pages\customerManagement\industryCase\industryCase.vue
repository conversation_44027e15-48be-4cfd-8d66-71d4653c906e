<template>
	<div class="industryCase">
		<!-- 行业案例明细组件 -->
		<industryCaseDetail
			ref="industryCaseDetail"
			:industryOptions="industryOptions"
			:productOptions="productOptions"
			@close="queryTableData(1)"
		/>
		<el-tabs v-model="activeTab" @tab-click="changeTab">
			<el-tab-pane label="行业案例" name="industryCase">
				<BaseLayout>
					<template #header>
						<!-- 带建议日期 -->
						<!-- <span class="search-label">期间</span>
						<DateSelect
							@change="
								searchForm.startTime = $event.startTime;
								searchForm.endTime = $event.endTime;
								queryTableData(1);
							"
						/> -->
						<span class="search-label">发布状态</span>
						<el-select
							v-model="searchForm.isPublish"
							placeholder="发布状态"
							size="small"
							clearable
							filterable
							@change="queryTableData(1)"
						>
							<el-option label="未发布" :value="0"></el-option>
							<el-option label="已发布" :value="1"></el-option>
						</el-select>

						<el-select
							v-model="searchForm.industries"
							placeholder="行业"
							size="small"
							class="w-180"
							popper-class="select-column-4"
							clearable
							filterable
							multiple
							collapse-tags
							@change="queryTableData(1)"
						>
							<el-option v-for="item in industryOptions" :key="item" :label="item" :value="item" :title="item.item"> </el-option>
						</el-select>

						<!-- 模糊查询 -->
						<el-input
							class="searchBox"
							size="small"
							v-model.trim="searchForm.project"
							placeholder="项目"
							@input="queryTableData(1)"
							clearable
						></el-input>
						<el-input
							class="searchBox"
							size="small"
							v-model.trim="searchForm.mainProduct"
							placeholder="主营产品"
							@input="queryTableData(1)"
							clearable
						></el-input>
						<el-input
							class="searchBox"
							size="small"
							v-model.trim="searchForm.mainProcess"
							placeholder="主要工序"
							@input="queryTableData(1)"
							clearable
						></el-input>

						<el-select
							v-model="searchForm.demands"
							placeholder="需求"
							size="small"
							class="w-180"
							popper-class="select-column-3"
							clearable
							filterable
							multiple
							collapse-tags
							@change="queryTableData(1)"
						>
							<el-option v-for="item in productOptions" :key="item" :label="item" :value="item" :title="item.item"> </el-option>
						</el-select>

						<el-input
							class="searchBox"
							size="small"
							v-model.trim="searchForm.area"
							placeholder="地区"
							@input="queryTableData(1)"
							clearable
						></el-input>

						<!-- <el-radio-group v-model="searchForm.isPublish" @change="queryTableData(1)">
							<el-radio :label="0">未发布</el-radio>
							<el-radio :label="1">已发布</el-radio>
						</el-radio-group> -->

						<div class="ml-auto">
							<el-button type="text" class="el-icon-refresh-right" @click="queryTableData('refresh')">刷新</el-button>
							<el-button type="text" class="icon-third-bt_newdoc" @click="openDetail(null)">添加</el-button>
						</div>
					</template>
					<template #main>
						<!-- 表格上的功能栏：如放大镜查询、添加、打印、导入导出等 注意调整table-main高度-->
						<div class="table-toolbar">
							<el-button type="text" class="el-icon-s-grid" @click="$refs.uTableRef.plDialogOpens()">字段设置</el-button>
						</div>
						<!-- 表格主体 -->
						<u-table
							ref="uTableRef"
							class="table-main"
							:height="1200"
							:row-height="45"
							:dialog-data="tableColumn"
							:total="tablePageForm.total"
							:page-size="tablePageForm.pageSize"
							:current-page="tablePageForm.currentPage"
							:page-sizes="tablePageForm.pageSizes"
							@handlePageSize="handlePageSize"
							@sort-change="sortChange"
							@header-dragend="headerDragend"
							@reset="updateColumn(tableColumnCopy)"
							@show-field="updateColumn"
							show-header-overflow="title"
							pagination-show
							use-virtual
							stripe
						>
							<u-table-column label="序号" width="60" type="index" align="center"></u-table-column>
							<u-table-column
								v-for="(item, index) in tableColumn.filter(item => item.state)"
								:key="item.colNo + index"
								:label="item.colName"
								:prop="item.colNo"
								:align="item.align"
								:width="item.width"
								sortable="custom"
								resizable
							>
								<template slot-scope="scope">
									<!-- 各种日期（默认不显示分秒 lineM ） -->
									<Tooltips
										v-if="item.colNo.includes('Time') || item.colNo.includes('Date')"
										:cont-str="dateFormat(scope.row[item.colNo], 'line')"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 样板工厂 -->
									<span v-else-if="item.colNo == 'isModelFactory' && scope.row[item.colNo] !== null">
										<i :class="scope.row[item.colNo] ? 'el-icon-check green' : ''"></i>
									</span>
									<!-- 项目 -->
									<Tooltips
										v-else-if="item.colNo == 'project' && scope.row.number"
										:cont-str="`${scope.row.project}(${scope.row.number})`"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
									<!-- 发布状态 -->
									<el-button
										v-else-if="item.colNo == 'isPublish'"
										type="text"
										size="small"
										:class="!scope.row[item.colNo] ? 'color-999' : ''"
										@click="openPublish(scope.row)"
										>{{ ['未发布', '已发布'][scope.row[item.colNo]] || '未发布' }}</el-button
									>
									<!-- 项目状态 -->
									<Tooltips
										v-else-if="item.colNo == 'status'"
										:class="scope.row[item.colNo] == 0 ? '' : 'green'"
										:cont-str="['未交付', '已交付'][scope.row[item.colNo]]"
										:cont-width="scope.column.width || scope.column.realWidth"
									/>

									<!-- 附件 -->
									<i
										v-else-if="item.colNo == 'fileVOList' && scope.row[item.colNo]"
										class="el-icon-paperclip pointer"
										@click="$refs.CaseFiles.openDialog(scope.row)"
									>
										<span>（{{ scope.row[item.colNo].length }}）</span>
									</i>

									<!-- 链接 -->
									<div v-else-if="item.colNo == 'url' && scope.row[item.colNo]">
										<el-popover placement="right" width="365" trigger="click">
											<div class="fs-12 color-666">链接：</div>
											<div v-for="(uItem, uIndex) in scope.row.url" :key="uIndex" class="flex-justify-between align-center">
												<a :href="encodeURI(uItem)" target="_blank">
													<Tooltips class="max-w-350" :cont-str="encodeURI(uItem)" :cont-width="320" />
												</a>
												<el-button
													type="text"
													size="mini"
													class="el-icon-document-copy"
													@click="copyToClipboard(encodeURI(uItem))"
												>
												</el-button>
											</div>
											<i slot="reference" class="el-icon-paperclip pointer">
												<span>（{{ scope.row[item.colNo].length }}）</span>
											</i>
										</el-popover>
									</div>

									<!-- 点赞 -->
									<div v-else-if="item.colNo == 'userLikeCount'" class="flex-justify-between align-center">
										<i
											class="icon-third-like pointer fs-18"
											:class="scope.row.currentUserIsLikeCase ? 'red' : ''"
											@click="likeCase(scope.row)"
										></i>

										<span>{{ scope.row[item.colNo] || 0 }}</span>
									</div>

									<!-- 问答 -->
									<div v-else-if="item.colNo == 'question'" class="flex-justify-between align-center">
										<i
											class="icon-third-wenda pointer fs-18"
											:class="scope.row.question ? 'green' : ''"
											@click="openQuestion(scope.row)"
										></i>
										<span>{{ scope.row[item.colNo] || 0 }}</span>
									</div>

									<!-- 默认显示 -->
									<Tooltips
										v-else
										:cont-str="scope.row[item.colNo] ? scope.row[item.colNo] + '' : ''"
										:cont-width="(scope.column.width || scope.column.realWidth) - 20"
									/>
								</template>
							</u-table-column>
							<!-- 其他列/操作 -->
							<u-table-column label="" width="" align="right" fixed="right">
								<template slot-scope="scope">
									<el-button type="text" class="el-icon-edit-outline" @click="openDetail(scope.row)"></el-button>
									<!-- <el-button type="text" class="el-icon-delete" @click="deleteRow(scope.row)"></el-button> -->
								</template>
							</u-table-column>
						</u-table>
					</template>
				</BaseLayout>
			</el-tab-pane>
		</el-tabs>

		<CaseFiles ref="CaseFiles" />
	</div>
</template>

<script>
import { deepClone, debounce, dateFormat, jointString, sortTableData, copyToClipboard } from '@/util/tool'; //按需引入常用工具函数
import { getColumn, updateColumn, updateColumnWidth } from '@/util/tableColumns';
import { mapGetters, mapState, mapMutations, mapActions } from 'vuex';
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》'
import industryCaseDetail from './industryCaseDetail.vue'; //明细组件
import { industryOptions, productOptions } from '@/assets/js/inquirySource.js';
import CaseFiles from '@/components/CaseFiles.vue'; //案例附件
// import btnAuth from '@/mixins/btnAuth';

export default {
	name: 'industryCase', //组件名应同路由名(否则keep-alive不生效)
	// import引入的组件需要注入到对象中才能使用
	components: {
		industryCaseDetail,
		CaseFiles,
	},
	// mixins: [btnAuth],
	data() {
		return {
			activeTab: 'industryCase', //激活tab页
			selectedData: [], //勾选的行
			//表格相关
			tableSort: { prop: '', order: '' }, //表格排序状态
			tableData: [],
			tablePageForm: {
				total: 0,
				pageSize: 100,
				currentPage: 1,
				pageSizes: [100, 500, 1000],
			},
			tableColumn: [
				{ colName: '类型', colNo: 'type', align: 'left', width: '' },
				{ colName: '样板工厂', colNo: 'isModelFactory', align: 'center', width: '' },
				{ colName: '项目', colNo: 'project', align: 'left', width: '' },
				{ colName: '项目状态', colNo: 'status', align: 'center', width: '' },
				{ colName: '行业', colNo: 'industry', align: 'left', width: '' },
				{ colName: '主营产品', colNo: 'mainProduct', align: 'left', width: '' },
				{ colName: '主要工序', colNo: 'mainProcess', align: 'left', width: '' },
				{ colName: '需求', colNo: 'demand', align: 'left', width: '' },
				{ colName: '地区', colNo: 'area', align: 'left', width: '' },
				{ colName: '业务顾问', colNo: 'businessAdviser', align: 'left', width: '' },
				{ colName: '实施顾问', colNo: 'implementName', align: 'left', width: '' },
				{ colName: '创作者', colNo: 'creatorName', align: 'left', width: '' },
				{ colName: '备注', colNo: 'remark', align: 'left', width: '' },
				{ colName: '附件', colNo: 'fileVOList', align: 'right', width: '' },
				{ colName: '链接', colNo: 'url', align: 'center', width: '' },
				{ colName: '发布状态', colNo: 'isPublish', align: 'center', width: '' },
				{ colName: '点赞', colNo: 'userLikeCount', align: 'right', width: '70' },
				// { colName: '问答', colNo: 'question', align: 'right', width: '70' },
			],
			tableColumnCopy: [],
			// 查询表单
			searchForm: {
				area: '',
				demands: [],
				industries: [],
				isPublish: '',
				mainProcess: '',
				mainProduct: '',
				project: '',
				twid: '',
				// 其他...
			},

			industryOptions,
			productOptions: productOptions.filter(item => item.item != '合伙人'),
		};
	},
	// 监听属性 类似于data概念
	computed: {
		...mapGetters(['userInfos', 'userList']), //当前登录用户信息（含团队/菜单/权限等）
		//业务顾问列表(标签：业务)
		salesmanList() {
			return this.userList?.filter(user => user?.userLabel?.includes('业务') || user?.userLabel?.includes('实施')) || [];
		},
		// 实施顾问列表(标签：实施)
		implementList() {
			return this.userList?.filter(user => user?.userLabel?.includes('实施')) || [];
		},
	},
	// 监控data中的数据变化
	watch: {},
	// 生命周期 - 创建完成（可以访问当前this实例）
	created() {},
	// 生命周期 - 挂载完成（可以访问DOM元素）
	mounted() {
		this.tableColumnCopy = deepClone(this.tableColumn);
		this.tableColumn = getColumn(this.tableColumn, this.$options.name);
		this.queryTableData();
	},
	activated() {
		// this.queryTableData(1);
		this.changeTab();
	},
	beforeMount() {}, // 生命周期 - 挂载之前
	beforeDestroy() {}, // 生命周期 - 销毁之前
	destroyed() {}, // 生命周期 - 销毁完成
	// 方法集合
	methods: {
		// 打开问答
		openQuestion: debounce(async function (row) {
			return this.$message.warning('正在开发中，暂未开放...');
		}),
		// 点赞
		likeCase: debounce(async function (row) {
			const API = 'updateIndustryCaseLikeStatus';
			try {
				const res = await this.$axios[API](JSON.stringify({ icid: row.icid, likeStatus: row.currentUserIsLikeCase ? 0 : 1 }));
				if (res.data.success) {
					row.currentUserIsLikeCase = row.currentUserIsLikeCase ? 0 : 1;
					row.userLikeCount = row.userLikeCount + (row.currentUserIsLikeCase ? 1 : -1);
					// this.queryTableData();
					// this.$succ(res.data.message);
				} else {
					this.$err(res.data.message);
				}
			} catch (error) {
				console.error(`${API} |` + error);
			}
		}),
		// 打开发布
		openPublish(row) {
			this.$confirm(
				`当前状态为${['未发布', '已发布'][row.status]}，操作后将变为${['未发布', '已发布'][row.status == 0 ? 1 : 0]}，是否继续?`,
				'提示',
				{
					confirmButtonText: '确定',
					cancelButtonText: '取消',
				},
			)
				.then(async () => {
					const API = 'updateIndustryCaseStatus';
					try {
						const res = await this.$axios[API](JSON.stringify({ icid: row.icid, isPublish: row.isPublish == 0 ? 1 : 0 }));
						if (res.data.success) {
							this.$succ(res.data.message);
							this.queryTableData();
						} else {
							this.$err(res.data.message);
						}
					} catch (error) {
						console.error(`${API} |` + error);
					}
				})
				.catch(() => {
					this.$message.info('已取消');
				});
		},
		// 打开明细
		openDetail(row) {
			this.$refs.industryCaseDetail.showDetailCom(row);
		},

		// 切换tab
		changeTab() {
			if (this.activeTab == 'industryCase') {
				this.queryTableData(1);
			} else {
				this.$nextTick(() => {
					this.$refs[this.activeTab]?.queryTableData(1);
				});
			}
		},
		// 查询表格数据
		queryTableData: debounce(function (type) {
			type && (this.tablePageForm.currentPage = 1);
			this.searchForm.pageNum = this.tablePageForm.currentPage;
			this.searchForm.pageSize = this.tablePageForm.pageSize;
			const API = 'selectIndustryCases'; //接口
			this.$axios[API](JSON.stringify({ ...this.searchForm }))
				.then(res => {
					if (res.data.success) {
						this.tableData = res.data.data;
						this.tablePageForm.total = res.data.totalItems;
						type == 'refresh' && this.$message.success('刷新成功，数据已更新！');
						this.sortChange(this.tableSort, true);
					} else {
						this.$message.warning(res.data.message);
					}
				})
				.catch(error => {
					console.log(`${API} |` + error);
				});
		}),
		//自定义排序
		sortChange({ prop, order }, doLayout = false) {
			this.$nextTick(() => {
				this.tableSort = { prop, order };
				const sortedData = order && prop ? sortTableData(this.tableData, prop, order) : this.tableData;
				this.$refs?.uTableRef?.reloadData(sortedData); // 加载页面数据
				doLayout && this.$refs.uTableRef?.doLayout(); // 页面自动布局（列对齐，滚动位置保持等）
			});
		},
		// 表格分页
		handlePageSize({ page, size }) {
			this.tablePageForm.pageSize = size;
			this.tablePageForm.currentPage = page;
			this.queryTableData();
		},
		// 展示字段
		updateColumn(data) {
			this.tableColumn = updateColumn(this.$options.name, data);
			this.sortChange(this.tableSort, true);
		},
		// 列宽拖动
		headerDragend(newWidth, oldWidth, column) {
			updateColumnWidth(this.$options.name, column.property, newWidth, this.tableColumn);
		},

		dateFormat, //日期format
		jointString, //拼接字符串
		copyToClipboard, //复制到剪切板
	},
};
</script>

<style lang="scss" scoped>
.industryCase {
	width: 100%;
	overflow: hidden;
	position: relative;
}
</style>
